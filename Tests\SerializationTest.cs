using System;
using System.IO;
using MyHMI.Settings;
using MyHMI.Helpers;

namespace MyHMI.Tests
{
    /// <summary>
    /// Settings序列化功能测试
    /// </summary>
    public static class SerializationTest
    {
        /// <summary>
        /// 运行序列化测试
        /// </summary>
        public static void RunSerializationTest()
        {
            try
            {
                LogHelper.Info("开始Settings序列化功能测试...");

                // 测试1：创建测试设置
                var testSettings = CreateTestSettings();

                // 测试2：序列化到文件
                string testFilePath = TestSerialization(testSettings);

                // 测试3：反序列化验证
                TestDeserialization(testFilePath, testSettings);

                // 清理测试文件
                if (File.Exists(testFilePath))
                {
                    File.Delete(testFilePath);
                    LogHelper.Info("✓ 测试文件清理完成");
                }

                LogHelper.Info("Settings序列化功能测试完成，所有测试通过！");
            }
            catch (Exception ex)
            {
                LogHelper.Error("Settings序列化测试失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 创建测试设置
        /// </summary>
        private static AppSettings CreateTestSettings()
        {
            LogHelper.Info("创建测试设置...");

            var settings = new AppSettings();
            
            // 设置一些测试值
            settings.System.Name = "序列化测试系统";
            settings.System.Version = "1.0.0.Test";
            settings.Motor.LeftFlipPulseEquivalent = 0.123456;
            settings.Motor.RightFlipPulseEquivalent = 0.654321;
            settings.Communication.EpsonRobot1IP = "*************";
            settings.Communication.EpsonRobot1ControlPort = 9001;
            settings.Vision.CameraIndex = 1;
            settings.Vision.ConfidenceThreshold = 0.95;
            settings.IO.InputChannels = 32;
            settings.IO.OutputChannels = 32;
            settings.Statistics.AutoExport = true;
            settings.Statistics.ExportInterval = 60;

            LogHelper.Info("✓ 测试设置创建完成");
            return settings;
        }

        /// <summary>
        /// 测试序列化
        /// </summary>
        private static string TestSerialization(AppSettings settings)
        {
            LogHelper.Info("测试序列化...");

            string testFilePath = Path.Combine(Path.GetTempPath(), "test_settings.dat");
            
            // 使用Settings类的序列化方法
            try
            {
                using (var stream = new FileStream(testFilePath, FileMode.Create))
                {
                    var formatter = new System.Runtime.Serialization.Formatters.Binary.BinaryFormatter();
                    formatter.Serialize(stream, settings);
                }
                
                LogHelper.Info($"✓ 序列化成功，文件路径: {testFilePath}");
                LogHelper.Info($"✓ 文件大小: {new FileInfo(testFilePath).Length} 字节");
                
                return testFilePath;
            }
            catch (Exception ex)
            {
                throw new Exception($"序列化失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 测试反序列化
        /// </summary>
        private static void TestDeserialization(string filePath, AppSettings originalSettings)
        {
            LogHelper.Info("测试反序列化...");

            try
            {
                AppSettings deserializedSettings;
                
                using (var stream = new FileStream(filePath, FileMode.Open))
                {
                    var formatter = new System.Runtime.Serialization.Formatters.Binary.BinaryFormatter();
                    deserializedSettings = (AppSettings)formatter.Deserialize(stream);
                }

                // 验证关键数据
                if (deserializedSettings.System.Name != originalSettings.System.Name)
                    throw new Exception($"System.Name不匹配: {deserializedSettings.System.Name} != {originalSettings.System.Name}");

                if (Math.Abs(deserializedSettings.Motor.LeftFlipPulseEquivalent - originalSettings.Motor.LeftFlipPulseEquivalent) > 0.000001)
                    throw new Exception($"Motor.LeftFlipPulseEquivalent不匹配");

                if (deserializedSettings.Communication.EpsonRobot1IP != originalSettings.Communication.EpsonRobot1IP)
                    throw new Exception($"Communication.EpsonRobot1IP不匹配");

                if (deserializedSettings.Vision.CameraIndex != originalSettings.Vision.CameraIndex)
                    throw new Exception($"Vision.CameraIndex不匹配");

                LogHelper.Info("✓ 反序列化成功，数据验证通过");
            }
            catch (Exception ex)
            {
                throw new Exception($"反序列化失败: {ex.Message}", ex);
            }
        }
    }
}
