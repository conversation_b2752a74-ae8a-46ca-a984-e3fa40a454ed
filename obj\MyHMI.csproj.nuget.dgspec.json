{"format": 1, "restore": {"E:\\projects\\C#_projects\\HR2\\MyHMI.csproj": {}}, "projects": {"E:\\projects\\C#_projects\\HR2\\MyHMI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\projects\\C#_projects\\HR2\\MyHMI.csproj", "projectName": "MyHMI", "projectPath": "E:\\projects\\C#_projects\\HR2\\MyHMI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\projects\\C#_projects\\HR2\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net48": {"dependencies": {"System.IO.Ports": {"target": "Package", "version": "[8.0.0, )"}}}}}}}