using System;
using System.Threading.Tasks;
using MyHMI.Events;
using MyHMI.Models;
using MyHMI.Helpers;

namespace MyHMI.Managers
{
    /// <summary>
    /// 工作流管理器 - 重构后的版本 2.0
    ///
    /// 职责说明:
    /// - 作为工作流协调器，统一管理各种AutoMode控制器
    /// - 不直接控制硬件，而是通过各专门的控制器进行协调
    /// - 实现真正的工作流调度和状态管理功能
    /// - 提供统一的工作流控制接口
    ///
    /// 重构改进:
    /// 1. 将皮带电机控制功能独立到BeltMotorAutoModeController
    /// 2. 实现统一的AutoMode控制器管理
    /// 3. 建立完整的事件驱动架构
    /// 4. 提供真正的工作流协调功能
    ///
    /// 使用方法:
    /// var workflowManager = WorkflowManager.Instance;
    /// await workflowManager.InitializeAsync();
    /// await workflowManager.StartWorkflowAsync("PRODUCT_ID");
    ///
    /// 作者: HR2项目组
    /// 版本: 2.0 (重构版本)
    /// 日期: 2025-09-27
    /// </summary>
    public class WorkflowManager
    {
        #region 单例模式
        private static readonly Lazy<WorkflowManager> _instance = new Lazy<WorkflowManager>(() => new WorkflowManager());
        public static WorkflowManager Instance => _instance.Value;
        private WorkflowManager() { }
        #endregion

        #region 私有字段
        private bool _isInitialized = false;
        private bool _isWorkflowEnabled = true;

        // 工作流状态
        private WorkflowState _currentState = WorkflowState.Idle;
        private WorkflowState _previousState = WorkflowState.Idle; // 用于暂停/恢复功能

        // AutoMode控制器引用
        private BeltMotorAutoModeController _beltMotorController;
        private ScannerAutoModeManager _scannerAutoModeManager;
        private ScaraAutoModeController _scaraController;

        // EpsonRobotAutoModeController - 已修复编译问题
        private EpsonRobotAutoModeController _epsonRobotController;
        #endregion

        #region 公共属性
        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// 当前工作流状态
        /// </summary>
        public WorkflowState CurrentState => _currentState;

        /// <summary>
        /// 工作流是否启用
        /// </summary>
        public bool IsWorkflowEnabled => _isWorkflowEnabled;
        #endregion

        #region 事件定义
        /// <summary>
        /// 工作流状态变化事件
        /// </summary>
        public event EventHandler<WorkflowStateChangedEventArgs> WorkflowStateChanged;

        /// <summary>
        /// 工作流完成事件
        /// </summary>
        public event EventHandler<WorkflowCompletedEventArgs> WorkflowCompleted;

        /// <summary>
        /// 工作流错误事件
        /// </summary>
        public event EventHandler<WorkflowErrorEventArgs> WorkflowError;
        #endregion

        #region 初始化和释放
        /// <summary>
        /// 异步初始化WorkflowManager
        /// </summary>
        /// <returns>初始化结果</returns>
        public async Task<bool> InitializeAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_isInitialized)
                {
                    LogHelper.Warning("WorkflowManager已经初始化，跳过重复初始化");
                    return true;
                }

                LogHelper.Info("开始初始化WorkflowManager...");

                // 初始化AutoMode控制器引用
                await InitializeAutoModeControllersAsync();

                // 订阅各Manager的事件
                await SubscribeToManagerEventsAsync();

                _isInitialized = true;
                LogHelper.Info("WorkflowManager初始化完成");
                return true;

            }, false, "WorkflowManager初始化");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public async Task DisposeAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info("开始释放WorkflowManager资源...");

                // 停止工作流
                await StopWorkflowAsync();

                // 取消订阅事件
                await UnsubscribeFromManagerEventsAsync();

                _isInitialized = false;
                LogHelper.Info("WorkflowManager资源释放完成");

                return true;
            }, false, "WorkflowManager资源释放");
        }
        #endregion

        #region 工作流控制
        /// <summary>
        /// 启动工作流
        /// </summary>
        /// <param name="productId">产品ID</param>
        /// <returns></returns>
        public async Task<bool> StartWorkflowAsync(string productId = "")
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (!_isWorkflowEnabled)
                {
                    LogHelper.Warning("工作流已禁用，无法启动");
                    return false;
                }

                // 智能启动逻辑：如果当前是暂停状态，则发送恢复命令
                if (_currentState == WorkflowState.Paused)
                {
                    LogHelper.Info("检测到暂停状态，执行恢复操作");
                    return await ResumeWorkflowAsync();
                }
                else if (_currentState != WorkflowState.Idle)
                {
                    LogHelper.Warning($"工作流正在运行中，当前状态: {_currentState}");
                    return false;
                }

                LogHelper.Info($"启动工作流（产品ID: {productId}）");

                // 执行工作流启动序列
                bool sequenceResult = await ExecuteWorkflowStartSequenceAsync(productId);
                if (!sequenceResult)
                {
                    LogHelper.Error("工作流启动序列执行失败");
                    ChangeState(WorkflowState.Error);
                    return false;
                }

                LogHelper.Info("工作流启动成功");
                return true;

            }, false, $"启动工作流({productId})");
        }

        /// <summary>
        /// 暂停工作流
        /// </summary>
        /// <returns></returns>
        public async Task<bool> PauseWorkflowAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_currentState == WorkflowState.Idle)
                {
                    LogHelper.Info("工作流处于空闲状态，无需暂停");
                    return true;
                }

                LogHelper.Info("暂停工作流...");

                // 暂停各AutoMode控制器
                await PauseAllAutoModeControllersAsync();

                // 保存当前状态并切换到暂停状态
                _previousState = _currentState;
                ChangeState(WorkflowState.Paused); // 使用Paused状态表示暂停

                LogHelper.Info("工作流暂停完成");
                return true;

            }, false, "暂停工作流");
        }

        /// <summary>
        /// 恢复工作流
        /// </summary>
        /// <returns></returns>
        public async Task<bool> ResumeWorkflowAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_currentState != WorkflowState.Paused)
                {
                    LogHelper.Info("工作流不在暂停状态，无需恢复");
                    return true;
                }

                LogHelper.Info("恢复工作流...");

                // 恢复各AutoMode控制器
                await ResumeAllAutoModeControllersAsync();

                // 恢复到之前的状态
                if (_previousState != WorkflowState.Idle)
                {
                    ChangeState(_previousState);
                }

                LogHelper.Info("工作流恢复完成");
                return true;

            }, false, "恢复工作流");
        }

        /// <summary>
        /// 停止工作流
        /// </summary>
        /// <returns></returns>
        public async Task<bool> StopWorkflowAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_currentState == WorkflowState.Idle)
                {
                    LogHelper.Info("工作流已经处于空闲状态");
                    return true;
                }

                LogHelper.Info("停止工作流...");

                // 停止各AutoMode控制器
                await StopAllAutoModeControllersAsync();

                ChangeState(WorkflowState.Idle);
                LogHelper.Info("工作流停止完成");
                return true;

            }, false, "停止工作流");
        }

        /// <summary>
        /// 重置工作流
        /// </summary>
        /// <returns></returns>
        public async Task<bool> ResetWorkflowAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info("重置工作流...");

                // 停止工作流
                await StopWorkflowAsync();

                // 重置各AutoMode控制器
                await ResetAllAutoModeControllersAsync();

                ChangeState(WorkflowState.Idle);
                LogHelper.Info("工作流重置完成");
                return true;

            }, false, "重置工作流");
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 初始化AutoMode控制器引用
        /// </summary>
        private Task InitializeAutoModeControllersAsync()
        {
            LogHelper.Info("初始化AutoMode控制器引用...");

            // 获取皮带电机控制器实例
            _beltMotorController = BeltMotorAutoModeController.Instance;

            // 获取SCARA自动模式控制器实例
            _scaraController = ScaraAutoModeController.Instance;

            // 获取Epson机器人自动模式控制器实例（已修复编译问题）
            _epsonRobotController = EpsonRobotAutoModeController.Instance;

            // 获取扫码器自动模式管理器实例
            _scannerAutoModeManager = ScannerAutoModeManager.Instance;

            LogHelper.Info("AutoMode控制器引用初始化完成");
            return Task.CompletedTask;
        }

        /// <summary>
        /// 执行工作流完成序列
        /// </summary>
        /// <param name="productId">产品ID</param>
        /// <returns>完成结果</returns>
        private async Task<bool> ExecuteWorkflowCompletionSequenceAsync(string productId)
        {
            try
            {
                LogHelper.Info($"开始执行工作流完成序列（产品ID: {productId}）...");

                // 步骤1: 检查所有控制器状态
                LogHelper.Info("步骤1: 检查所有控制器状态");
                bool statusCheck = await CheckAllControllersStatusAsync();
                if (!statusCheck)
                {
                    LogHelper.Warning("部分控制器状态异常，但继续完成流程");
                }

                // 步骤2: 触发工作流完成事件
                LogHelper.Info("步骤2: 触发工作流完成事件");
                WorkflowCompleted?.Invoke(this, new WorkflowCompletedEventArgs(productId, true, TimeSpan.FromSeconds(0), $"产品{productId}处理完成"));

                // 步骤3: 重置工作流状态
                ChangeState(WorkflowState.Idle);
                LogHelper.Info("步骤3: 工作流状态已重置为空闲");

                LogHelper.Info($"工作流完成序列执行完成（产品ID: {productId}）");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("执行工作流完成序列时发生异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 检查所有控制器状态
        /// </summary>
        /// <returns>状态检查结果</returns>
        private Task<bool> CheckAllControllersStatusAsync()
        {
            try
            {
                bool allHealthy = true;

                // 检查皮带电机控制器状态
                if (_beltMotorController != null)
                {
                    if (_beltMotorController.CurrentState == BeltMotorState.Error)
                    {
                        LogHelper.Warning("皮带电机控制器处于错误状态");
                        allHealthy = false;
                    }
                }

                // 检查扫码器自动模式管理器状态
                if (_scannerAutoModeManager != null)
                {
                    if (!_scannerAutoModeManager.IsAutoModeEnabled)
                    {
                        LogHelper.Warning("扫码器自动模式未启用");
                        allHealthy = false;
                    }
                }

                // TODO: 检查机器人控制器状态
                // if (_epsonRobotController != null)
                // {
                //     if (_epsonRobotController.CurrentState == EpsonAutoModeState.Error)
                //     {
                //         LogHelper.Warning("机器人控制器处于错误状态");
                //         allHealthy = false;
                //     }
                // }

                return Task.FromResult(allHealthy);
            }
            catch (Exception ex)
            {
                LogHelper.Error("检查控制器状态时发生异常", ex);
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// 停止所有AutoMode控制器
        /// </summary>
        private async Task StopAllAutoModeControllersAsync()
        {
            try
            {
                LogHelper.Info("停止所有AutoMode控制器...");

                // 停止皮带电机控制器
                if (_beltMotorController != null)
                {
                    await _beltMotorController.StopAsync();
                }

                // 停止SCARA自动模式控制器
                if (_scaraController != null)
                {
                    await _scaraController.StopAsync();
                }

                // 停止扫码器自动模式管理器
                if (_scannerAutoModeManager != null)
                {
                    await _scannerAutoModeManager.StopAutoModeAsync();
                }

                // 停止6轴机器人自动模式控制器
                if (_epsonRobotController != null)
                {
                    await _epsonRobotController.StopAsync();
                    LogHelper.Info("6轴机器人自动模式控制器已停止");
                }

                LogHelper.Info("所有AutoMode控制器停止完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("停止AutoMode控制器时发生异常", ex);
            }
        }

        /// <summary>
        /// 暂停所有AutoMode控制器
        /// </summary>
        private async Task PauseAllAutoModeControllersAsync()
        {
            try
            {
                LogHelper.Info("暂停所有AutoMode控制器...");

                // 暂停皮带电机控制器（通过停止实现）
                if (_beltMotorController != null && _beltMotorController.IsRunning)
                {
                    await _beltMotorController.StopAsync();
                    LogHelper.Info("皮带电机控制器已暂停");
                }

                // 暂停SCARA自动模式控制器
                if (_scaraController != null && _scaraController.IsRunning)
                {
                    await _scaraController.StopAsync();
                    LogHelper.Info("SCARA自动模式控制器已暂停");
                }

                // 暂停扫码器自动模式管理器
                if (_scannerAutoModeManager != null)
                {
                    await _scannerAutoModeManager.StopAutoModeAsync();
                    LogHelper.Info("扫码器自动模式管理器已暂停");
                }

                // 暂停6轴机器人自动模式控制器
                if (_epsonRobotController != null)
                {
                    await _epsonRobotController.PauseAsync();
                    LogHelper.Info("6轴机器人自动模式控制器已暂停");
                }

                LogHelper.Info("所有AutoMode控制器暂停完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("暂停AutoMode控制器时发生异常", ex);
            }
        }

        /// <summary>
        /// 恢复所有AutoMode控制器
        /// </summary>
        private async Task ResumeAllAutoModeControllersAsync()
        {
            try
            {
                LogHelper.Info("恢复所有AutoMode控制器...");

                // 恢复皮带电机控制器
                if (_beltMotorController != null && !_beltMotorController.IsRunning)
                {
                    await _beltMotorController.StartAsync();
                    LogHelper.Info("皮带电机控制器已恢复");
                }

                // 恢复SCARA自动模式控制器
                if (_scaraController != null && !_scaraController.IsRunning)
                {
                    await _scaraController.StartAsync();
                    LogHelper.Info("SCARA自动模式控制器已恢复");
                }

                // 恢复扫码器自动模式管理器
                if (_scannerAutoModeManager != null)
                {
                    await _scannerAutoModeManager.StartAutoModeAsync();
                    LogHelper.Info("扫码器自动模式管理器已恢复");
                }

                // 恢复6轴机器人自动模式控制器
                if (_epsonRobotController != null)
                {
                    await _epsonRobotController.ResumeAsync();
                    LogHelper.Info("6轴机器人自动模式控制器已恢复");
                }

                LogHelper.Info("所有AutoMode控制器恢复完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("恢复AutoMode控制器时发生异常", ex);
            }
        }

        /// <summary>
        /// 重置所有AutoMode控制器
        /// </summary>
        private async Task ResetAllAutoModeControllersAsync()
        {
            try
            {
                LogHelper.Info("重置所有AutoMode控制器...");

                // 重置皮带电机控制器
                if (_beltMotorController != null)
                {
                    await _beltMotorController.ResetAsync();
                }

                // 重置SCARA自动模式控制器
                if (_scaraController != null)
                {
                    await _scaraController.ResetAsync();
                }

                // 重置扫码器自动模式管理器
                if (_scannerAutoModeManager != null)
                {
                    await _scannerAutoModeManager.StopAutoModeAsync();
                    await _scannerAutoModeManager.StartAutoModeAsync();
                }

                // 重置6轴机器人自动模式控制器
                if (_epsonRobotController != null)
                {
                    await _epsonRobotController.ResetAsync();
                    LogHelper.Info("6轴机器人自动模式控制器已重置");
                }

                LogHelper.Info("所有AutoMode控制器重置完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("重置AutoMode控制器时发生异常", ex);
            }
        }

        /// <summary>
        /// 订阅各Manager的事件
        /// </summary>
        private Task SubscribeToManagerEventsAsync()
        {
            try
            {
                LogHelper.Info("开始订阅各Manager事件...");

                // 订阅皮带电机控制器事件
                if (_beltMotorController != null)
                {
                    _beltMotorController.StateChanged += OnBeltMotorStateChanged;
                    _beltMotorController.ErrorOccurred += OnBeltMotorErrorOccurred;
                    LogHelper.Info("已订阅皮带电机控制器事件");
                }

                // 订阅扫码器自动模式管理器事件
                if (_scannerAutoModeManager != null)
                {
                    _scannerAutoModeManager.AllScannersCompleted += OnAllScannersCompleted;
                    _scannerAutoModeManager.AutoModeStatusChanged += OnScannerAutoModeStatusChanged;
                    LogHelper.Info("已订阅扫码器自动模式管理器事件");
                }

                // 订阅EpsonRobotAutoModeController事件（暂时注释，待添加事件处理方法）
                // if (_epsonRobotController != null)
                // {
                //     _epsonRobotController.StateChanged += OnEpsonRobotStateChanged;
                //     _epsonRobotController.WorkflowCompleted += OnEpsonRobotWorkflowCompleted;
                //     LogHelper.Info("已订阅Epson机器人自动模式控制器事件");
                // }

                LogHelper.Info("Manager事件订阅完成");
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                LogHelper.Error("订阅Manager事件时发生异常", ex);
                return Task.CompletedTask;
            }
        }

        /// <summary>
        /// 取消订阅各Manager的事件
        /// </summary>
        private Task UnsubscribeFromManagerEventsAsync()
        {
            try
            {
                LogHelper.Info("开始取消订阅各Manager事件...");

                // 取消订阅皮带电机控制器事件
                if (_beltMotorController != null)
                {
                    _beltMotorController.StateChanged -= OnBeltMotorStateChanged;
                    _beltMotorController.ErrorOccurred -= OnBeltMotorErrorOccurred;
                    LogHelper.Info("已取消订阅皮带电机控制器事件");
                }

                // 取消订阅扫码器自动模式管理器事件
                if (_scannerAutoModeManager != null)
                {
                    _scannerAutoModeManager.AllScannersCompleted -= OnAllScannersCompleted;
                    _scannerAutoModeManager.AutoModeStatusChanged -= OnScannerAutoModeStatusChanged;
                    LogHelper.Info("已取消订阅扫码器自动模式管理器事件");
                }

                // TODO: 取消订阅EpsonRobotAutoModeController事件
                // if (_epsonRobotController != null)
                // {
                //     _epsonRobotController.StateChanged -= OnEpsonRobotStateChanged;
                //     _epsonRobotController.WorkflowCompleted -= OnEpsonRobotWorkflowCompleted;
                // }

                LogHelper.Info("Manager事件取消订阅完成");
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                LogHelper.Error("取消订阅Manager事件时发生异常", ex);
                return Task.CompletedTask;
            }
        }

        /// <summary>
        /// 执行工作流启动序列
        /// </summary>
        /// <param name="productId">产品ID</param>
        /// <returns>启动结果</returns>
        private async Task<bool> ExecuteWorkflowStartSequenceAsync(string productId)
        {
            try
            {
                LogHelper.Info("开始执行工作流启动序列...");

                // 步骤1: 初始化所有AutoMode控制器
                ChangeState(WorkflowState.WaitingForScan);
                LogHelper.Info("步骤1: 初始化AutoMode控制器");
                bool initResult = await InitializeAllAutoModeControllersAsync();
                if (!initResult)
                {
                    LogHelper.Error("AutoMode控制器初始化失败");
                    return false;
                }

                // 步骤2: 启动扫码器自动模式
                LogHelper.Info("步骤2: 启动扫码器自动模式");
                if (_scannerAutoModeManager != null)
                {
                    bool scannerStartResult = await _scannerAutoModeManager.StartAutoModeAsync();
                    if (!scannerStartResult)
                    {
                        LogHelper.Error("扫码器自动模式启动失败");
                        return false;
                    }
                }

                // 步骤3: 启动皮带电机控制器
                ChangeState(WorkflowState.MotorMoving);
                LogHelper.Info("步骤3: 启动皮带电机控制器");
                if (_beltMotorController != null)
                {
                    bool beltStartResult = await _beltMotorController.StartAsync();
                    if (!beltStartResult)
                    {
                        LogHelper.Error("皮带电机控制器启动失败");
                        return false;
                    }
                }

                // 步骤4: 启动SCARA自动模式控制器
                LogHelper.Info("步骤4: 启动SCARA自动模式控制器");
                if (_scaraController != null)
                {
                    bool scaraStartResult = await _scaraController.StartAsync();
                    if (!scaraStartResult)
                    {
                        LogHelper.Error("SCARA自动模式控制器启动失败");
                        return false;
                    }
                    LogHelper.Info("SCARA自动模式控制器启动成功");
                }

                // 步骤5: 启动6轴机器人控制器
                LogHelper.Info("步骤5: 启动6轴机器人控制器");
                if (_epsonRobotController != null)
                {
                    bool robotStartResult = await _epsonRobotController.StartAsync();
                    if (!robotStartResult)
                    {
                        LogHelper.Error("6轴机器人控制器启动失败");
                        return false;
                    }
                    LogHelper.Info("6轴机器人控制器启动成功");
                }

                // 步骤6: 等待系统稳定
                LogHelper.Info("步骤6: 等待系统稳定");
                await Task.Delay(1000); // 等待1秒让系统稳定

                ChangeState(WorkflowState.RobotOperating);
                LogHelper.Info("工作流启动序列执行完成");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("执行工作流启动序列时发生异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 初始化所有AutoMode控制器
        /// </summary>
        /// <returns>初始化结果</returns>
        private async Task<bool> InitializeAllAutoModeControllersAsync()
        {
            try
            {
                LogHelper.Info("初始化所有AutoMode控制器...");

                // 初始化皮带电机控制器
                if (_beltMotorController != null && !_beltMotorController.IsInitialized)
                {
                    bool beltInitResult = await _beltMotorController.InitializeAsync();
                    if (!beltInitResult)
                    {
                        LogHelper.Error("皮带电机控制器初始化失败");
                        return false;
                    }
                }

                // 初始化扫码器自动模式管理器
                if (_scannerAutoModeManager != null && !_scannerAutoModeManager.IsInitialized)
                {
                    bool scannerInitResult = await _scannerAutoModeManager.InitializeAsync();
                    if (!scannerInitResult)
                    {
                        LogHelper.Error("扫码器自动模式管理器初始化失败");
                        return false;
                    }
                }

                // 初始化SCARA自动模式控制器
                if (_scaraController != null && !_scaraController.IsInitialized)
                {
                    bool scaraInitResult = await _scaraController.InitializeAsync();
                    if (!scaraInitResult)
                    {
                        LogHelper.Error("SCARA自动模式控制器初始化失败");
                        return false;
                    }
                }

                // 初始化6轴机器人控制器
                if (_epsonRobotController != null)
                {
                    bool robotInitResult = await _epsonRobotController.InitializeAsync();
                    if (!robotInitResult)
                    {
                        LogHelper.Error("6轴机器人控制器初始化失败");
                        return false;
                    }
                }

                LogHelper.Info("所有AutoMode控制器初始化完成");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("初始化AutoMode控制器时发生异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 改变工作流状态
        /// </summary>
        /// <param name="newState">新状态</param>
        private void ChangeState(WorkflowState newState)
        {
            WorkflowState oldState = _currentState;
            _currentState = newState;

            if (oldState != newState)
            {
                LogHelper.Info($"工作流状态变更: {oldState} -> {newState}");
                WorkflowStateChanged?.Invoke(this, new WorkflowStateChangedEventArgs(oldState, newState, ""));
            }
        }
        #endregion

        #region 事件处理方法
        /// <summary>
        /// 处理皮带电机状态变化事件
        /// </summary>
        private void OnBeltMotorStateChanged(object sender, BeltMotorStateChangedEventArgs e)
        {
            try
            {
                LogHelper.Info($"皮带电机状态变化: {e.OldState} -> {e.NewState}");

                // 根据皮带电机状态调整工作流状态
                if (e.NewState == BeltMotorState.Error)
                {
                    ChangeState(WorkflowState.Error);
                }
                else if (e.NewState == BeltMotorState.Running)
                {
                    ChangeState(WorkflowState.MotorMoving);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理皮带电机状态变化事件时发生异常", ex);
            }
        }

        /// <summary>
        /// 处理皮带电机错误事件
        /// </summary>
        private void OnBeltMotorErrorOccurred(object sender, BeltMotorErrorEventArgs e)
        {
            try
            {
                LogHelper.Error($"皮带电机发生错误: {e.ErrorMessage}");
                ChangeState(WorkflowState.Error);

                // 触发工作流错误事件
                WorkflowError?.Invoke(this, new WorkflowErrorEventArgs("", "BeltMotorError", $"皮带电机错误: {e.ErrorMessage}"));
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理皮带电机错误事件时发生异常", ex);
            }
        }

        /// <summary>
        /// 处理所有扫码器完成事件
        /// </summary>
        private async void OnAllScannersCompleted(object sender, AllScannersCompletedEventArgs e)
        {
            try
            {
                LogHelper.Info("所有扫码器扫码完成");
                ChangeState(WorkflowState.VisionDetecting);

                // 触发工作流完成序列
                string productId = DateTime.Now.ToString("yyyyMMddHHmmss"); // 生成产品ID
                bool completionResult = await ExecuteWorkflowCompletionSequenceAsync(productId);
                if (!completionResult)
                {
                    LogHelper.Error("工作流完成序列执行失败");
                    ChangeState(WorkflowState.Error);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理扫码器完成事件时发生异常", ex);
                ChangeState(WorkflowState.Error);
            }
        }

        /// <summary>
        /// 处理扫码器自动模式状态变化事件
        /// </summary>
        private void OnScannerAutoModeStatusChanged(object sender, AutoModeStatusChangedEventArgs e)
        {
            try
            {
                LogHelper.Info($"扫码器自动模式状态变化: {(e.IsEnabled ? "启用" : "禁用")} - {e.Message}");

                if (!e.IsEnabled)
                {
                    // 扫码器自动模式被禁用，可能需要调整工作流状态
                    LogHelper.Warning("扫码器自动模式已禁用，工作流可能受到影响");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理扫码器自动模式状态变化事件时发生异常", ex);
            }
        }
        #endregion
    }
}
