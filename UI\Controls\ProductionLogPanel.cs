using System;
using System.Drawing;
using System.Windows.Forms;
using MyHMI.Helpers;

namespace MyHMI.UI.Controls
{
    /// <summary>
    /// 生产日志管理面板 - 按HTML原型设计
    /// </summary>
    public partial class ProductionLogPanel : UserControl
    {
        #region 私有字段
        
        private Panel _mainPanel;
        private Label _titleLabel;
        private Panel _timeSelectionGroup;
        private Panel _dataOperationGroup;
        private Panel _statisticsGroup;
        
        #endregion

        #region 构造函数
        
        public ProductionLogPanel()
        {
            InitializeComponent();
            InitializeInterface();
        }
        
        #endregion

        #region 界面初始化
        
        /// <summary>
        /// 初始化界面 - 按HTML原型精确设计
        /// </summary>
        private void InitializeInterface()
        {
            try
            {
                // 设置面板属性 - 按HTML原型 660x840 (700-40padding)
                this.Size = new Size(660, 840);
                this.BackColor = ColorTranslator.FromHtml("#34495e");
                this.Dock = DockStyle.Fill;
                this.Padding = new Padding(0);

                // 创建主面板 - 按HTML原型padding: 20px
                CreateMainPanel();
                
                // 创建标题 - 按HTML原型样式
                CreateTitle();
                
                // 创建时间选择组 - 按HTML原型样式
                CreateTimeSelectionGroup();
                
                // 创建数据操作组 - 按HTML原型样式
                CreateDataOperationGroup();
                
                // 创建生产统计组 - 按HTML原型样式
                CreateStatisticsGroup();

                LogHelper.Info("生产日志管理面板初始化完成 - 按HTML原型设计");
            }
            catch (Exception ex)
            {
                LogHelper.Error("生产日志管理面板初始化失败", ex);
            }
        }
        
        /// <summary>
        /// 创建主面板 - 按HTML原型
        /// </summary>
        private void CreateMainPanel()
        {
            _mainPanel = new Panel
            {
                Size = new Size(660, 840),
                Location = new Point(0, 0),
                BackColor = ColorTranslator.FromHtml("#34495e"),
                Padding = new Padding(20), // 按HTML原型 padding: 20px
                Dock = DockStyle.Fill
            };
            
            this.Controls.Add(_mainPanel);
        }
        
        /// <summary>
        /// 创建标题 - 按HTML原型样式
        /// </summary>
        private void CreateTitle()
        {
            _titleLabel = new Label
            {
                Text = "生产日志管理",
                Font = new Font("微软雅黑", 20F, FontStyle.Bold), // 按HTML原型 font-size: 20px
                ForeColor = ColorTranslator.FromHtml("#3498db"),   // 按HTML原型 color: #3498db
                Size = new Size(300, 30),
                Location = new Point(0, 0),
                AutoSize = true
            };
            
            _mainPanel.Controls.Add(_titleLabel);
        }
        
        /// <summary>
        /// 创建时间选择组 - 按HTML原型样式
        /// </summary>
        private void CreateTimeSelectionGroup()
        {
            _timeSelectionGroup = new Panel
            {
                Size = new Size(600, 120), // 调整尺寸确保不超出功能区域
                Location = new Point(0, 50), // 标题下方20px间距
                BackColor = ColorTranslator.FromHtml("#2c3e50"), // 按HTML原型
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15) // 按HTML原型 padding
            };
            
            // 设置边框颜色 - 按HTML原型 border: 1px solid #4a5661
            _timeSelectionGroup.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _timeSelectionGroup.Width - 1, _timeSelectionGroup.Height - 1);
                }
            };
            
            // 创建组标题 - 按HTML原型 h3
            var groupTitle = new Label
            {
                Text = "时间选择",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold), // 按HTML原型 h3
                ForeColor = Color.White,
                Size = new Size(200, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };
            
            // 时间选择按钮 - 按HTML原型 .btn .btn-primary
            var btnDaily = CreateButton("按日查看", new Point(0, 35), ColorTranslator.FromHtml("#3498db"));
            var btnWeekly = CreateButton("按周查看", new Point(120, 35), ColorTranslator.FromHtml("#3498db"));
            var btnMonthly = CreateButton("按月查看", new Point(240, 35), ColorTranslator.FromHtml("#3498db"));
            
            _timeSelectionGroup.Controls.Add(groupTitle);
            _timeSelectionGroup.Controls.Add(btnDaily);
            _timeSelectionGroup.Controls.Add(btnWeekly);
            _timeSelectionGroup.Controls.Add(btnMonthly);
            
            _mainPanel.Controls.Add(_timeSelectionGroup);
        }
        
        /// <summary>
        /// 创建数据操作组 - 按HTML原型样式
        /// </summary>
        private void CreateDataOperationGroup()
        {
            _dataOperationGroup = new Panel
            {
                Size = new Size(600, 120), // 调整尺寸确保不超出功能区域
                Location = new Point(0, 190), // 时间选择组下方20px间距
                BackColor = ColorTranslator.FromHtml("#2c3e50"), // 按HTML原型
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15) // 按HTML原型 padding
            };
            
            // 设置边框颜色 - 按HTML原型 border: 1px solid #4a5661
            _dataOperationGroup.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _dataOperationGroup.Width - 1, _dataOperationGroup.Height - 1);
                }
            };
            
            // 创建组标题 - 按HTML原型 h3
            var groupTitle = new Label
            {
                Text = "数据操作",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold), // 按HTML原型 h3
                ForeColor = Color.White,
                Size = new Size(200, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };
            
            // 数据操作按钮 - 按HTML原型 .btn .btn-success .btn-danger
            var btnExport = CreateButton("数据导出", new Point(0, 35), ColorTranslator.FromHtml("#27ae60"));
            var btnDelete = CreateButton("数据删除", new Point(120, 35), ColorTranslator.FromHtml("#e74c3c"));
            
            _dataOperationGroup.Controls.Add(groupTitle);
            _dataOperationGroup.Controls.Add(btnExport);
            _dataOperationGroup.Controls.Add(btnDelete);
            
            _mainPanel.Controls.Add(_dataOperationGroup);
        }
        
        /// <summary>
        /// 创建生产统计组 - 按HTML原型样式
        /// </summary>
        private void CreateStatisticsGroup()
        {
            _statisticsGroup = new Panel
            {
                Size = new Size(600, 150), // 调整尺寸确保不超出功能区域
                Location = new Point(0, 330), // 数据操作组下方20px间距
                BackColor = ColorTranslator.FromHtml("#2c3e50"), // 按HTML原型
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15) // 按HTML原型 padding
            };
            
            // 设置边框颜色 - 按HTML原型 border: 1px solid #4a5661
            _statisticsGroup.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _statisticsGroup.Width - 1, _statisticsGroup.Height - 1);
                }
            };
            
            // 创建组标题 - 按HTML原型 h3
            var groupTitle = new Label
            {
                Text = "生产统计",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold), // 按HTML原型 h3
                ForeColor = Color.White,
                Size = new Size(200, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };
            
            // 统计数据显示 - 按HTML原型 display: flex; gap: 20px
            CreateStatisticsItem("总产量:", "1234", ColorTranslator.FromHtml("#3498db"), new Point(0, 35));
            CreateStatisticsItem("OK量:", "1200", ColorTranslator.FromHtml("#27ae60"), new Point(0, 65));
            CreateStatisticsItem("NG量:", "34", ColorTranslator.FromHtml("#e74c3c"), new Point(0, 95));
            CreateStatisticsItem("良品率:", "97.2%", ColorTranslator.FromHtml("#3498db"), new Point(300, 35));
            
            _statisticsGroup.Controls.Add(groupTitle);
            _mainPanel.Controls.Add(_statisticsGroup);
        }
        
        /// <summary>
        /// 创建统计项目 - 按HTML原型样式
        /// </summary>
        private void CreateStatisticsItem(string labelText, string value, Color valueColor, Point location)
        {
            var label = new Label
            {
                Text = labelText,
                Font = new Font("微软雅黑", 14F), // 按HTML原型
                ForeColor = Color.White,
                Size = new Size(80, 25),
                Location = location,
                TextAlign = ContentAlignment.MiddleLeft
            };
            
            var valueLabel = new Label
            {
                Text = value,
                Font = new Font("微软雅黑", 14F, FontStyle.Bold), // 按HTML原型 font-weight: bold
                ForeColor = valueColor,
                Size = new Size(100, 25),
                Location = new Point(location.X + 85, location.Y),
                TextAlign = ContentAlignment.MiddleLeft
            };
            
            _statisticsGroup.Controls.Add(label);
            _statisticsGroup.Controls.Add(valueLabel);
        }
        
        /// <summary>
        /// 创建按钮 - 按HTML原型样式
        /// </summary>
        private Button CreateButton(string text, Point location, Color backColor)
        {
            var button = new Button
            {
                Text = text,
                Location = location,
                Size = new Size(100, 35), // 按HTML原型调整按钮尺寸
                BackColor = backColor,
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 14F, FontStyle.Bold), // 按HTML原型字体
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false,
                Cursor = Cursors.Hand
            };
            
            // 设置按钮边框 - 按HTML原型
            button.FlatAppearance.BorderSize = 1;
            button.FlatAppearance.BorderColor = ColorTranslator.FromHtml("#4a5661");
            
            return button;
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 更新统计数据
        /// </summary>
        public void UpdateStatistics(int total, int ok, int ng, double efficiency)
        {
            try
            {
                // 这里可以添加更新统计数据的逻辑
                LogHelper.Info($"更新生产统计 - 总产量:{total}, OK:{ok}, NG:{ng}, 良品率:{efficiency:F1}%");
            }
            catch (Exception ex)
            {
                LogHelper.Error("更新生产统计失败", ex);
            }
        }
        
        #endregion
    }
}
