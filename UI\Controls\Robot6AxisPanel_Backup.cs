using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using MyHMI.Helpers;
using MyHMI.Managers;
using MyHMI.Models;
using MyHMI.Events;

namespace MyHMI.UI.Controls
{
    /// <summary>
    /// 6轴机器人控制面板 - Epson机器人控制
    /// </summary>
    public partial class Robot6AxisPanel : UserControl
    {
        #region 私有字段

        private Panel _mainPanel;
        private Label _titleLabel;
        private Panel _commGroup;
        private Panel _controlGroup;
        private Panel _dataGroup;
        private Panel _monitorGroup;

        // Epson机器人管理器
        private EpsonRobotManager _epsonRobotManager;

        // 启动/停止IP控件
        private TextBox _startStopIPTextBox;
        private TextBox _startStopPortTextBox;
        private Button _startButton;
        private Button _stopButton;

        // 数据收发IP控件
        private TextBox _dataIPTextBox;
        private TextBox _dataPortTextBox;

        // 数据输入发送控件
        private TextBox _commandInputTextBox;
        private Button _sendButton;

        // 通讯监听控件
        private TextBox _sendDataTextBox;
        private TextBox _receiveDataTextBox;
        private Label _connectionStatusLabel;
        private Label _automationStatusLabel;

        #endregion

        #region 构造函数

        public Robot6AxisPanel()
        {
            InitializeComponent();
            InitializeEpsonRobotManager();
            InitializeInterface();
        }



        #endregion

        #region 界面初始化

        /// <summary>
        /// 初始化界面 - 按HTML原型精确设计
        /// </summary>
        private void InitializeInterface()
        {
            try
            {
                // 设置面板属性 - 按HTML原型 660x840 (700-40padding)
                this.Size = new Size(660, 840);
                this.BackColor = ColorTranslator.FromHtml("#34495e");
                this.Dock = DockStyle.Fill;
                this.Padding = new Padding(0);

                // 创建主面板 - 按HTML原型padding: 20px
                CreateMainPanel();

                // 创建标题 - 按HTML原型样式
                CreateTitle();

                // 创建通讯设置组 - 按HTML原型样式
                CreateCommGroup();

                // 创建控制操作组 - 按HTML原型样式
                CreateControlGroup();

                // 创建数据发送组 - 按HTML原型样式
                CreateDataGroup();

                // 创建通信监控组 - 按HTML原型样式
                CreateMonitorGroup();

                LogHelper.Info("6轴机器人控制面板初始化完成 - 按HTML原型设计");
            }
            catch (Exception ex)
            {
                LogHelper.Error("6轴机器人控制面板初始化失败", ex);
            }
        }

        /// <summary>
        /// 创建主面板 - 按HTML原型
        /// </summary>
        private void CreateMainPanel()
        {
            _mainPanel = new Panel
            {
                Size = new Size(660, 840),
                Location = new Point(0, 0),
                BackColor = ColorTranslator.FromHtml("#34495e"),
                Padding = new Padding(20), // 按HTML原型 padding: 20px
                Dock = DockStyle.Fill
            };

            this.Controls.Add(_mainPanel);
        }

        /// <summary>
        /// 创建标题 - 按HTML原型样式
        /// </summary>
        private void CreateTitle()
        {
            _titleLabel = new Label
            {
                Text = "6轴机器人控制",
                Font = new Font("微软雅黑", 20F, FontStyle.Bold), // 按HTML原型 font-size: 20px
                ForeColor = ColorTranslator.FromHtml("#3498db"),   // 按HTML原型 color: #3498db
                Size = new Size(300, 30),
                Location = new Point(0, 0),
                AutoSize = true
            };

            _mainPanel.Controls.Add(_titleLabel);
        }

        /// <summary>
        /// 创建通讯设置组 - Epson机器人双IP配置
        /// </summary>
        private void CreateCommGroup()
        {
            _commGroup = new Panel
            {
                Size = new Size(600, 180), // 增加高度以容纳两组IP配置
                Location = new Point(0, 50), // 标题下方20px间距
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15)
            };

            // 设置边框颜色
            _commGroup.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _commGroup.Width - 1, _commGroup.Height - 1);
                }
            };

            // 创建组标题
            var groupTitle = new Label
            {
                Text = "通讯设置",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(200, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };

            // 启动/停止IP配置
            CreateIPConfigGroup(_commGroup, "启动/停止IP:", "*************", "5000", 35,
                out _startStopIPTextBox, out _startStopPortTextBox);

            // 数据收发IP配置
            CreateIPConfigGroup(_commGroup, "数据收发IP:", "*************", "5001", 95,
                out _dataIPTextBox, out _dataPortTextBox);

            // 连接状态显示
            var statusLabel = new Label
            {
                Text = "连接状态:",
                Font = new Font("微软雅黑", 14F),
                ForeColor = Color.White,
                Size = new Size(100, 20),
                Location = new Point(0, 135),
                AutoSize = true
            };

            _connectionStatusLabel = new Label
            {
                Text = "未连接",
                Font = new Font("微软雅黑", 14F),
                ForeColor = ColorTranslator.FromHtml("#e74c3c"), // 红色表示未连接
                Size = new Size(200, 20),
                Location = new Point(100, 135),
                AutoSize = true
            };

            _commGroup.Controls.Add(groupTitle);
            _commGroup.Controls.Add(statusLabel);
            _commGroup.Controls.Add(_connectionStatusLabel);
            _mainPanel.Controls.Add(_commGroup);
        }

        /// <summary>
        /// 创建IP配置组
        /// </summary>
        /// <param name="parent">父控件</param>
        /// <param name="labelText">标签文本</param>
        /// <param name="defaultIP">默认IP</param>
        /// <param name="defaultPort">默认端口</param>
        /// <param name="yOffset">Y偏移</param>
        /// <param name="ipTextBox">IP输入框</param>
        /// <param name="portTextBox">端口输入框</param>
        private void CreateIPConfigGroup(Panel parent, string labelText, string defaultIP, string defaultPort,
            int yOffset, out TextBox ipTextBox, out TextBox portTextBox)
        {
            // 标签
            var label = new Label
            {
                Text = labelText,
                Font = new Font("微软雅黑", 14F),
                ForeColor = Color.White,
                Size = new Size(120, 30),
                Location = new Point(0, yOffset),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // IP地址输入框
            ipTextBox = new TextBox
            {
                Text = defaultIP,
                Font = new Font("微软雅黑", 12F),
                Size = new Size(120, 25),
                Location = new Point(130, yOffset + 2),
                BackColor = ColorTranslator.FromHtml("#34495e"),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // 端口标签
            var portLabel = new Label
            {
                Text = "端口:",
                Font = new Font("微软雅黑", 12F),
                ForeColor = Color.White,
                Size = new Size(40, 25),
                Location = new Point(260, yOffset + 2),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // 端口输入框
            portTextBox = new TextBox
            {
                Text = defaultPort,
                Font = new Font("微软雅黑", 12F),
                Size = new Size(80, 25),
                Location = new Point(300, yOffset + 2),
                BackColor = ColorTranslator.FromHtml("#34495e"),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            parent.Controls.Add(label);
            parent.Controls.Add(ipTextBox);
            parent.Controls.Add(portLabel);
            parent.Controls.Add(portTextBox);
        }

        /// <summary>
        /// 创建控制操作组 - Epson机器人启动停止控制
        /// </summary>
        private void CreateControlGroup()
        {
            _controlGroup = new Panel
            {
                Size = new Size(600, 120), // 增加高度以容纳自动化状态显示
                Location = new Point(0, 250), // 调整位置
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15)
            };

            // 设置边框颜色
            _controlGroup.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _controlGroup.Width - 1, _controlGroup.Height - 1);
                }
            };

            // 创建组标题
            var groupTitle = new Label
            {
                Text = "控制操作",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(200, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };

            // 创建启动按钮
            _startButton = CreateControlButton("启动", new Point(0, 35), ColorTranslator.FromHtml("#27ae60"));
            _startButton.Click += StartButton_Click;

            // 创建停止按钮
            _stopButton = CreateControlButton("停止", new Point(120, 35), ColorTranslator.FromHtml("#e74c3c"));
            _stopButton.Click += StopButton_Click;

            // 自动化状态显示
            var automationLabel = new Label
            {
                Text = "自动化状态:",
                Font = new Font("微软雅黑", 14F),
                ForeColor = Color.White,
                Size = new Size(100, 20),
                Location = new Point(0, 80),
                AutoSize = true
            };

            _automationStatusLabel = new Label
            {
                Text = "未启动",
                Font = new Font("微软雅黑", 14F),
                ForeColor = ColorTranslator.FromHtml("#95a5a6"), // 灰色表示未启动
                Size = new Size(400, 20),
                Location = new Point(100, 80),
                AutoSize = true
            };

            _controlGroup.Controls.Add(groupTitle);
            _controlGroup.Controls.Add(_startButton);
            _controlGroup.Controls.Add(_stopButton);
            _controlGroup.Controls.Add(automationLabel);
            _controlGroup.Controls.Add(_automationStatusLabel);

            _mainPanel.Controls.Add(_controlGroup);
        }

        /// <summary>
        /// 创建数据发送组 - Epson机器人命令发送
        /// </summary>
        private void CreateDataGroup()
        {
            _dataGroup = new Panel
            {
                Size = new Size(600, 80),
                Location = new Point(0, 390), // 调整位置
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15)
            };

            // 设置边框颜色
            _dataGroup.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _dataGroup.Width - 1, _dataGroup.Height - 1);
                }
            };

            // 创建组标题
            var groupTitle = new Label
            {
                Text = "数据发送",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(200, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };

            // 指令输入
            var label = new Label
            {
                Text = "指令:",
                Font = new Font("微软雅黑", 14F),
                ForeColor = Color.White,
                Size = new Size(60, 30),
                Location = new Point(0, 35),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _commandInputTextBox = new TextBox
            {
                Font = new Font("微软雅黑", 12F),
                Size = new Size(250, 25),
                Location = new Point(70, 37),
                BackColor = ColorTranslator.FromHtml("#34495e"),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Text = "" // 用户输入命令
            };

            _sendButton = new Button
            {
                Text = "发送",
                Font = new Font("微软雅黑", 12F),
                Size = new Size(80, 25),
                Location = new Point(330, 37),
                BackColor = ColorTranslator.FromHtml("#3498db"),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false,
                Cursor = Cursors.Hand
            };
            _sendButton.Click += SendButton_Click;

            _dataGroup.Controls.Add(groupTitle);
            _dataGroup.Controls.Add(label);
            _dataGroup.Controls.Add(_commandInputTextBox);
            _dataGroup.Controls.Add(_sendButton);

            _mainPanel.Controls.Add(_dataGroup);
        }

        /// <summary>
        /// 创建通信监控组 - Epson机器人通信监听
        /// </summary>
        private void CreateMonitorGroup()
        {
            _monitorGroup = new Panel
            {
                Size = new Size(600, 200),
                Location = new Point(0, 490), // 调整位置
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15)
            };

            // 设置边框颜色
            _monitorGroup.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _monitorGroup.Width - 1, _monitorGroup.Height - 1);
                }
            };

            // 创建组标题
            var groupTitle = new Label
            {
                Text = "通信监控",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(200, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };

            // 创建发送数据区域
            var sendLabel = new Label
            {
                Text = "发送数据:",
                Font = new Font("微软雅黑", 12F),
                ForeColor = Color.White,
                Size = new Size(100, 20),
                Location = new Point(0, 35),
                AutoSize = true
            };

            _sendDataTextBox = new TextBox
            {
                Font = new Font("Consolas", 10F),
                Size = new Size(270, 100),
                Location = new Point(0, 55),
                BackColor = ColorTranslator.FromHtml("#1e1e1e"),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                Text = "" // 实时显示发送的数据
            };

            // 创建接收数据区域
            var receiveLabel = new Label
            {
                Text = "接收数据:",
                Font = new Font("微软雅黑", 12F),
                ForeColor = Color.White,
                Size = new Size(100, 20),
                Location = new Point(290, 35),
                AutoSize = true
            };

            _receiveDataTextBox = new TextBox
            {
                Font = new Font("Consolas", 10F),
                Size = new Size(270, 100),
                Location = new Point(290, 55),
                BackColor = ColorTranslator.FromHtml("#1e1e1e"),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                Text = "" // 实时显示接收的数据
            };

            _monitorGroup.Controls.Add(groupTitle);
            _monitorGroup.Controls.Add(sendLabel);
            _monitorGroup.Controls.Add(_sendDataTextBox);
            _monitorGroup.Controls.Add(receiveLabel);
            _monitorGroup.Controls.Add(_receiveDataTextBox);

            _mainPanel.Controls.Add(_monitorGroup);
        }

        /// <summary>
        /// 创建控制按钮 - 按HTML原型样式
        /// </summary>
        private Button CreateControlButton(string text, Point location, Color backColor)
        {
            var button = new Button
            {
                Text = text,
                Location = location,
                Size = new Size(100, 35), // 按HTML原型调整按钮尺寸
                BackColor = backColor,
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 14F, FontStyle.Bold), // 按HTML原型字体
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false,
                Cursor = Cursors.Hand
            };

            // 设置按钮边框 - 按HTML原型
            button.FlatAppearance.BorderSize = 1;
            button.FlatAppearance.BorderColor = ColorTranslator.FromHtml("#4a5661");

            return button;
        }

        #endregion

        #region Epson机器人管理器初始化和事件处理

        /// <summary>
        /// 初始化Epson机器人管理器
        /// </summary>
        private async void InitializeEpsonRobotManager()
        {
            try
            {
                _epsonRobotManager = EpsonRobotManager.Instance;

                // 订阅事件
                SubscribeToEpsonRobotEvents();

                // 初始化管理器
                await _epsonRobotManager.InitializeAsync();

                LogHelper.Info("Epson机器人管理器初始化完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("初始化Epson机器人管理器失败", ex);
                MessageBox.Show($"初始化Epson机器人管理器失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 订阅Epson机器人事件
        /// </summary>
        private void SubscribeToEpsonRobotEvents()
        {
            if (_epsonRobotManager != null)
            {
                _epsonRobotManager.ConnectionStatusChanged += OnConnectionStatusChanged;
                _epsonRobotManager.ResponseReceived += OnResponseReceived;
                _epsonRobotManager.CommandSent += OnCommandSent;
                _epsonRobotManager.RobotStarted += OnRobotStarted;
                _epsonRobotManager.SpecialCommandReceived += OnSpecialCommandReceived;
                _epsonRobotManager.AutomationStatusChanged += OnAutomationStatusChanged;
                _epsonRobotManager.RobotError += OnRobotError;
            }
        }

        /// <summary>
        /// 取消订阅Epson机器人事件
        /// </summary>
        private void UnsubscribeFromEpsonRobotEvents()
        {
            if (_epsonRobotManager != null)
            {
                _epsonRobotManager.ConnectionStatusChanged -= OnConnectionStatusChanged;
                _epsonRobotManager.ResponseReceived -= OnResponseReceived;
                _epsonRobotManager.CommandSent -= OnCommandSent;
                _epsonRobotManager.RobotStarted -= OnRobotStarted;
                _epsonRobotManager.SpecialCommandReceived -= OnSpecialCommandReceived;
                _epsonRobotManager.AutomationStatusChanged -= OnAutomationStatusChanged;
                _epsonRobotManager.RobotError -= OnRobotError;
            }
        }

        #endregion

        #region 事件处理方法

        /// <summary>
        /// 连接状态变化事件处理
        /// </summary>
        private void OnConnectionStatusChanged(object sender, EpsonRobotConnectionStatusChangedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnConnectionStatusChanged(sender, e)));
                return;
            }

            try
            {
                string statusText = "";
                Color statusColor = Color.White;

                switch (e.Status)
                {
                    case CommunicationStatus.Connected:
                        statusText = $"{e.ConnectionType}已连接";
                        statusColor = ColorTranslator.FromHtml("#27ae60"); // 绿色
                        break;
                    case CommunicationStatus.Connecting:
                        statusText = $"{e.ConnectionType}连接中...";
                        statusColor = ColorTranslator.FromHtml("#f39c12"); // 橙色
                        break;
                    case CommunicationStatus.Disconnected:
                        statusText = $"{e.ConnectionType}已断开";
                        statusColor = ColorTranslator.FromHtml("#e74c3c"); // 红色
                        break;
                    case CommunicationStatus.Error:
                        statusText = $"{e.ConnectionType}连接错误";
                        statusColor = ColorTranslator.FromHtml("#e74c3c"); // 红色
                        break;
                }

                _connectionStatusLabel.Text = statusText;
                _connectionStatusLabel.ForeColor = statusColor;

                LogHelper.Info($"连接状态变化: {statusText} - {e.Description}");
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理连接状态变化事件失败", ex);
            }
        }

        /// <summary>
        /// 响应接收事件处理
        /// </summary>
        private void OnResponseReceived(object sender, EpsonRobotResponseReceivedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnResponseReceived(sender, e)));
                return;
            }

            try
            {
                string timestamp = DateTime.Now.ToString("HH:mm:ss");
                string message = $"[{timestamp}] 接收({e.ConnectionType}): {e.Response.RawResponse}\r\n";

                _receiveDataTextBox.AppendText(message);
                _receiveDataTextBox.ScrollToCaret();

                LogHelper.Debug($"接收到响应: {e.Response.RawResponse} ({e.ConnectionType})");
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理响应接收事件失败", ex);
            }
        }

        /// <summary>
        /// 命令发送事件处理
        /// </summary>
        private void OnCommandSent(object sender, EpsonRobotCommandSentEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnCommandSent(sender, e)));
                return;
            }

            try
            {
                string timestamp = DateTime.Now.ToString("HH:mm:ss");
                string message = $"[{timestamp}] 发送({e.ConnectionType}): {e.Command.GetFullCommand().Trim()}\r\n";

                _sendDataTextBox.AppendText(message);
                _sendDataTextBox.ScrollToCaret();

                LogHelper.Debug($"发送命令: {e.Command.GetFullCommand().Trim()} ({e.ConnectionType})");
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理命令发送事件失败", ex);
            }
        }

        /// <summary>
        /// 机器人启动事件处理
        /// </summary>
        private void OnRobotStarted(object sender, EpsonRobotStartedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnRobotStarted(sender, e)));
                return;
            }

            try
            {
                if (e.IsSuccess)
                {
                    MessageBox.Show("机器人已启动成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    _startButton.BackColor = ColorTranslator.FromHtml("#2ecc71"); // 更深的绿色表示已启动
                    _startButton.Text = "已启动";
                    _startButton.Enabled = false;
                    _stopButton.Enabled = true;

                    // 启动自动化流程
                    Task.Run(async () => await _epsonRobotManager.StartAutomationAsync());
                }
                else
                {
                    MessageBox.Show($"机器人启动失败: {e.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

                LogHelper.Info($"机器人启动结果: {(e.IsSuccess ? "成功" : "失败")} - {e.Message}");
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理机器人启动事件失败", ex);
            }
        }

        /// <summary>
        /// 特殊命令接收事件处理
        /// </summary>
        private void OnSpecialCommandReceived(object sender, SpecialCommandReceivedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnSpecialCommandReceived(sender, e)));
                return;
            }

            try
            {
                string commandName = e.CommandType.ToString();
                string message = $"收到型号：{commandName}，等待手动发送输入框命令";

                MessageBox.Show(message, "特殊命令", MessageBoxButtons.OK, MessageBoxIcon.Information);

                LogHelper.Info($"接收到特殊命令: {commandName} - {e.RawMessage}");
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理特殊命令接收事件失败", ex);
            }
        }

        /// <summary>
        /// 自动化状态变化事件处理
        /// </summary>
        private void OnAutomationStatusChanged(object sender, AutomationStatusChangedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnAutomationStatusChanged(sender, e)));
                return;
            }

            try
            {
                _automationStatusLabel.Text = e.StatusDescription;

                if (e.IsScanning)
                {
                    _automationStatusLabel.ForeColor = ColorTranslator.FromHtml("#27ae60"); // 绿色表示正在扫描
                }
                else if (e.IsWaitingForManualInput)
                {
                    _automationStatusLabel.ForeColor = ColorTranslator.FromHtml("#f39c12"); // 橙色表示等待输入
                }
                else
                {
                    _automationStatusLabel.ForeColor = ColorTranslator.FromHtml("#95a5a6"); // 灰色表示停止
                }

                LogHelper.Info($"自动化状态变化: {e.StatusDescription}");
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理自动化状态变化事件失败", ex);
            }
        }

        /// <summary>
        /// 机器人错误事件处理
        /// </summary>
        private void OnRobotError(object sender, EpsonRobotErrorEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnRobotError(sender, e)));
                return;
            }

            try
            {
                string errorMessage = $"机器人错误 ({e.ConnectionType}): {e.ErrorMessage}";

                if (e.IsCritical)
                {
                    MessageBox.Show(errorMessage, "严重错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

                LogHelper.Error($"机器人错误: {e.ErrorType} - {e.ErrorMessage} ({e.ConnectionType})");
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理机器人错误事件失败", ex);
            }
        }

        #endregion

        #region 按钮点击事件

        /// <summary>
        /// 启动按钮点击事件
        /// </summary>
        private async void StartButton_Click(object sender, EventArgs e)
        {
            try
            {
                _startButton.Enabled = false;
                _startButton.Text = "连接中...";

                // 更新配置
                var config = new EpsonRobotConfiguration
                {
                    StartStopIPAddress = _startStopIPTextBox.Text.Trim(),
                    StartStopPort = int.Parse(_startStopPortTextBox.Text.Trim()),
                    DataIPAddress = _dataIPTextBox.Text.Trim(),
                    DataPort = int.Parse(_dataPortTextBox.Text.Trim()),
                    Password = "EPSON", // 默认密码
                    ConnectTimeout = 5000,
                    ReceiveTimeout = 3000,
                    SendTimeout = 3000,
                    ScanInterval = 100,
                    AutoReconnect = true,
                    ReconnectInterval = 5000,
                    MaxReconnectAttempts = 10
                };

                // 重新初始化管理器
                await _epsonRobotManager.InitializeAsync(config);

                // 连接启动/停止TCP
                bool startStopConnected = await _epsonRobotManager.ConnectStartStopAsync();
                if (!startStopConnected)
                {
                    MessageBox.Show("启动/停止TCP连接失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    _startButton.Enabled = true;
                    _startButton.Text = "启动";
                    return;
                }

                // 连接数据收发TCP
                bool dataConnected = await _epsonRobotManager.ConnectDataAsync();
                if (!dataConnected)
                {
                    MessageBox.Show("数据收发TCP连接失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    _startButton.Enabled = true;
                    _startButton.Text = "启动";
                    return;
                }

                // 登录（根据EPSON RC+文档，连接后必须在5分钟内Login）
                bool loginSuccess = await _epsonRobotManager.LoginAsync();
                if (!loginSuccess)
                {
                    MessageBox.Show("机器人登录失败，请检查密码和机器人状态", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    _startButton.Enabled = true;
                    _startButton.Text = "启动";
                    return;
                }

                // 启动机器人
                await _epsonRobotManager.StartRobotAsync();
            }
            catch (Exception ex)
            {
                LogHelper.Error("启动按钮点击处理失败", ex);
                MessageBox.Show($"启动失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                _startButton.Enabled = true;
                _startButton.Text = "启动";
            }
        }

        /// <summary>
        /// 停止按钮点击事件
        /// </summary>
        private async void StopButton_Click(object sender, EventArgs e)
        {
            try
            {
                _stopButton.Enabled = false;

                // 停止自动化流程
                await _epsonRobotManager.StopAutomationAsync();

                // 停止机器人
                await _epsonRobotManager.StopRobotAsync();

                // 断开连接
                await _epsonRobotManager.DisconnectAllAsync();

                // 重置UI状态
                _startButton.BackColor = ColorTranslator.FromHtml("#27ae60");
                _startButton.Text = "启动";
                _startButton.Enabled = true;
                _stopButton.Enabled = false;

                _connectionStatusLabel.Text = "未连接";
                _connectionStatusLabel.ForeColor = ColorTranslator.FromHtml("#e74c3c");

                _automationStatusLabel.Text = "未启动";
                _automationStatusLabel.ForeColor = ColorTranslator.FromHtml("#95a5a6");

                LogHelper.Info("机器人已停止");
            }
            catch (Exception ex)
            {
                LogHelper.Error("停止按钮点击处理失败", ex);
                MessageBox.Show($"停止失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                _stopButton.Enabled = true;
            }
        }

        /// <summary>
        /// 发送按钮点击事件
        /// </summary>
        private async void SendButton_Click(object sender, EventArgs e)
        {
            try
            {
                string command = _commandInputTextBox.Text.Trim();
                if (string.IsNullOrEmpty(command))
                {
                    MessageBox.Show("请输入要发送的命令", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                _sendButton.Enabled = false;
                _sendButton.Text = "发送中...";

                // 发送自定义命令
                var response = await _epsonRobotManager.SendCustomCommandAsync(command, "Data");

                if (response != null && response.IsSuccess)
                {
                    LogHelper.Info($"命令发送成功: {command}");

                    // 如果当前正在等待手动输入，继续扫描
                    if (_epsonRobotManager.IsWaitingForManualInput)
                    {
                        await _epsonRobotManager.ContinueScanningAsync();
                    }
                }
                else
                {
                    MessageBox.Show($"命令发送失败: {response?.ErrorMessage}", "错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

                _commandInputTextBox.Clear();
            }
            catch (Exception ex)
            {
                LogHelper.Error("发送按钮点击处理失败", ex);
                MessageBox.Show($"发送失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _sendButton.Enabled = true;
                _sendButton.Text = "发送";
            }
        }

        #endregion
    }
}
