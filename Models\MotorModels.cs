using System;
using System.Collections.Generic;
using System.Linq;

namespace MyHMI.Models
{
    /// <summary>
    /// 电机参数配置类
    /// 包含电机运行所需的所有参数
    /// </summary>
    public class MotorParams
    {
        /// <summary>
        /// 电机ID
        /// </summary>
        public int MotorId { get; set; }

        /// <summary>
        /// 电机名称
        /// </summary>
        public string MotorName { get; set; }

        /// <summary>
        /// 最大速度 (脉冲/秒)
        /// </summary>
        public double MaxSpeed { get; set; }

        /// <summary>
        /// 加速度 (脉冲/秒²)
        /// </summary>
        public double Acceleration { get; set; }

        /// <summary>
        /// 减速度 (脉冲/秒²)
        /// </summary>
        public double Deceleration { get; set; }

        /// <summary>
        /// 脉冲当量 (脉冲/毫米)
        /// </summary>
        public double PulseEquivalent { get; set; }

        /// <summary>
        /// 正向限位开关IO索引
        /// </summary>
        public int PositiveLimitIO { get; set; }

        /// <summary>
        /// 负向限位开关IO索引
        /// </summary>
        public int NegativeLimitIO { get; set; }

        /// <summary>
        /// 原点开关IO索引
        /// </summary>
        public int HomeIO { get; set; }

        /// <summary>
        /// 回零速度 (脉冲/秒)
        /// </summary>
        public double HomeSpeed { get; set; }

        /// <summary>
        /// 回零方向 (true: 正向, false: 负向)
        /// </summary>
        public bool HomeDirection { get; set; }

        /// <summary>
        /// 软件正向限位 (毫米)
        /// </summary>
        public double SoftPositiveLimit { get; set; }

        /// <summary>
        /// 软件负向限位 (毫米)
        /// </summary>
        public double SoftNegativeLimit { get; set; }

        /// <summary>
        /// 是否启用软件限位
        /// </summary>
        public bool EnableSoftLimit { get; set; }

        /// <summary>
        /// 轴ID（用于兼容性）
        /// </summary>
        public int AxisId { get; set; }

        /// <summary>
        /// 最大加速度（用于兼容性）
        /// </summary>
        public double MaxAcceleration { get; set; }

        /// <summary>
        /// 最大位置（用于兼容性）
        /// </summary>
        public double MaxPosition { get; set; }

        /// <summary>
        /// 最小位置（用于兼容性）
        /// </summary>
        public double MinPosition { get; set; }

        /// <summary>
        /// 位置容差（毫米）
        /// </summary>
        public double PositionTolerance { get; set; }

        /// <summary>
        /// 构造函数，设置默认值
        /// </summary>
        public MotorParams()
        {
            MotorName = "未命名电机";
            MaxSpeed = 10000;
            Acceleration = 50000;
            Deceleration = 50000;
            PulseEquivalent = 1000;
            PositiveLimitIO = -1;
            NegativeLimitIO = -1;
            HomeIO = -1;
            HomeSpeed = 5000;
            HomeDirection = true;
            SoftPositiveLimit = 100;
            SoftNegativeLimit = -100;
            EnableSoftLimit = true;
            PositionTolerance = 0.1; // 默认位置容差 0.1mm

            // 设置兼容性属性
            AxisId = MotorId;
            MaxAcceleration = Acceleration;
            MaxPosition = SoftPositiveLimit;
            MinPosition = SoftNegativeLimit;
        }

        /// <summary>
        /// 验证参数有效性
        /// </summary>
        /// <returns>验证结果和错误信息</returns>
        public (bool IsValid, string ErrorMessage) Validate()
        {
            if (MaxSpeed <= 0)
                return (false, "最大速度必须大于0");

            if (Acceleration <= 0)
                return (false, "加速度必须大于0");

            if (Deceleration <= 0)
                return (false, "减速度必须大于0");

            if (PulseEquivalent <= 0)
                return (false, "脉冲当量必须大于0");

            if (HomeSpeed <= 0)
                return (false, "回零速度必须大于0");

            if (EnableSoftLimit && SoftPositiveLimit <= SoftNegativeLimit)
                return (false, "软件正向限位必须大于负向限位");

            return (true, string.Empty);
        }
    }

    /// <summary>
    /// 电机状态信息类
    /// </summary>
    public class MotorStatus
    {
        /// <summary>
        /// 电机ID
        /// </summary>
        public int MotorId { get; set; }

        /// <summary>
        /// 当前位置 (毫米)
        /// </summary>
        public double CurrentPosition { get; set; }

        /// <summary>
        /// 当前速度 (脉冲/秒)
        /// </summary>
        public double CurrentSpeed { get; set; }

        /// <summary>
        /// 是否正在运动
        /// </summary>
        public bool IsMoving { get; set; }

        /// <summary>
        /// 是否已回零
        /// </summary>
        public bool IsHomed { get; set; }

        /// <summary>
        /// 是否触发正向限位
        /// </summary>
        public bool PositiveLimitTriggered { get; set; }

        /// <summary>
        /// 是否触发负向限位
        /// </summary>
        public bool NegativeLimitTriggered { get; set; }

        /// <summary>
        /// 是否触发原点开关
        /// </summary>
        public bool HomeTriggered { get; set; }

        /// <summary>
        /// 电机是否使能
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 是否有报警
        /// </summary>
        public bool HasAlarm { get; set; }

        /// <summary>
        /// 报警信息
        /// </summary>
        public string AlarmMessage { get; set; }

        /// <summary>
        /// 状态更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; }

        /// <summary>
        /// 最后归零时间
        /// </summary>
        public DateTime? LastHomedTime { get; set; }

        /// <summary>
        /// 本次会话是否已归零
        /// </summary>
        public bool IsHomedThisSession { get; set; }

        /// <summary>
        /// 轴ID（用于兼容性）
        /// </summary>
        public int AxisId { get; set; }

        /// <summary>
        /// 目标位置（毫米）
        /// </summary>
        public double TargetPosition { get; set; }

        /// <summary>
        /// 是否有错误（基于报警状态）
        /// </summary>
        public bool HasError { get; set; }

        /// <summary>
        /// 错误信息（基于报警信息）
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 速度（用于兼容性）
        /// </summary>
        public double Speed { get; set; }

        /// <summary>
        /// 最后更新时间（用于兼容性）
        /// </summary>
        public DateTime LastUpdateTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public MotorStatus()
        {
            AlarmMessage = string.Empty;
            UpdateTime = DateTime.Now;

            // 设置兼容性属性
            AxisId = MotorId;
            HasError = HasAlarm;
            ErrorMessage = AlarmMessage;
            Speed = CurrentSpeed;
            LastUpdateTime = UpdateTime;
        }
    }

    /// <summary>
    /// 电机运动指令类
    /// </summary>
    public class MotorCommand
    {
        /// <summary>
        /// 电机ID
        /// </summary>
        public int MotorId { get; set; }

        /// <summary>
        /// 指令类型
        /// </summary>
        public MotorCommandType CommandType { get; set; }

        /// <summary>
        /// 目标位置 (毫米)
        /// </summary>
        public double TargetPosition { get; set; }

        /// <summary>
        /// 运动速度 (脉冲/秒)
        /// </summary>
        public double Speed { get; set; }

        /// <summary>
        /// 是否为相对运动
        /// </summary>
        public bool IsRelativeMove { get; set; }

        /// <summary>
        /// 指令创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public MotorCommand()
        {
            CreateTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 电机指令类型枚举
    /// </summary>
    public enum MotorCommandType
    {
        /// <summary>
        /// 停止
        /// </summary>
        Stop,

        /// <summary>
        /// 点位运动
        /// </summary>
        MoveTo,

        /// <summary>
        /// 连续运动
        /// </summary>
        ContinuousMove,

        /// <summary>
        /// 回零
        /// </summary>
        Home,

        /// <summary>
        /// 使能
        /// </summary>
        Enable,

        /// <summary>
        /// 失能
        /// </summary>
        Disable,

        /// <summary>
        /// 清除报警
        /// </summary>
        ClearAlarm
    }

    /// <summary>
    /// 翻转电机安全参数限制
    /// </summary>
    public static class FlipMotorSafetyLimits
    {
        public const double MAX_SAFE_SPEED = 180;           // 最大安全速度 180°/s
        public const double MAX_SAFE_ACCELERATION = 900;    // 最大安全加速度 900°/s²
        public const double MIN_START_SPEED = 1;            // 最小起始速度 1°/s
        public const double MAX_START_SPEED_RATIO = 0.5;    // 起始速度不超过最大速度的50%
        public const double MAX_HOME_SPEED_RATIO = 0.3;     // 回零速度不超过最大速度的30%
        public const double MAX_PULSE_EQUIVALENT = 0.1;     // 最大脉冲当量 0.1°/pulse (对应10 pulse/°)
        public const double MIN_PULSE_EQUIVALENT = 0.0001;  // 最小脉冲当量 0.0001°/pulse (对应10000 pulse/°)
        public const double MIN_ACCELERATION_TIME = 0.1;    // 最小加速时间 0.1秒
        public const double MAX_ACCELERATION_TIME = 10.0;   // 最大加速时间 10秒
    }

    /// <summary>
    /// 翻转电机参数模型
    /// </summary>
    public class FlipMotorParams
    {
        /// <summary>
        /// 电机名称
        /// </summary>
        public string MotorName { get; set; } = "";

        /// <summary>
        /// 脉冲当量 (°/pulse) - 每个脉冲对应的角度，用于精确控制
        /// 基于硬件规格：电机10,000 pulse/r × 减速比1:3 = 30,000 pulse/r
        /// 脉冲当量 = 360° ÷ 30,000 pulse = 0.012 °/pulse
        /// </summary>
        public double PulseEquivalent { get; set; } = 0.012;

        /// <summary>
        /// 最大速度 (°/s) - 默认降低到60°/s以提高安全性
        /// </summary>
        public double MaxSpeed { get; set; } = 60;

        /// <summary>
        /// 起始速度 (°/s) - 电机启动时的初始速度
        /// </summary>
        public double StartSpeed { get; set; } = 5;

        /// <summary>
        /// 加速度 (°/s²) - 默认降低到120°/s²以提高安全性
        /// </summary>
        public double Acceleration { get; set; } = 120;

        /// <summary>
        /// 加速时间 (s) - 从起始速度加速到最大速度的时间
        /// </summary>
        public double AccelerationTime { get; set; } = 0.1;

        /// <summary>
        /// 回零速度 (°/s)
        /// </summary>
        public double HomeSpeed { get; set; } = 20;

        /// <summary>
        /// 原点传感器IO编号 (X输入编号，-1表示未配置)
        /// </summary>
        public int HomeIO { get; set; } = -1;

        /// <summary>
        /// 正向限位开关IO编号 (X输入编号，-1表示未配置)
        /// </summary>
        public int PositiveLimitIO { get; set; } = -1;

        /// <summary>
        /// 负向限位开关IO编号 (X输入编号，-1表示未配置)
        /// </summary>
        public int NegativeLimitIO { get; set; } = -1;

        /// <summary>
        /// 回零方向 (true: 正向, false: 负向)
        /// 需要根据实际机械设计确定正确方向
        /// </summary>
        public bool HomeDirection { get; set; } = false;

        /// <summary>
        /// 回零超时时间（毫秒）
        /// </summary>
        public int HomeTimeout { get; set; } = 30000;

        /// <summary>
        /// 验证参数有效性（增强版，包含安全传感器检查）
        /// </summary>
        /// <returns>验证结果</returns>
        public ValidationResult Validate()
        {
            // 脉冲当量检查
            if (PulseEquivalent <= 0)
                return new ValidationResult(false, "脉冲当量必须大于0");
            if (PulseEquivalent < FlipMotorSafetyLimits.MIN_PULSE_EQUIVALENT)
                return new ValidationResult(false, $"脉冲当量不能小于{FlipMotorSafetyLimits.MIN_PULSE_EQUIVALENT}");
            if (PulseEquivalent > FlipMotorSafetyLimits.MAX_PULSE_EQUIVALENT)
                return new ValidationResult(false, $"脉冲当量不能大于{FlipMotorSafetyLimits.MAX_PULSE_EQUIVALENT}");

            // 最大速度检查
            if (MaxSpeed <= 0)
                return new ValidationResult(false, "最大速度必须大于0");
            if (MaxSpeed > FlipMotorSafetyLimits.MAX_SAFE_SPEED)
                return new ValidationResult(false, $"最大速度不能超过安全限制{FlipMotorSafetyLimits.MAX_SAFE_SPEED}°/s");

            // 起始速度检查
            if (StartSpeed <= 0)
                return new ValidationResult(false, "起始速度必须大于0");
            if (StartSpeed < 5)
                return new ValidationResult(false, "起始速度不能小于5°/s");
            if (StartSpeed > 30)
                return new ValidationResult(false, "起始速度不能大于30°/s");
            if (StartSpeed >= MaxSpeed)
                return new ValidationResult(false, "起始速度必须小于最大速度");

            // 加速度检查
            if (Acceleration <= 0)
                return new ValidationResult(false, "加速度必须大于0");
            if (Acceleration > FlipMotorSafetyLimits.MAX_SAFE_ACCELERATION)
                return new ValidationResult(false, $"加速度不能超过安全限制{FlipMotorSafetyLimits.MAX_SAFE_ACCELERATION}°/s²");

            // 回零速度检查
            if (HomeSpeed <= 0)
                return new ValidationResult(false, "回零速度必须大于0");
            if (HomeSpeed > MaxSpeed * FlipMotorSafetyLimits.MAX_HOME_SPEED_RATIO)
                return new ValidationResult(false, $"回零速度不能超过最大速度的{FlipMotorSafetyLimits.MAX_HOME_SPEED_RATIO * 100}%");

            // 传感器IO配置检查（安全关键）
            if (HomeIO < -1 || HomeIO > 31)
                return new ValidationResult(false, "原点传感器IO编号必须在-1到31范围内（-1表示未配置）");
            if (PositiveLimitIO < -1 || PositiveLimitIO > 31)
                return new ValidationResult(false, "正向限位开关IO编号必须在-1到31范围内（-1表示未配置）");
            if (NegativeLimitIO < -1 || NegativeLimitIO > 31)
                return new ValidationResult(false, "负向限位开关IO编号必须在-1到31范围内（-1表示未配置）");

            // 传感器IO重复检查
            var ioList = new List<int>();
            if (HomeIO >= 0) ioList.Add(HomeIO);
            if (PositiveLimitIO >= 0) ioList.Add(PositiveLimitIO);
            if (NegativeLimitIO >= 0) ioList.Add(NegativeLimitIO);

            if (ioList.Count != ioList.Distinct().Count())
                return new ValidationResult(false, "传感器IO编号不能重复");

            // 回零超时检查
            if (HomeTimeout < 5000 || HomeTimeout > 120000)
                return new ValidationResult(false, "回零超时时间必须在5秒到120秒之间");

            // 加速时间直接验证（用户输入值）
            if (AccelerationTime <= 0)
                return new ValidationResult(false, "加速时间必须大于0");
            if (AccelerationTime < FlipMotorSafetyLimits.MIN_ACCELERATION_TIME)
                return new ValidationResult(false, $"加速时间不能小于{FlipMotorSafetyLimits.MIN_ACCELERATION_TIME}s");
            if (AccelerationTime > FlipMotorSafetyLimits.MAX_ACCELERATION_TIME)
                return new ValidationResult(false, $"加速时间不能大于{FlipMotorSafetyLimits.MAX_ACCELERATION_TIME}s");

            // 验证加速时间与速度参数的合理性
            double impliedAcceleration = (MaxSpeed - StartSpeed) / AccelerationTime;
            if (impliedAcceleration > FlipMotorSafetyLimits.MAX_SAFE_ACCELERATION)
                return new ValidationResult(false, $"当前加速时间({AccelerationTime:F2}s)导致加速度过大({impliedAcceleration:F1}°/s²)，请增加加速时间");

            return new ValidationResult(true, "");
        }

        /// <summary>
        /// 验证回零安全配置
        /// </summary>
        /// <returns>验证结果</returns>
        public ValidationResult ValidateHomingSafety()
        {
            if (HomeIO < 0)
                return new ValidationResult(false, "回零功能需要配置原点传感器IO");

            // 建议配置限位开关以提高安全性
            if (PositiveLimitIO < 0 && NegativeLimitIO < 0)
                return new ValidationResult(false, "建议配置至少一个限位开关以提高回零安全性");

            return new ValidationResult(true, "回零安全配置验证通过");
        }

        /// <summary>
        /// 计算安全的加速时间（基于加速度）
        /// 注意：此方法仅用于验证或默认值计算，实际控制使用AccelerationTime属性
        /// </summary>
        /// <returns>加速时间（秒）</returns>
        public double CalculateAccelerationTime()
        {
            if (Acceleration <= 0)
                return 1.0; // 默认1秒

            // 修正公式：考虑起始速度
            double speedDifference = MaxSpeed - StartSpeed;
            if (speedDifference <= 0)
                return 0.1; // 如果最大速度不大于起始速度，使用最小加速时间

            double tacc = speedDifference / Acceleration;

            // 限制在安全范围内
            if (tacc < FlipMotorSafetyLimits.MIN_ACCELERATION_TIME)
                tacc = FlipMotorSafetyLimits.MIN_ACCELERATION_TIME;
            if (tacc > FlipMotorSafetyLimits.MAX_ACCELERATION_TIME)
                tacc = FlipMotorSafetyLimits.MAX_ACCELERATION_TIME;

            return tacc;
        }

        /// <summary>
        /// 安全的脉冲计算
        /// </summary>
        /// <param name="targetAngle">目标角度</param>
        /// <returns>脉冲数</returns>
        public int CalculateTargetPulse(double targetAngle)
        {
            // 正确的计算：目标角度 ÷ 脉冲当量 = 所需脉冲数
            double pulseDouble = targetAngle / PulseEquivalent;

            // 检查溢出
            if (pulseDouble > int.MaxValue || pulseDouble < int.MinValue)
            {
                throw new ArgumentOutOfRangeException($"计算的脉冲数超出范围: {pulseDouble}，目标角度: {targetAngle}°，脉冲当量: {PulseEquivalent}°/pulse");
            }

            return (int)Math.Round(pulseDouble);
        }

        /// <summary>
        /// 计算速度对应的脉冲频率
        /// </summary>
        /// <param name="speed">速度 (°/s)</param>
        /// <returns>脉冲频率 (pulse/s)</returns>
        public int CalculateSpeedPulse(double speed)
        {
            // 速度 ÷ 脉冲当量 = 脉冲频率
            double pulseSpeed = speed / PulseEquivalent;

            // 检查溢出
            if (pulseSpeed > int.MaxValue || pulseSpeed < int.MinValue)
            {
                throw new ArgumentOutOfRangeException($"计算的脉冲频率超出范围: {pulseSpeed}，速度: {speed}°/s，脉冲当量: {PulseEquivalent}°/pulse");
            }

            return (int)Math.Round(pulseSpeed);
        }
    }

    /// <summary>
    /// 皮带电机安全参数限制
    /// </summary>
    public static class BeltMotorSafetyLimits
    {
        public const double MAX_SAFE_SPEED = 200;            // 最大安全速度 200mm/s
        public const double MAX_SAFE_ACCELERATION = 1000;    // 最大安全加速度 1000mm/s²
        public const double MIN_START_SPEED = 1;             // 最小起始速度 1mm/s
        public const double MAX_START_SPEED_RATIO = 0.5;     // 起始速度不超过最大速度的50%
        public const double MAX_JOG_DISTANCE = 100;          // 最大点动距离 100mm
        public const double MIN_JOG_DISTANCE = 0.1;          // 最小点动距离 0.1mm
        public const double MAX_PULSE_EQUIVALENT = 0.1;      // 最大脉冲当量 0.1mm/pulse (对应10 pulse/mm)
        public const double MIN_PULSE_EQUIVALENT = 0.0001;   // 最小脉冲当量 0.0001mm/pulse (对应10000 pulse/mm)
        public const double MIN_ACCELERATION_TIME = 0.1;     // 最小加速时间 0.1秒
        public const double MAX_ACCELERATION_TIME = 10.0;    // 最大加速时间 10秒
    }

    /// <summary>
    /// 皮带电机参数模型
    /// </summary>
    public class BeltMotorParams
    {
        /// <summary>
        /// 电机名称
        /// </summary>
        public string MotorName { get; set; } = "";

        /// <summary>
        /// 脉冲当量 (mm/pulse) - 每个脉冲对应的移动距离
        /// 基于10000 pulse/r的步进电机设置，假设皮带轮周长为100mm
        /// 则脉冲当量 = 100mm / 10000 pulse = 0.01 mm/pulse
        /// </summary>
        public double PulseEquivalent { get; set; } = 0.01;

        /// <summary>
        /// 最大速度 (mm/s) - 默认降低到80mm/s以提高安全性
        /// </summary>
        public double MaxSpeed { get; set; } = 80;

        /// <summary>
        /// 起始速度 (mm/s)
        /// </summary>
        public double StartSpeed { get; set; } = 10;

        /// <summary>
        /// 加速度 (mm/s²) - 默认降低到300mm/s²以提高安全性
        /// </summary>
        public double Acceleration { get; set; } = 300;

        /// <summary>
        /// 加速时间 (s) - 从起始速度加速到最大速度的时间
        /// </summary>
        public double AccelerationTime { get; set; } = 0.1;

        /// <summary>
        /// 点动距离 (mm)
        /// </summary>
        public double JogDistance { get; set; } = 10;

        /// <summary>
        /// 验证参数有效性（增强版）
        /// </summary>
        /// <returns>验证结果</returns>
        public ValidationResult Validate()
        {
            // 脉冲当量检查
            if (PulseEquivalent <= 0)
                return new ValidationResult(false, "脉冲当量必须大于0");
            if (PulseEquivalent < BeltMotorSafetyLimits.MIN_PULSE_EQUIVALENT)
                return new ValidationResult(false, $"脉冲当量不能小于{BeltMotorSafetyLimits.MIN_PULSE_EQUIVALENT}");
            if (PulseEquivalent > BeltMotorSafetyLimits.MAX_PULSE_EQUIVALENT)
                return new ValidationResult(false, $"脉冲当量不能大于{BeltMotorSafetyLimits.MAX_PULSE_EQUIVALENT}");

            // 最大速度检查
            if (MaxSpeed <= 0)
                return new ValidationResult(false, "最大速度必须大于0");
            if (MaxSpeed > BeltMotorSafetyLimits.MAX_SAFE_SPEED)
                return new ValidationResult(false, $"最大速度不能超过安全限制{BeltMotorSafetyLimits.MAX_SAFE_SPEED}mm/s");

            // 起始速度检查
            if (StartSpeed < BeltMotorSafetyLimits.MIN_START_SPEED)
                return new ValidationResult(false, $"起始速度不能小于{BeltMotorSafetyLimits.MIN_START_SPEED}mm/s");
            if (StartSpeed > MaxSpeed * BeltMotorSafetyLimits.MAX_START_SPEED_RATIO)
                return new ValidationResult(false, $"起始速度不能超过最大速度的{BeltMotorSafetyLimits.MAX_START_SPEED_RATIO * 100}%");

            // 加速度检查
            if (Acceleration <= 0)
                return new ValidationResult(false, "加速度必须大于0");
            if (Acceleration > BeltMotorSafetyLimits.MAX_SAFE_ACCELERATION)
                return new ValidationResult(false, $"加速度不能超过安全限制{BeltMotorSafetyLimits.MAX_SAFE_ACCELERATION}mm/s²");

            // 点动距离检查
            if (JogDistance < BeltMotorSafetyLimits.MIN_JOG_DISTANCE)
                return new ValidationResult(false, $"点动距离不能小于{BeltMotorSafetyLimits.MIN_JOG_DISTANCE}mm");
            if (JogDistance > BeltMotorSafetyLimits.MAX_JOG_DISTANCE)
                return new ValidationResult(false, $"点动距离不能大于{BeltMotorSafetyLimits.MAX_JOG_DISTANCE}mm");

            // 加速时间合理性检查
            double accelerationTime = MaxSpeed / Acceleration;
            if (accelerationTime < BeltMotorSafetyLimits.MIN_ACCELERATION_TIME)
                return new ValidationResult(false, $"计算的加速时间过短({accelerationTime:F2}s)，请降低加速度");
            if (accelerationTime > BeltMotorSafetyLimits.MAX_ACCELERATION_TIME)
                return new ValidationResult(false, $"计算的加速时间过长({accelerationTime:F2}s)，请提高加速度");

            return new ValidationResult(true, "");
        }

        /// <summary>
        /// 计算安全的加速时间（基于加速度）
        /// 注意：此方法仅用于验证或默认值计算，实际控制使用AccelerationTime属性
        /// </summary>
        /// <returns>加速时间（秒）</returns>
        public double CalculateAccelerationTime()
        {
            if (Acceleration <= 0)
                return 1.0; // 默认1秒

            // 修正公式：考虑起始速度
            double speedDifference = MaxSpeed - StartSpeed;
            if (speedDifference <= 0)
                return 0.1; // 如果最大速度不大于起始速度，使用最小加速时间

            double tacc = speedDifference / Acceleration;

            // 限制在安全范围内
            if (tacc < BeltMotorSafetyLimits.MIN_ACCELERATION_TIME)
                tacc = BeltMotorSafetyLimits.MIN_ACCELERATION_TIME;
            if (tacc > BeltMotorSafetyLimits.MAX_ACCELERATION_TIME)
                tacc = BeltMotorSafetyLimits.MAX_ACCELERATION_TIME;

            return tacc;
        }

        /// <summary>
        /// 安全的脉冲计算
        /// </summary>
        /// <param name="targetDistance">目标距离</param>
        /// <returns>脉冲数</returns>
        public int CalculateTargetPulse(double targetDistance)
        {
            // 正确的计算：目标距离 ÷ 脉冲当量 = 所需脉冲数
            double pulseDouble = targetDistance / PulseEquivalent;

            // 检查溢出
            if (pulseDouble > int.MaxValue || pulseDouble < int.MinValue)
            {
                throw new ArgumentOutOfRangeException($"计算的脉冲数超出范围: {pulseDouble}，目标距离: {targetDistance}mm，脉冲当量: {PulseEquivalent}mm/pulse");
            }

            return (int)Math.Round(pulseDouble);
        }

        /// <summary>
        /// 计算速度对应的脉冲频率
        /// </summary>
        /// <param name="speed">速度 (mm/s)</param>
        /// <returns>脉冲频率 (pulse/s)</returns>
        public int CalculateSpeedPulse(double speed)
        {
            // 速度 ÷ 脉冲当量 = 脉冲频率
            double pulseSpeed = speed / PulseEquivalent;

            // 检查溢出
            if (pulseSpeed > int.MaxValue || pulseSpeed < int.MinValue)
            {
                throw new ArgumentOutOfRangeException($"计算的脉冲频率超出范围: {pulseSpeed}，速度: {speed}mm/s，脉冲当量: {PulseEquivalent}mm/pulse");
            }

            return (int)Math.Round(pulseSpeed);
        }
    }

    /// <summary>
    /// 翻转电机位置保存模型
    /// </summary>
    public class FlipMotorPositions
    {
        /// <summary>
        /// 位置1 (度)
        /// </summary>
        public double Position1 { get; set; } = double.NaN;

        /// <summary>
        /// 位置2 (度)
        /// </summary>
        public double Position2 { get; set; } = double.NaN;

        /// <summary>
        /// 位置3 (度)
        /// </summary>
        public double Position3 { get; set; } = double.NaN;

        /// <summary>
        /// 位置4 (度)
        /// </summary>
        public double Position4 { get; set; } = double.NaN;

        /// <summary>
        /// 位置5 (度)
        /// </summary>
        public double Position5 { get; set; } = double.NaN;

        /// <summary>
        /// 检查位置是否已保存
        /// </summary>
        /// <param name="positionIndex">位置索引 (1-4)</param>
        /// <returns>是否已保存</returns>
        public bool IsPositionSaved(int positionIndex)
        {
            switch (positionIndex)
            {
                case 1:
                    return !double.IsNaN(Position1);
                case 2:
                    return !double.IsNaN(Position2);
                case 3:
                    return !double.IsNaN(Position3);
                case 4:
                    return !double.IsNaN(Position4);
                default:
                    return false;
            }
        }

        /// <summary>
        /// 获取指定位置的角度值
        /// </summary>
        /// <param name="positionIndex">位置索引 (1-3)</param>
        /// <returns>角度值</returns>
        public double GetPosition(int positionIndex)
        {
            switch (positionIndex)
            {
                case 1:
                    return Position1;
                case 2:
                    return Position2;
                case 3:
                    return Position3;
                case 4:
                    return Position4;
                default:
                    return double.NaN;
            }
        }
    }

    /// <summary>
    /// 验证结果模型
    /// </summary>
    public class ValidationResult
    {
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="isValid">是否有效</param>
        /// <param name="errorMessage">错误消息</param>
        public ValidationResult(bool isValid, string errorMessage)
        {
            IsValid = isValid;
            ErrorMessage = errorMessage;
        }
    }
}
