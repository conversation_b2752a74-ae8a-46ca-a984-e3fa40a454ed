<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x64</Platform>
    <ProjectGuid>{12345678-1234-5678-9ABC-123456789ABC}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>MyHMI</RootNamespace>
    <AssemblyName>MyHMI</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
    <PlatformTarget>x64</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <PlatformTarget>x64</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />

    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="Newtonsoft.Json">
      <HintPath>packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NLog">
      <HintPath>packages\NLog.5.2.8\lib\net46\NLog.dll</HintPath>
    </Reference>
    <Reference Include="NModbus4">
      <HintPath>packages\NModbus4.2.1.0\lib\net40\NModbus4.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus">
      <HintPath>packages\EPPlus.4.5.3.3\lib\net40\EPPlus.dll</HintPath>
    </Reference>
    <Reference Include="CsvHelper">
      <HintPath>packages\CsvHelper.30.0.1\lib\net47\CsvHelper.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <!-- Config -->
    <!-- Settings -->
    <Compile Include="Settings\AppSettings.cs" />
    <Compile Include="Settings\Settings.cs" />
    <!-- Events -->
    <Compile Include="Events\CommunicationEventArgs.cs" />
    <!-- EpsonRobotAutoModeEventArgs - 重新启用进行修复 -->
    <Compile Include="Events\EpsonRobotAutoModeEventArgs.cs" />
    <Compile Include="Events\EpsonRobotEventArgs.cs" />
    <Compile Include="Events\IOEventArgs.cs" />
    <Compile Include="Events\MotorEventArgs.cs" />
    <Compile Include="Events\SafetyEventArgs.cs" />
    <Compile Include="Events\SystemModeEventArgs.cs" />
    <!-- Helpers -->
    <Compile Include="Helpers\ExceptionHelper.cs" />
    <Compile Include="Helpers\LogHelper.cs" />
    <Compile Include="Helpers\UIHelper.cs" />
    <!-- Managers -->
    <Compile Include="Managers\ModbusTcpManager.cs" />
    <Compile Include="Managers\MotorManager.cs" />
    <Compile Include="Managers\DMC1000BCardManager.cs" />
    <Compile Include="Managers\DMC1000BMotorManager.cs" />
    <Compile Include="Managers\GlobalMotorParameterManager.cs" />
    <Compile Include="Managers\DMC1000BIOManager.cs" />
    <Compile Include="Managers\csDmc1000.cs" />
    <Compile Include="Managers\EpsonRobotManager.cs" />
    <Compile Include="Managers\EpsonRobotManager2.cs" />
    <Compile Include="Managers\SafetyManager.cs" />
    <Compile Include="Managers\ScannerManager.cs" />
    <Compile Include="Managers\MultiScannerManager.cs" />
    <Compile Include="Managers\ScannerAutoModeManager.cs" />
    <Compile Include="Managers\StartupSelfCheckManager.cs" />
    <Compile Include="Managers\StatisticsManager.cs" />

    <Compile Include="Managers\BeltMotorAutoModeController.cs" />
    <!-- EpsonRobotAutoModeController - 重新启用进行修复 -->
    <Compile Include="Managers\EpsonRobotAutoModeController.cs" />
    <Compile Include="Managers\EpsonRobotAutoModeController.BusinessLogic.cs" />
    <Compile Include="Managers\EpsonRobotAutoModeController.Helpers.cs" />
    <Compile Include="Managers\ScaraCommunicationManager.cs" />
    <Compile Include="Managers\ScaraAutoModeController.cs" />
    <Compile Include="Managers\SystemModeManager.cs" />
    <Compile Include="Managers\InitializationStatusManager.cs" />
    <Compile Include="Managers\VisionManager.cs" />
    <Compile Include="Managers\WorkflowManager.cs" />
    <!-- Models -->
    <Compile Include="Models\CommunicationModels.cs" />
    <!-- EpsonRobotAutoMode相关模型 - 重新启用进行修复 -->
    <Compile Include="Models\EpsonRobotAutoModeConfiguration.cs" />
    <Compile Include="Models\EpsonRobotAutoModeModels.cs" />
    <Compile Include="Models\EpsonRobotModels.cs" />
    <Compile Include="Models\IOModels.cs" />
    <Compile Include="Models\MotorModels.cs" />
    <Compile Include="Models\ProductionModels.cs" />
    <Compile Include="Models\SafetyModels.cs" />
    <Compile Include="Models\ScaraModels.cs" />
    <!-- UI -->
    <Compile Include="UI\MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Controls\CommunicationPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Controls\CommunicationPanel.Designer.cs">
      <DependentUpon>CommunicationPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Controls\IOControlPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Controls\IOControlPanel.Designer.cs">
      <DependentUpon>IOControlPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Controls\LogPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Controls\LogPanel.Designer.cs">
      <DependentUpon>LogPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Controls\MotorControlPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Controls\MotorControlPanel.Designer.cs">
      <DependentUpon>MotorControlPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Controls\StatisticsPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Controls\StatisticsPanel.Designer.cs">
      <DependentUpon>StatisticsPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Controls\VisionPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Controls\VisionPanel.Designer.cs">
      <DependentUpon>VisionPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Controls\VisionPositionPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Controls\VisionPositionPanel.Designer.cs">
      <DependentUpon>VisionPositionPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Controls\VisionAlignPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Controls\VisionAlignPanel.Designer.cs">
      <DependentUpon>VisionAlignPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Controls\MotorFlipPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Controls\MotorFlipPanel.Designer.cs">
      <DependentUpon>MotorFlipPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Controls\MotorFlipTeachPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Controls\MotorFlipTeachPanel.Designer.cs">
      <DependentUpon>MotorFlipTeachPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Controls\MotorBeltPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Controls\MotorBeltPanel.Designer.cs">
      <DependentUpon>MotorBeltPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Controls\Robot6AxisPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Controls\Robot6AxisPanel.Designer.cs">
      <DependentUpon>Robot6AxisPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Controls\Robot6AxisPanel2.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Controls\Robot6AxisPanel2.Designer.cs">
      <DependentUpon>Robot6AxisPanel2.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Controls\ScannerControlPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Controls\ScannerControlPanel.Designer.cs">
      <DependentUpon>ScannerControlPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Controls\ScaraCommPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Controls\ScaraCommPanel.Designer.cs">
      <DependentUpon>ScaraCommPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Controls\ScaraTeachPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Controls\ScaraTeachPanel.Designer.cs">
      <DependentUpon>ScaraTeachPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Controls\IOReadPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Controls\IOReadPanel.Designer.cs">
      <DependentUpon>IOReadPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Controls\IOWritePanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Controls\IOWritePanel.Designer.cs">
      <DependentUpon>IOWritePanel.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Controls\ProductionLogPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Controls\ProductionLogPanel.Designer.cs">
      <DependentUpon>ProductionLogPanel.cs</DependentUpon>
    </Compile>
    <!-- SCARA机器人模拟器 - 临时功能，包含Simulator标识 -->
    <Compile Include="UI\ScaraRobotSimulatorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\ScaraRobotSimulatorForm.Designer.cs">
      <DependentUpon>ScaraRobotSimulatorForm.cs</DependentUpon>
    </Compile>
    <!-- UI Core Architecture - 整个UI/Core目录已完全移除 -->
    <!-- 原因: UI/Core中的所有组件都与事件驱动重构目标不符 -->
    <!-- UnifiedResourceManager本身就是基于30秒定时器的复杂管理系统 -->
    <!-- 这与移除高频刷新架构的目标完全矛盾 -->
    <!-- 按照"安全、简单直接"原则，改为直接的事件驱动实现 -->
    <!-- Testing -->
    <Compile Include="Testing\IOConfigTest.cs" />
    <Compile Include="Testing\MockIOManager.cs" />
    <Compile Include="Testing\MockMotorManager.cs" />
    <Compile Include="Testing\SimpleTestFramework.cs" />
    <Compile Include="Testing\SystemTests.cs" />
    <Compile Include="Testing\BeltMotorAutoModeController_Test.cs" />
    <Compile Include="Testing\WorkflowManager_Refactoring_Test.cs" />
    <Compile Include="Testing\ComprehensiveTestExecutor.cs" />
    <Compile Include="Testing\TestRunner.cs" />
    <Compile Include="Testing\PerformanceTestExecutor.cs" />
    <Compile Include="Testing\TestRunnerForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Testing\TestRunnerForm.Designer.cs">
      <DependentUpon>TestRunnerForm.cs</DependentUpon>
    </Compile>
    <!-- Tests section removed - no test files in production build -->
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <None Include="App.config" />
    <None Include="packages.config" />

    <EmbeddedResource Include="UI\MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Controls\CommunicationPanel.resx">
      <DependentUpon>CommunicationPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Controls\IOControlPanel.resx">
      <DependentUpon>IOControlPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Controls\LogPanel.resx">
      <DependentUpon>LogPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Controls\MotorControlPanel.resx">
      <DependentUpon>MotorControlPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Controls\StatisticsPanel.resx">
      <DependentUpon>StatisticsPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Controls\VisionPanel.resx">
      <DependentUpon>VisionPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Testing\TestRunnerForm.resx">
      <DependentUpon>TestRunnerForm.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="System.IO.Ports" Version="8.0.0" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
