using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;
using MyHMI.Helpers;
using MyHMI.Managers;

namespace MyHMI.Testing
{
    /// <summary>
    /// 性能测试执行器
    /// 用于测试WorkflowManager重构后的性能表现
    /// </summary>
    public class PerformanceTestExecutor
    {
        #region 性能测试结果数据结构

        /// <summary>
        /// 性能测试结果
        /// </summary>
        public class PerformanceTestResult
        {
            public string TestName { get; set; }
            public TimeSpan AverageExecutionTime { get; set; }
            public TimeSpan MinExecutionTime { get; set; }
            public TimeSpan MaxExecutionTime { get; set; }
            public long MemoryUsageBefore { get; set; }
            public long MemoryUsageAfter { get; set; }
            public long MemoryDelta => MemoryUsageAfter - MemoryUsageBefore;
            public int IterationCount { get; set; }
            public bool Passed { get; set; }
            public string Notes { get; set; }

            public PerformanceTestResult(string testName)
            {
                TestName = testName;
                Notes = string.Empty;
            }
        }

        /// <summary>
        /// 性能基准
        /// </summary>
        public static class PerformanceBenchmarks
        {
            public static readonly TimeSpan WorkflowManagerInitialization = TimeSpan.FromMilliseconds(100);
            public static readonly TimeSpan BeltMotorControllerInitialization = TimeSpan.FromMilliseconds(50);
            public static readonly TimeSpan WorkflowStartup = TimeSpan.FromMilliseconds(200);
            public static readonly TimeSpan WorkflowReset = TimeSpan.FromMilliseconds(100);
            public static readonly long MaxMemoryUsage = 10 * 1024 * 1024; // 10MB
        }

        #endregion

        #region 私有字段

        private List<PerformanceTestResult> _performanceResults;
        private const int DefaultIterations = 10;

        #endregion

        #region 公共方法

        /// <summary>
        /// 执行所有性能测试
        /// </summary>
        /// <returns>性能测试结果</returns>
        public async Task<bool> ExecuteAllPerformanceTestsAsync()
        {
            try
            {
                LogHelper.Info("========== 开始执行性能测试 ==========");
                
                _performanceResults = new List<PerformanceTestResult>();

                // 执行各项性能测试
                await ExecuteWorkflowManagerInitializationPerformanceTest();
                await ExecuteBeltMotorControllerInitializationPerformanceTest();
                await ExecuteWorkflowStartupPerformanceTest();
                await ExecuteWorkflowResetPerformanceTest();
                await ExecuteMemoryUsageTest();

                // 生成性能报告
                GeneratePerformanceReport();

                // 计算总体结果
                bool overallResult = CalculateOverallPerformanceResult();
                
                LogHelper.Info($"========== 性能测试完成，总体结果: {(overallResult ? "通过" : "失败")} ==========");
                return overallResult;
            }
            catch (Exception ex)
            {
                LogHelper.Error("执行性能测试时发生异常", ex);
                return false;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 执行WorkflowManager初始化性能测试
        /// </summary>
        private async Task ExecuteWorkflowManagerInitializationPerformanceTest()
        {
            var result = new PerformanceTestResult("WorkflowManager初始化性能测试");
            var executionTimes = new List<TimeSpan>();

            LogHelper.Info("--- 开始WorkflowManager初始化性能测试 ---");

            try
            {
                result.MemoryUsageBefore = GC.GetTotalMemory(true);

                for (int i = 0; i < DefaultIterations; i++)
                {
                    var stopwatch = Stopwatch.StartNew();
                    
                    // 获取WorkflowManager实例（测试单例获取性能）
                    var workflowManager = WorkflowManager.Instance;
                    
                    // 测试初始化性能
                    if (!workflowManager.IsInitialized)
                    {
                        await workflowManager.InitializeAsync();
                    }
                    
                    stopwatch.Stop();
                    executionTimes.Add(stopwatch.Elapsed);
                }

                result.MemoryUsageAfter = GC.GetTotalMemory(false);
                result.IterationCount = DefaultIterations;
                
                CalculateStatistics(result, executionTimes);
                result.Passed = result.AverageExecutionTime <= PerformanceBenchmarks.WorkflowManagerInitialization;
                
                if (!result.Passed)
                {
                    result.Notes = $"平均执行时间 {result.AverageExecutionTime.TotalMilliseconds:F2}ms 超过基准 {PerformanceBenchmarks.WorkflowManagerInitialization.TotalMilliseconds}ms";
                }

                LogHelper.Info($"WorkflowManager初始化性能: 平均 {result.AverageExecutionTime.TotalMilliseconds:F2}ms, 结果: {(result.Passed ? "通过" : "失败")}");
            }
            catch (Exception ex)
            {
                result.Passed = false;
                result.Notes = $"测试异常: {ex.Message}";
                LogHelper.Error("WorkflowManager初始化性能测试异常", ex);
            }

            _performanceResults.Add(result);
        }

        /// <summary>
        /// 执行BeltMotorAutoModeController初始化性能测试
        /// </summary>
        private async Task ExecuteBeltMotorControllerInitializationPerformanceTest()
        {
            var result = new PerformanceTestResult("BeltMotorAutoModeController初始化性能测试");
            var executionTimes = new List<TimeSpan>();

            LogHelper.Info("--- 开始BeltMotorAutoModeController初始化性能测试 ---");

            try
            {
                result.MemoryUsageBefore = GC.GetTotalMemory(true);

                for (int i = 0; i < DefaultIterations; i++)
                {
                    var stopwatch = Stopwatch.StartNew();
                    
                    // 获取BeltMotorAutoModeController实例
                    var beltMotorController = BeltMotorAutoModeController.Instance;
                    
                    // 测试初始化性能
                    if (!beltMotorController.IsInitialized)
                    {
                        await beltMotorController.InitializeAsync();
                    }
                    
                    stopwatch.Stop();
                    executionTimes.Add(stopwatch.Elapsed);
                }

                result.MemoryUsageAfter = GC.GetTotalMemory(false);
                result.IterationCount = DefaultIterations;
                
                CalculateStatistics(result, executionTimes);
                result.Passed = result.AverageExecutionTime <= PerformanceBenchmarks.BeltMotorControllerInitialization;
                
                if (!result.Passed)
                {
                    result.Notes = $"平均执行时间 {result.AverageExecutionTime.TotalMilliseconds:F2}ms 超过基准 {PerformanceBenchmarks.BeltMotorControllerInitialization.TotalMilliseconds}ms";
                }

                LogHelper.Info($"BeltMotorAutoModeController初始化性能: 平均 {result.AverageExecutionTime.TotalMilliseconds:F2}ms, 结果: {(result.Passed ? "通过" : "失败")}");
            }
            catch (Exception ex)
            {
                result.Passed = false;
                result.Notes = $"测试异常: {ex.Message}";
                LogHelper.Error("BeltMotorAutoModeController初始化性能测试异常", ex);
            }

            _performanceResults.Add(result);
        }

        /// <summary>
        /// 执行工作流启动性能测试
        /// </summary>
        private async Task ExecuteWorkflowStartupPerformanceTest()
        {
            var result = new PerformanceTestResult("工作流启动性能测试");
            var executionTimes = new List<TimeSpan>();

            LogHelper.Info("--- 开始工作流启动性能测试 ---");

            try
            {
                var workflowManager = WorkflowManager.Instance;
                await workflowManager.InitializeAsync();

                result.MemoryUsageBefore = GC.GetTotalMemory(true);

                for (int i = 0; i < DefaultIterations; i++)
                {
                    var stopwatch = Stopwatch.StartNew();
                    
                    // 测试工作流启动性能
                    await workflowManager.StartWorkflowAsync($"TEST_PRODUCT_{i:D3}");
                    
                    // 立即停止以准备下次测试
                    await workflowManager.StopWorkflowAsync();
                    
                    stopwatch.Stop();
                    executionTimes.Add(stopwatch.Elapsed);
                }

                result.MemoryUsageAfter = GC.GetTotalMemory(false);
                result.IterationCount = DefaultIterations;
                
                CalculateStatistics(result, executionTimes);
                result.Passed = result.AverageExecutionTime <= PerformanceBenchmarks.WorkflowStartup;
                
                if (!result.Passed)
                {
                    result.Notes = $"平均执行时间 {result.AverageExecutionTime.TotalMilliseconds:F2}ms 超过基准 {PerformanceBenchmarks.WorkflowStartup.TotalMilliseconds}ms";
                }

                LogHelper.Info($"工作流启动性能: 平均 {result.AverageExecutionTime.TotalMilliseconds:F2}ms, 结果: {(result.Passed ? "通过" : "失败")}");
            }
            catch (Exception ex)
            {
                result.Passed = false;
                result.Notes = $"测试异常: {ex.Message}";
                LogHelper.Error("工作流启动性能测试异常", ex);
            }

            _performanceResults.Add(result);
        }

        /// <summary>
        /// 执行工作流重置性能测试
        /// </summary>
        private async Task ExecuteWorkflowResetPerformanceTest()
        {
            var result = new PerformanceTestResult("工作流重置性能测试");
            var executionTimes = new List<TimeSpan>();

            LogHelper.Info("--- 开始工作流重置性能测试 ---");

            try
            {
                var workflowManager = WorkflowManager.Instance;
                await workflowManager.InitializeAsync();

                result.MemoryUsageBefore = GC.GetTotalMemory(true);

                for (int i = 0; i < DefaultIterations; i++)
                {
                    var stopwatch = Stopwatch.StartNew();
                    
                    // 测试工作流重置性能
                    await workflowManager.ResetWorkflowAsync();
                    
                    stopwatch.Stop();
                    executionTimes.Add(stopwatch.Elapsed);
                }

                result.MemoryUsageAfter = GC.GetTotalMemory(false);
                result.IterationCount = DefaultIterations;
                
                CalculateStatistics(result, executionTimes);
                result.Passed = result.AverageExecutionTime <= PerformanceBenchmarks.WorkflowReset;
                
                if (!result.Passed)
                {
                    result.Notes = $"平均执行时间 {result.AverageExecutionTime.TotalMilliseconds:F2}ms 超过基准 {PerformanceBenchmarks.WorkflowReset.TotalMilliseconds}ms";
                }

                LogHelper.Info($"工作流重置性能: 平均 {result.AverageExecutionTime.TotalMilliseconds:F2}ms, 结果: {(result.Passed ? "通过" : "失败")}");
            }
            catch (Exception ex)
            {
                result.Passed = false;
                result.Notes = $"测试异常: {ex.Message}";
                LogHelper.Error("工作流重置性能测试异常", ex);
            }

            _performanceResults.Add(result);
        }

        /// <summary>
        /// 执行内存使用测试
        /// </summary>
        private async Task ExecuteMemoryUsageTest()
        {
            var result = new PerformanceTestResult("内存使用测试");

            LogHelper.Info("--- 开始内存使用测试 ---");

            try
            {
                // 强制垃圾回收以获得准确的基准
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                result.MemoryUsageBefore = GC.GetTotalMemory(false);

                // 创建和初始化所有管理器
                var workflowManager = WorkflowManager.Instance;
                await workflowManager.InitializeAsync();

                var beltMotorController = BeltMotorAutoModeController.Instance;
                await beltMotorController.InitializeAsync();

                // 执行一些操作
                for (int i = 0; i < 5; i++)
                {
                    await workflowManager.StartWorkflowAsync($"MEMORY_TEST_{i}");
                    await workflowManager.StopWorkflowAsync();
                    await workflowManager.ResetWorkflowAsync();
                }

                result.MemoryUsageAfter = GC.GetTotalMemory(false);
                result.IterationCount = 1;
                
                result.Passed = result.MemoryDelta <= PerformanceBenchmarks.MaxMemoryUsage;
                
                if (!result.Passed)
                {
                    result.Notes = $"内存增长 {result.MemoryDelta / 1024 / 1024:F2}MB 超过基准 {PerformanceBenchmarks.MaxMemoryUsage / 1024 / 1024}MB";
                }

                LogHelper.Info($"内存使用: 增长 {result.MemoryDelta / 1024:F2}KB, 结果: {(result.Passed ? "通过" : "失败")}");
            }
            catch (Exception ex)
            {
                result.Passed = false;
                result.Notes = $"测试异常: {ex.Message}";
                LogHelper.Error("内存使用测试异常", ex);
            }

            _performanceResults.Add(result);
        }

        /// <summary>
        /// 计算统计信息
        /// </summary>
        private void CalculateStatistics(PerformanceTestResult result, List<TimeSpan> executionTimes)
        {
            if (executionTimes.Count == 0) return;

            var totalTicks = 0L;
            var minTicks = long.MaxValue;
            var maxTicks = long.MinValue;

            foreach (var time in executionTimes)
            {
                totalTicks += time.Ticks;
                if (time.Ticks < minTicks) minTicks = time.Ticks;
                if (time.Ticks > maxTicks) maxTicks = time.Ticks;
            }

            result.AverageExecutionTime = new TimeSpan(totalTicks / executionTimes.Count);
            result.MinExecutionTime = new TimeSpan(minTicks);
            result.MaxExecutionTime = new TimeSpan(maxTicks);
        }

        /// <summary>
        /// 生成性能报告
        /// </summary>
        private void GeneratePerformanceReport()
        {
            LogHelper.Info("========== 性能测试报告 ==========");

            int totalTests = _performanceResults.Count;
            int passedTests = 0;

            foreach (var result in _performanceResults)
            {
                LogHelper.Info($"\n【{result.TestName}】");
                LogHelper.Info($"迭代次数: {result.IterationCount}");
                
                if (result.IterationCount > 1)
                {
                    LogHelper.Info($"平均执行时间: {result.AverageExecutionTime.TotalMilliseconds:F2}ms");
                    LogHelper.Info($"最小执行时间: {result.MinExecutionTime.TotalMilliseconds:F2}ms");
                    LogHelper.Info($"最大执行时间: {result.MaxExecutionTime.TotalMilliseconds:F2}ms");
                }

                if (result.MemoryDelta != 0)
                {
                    LogHelper.Info($"内存使用变化: {result.MemoryDelta / 1024:F2}KB");
                }

                LogHelper.Info($"测试结果: {(result.Passed ? "通过" : "失败")}");
                
                if (!string.IsNullOrEmpty(result.Notes))
                {
                    LogHelper.Info($"备注: {result.Notes}");
                }

                if (result.Passed) passedTests++;
            }

            double passRate = totalTests > 0 ? (double)passedTests / totalTests * 100 : 0;

            LogHelper.Info("\n【性能测试总结】");
            LogHelper.Info($"总测试数量: {totalTests}");
            LogHelper.Info($"通过测试数量: {passedTests}");
            LogHelper.Info($"失败测试数量: {totalTests - passedTests}");
            LogHelper.Info($"通过率: {passRate:F1}%");

            string performanceAssessment = GetPerformanceAssessment(passRate);
            LogHelper.Info($"性能评估: {performanceAssessment}");

            LogHelper.Info("========== 性能测试报告结束 ==========");
        }

        /// <summary>
        /// 计算总体性能结果
        /// </summary>
        private bool CalculateOverallPerformanceResult()
        {
            foreach (var result in _performanceResults)
            {
                if (!result.Passed)
                {
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 获取性能评估
        /// </summary>
        private string GetPerformanceAssessment(double passRate)
        {
            if (passRate >= 90) return "优秀 - 性能表现很好";
            if (passRate >= 75) return "良好 - 性能表现较好";
            if (passRate >= 60) return "一般 - 性能需要改进";
            return "较差 - 性能需要重点优化";
        }

        #endregion
    }
}
