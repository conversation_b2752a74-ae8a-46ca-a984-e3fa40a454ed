# HR2 上位机控制系统

## 项目概述

HR2是一个基于C# WinForms开发的工业自动化上位机控制系统，主要用于控制DMC1000B运动控制卡、Epson机器人和各种IO设备。系统采用分层架构设计，具有良好的可扩展性和维护性。

## 系统特性

- 🎯 **多设备控制**: 支持DMC1000B运动控制卡、Epson机器人、IO设备
- 🔧 **电机控制**: 4轴步进电机控制（左右翻转电机、输入输出皮带电机）
- 📊 **实时监控**: 10Hz频率的IO状态监控和电机位置监控
- 🎨 **友好界面**: 基于WinForms的直观用户界面
- 📝 **完整日志**: 详细的操作日志和异常记录
- 🔒 **安全保护**: 完善的参数验证和异常处理机制

## 技术栈

- **开发语言**: C# (.NET Framework)
- **UI框架**: Windows Forms
- **硬件接口**: P/Invoke调用原生DLL
- **控制卡**: DMC1000B运动控制卡
- **机器人**: Epson机器人（TCP/IP通信）
- **架构模式**: 分层架构 + 单例模式

## 项目结构

```
HR2/
├── Config/                          # 配置文件
│   └── SystemConfig.json           # 系统配置
├── Development_Documents/           # 开发文档
│   ├── Architecture/               # 架构文档
│   ├── Development_Logs/           # 开发日志
│   └── 电机控制业务逻辑开发文档/    # 业务逻辑文档
├── Helpers/                        # 辅助工具类
│   ├── ConfigHelper.cs            # 配置管理
│   ├── ExceptionHelper.cs         # 异常处理
│   └── LogHelper.cs               # 日志管理
├── Managers/                       # 业务管理器
│   ├── DMC1000BCardManager.cs     # DMC1000B控制卡管理
│   ├── DMC1000BIOManager.cs       # IO管理
│   ├── DMC1000BMotorManager.cs    # 电机管理
│   ├── EpsonRobotManager.cs       # Epson机器人管理
│   ├── ModbusTcpManager.cs        # Modbus TCP通信
│   ├── MotorManager.cs            # 通用电机管理
│   ├── StatisticsManager.cs       # 统计管理
│   ├── SystemModeManager.cs       # 系统模式管理
│   └── WorkflowManager.cs         # 工作流管理
├── Models/                         # 数据模型
│   ├── IOModels.cs                # IO相关模型
│   ├── MotorModels.cs             # 电机相关模型
│   └── RobotModels.cs             # 机器人相关模型
├── Testing/                        # 测试相关
│   └── SystemTests.cs             # 系统测试
└── UI/                            # 用户界面
    ├── Controls/                  # 自定义控件
    │   ├── IOControlPanel.cs      # IO控制面板
    │   ├── MotorFlipPanel.cs      # 翻转电机控制面板
    │   └── Robot6AxisPanel.cs     # 6轴机器人控制面板
    └── MainForm.cs               # 主窗体
```

## 核心组件说明

### 1. 管理器层 (Managers/)

#### DMC1000BCardManager.cs
- **作用**: DMC1000B控制卡的统一管理
- **功能**: 控制卡初始化、连接管理、资源释放
- **单例模式**: 确保全局唯一的控制卡实例

#### DMC1000BIOManager.cs  
- **作用**: IO输入输出管理
- **功能**: 
  - 普通IO读写（使用d1000_in_bit/d1000_set_outbit）
  - 专用轴信号读取（使用d1000_get_axis_status）
  - 10Hz频率的IO状态监控
- **支持IO类型**: 基础IO、扩展IO、专用轴信号（ORG、PEL、NEL）

#### DMC1000BMotorManager.cs
- **作用**: 4轴步进电机控制
- **支持电机**:
  - 轴0: 左翻转电机
  - 轴1: 右翻转电机  
  - 轴2: 输出皮带电机
  - 轴3: 输入皮带电机
- **功能**: 电机运动、回原点、位置保存、参数设置

#### EpsonRobotManager.cs
- **作用**: Epson 6轴机器人控制
- **通信方式**: TCP/IP (默认端口9999)
- **功能**: 机器人运动控制、状态监控、程序执行

### 2. 数据模型层 (Models/)

#### IOModels.cs
- **IOPortDefinition**: IO端口定义
- **IOConfiguration**: IO配置管理
- **IOPortType**: IO类型枚举（Input/Output/AxisSpecialInput）

#### MotorModels.cs  
- **FlipMotorParams**: 翻转电机参数
- **BeltMotorParams**: 皮带电机参数
- **参数验证**: 完整的参数范围和逻辑验证

#### RobotModels.cs
- **RobotPosition**: 机器人位置信息
- **RobotStatus**: 机器人状态信息

### 3. 用户界面层 (UI/)

#### MainForm.cs
- **作用**: 主窗体，整合所有功能模块
- **布局**: 选项卡式界面，分别显示不同的控制面板

#### Controls/MotorFlipPanel.cs
- **作用**: 左右翻转电机控制面板
- **功能**: 
  - 电机参数设置（脉冲当量、起始速度、最大速度、加速时间）
  - 电机运动控制（角度旋转、回原点）
  - 位置保存和移动到指定位置
  - 实时状态显示

#### Controls/IOControlPanel.cs  
- **作用**: IO控制面板
- **功能**: IO状态监控、输出控制、批量操作

#### Controls/Robot6AxisPanel.cs
- **作用**: 6轴机器人控制面板  
- **功能**: 机器人运动控制、状态监控、程序管理

### 4. 辅助工具层 (Helpers/)

#### LogHelper.cs
- **作用**: 统一的日志管理
- **功能**: 分级日志记录、文件输出、控制台输出

#### ExceptionHelper.cs
- **作用**: 异常处理封装
- **功能**: 安全执行、异常捕获、错误日志记录

#### ConfigHelper.cs
- **作用**: 配置文件管理
- **功能**: JSON配置读取、参数管理

## 硬件配置

### DMC1000B控制卡端口定义

| 脚位号 | 名称 | 连接位置 | 类型 |
|--------|------|----------|------|
| 14 | ORG0 | 左翻转电机原点 | 专用轴信号 |
| 19 | ORG1 | 右翻转电机原点 | 专用轴信号 |
| 30 | I0001 | 启动按钮 | 普通输入 |
| 31 | I0002 | 暂停/停止按钮 | 普通输入 |
| 32 | I0003 | 急停按钮 | 普通输入 |
| 43 | O0001 | 左夹料气缸 | 普通输出 |
| 44 | O0002 | 左顶料气缸 | 普通输出 |
| ... | ... | ... | ... |

### 电机参数配置

#### 翻转电机默认参数
- **脉冲当量**: 0.012°/pulse
- **起始速度**: 5°/s  
- **最大速度**: 60°/s
- **加速时间**: 0.1s
- **最大加速度**: 120°/s²

## 开发指南

### 添加新的IO设备

1. **在IOModels.cs中添加IO定义**:
```csharp
new IOPortDefinition { 
    IONumber = "I0017", 
    PinNumber = 69, 
    Name = "新传感器", 
    Connection = "新传感器连接", 
    Type = IOPortType.Input, 
    IsExtended = false, 
    BitNumber = 17 
}
```

2. **更新UI界面显示**:
在IOControlPanel.cs中会自动包含新的IO定义。

### 添加新的电机轴

1. **在DMC1000BMotorManager.cs中添加轴定义**:
```csharp
public const short AXIS_NEW_MOTOR = 4;  // 新电机轴
```

2. **创建电机参数模型**:
在MotorModels.cs中添加新的电机参数类。

3. **实现电机控制方法**:
在DMC1000BMotorManager.cs中添加对应的控制方法。

### 添加新的机器人功能

1. **在RobotModels.cs中添加数据模型**
2. **在EpsonRobotManager.cs中实现控制逻辑**  
3. **在Robot6AxisPanel.cs中添加UI控件**

### 扩展专用轴信号

1. **在IOModels.cs的AxisSpecialInputPorts中添加定义**:
```csharp
new IOPortDefinition { 
    IONumber = "PEL0", 
    Name = "左翻转电机正限位", 
    Type = IOPortType.AxisSpecialInput, 
    BitNumber = 1,  // +EL信号位
    AxisNumber = 0 
}
```

2. **在DMC1000BIOManager.cs的ReadAxisSpecialInputAsync中添加解析逻辑**:
```csharp
case "PEL0":
case "PEL1":
    signalState = (axisState & 0x02) != 0; // 位1是+EL信号
    break;
```

## 编译和部署

### 开发环境要求
- Visual Studio 2019或更高版本
- .NET Framework 4.7.2或更高版本
- Windows 10或更高版本

### 编译步骤
```bash
# 使用dotnet CLI编译
dotnet build MyHMI.sln --configuration Debug

# 或使用MSBuild
msbuild MyHMI.sln /p:Configuration=Debug /p:Platform=x64
```

### 部署要求
- 目标机器需要安装.NET Framework运行时
- 需要DMC1000B控制卡驱动和Dmc1000.dll
- 确保硬件设备正确连接

## 故障排除

### 常见问题

1. **控制卡初始化失败**
   - 检查DMC1000B控制卡连接
   - 确认Dmc1000.dll文件存在
   - 检查驱动程序安装

2. **IO状态读取异常**  
   - 检查24V外部电源连接（67脚和66脚）
   - 确认IO设备正确连接
   - 检查IO配置是否正确

3. **电机运动异常**
   - 检查电机参数设置
   - 确认原点开关和限位开关连接
   - 检查电机驱动器状态

4. **机器人通信失败**
   - 检查网络连接和IP地址配置
   - 确认机器人控制器状态
   - 检查防火墙设置

### 日志查看
系统日志保存在程序目录的Logs文件夹中，按日期分类存储。

## 贡献指南

1. 遵循现有的代码风格和架构模式
2. 添加完整的中文注释
3. 进行充分的测试验证
4. 更新相关文档
5. 记录开发日志

## 许可证

本项目为内部开发项目，版权归公司所有。

## 系统架构详解

### 分层架构设计

```
┌─────────────────────────────────────────┐
│              UI Layer (UI/)             │
│  MainForm, MotorFlipPanel, IOControl... │
├─────────────────────────────────────────┤
│           Manager Layer (Managers/)     │
│  DMC1000BMotorManager, IOManager...     │
├─────────────────────────────────────────┤
│            Model Layer (Models/)        │
│  MotorModels, IOModels, RobotModels     │
├─────────────────────────────────────────┤
│           Helper Layer (Helpers/)       │
│  LogHelper, ExceptionHelper, Config...  │
├─────────────────────────────────────────┤
│              Hardware Layer             │
│  DMC1000B Card, Epson Robot, IO Devices│
└─────────────────────────────────────────┘
```

### 设计模式应用

1. **单例模式**: 所有Manager类都采用单例模式，确保全局唯一实例
2. **观察者模式**: 事件驱动的状态监控和UI更新
3. **工厂模式**: 统一的异常处理和日志记录
4. **策略模式**: 不同类型IO的读取策略

## API参考

### DMC1000BMotorManager 主要方法

```csharp
// 电机初始化
Task<bool> InitializeAsync()

// 设置电机参数
Task<bool> SetFlipMotorParamsAsync(short axis, FlipMotorParams parameters)

// 电机旋转
Task<bool> RotateFlipMotorAsync(short axis, double angle)

// 电机回原点
Task<bool> HomeFlipMotorAsync(short axis)

// 保存电机位置
Task<bool> SaveFlipMotorPositionAsync(short axis, int positionIndex)

// 移动到保存位置
Task<bool> MoveToSavedPositionAsync(short axis, int positionIndex)

// 获取电机当前位置
Task<double> GetMotorCurrentAngleAsync(short axis)
```

### DMC1000BIOManager 主要方法

```csharp
// IO管理器初始化
Task<bool> InitializeAsync()

// 读取输入IO状态
Task<bool> ReadInputAsync(string ioNumber)

// 设置输出IO状态
Task<bool> SetOutputAsync(string ioNumber, bool state)

// 读取专用轴信号
Task<bool> ReadAxisSpecialInputAsync(string ioNumber)

// 批量读取所有输入IO
Task<Dictionary<string, bool>> ReadAllInputsAsync()

// 启动IO状态监控
Task<bool> StartMonitoringAsync()
```

### EpsonRobotManager 主要方法

```csharp
// 机器人连接
Task<bool> ConnectAsync(string ipAddress, int port)

// 机器人运动
Task<bool> MoveToPositionAsync(RobotPosition position)

// 执行机器人程序
Task<bool> ExecuteProgramAsync(string programName)

// 获取机器人状态
Task<RobotStatus> GetStatusAsync()
```

## 配置文件说明

### SystemConfig.json 配置项

```json
{
  "System": {
    "Name": "上位机控制系统",
    "Version": "1.0.0",
    "LogLevel": "Info",           // 日志级别: Debug/Info/Warning/Error
    "MaxLogFiles": 30,            // 最大日志文件数
    "AutoStartWorkflow": false    // 是否自动启动工作流
  },
  "IO": {
    "CardType": "DMC1000",        // 控制卡类型
    "InputChannels": 16,          // 输入通道数
    "OutputChannels": 16,         // 输出通道数
    "MonitorInterval": 100,       // 监控间隔(ms)
    "DebounceTime": 50           // 防抖时间(ms)
  },
  "Motor": {
    "AxisCount": 4,               // 轴数量
    "DefaultSpeed": 1000,         // 默认速度
    "DefaultAcceleration": 5000,  // 默认加速度
    "HomeSpeed": 500,             // 回零速度
    "MaxPosition": 100000,        // 最大位置
    "MinPosition": -100000,       // 最小位置
    "PositionTolerance": 0.01     // 位置容差
  },
  "Robot": {
    "IPAddress": "*************", // 机器人IP地址
    "Port": 9999,                 // 通信端口
    "ConnectionTimeout": 5000,    // 连接超时(ms)
    "CommandTimeout": 10000       // 命令超时(ms)
  }
}
```

## 开发最佳实践

### 1. 异常处理规范

```csharp
// 使用ExceptionHelper进行安全执行
public async Task<bool> YourMethodAsync()
{
    return await ExceptionHelper.SafeExecuteAsync<bool>(async () =>
    {
        // 您的业务逻辑
        return true;
    }, false, "操作描述");
}
```

### 2. 日志记录规范

```csharp
// 使用LogHelper记录日志
LogHelper.Info("操作成功完成");
LogHelper.Warning("发现潜在问题");
LogHelper.Error("操作失败", exception);
LogHelper.Debug("调试信息");
```

### 3. 参数验证规范

```csharp
// 在Model类中实现Validate方法
public ValidationResult Validate()
{
    if (Speed <= 0)
        return new ValidationResult(false, "速度必须大于0");

    if (Speed > MaxSpeed)
        return new ValidationResult(false, $"速度不能超过{MaxSpeed}");

    return new ValidationResult(true, "验证通过");
}
```

### 4. UI更新规范

```csharp
// 使用Invoke确保线程安全的UI更新
private void UpdateUI(string message)
{
    if (InvokeRequired)
    {
        Invoke(new Action<string>(UpdateUI), message);
        return;
    }

    // 更新UI控件
    statusLabel.Text = message;
}
```

## 性能优化建议

### 1. IO监控优化
- 监控频率设置为10Hz，平衡实时性和性能
- 使用缓存机制减少重复读取
- 批量读取IO状态提高效率

### 2. 电机控制优化
- 参数验证在UI层进行，减少无效调用
- 使用异步方法避免UI阻塞
- 合理设置超时时间

### 3. 内存管理
- 及时释放资源，特别是硬件连接
- 使用using语句管理IDisposable对象
- 避免内存泄漏，注意事件订阅和取消

## 测试指南

### 单元测试

```csharp
[Test]
public async Task TestMotorRotation()
{
    // Arrange
    var motorManager = DMC1000BMotorManager.Instance;
    await motorManager.InitializeAsync();

    // Act
    var result = await motorManager.RotateFlipMotorAsync(0, 90);

    // Assert
    Assert.IsTrue(result);
}
```

### 集成测试

1. **硬件连接测试**: 验证所有硬件设备连接正常
2. **IO功能测试**: 测试所有IO的读写功能
3. **电机运动测试**: 测试电机的各种运动模式
4. **机器人通信测试**: 测试机器人的通信和控制

### 性能测试

1. **IO监控性能**: 验证10Hz监控频率的稳定性
2. **电机响应时间**: 测试电机命令的响应时间
3. **内存使用情况**: 长时间运行的内存占用测试

## 版本历史

### v1.0.0 (当前版本)
- ✅ 完成DMC1000B控制卡集成
- ✅ 实现4轴电机控制功能
- ✅ 完成IO监控系统
- ✅ 集成Epson机器人控制
- ✅ 实现专用轴信号监控（ORG0/ORG1）
- ✅ 添加起始速度控制功能
- ✅ 完善参数验证和异常处理

### 计划功能
- 🔄 添加更多专用轴信号支持（PEL/NEL）
- 🔄 实现工作流自动化
- 🔄 添加数据统计和报表功能
- 🔄 支持多语言界面
- 🔄 添加远程监控功能

## 联系信息

如有问题或建议，请联系开发团队。

---

**最后更新**: 2025-01-21
**文档版本**: v1.0.0
