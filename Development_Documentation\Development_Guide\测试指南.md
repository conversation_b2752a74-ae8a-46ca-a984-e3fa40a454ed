# MyHMI 上位机控制系统 - 测试指南

## 测试概述

MyHMI 系统提供了完整的测试框架，支持单元测试、集成测试和系统测试。测试框架包括 Mock 对象、测试运行器和自动化测试工具。

## 测试架构

```
Testing/
├── SimpleTestFramework.cs         # 轻量级测试框架
├── SystemTests.cs                 # 系统测试用例
├── MockIOManager.cs               # IO管理器Mock类
├── MockMotorManager.cs            # 电机管理器Mock类
├── TestRunnerForm.cs              # 测试运行器界面
└── TestRunnerForm.Designer.cs     # 测试运行器设计器文件
```

## 测试框架使用

### SimpleTestFramework 基本用法

#### 创建测试框架实例
```csharp
var framework = new SimpleTestFramework();
```

#### 添加测试用例
```csharp
// 添加异步测试
framework.AddTest("异步测试示例", async () =>
{
    var result = await SomeAsyncOperation();
    SimpleTestFramework.AssertTrue(result, "操作应该成功");
}, "AsyncTests");

// 添加同步测试
framework.AddTest("同步测试示例", () =>
{
    var value = SomeOperation();
    SimpleTestFramework.AssertEqual(100, value, "值应该等于100");
}, "SyncTests");
```

#### 运行测试
```csharp
// 运行所有测试
var result = await framework.RunAllTestsAsync();

// 运行指定分类的测试
var result = await framework.RunTestsByCategoryAsync("AsyncTests");
```

### 断言方法

#### 基本断言
```csharp
// 断言为真
SimpleTestFramework.AssertTrue(condition, "失败消息");

// 断言为假
SimpleTestFramework.AssertFalse(condition, "失败消息");

// 断言相等
SimpleTestFramework.AssertEqual(expected, actual, "失败消息");

// 断言不相等
SimpleTestFramework.AssertNotEqual(expected, actual, "失败消息");

// 断言为空
SimpleTestFramework.AssertNull(value, "失败消息");

// 断言不为空
SimpleTestFramework.AssertNotNull(value, "失败消息");
```

#### 异常断言
```csharp
// 断言抛出指定异常
SimpleTestFramework.AssertThrows<ArgumentException>(() =>
{
    SomeMethodThatShouldThrowException();
}, "应该抛出ArgumentException");
```

## Mock 对象使用

### MockIOManager 使用

#### 基本操作测试
```csharp
framework.AddTest("MockIOManager基本操作测试", async () =>
{
    var mockIO = MockIOManager.Instance;
    
    // 初始化
    bool initResult = await mockIO.InitializeAsync();
    SimpleTestFramework.AssertTrue(initResult, "初始化应该成功");
    
    // 设置输出
    bool setResult = await mockIO.SetOutputAsync(0, true);
    SimpleTestFramework.AssertTrue(setResult, "设置输出应该成功");
    
    // 读取输出
    bool outputState = await mockIO.ReadOutputAsync(0);
    SimpleTestFramework.AssertTrue(outputState, "输出状态应该为true");
    
    // 读取输入
    bool inputState = await mockIO.ReadInputAsync(0);
    // 输入状态可能是随机的，只要不抛异常就算成功
    SimpleTestFramework.AssertTrue(true, "读取输入应该成功");
});
```

#### 模拟输入变化
```csharp
framework.AddTest("模拟输入变化测试", async () =>
{
    var mockIO = MockIOManager.Instance;
    await mockIO.InitializeAsync();
    
    bool eventTriggered = false;
    mockIO.IOStateChanged += (sender, e) =>
    {
        if (e.Channel == 1 && e.IsInput && e.State == true)
        {
            eventTriggered = true;
        }
    };
    
    // 模拟输入变化
    mockIO.SimulateInputChange(1, true);
    
    // 等待事件处理
    await Task.Delay(100);
    
    SimpleTestFramework.AssertTrue(eventTriggered, "应该触发IO状态变化事件");
});
```

### MockMotorManager 使用

#### 电机运动测试
```csharp
framework.AddTest("电机运动测试", async () =>
{
    var mockMotor = MockMotorManager.Instance;
    await mockMotor.InitializeAsync();
    
    // 移动到指定位置
    bool moveResult = await mockMotor.MoveToAsync(0, 100.0, 1000.0);
    SimpleTestFramework.AssertTrue(moveResult, "电机移动应该成功");
    
    // 等待运动完成
    await Task.Delay(500);
    
    // 检查位置
    var status = await mockMotor.GetMotorStatusAsync(0);
    SimpleTestFramework.AssertEqual(100.0, status.CurrentPosition, "电机应该到达目标位置", 0.1);
    SimpleTestFramework.AssertFalse(status.IsMoving, "运动完成后应该停止");
});
```

#### 电机错误模拟
```csharp
framework.AddTest("电机错误处理测试", async () =>
{
    var mockMotor = MockMotorManager.Instance;
    await mockMotor.InitializeAsync();
    
    bool errorEventTriggered = false;
    mockMotor.MotorStatusChanged += (sender, e) =>
    {
        if (e.Status.HasError)
        {
            errorEventTriggered = true;
        }
    };
    
    // 模拟电机错误
    mockMotor.SimulateMotorError(0, "模拟错误");
    
    // 等待事件处理
    await Task.Delay(100);
    
    SimpleTestFramework.AssertTrue(errorEventTriggered, "应该触发电机错误事件");
    
    // 检查错误状态
    var status = await mockMotor.GetMotorStatusAsync(0);
    SimpleTestFramework.AssertTrue(status.HasError, "电机应该处于错误状态");
    SimpleTestFramework.AssertEqual("模拟错误", status.ErrorMessage, "错误消息应该正确");
});
```

## 系统测试用例

### 配置系统测试
```csharp
framework.AddTest("配置系统测试", async () =>
{
    // 初始化配置系统
    ConfigHelper.Initialize();
    
    // 加载配置
    bool configLoaded = SystemConfiguration.Instance.LoadConfiguration();
    SimpleTestFramework.AssertTrue(configLoaded || SystemConfiguration.Instance.IsLoaded, 
        "配置系统应该能够加载配置");
    
    // 验证配置内容
    var config = SystemConfiguration.Instance.Config;
    SimpleTestFramework.AssertNotNull(config, "配置对象不应该为空");
    SimpleTestFramework.AssertNotNull(config.System, "系统配置不应该为空");
    
    await Task.CompletedTask;
});
```

### 辅助类测试
```csharp
framework.AddTest("ExceptionHelper测试", async () =>
{
    // 测试成功执行
    bool result1 = await ExceptionHelper.SafeExecuteAsync(async () =>
    {
        await Task.Delay(10);
        return true;
    }, false, "测试操作");
    
    SimpleTestFramework.AssertTrue(result1, "正常操作应该返回true");
    
    // 测试异常处理
    bool result2 = await ExceptionHelper.SafeExecuteAsync(async () =>
    {
        await Task.Delay(10);
        throw new InvalidOperationException("测试异常");
        return true;
    }, false, "测试异常操作");
    
    SimpleTestFramework.AssertFalse(result2, "异常操作应该返回false");
});
```

## 测试运行器使用

### 启动测试运行器
1. 运行 MyHMI 应用程序
2. 点击菜单 "工具 → 测试运行器"
3. 测试运行器窗口将打开

### 运行测试
```csharp
// 运行所有测试
点击 "运行所有测试" 按钮

// 运行特定分类测试
点击 "Mock管理器测试" 按钮
点击 "配置系统测试" 按钮
点击 "辅助类测试" 按钮
```

### 查看测试结果
- **绿色文本**: 测试通过
- **红色文本**: 测试失败
- **黄色文本**: 测试跳过
- **测试摘要**: 显示总数、通过数、失败数、跳过数和成功率

## 编写自定义测试

### 创建新的测试类
```csharp
public static class CustomTests
{
    public static async Task<TestResult> RunCustomTestsAsync()
    {
        var framework = new SimpleTestFramework();
        
        // 添加自定义测试
        RegisterCustomTests(framework);
        
        return await framework.RunAllTestsAsync();
    }
    
    private static void RegisterCustomTests(SimpleTestFramework framework)
    {
        framework.AddTest("自定义测试1", async () =>
        {
            // 测试逻辑
            await Task.Delay(100);
            SimpleTestFramework.AssertTrue(true, "测试通过");
        }, "Custom");
        
        framework.AddTest("自定义测试2", () =>
        {
            // 同步测试逻辑
            var result = SomeCustomOperation();
            SimpleTestFramework.AssertNotNull(result, "结果不应该为空");
        }, "Custom");
    }
}
```

### 集成到测试运行器
```csharp
// 在 TestRunnerForm 中添加新按钮
private void CustomTestsButton_Click(object sender, EventArgs e)
{
    await RunTestsAsync("自定义测试", async () => await CustomTests.RunCustomTestsAsync());
}
```

## 性能测试

### 响应时间测试
```csharp
framework.AddTest("响应时间测试", async () =>
{
    var stopwatch = Stopwatch.StartNew();
    
    await SomeOperation();
    
    stopwatch.Stop();
    
    SimpleTestFramework.AssertTrue(stopwatch.ElapsedMilliseconds < 1000, 
        $"操作应该在1秒内完成，实际耗时: {stopwatch.ElapsedMilliseconds}ms");
});
```

### 并发测试
```csharp
framework.AddTest("并发操作测试", async () =>
{
    var tasks = new List<Task<bool>>();
    
    // 创建多个并发任务
    for (int i = 0; i < 10; i++)
    {
        tasks.Add(SomeAsyncOperation());
    }
    
    var results = await Task.WhenAll(tasks);
    
    SimpleTestFramework.AssertTrue(results.All(r => r), "所有并发操作都应该成功");
});
```

## 集成测试

### 工作流集成测试
```csharp
framework.AddTest("工作流集成测试", async () =>
{
    // 初始化所有管理器
    await IOManager.Instance.InitializeAsync();
    await MotorManager.Instance.InitializeAsync();
    await WorkflowManager.Instance.InitializeAsync();
    
    // 启动工作流
    bool startResult = await WorkflowManager.Instance.StartWorkflowAsync();
    SimpleTestFramework.AssertTrue(startResult, "工作流启动应该成功");
    
    // 等待工作流运行
    await Task.Delay(1000);
    
    // 检查工作流状态
    bool isRunning = WorkflowManager.Instance.IsRunning;
    SimpleTestFramework.AssertTrue(isRunning, "工作流应该正在运行");
    
    // 停止工作流
    bool stopResult = await WorkflowManager.Instance.StopWorkflowAsync();
    SimpleTestFramework.AssertTrue(stopResult, "工作流停止应该成功");
});
```

## 测试数据管理

### 测试数据准备
```csharp
public static class TestDataHelper
{
    public static void PrepareTestData()
    {
        // 创建测试配置文件
        CreateTestConfigFile();
        
        // 创建测试数据目录
        Directory.CreateDirectory("./TestData");
        
        // 准备测试图像
        CreateTestImages();
    }
    
    public static void CleanupTestData()
    {
        // 清理测试数据
        if (Directory.Exists("./TestData"))
        {
            Directory.Delete("./TestData", true);
        }
    }
}
```

### 测试环境隔离
```csharp
framework.AddTest("环境隔离测试", async () =>
{
    // 备份当前配置
    var originalConfig = SystemConfiguration.Instance.Config;
    
    try
    {
        // 使用测试配置
        TestDataHelper.PrepareTestData();
        
        // 执行测试
        await SomeTestOperation();
        
        SimpleTestFramework.AssertTrue(true, "测试执行成功");
    }
    finally
    {
        // 恢复原始配置
        SystemConfiguration.Instance.Config = originalConfig;
        TestDataHelper.CleanupTestData();
    }
});
```

## 测试最佳实践

### 1. 测试命名规范
```csharp
// 好的测试名称
"IOManager初始化测试"
"电机移动到指定位置测试"
"配置文件加载失败处理测试"

// 避免的测试名称
"测试1"
"Test"
"检查功能"
```

### 2. 测试独立性
```csharp
// 每个测试应该独立，不依赖其他测试的结果
framework.AddTest("独立测试示例", async () =>
{
    // 准备测试数据
    var testData = PrepareTestData();
    
    try
    {
        // 执行测试
        var result = await TestOperation(testData);
        SimpleTestFramework.AssertTrue(result, "操作应该成功");
    }
    finally
    {
        // 清理测试数据
        CleanupTestData(testData);
    }
});
```

### 3. 异常测试
```csharp
// 测试异常情况
framework.AddTest("参数验证测试", async () =>
{
    SimpleTestFramework.AssertThrows<ArgumentNullException>(() =>
    {
        SomeMethod(null);
    }, "空参数应该抛出ArgumentNullException");
});
```

### 4. 边界条件测试
```csharp
framework.AddTest("边界条件测试", async () =>
{
    // 测试最小值
    var result1 = await TestWithValue(int.MinValue);
    SimpleTestFramework.AssertTrue(result1, "最小值测试应该通过");
    
    // 测试最大值
    var result2 = await TestWithValue(int.MaxValue);
    SimpleTestFramework.AssertTrue(result2, "最大值测试应该通过");
    
    // 测试零值
    var result3 = await TestWithValue(0);
    SimpleTestFramework.AssertTrue(result3, "零值测试应该通过");
});
```

通过完善的测试框架和测试用例，可以确保 MyHMI 系统的稳定性和可靠性。
