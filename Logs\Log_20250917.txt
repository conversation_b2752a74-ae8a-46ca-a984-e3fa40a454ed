[2025-09-17 12:05:12.128] [INFO] 程序启动开始
[2025-09-17 12:05:12.128] [INFO] 配置系统初始化成功
[2025-09-17 12:05:12.285] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR\bin\x64\Debug\Config\SystemConfig.json
[2025-09-17 12:05:12.285] [INFO] 配置系统初始化完成
[2025-09-17 12:05:12.285] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-17 12:05:12.285] [INFO] 开始初始化各个Manager...
[2025-09-17 12:05:12.285] [INFO] 初始化基础Manager...
[2025-09-17 12:05:12.300] [INFO] 开始初始化IOManager...
[2025-09-17 12:05:12.300] [INFO] 模拟初始化雷赛卡，卡号: 0
[2025-09-17 12:05:12.410] [INFO] IO监控任务已启动
[2025-09-17 12:05:12.410] [INFO] IOManager初始化完成
[2025-09-17 12:05:12.425] [INFO] IO监控循环开始
[2025-09-17 12:05:12.425] [INFO] 开始初始化MotorManager...
[2025-09-17 12:05:12.425] [INFO] 模拟初始化运动控制卡
[2025-09-17 12:05:12.646] [INFO] 加载了8个电机的默认配置
[2025-09-17 12:05:12.646] [INFO] 电机监控任务已启动
[2025-09-17 12:05:12.646] [INFO] MotorManager初始化完成
[2025-09-17 12:05:12.646] [INFO] 初始化通信Manager...
[2025-09-17 12:05:12.646] [INFO] 电机监控循环开始
[2025-09-17 12:05:12.646] [INFO] 开始初始化ScannerManager...
[2025-09-17 12:05:12.646] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-17 12:05:12.646] [INFO] 串口初始化完成
[2025-09-17 12:05:12.646] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-17 12:05:12.646] [INFO] 扫描枪连接成功: COM1
[2025-09-17 12:05:12.646] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-17 12:05:12.646] [INFO] ScannerManager初始化完成
[2025-09-17 12:05:12.662] [INFO] 开始初始化ModbusTcpManager...
[2025-09-17 12:05:12.662] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-17 12:05:12.662] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-17 12:05:17.665] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR\Helpers\ExceptionHelper.cs:行号 119
[2025-09-17 12:05:17.680] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-17 12:05:17.680] [INFO] ModbusTcpManager初始化完成
[2025-09-17 12:05:17.680] [INFO] 开始初始化RobotTcpManager...
[2025-09-17 12:05:17.696] [INFO] 机器人TCP配置: 192.168.1.100:8080
[2025-09-17 12:05:17.696] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-17 12:05:22.699] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: 192.168.1.100:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR\Helpers\ExceptionHelper.cs:行号 119
[2025-09-17 12:05:22.699] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-17 12:05:22.699] [INFO] RobotTcpManager初始化完成
[2025-09-17 12:05:22.699] [INFO] 初始化视觉Manager...
[2025-09-17 12:05:22.699] [INFO] 开始初始化VisionManager...
[2025-09-17 12:05:22.699] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-17 12:05:22.699] [INFO] 模拟初始化相机，索引: 0
[2025-09-17 12:05:23.207] [INFO] 相机初始化成功
[2025-09-17 12:05:23.209] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-17 12:05:23.209] [INFO] 视觉配置加载完成
[2025-09-17 12:05:23.209] [INFO] VisionManager初始化完成
[2025-09-17 12:05:23.209] [INFO] 初始化数据Manager...
[2025-09-17 12:05:23.215] [INFO] 开始初始化StatisticsManager...
[2025-09-17 12:05:23.215] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-17 12:05:23.218] [INFO] 创建数据目录: Data\Statistics
[2025-09-17 12:05:23.218] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-17 12:05:23.218] [INFO] 历史数据加载完成
[2025-09-17 12:05:23.218] [INFO] 自动保存任务已启动
[2025-09-17 12:05:23.218] [INFO] StatisticsManager初始化完成
[2025-09-17 12:05:23.218] [INFO] Manager初始化完成，成功率: 100%
[2025-09-17 12:05:23.218] [INFO] 所有Manager初始化完成
[2025-09-17 12:05:23.229] [INFO] 自动保存循环开始
[2025-09-17 12:05:23.583] [INFO] 开始初始化WorkflowManager...
[2025-09-17 12:05:23.585] [INFO] 工作流事件订阅完成
[2025-09-17 12:05:23.585] [INFO] WorkflowManager初始化完成
[2025-09-17 12:08:52.445] [INFO] 开始释放WorkflowManager资源...
[2025-09-17 12:08:52.450] [INFO] 工作流事件取消订阅完成
[2025-09-17 12:08:52.470] [WARN] 没有可用的UI窗体进行线程切换
[2025-09-17 12:08:52.470] [INFO] WorkflowManager资源释放完成
[2025-09-17 12:08:52.470] [WARN] 没有可用的UI窗体进行线程切换
[2025-09-17 12:08:52.470] [WARN] 没有可用的UI窗体进行线程切换
[2025-09-17 12:08:52.475] [WARN] 没有可用的UI窗体进行线程切换
[2025-09-17 12:08:52.475] [WARN] 没有可用的UI窗体进行线程切换
[2025-09-17 12:08:52.475] [INFO] IO控制面板资源释放完成
