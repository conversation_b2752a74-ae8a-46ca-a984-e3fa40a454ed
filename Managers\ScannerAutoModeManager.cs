using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using MyHMI.Helpers;
using MyHMI.Events;
using MyHMI.Models;

namespace MyHMI.Managers
{
    /// <summary>
    /// 扫码器自动模式管理器
    /// 负责监听SCARA通信字段变化，自动触发扫码操作
    /// </summary>
    public class ScannerAutoModeManager
    {
        #region 单例模式
        private static readonly Lazy<ScannerAutoModeManager> _instance = new Lazy<ScannerAutoModeManager>(() => new ScannerAutoModeManager());
        public static ScannerAutoModeManager Instance => _instance.Value;
        private ScannerAutoModeManager() 
        {
            InitializeManagers();
        }
        #endregion

        #region 私有字段
        private MultiScannerManager _multiScannerManager;
        private ScaraCommunicationManager _scaraCommunicationManager;
        private readonly object _lockObject = new object();
        private bool _isInitialized = false;
        private bool _isAutoModeEnabled = false;
        private CancellationTokenSource _cancellationTokenSource;

        // 扫码器数据存储
        private readonly Dictionary<int, string> _scannerData = new Dictionary<int, string>();
        private readonly object _dataLock = new object();

        // 中间扫码器专用数据存储
        private string _middleLeftScannerData = string.Empty;
        private string _middleRightScannerData = string.Empty;

        // 扫码器状态跟踪
        private readonly Dictionary<int, bool> _scannerCompleted = new Dictionary<int, bool>();
        private bool _middleLeftCompleted = false;
        private bool _middleRightCompleted = false;

        // 中间扫码器触发状态跟踪
        private string _currentMiddleTrigger = string.Empty; // "ML" 或 "MR" 或空字符串
        #endregion

        #region 事件定义
        /// <summary>
        /// 扫码完成事件
        /// </summary>
        public event EventHandler<ScannerCompletedEventArgs> ScannerCompleted;

        /// <summary>
        /// 所有扫码器完成事件
        /// </summary>
        public event EventHandler<AllScannersCompletedEventArgs> AllScannersCompleted;

        /// <summary>
        /// 自动模式状态变化事件
        /// </summary>
        public event EventHandler<AutoModeStatusChangedEventArgs> AutoModeStatusChanged;
        #endregion

        #region 公共属性
        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// 自动模式是否启用
        /// </summary>
        public bool IsAutoModeEnabled => _isAutoModeEnabled;

        /// <summary>
        /// 获取扫码器数据
        /// </summary>
        /// <param name="scannerId">扫码器ID</param>
        /// <returns>扫码数据</returns>
        public string GetScannerData(int scannerId)
        {
            lock (_dataLock)
            {
                return _scannerData.TryGetValue(scannerId, out string data) ? data : string.Empty;
            }
        }

        /// <summary>
        /// 获取所有扫码器数据
        /// </summary>
        /// <returns>扫码器数据字典</returns>
        public Dictionary<int, string> GetAllScannerData()
        {
            lock (_dataLock)
            {
                return new Dictionary<int, string>(_scannerData);
            }
        }

        /// <summary>
        /// 获取中间向左扫码器数据
        /// </summary>
        /// <returns>中间向左扫码数据</returns>
        public string GetMiddleLeftScannerData()
        {
            lock (_dataLock)
            {
                return _middleLeftScannerData;
            }
        }

        /// <summary>
        /// 获取中间向右扫码器数据
        /// </summary>
        /// <returns>中间向右扫码数据</returns>
        public string GetMiddleRightScannerData()
        {
            lock (_dataLock)
            {
                return _middleRightScannerData;
            }
        }
        #endregion

        #region 初始化和释放
        /// <summary>
        /// 初始化管理器
        /// </summary>
        private void InitializeManagers()
        {
            try
            {
                _multiScannerManager = MultiScannerManager.Instance;
                _scaraCommunicationManager = ScaraCommunicationManager.Instance;

                // 初始化扫码器状态
                for (int i = 1; i <= 3; i++)
                {
                    _scannerData[i] = string.Empty;
                    _scannerCompleted[i] = false;
                }

                LogHelper.Info("ScannerAutoModeManager管理器引用初始化完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("ScannerAutoModeManager管理器引用初始化失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 异步初始化扫码器自动模式管理器
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> InitializeAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                lock (_lockObject)
                {
                    if (_isInitialized)
                    {
                        LogHelper.Warning("ScannerAutoModeManager已经初始化，跳过重复初始化");
                        return true;
                    }
                }

                LogHelper.Info("开始初始化ScannerAutoModeManager...");

                // 初始化多扫码器管理器
                bool multiScannerInitResult = await _multiScannerManager.InitializeAsync();
                if (!multiScannerInitResult)
                {
                    LogHelper.Error("多扫码器管理器初始化失败");
                    return false;
                }

                // 订阅扫码器事件
                _multiScannerManager.BarcodeScanned += OnBarcodeScanned;
                _multiScannerManager.StatusChanged += OnScannerStatusChanged;

                // 订阅SCARA通信字段变化事件
                _scaraCommunicationManager.FieldChanged += OnScaraFieldChanged;

                // 注释掉自动连接验证，避免与开机自检重复
                // await AutoConnectAndVerifyScannersAsync();

                lock (_lockObject)
                {
                    _isInitialized = true;
                }

                LogHelper.Info("ScannerAutoModeManager初始化完成");
                return true;

            }, false, "ScannerAutoModeManager初始化");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public async Task DisposeAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info("开始释放ScannerAutoModeManager资源...");

                // 停止自动模式
                await StopAutoModeAsync();

                // 取消订阅事件
                if (_multiScannerManager != null)
                {
                    _multiScannerManager.BarcodeScanned -= OnBarcodeScanned;
                    _multiScannerManager.StatusChanged -= OnScannerStatusChanged;
                }

                if (_scaraCommunicationManager != null)
                {
                    _scaraCommunicationManager.FieldChanged -= OnScaraFieldChanged;
                }

                // 释放多扫码器管理器
                if (_multiScannerManager != null)
                {
                    await _multiScannerManager.DisposeAsync();
                }

                lock (_lockObject)
                {
                    _isInitialized = false;
                }

                LogHelper.Info("ScannerAutoModeManager资源释放完成");
                return true;

            }, false, "ScannerAutoModeManager资源释放");
        }
        #endregion

        #region 自动模式控制
        /// <summary>
        /// 启动自动模式
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> StartAutoModeAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                lock (_lockObject)
                {
                    if (!_isInitialized)
                    {
                        LogHelper.Error("ScannerAutoModeManager未初始化，无法启动自动模式");
                        return false;
                    }

                    if (_isAutoModeEnabled)
                    {
                        LogHelper.Warning("扫码器自动模式已经启动");
                        return true;
                    }

                    _cancellationTokenSource = new CancellationTokenSource();
                    _isAutoModeEnabled = true;
                }

                LogHelper.Info("扫码器自动模式已启动");
                OnAutoModeStatusChanged(true, "自动模式已启动");

                // 重置扫码器状态
                ResetScannerStates();

                await Task.CompletedTask;
                return true;

            }, false, "启动扫码器自动模式");
        }

        /// <summary>
        /// 停止自动模式
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> StopAutoModeAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                lock (_lockObject)
                {
                    if (!_isAutoModeEnabled)
                    {
                        LogHelper.Info("扫码器自动模式未启动，无需停止");
                        return true;
                    }

                    _isAutoModeEnabled = false;
                    _cancellationTokenSource?.Cancel();
                    _cancellationTokenSource?.Dispose();
                    _cancellationTokenSource = null;
                }

                LogHelper.Info("扫码器自动模式已停止");
                OnAutoModeStatusChanged(false, "自动模式已停止");

                await Task.CompletedTask;
                return true;

            }, false, "停止扫码器自动模式");
        }
        #endregion

        #region 自动连接和验证
        /// <summary>
        /// 自动连接并验证所有扫码器
        /// </summary>
        /// <returns>是否成功</returns>
        private async Task<bool> AutoConnectAndVerifyScannersAsync()
        {
            try
            {
                LogHelper.Info("开始自动连接并验证所有扫码器...");

                bool allConnected = true;

                // 连接所有扫码器
                for (int scannerId = 1; scannerId <= 3; scannerId++)
                {
                    try
                    {
                        LogHelper.Info($"正在连接扫码器{scannerId}...");

                        bool connectResult = await _multiScannerManager.ConnectScannerAsync(scannerId);
                        if (connectResult)
                        {
                            LogHelper.Info($"扫码器{scannerId}连接成功");

                            // 发送hello命令验证通信
                            bool verifyResult = await VerifyScannerCommunicationAsync(scannerId);
                            if (verifyResult)
                            {
                                LogHelper.Info($"扫码器{scannerId}通信验证成功");
                            }
                            else
                            {
                                LogHelper.Warning($"扫码器{scannerId}通信验证失败");
                                allConnected = false;
                            }
                        }
                        else
                        {
                            LogHelper.Warning($"扫码器{scannerId}连接失败");
                            allConnected = false;
                        }
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error($"连接扫码器{scannerId}时发生异常", ex);
                        allConnected = false;
                    }

                    // 连接间隔，避免串口冲突
                    await Task.Delay(500);
                }

                if (allConnected)
                {
                    LogHelper.Info("所有扫码器连接和验证完成");
                }
                else
                {
                    LogHelper.Warning("部分扫码器连接或验证失败，但系统将继续运行");
                }

                return true; // 即使部分失败也返回true，允许系统继续运行
            }
            catch (Exception ex)
            {
                LogHelper.Error("自动连接扫码器过程中发生异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 验证扫码器通信
        /// </summary>
        /// <param name="scannerId">扫码器ID</param>
        /// <returns>是否验证成功</returns>
        private async Task<bool> VerifyScannerCommunicationAsync(int scannerId)
        {
            try
            {
                // 发送hello命令
                bool sendResult = await _multiScannerManager.SendDataAsync(scannerId, "hello");
                if (!sendResult)
                {
                    LogHelper.Warning($"向扫码器{scannerId}发送hello命令失败");
                    return false;
                }

                // 等待回复（这里简化处理，实际应该监听接收事件）
                await Task.Delay(1000);

                LogHelper.Info($"扫码器{scannerId}hello验证完成");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"验证扫码器{scannerId}通信时发生异常", ex);
                return false;
            }
        }
        #endregion

        #region 事件处理
        /// <summary>
        /// 处理条码扫描事件
        /// </summary>
        private void OnBarcodeScanned(object sender, MultiBarcodeScannedEventArgs e)
        {
            try
            {
                string barcode = e.ScannerData?.Data ?? string.Empty;
                LogHelper.Info($"扫码器{e.ScannerId}扫描到条码: {barcode}");

                lock (_dataLock)
                {
                    // 处理中间扫码器（ID=3）的特殊逻辑
                    if (e.ScannerId == 3)
                    {
                        if (_currentMiddleTrigger == "ML")
                        {
                            _middleLeftScannerData = barcode;
                            _middleLeftCompleted = true;
                            LogHelper.Info($"中间向左扫码器数据已保存: {barcode}");
                        }
                        else if (_currentMiddleTrigger == "MR")
                        {
                            _middleRightScannerData = barcode;
                            _middleRightCompleted = true;
                            LogHelper.Info($"中间向右扫码器数据已保存: {barcode}");
                        }
                        else
                        {
                            LogHelper.Warning($"中间扫码器扫描到数据，但未找到对应的触发状态: {barcode}");
                        }

                        // 清除当前触发状态
                        _currentMiddleTrigger = string.Empty;
                    }
                    else
                    {
                        // 处理左右扫码器（ID=1,2）的正常逻辑
                        _scannerData[e.ScannerId] = barcode;
                        _scannerCompleted[e.ScannerId] = true;
                    }
                }

                // 触发单个扫码器完成事件
                OnScannerCompleted(e.ScannerId, barcode);

                // 检查是否所有扫码器都完成了
                CheckAllScannersCompleted();
            }
            catch (Exception ex)
            {
                LogHelper.Error($"处理扫码器{e.ScannerId}扫描事件时发生异常", ex);
            }
        }

        /// <summary>
        /// 处理扫码器状态变化事件
        /// </summary>
        private void OnScannerStatusChanged(object sender, MultiScannerStatusChangedEventArgs e)
        {
            try
            {
                LogHelper.Info($"扫码器{e.ScannerId}状态变化: {e.Status} - {e.Description}");
            }
            catch (Exception ex)
            {
                LogHelper.Error($"处理扫码器{e.ScannerId}状态变化事件时发生异常", ex);
            }
        }

        /// <summary>
        /// 处理SCARA通信字段变化事件
        /// </summary>
        private async void OnScaraFieldChanged(object sender, CommunicationEventArgs e)
        {
            try
            {
                if (!_isAutoModeEnabled)
                {
                    return; // 自动模式未启用，忽略事件
                }

                // 只处理从false变为true的字段变化
                if (!e.NewValue)
                {
                    return;
                }

                LogHelper.Info($"SCARA通信字段变化: {e.FieldName} = {e.NewValue}");

                // 根据字段名称触发对应的扫码器
                switch (e.FieldName)
                {
                    case "ML_dataget_ok":
                        await TriggerMiddleScannerAsync("ML", "中间向左扫码器");
                        break;
                    case "MR_dataget_ok":
                        await TriggerMiddleScannerAsync("MR", "中间向右扫码器");
                        break;
                    case "L_dataget_ok":
                        await TriggerScannerAsync(1, "左侧扫码器");
                        break;
                    case "R_dataget_ok":
                        await TriggerScannerAsync(2, "右侧扫码器");
                        break;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"处理SCARA字段变化事件时发生异常: {e.FieldName}", ex);
            }
        }
        #endregion

        #region 扫码触发逻辑
        /// <summary>
        /// 触发指定扫码器进行扫码
        /// </summary>
        /// <param name="scannerId">扫码器ID</param>
        /// <param name="scannerName">扫码器名称</param>
        /// <returns>是否成功</returns>
        private async Task<bool> TriggerScannerAsync(int scannerId, string scannerName)
        {
            try
            {
                LogHelper.Info($"触发{scannerName}(ID:{scannerId})开始扫码...");

                // 发送start命令触发扫码
                bool sendResult = await _multiScannerManager.SendDataAsync(scannerId, "start");
                if (sendResult)
                {
                    LogHelper.Info($"{scannerName}扫码命令发送成功");
                    return true;
                }
                else
                {
                    LogHelper.Error($"{scannerName}扫码命令发送失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"触发{scannerName}扫码时发生异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 触发中间扫码器进行扫码（支持不同的数据存储路径）
        /// </summary>
        /// <param name="direction">方向标识（ML或MR）</param>
        /// <param name="scannerName">扫码器名称</param>
        /// <returns>是否成功</returns>
        private async Task<bool> TriggerMiddleScannerAsync(string direction, string scannerName)
        {
            try
            {
                LogHelper.Info($"触发{scannerName}开始扫码...");

                // 发送start命令触发扫码（中间扫码器ID为3）
                bool sendResult = await _multiScannerManager.SendDataAsync(3, "start");
                if (sendResult)
                {
                    LogHelper.Info($"{scannerName}扫码命令发送成功");

                    // 标记对应方向的扫码器为已触发
                    lock (_dataLock)
                    {
                        _currentMiddleTrigger = direction; // 记录当前触发的方向

                        if (direction == "ML")
                        {
                            _middleLeftCompleted = false; // 重置完成状态，等待扫码结果
                        }
                        else if (direction == "MR")
                        {
                            _middleRightCompleted = false; // 重置完成状态，等待扫码结果
                        }
                    }

                    return true;
                }
                else
                {
                    LogHelper.Error($"{scannerName}扫码命令发送失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"触发{scannerName}扫码时发生异常", ex);
                return false;
            }
        }
        #endregion

        #region 辅助方法
        /// <summary>
        /// 重置扫码器状态
        /// </summary>
        private void ResetScannerStates()
        {
            lock (_dataLock)
            {
                for (int i = 1; i <= 3; i++)
                {
                    _scannerData[i] = string.Empty;
                    _scannerCompleted[i] = false;
                }

                // 重置中间扫码器的专用数据和状态
                _middleLeftScannerData = string.Empty;
                _middleRightScannerData = string.Empty;
                _middleLeftCompleted = false;
                _middleRightCompleted = false;
                _currentMiddleTrigger = string.Empty;
            }
            LogHelper.Info("扫码器状态已重置");
        }

        /// <summary>
        /// 检查所有扫码器是否都完成了扫码
        /// </summary>
        private void CheckAllScannersCompleted()
        {
            lock (_dataLock)
            {
                bool allCompleted = true;
                for (int i = 1; i <= 3; i++)
                {
                    if (!_scannerCompleted[i])
                    {
                        allCompleted = false;
                        break;
                    }
                }

                if (allCompleted)
                {
                    LogHelper.Info("所有扫码器都已完成扫码");

                    // 触发所有扫码器完成事件
                    OnAllScannersCompleted();

                    // 复位SCARA通信字段
                    ResetScaraCommunicationFields();
                }
            }
        }

        /// <summary>
        /// 复位SCARA通信字段
        /// </summary>
        private void ResetScaraCommunicationFields()
        {
            try
            {
                LogHelper.Info("开始复位SCARA通信字段...");

                _scaraCommunicationManager.ML_dataget_ok = false;
                _scaraCommunicationManager.MR_dataget_ok = false;
                _scaraCommunicationManager.L_dataget_ok = false;
                _scaraCommunicationManager.R_dataget_ok = false;

                LogHelper.Info("SCARA通信字段复位完成");

                // 重置扫码器状态，准备下一轮扫码
                ResetScannerStates();
            }
            catch (Exception ex)
            {
                LogHelper.Error("复位SCARA通信字段时发生异常", ex);
            }
        }
        #endregion

        #region 事件触发方法
        /// <summary>
        /// 触发单个扫码器完成事件
        /// </summary>
        private void OnScannerCompleted(int scannerId, string barcode)
        {
            ScannerCompleted?.Invoke(this, new ScannerCompletedEventArgs
            {
                ScannerId = scannerId,
                Barcode = barcode,
                Timestamp = DateTime.Now
            });
        }

        /// <summary>
        /// 触发所有扫码器完成事件
        /// </summary>
        private void OnAllScannersCompleted()
        {
            var allData = GetAllScannerData();
            AllScannersCompleted?.Invoke(this, new AllScannersCompletedEventArgs
            {
                ScannerData = allData,
                Timestamp = DateTime.Now
            });
        }

        /// <summary>
        /// 触发自动模式状态变化事件
        /// </summary>
        private void OnAutoModeStatusChanged(bool enabled, string message)
        {
            AutoModeStatusChanged?.Invoke(this, new AutoModeStatusChangedEventArgs
            {
                IsEnabled = enabled,
                Message = message,
                Timestamp = DateTime.Now
            });
        }
        #endregion
    }
}
