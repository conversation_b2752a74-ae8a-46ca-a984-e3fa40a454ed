using System;
using System.Threading;
using System.Threading.Tasks;
using MyHMI.Events;
using MyHMI.Helpers;
using MyHMI.Models;

namespace MyHMI.Managers
{
    /// <summary>
    /// SCARA自动模式控制器
    /// 负责左/右翻转电机的自动模式工作流程控制
    /// 实现文档中详细的翻转电机自动模式逻辑
    /// </summary>
    public class ScaraAutoModeController : IDisposable
    {
        #region 单例模式
        private static readonly Lazy<ScaraAutoModeController> _instance = 
            new Lazy<ScaraAutoModeController>(() => new ScaraAutoModeController());
        
        /// <summary>
        /// 获取控制器实例
        /// </summary>
        public static ScaraAutoModeController Instance => _instance.Value;

        /// <summary>
        /// 私有构造函数
        /// </summary>
        private ScaraAutoModeController()
        {
            InitializeController();
        }
        #endregion

        #region 私有字段
        private readonly object _lockObject = new object();
        private bool _isInitialized = false;
        private bool _isRunning = false;
        private bool _disposed = false;
        
        // 取消令牌
        private CancellationTokenSource _cancellationTokenSource;
        
        // 依赖项
        private ScaraCommunicationManager _communicationManager;
        private DMC1000BIOManager _ioManager;
        private DMC1000BMotorManager _motorManager;
        
        // 工作流程状态
        private ScaraWorkflowState _leftMotorState = ScaraWorkflowState.Idle;
        private ScaraWorkflowState _rightMotorState = ScaraWorkflowState.Idle;
        
        // 工作流程任务
        private Task _leftMotorWorkflowTask;
        private Task _rightMotorWorkflowTask;
        #endregion

        #region 事件定义
        /// <summary>
        /// 状态变化事件
        /// </summary>
        public event EventHandler<ScaraStateChangedEventArgs> StateChanged;

        /// <summary>
        /// 工作流程完成事件
        /// </summary>
        public event EventHandler<ScaraWorkflowCompletedEventArgs> WorkflowCompleted;

        /// <summary>
        /// 错误事件
        /// </summary>
        public event EventHandler<ScaraErrorEventArgs> ErrorOccurred;
        #endregion

        #region 公共属性
        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// 是否正在运行
        /// </summary>
        public bool IsRunning => _isRunning;

        /// <summary>
        /// 左电机工作流程状态
        /// </summary>
        public ScaraWorkflowState LeftMotorState => _leftMotorState;

        /// <summary>
        /// 右电机工作流程状态
        /// </summary>
        public ScaraWorkflowState RightMotorState => _rightMotorState;
        #endregion

        #region 初始化方法
        /// <summary>
        /// 初始化控制器
        /// </summary>
        private void InitializeController()
        {
            try
            {
                LogHelper.Info("开始初始化SCARA自动模式控制器...");

                // 获取依赖项实例
                _communicationManager = ScaraCommunicationManager.Instance;
                _ioManager = DMC1000BIOManager.Instance;
                _motorManager = DMC1000BMotorManager.Instance;

                // 初始化取消令牌
                _cancellationTokenSource = new CancellationTokenSource();

                lock (_lockObject)
                {
                    _isInitialized = true;
                }

                LogHelper.Info("SCARA自动模式控制器初始化完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("初始化SCARA自动模式控制器时发生异常", ex);
                OnErrorOccurred("初始化失败", ex);
            }
        }

        /// <summary>
        /// 异步初始化
        /// </summary>
        /// <returns>是否初始化成功</returns>
        public async Task<bool> InitializeAsync()
        {
            await Task.CompletedTask; // 消除CS1998警告
            try
            {
                if (!_isInitialized)
                {
                    LogHelper.Warning("控制器未初始化，无法执行异步初始化");
                    return false;
                }

                LogHelper.Info("开始SCARA自动模式控制器异步初始化...");

                // 验证依赖项
                if (_communicationManager == null || _ioManager == null || _motorManager == null)
                {
                    LogHelper.Error("依赖项未正确初始化");
                    return false;
                }

                // 订阅通信字段变化事件
                _communicationManager.FieldChanged += OnCommunicationFieldChanged;

                LogHelper.Info("SCARA自动模式控制器异步初始化完成");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("SCARA自动模式控制器异步初始化时发生异常", ex);
                OnErrorOccurred("异步初始化失败", ex);
                return false;
            }
        }
        #endregion

        #region 启动和停止方法
        /// <summary>
        /// 启动自动模式
        /// </summary>
        /// <returns>是否启动成功</returns>
        public async Task<bool> StartAsync()
        {
            try
            {
                if (!_isInitialized)
                {
                    LogHelper.Warning("控制器未初始化，无法启动");
                    return false;
                }

                if (_isRunning)
                {
                    LogHelper.Info("SCARA自动模式已在运行中");
                    return true;
                }

                LogHelper.Info("启动SCARA自动模式...");

                // 重置取消令牌
                _cancellationTokenSource?.Cancel();
                _cancellationTokenSource = new CancellationTokenSource();

                // 设置电机准备状态
                _communicationManager.L_moto_ready = true;
                _communicationManager.R_moto_ready = true;

                // 启动工作流程监听
                await StartWorkflowMonitoring();

                lock (_lockObject)
                {
                    _isRunning = true;
                }

                LogHelper.Info("SCARA自动模式启动成功");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("启动SCARA自动模式时发生异常", ex);
                OnErrorOccurred("启动失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 停止自动模式
        /// </summary>
        /// <returns>是否停止成功</returns>
        public async Task<bool> StopAsync()
        {
            try
            {
                if (!_isRunning)
                {
                    LogHelper.Info("SCARA自动模式已停止");
                    return true;
                }

                LogHelper.Info("停止SCARA自动模式...");

                // 取消所有任务
                _cancellationTokenSource?.Cancel();

                // 等待工作流程任务完成
                await StopWorkflowMonitoring();

                // 重置电机状态
                _communicationManager.L_moto_ready = false;
                _communicationManager.R_moto_ready = false;

                lock (_lockObject)
                {
                    _isRunning = false;
                    _leftMotorState = ScaraWorkflowState.Idle;
                    _rightMotorState = ScaraWorkflowState.Idle;
                }

                LogHelper.Info("SCARA自动模式停止成功");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("停止SCARA自动模式时发生异常", ex);
                OnErrorOccurred("停止失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 重置自动模式
        /// </summary>
        /// <returns>是否重置成功</returns>
        public async Task<bool> ResetAsync()
        {
            try
            {
                LogHelper.Info("重置SCARA自动模式...");

                // 先停止
                await StopAsync();

                // 重置所有通信字段
                ResetAllCommunicationFields();

                // 重置状态
                lock (_lockObject)
                {
                    _leftMotorState = ScaraWorkflowState.Idle;
                    _rightMotorState = ScaraWorkflowState.Idle;
                }

                LogHelper.Info("SCARA自动模式重置完成");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("重置SCARA自动模式时发生异常", ex);
                OnErrorOccurred("重置失败", ex);
                return false;
            }
        }
        #endregion

        #region 工作流程监听
        /// <summary>
        /// 启动工作流程监听
        /// </summary>
        private async Task StartWorkflowMonitoring()
        {
            await Task.CompletedTask; // 消除CS1998警告
            LogHelper.Info("启动SCARA工作流程监听...");

            // 启动左电机工作流程监听
            _leftMotorWorkflowTask = Task.Run(() => LeftMotorWorkflowAsync(_cancellationTokenSource.Token));

            // 启动右电机工作流程监听
            _rightMotorWorkflowTask = Task.Run(() => RightMotorWorkflowAsync(_cancellationTokenSource.Token));

            LogHelper.Info("SCARA工作流程监听已启动");
        }

        /// <summary>
        /// 停止工作流程监听
        /// </summary>
        private async Task StopWorkflowMonitoring()
        {
            await Task.CompletedTask; // 消除CS1998警告
            LogHelper.Info("停止SCARA工作流程监听...");

            try
            {
                // 等待任务完成（最多等待5秒）
                var timeout = TimeSpan.FromSeconds(5);

                if (_leftMotorWorkflowTask != null)
                {
                    if (!_leftMotorWorkflowTask.Wait(timeout))
                    {
                        LogHelper.Warning("等待左电机工作流程任务完成超时");
                    }
                }

                if (_rightMotorWorkflowTask != null)
                {
                    if (!_rightMotorWorkflowTask.Wait(timeout))
                    {
                        LogHelper.Warning("等待右电机工作流程任务完成超时");
                    }
                }
            }
            catch (AggregateException ex)
            {
                LogHelper.Warning($"等待工作流程任务完成时发生异常: {ex.Message}");
            }
            catch (Exception ex)
            {
                LogHelper.Error("停止工作流程监听时发生异常", ex);
            }

            LogHelper.Info("SCARA工作流程监听已停止");
        }
        #endregion

        #region 工作流程实现
        /// <summary>
        /// 左电机工作流程
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        private async Task LeftMotorWorkflowAsync(CancellationToken cancellationToken)
        {
            try
            {
                LogHelper.Info("左电机工作流程开始监听...");

                while (!cancellationToken.IsCancellationRequested)
                {
                    try
                    {
                        // 等待L_position_Arrived置1
                        if (_communicationManager.L_position_Arrived && _leftMotorState == ScaraWorkflowState.Idle)
                        {
                            await ExecuteLeftMotorWorkflow(cancellationToken);
                        }

                        // 短暂延迟避免CPU占用过高
                        await Task.Delay(50, cancellationToken);
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error("左电机工作流程执行异常", ex);
                        OnErrorOccurred("左电机工作流程异常", ex);
                        await Task.Delay(1000, cancellationToken); // 错误后等待1秒再继续
                    }
                }

                LogHelper.Info("左电机工作流程监听结束");
            }
            catch (OperationCanceledException)
            {
                LogHelper.Info("左电机工作流程被取消");
            }
            catch (Exception ex)
            {
                LogHelper.Error("左电机工作流程发生异常", ex);
                OnErrorOccurred("左电机工作流程失败", ex);
            }
        }

        /// <summary>
        /// 右电机工作流程
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        private async Task RightMotorWorkflowAsync(CancellationToken cancellationToken)
        {
            try
            {
                LogHelper.Info("右电机工作流程开始监听...");

                while (!cancellationToken.IsCancellationRequested)
                {
                    try
                    {
                        // 等待R_position_Arrived置1
                        if (_communicationManager.R_position_Arrived && _rightMotorState == ScaraWorkflowState.Idle)
                        {
                            await ExecuteRightMotorWorkflow(cancellationToken);
                        }

                        // 短暂延迟避免CPU占用过高
                        await Task.Delay(50, cancellationToken);
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error("右电机工作流程执行异常", ex);
                        OnErrorOccurred("右电机工作流程异常", ex);
                        await Task.Delay(1000, cancellationToken); // 错误后等待1秒再继续
                    }
                }

                LogHelper.Info("右电机工作流程监听结束");
            }
            catch (OperationCanceledException)
            {
                LogHelper.Info("右电机工作流程被取消");
            }
            catch (Exception ex)
            {
                LogHelper.Error("右电机工作流程发生异常", ex);
                OnErrorOccurred("右电机工作流程失败", ex);
            }
        }

        /// <summary>
        /// 执行左电机完整工作流程
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        private async Task ExecuteLeftMotorWorkflow(CancellationToken cancellationToken)
        {
            var oldState = _leftMotorState;
            _leftMotorState = ScaraWorkflowState.Working;
            OnStateChanged("Left", oldState, _leftMotorState);

            try
            {
                LogHelper.Info("开始执行左电机工作流程...");

                // 步骤1: 夹爪控制
                LogHelper.Info("左电机步骤1: 夹爪控制");
                await _ioManager.SetOutputAsync("O0001", true); // 左夹爪气缸输出1
                await Task.Delay(100, cancellationToken); // 等待100ms

                // 检测I0005状态
                bool gripperStatus = await _ioManager.ReadInputAsync("I0005");
                if (!gripperStatus)
                {
                    throw new InvalidOperationException("左夹爪错误");
                }

                _communicationManager.L_gripper_ok = true;
                LogHelper.Info("左电机夹爪控制完成");

                // 步骤2: 等待角度矫正完成
                LogHelper.Info("左电机步骤2: 等待角度矫正完成");
                await WaitForFieldAsync(() => _communicationManager.L_Angle_ok,
                    (value) => _communicationManager.L_Angle_ok = value, "L_Angle_ok", cancellationToken);

                // 步骤3: 等待安全距离确认
                LogHelper.Info("左电机步骤3: 等待安全距离确认");
                await WaitForFieldAsync(() => _communicationManager.L_safe_ok,
                    (value) => _communicationManager.L_safe_ok = value, "L_safe_ok", cancellationToken);

                // 步骤4: 移动到位置2并顶料控制
                LogHelper.Info("左电机步骤4: 移动到位置2并顶料控制");
                await _motorManager.MoveToFlipMotorPositionAsync(0, 2); // 移动到位置2（轴0为左翻转电机）
                await _ioManager.SetOutputAsync("O0002", true); // 左顶料气缸输出1
                await Task.Delay(100, cancellationToken); // 等待100ms

                // 检测I0007状态
                bool pushStatus = await _ioManager.ReadInputAsync("I0007");
                if (!pushStatus)
                {
                    throw new InvalidOperationException("左顶料错误");
                }

                // 步骤5: 移动到位置3并退料控制
                LogHelper.Info("左电机步骤5: 移动到位置3并退料控制");
                await _motorManager.MoveToFlipMotorPositionAsync(0, 3); // 移动到位置3（轴0为左翻转电机）
                await _ioManager.SetOutputAsync("O0002", false); // 左顶料气缸输出0
                await Task.Delay(100, cancellationToken); // 等待100ms

                // 检测I0008状态
                bool retractStatus = await _ioManager.ReadInputAsync("I0008");
                if (!retractStatus)
                {
                    throw new InvalidOperationException("左退料错误");
                }

                // 步骤6: 移动到位置4并设置数据获取标志
                LogHelper.Info("左电机步骤6: 移动到位置4");
                await _motorManager.MoveToFlipMotorPositionAsync(0, 4); // 移动到位置4（轴0为左翻转电机）
                _communicationManager.L_dataget_ok = true;

                // 工作流程完成，重置相关字段
                _communicationManager.L_position_Arrived = false;
                _communicationManager.L_gripper_ok = false;
                _communicationManager.L_Angle_ok = false;
                _communicationManager.L_safe_ok = false;
                _communicationManager.L_moto_finish = true;

                _leftMotorState = ScaraWorkflowState.Completed;
                OnStateChanged("Left", ScaraWorkflowState.Working, _leftMotorState);
                OnWorkflowCompleted("Left", true);

                LogHelper.Info("左电机工作流程执行完成");

                // 短暂延迟后重置为空闲状态，准备下一轮
                await Task.Delay(1000, cancellationToken);
                _leftMotorState = ScaraWorkflowState.Idle;
                OnStateChanged("Left", ScaraWorkflowState.Completed, _leftMotorState);
            }
            catch (Exception ex)
            {
                LogHelper.Error("左电机工作流程执行失败", ex);
                _leftMotorState = ScaraWorkflowState.Error;
                OnStateChanged("Left", ScaraWorkflowState.Working, _leftMotorState);
                OnWorkflowCompleted("Left", false);
                OnErrorOccurred($"左电机工作流程失败: {ex.Message}", ex);

                // 错误后等待一段时间再重置为空闲状态
                await Task.Delay(5000, cancellationToken);
                _leftMotorState = ScaraWorkflowState.Idle;
                OnStateChanged("Left", ScaraWorkflowState.Error, _leftMotorState);
            }
        }
        #endregion

        #region 通信字段事件处理
        /// <summary>
        /// 处理通信字段变化事件
        /// </summary>
        /// <param name="sender">发送者</param>
        /// <param name="e">事件参数</param>
        private void OnCommunicationFieldChanged(object sender, CommunicationEventArgs e)
        {
            try
            {
                if (!_isRunning || e.NewValue != true)
                {
                    return; // 只处理字段置1的情况
                }

                LogHelper.Debug($"SCARA通信字段变化: {e.FieldName} = {e.NewValue}");

                // 根据字段名称触发相应的处理
                // 这里只是记录，具体的工作流程在独立的任务中处理
            }
            catch (Exception ex)
            {
                LogHelper.Error($"处理通信字段变化事件时发生异常: {e.FieldName}", ex);
                OnErrorOccurred($"处理字段变化失败: {e.FieldName}", ex);
            }
        }
        #endregion

        /// <summary>
        /// 执行右电机完整工作流程
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        private async Task ExecuteRightMotorWorkflow(CancellationToken cancellationToken)
        {
            var oldState = _rightMotorState;
            _rightMotorState = ScaraWorkflowState.Working;
            OnStateChanged("Right", oldState, _rightMotorState);

            try
            {
                LogHelper.Info("开始执行右电机工作流程...");

                // 步骤1: 夹爪控制
                LogHelper.Info("右电机步骤1: 夹爪控制");
                await _ioManager.SetOutputAsync("O0003", true); // 右夹爪气缸输出1
                await Task.Delay(100, cancellationToken); // 等待100ms

                // 检测I0009状态
                bool gripperStatus = await _ioManager.ReadInputAsync("I0009");
                if (!gripperStatus)
                {
                    throw new InvalidOperationException("右夹爪错误");
                }

                _communicationManager.R_gripper_ok = true;
                LogHelper.Info("右电机夹爪控制完成");

                // 步骤2: 等待角度矫正完成
                LogHelper.Info("右电机步骤2: 等待角度矫正完成");
                await WaitForFieldAsync(() => _communicationManager.R_Angle_ok,
                    (value) => _communicationManager.R_Angle_ok = value, "R_Angle_ok", cancellationToken);

                // 步骤3: 等待安全距离确认
                LogHelper.Info("右电机步骤3: 等待安全距离确认");
                await WaitForFieldAsync(() => _communicationManager.R_safe_ok,
                    (value) => _communicationManager.R_safe_ok = value, "R_safe_ok", cancellationToken);

                // 步骤4: 移动到位置2并顶料控制
                LogHelper.Info("右电机步骤4: 移动到位置2并顶料控制");
                await _motorManager.MoveToFlipMotorPositionAsync(1, 2); // 移动到位置2（轴1为右翻转电机）
                await _ioManager.SetOutputAsync("O0004", true); // 右顶料气缸输出1
                await Task.Delay(100, cancellationToken); // 等待100ms

                // 检测I0011状态
                bool pushStatus = await _ioManager.ReadInputAsync("I0011");
                if (!pushStatus)
                {
                    throw new InvalidOperationException("右顶料错误");
                }

                // 步骤5: 移动到位置3并退料控制
                LogHelper.Info("右电机步骤5: 移动到位置3并退料控制");
                await _motorManager.MoveToFlipMotorPositionAsync(1, 3); // 移动到位置3（轴1为右翻转电机）
                await _ioManager.SetOutputAsync("O0004", false); // 右顶料气缸输出0
                await Task.Delay(100, cancellationToken); // 等待100ms

                // 检测I0012状态
                bool retractStatus = await _ioManager.ReadInputAsync("I0012");
                if (!retractStatus)
                {
                    throw new InvalidOperationException("右退料错误");
                }

                // 步骤6: 移动到位置4并设置数据获取标志
                LogHelper.Info("右电机步骤6: 移动到位置4");
                await _motorManager.MoveToFlipMotorPositionAsync(1, 4); // 移动到位置4（轴1为右翻转电机）
                _communicationManager.R_dataget_ok = true;

                // 工作流程完成，重置相关字段
                _communicationManager.R_position_Arrived = false;
                _communicationManager.R_gripper_ok = false;
                _communicationManager.R_Angle_ok = false;
                _communicationManager.R_safe_ok = false;
                _communicationManager.R_moto_finish = true;

                _rightMotorState = ScaraWorkflowState.Completed;
                OnStateChanged("Right", ScaraWorkflowState.Working, _rightMotorState);
                OnWorkflowCompleted("Right", true);

                LogHelper.Info("右电机工作流程执行完成");

                // 短暂延迟后重置为空闲状态，准备下一轮
                await Task.Delay(1000, cancellationToken);
                _rightMotorState = ScaraWorkflowState.Idle;
                OnStateChanged("Right", ScaraWorkflowState.Completed, _rightMotorState);
            }
            catch (Exception ex)
            {
                LogHelper.Error("右电机工作流程执行失败", ex);
                _rightMotorState = ScaraWorkflowState.Error;
                OnStateChanged("Right", ScaraWorkflowState.Working, _rightMotorState);
                OnWorkflowCompleted("Right", false);
                OnErrorOccurred($"右电机工作流程失败: {ex.Message}", ex);

                // 错误后等待一段时间再重置为空闲状态
                await Task.Delay(5000, cancellationToken);
                _rightMotorState = ScaraWorkflowState.Idle;
                OnStateChanged("Right", ScaraWorkflowState.Error, _rightMotorState);
            }
        }

        #region 辅助方法
        /// <summary>
        /// 等待指定字段置1，并在检测到后立即复位为0（符合文档要求）
        /// </summary>
        /// <param name="fieldGetter">字段获取函数</param>
        /// <param name="fieldSetter">字段设置函数</param>
        /// <param name="fieldName">字段名称</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <param name="timeoutMs">超时时间（毫秒）</param>
        private async Task WaitForFieldAsync(Func<bool> fieldGetter, Action<bool> fieldSetter, string fieldName, CancellationToken cancellationToken, int timeoutMs = 30000)
        {
            var startTime = DateTime.Now;
            LogHelper.Debug($"开始等待字段 {fieldName} 置1...");

            while (!cancellationToken.IsCancellationRequested)
            {
                if (fieldGetter())
                {
                    LogHelper.Debug($"字段 {fieldName} 已置1，立即复位为0（符合文档要求）");
                    fieldSetter(false); // 立即复位为0，符合文档第48行要求
                    return;
                }

                // 检查超时
                if ((DateTime.Now - startTime).TotalMilliseconds > timeoutMs)
                {
                    throw new TimeoutException($"等待字段 {fieldName} 置1超时（{timeoutMs}ms）");
                }

                await Task.Delay(100, cancellationToken); // 每100ms检查一次
            }

            throw new OperationCanceledException($"等待字段 {fieldName} 置1被取消");
        }

        /// <summary>
        /// 重置所有通信字段
        /// </summary>
        private void ResetAllCommunicationFields()
        {
            try
            {
                LogHelper.Info("重置SCARA通信字段...");

                _communicationManager.L_moto_ready = false;
                _communicationManager.R_moto_ready = false;
                _communicationManager.L_position_Arrived = false;
                _communicationManager.R_position_Arrived = false;
                _communicationManager.L_gripper_ok = false;
                _communicationManager.R_gripper_ok = false;
                _communicationManager.L_Angle_ok = false;
                _communicationManager.R_Angle_ok = false;
                _communicationManager.L_safe_ok = false;
                _communicationManager.R_safe_ok = false;
                _communicationManager.L_dataget_ok = false;
                _communicationManager.R_dataget_ok = false;
                _communicationManager.L_moto_finish = false;
                _communicationManager.R_moto_finish = false;

                LogHelper.Info("SCARA通信字段重置完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("重置SCARA通信字段时发生异常", ex);
                OnErrorOccurred("重置通信字段失败", ex);
            }
        }
        #endregion

        #region 事件触发方法
        /// <summary>
        /// 触发状态变化事件
        /// </summary>
        /// <param name="motorSide">电机侧（Left/Right）</param>
        /// <param name="oldState">旧状态</param>
        /// <param name="newState">新状态</param>
        private void OnStateChanged(string motorSide, ScaraWorkflowState oldState, ScaraWorkflowState newState)
        {
            try
            {
                StateChanged?.Invoke(this, new ScaraStateChangedEventArgs(motorSide, oldState, newState));
            }
            catch (Exception ex)
            {
                LogHelper.Error("触发状态变化事件时发生异常", ex);
            }
        }

        /// <summary>
        /// 触发工作流程完成事件
        /// </summary>
        /// <param name="motorSide">电机侧</param>
        /// <param name="success">是否成功</param>
        private void OnWorkflowCompleted(string motorSide, bool success)
        {
            try
            {
                WorkflowCompleted?.Invoke(this, new ScaraWorkflowCompletedEventArgs(motorSide, success));
            }
            catch (Exception ex)
            {
                LogHelper.Error("触发工作流程完成事件时发生异常", ex);
            }
        }

        /// <summary>
        /// 触发错误事件
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="exception">异常</param>
        private void OnErrorOccurred(string message, Exception exception = null)
        {
            try
            {
                ErrorOccurred?.Invoke(this, new ScaraErrorEventArgs(message, exception));
            }
            catch (Exception ex)
            {
                LogHelper.Error("触发错误事件时发生异常", ex);
            }
        }
        #endregion

        #region IDisposable实现
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                try
                {
                    // 停止自动模式
                    StopAsync().Wait(TimeSpan.FromSeconds(5));

                    // 取消订阅事件
                    if (_communicationManager != null)
                    {
                        _communicationManager.FieldChanged -= OnCommunicationFieldChanged;
                    }

                    // 释放取消令牌
                    _cancellationTokenSource?.Cancel();
                    _cancellationTokenSource?.Dispose();
                }
                catch (Exception ex)
                {
                    LogHelper.Error("释放SCARA自动模式控制器资源时发生异常", ex);
                }

                _disposed = true;
            }
        }
        #endregion
    }
}
