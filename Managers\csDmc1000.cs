using System;
using System.Runtime.InteropServices;

namespace csDmc1000
{
    /// <summary>
    /// DMC1000B运动控制卡P/Invoke封装类
    /// 封装Dmc1000.dll中的所有函数
    /// </summary>
    public partial class DMC1000
    {
        /// <summary>
        /// 初始化控制卡，为控制卡分配系统资源，并初始化控制卡
        /// </summary>
        /// <returns>卡数：0~12，其中0表示没有卡</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_board_init", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_board_init();

        /// <summary>
        /// 关闭控制卡，释放系统资源
        /// </summary>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_board_close", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_board_close();

        /// <summary>
        /// 设置控制卡脉冲输出模式，用户可以根据驱动器具体接收脉冲的模式来选择控制卡不同脉冲输出模式
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="pls_outmode">脉冲输出模式：0-pulse/dir模式脉冲上升沿有效；1-pulse/dir模式脉冲下降沿有效；2-CW/CCW模式脉冲上升沿有效；3-CW/CCW模式脉冲下降沿有效</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_set_pls_outmode", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_set_pls_outmode(short axis, short pls_outmode);

        /// <summary>
        /// 读取指定轴当前脉冲输出速度
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <returns>指定轴当前的运动速度，单位pps</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_get_speed", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern int d1000_get_speed(short axis);

        /// <summary>
        /// 改变指定轴当前脉冲输出速度
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="NewVel">新设置的速度，单位：pps，取值范围：1~409550</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_change_speed", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_change_speed(short axis, int NewVel);

        /// <summary>
        /// 减速停止指定轴脉冲输出
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_decel_stop", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_decel_stop(short axis);

        /// <summary>
        /// 急停指定轴脉冲输出
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_immediate_stop", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_immediate_stop(short axis);

        /// <summary>
        /// 以梯形速度曲线控制指定轴至运行速度，并以相对坐标运行一段指定距离
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="Dist">相对运动距离，单位：pulse，其值的正负表示运动方向</param>
        /// <param name="StrVel">初始速度，单位：pps</param>
        /// <param name="MaxVel">运行速度，单位：pps</param>
        /// <param name="Tacc">加速时间，单位：s</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_start_t_move", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_start_t_move(short axis, int Dist, int StrVel, int MaxVel, double Tacc);

        /// <summary>
        /// 以梯形速度曲线控制指定轴至运行速度，并以绝对坐标运行一段指定距离
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="Pos">绝对运动位置，单位：pulse</param>
        /// <param name="StrVel">初始速度，单位：pps</param>
        /// <param name="MaxVel">运行速度，单位：pps</param>
        /// <param name="Tacc">加速时间，单位：s</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_start_ta_move", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_start_ta_move(short axis, int Pos, int StrVel, int MaxVel, double Tacc);

        /// <summary>
        /// 以S形速度曲线控制指定轴至运行速度，并以相对坐标运行一段指定距离
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="Dist">相对运动距离，单位：pulse，其值的正负表示运动方向</param>
        /// <param name="StrVel">初始速度，单位：pps</param>
        /// <param name="MaxVel">运行速度，单位：pps</param>
        /// <param name="Tacc">加速时间，单位：s</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_start_s_move", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_start_s_move(short axis, int Dist, int StrVel, int MaxVel, double Tacc);

        /// <summary>
        /// 以S形速度曲线控制指定轴至运行速度，并以绝对坐标运行一段指定距离
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="Pos">绝对运动位置，单位：pulse</param>
        /// <param name="StrVel">初始速度，单位：pps</param>
        /// <param name="MaxVel">运行速度，单位：pps</param>
        /// <param name="Tacc">加速时间，单位：s</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_start_sa_move", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_start_sa_move(short axis, int Pos, int StrVel, int MaxVel, double Tacc);

        /// <summary>
        /// 以梯形速度曲线控制指定轴至运行速度，并以运行速度连续运行
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="StrVel">初始速度，单位：pps</param>
        /// <param name="MaxVel">运行速度，单位：pps，其值的正负表示运动方向</param>
        /// <param name="Tacc">加速时间，单位：s</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_start_tv_move", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_start_tv_move(short axis, int StrVel, int MaxVel, double Tacc);

        /// <summary>
        /// 以S形速度曲线控制指定轴至运行速度，并以运行速度连续运行
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="StrVel">初始速度，单位：pps</param>
        /// <param name="MaxVel">运行速度，单位：pps，其值的正负表示运动方向</param>
        /// <param name="Tacc">加速时间，单位：s</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_start_sv_move", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_start_sv_move(short axis, int StrVel, int MaxVel, double Tacc);

        /// <summary>
        /// 设置S形运动曲线参数
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="s_para">S形曲线参数</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_set_s_profile", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_set_s_profile(short axis, double s_para);

        /// <summary>
        /// 读取S形运动曲线参数
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="s_para">返回S形曲线参数</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_get_s_profile", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_get_s_profile(short axis, ref double s_para);

        /// <summary>
        /// 启动指定轴进行回原点运动
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="StrVel">回原点运动初始速度，单位：pps</param>
        /// <param name="MaxVel">回原点运动速度，负值表示往负方向找原点，正值表示往正方向找原点，单位：pps</param>
        /// <param name="Tacc">加速时间，单位：s</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_home_move", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_home_move(short axis, int StrVel, int MaxVel, double Tacc);

        /// <summary>
        /// 检测指定轴的运动状态
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <returns>0：正在运行；1：脉冲输出完毕停止；2：指令停止；3：遇限位停止；4：遇原点停止</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_check_done", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_check_done(short axis);

        /// <summary>
        /// 读取指令位置计数器计数值
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <returns>指定轴当前指令位置计数器值，单位：pulse</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_get_command_pos", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern int d1000_get_command_pos(short axis);

        /// <summary>
        /// 设置指令位置计数器计数值
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="Pos">设置指令位置计数器值，单位：pulse</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_set_command_pos", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_set_command_pos(short axis, double Pos);

        /// <summary>
        /// 输出通用输出信号
        /// </summary>
        /// <param name="BitNo">表示要输出的通用输出口的位号</param>
        /// <param name="BitData">输出信号：0-表示低电平；1-表示高电平</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_out_bit", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern int d1000_out_bit(int BitNo, int BitData);

        /// <summary>
        /// 读取通用输入信号状态
        /// </summary>
        /// <param name="BitNo">表示要读取的通用输入口的位号</param>
        /// <returns>输入口状态：0-表示低电平；1-表示高电平</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_in_bit", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern int d1000_in_bit(int BitNo);

        /// <summary>
        /// 读取通用输出信号状态
        /// </summary>
        /// <param name="BitNo">通用输出口位号</param>
        /// <returns>输出口状态：0-表示低电平；1-表示高电平</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_get_outbit", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern int d1000_get_outbit(int BitNo);

        /// <summary>
        /// 设置减速信号是否使能
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="SdMode">减速使能模式：0-SD信号无效；1-SD信号有效</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_set_sd", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_set_sd(short axis, short SdMode);

        /// <summary>
        /// 读取指定轴的专用接口信号状态
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <returns>指定轴专用信号接口状态</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_get_axis_status", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_get_axis_status(short axis);
    }
}
