using System;
using System.Threading.Tasks;
using MyHMI.Settings;
using MyHMI.Helpers;
using MyHMI.Models;

namespace MyHMI.Tests
{
    /// <summary>
    /// Motor模块参数管理测试
    /// </summary>
    public static class MotorParameterTest
    {
        /// <summary>
        /// 运行Motor模块参数管理测试
        /// </summary>
        public static async Task RunMotorParameterTestAsync()
        {
            try
            {
                LogHelper.Info("开始Motor模块参数管理测试...");

                // 测试1：验证Motor参数读取
                await TestMotorParameterReading();

                // 测试2：验证Motor参数写入
                await TestMotorParameterWriting();

                // 测试3：验证翻转电机参数
                await TestFlipMotorParameters();

                // 测试4：验证参数持久化
                await TestParameterPersistence();

                LogHelper.Info("Motor模块参数管理测试完成，所有测试通过！");
            }
            catch (Exception ex)
            {
                LogHelper.Error("Motor模块参数管理测试失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 测试Motor参数读取
        /// </summary>
        private static async Task TestMotorParameterReading()
        {
            LogHelper.Info("测试1：Motor参数读取...");

            await Task.Run(() =>
            {
                var motorSettings = Settings.Settings.Current.Motor;
                
                // 验证基本参数
                if (motorSettings == null)
                    throw new Exception("Motor设置为null");

                // 验证翻转电机参数
                if (motorSettings.LeftFlipPulseEquivalent <= 0)
                    throw new Exception("左翻转电机脉冲当量无效");

                if (motorSettings.RightFlipPulseEquivalent <= 0)
                    throw new Exception("右翻转电机脉冲当量无效");

                // 验证速度参数
                if (motorSettings.LeftFlipMaxSpeed <= 0)
                    throw new Exception("左翻转电机最大速度无效");

                if (motorSettings.RightFlipMaxSpeed <= 0)
                    throw new Exception("右翻转电机最大速度无效");

                LogHelper.Info($"✓ 左翻转电机脉冲当量: {motorSettings.LeftFlipPulseEquivalent}");
                LogHelper.Info($"✓ 右翻转电机脉冲当量: {motorSettings.RightFlipPulseEquivalent}");
                LogHelper.Info($"✓ 左翻转电机最大速度: {motorSettings.LeftFlipMaxSpeed}");
                LogHelper.Info($"✓ 右翻转电机最大速度: {motorSettings.RightFlipMaxSpeed}");
            });

            LogHelper.Info("✓ Motor参数读取测试通过");
        }

        /// <summary>
        /// 测试Motor参数写入
        /// </summary>
        private static async Task TestMotorParameterWriting()
        {
            LogHelper.Info("测试2：Motor参数写入...");

            await Task.Run(() =>
            {
                var motorSettings = Settings.Settings.Current.Motor;
                
                // 保存原始值
                double originalLeftPulse = motorSettings.LeftFlipPulseEquivalent;
                double originalRightPulse = motorSettings.RightFlipPulseEquivalent;
                double originalLeftSpeed = motorSettings.LeftFlipMaxSpeed;
                double originalRightSpeed = motorSettings.RightFlipMaxSpeed;

                try
                {
                    // 修改参数
                    double testLeftPulse = 0.123456;
                    double testRightPulse = 0.654321;
                    double testLeftSpeed = 45.0;
                    double testRightSpeed = 55.0;

                    motorSettings.LeftFlipPulseEquivalent = testLeftPulse;
                    motorSettings.RightFlipPulseEquivalent = testRightPulse;
                    motorSettings.LeftFlipMaxSpeed = testLeftSpeed;
                    motorSettings.RightFlipMaxSpeed = testRightSpeed;

                    // 保存设置
                    Settings.Settings.Save();

                    // 验证修改
                    if (Math.Abs(motorSettings.LeftFlipPulseEquivalent - testLeftPulse) > 0.000001)
                        throw new Exception("左翻转电机脉冲当量写入失败");

                    if (Math.Abs(motorSettings.RightFlipPulseEquivalent - testRightPulse) > 0.000001)
                        throw new Exception("右翻转电机脉冲当量写入失败");

                    if (Math.Abs(motorSettings.LeftFlipMaxSpeed - testLeftSpeed) > 0.001)
                        throw new Exception("左翻转电机最大速度写入失败");

                    if (Math.Abs(motorSettings.RightFlipMaxSpeed - testRightSpeed) > 0.001)
                        throw new Exception("右翻转电机最大速度写入失败");

                    LogHelper.Info("✓ Motor参数写入验证通过");
                }
                finally
                {
                    // 恢复原始值
                    motorSettings.LeftFlipPulseEquivalent = originalLeftPulse;
                    motorSettings.RightFlipPulseEquivalent = originalRightPulse;
                    motorSettings.LeftFlipMaxSpeed = originalLeftSpeed;
                    motorSettings.RightFlipMaxSpeed = originalRightSpeed;
                    Settings.Settings.Save();
                }
            });

            LogHelper.Info("✓ Motor参数写入测试通过");
        }

        /// <summary>
        /// 测试翻转电机参数
        /// </summary>
        private static async Task TestFlipMotorParameters()
        {
            LogHelper.Info("测试3：翻转电机参数...");

            await Task.Run(() =>
            {
                var motorSettings = Settings.Settings.Current.Motor;

                // 测试位置参数
                LogHelper.Info($"✓ 左翻转电机原点位置: {motorSettings.LeftFlipHomePosition}");
                LogHelper.Info($"✓ 左翻转电机工作位置: {motorSettings.LeftFlipWorkPosition}");
                LogHelper.Info($"✓ 左翻转电机安全位置: {motorSettings.LeftFlipSafePosition}");
                LogHelper.Info($"✓ 左翻转电机测试位置: {motorSettings.LeftFlipTestPosition}");

                LogHelper.Info($"✓ 右翻转电机原点位置: {motorSettings.RightFlipHomePosition}");
                LogHelper.Info($"✓ 右翻转电机工作位置: {motorSettings.RightFlipWorkPosition}");
                LogHelper.Info($"✓ 右翻转电机安全位置: {motorSettings.RightFlipSafePosition}");
                LogHelper.Info($"✓ 右翻转电机测试位置: {motorSettings.RightFlipTestPosition}");

                // 测试加速度参数
                if (motorSettings.LeftFlipAcceleration <= 0)
                    throw new Exception("左翻转电机加速度无效");

                if (motorSettings.RightFlipAcceleration <= 0)
                    throw new Exception("右翻转电机加速度无效");

                LogHelper.Info($"✓ 左翻转电机加速度: {motorSettings.LeftFlipAcceleration}");
                LogHelper.Info($"✓ 右翻转电机加速度: {motorSettings.RightFlipAcceleration}");

                // 测试起始速度参数
                if (motorSettings.LeftFlipStartSpeed <= 0)
                    throw new Exception("左翻转电机起始速度无效");

                if (motorSettings.RightFlipStartSpeed <= 0)
                    throw new Exception("右翻转电机起始速度无效");

                LogHelper.Info($"✓ 左翻转电机起始速度: {motorSettings.LeftFlipStartSpeed}");
                LogHelper.Info($"✓ 右翻转电机起始速度: {motorSettings.RightFlipStartSpeed}");
            });

            LogHelper.Info("✓ 翻转电机参数测试通过");
        }

        /// <summary>
        /// 测试参数持久化
        /// </summary>
        private static async Task TestParameterPersistence()
        {
            LogHelper.Info("测试4：参数持久化...");

            await Task.Run(() =>
            {
                var motorSettings = Settings.Settings.Current.Motor;

                // 保存原始值
                double originalLeftPosition = motorSettings.LeftFlipWorkPosition;
                double originalRightPosition = motorSettings.RightFlipWorkPosition;

                try
                {
                    // 修改位置参数
                    double testLeftPosition = 123.456;
                    double testRightPosition = 654.321;

                    motorSettings.LeftFlipWorkPosition = testLeftPosition;
                    motorSettings.RightFlipWorkPosition = testRightPosition;

                    // 保存到文件
                    Settings.Settings.Save();

                    // 重新加载
                    Settings.Settings.Load();
                    var reloadedSettings = Settings.Settings.Current.Motor;

                    // 验证持久化
                    if (Math.Abs(reloadedSettings.LeftFlipWorkPosition - testLeftPosition) > 0.001)
                        throw new Exception("左翻转电机工作位置持久化失败");

                    if (Math.Abs(reloadedSettings.RightFlipWorkPosition - testRightPosition) > 0.001)
                        throw new Exception("右翻转电机工作位置持久化失败");

                    LogHelper.Info("✓ 参数持久化验证通过");
                }
                finally
                {
                    // 恢复原始值
                    var currentSettings = Settings.Settings.Current.Motor;
                    currentSettings.LeftFlipWorkPosition = originalLeftPosition;
                    currentSettings.RightFlipWorkPosition = originalRightPosition;
                    Settings.Settings.Save();
                }
            });

            LogHelper.Info("✓ 参数持久化测试通过");
        }
    }
}
