using System;
using System.Collections.Generic;

namespace MyHMI.Models
{
    /// <summary>
    /// 生产记录类
    /// 记录每个产品的生产过程信息
    /// </summary>
    public class ProductionRecord
    {
        /// <summary>
        /// 记录ID（自动生成）
        /// </summary>
        public string RecordId { get; set; }

        /// <summary>
        /// 产品ID（扫描枪获取）
        /// </summary>
        public string ProductId { get; set; }

        /// <summary>
        /// 批次号
        /// </summary>
        public string BatchNumber { get; set; }

        /// <summary>
        /// 操作员
        /// </summary>
        public string Operator { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 处理时长（秒）
        /// </summary>
        public double ProcessingTimeSeconds => (EndTime - StartTime).TotalSeconds;

        /// <summary>
        /// 产品状态
        /// </summary>
        public ProductStatus Status { get; set; }

        /// <summary>
        /// 视觉检测结果
        /// </summary>
        public VisionResult VisionResult { get; set; }

        /// <summary>
        /// 电机运动记录
        /// </summary>
        public MotorMovementRecord MotorMovement { get; set; }

        /// <summary>
        /// 机器人操作记录
        /// </summary>
        public RobotOperationRecord RobotOperation { get; set; }

        /// <summary>
        /// 不良品原因（如果是不良品）
        /// </summary>
        public string DefectReason { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        public string Remarks { get; set; }

        /// <summary>
        /// 时间戳（用于统计和排序）
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 是否成功（基于状态判断）
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 处理时间（毫秒）
        /// </summary>
        public double ProcessingTimeMs { get; set; }

        /// <summary>
        /// 错误信息（综合各模块的错误信息）
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ProductionRecord()
        {
            RecordId = Guid.NewGuid().ToString();
            StartTime = DateTime.Now;
            Timestamp = StartTime;
            Status = ProductStatus.Processing;
            IsSuccess = false;
            ProcessingTimeMs = 0;
            ErrorMessage = string.Empty;
            VisionResult = new VisionResult();
            MotorMovement = new MotorMovementRecord();
            RobotOperation = new RobotOperationRecord();
            DefectReason = string.Empty;
            Remarks = string.Empty;
        }
    }

    /// <summary>
    /// 产品状态枚举
    /// </summary>
    public enum ProductStatus
    {
        /// <summary>
        /// 处理中
        /// </summary>
        Processing,

        /// <summary>
        /// 良品
        /// </summary>
        Good,

        /// <summary>
        /// 不良品
        /// </summary>
        Defective,

        /// <summary>
        /// 异常
        /// </summary>
        Error
    }

    /// <summary>
    /// 视觉检测结果类
    /// </summary>
    public class VisionResult
    {
        /// <summary>
        /// 检测是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// X坐标
        /// </summary>
        public double X { get; set; }

        /// <summary>
        /// Y坐标
        /// </summary>
        public double Y { get; set; }

        /// <summary>
        /// 角度
        /// </summary>
        public double Angle { get; set; }

        /// <summary>
        /// 置信度 (0-1)
        /// </summary>
        public double Confidence { get; set; }

        /// <summary>
        /// 检测时间
        /// </summary>
        public DateTime DetectionTime { get; set; }

        /// <summary>
        /// 处理时长（毫秒）
        /// </summary>
        public double ProcessingTimeMs { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public VisionResult()
        {
            DetectionTime = DateTime.Now;
            ErrorMessage = string.Empty;
        }
    }

    /// <summary>
    /// 电机运动记录类
    /// </summary>
    public class MotorMovementRecord
    {
        /// <summary>
        /// 起始位置
        /// </summary>
        public double StartPosition { get; set; }

        /// <summary>
        /// 目标位置
        /// </summary>
        public double TargetPosition { get; set; }

        /// <summary>
        /// 实际到达位置
        /// </summary>
        public double ActualPosition { get; set; }

        /// <summary>
        /// 运动时间（毫秒）
        /// </summary>
        public double MovementTimeMs { get; set; }

        /// <summary>
        /// 运动是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public MotorMovementRecord()
        {
            ErrorMessage = string.Empty;
        }
    }

    /// <summary>
    /// 机器人操作记录类
    /// </summary>
    public class RobotOperationRecord
    {
        /// <summary>
        /// 操作类型
        /// </summary>
        public string OperationType { get; set; }

        /// <summary>
        /// 目标坐标
        /// </summary>
        public double[] TargetCoordinates { get; set; }

        /// <summary>
        /// 操作是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 操作时间（毫秒）
        /// </summary>
        public double OperationTimeMs { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public RobotOperationRecord()
        {
            OperationType = string.Empty;
            TargetCoordinates = new double[6]; // 6轴机器人坐标
            ErrorMessage = string.Empty;
        }
    }

    /// <summary>
    /// 生产统计汇总类
    /// </summary>
    public class ProductionSummary
    {
        /// <summary>
        /// 统计日期
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// 总产量
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 良品数量
        /// </summary>
        public int GoodCount { get; set; }

        /// <summary>
        /// 不良品数量
        /// </summary>
        public int DefectiveCount { get; set; }

        /// <summary>
        /// 异常数量
        /// </summary>
        public int ErrorCount { get; set; }

        /// <summary>
        /// 良品率
        /// </summary>
        public double GoodRate => TotalCount > 0 ? (double)GoodCount / TotalCount * 100 : 0;

        /// <summary>
        /// 不良率
        /// </summary>
        public double DefectiveRate => TotalCount > 0 ? (double)DefectiveCount / TotalCount * 100 : 0;

        /// <summary>
        /// 平均处理时间（秒）
        /// </summary>
        public double AverageProcessingTime { get; set; }

        /// <summary>
        /// 最快处理时间（秒）
        /// </summary>
        public double MinProcessingTime { get; set; }

        /// <summary>
        /// 最慢处理时间（秒）
        /// </summary>
        public double MaxProcessingTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ProductionSummary()
        {
            Date = DateTime.Today;
        }
    }

    /// <summary>
    /// 生产统计类
    /// 用于实时统计和历史数据管理
    /// </summary>
    public class ProductionStatistics
    {
        /// <summary>
        /// 统计ID
        /// </summary>
        public string StatisticsId { get; set; }

        /// <summary>
        /// 统计开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 统计结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 班次信息
        /// </summary>
        public string Shift { get; set; }

        /// <summary>
        /// 操作员
        /// </summary>
        public string Operator { get; set; }

        /// <summary>
        /// 总产量
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 良品数量
        /// </summary>
        public int GoodCount { get; set; }

        /// <summary>
        /// 不良品数量
        /// </summary>
        public int DefectiveCount { get; set; }

        /// <summary>
        /// 异常数量
        /// </summary>
        public int ErrorCount { get; set; }

        /// <summary>
        /// 良品率
        /// </summary>
        public double GoodRate => TotalCount > 0 ? (double)GoodCount / TotalCount * 100 : 0;

        /// <summary>
        /// 不良率
        /// </summary>
        public double DefectiveRate => TotalCount > 0 ? (double)DefectiveCount / TotalCount * 100 : 0;

        /// <summary>
        /// 设备运行时间（分钟）
        /// </summary>
        public double RunningTimeMinutes { get; set; }

        /// <summary>
        /// 设备停机时间（分钟）
        /// </summary>
        public double DownTimeMinutes { get; set; }

        /// <summary>
        /// 设备利用率
        /// </summary>
        public double EquipmentUtilization
        {
            get
            {
                var totalTime = RunningTimeMinutes + DownTimeMinutes;
                return totalTime > 0 ? RunningTimeMinutes / totalTime * 100 : 0;
            }
        }

        /// <summary>
        /// 平均节拍时间（秒/件）
        /// </summary>
        public double AverageCycleTime { get; set; }

        /// <summary>
        /// 目标产量
        /// </summary>
        public int TargetCount { get; set; }

        /// <summary>
        /// 产量达成率
        /// </summary>
        public double ProductionAchievementRate => TargetCount > 0 ? (double)TotalCount / TargetCount * 100 : 0;

        /// <summary>
        /// 备注信息
        /// </summary>
        public string Remarks { get; set; }

        /// <summary>
        /// 统计日期（用于兼容性）
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ProductionStatistics()
        {
            StatisticsId = Guid.NewGuid().ToString();
            StartTime = DateTime.Now;
            Date = StartTime.Date;
            Shift = GetCurrentShift();
            Remarks = string.Empty;
        }

        /// <summary>
        /// 获取当前班次
        /// </summary>
        /// <returns>班次名称</returns>
        private string GetCurrentShift()
        {
            var hour = DateTime.Now.Hour;
            if (hour >= 8 && hour < 16)
                return "白班";
            else if (hour >= 16 && hour < 24)
                return "中班";
            else
                return "夜班";
        }

        /// <summary>
        /// 更新统计数据
        /// </summary>
        /// <param name="record">生产记录</param>
        public void UpdateStatistics(ProductionRecord record)
        {
            TotalCount++;

            switch (record.Status)
            {
                case ProductStatus.Good:
                    GoodCount++;
                    break;
                case ProductStatus.Defective:
                    DefectiveCount++;
                    break;
                case ProductStatus.Error:
                    ErrorCount++;
                    break;
            }

            // 更新平均节拍时间
            if (TotalCount > 0)
            {
                var totalCycleTime = AverageCycleTime * (TotalCount - 1) + record.ProcessingTimeSeconds;
                AverageCycleTime = totalCycleTime / TotalCount;
            }
        }

        /// <summary>
        /// 重置统计数据
        /// </summary>
        public void Reset()
        {
            TotalCount = 0;
            GoodCount = 0;
            DefectiveCount = 0;
            ErrorCount = 0;
            RunningTimeMinutes = 0;
            DownTimeMinutes = 0;
            AverageCycleTime = 0;
            StartTime = DateTime.Now;
        }

        /// <summary>
        /// 克隆统计对象
        /// </summary>
        /// <returns>克隆的统计对象</returns>
        public ProductionStatistics Clone()
        {
            return new ProductionStatistics
            {
                StatisticsId = this.StatisticsId,
                StartTime = this.StartTime,
                EndTime = this.EndTime,
                Shift = this.Shift,
                Operator = this.Operator,
                TotalCount = this.TotalCount,
                GoodCount = this.GoodCount,
                DefectiveCount = this.DefectiveCount,
                ErrorCount = this.ErrorCount,
                RunningTimeMinutes = this.RunningTimeMinutes,
                DownTimeMinutes = this.DownTimeMinutes,
                AverageCycleTime = this.AverageCycleTime,
                TargetCount = this.TargetCount,
                Remarks = this.Remarks
            };
        }

        /// <summary>
        /// 使用生产记录更新统计数据
        /// </summary>
        /// <param name="record">生产记录</param>
        public void UpdateWithRecord(ProductionRecord record)
        {
            UpdateStatistics(record);
        }
    }
}
