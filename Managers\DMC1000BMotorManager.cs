using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MyHMI.Events;
using MyHMI.Models;
using MyHMI.Helpers;

using MyHMI.Settings;

namespace MyHMI.Managers
{
    /// <summary>
    /// DMC1000B运动控制卡电机管理器
    /// 负责4个独立电机轴的控制：轴0(左翻转)、轴1(右翻转)、轴2(输出皮带)、轴3(输入皮带)
    /// </summary>
    public class DMC1000BMotorManager
    {
        #region 单例模式
        private static readonly Lazy<DMC1000BMotorManager> _instance = new Lazy<DMC1000BMotorManager>(() => new DMC1000BMotorManager());
        public static DMC1000BMotorManager Instance => _instance.Value;
        private DMC1000BMotorManager() { }
        #endregion

        #region 事件定义
        /// <summary>
        /// 电机位置变化事件
        /// </summary>
        public event EventHandler<MotorPositionEventArgs> MotorPositionChanged;

        /// <summary>
        /// 电机状态变化事件
        /// </summary>
        public event EventHandler<MotorStatusEventArgs> MotorStatusChanged;

        /// <summary>
        /// 电机运动完成事件
        /// </summary>
        public event EventHandler<MotorMovementCompletedEventArgs> MotorMovementCompleted;

        /// <summary>
        /// 电机报警事件
        /// </summary>
        public event EventHandler<MotorAlarmEventArgs> MotorAlarm;
        #endregion

        #region 常量定义
        /// <summary>
        /// 电机轴定义
        /// </summary>
        public const short AXIS_LEFT_FLIP = 0;      // 左翻转电机
        public const short AXIS_RIGHT_FLIP = 1;     // 右翻转电机
        public const short AXIS_OUTPUT_BELT = 2;    // 输出皮带电机
        public const short AXIS_INPUT_BELT = 3;     // 输入皮带电机

        // 为了与UI控件保持一致，添加别名常量
        public const short LEFT_FLIP_AXIS = AXIS_LEFT_FLIP;
        public const short RIGHT_FLIP_AXIS = AXIS_RIGHT_FLIP;
        public const short INPUT_BELT_AXIS = AXIS_INPUT_BELT;
        public const short OUTPUT_BELT_AXIS = AXIS_OUTPUT_BELT;

        /// <summary>
        /// 原点开关IO定义
        /// </summary>
        public const short ORG_LEFT_FLIP = 0;       // 左翻转电机原点开关
        public const short ORG_RIGHT_FLIP = 1;      // 右翻转电机原点开关

        /// <summary>
        /// 运动状态定义
        /// </summary>
        public const short MOTION_RUNNING = 0;      // 正在运行
        public const short MOTION_DONE = 1;         // 脉冲输出完毕停止
        public const short MOTION_STOP_CMD = 2;     // 指令停止
        public const short MOTION_STOP_LIMIT = 3;   // 遇限位停止
        public const short MOTION_STOP_HOME = 4;    // 遇原点停止

        /// <summary>
        /// 错误码定义
        /// </summary>
        public const short ERR_NO_ERROR = 0;        // 无错误
        #endregion

        #region 私有字段
        private bool _isInitialized = false;
        private bool _isMonitoring = false;
        private Task _monitorTask;
        private CancellationTokenSource _cancellationTokenSource;
        
        // 电机配置和状态 - 移除翻转电机参数本地缓存，使用全局参数管理器
        private readonly Dictionary<short, BeltMotorParams> _beltMotorParams = new Dictionary<short, BeltMotorParams>();
        private readonly Dictionary<short, MotorStatus> _motorStatus = new Dictionary<short, MotorStatus>();
        private readonly Dictionary<short, FlipMotorPositions> _flipMotorPositions = new Dictionary<short, FlipMotorPositions>();
        private readonly object _motorLock = new object();
        
        // 配置参数
        private int _monitorIntervalMs = 50;  // 20Hz监控频率
        #endregion

        #region 公共属性
        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// 是否正在监控
        /// </summary>
        public bool IsMonitoring => _isMonitoring;
        #endregion

        #region 初始化和释放
        /// <summary>
        /// 异步初始化DMC1000B控制卡
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> InitializeAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_isInitialized)
                {
                    LogHelper.Warning("DMC1000BMotorManager已经初始化，跳过重复初始化");
                    return true;
                }

                LogHelper.Info("开始初始化DMC1000B电机管理器...");

                // 检查控制卡是否可用
                if (!DMC1000BCardManager.Instance.IsCardAvailable())
                {
                    throw new Exception("DMC1000B控制卡不可用，电机管理器初始化失败");
                }

                // 设置脉冲输出模式 (pulse/dir模式，脉冲上升沿有效)
                await SetPulseOutputModeAsync();

                // 初始化电机参数
                await InitializeMotorParametersAsync();

                // 启动监控任务
                await StartMonitoringAsync();

                _isInitialized = true;
                LogHelper.Info("DMC1000B运动控制卡初始化完成");
                return true;

            }, false, "DMC1000B初始化");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public async Task DisposeAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info("开始释放DMC1000B资源...");

                // 停止所有电机
                await StopAllMotorsAsync();

                // 停止监控
                await StopMonitoringAsync();

                _isInitialized = false;
                LogHelper.Info("DMC1000B资源释放完成");

                return true;
            }, false, "DMC1000B资源释放");
        }
        #endregion

        #region 翻转电机控制方法
        /// <summary>
        /// 设置翻转电机参数 - 使用全局参数管理器
        /// </summary>
        /// <param name="axis">电机轴号 (0或1)</param>
        /// <param name="parameters">翻转电机参数</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SetFlipMotorParamsAsync(short axis, FlipMotorParams parameters)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                ValidateFlipMotorAxis(axis);

                if (parameters == null)
                    throw new ArgumentNullException(nameof(parameters));

                // 使用全局参数管理器更新参数
                await GlobalMotorParameterManager.UpdateFlipMotorParamsAsync(axis, parameters, "DMC1000BMotorManager设置参数");

                LogHelper.Info($"翻转电机轴{axis}参数设置成功: 脉冲当量={parameters.PulseEquivalent}°/pulse, 最大速度={parameters.MaxSpeed}°/s");
                return true;

            }, false, $"设置翻转电机轴{axis}参数");
        }

        /// <summary>
        /// 获取翻转电机参数 - 使用全局参数管理器
        /// </summary>
        /// <param name="axis">电机轴号 (0或1)</param>
        /// <returns>翻转电机参数</returns>
        public async Task<FlipMotorParams> GetFlipMotorParamsAsync(short axis)
        {
            return await Task.Run(() =>
            {
                ValidateFlipMotorAxis(axis);
                return GlobalMotorParameterManager.GetFlipMotorParams(axis);
            });
        }

        /// <summary>
        /// 翻转电机回零（简化版本，参考moto_demo实现）
        /// </summary>
        /// <param name="axis">电机轴号 (0或1)</param>
        /// <returns>是否成功</returns>
        public async Task<bool> FlipMotorHomeAsync(short axis)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                ValidateFlipMotorAxis(axis);

                var motorParams = GetFlipMotorParams(axis);
                if (motorParams == null)
                    throw new InvalidOperationException($"翻转电机轴{axis}参数未设置");

                LogHelper.Info($"翻转电机轴{axis}开始回零");

                // 停止当前运动
                await StopMotorAsync(axis);

                // 计算回零参数 - 参考moto_demo的简单实现
                int strVel = motorParams.CalculateSpeedPulse(motorParams.HomeSpeed * 0.5); // 初始速度
                int maxVel = motorParams.CalculateSpeedPulse(motorParams.HomeSpeed);       // 回零速度
                double tacc = motorParams.AccelerationTime; // 直接使用用户输入的加速时间

                // 根据配置确定回零方向（负方向回零时速度为负值）
                if (!motorParams.HomeDirection)
                {
                    maxVel = -maxVel;
                }

                LogHelper.Info($"翻转电机轴{axis}回零参数: 起始速度={strVel}pps, 最大速度={maxVel}pps, 加速时间={tacc}s");

                // 执行回零运动 - 直接调用DMC1000B API，类似moto_demo
                short result = csDmc1000.DMC1000.d1000_home_move(axis, strVel, maxVel, tacc);
                if (result != ERR_NO_ERROR)
                {
                    throw new Exception($"翻转电机轴{axis}回零启动失败，错误码: {result}");
                }

                // 监控回零过程
                bool success = await MonitorHomingProcessAsync(axis, motorParams.HomeTimeout);

                if (success)
                {
                    // 安全设置当前位置为0（带超时保护）
                    try
                    {
                        LogHelper.Debug($"翻转电机轴{axis}设置当前位置为0");

                        var setPositionTask = Task.Run(() =>
                        {
                            try
                            {
                                return csDmc1000.DMC1000.d1000_set_command_pos(axis, 0);
                            }
                            catch (Exception ex)
                            {
                                LogHelper.Error($"翻转电机轴{axis}设置位置API调用异常: {ex.Message}");
                                throw;
                            }
                        });

                        var timeoutTask = Task.Delay(2000); // 2秒超时
                        var completedTask = await Task.WhenAny(setPositionTask, timeoutTask);

                        if (completedTask == setPositionTask)
                        {
                            short setResult = await setPositionTask;
                            if (setResult == ERR_NO_ERROR)
                            {
                                LogHelper.Debug($"翻转电机轴{axis}位置设置完成");
                            }
                            else
                            {
                                LogHelper.Warning($"翻转电机轴{axis}设置位置返回错误码: {setResult}");
                            }
                        }
                        else
                        {
                            LogHelper.Warning($"翻转电机轴{axis}设置位置超时，但归零操作本身成功");
                        }
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error($"翻转电机轴{axis}设置位置为0失败: {ex.Message}");
                        // 即使设置位置失败，也继续设置归零状态，因为归零操作本身是成功的
                    }

                    // 设置归零状态标志
                    lock (_motorLock)
                    {
                        if (_motorStatus.ContainsKey(axis))
                        {
                            _motorStatus[axis].IsHomedThisSession = true;
                            _motorStatus[axis].LastHomedTime = DateTime.Now;
                            _motorStatus[axis].IsHomed = true;
                            LogHelper.Info($"翻转电机轴{axis}归零状态已更新: IsHomedThisSession=true, LastHomedTime={DateTime.Now}");
                        }
                        else
                        {
                            LogHelper.Error($"翻转电机轴{axis}状态记录不存在，无法设置归零状态");
                        }
                    }

                    LogHelper.Info($"翻转电机轴{axis}回零成功");
                }

                return success;

            }, false, $"翻转电机轴{axis}回零");
        }

        /// <summary>
        /// 翻转电机移动到指定角度
        /// </summary>
        /// <param name="axis">电机轴号 (0或1)</param>
        /// <param name="targetAngle">目标角度 (度)</param>
        /// <returns>是否成功</returns>
        public async Task<bool> FlipMotorMoveToAngleAsync(short axis, double targetAngle)
        {
            LogHelper.Info($"FlipMotorMoveToAngleAsync调用: 轴{axis}, 目标角度{targetAngle}°");

            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                ValidateFlipMotorAxis(axis);

                var motorParams = GetFlipMotorParams(axis);
                if (motorParams == null)
                    throw new InvalidOperationException($"翻转电机轴{axis}参数未设置");

                LogHelper.Debug($"翻转电机轴{axis}参数获取成功: PulseEquivalent={motorParams.PulseEquivalent}");

                // 检查当前运动状态，如果正在运动则停止
                short currentStatus = csDmc1000.DMC1000.d1000_check_done(axis);
                LogHelper.Debug($"翻转电机轴{axis}当前运动状态: {currentStatus}");

                if (currentStatus == MOTION_RUNNING)
                {
                    LogHelper.Info($"翻转电机轴{axis}正在运动，先停止当前运动");
                    await StopMotorAsync(axis);
                    // 等待停止完成
                    await WaitForMotionCompleteAsync(axis, 5000);
                }

                // 使用安全的脉冲计算
                int targetPulse = motorParams.CalculateTargetPulse(targetAngle);

                // 计算运动参数 - 使用新的速度计算方法
                int strVel = motorParams.CalculateSpeedPulse(motorParams.StartSpeed);
                int maxVel = motorParams.CalculateSpeedPulse(motorParams.MaxSpeed);
                double tacc = motorParams.AccelerationTime; // 直接使用用户输入的加速时间

                // 增强调试信息
                LogHelper.Info($"翻转电机轴{axis}绝对位置运动参数:");
                LogHelper.Info($"  目标角度: {targetAngle}° -> {targetPulse}脉冲");
                LogHelper.Info($"  起始速度: {motorParams.StartSpeed}°/s -> {strVel}pps");
                LogHelper.Info($"  最大速度: {motorParams.MaxSpeed}°/s -> {maxVel}pps");
                LogHelper.Info($"  加速时间: {tacc}s");

                // 安全获取当前位置并计算相对移动距离
                int currentPulse;
                try
                {
                    LogHelper.Info($"翻转电机轴{axis}获取当前位置...");
                    currentPulse = csDmc1000.DMC1000.d1000_get_command_pos(axis);
                    LogHelper.Info($"翻转电机轴{axis}当前位置获取成功: {currentPulse}脉冲");
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"翻转电机轴{axis}获取当前位置失败: {ex.Message}");
                    LogHelper.Warning($"翻转电机轴{axis}使用绝对位置运动作为备用方案");

                    // 备用方案：使用绝对位置运动
                    LogHelper.Info($"调用d1000_start_ta_move: axis={axis}, targetPulse={targetPulse}, strVel={strVel}, maxVel={maxVel}, tacc={tacc}");
                    short backupResult = csDmc1000.DMC1000.d1000_start_ta_move(axis, targetPulse, strVel, maxVel, tacc);
                    LogHelper.Info($"d1000_start_ta_move返回结果: {backupResult}");

                    if (backupResult != ERR_NO_ERROR)
                    {
                        throw new Exception($"翻转电机轴{axis}移动到角度{targetAngle}°失败，错误码: {backupResult}");
                    }

                    LogHelper.Info($"翻转电机轴{axis}开始移动到角度{targetAngle}°成功（备用方案）");
                    return true;
                }

                double currentAngle = currentPulse * motorParams.PulseEquivalent;
                double relativeAngle = targetAngle - currentAngle;
                int relativePulse = motorParams.CalculateTargetPulse(relativeAngle);

                LogHelper.Info($"翻转电机轴{axis}位置分析:");
                LogHelper.Info($"  当前位置: {currentAngle:F3}° ({currentPulse}脉冲)");
                LogHelper.Info($"  目标位置: {targetAngle:F3}° ({targetPulse}脉冲)");
                LogHelper.Info($"  相对移动: {relativeAngle:F3}° ({relativePulse}脉冲)");

                // 检查是否需要移动
                if (Math.Abs(relativePulse) < 1)
                {
                    LogHelper.Info($"翻转电机轴{axis}已在目标位置附近，无需移动");
                    return true;
                }

                // 使用相对位置运动确保电机实际移动
                LogHelper.Info($"调用d1000_start_t_move: axis={axis}, relativePulse={relativePulse}, strVel={strVel}, maxVel={maxVel}, tacc={tacc}");
                short result = csDmc1000.DMC1000.d1000_start_t_move(axis, relativePulse, strVel, maxVel, tacc);
                LogHelper.Info($"d1000_start_t_move返回结果: {result}");

                if (result != ERR_NO_ERROR)
                {
                    throw new Exception($"翻转电机轴{axis}移动到角度{targetAngle}°失败，错误码: {result}");
                }

                LogHelper.Info($"翻转电机轴{axis}开始移动到角度{targetAngle}°成功");
                return true;

            }, false, $"翻转电机轴{axis}移动到角度{targetAngle}°");
        }
        /// <summary>
        /// 翻转电机点动控制
        /// </summary>
        /// <param name="axis">电机轴号 (0或1)</param>
        /// <param name="direction">运动方向 (true: 正向, false: 负向)</param>
        /// <param name="jogDistance">点动距离 (度)</param>
        /// <returns>是否成功</returns>
        public async Task<bool> FlipMotorJogAsync(short axis, bool direction, double jogDistance)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                // 检查初始化状态
                if (!_isInitialized)
                {
                    LogHelper.Error("DMC1000BMotorManager未初始化，无法执行电机操作");
                    throw new InvalidOperationException("电机管理器未初始化");
                }

                ValidateFlipMotorAxis(axis);

                // 增强调试信息
                LogHelper.Info($"翻转电机轴{axis}点动请求: 方向={direction}, 距离={jogDistance}°");

                var motorParams = GetFlipMotorParams(axis);
                if (motorParams == null)
                {
                    LogHelper.Warning($"翻转电机轴{axis}参数未设置，使用默认参数进行测试");

                    // 使用默认参数进行测试，确保速度计算正确
                    var defaultParams = new FlipMotorParams(); // 使用默认值：PulseEquivalent=0.012, StartSpeed=10, MaxSpeed=60
                    int testPulse = direction ? 84 : -84; // 测试脉冲数：84脉冲 ≈ 1° (84 × 0.012 = 1.008°)

                    // 正确计算速度参数（转换为pps）
                    int testStrVel = defaultParams.CalculateSpeedPulse(defaultParams.StartSpeed);   // 10°/s -> 833pps
                    int testMaxVel = defaultParams.CalculateSpeedPulse(defaultParams.MaxSpeed);     // 60°/s -> 5000pps
                    double testTacc = defaultParams.AccelerationTime;                               // 直接使用加速时间

                    LogHelper.Info($"翻转电机轴{axis}默认测试参数: 脉冲={testPulse}, 起始速度={testStrVel}pps, 最大速度={testMaxVel}pps, 加速时间={testTacc}s");

                    short result = csDmc1000.DMC1000.d1000_start_t_move(axis, testPulse, testStrVel, testMaxVel, testTacc);
                    if (result != ERR_NO_ERROR)
                    {
                        throw new Exception($"翻转电机轴{axis}测试点动失败，错误码: {result}");
                    }
                    LogHelper.Info($"翻转电机轴{axis}使用默认参数测试点动{(direction ? "正向" : "负向")}约1°");
                    return true;
                }

                // 检查当前运动状态，如果正在运动则停止
                short currentStatus = csDmc1000.DMC1000.d1000_check_done(axis);
                if (currentStatus == MOTION_RUNNING)
                {
                    LogHelper.Info($"翻转电机轴{axis}正在运动，先停止当前运动");
                    await StopMotorAsync(axis);
                    // 等待停止完成
                    await WaitForMotionCompleteAsync(axis, 3000);
                }

                // 使用安全的脉冲计算
                int jogPulse = motorParams.CalculateTargetPulse(jogDistance);
                if (!direction) jogPulse = -jogPulse;

                // 计算运动参数 - 使用新的速度计算方法
                int strVel = motorParams.CalculateSpeedPulse(motorParams.StartSpeed);
                int maxVel = motorParams.CalculateSpeedPulse(motorParams.MaxSpeed);
                double tacc = motorParams.AccelerationTime; // 直接使用用户输入的加速时间

                // 增强调试信息
                LogHelper.Info($"翻转电机轴{axis}相对位置运动参数:");
                LogHelper.Info($"  点动距离: {jogDistance}° -> {jogPulse}脉冲");
                LogHelper.Info($"  起始速度: {motorParams.StartSpeed}°/s -> {strVel}pps");
                LogHelper.Info($"  最大速度: {motorParams.MaxSpeed}°/s -> {maxVel}pps");
                LogHelper.Info($"  加速时间: {tacc}s");

                // 执行相对位置运动
                short moveResult = csDmc1000.DMC1000.d1000_start_t_move(axis, jogPulse, strVel, maxVel, tacc);
                if (moveResult != ERR_NO_ERROR)
                {
                    throw new Exception($"翻转电机轴{axis}点动失败，错误码: {moveResult}");
                }

                LogHelper.Info($"翻转电机轴{axis}点动{(direction ? "正向" : "负向")}{jogDistance}°");
                return true;

            }, false, $"翻转电机轴{axis}点动");
        }

        /// <summary>
        /// 保存翻转电机位置
        /// </summary>
        /// <param name="axis">电机轴号 (0或1)</param>
        /// <param name="positionIndex">位置索引 (1-4)</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SaveFlipMotorPositionAsync(short axis, int positionIndex)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                ValidateFlipMotorAxis(axis);

                if (positionIndex < 1 || positionIndex > 5)
                    throw new ArgumentOutOfRangeException(nameof(positionIndex), "位置索引必须在1-5之间");

                // 检查电机是否已归零
                if (!await IsMotorHomedAsync(axis))
                {
                    throw new InvalidOperationException($"翻转电机轴{axis}未归零，请先执行归零操作后再保存位置");
                }

                var motorParams = GetFlipMotorParams(axis);
                if (motorParams == null)
                    throw new InvalidOperationException($"翻转电机轴{axis}参数未设置");

                // 获取当前位置
                int currentPulse = csDmc1000.DMC1000.d1000_get_command_pos(axis);
                double currentAngle = currentPulse * motorParams.PulseEquivalent;

                // 保存位置
                lock (_motorLock)
                {
                    if (!_flipMotorPositions.ContainsKey(axis))
                    {
                        _flipMotorPositions[axis] = new FlipMotorPositions();
                    }

                    switch (positionIndex)
                    {
                        case 1:
                            _flipMotorPositions[axis].Position1 = currentAngle;
                            break;
                        case 2:
                            _flipMotorPositions[axis].Position2 = currentAngle;
                            break;
                        case 3:
                            _flipMotorPositions[axis].Position3 = currentAngle;
                            break;
                        case 4:
                            _flipMotorPositions[axis].Position4 = currentAngle;
                            break;
                        case 5:
                            _flipMotorPositions[axis].Position5 = currentAngle;
                            break;
                    }
                }

                LogHelper.Info($"翻转电机轴{axis}位置{positionIndex}已保存: {currentAngle:F2}°");

                // 异步保存到配置文件（不等待结果，避免阻塞）
                _ = Task.Run(() =>
                {
                    try
                    {
                        Settings.Settings.Save();
                        LogHelper.Debug($"翻转电机轴{axis}位置{positionIndex}配置已保存");
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Warning($"翻转电机轴{axis}位置{positionIndex}保存到配置文件失败: {ex.Message}");
                    }
                });

                await Task.CompletedTask; // 消除CS1998警告
                return true;

            }, false, $"保存翻转电机轴{axis}位置{positionIndex}");
        }

        /// <summary>
        /// 移动到保存的翻转电机位置
        /// </summary>
        /// <param name="axis">电机轴号 (0或1)</param>
        /// <param name="positionIndex">位置索引 (1-4)</param>
        /// <returns>是否成功</returns>
        public async Task<bool> MoveToFlipMotorPositionAsync(short axis, int positionIndex)
        {
            LogHelper.Info($"开始移动到翻转电机轴{axis}位置{positionIndex}");

            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                ValidateFlipMotorAxis(axis);

                if (positionIndex < 1 || positionIndex > 5)
                    throw new ArgumentOutOfRangeException(nameof(positionIndex), "位置索引必须在1-5之间");

                LogHelper.Debug($"翻转电机轴{axis}位置{positionIndex}移动请求 - 开始检查归零状态");

                // 检查电机是否已归零
                if (!await IsMotorHomedAsync(axis))
                {
                    throw new InvalidOperationException($"翻转电机轴{axis}未归零，请先执行归零操作后再移动到位置");
                }

                LogHelper.Debug($"翻转电机轴{axis}归零状态检查通过，开始获取目标位置");

                double targetAngle;
                lock (_motorLock)
                {
                    if (!_flipMotorPositions.ContainsKey(axis))
                        throw new InvalidOperationException($"翻转电机轴{axis}没有保存的位置");

                    var positions = _flipMotorPositions[axis];

                    switch (positionIndex)
                    {
                        case 1:
                            targetAngle = positions.Position1;
                            break;
                        case 2:
                            targetAngle = positions.Position2;
                            break;
                        case 3:
                            targetAngle = positions.Position3;
                            break;
                        case 4:
                            targetAngle = positions.Position4;
                            break;
                        case 5:
                            targetAngle = positions.Position5;
                            break;
                        default:
                            throw new ArgumentOutOfRangeException(nameof(positionIndex));
                    }

                    if (double.IsNaN(targetAngle))
                        throw new InvalidOperationException($"翻转电机轴{axis}位置{positionIndex}未保存");
                }

                LogHelper.Info($"翻转电机轴{axis}位置{positionIndex}目标角度: {targetAngle}°，开始移动");

                bool result = await FlipMotorMoveToAngleAsync(axis, targetAngle);

                LogHelper.Info($"翻转电机轴{axis}移动到位置{positionIndex}结果: {result}");

                return result;

            }, false, $"移动到翻转电机轴{axis}位置{positionIndex}");
        }
        #endregion

        #region 皮带电机控制方法
        /// <summary>
        /// 设置皮带电机参数
        /// </summary>
        /// <param name="axis">电机轴号 (2或3)</param>
        /// <param name="parameters">皮带电机参数</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SetBeltMotorParamsAsync(short axis, BeltMotorParams parameters)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                ValidateBeltMotorAxis(axis);

                if (parameters == null)
                    throw new ArgumentNullException(nameof(parameters));

                // 验证参数
                var validation = parameters.Validate();
                if (!validation.IsValid)
                    throw new ArgumentException($"皮带电机参数无效: {validation.ErrorMessage}");

                lock (_motorLock)
                {
                    _beltMotorParams[axis] = parameters;
                }

                // 异步保存到配置文件（不等待结果，避免阻塞）
                _ = Task.Run(async () =>
                {
                    bool saveSuccess = await SaveBeltMotorConfigAsync();
                    if (!saveSuccess)
                    {
                        LogHelper.Warning($"皮带电机轴{axis}参数保存到配置文件失败");
                    }
                });

                LogHelper.Info($"皮带电机轴{axis}参数设置成功: 脉冲当量={parameters.PulseEquivalent}mm/pulse, 最大速度={parameters.MaxSpeed}mm/s");
                await Task.CompletedTask; // 消除CS1998警告
                return true;

            }, false, $"设置皮带电机轴{axis}参数");
        }

        /// <summary>
        /// 皮带电机点动控制
        /// </summary>
        /// <param name="axis">电机轴号 (2或3)</param>
        /// <param name="direction">运动方向 (true: 正向, false: 负向)</param>
        /// <param name="jogDistance">点动距离 (毫米)</param>
        /// <returns>是否成功</returns>
        public async Task<bool> BeltMotorJogAsync(short axis, bool direction, double jogDistance)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                ValidateBeltMotorAxis(axis);

                var motorParams = GetBeltMotorParams(axis);
                if (motorParams == null)
                    throw new InvalidOperationException($"皮带电机轴{axis}参数未设置");

                // 检查当前运动状态，如果正在运动则停止
                short currentStatus = csDmc1000.DMC1000.d1000_check_done(axis);
                if (currentStatus == MOTION_RUNNING)
                {
                    LogHelper.Info($"皮带电机轴{axis}正在运动，先停止当前运动");
                    await StopMotorAsync(axis);
                    // 等待停止完成
                    await WaitForMotionCompleteAsync(axis, 3000);
                }

                // 使用安全的脉冲计算
                int jogPulse = motorParams.CalculateTargetPulse(jogDistance);
                if (!direction) jogPulse = -jogPulse;

                // 计算运动参数 - 使用新的速度计算方法
                int strVel = motorParams.CalculateSpeedPulse(motorParams.StartSpeed);
                int maxVel = motorParams.CalculateSpeedPulse(motorParams.MaxSpeed);
                double tacc = motorParams.AccelerationTime; // 直接使用用户输入的加速时间

                // 执行相对位置运动
                short result = csDmc1000.DMC1000.d1000_start_t_move(axis, jogPulse, strVel, maxVel, tacc);
                if (result != ERR_NO_ERROR)
                {
                    throw new Exception($"皮带电机轴{axis}点动失败，错误码: {result}");
                }

                LogHelper.Info($"皮带电机轴{axis}点动{(direction ? "正向" : "负向")}{jogDistance}mm");
                return true;

            }, false, $"皮带电机轴{axis}点动");
        }

        /// <summary>
        /// 皮带电机连续运转
        /// </summary>
        /// <param name="axis">电机轴号 (2或3)</param>
        /// <param name="direction">运动方向 (true: 正向, false: 负向)</param>
        /// <returns>是否成功</returns>
        public async Task<bool> BeltMotorContinuousRunAsync(short axis, bool direction)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                ValidateBeltMotorAxis(axis);

                var motorParams = GetBeltMotorParams(axis);
                if (motorParams == null)
                    throw new InvalidOperationException($"皮带电机轴{axis}参数未设置");

                // 检查当前运动状态，如果正在运动则停止
                short currentStatus = csDmc1000.DMC1000.d1000_check_done(axis);
                if (currentStatus == MOTION_RUNNING)
                {
                    LogHelper.Info($"皮带电机轴{axis}正在运动，先停止当前运动");
                    await StopMotorAsync(axis);
                    // 等待停止完成
                    await WaitForMotionCompleteAsync(axis, 3000);
                }

                // 计算运动参数 - 使用新的速度计算方法
                int strVel = motorParams.CalculateSpeedPulse(motorParams.StartSpeed);
                int maxVel = motorParams.CalculateSpeedPulse(motorParams.MaxSpeed);
                if (!direction) maxVel = -maxVel;
                double tacc = motorParams.AccelerationTime; // 直接使用用户输入的加速时间

                // 执行连续运动
                short result = csDmc1000.DMC1000.d1000_start_tv_move(axis, strVel, maxVel, tacc);
                if (result != ERR_NO_ERROR)
                {
                    throw new Exception($"皮带电机轴{axis}连续运转失败，错误码: {result}");
                }

                LogHelper.Info($"皮带电机轴{axis}开始连续{(direction ? "正向" : "负向")}运转");
                return true;

            }, false, $"皮带电机轴{axis}连续运转");
        }
        #endregion

        #region 电机诊断和验证方法
        /// <summary>
        /// 验证电机初始化状态
        /// </summary>
        /// <returns>验证结果和详细信息</returns>
        public async Task<(bool IsValid, string Details)> ValidateMotorInitializationAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                await Task.CompletedTask; // 消除CS1998警告
                var details = new List<string>();
                bool isValid = true;

                // 1. 检查管理器初始化状态
                if (!_isInitialized)
                {
                    isValid = false;
                    details.Add("❌ DMC1000BMotorManager未初始化");
                }
                else
                {
                    details.Add("✅ DMC1000BMotorManager已初始化");
                }

                // 2. 检查控制卡初始化状态
                var cardStatus = DMC1000BCardManager.Instance.GetCardStatus();
                if (!cardStatus.IsInitialized)
                {
                    isValid = false;
                    details.Add("❌ DMC1000B控制卡未初始化");
                }
                else
                {
                    details.Add($"✅ DMC1000B控制卡已初始化 (卡数: {cardStatus.CardCount})");
                }

                // 3. 检查电机参数配置
                for (short axis = 0; axis <= 1; axis++)
                {
                    var motorParams = GetFlipMotorParams(axis);
                    if (motorParams == null)
                    {
                        isValid = false;
                        details.Add($"❌ 翻转电机轴{axis}参数未配置");
                    }
                    else
                    {
                        var validation = motorParams.Validate();
                        if (!validation.IsValid)
                        {
                            isValid = false;
                            details.Add($"❌ 翻转电机轴{axis}参数无效: {validation.ErrorMessage}");
                        }
                        else
                        {
                            details.Add($"✅ 翻转电机轴{axis}参数配置正确");
                        }
                    }
                }

                // 4. 检查脉冲输出模式（如果可能的话）
                details.Add("ℹ️ 脉冲输出模式: pulse/dir模式，脉冲上升沿有效");

                return (isValid, string.Join("\n", details));

            }, (false, "验证过程中发生异常"), "验证电机初始化状态");
        }

        /// <summary>
        /// 诊断电机问题
        /// </summary>
        /// <param name="axis">电机轴号</param>
        /// <returns>诊断结果</returns>
        public async Task<string> DiagnoseMotorIssuesAsync(short axis)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                await Task.CompletedTask; // 消除CS1998警告
                var diagnosis = new List<string>();
                diagnosis.Add($"=== 翻转电机轴{axis}诊断报告 ===");

                // 1. 基础状态检查
                try
                {
                    short motionStatus = csDmc1000.DMC1000.d1000_check_done(axis);
                    string statusText;
                    switch (motionStatus)
                    {
                        case 0:
                            statusText = "正在运行";
                            break;
                        case 1:
                            statusText = "脉冲输出完毕停止";
                            break;
                        case 2:
                            statusText = "指令停止";
                            break;
                        case 3:
                            statusText = "遇限位停止";
                            break;
                        case 4:
                            statusText = "遇原点停止";
                            break;
                        default:
                            statusText = $"未知状态({motionStatus})";
                            break;
                    }
                    diagnosis.Add($"✅ 运动状态: {statusText}");
                }
                catch (Exception ex)
                {
                    diagnosis.Add($"❌ 无法读取运动状态: {ex.Message}");
                }

                // 2. 位置信息
                try
                {
                    int currentPulse = csDmc1000.DMC1000.d1000_get_command_pos(axis);
                    double currentAngle = GetFlipMotorCurrentAngle(axis);
                    diagnosis.Add($"✅ 当前位置: {currentPulse}脉冲 = {currentAngle:F3}°");
                }
                catch (Exception ex)
                {
                    diagnosis.Add($"❌ 无法读取位置信息: {ex.Message}");
                }

                // 3. 参数配置检查
                var motorParams = GetFlipMotorParams(axis);
                if (motorParams != null)
                {
                    diagnosis.Add($"✅ 电机参数已配置:");
                    diagnosis.Add($"   - 脉冲当量: {motorParams.PulseEquivalent}°/pulse");
                    diagnosis.Add($"   - 最大速度: {motorParams.MaxSpeed}°/s");
                    diagnosis.Add($"   - 起始速度: {motorParams.StartSpeed}°/s");
                    diagnosis.Add($"   - 加速度: {motorParams.Acceleration}°/s²");
                }
                else
                {
                    diagnosis.Add($"❌ 电机参数未配置");
                }

                return string.Join("\n", diagnosis);

            }, $"诊断翻转电机轴{axis}时发生异常", $"诊断翻转电机轴{axis}");
        }

        /// <summary>
        /// 测试电机基础运动
        /// </summary>
        /// <param name="axis">电机轴号</param>
        /// <returns>测试结果</returns>
        public async Task<bool> TestMotorBasicMovementAsync(short axis)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info($"开始测试翻转电机轴{axis}基础运动");

                // 1. 验证初始化状态
                var (isValid, details) = await ValidateMotorInitializationAsync();
                if (!isValid)
                {
                    LogHelper.Error($"电机初始化验证失败:\n{details}");
                    return false;
                }

                // 2. 执行小幅度测试运动
                double testAngle = 1.0; // 测试1度运动
                LogHelper.Info($"执行测试运动: {testAngle}°");

                bool moveResult = await FlipMotorJogAsync(axis, true, testAngle);
                if (!moveResult)
                {
                    LogHelper.Error($"翻转电机轴{axis}测试运动失败");
                    return false;
                }

                // 3. 等待运动完成
                bool completed = await WaitForMotionCompleteAsync(axis, 10000);
                if (!completed)
                {
                    LogHelper.Warning($"翻转电机轴{axis}测试运动超时");
                    return false;
                }

                LogHelper.Info($"翻转电机轴{axis}基础运动测试成功");
                return true;

            }, false, $"测试翻转电机轴{axis}基础运动");
        }
        #endregion

        #region 状态监控方法
        /// <summary>
        /// 获取电机当前角度 (翻转电机)
        /// </summary>
        /// <param name="axis">电机轴号 (0或1)</param>
        /// <returns>当前角度 (度)</returns>
        public double GetFlipMotorCurrentAngle(short axis)
        {
            try
            {
                ValidateFlipMotorAxis(axis);

                var motorParams = GetFlipMotorParams(axis);
                if (motorParams == null) return 0;

                int currentPulse = csDmc1000.DMC1000.d1000_get_command_pos(axis);
                // 修复：正确的角度计算 - 脉冲数 × 脉冲当量 = 角度
                // PulseEquivalent = 0.012 (°/pulse)，基于10,000 pulse/r × 1:3减速比
                return currentPulse * motorParams.PulseEquivalent;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"获取翻转电机轴{axis}当前角度失败", ex);
                return 0;
            }
        }

        /// <summary>
        /// 获取电机当前位置 (皮带电机)
        /// </summary>
        /// <param name="axis">电机轴号 (2或3)</param>
        /// <returns>当前位置 (毫米)</returns>
        public double GetBeltMotorCurrentPosition(short axis)
        {
            try
            {
                ValidateBeltMotorAxis(axis);

                var motorParams = GetBeltMotorParams(axis);
                if (motorParams == null) return 0;

                int currentPulse = csDmc1000.DMC1000.d1000_get_command_pos(axis);
                // 修复：正确的位置计算 - 脉冲数 × 脉冲当量 = 位置
                // PulseEquivalent = 0.001 (mm/pulse)，表示每个脉冲对应0.001mm
                return currentPulse * motorParams.PulseEquivalent;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"获取皮带电机轴{axis}当前位置失败", ex);
                return 0;
            }
        }

        /// <summary>
        /// 获取电机运动状态
        /// </summary>
        /// <param name="axis">电机轴号</param>
        /// <returns>运动状态</returns>
        public short GetMotorMotionStatus(short axis)
        {
            try
            {
                return csDmc1000.DMC1000.d1000_check_done(axis);
            }
            catch (Exception ex)
            {
                LogHelper.Error($"获取电机轴{axis}运动状态失败", ex);
                return MOTION_DONE;
            }
        }

        /// <summary>
        /// 检查电机是否正在运动
        /// </summary>
        /// <param name="axis">电机轴号</param>
        /// <returns>是否正在运动</returns>
        public bool IsMotorMoving(short axis)
        {
            return GetMotorMotionStatus(axis) == MOTION_RUNNING;
        }

        /// <summary>
        /// 检查电机是否已归零 - 优化版本，解决误报问题
        /// </summary>
        /// <param name="axis">电机轴号</param>
        /// <returns>是否已归零</returns>
        public async Task<bool> IsMotorHomedAsync(short axis)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                ValidateFlipMotorAxis(axis);

                // 1. 检查本次会话归零状态
                lock (_motorLock)
                {
                    if (_motorStatus.ContainsKey(axis))
                    {
                        bool isHomedThisSession = _motorStatus[axis].IsHomedThisSession;
                        LogHelper.Debug($"翻转电机轴{axis}会话归零状态: IsHomedThisSession={isHomedThisSession}");

                        if (isHomedThisSession)
                        {
                            // 如果本次会话已归零，直接返回true，无论当前位置在哪里
                            LogHelper.Info($"翻转电机轴{axis}归零状态检查: 本次会话已归零，直接返回true");
                            return true;
                        }
                    }
                    else
                    {
                        LogHelper.Warning($"翻转电机轴{axis}状态记录不存在");
                    }
                }

                // 2. 如果没有会话记录，进行完整检查
                var motorParams = GetFlipMotorParams(axis);
                if (motorParams == null)
                {
                    LogHelper.Warning($"翻转电机轴{axis}参数未设置，无法检查归零状态");
                    return false;
                }

                // 检查当前位置是否接近零点（1度以内）
                int currentPulse = csDmc1000.DMC1000.d1000_get_command_pos(axis);
                double currentAngle = currentPulse * motorParams.PulseEquivalent;
                bool isNearZero = Math.Abs(currentAngle) <= 1.0;

                // 检查原点传感器状态（如果配置了）
                bool homeSensorCheck = true;
                if (motorParams.HomeIO >= 0 && isNearZero)
                {
                    try
                    {
                        homeSensorCheck = await CheckHomeSensorAsync(motorParams.HomeIO);
                        LogHelper.Debug($"翻转电机轴{axis}原点传感器状态: {homeSensorCheck}");
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Warning($"检查翻转电机轴{axis}原点传感器状态失败，忽略传感器检查: {ex.Message}");
                        homeSensorCheck = true;
                    }
                }

                bool isHomed = isNearZero && homeSensorCheck;
                LogHelper.Info($"翻转电机轴{axis}归零状态检查: 当前角度={currentAngle:F3}°, 接近零点={isNearZero}, 传感器检查={homeSensorCheck}, 最终结果={isHomed}");

                return isHomed;

            }, false, $"检查翻转电机轴{axis}归零状态");
        }

        /// <summary>
        /// 获取翻转电机保存的位置
        /// </summary>
        /// <param name="axis">电机轴号 (0或1)</param>
        /// <returns>保存的位置</returns>
        public FlipMotorPositions GetFlipMotorPositions(short axis)
        {
            lock (_motorLock)
            {
                return _flipMotorPositions.ContainsKey(axis) ? _flipMotorPositions[axis] : new FlipMotorPositions();
            }
        }

        /// <summary>
        /// 停止所有电机
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> StopAllMotorsAsync()
        {
            var tasks = new List<Task<bool>>();

            // 停止所有4个轴
            for (short axis = 0; axis < 4; axis++)
            {
                tasks.Add(StopMotorAsync(axis));
            }

            var results = await Task.WhenAll(tasks);
            bool allSuccess = results.All(r => r);

            LogHelper.Info($"停止所有电机{(allSuccess ? "成功" : "部分失败")}");
            return allSuccess;
        }

        /// <summary>
        /// 急停所有电机
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> EmergencyStopAllMotorsAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    bool allSuccess = true;
                    for (short axis = 0; axis < 4; axis++)
                    {
                        short result = csDmc1000.DMC1000.d1000_immediate_stop(axis);
                        if (result != ERR_NO_ERROR)
                        {
                            allSuccess = false;
                            LogHelper.Error($"急停电机轴{axis}失败，错误码: {result}");
                        }
                    }

                    LogHelper.Info($"急停所有电机{(allSuccess ? "成功" : "部分失败")}");
                    return allSuccess;
                }
                catch (Exception ex)
                {
                    LogHelper.Error("急停所有电机失败", ex);
                    return false;
                }
            });
        }
        #endregion

        #region 私有辅助方法
        /// <summary>
        /// 验证翻转电机轴号
        /// </summary>
        /// <param name="axis">轴号</param>
        private void ValidateFlipMotorAxis(short axis)
        {
            if (axis != AXIS_LEFT_FLIP && axis != AXIS_RIGHT_FLIP)
                throw new ArgumentOutOfRangeException(nameof(axis), $"无效的翻转电机轴号: {axis}");
        }

        /// <summary>
        /// 验证皮带电机轴号
        /// </summary>
        /// <param name="axis">轴号</param>
        private void ValidateBeltMotorAxis(short axis)
        {
            if (axis != AXIS_OUTPUT_BELT && axis != AXIS_INPUT_BELT)
                throw new ArgumentOutOfRangeException(nameof(axis), $"无效的皮带电机轴号: {axis}");
        }

        /// <summary>
        /// 获取翻转电机参数 - 使用全局参数管理器
        /// </summary>
        /// <param name="axis">轴号</param>
        /// <returns>翻转电机参数</returns>
        private FlipMotorParams GetFlipMotorParams(short axis)
        {
            return GlobalMotorParameterManager.GetFlipMotorParams(axis);
        }

        /// <summary>
        /// 获取皮带电机参数
        /// </summary>
        /// <param name="axis">轴号</param>
        /// <returns>皮带电机参数</returns>
        public BeltMotorParams GetBeltMotorParams(short axis)
        {
            lock (_motorLock)
            {
                return _beltMotorParams.ContainsKey(axis) ? _beltMotorParams[axis] : null;
            }
        }

        /// <summary>
        /// 停止指定电机
        /// </summary>
        /// <param name="axis">轴号</param>
        /// <returns>是否成功</returns>
        public async Task<bool> StopMotorAsync(short axis)
        {
            return await Task.Run(() =>
            {
                try
                {
                    short result = csDmc1000.DMC1000.d1000_decel_stop(axis);
                    return result == ERR_NO_ERROR;
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"停止电机轴{axis}失败", ex);
                    return false;
                }
            });
        }

        /// <summary>
        /// 等待运动完成
        /// </summary>
        /// <param name="axis">轴号</param>
        /// <param name="timeoutMs">超时时间(毫秒)</param>
        /// <returns>是否成功</returns>
        private async Task<bool> WaitForMotionCompleteAsync(short axis, int timeoutMs)
        {
            return await Task.Run(async () =>
            {
                var startTime = DateTime.Now;
                while ((DateTime.Now - startTime).TotalMilliseconds < timeoutMs)
                {
                    try
                    {
                        short status = csDmc1000.DMC1000.d1000_check_done(axis);
                        if (status != MOTION_RUNNING)
                        {
                            return status == MOTION_DONE || status == MOTION_STOP_HOME;
                        }
                        await Task.Delay(50);
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error($"检查电机轴{axis}运动状态失败", ex);
                        return false;
                    }
                }
                return false; // 超时
            });
        }

        /// <summary>
        /// 设置脉冲输出模式
        /// </summary>
        /// <returns>是否成功</returns>
        private async Task<bool> SetPulseOutputModeAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    bool allSuccess = true;
                    for (short axis = 0; axis < 4; axis++)
                    {
                        // 设置为pulse/dir模式，脉冲上升沿有效
                        short result = csDmc1000.DMC1000.d1000_set_pls_outmode(axis, 0);
                        if (result != ERR_NO_ERROR)
                        {
                            allSuccess = false;
                            LogHelper.Error($"设置电机轴{axis}脉冲输出模式失败，错误码: {result}");
                        }
                    }

                    LogHelper.Info($"设置脉冲输出模式{(allSuccess ? "成功" : "部分失败")}");
                    return allSuccess;
                }
                catch (Exception ex)
                {
                    LogHelper.Error("设置脉冲输出模式失败", ex);
                    return false;
                }
            });
        }

        /// <summary>
        /// 初始化电机参数
        /// </summary>
        /// <returns>是否成功</returns>
        private async Task<bool> InitializeMotorParametersAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    // 翻转电机参数现在由GlobalMotorParameterManager管理，不需要本地初始化
                    // 全局参数管理器会自动从Settings系统获取参数，如果没有则使用默认值

                    // 从配置文件加载皮带电机参数
                    LoadBeltMotorConfigFromSystem();

                    // 如果皮带电机配置加载失败，使用默认参数
                    if (!_beltMotorParams.ContainsKey(OUTPUT_BELT_AXIS) || _beltMotorParams[OUTPUT_BELT_AXIS] == null)
                    {
                        var outputBeltParams = new BeltMotorParams
                        {
                            MotorName = "输出皮带电机",
                            PulseEquivalent = 0.01,     // 0.01mm/pulse (基于10000 pulse/r)
                            MaxSpeed = 100,             // 100mm/s
                            StartSpeed = 10,            // 10mm/s
                            Acceleration = 500,         // 500mm/s²
                            JogDistance = 10            // 10mm
                        };
                        _beltMotorParams[OUTPUT_BELT_AXIS] = outputBeltParams;
                    }

                    if (!_beltMotorParams.ContainsKey(INPUT_BELT_AXIS) || _beltMotorParams[INPUT_BELT_AXIS] == null)
                    {
                        var inputBeltParams = new BeltMotorParams
                        {
                            MotorName = "输入皮带电机",
                            PulseEquivalent = 0.01,     // 0.01mm/pulse (基于10000 pulse/r)
                            MaxSpeed = 100,             // 100mm/s
                            StartSpeed = 10,            // 10mm/s
                            Acceleration = 500,         // 500mm/s²
                            JogDistance = 10            // 10mm
                        };
                        _beltMotorParams[INPUT_BELT_AXIS] = inputBeltParams;
                    }

                    lock (_motorLock)
                    {

                        // 初始化翻转电机位置（如果配置中没有）
                        if (!_flipMotorPositions.ContainsKey(AXIS_LEFT_FLIP))
                            _flipMotorPositions[AXIS_LEFT_FLIP] = new FlipMotorPositions();
                        if (!_flipMotorPositions.ContainsKey(AXIS_RIGHT_FLIP))
                            _flipMotorPositions[AXIS_RIGHT_FLIP] = new FlipMotorPositions();

                        // 初始化电机状态
                        for (short axis = 0; axis < 4; axis++)
                        {
                            _motorStatus[axis] = new MotorStatus
                            {
                                MotorId = axis,
                                CurrentPosition = 0,
                                IsEnabled = true,
                                IsHomed = false,
                                IsMoving = false,
                                UpdateTime = DateTime.Now
                            };
                        }
                    }

                    LogHelper.Info("电机参数初始化完成");
                    return true;
                }
                catch (Exception ex)
                {
                    LogHelper.Error("初始化电机参数失败", ex);
                    return false;
                }
            });
        }

        /// <summary>
        /// 启动监控任务
        /// </summary>
        /// <returns>是否成功</returns>
        private async Task<bool> StartMonitoringAsync()
        {
            await Task.CompletedTask; // 消除CS1998警告
            try
            {
                if (_isMonitoring)
                {
                    LogHelper.Warning("电机监控已经在运行");
                    return true;
                }

                _cancellationTokenSource = new CancellationTokenSource();
                _monitorTask = Task.Run(async () => await MonitorMotorsLoopAsync(_cancellationTokenSource.Token));
                _isMonitoring = true;

                LogHelper.Info("电机监控任务已启动");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("启动电机监控任务失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 停止监控任务
        /// </summary>
        /// <returns>是否成功</returns>
        private async Task<bool> StopMonitoringAsync()
        {
            try
            {
                if (!_isMonitoring)
                    return true;

                _cancellationTokenSource?.Cancel();

                if (_monitorTask != null)
                {
                    await _monitorTask;
                }

                _isMonitoring = false;
                LogHelper.Info("电机监控任务已停止");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("停止电机监控任务失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 电机监控循环
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        private async Task MonitorMotorsLoopAsync(CancellationToken cancellationToken)
        {
            LogHelper.Info("电机监控循环开始");

            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    await MonitorAllMotorsAsync();
                    await Task.Delay(_monitorIntervalMs, cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                LogHelper.Info("电机监控循环被取消");
            }
            catch (Exception ex)
            {
                LogHelper.Error("电机监控循环异常", ex);
            }

            LogHelper.Info("电机监控循环结束");
        }

        /// <summary>
        /// 监控所有电机状态
        /// </summary>
        /// <returns></returns>
        private async Task MonitorAllMotorsAsync()
        {
            try
            {
                for (short axis = 0; axis < 4; axis++)
                {
                    await MonitorSingleMotorAsync(axis);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("监控电机状态失败", ex);
            }
        }

        /// <summary>
        /// 监控单个电机状态
        /// </summary>
        /// <param name="axis">电机轴号</param>
        /// <returns></returns>
        private async Task MonitorSingleMotorAsync(short axis)
        {
            await Task.Run(() =>
            {
                try
                {
                    lock (_motorLock)
                    {
                        if (_motorStatus.ContainsKey(axis))
                        {
                            var status = _motorStatus[axis];
                            var previousPosition = status.CurrentPosition;
                            var previousMoving = status.IsMoving;

                            // 更新位置
                            if (axis <= 1) // 翻转电机
                            {
                                status.CurrentPosition = GetFlipMotorCurrentAngle(axis);
                            }
                            else // 皮带电机
                            {
                                status.CurrentPosition = GetBeltMotorCurrentPosition(axis);
                            }

                            // 更新运动状态
                            status.IsMoving = IsMotorMoving(axis);
                            status.UpdateTime = DateTime.Now;

                            // 触发位置变化事件
                            if (Math.Abs(status.CurrentPosition - previousPosition) > 0.01)
                            {
                                MotorPositionChanged?.Invoke(this, new MotorPositionEventArgs(axis, status.CurrentPosition, 0));
                            }

                            // 触发状态变化事件
                            if (status.IsMoving != previousMoving)
                            {
                                MotorStatusChanged?.Invoke(this, new MotorStatusEventArgs(status));
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"监控电机轴{axis}状态失败", ex);
                }
            });
        }

        /// <summary>
        /// 设置翻转电机回零方向
        /// </summary>
        /// <param name="axis">电机轴号 (0或1)</param>
        /// <param name="positiveDirection">true: 正方向回零, false: 负方向回零</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SetFlipMotorHomeDirectionAsync(short axis, bool positiveDirection)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                ValidateFlipMotorAxis(axis);

                // 获取当前参数
                var motorParams = GetFlipMotorParams(axis);
                if (motorParams == null)
                    throw new InvalidOperationException($"翻转电机轴{axis}参数未设置");

                // 更新回零方向
                motorParams.HomeDirection = positiveDirection;

                // 使用全局参数管理器保存更新后的参数
                await GlobalMotorParameterManager.UpdateFlipMotorParamsAsync(axis, motorParams, "设置回零方向");

                string direction = positiveDirection ? "正方向" : "负方向";
                LogHelper.Info($"翻转电机轴{axis}回零方向已设置为: {direction}");

                return true;

            }, false, $"设置翻转电机轴{axis}回零方向");
        }

        /// <summary>
        /// 获取翻转电机回零方向
        /// </summary>
        /// <param name="axis">电机轴号 (0或1)</param>
        /// <returns>true: 正方向, false: 负方向</returns>
        public bool GetFlipMotorHomeDirection(short axis)
        {
            ValidateFlipMotorAxis(axis);
            var motorParams = GetFlipMotorParams(axis);
            return motorParams?.HomeDirection ?? false;
        }

        /// <summary>
        /// 翻转电机正方向移动（用于测试正方向）
        /// </summary>
        /// <param name="axis">电机轴号 (0或1)</param>
        /// <param name="angle">移动角度（度）</param>
        /// <returns>是否成功</returns>
        public async Task<bool> FlipMotorMovePositiveAsync(short axis, double angle)
        {
            return await FlipMotorMoveToAngleAsync(axis, Math.Abs(angle));
        }

        /// <summary>
        /// 翻转电机负方向移动（用于测试负方向）
        /// </summary>
        /// <param name="axis">电机轴号 (0或1)</param>
        /// <param name="angle">移动角度（度）</param>
        /// <returns>是否成功</returns>
        public async Task<bool> FlipMotorMoveNegativeAsync(short axis, double angle)
        {
            return await FlipMotorMoveToAngleAsync(axis, -Math.Abs(angle));
        }

        #region 安全回零私有方法
        /// <summary>
        /// 执行回零前安全检查
        /// </summary>
        /// <param name="axis">电机轴号</param>
        /// <param name="motorParams">电机参数</param>
        private async Task PerformHomingSafetyChecksAsync(short axis, FlipMotorParams motorParams)
        {
            LogHelper.Info($"翻转电机轴{axis}执行回零前安全检查");

            // 检查原点传感器当前状态
            if (motorParams.HomeIO >= 0)
            {
                bool homeState = await CheckHomeSensorAsync(motorParams.HomeIO);
                if (homeState)
                {
                    LogHelper.Warning($"翻转电机轴{axis}原点传感器已触发，可能已在原点位置");
                    // 注意：这里可以选择直接设置为原点或进行微调，根据实际需求决定
                }
            }

            // 检查正向限位开关状态
            if (motorParams.PositiveLimitIO >= 0)
            {
                bool posLimitState = await CheckLimitSensorAsync(motorParams.PositiveLimitIO);
                if (posLimitState)
                    throw new InvalidOperationException($"翻转电机轴{axis}正向限位已触发，无法安全回零");
            }

            // 检查负向限位开关状态
            if (motorParams.NegativeLimitIO >= 0)
            {
                bool negLimitState = await CheckLimitSensorAsync(motorParams.NegativeLimitIO);
                if (negLimitState)
                    throw new InvalidOperationException($"翻转电机轴{axis}负向限位已触发，无法安全回零");
            }

            LogHelper.Info($"翻转电机轴{axis}回零前安全检查通过");
        }

        /// <summary>
        /// 执行安全回零
        /// </summary>
        /// <param name="axis">电机轴号</param>
        /// <param name="motorParams">电机参数</param>
        /// <returns>是否成功</returns>
        private async Task<bool> ExecuteSafeHomingAsync(short axis, FlipMotorParams motorParams)
        {
            // 计算回零参数 - 使用新的安全计算方法
            int strVel = motorParams.CalculateSpeedPulse(motorParams.HomeSpeed * 0.5); // 初始速度为回零速度的50%
            int maxVel = motorParams.CalculateSpeedPulse(motorParams.HomeSpeed);       // 回零速度

            // 根据配置确定回零方向
            if (!motorParams.HomeDirection) maxVel = -maxVel;

            double tacc = motorParams.CalculateAccelerationTime();

            LogHelper.Info($"翻转电机轴{axis}开始回零: 方向={motorParams.HomeDirection}, 速度={motorParams.HomeSpeed}°/s");

            // 执行回零运动
            short result = csDmc1000.DMC1000.d1000_home_move(axis, strVel, maxVel, tacc);
            if (result != ERR_NO_ERROR)
            {
                throw new Exception($"翻转电机轴{axis}回零启动失败，错误码: {result}");
            }

            // 监控回零过程
            return await MonitorHomingProcessAsync(axis, motorParams.HomeTimeout);
        }

        /// <summary>
        /// 监控回零过程（带API调用超时保护）
        /// </summary>
        /// <param name="axis">电机轴号</param>
        /// <param name="timeoutMs">超时时间（毫秒）</param>
        /// <returns>是否成功</returns>
        private async Task<bool> MonitorHomingProcessAsync(short axis, int timeoutMs)
        {
            var startTime = DateTime.Now;
            var timeout = TimeSpan.FromMilliseconds(timeoutMs);
            int consecutiveFailures = 0;
            const int maxConsecutiveFailures = 3; // 允许连续3次API调用失败
            const int apiTimeoutMs = 1000; // API调用超时时间1秒

            LogHelper.Info($"翻转电机轴{axis}开始监控回零过程，超时时间: {timeoutMs}ms");

            while (DateTime.Now - startTime < timeout)
            {
                short status;

                try
                {
                    // 使用超时保护的API调用
                    LogHelper.Debug($"翻转电机轴{axis}检查运动状态...");

                    var checkTask = Task.Run(() =>
                    {
                        try
                        {
                            return csDmc1000.DMC1000.d1000_check_done(axis);
                        }
                        catch (Exception ex)
                        {
                            LogHelper.Error($"翻转电机轴{axis}硬件API调用异常: {ex.Message}");
                            throw;
                        }
                    });

                    // 使用Task.WhenAny实现超时（兼容.NET Framework）
                    var timeoutTask = Task.Delay(apiTimeoutMs);
                    var completedTask = await Task.WhenAny(checkTask, timeoutTask);

                    if (completedTask == checkTask)
                    {
                        // API调用成功完成
                        status = await checkTask;
                        consecutiveFailures = 0; // 重置失败计数
                        LogHelper.Debug($"翻转电机轴{axis}状态检查成功: {status}");
                    }
                    else
                    {
                        // API调用超时
                        consecutiveFailures++;
                        LogHelper.Warning($"翻转电机轴{axis}状态检查超时 ({consecutiveFailures}/{maxConsecutiveFailures})");

                        if (consecutiveFailures >= maxConsecutiveFailures)
                        {
                            throw new TimeoutException($"翻转电机轴{axis}连续{maxConsecutiveFailures}次API调用超时，可能存在硬件问题");
                        }

                        // 短暂等待后继续尝试
                        await Task.Delay(200);
                        continue;
                    }
                }
                catch (TimeoutException)
                {
                    // 重新抛出超时异常
                    throw;
                }
                catch (Exception ex)
                {
                    consecutiveFailures++;
                    LogHelper.Error($"翻转电机轴{axis}状态检查异常 ({consecutiveFailures}/{maxConsecutiveFailures}): {ex.Message}");

                    if (consecutiveFailures >= maxConsecutiveFailures)
                    {
                        throw new Exception($"翻转电机轴{axis}连续{maxConsecutiveFailures}次API调用失败: {ex.Message}");
                    }

                    // 短暂等待后继续尝试
                    await Task.Delay(200);
                    continue;
                }

                // 处理运动状态
                switch (status)
                {
                    case 0: // 正在运行
                        LogHelper.Debug($"翻转电机轴{axis}正在回零中...");
                        await Task.Delay(100);
                        continue;

                    case 4: // 遇原点停止 - 回零成功
                        LogHelper.Info($"翻转电机轴{axis}回零成功：遇原点停止");
                        return true;

                    case 3: // 遇限位停止 - 回零失败
                        throw new Exception($"翻转电机轴{axis}回零失败：遇限位停止");

                    case 2: // 指令停止 - 被手动停止
                        throw new Exception($"翻转电机轴{axis}回零失败：被手动停止");

                    case 1: // 脉冲输出完毕停止 - 未找到原点
                        throw new Exception($"翻转电机轴{axis}回零失败：未找到原点信号");

                    default:
                        throw new Exception($"翻转电机轴{axis}回零失败：未知状态 {status}");
                }
            }

            // 超时处理
            LogHelper.Error($"翻转电机轴{axis}回零超时，执行紧急停止");
            try
            {
                await StopMotorAsync(axis);
            }
            catch (Exception ex)
            {
                LogHelper.Error($"翻转电机轴{axis}紧急停止失败: {ex.Message}");
            }
            throw new TimeoutException($"翻转电机轴{axis}回零超时");
        }

        /// <summary>
        /// 验证回零结果
        /// </summary>
        /// <param name="axis">电机轴号</param>
        /// <param name="motorParams">电机参数</param>
        private async Task VerifyHomingResultAsync(short axis, FlipMotorParams motorParams)
        {
            // 设置当前位置为0
            short result = csDmc1000.DMC1000.d1000_set_command_pos(axis, 0);
            if (result != ERR_NO_ERROR)
            {
                LogHelper.Warning($"设置翻转电机轴{axis}位置为0失败，错误码: {result}");
            }

            // 验证原点传感器状态（如果配置了）
            if (motorParams.HomeIO >= 0)
            {
                bool homeState = await CheckHomeSensorAsync(motorParams.HomeIO);
                if (!homeState)
                {
                    LogHelper.Warning($"翻转电机轴{axis}回零完成但原点传感器未触发，请检查传感器状态");
                }
                else
                {
                    LogHelper.Info($"翻转电机轴{axis}回零验证通过：原点传感器已触发");
                }
            }
        }

        /// <summary>
        /// 检查原点传感器状态
        /// </summary>
        /// <param name="homeIO">原点传感器IO编号</param>
        /// <returns>传感器状态</returns>
        private async Task<bool> CheckHomeSensorAsync(int homeIO)
        {
            string ioNumber = $"X{homeIO:D3}";
            return await DMC1000BIOManager.Instance.ReadInputAsync(ioNumber);
        }

        /// <summary>
        /// 检查限位开关状态
        /// </summary>
        /// <param name="limitIO">限位开关IO编号</param>
        /// <returns>限位开关状态</returns>
        private async Task<bool> CheckLimitSensorAsync(int limitIO)
        {
            string ioNumber = $"X{limitIO:D3}";
            return await DMC1000BIOManager.Instance.ReadInputAsync(ioNumber);
        }
        #endregion

        #region 配置管理方法
        /// <summary>
        /// 从系统配置加载翻转电机配置（仅加载位置信息）
        /// </summary>
        private void LoadFlipMotorConfigFromSystem()
        {
            try
            {
                // 翻转电机参数现在由GlobalMotorParameterManager自动管理
                // 此方法仅加载位置信息
                var motorSettings = Settings.Settings.Current.Motor;

                lock (_motorLock)
                {
                    // 加载左翻转电机位置
                    var leftPositions = new FlipMotorPositions
                    {
                        Position1 = motorSettings.LeftFlipPosition1,
                        Position2 = motorSettings.LeftFlipPosition2,
                        Position3 = motorSettings.LeftFlipPosition3,
                        Position4 = motorSettings.LeftFlipPosition4,
                        Position5 = motorSettings.LeftFlipPosition5
                    };
                    _flipMotorPositions[AXIS_LEFT_FLIP] = leftPositions;
                    LogHelper.Info("从Settings系统加载左翻转电机位置成功");

                    // 加载右翻转电机位置
                    var rightPositions = new FlipMotorPositions
                    {
                        Position1 = motorSettings.RightFlipPosition1,
                        Position2 = motorSettings.RightFlipPosition2,
                        Position3 = motorSettings.RightFlipPosition3,
                        Position4 = motorSettings.RightFlipPosition4,
                        Position5 = motorSettings.RightFlipPosition5
                    };
                    _flipMotorPositions[AXIS_RIGHT_FLIP] = rightPositions;
                    LogHelper.Info("从Settings系统加载右翻转电机位置成功");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("从Settings系统加载翻转电机配置失败", ex);
            }
        }

        /// <summary>
        /// 从系统配置加载皮带电机参数
        /// </summary>
        private void LoadBeltMotorConfigFromSystem()
        {
            try
            {
                // 从新的Settings系统加载参数
                var motorSettings = Settings.Settings.Current.Motor;

                lock (_motorLock)
                {
                    // 加载输入皮带电机参数
                    var inputParams = new BeltMotorParams
                    {
                        MotorName = "输入皮带电机",
                        PulseEquivalent = motorSettings.InputBeltPulseEquivalent,
                        MaxSpeed = motorSettings.InputBeltMaxSpeed,
                        StartSpeed = motorSettings.InputBeltStartSpeed,
                        Acceleration = motorSettings.InputBeltAcceleration,
                        AccelerationTime = motorSettings.InputBeltAccelerationTime
                    };
                    _beltMotorParams[INPUT_BELT_AXIS] = inputParams;
                    LogHelper.Info($"从Settings系统加载输入皮带电机参数成功: 脉冲当量={inputParams.PulseEquivalent}mm/pulse");

                    // 加载输出皮带电机参数
                    var outputParams = new BeltMotorParams
                    {
                        MotorName = "输出皮带电机",
                        PulseEquivalent = motorSettings.OutputBeltPulseEquivalent,
                        MaxSpeed = motorSettings.OutputBeltMaxSpeed,
                        StartSpeed = motorSettings.OutputBeltStartSpeed,
                        Acceleration = motorSettings.OutputBeltAcceleration,
                        AccelerationTime = motorSettings.OutputBeltAccelerationTime
                    };
                    _beltMotorParams[OUTPUT_BELT_AXIS] = outputParams;
                    LogHelper.Info($"从Settings系统加载输出皮带电机参数成功: 脉冲当量={outputParams.PulseEquivalent}mm/pulse");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("从Settings系统加载皮带电机参数失败", ex);
            }
        }

        /// <summary>
        /// 保存翻转电机配置到系统配置（仅保存位置信息）
        /// 翻转电机参数现在由GlobalMotorParameterManager自动管理
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> SaveFlipMotorConfigAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    // 翻转电机参数现在由GlobalMotorParameterManager自动管理
                    // 此方法仅保存位置信息
                    var motorSettings = Settings.Settings.Current.Motor;

                    lock (_motorLock)
                    {
                        // 翻转电机参数由GlobalMotorParameterManager管理，此处不再保存参数

                        // 保存左翻转电机位置
                        if (_flipMotorPositions.ContainsKey(AXIS_LEFT_FLIP))
                        {
                            var leftPositions = _flipMotorPositions[AXIS_LEFT_FLIP];
                            motorSettings.LeftFlipPosition1 = leftPositions.Position1;
                            motorSettings.LeftFlipPosition2 = leftPositions.Position2;
                            motorSettings.LeftFlipPosition3 = leftPositions.Position3;
                            motorSettings.LeftFlipPosition4 = leftPositions.Position4;
                            motorSettings.LeftFlipPosition5 = leftPositions.Position5;
                        }

                        // 保存右翻转电机位置
                        if (_flipMotorPositions.ContainsKey(AXIS_RIGHT_FLIP))
                        {
                            var rightPositions = _flipMotorPositions[AXIS_RIGHT_FLIP];
                            motorSettings.RightFlipPosition1 = rightPositions.Position1;
                            motorSettings.RightFlipPosition2 = rightPositions.Position2;
                            motorSettings.RightFlipPosition3 = rightPositions.Position3;
                            motorSettings.RightFlipPosition4 = rightPositions.Position4;
                            motorSettings.RightFlipPosition5 = rightPositions.Position5;
                        }
                    }

                    // 保存到Settings系统
                    Settings.Settings.Save();
                    LogHelper.Info("翻转电机配置保存到Settings系统成功");
                    return true;
                }
                catch (Exception ex)
                {
                    LogHelper.Error("保存翻转电机配置到Settings系统失败", ex);
                    return false;
                }
            });
        }

        /// <summary>
        /// 保存皮带电机参数到系统配置
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> SaveBeltMotorConfigAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    // 使用新的Settings系统保存参数
                    var motorSettings = Settings.Settings.Current.Motor;

                    lock (_motorLock)
                    {
                        // 保存输入皮带电机参数
                        if (_beltMotorParams.ContainsKey(INPUT_BELT_AXIS))
                        {
                            var inputParams = _beltMotorParams[INPUT_BELT_AXIS];
                            motorSettings.InputBeltPulseEquivalent = inputParams.PulseEquivalent;
                            motorSettings.InputBeltMaxSpeed = inputParams.MaxSpeed;
                            motorSettings.InputBeltStartSpeed = inputParams.StartSpeed;
                            motorSettings.InputBeltAcceleration = inputParams.Acceleration;
                            motorSettings.InputBeltAccelerationTime = inputParams.AccelerationTime;
                        }

                        // 保存输出皮带电机参数
                        if (_beltMotorParams.ContainsKey(OUTPUT_BELT_AXIS))
                        {
                            var outputParams = _beltMotorParams[OUTPUT_BELT_AXIS];
                            motorSettings.OutputBeltPulseEquivalent = outputParams.PulseEquivalent;
                            motorSettings.OutputBeltMaxSpeed = outputParams.MaxSpeed;
                            motorSettings.OutputBeltStartSpeed = outputParams.StartSpeed;
                            motorSettings.OutputBeltAcceleration = outputParams.Acceleration;
                            motorSettings.OutputBeltAccelerationTime = outputParams.AccelerationTime;
                        }
                    }

                    // 保存到Settings系统
                    Settings.Settings.Save();
                    LogHelper.Info("皮带电机配置保存到Settings系统成功");
                    return true;
                }
                catch (Exception ex)
                {
                    LogHelper.Error("保存皮带电机配置到Settings系统失败", ex);
                    return false;
                }
            });
        }

        #endregion
        #endregion
    }
}
