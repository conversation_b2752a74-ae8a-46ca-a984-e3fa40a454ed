using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;
using MyHMI.Helpers;

namespace MyHMI.Testing
{
    /// <summary>
    /// 综合测试执行器
    /// 执行所有重构验证测试并生成详细报告
    /// </summary>
    public class ComprehensiveTestExecutor
    {
        #region 测试结果数据结构

        /// <summary>
        /// 测试结果
        /// </summary>
        public class TestResult
        {
            public string TestName { get; set; }
            public bool Passed { get; set; }
            public TimeSpan ExecutionTime { get; set; }
            public string ErrorMessage { get; set; }
            public DateTime ExecutionDateTime { get; set; }

            public TestResult(string testName)
            {
                TestName = testName;
                ExecutionDateTime = DateTime.Now;
                ErrorMessage = string.Empty;
            }
        }

        /// <summary>
        /// 测试套件结果
        /// </summary>
        public class TestSuiteResult
        {
            public string SuiteName { get; set; }
            public List<TestResult> TestResults { get; set; }
            public TimeSpan TotalExecutionTime { get; set; }
            public int PassedCount => TestResults.FindAll(t => t.Passed).Count;
            public int FailedCount => TestResults.Count - PassedCount;
            public double PassRate => TestResults.Count > 0 ? (double)PassedCount / TestResults.Count * 100 : 0;

            public TestSuiteResult(string suiteName)
            {
                SuiteName = suiteName;
                TestResults = new List<TestResult>();
            }
        }

        #endregion

        #region 私有字段

        private List<TestSuiteResult> _testSuiteResults;
        private Stopwatch _totalStopwatch;

        #endregion

        #region 公共方法

        /// <summary>
        /// 执行所有测试
        /// </summary>
        /// <returns>总体测试结果</returns>
        public async Task<bool> ExecuteAllTestsAsync()
        {
            try
            {
                LogHelper.Info("========== 开始执行综合测试验证 ==========");
                
                _testSuiteResults = new List<TestSuiteResult>();
                _totalStopwatch = Stopwatch.StartNew();

                // 执行BeltMotorAutoModeController测试套件
                var beltMotorSuite = await ExecuteBeltMotorTestSuiteAsync();
                _testSuiteResults.Add(beltMotorSuite);

                // 执行WorkflowManager重构验证测试套件
                var workflowSuite = await ExecuteWorkflowTestSuiteAsync();
                _testSuiteResults.Add(workflowSuite);

                _totalStopwatch.Stop();

                // 生成测试报告
                GenerateTestReport();

                // 计算总体结果
                bool overallResult = CalculateOverallResult();
                
                LogHelper.Info($"========== 综合测试验证完成，总体结果: {(overallResult ? "通过" : "失败")} ==========");
                return overallResult;
            }
            catch (Exception ex)
            {
                LogHelper.Error("执行综合测试时发生异常", ex);
                return false;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 执行BeltMotorAutoModeController测试套件
        /// </summary>
        private async Task<TestSuiteResult> ExecuteBeltMotorTestSuiteAsync()
        {
            var suite = new TestSuiteResult("BeltMotorAutoModeController测试套件");
            var suiteStopwatch = Stopwatch.StartNew();

            LogHelper.Info("--- 开始执行BeltMotorAutoModeController测试套件 ---");

            // 执行完整的BeltMotorAutoModeController测试
            await ExecuteTestAsync(suite, "BeltMotorAutoModeController完整功能测试",
                () => BeltMotorAutoModeControllerTest.RunCompleteTestAsync());

            suiteStopwatch.Stop();
            suite.TotalExecutionTime = suiteStopwatch.Elapsed;

            LogHelper.Info($"--- BeltMotorAutoModeController测试套件完成，通过率: {suite.PassRate:F1}% ---");
            return suite;
        }

        /// <summary>
        /// 执行WorkflowManager重构验证测试套件
        /// </summary>
        private async Task<TestSuiteResult> ExecuteWorkflowTestSuiteAsync()
        {
            var suite = new TestSuiteResult("WorkflowManager重构验证测试套件");
            var suiteStopwatch = Stopwatch.StartNew();

            LogHelper.Info("--- 开始执行WorkflowManager重构验证测试套件 ---");

            // 测试1：WorkflowManager单例模式验证
            await ExecuteTestAsync(suite, "WorkflowManager单例模式验证", 
                () => WorkflowManagerRefactoringTest.TestWorkflowManagerSingleton());

            // 测试2：WorkflowManager初始化验证
            await ExecuteTestAsync(suite, "WorkflowManager初始化验证", 
                () => WorkflowManagerRefactoringTest.TestWorkflowManagerInitialization());

            // 测试3：BeltMotorAutoModeController独立性验证
            await ExecuteTestAsync(suite, "BeltMotorAutoModeController独立性验证", 
                () => WorkflowManagerRefactoringTest.TestBeltMotorAutoModeControllerIndependence());

            // 测试4：工作流启动序列验证
            await ExecuteTestAsync(suite, "工作流启动序列验证", 
                () => WorkflowManagerRefactoringTest.TestWorkflowStartSequence());

            // 测试5：工作流重置功能验证
            await ExecuteTestAsync(suite, "工作流重置功能验证", 
                () => WorkflowManagerRefactoringTest.TestWorkflowReset());

            // 测试6：StartupSelfCheckManager调用关系验证
            await ExecuteTestAsync(suite, "StartupSelfCheckManager调用关系验证", 
                () => WorkflowManagerRefactoringTest.TestStartupSelfCheckManagerIntegration());

            suiteStopwatch.Stop();
            suite.TotalExecutionTime = suiteStopwatch.Elapsed;

            LogHelper.Info($"--- WorkflowManager重构验证测试套件完成，通过率: {suite.PassRate:F1}% ---");
            return suite;
        }

        /// <summary>
        /// 执行单个测试
        /// </summary>
        private async Task ExecuteTestAsync(TestSuiteResult suite, string testName, Func<Task<bool>> testMethod)
        {
            var testResult = new TestResult(testName);
            var stopwatch = Stopwatch.StartNew();

            try
            {
                LogHelper.Info($"执行测试: {testName}");
                testResult.Passed = await testMethod();
                stopwatch.Stop();
                testResult.ExecutionTime = stopwatch.Elapsed;

                LogHelper.Info($"测试结果: {testName} - {(testResult.Passed ? "通过" : "失败")} ({testResult.ExecutionTime.TotalMilliseconds:F0}ms)");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                testResult.Passed = false;
                testResult.ExecutionTime = stopwatch.Elapsed;
                testResult.ErrorMessage = ex.Message;

                LogHelper.Error($"测试异常: {testName} - {ex.Message}");
            }

            suite.TestResults.Add(testResult);
        }

        /// <summary>
        /// 生成测试报告
        /// </summary>
        private void GenerateTestReport()
        {
            LogHelper.Info("========== 综合测试报告 ==========");

            int totalTests = 0;
            int totalPassed = 0;
            int totalFailed = 0;

            foreach (var suite in _testSuiteResults)
            {
                LogHelper.Info($"\n【{suite.SuiteName}】");
                LogHelper.Info($"执行时间: {suite.TotalExecutionTime.TotalSeconds:F2}秒");
                LogHelper.Info($"测试数量: {suite.TestResults.Count}");
                LogHelper.Info($"通过数量: {suite.PassedCount}");
                LogHelper.Info($"失败数量: {suite.FailedCount}");
                LogHelper.Info($"通过率: {suite.PassRate:F1}%");

                // 显示失败的测试详情
                foreach (var test in suite.TestResults)
                {
                    if (!test.Passed)
                    {
                        LogHelper.Warning($"  失败测试: {test.TestName}");
                        if (!string.IsNullOrEmpty(test.ErrorMessage))
                        {
                            LogHelper.Warning($"    错误信息: {test.ErrorMessage}");
                        }
                    }
                }

                totalTests += suite.TestResults.Count;
                totalPassed += suite.PassedCount;
                totalFailed += suite.FailedCount;
            }

            // 总体统计
            double overallPassRate = totalTests > 0 ? (double)totalPassed / totalTests * 100 : 0;

            LogHelper.Info("\n【总体统计】");
            LogHelper.Info($"总执行时间: {_totalStopwatch.Elapsed.TotalSeconds:F2}秒");
            LogHelper.Info($"总测试数量: {totalTests}");
            LogHelper.Info($"总通过数量: {totalPassed}");
            LogHelper.Info($"总失败数量: {totalFailed}");
            LogHelper.Info($"总通过率: {overallPassRate:F1}%");

            // 测试质量评估
            string qualityAssessment = GetQualityAssessment(overallPassRate);
            LogHelper.Info($"质量评估: {qualityAssessment}");

            LogHelper.Info("========== 测试报告结束 ==========");
        }

        /// <summary>
        /// 计算总体结果
        /// </summary>
        private bool CalculateOverallResult()
        {
            foreach (var suite in _testSuiteResults)
            {
                if (suite.FailedCount > 0)
                {
                    // 如果有任何测试失败，检查是否是可接受的失败
                    foreach (var test in suite.TestResults)
                    {
                        if (!test.Passed && !IsAcceptableFailure(test.TestName))
                        {
                            return false;
                        }
                    }
                }
            }
            return true;
        }

        /// <summary>
        /// 判断是否是可接受的失败（例如硬件相关的测试在脱机模式下）
        /// </summary>
        private bool IsAcceptableFailure(string testName)
        {
            // StartupSelfCheckManager相关测试在脱机模式下可能失败，但这是可接受的
            return testName.Contains("StartupSelfCheckManager") || 
                   testName.Contains("手动皮带控制");
        }

        /// <summary>
        /// 获取质量评估
        /// </summary>
        private string GetQualityAssessment(double passRate)
        {
            if (passRate >= 95) return "优秀 - 重构质量很高";
            if (passRate >= 85) return "良好 - 重构质量较好";
            if (passRate >= 70) return "一般 - 需要进一步改进";
            return "较差 - 需要重点关注和修复";
        }

        #endregion
    }
}
