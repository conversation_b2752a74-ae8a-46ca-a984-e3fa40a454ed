using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MyHMI.Helpers;
using MyHMI.Events;

using MyHMI.Models;

namespace MyHMI.Managers
{
    /// <summary>
    /// 系统运行模式枚举
    /// </summary>
    public enum SystemMode
    {
        /// <summary>
        /// 非自动化模式（手动操作模式）
        /// </summary>
        Manual = 0,

        /// <summary>
        /// 自动化模式（全自动业务逻辑模式）
        /// </summary>
        Automatic = 1
    }

    /// <summary>
    /// 系统运行模式管理器
    /// 负责管理系统在非自动化模式（手动操作）和自动化模式（全自动业务逻辑）之间的切换
    /// 实现单例模式，确保全局统一的模式管理
    /// </summary>
    public class SystemModeManager
    {
        #region 单例模式
        private static readonly Lazy<SystemModeManager> _instance = new Lazy<SystemModeManager>(() => new SystemModeManager());
        public static SystemModeManager Instance => _instance.Value;
        private SystemModeManager()
        {
            _currentMode = SystemMode.Manual; // 默认为手动模式

            // 初始化安全管理器
            _safetyManager = SafetyManager.Instance;

            // 程序启动时自动恢复上次的系统模式
            _ = Task.Run(async () => await RestoreLastSystemModeAsync());
        }
        #endregion

        #region 事件定义
        /// <summary>
        /// 系统模式变化事件
        /// </summary>
        public event EventHandler<SystemModeChangedEventArgs> ModeChanged;

        /// <summary>
        /// 自动化流程开始事件
        /// </summary>
        public event EventHandler<AutomationStartedEventArgs> AutomationStarted;

        /// <summary>
        /// 自动化流程完成事件
        /// </summary>
        public event EventHandler<AutomationCompletedEventArgs> AutomationCompleted;

        /// <summary>
        /// 自动化流程错误事件
        /// </summary>
        public event EventHandler<AutomationErrorEventArgs> AutomationError;
        #endregion

        #region 私有字段
        private SystemMode _currentMode;
        private readonly object _modeLock = new object();
        private bool _isTransitioning = false;
        private CancellationTokenSource _automationCancellationTokenSource;
        private Task _automationTask;

        // 安全管理器引用
        private SafetyManager _safetyManager;
        #endregion

        #region 公共属性
        /// <summary>
        /// 当前系统运行模式
        /// </summary>
        public SystemMode CurrentMode
        {
            get
            {
                lock (_modeLock)
                {
                    return _currentMode;
                }
            }
        }

        /// <summary>
        /// 是否正在模式切换中
        /// </summary>
        public bool IsTransitioning
        {
            get
            {
                lock (_modeLock)
                {
                    return _isTransitioning;
                }
            }
        }

        /// <summary>
        /// 是否为手动模式
        /// </summary>
        public bool IsManualMode => CurrentMode == SystemMode.Manual;

        /// <summary>
        /// 是否为自动模式
        /// </summary>
        public bool IsAutomaticMode => CurrentMode == SystemMode.Automatic;

        /// <summary>
        /// 自动化流程是否正在运行
        /// </summary>
        public bool IsAutomationRunning => _automationTask != null && !_automationTask.IsCompleted;
        #endregion

        #region 模式切换方法
        /// <summary>
        /// 切换到手动模式
        /// </summary>
        /// <returns>切换结果</returns>
        public async Task<bool> SwitchToManualModeAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync<bool>(async () =>
            {
                lock (_modeLock)
                {
                    if (_currentMode == SystemMode.Manual)
                    {
                        LogHelper.Info("系统已经处于手动模式");
                        return true;
                    }

                    if (_isTransitioning)
                    {
                        LogHelper.Warning("系统正在模式切换中，无法切换到手动模式");
                        return false;
                    }

                    _isTransitioning = true;
                }

                try
                {
                    LogHelper.Info("开始切换到手动模式...");

                    SystemMode previousMode = _currentMode;

                    // 停止当前运行的流程
                    if (IsAutomationRunning)
                    {
                        LogHelper.Info("停止正在运行的自动化流程...");
                        await StopAutomationAsync();
                    }



                    // 停止安全管理器
                    if (_safetyManager != null && _safetyManager.IsRunning)
                    {
                        LogHelper.Info("停止安全管理器...");
                        await _safetyManager.StopAsync();
                    }

                    // 切换模式
                    lock (_modeLock)
                    {
                        _currentMode = SystemMode.Manual;
                        _isTransitioning = false;
                    }

                    // 触发模式变化事件
                    ModeChanged?.Invoke(this, new SystemModeChangedEventArgs(previousMode, SystemMode.Manual));

                    // 保存当前模式到配置文件
                    _ = Task.Run(async () => await SaveCurrentSystemModeAsync());

                    LogHelper.Info("成功切换到手动模式");
                    return true;
                }
                catch (Exception ex)
                {
                    lock (_modeLock)
                    {
                        _isTransitioning = false;
                    }
                    LogHelper.Error("切换到手动模式失败", ex);
                    return false;
                }
            }, false, "切换到手动模式");
        }

        /// <summary>
        /// 切换到自动模式并启动自动化流程
        /// </summary>
        /// <returns>切换结果</returns>
        public async Task<bool> SwitchToAutomaticModeAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync<bool>(async () =>
            {
                lock (_modeLock)
                {
                    if (_currentMode == SystemMode.Automatic)
                    {
                        LogHelper.Info("系统已经处于自动模式");
                        return true;
                    }

                    if (_isTransitioning)
                    {
                        LogHelper.Warning("系统正在模式切换中，无法切换到自动模式");
                        return false;
                    }

                    _isTransitioning = true;
                }

                try
                {
                    LogHelper.Info("开始切换到自动模式...");

                    // 检查系统是否准备好进入自动模式
                    bool systemReady = await CheckSystemReadyForAutomationAsync();
                    if (!systemReady)
                    {
                        LogHelper.Error("系统未准备好进入自动模式");
                        lock (_modeLock)
                        {
                            _isTransitioning = false;
                        }
                        return false;
                    }

                    // 切换模式
                    lock (_modeLock)
                    {
                        _currentMode = SystemMode.Automatic;
                        _isTransitioning = false;
                    }

                    // 触发模式变化事件
                    ModeChanged?.Invoke(this, new SystemModeChangedEventArgs(SystemMode.Manual, SystemMode.Automatic));

                    // 保存当前模式到配置文件
                    _ = Task.Run(async () => await SaveCurrentSystemModeAsync());

                    // 初始化并启动安全管理器
                    if (_safetyManager != null)
                    {
                        // 先确保安全管理器已初始化
                        if (!_safetyManager.IsInitialized)
                        {
                            LogHelper.Info("初始化安全管理器...");
                            bool safetyInitResult = await _safetyManager.InitializeAsync();
                            if (!safetyInitResult)
                            {
                                LogHelper.Warning("安全管理器初始化失败，可能是硬件依赖不可用，尝试降级运行模式");
                                // 不立即失败，继续尝试启动（降级模式）
                            }
                        }

                        // 启动安全管理器
                        if (!_safetyManager.IsRunning)
                        {
                            LogHelper.Info("启动安全管理器...");
                            bool safetyStartResult = await _safetyManager.StartAsync();
                            if (!safetyStartResult)
                            {
                                LogHelper.Warning("启动安全管理器失败，系统将在降级模式下运行");
                                // 不再回退到手动模式，允许降级运行
                                // 但记录警告信息供用户参考
                            }

                            // 更新初始化状态管理器
                            InitializationStatusManager.Instance.UpdateModuleStatus("SafetyManager",
                                _safetyManager.IsInitialized, _safetyManager.IsRunning,
                                _safetyManager.IsInitialized ? null : "安全管理器初始化失败");
                        }
                    }

                    // 启动自动化流程（包含开机自检）
                    await StartAutomationInternalAsync();

                    LogHelper.Info("成功切换到自动模式并启动自动化流程");
                    return true;
                }
                catch (Exception ex)
                {
                    lock (_modeLock)
                    {
                        _isTransitioning = false;
                        _currentMode = SystemMode.Manual; // 出错时回退到手动模式
                    }

                    // 提供详细的错误信息和用户建议
                    string detailedError = GenerateDetailedErrorMessage("切换到自动模式失败", ex);
                    LogHelper.Error(detailedError, ex);

                    // 触发错误事件，让UI能够显示详细信息
                    AutomationError?.Invoke(this, new AutomationErrorEventArgs(ex, detailedError));

                    return false;
                }
            }, false, "切换到自动模式");
        }


        #endregion

        #region 自动化流程控制
        /// <summary>
        /// 启动自动化流程（公共方法）
        /// 只能在自动模式下调用
        /// </summary>
        /// <returns>启动结果</returns>
        public async Task<bool> StartAutomationAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync<bool>(async () =>
            {
                if (_currentMode != SystemMode.Automatic)
                {
                    LogHelper.Warning("只能在自动模式下启动自动化流程");
                    return false;
                }

                if (IsAutomationRunning)
                {
                    LogHelper.Info("自动化流程已经在运行中");
                    return true;
                }

                return await StartAutomationInternalAsync();
            }, false, "启动自动化流程");
        }

        /// <summary>
        /// 启动自动化流程（内部方法）
        /// 包含开机自检流程
        /// </summary>
        /// <returns>启动结果</returns>
        private async Task<bool> StartAutomationInternalAsync()
        {
            try
            {
                // 取消之前的自动化任务
                _automationCancellationTokenSource?.Cancel();
                _automationCancellationTokenSource = new CancellationTokenSource();

                // 执行开机自检流程
                LogHelper.Info("开始执行开机自检流程...");
                var selfCheckResult = await StartupSelfCheckManager.Instance.ExecuteStartupSelfCheckAsync();
                if (!selfCheckResult.OverallSuccess)
                {
                    LogHelper.Warning("开机自检发现一些问题，但系统可以在脱机测试模式下运行");
                    // 不阻止自动化流程启动，允许脱机测试
                }

                // 触发自动化开始事件
                AutomationStarted?.Invoke(this, new AutomationStartedEventArgs());

                // 启动自动化任务
                _automationTask = Task.Run(async () => await ExecuteAutomationWorkflowAsync(_automationCancellationTokenSource.Token));

                LogHelper.Info("开机自检完成，自动化流程已启动");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("启动自动化流程失败", ex);
                AutomationError?.Invoke(this, new AutomationErrorEventArgs(ex));
                return false;
            }
        }

        /// <summary>
        /// 停止自动化流程
        /// </summary>
        /// <returns>停止结果</returns>
        private async Task<bool> StopAutomationAsync()
        {
            try
            {
                if (_automationCancellationTokenSource != null && !_automationCancellationTokenSource.Token.IsCancellationRequested)
                {
                    _automationCancellationTokenSource.Cancel();
                }

                if (_automationTask != null && !_automationTask.IsCompleted)
                {
                    await _automationTask;
                }

                LogHelper.Info("自动化流程已停止");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("停止自动化流程失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 执行自动化工作流程
        /// 集成WorkflowManager进行真正的工作流协调
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        private async Task ExecuteAutomationWorkflowAsync(CancellationToken cancellationToken)
        {
            try
            {
                LogHelper.Info("开始执行自动化工作流程...");

                // 1. 初始化WorkflowManager
                var workflowManager = WorkflowManager.Instance;
                if (!workflowManager.IsInitialized)
                {
                    LogHelper.Info("初始化WorkflowManager...");
                    bool initResult = await workflowManager.InitializeAsync();
                    if (!initResult)
                    {
                        LogHelper.Error("WorkflowManager初始化失败");
                        throw new InvalidOperationException("WorkflowManager初始化失败");
                    }
                }

                // 2. 启动工作流
                LogHelper.Info("启动工作流管理器...");
                bool startResult = await workflowManager.StartWorkflowAsync();
                if (!startResult)
                {
                    LogHelper.Warning("工作流启动失败，可能是硬件未连接，但系统可以在脱机模式下运行");
                }

                // 3. 监控工作流状态，直到取消或完成
                while (!cancellationToken.IsCancellationRequested)
                {
                    // 检查工作流状态
                    var currentState = workflowManager.CurrentState;
                    LogHelper.Debug($"当前工作流状态: {currentState}");

                    // 如果工作流出错，记录并继续监控
                    if (currentState == WorkflowState.Error)
                    {
                        LogHelper.Warning("工作流处于错误状态，尝试重置...");
                        await workflowManager.ResetWorkflowAsync();
                    }

                    // 等待一段时间再检查
                    await Task.Delay(1000, cancellationToken);
                }

                // 4. 停止工作流
                LogHelper.Info("停止工作流管理器...");
                await workflowManager.StopWorkflowAsync();

                // 触发自动化完成事件
                AutomationCompleted?.Invoke(this, new AutomationCompletedEventArgs(true, "自动化流程执行完成"));

                LogHelper.Info("自动化工作流程执行完成");
            }
            catch (OperationCanceledException)
            {
                LogHelper.Info("自动化工作流程被取消");

                // 确保停止工作流
                try
                {
                    await WorkflowManager.Instance.StopWorkflowAsync();
                }
                catch (Exception ex)
                {
                    LogHelper.Error("停止工作流时发生异常", ex);
                }

                AutomationCompleted?.Invoke(this, new AutomationCompletedEventArgs(false, "自动化流程被取消"));
            }
            catch (Exception ex)
            {
                LogHelper.Error("自动化工作流程执行异常", ex);

                // 确保停止工作流
                try
                {
                    await WorkflowManager.Instance.StopWorkflowAsync();
                }
                catch (Exception stopEx)
                {
                    LogHelper.Error("停止工作流时发生异常", stopEx);
                }

                AutomationError?.Invoke(this, new AutomationErrorEventArgs(ex));
                AutomationCompleted?.Invoke(this, new AutomationCompletedEventArgs(false, $"自动化流程执行异常: {ex.Message}"));
            }
        }



        /// <summary>
        /// 检查系统是否准备好进入自动模式
        /// 使用分层初始化检查，支持降级运行模式
        /// </summary>
        /// <returns>是否准备好</returns>
        private async Task<bool> CheckSystemReadyForAutomationAsync()
        {
            try
            {
                LogHelper.Info("开始检查系统是否准备好进入自动模式...");

                // 使用初始化状态管理器进行全面检查
                var report = await InitializationStatusManager.Instance.CheckAllModulesAsync();

                // 记录详细的检查结果
                LogHelper.Info($"系统初始化检查完成 - 推荐模式: {report.RecommendedMode}");

                // 记录关键模块状态
                foreach (var module in report.CriticalModules)
                {
                    if (module.IsInitialized)
                    {
                        LogHelper.Info($"✅ 关键模块 {module.Description} 状态正常");
                    }
                    else
                    {
                        LogHelper.Error($"❌ 关键模块 {module.Description} 初始化失败: {module.ErrorMessage}");
                    }
                }

                // 记录重要模块状态
                foreach (var module in report.ImportantModules)
                {
                    if (module.IsInitialized)
                    {
                        LogHelper.Info($"✅ 重要模块 {module.Description} 状态正常");
                    }
                    else
                    {
                        LogHelper.Warning($"⚠️ 重要模块 {module.Description} 初始化失败: {module.ErrorMessage}");
                    }
                }

                // 记录可选模块状态
                foreach (var module in report.OptionalModules)
                {
                    if (module.IsInitialized)
                    {
                        LogHelper.Info($"✅ 可选模块 {module.Description} 状态正常");
                    }
                    else
                    {
                        LogHelper.Info($"ℹ️ 可选模块 {module.Description} 不可用: {module.ErrorMessage}");
                    }
                }

                // 记录用户操作建议
                foreach (var action in report.UserActions)
                {
                    LogHelper.Info($"💡 {action}");
                }

                // 根据检查结果决定是否可以切换到自动模式
                if (report.CanSwitchToAutomatic)
                {
                    LogHelper.Info($"系统检查完成，可以切换到自动模式（运行模式: {report.RecommendedMode}）");
                    return true;
                }
                else
                {
                    LogHelper.Warning("系统检查发现关键问题，无法切换到自动模式");

                    // 生成详细的错误信息
                    string detailedError = GenerateSystemCheckErrorMessage(report);
                    throw new InvalidOperationException(detailedError);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("系统准备检查失败", ex);
                return false;
            }
        }


        #endregion

        #region 状态查询方法
        /// <summary>
        /// 获取系统模式状态信息
        /// </summary>
        /// <returns>状态信息</returns>
        public (SystemMode CurrentMode, bool IsTransitioning, bool IsAutomationRunning) GetModeStatus()
        {
            lock (_modeLock)
            {
                return (_currentMode, _isTransitioning, IsAutomationRunning);
            }
        }
        #endregion

        #region 模式记忆功能
        /// <summary>
        /// 恢复上次退出时的系统模式
        /// </summary>
        /// <returns>恢复结果</returns>
        private async Task<bool> RestoreLastSystemModeAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync<bool>(async () =>
            {
                try
                {
                    LogHelper.Info("开始恢复上次退出时的系统模式...");

                    var systemConfig = Settings.Settings.Current;
                    if (systemConfig?.System?.LastSystemMode == null)
                    {
                        LogHelper.Info("配置中未找到上次系统模式，保持默认手动模式");
                        return true;
                    }

                    string lastModeString = systemConfig.System.LastSystemMode;
                    SystemMode lastMode;

                    if (!Enum.TryParse<SystemMode>(lastModeString, out lastMode))
                    {
                        LogHelper.Warning($"无效的系统模式配置: {lastModeString}，保持默认手动模式");
                        return true;
                    }

                    if (lastMode == _currentMode)
                    {
                        LogHelper.Info($"当前模式已经是 {lastMode}，无需切换");
                        return true;
                    }

                    LogHelper.Info($"恢复上次系统模式: {lastMode}");

                    // 根据上次模式进行切换
                    bool switchResult = false;
                    if (lastMode == SystemMode.Automatic)
                    {
                        switchResult = await SwitchToAutomaticModeAsync();
                    }
                    else
                    {
                        switchResult = await SwitchToManualModeAsync();
                    }

                    if (switchResult)
                    {
                        LogHelper.Info($"成功恢复到上次系统模式: {lastMode}");
                    }
                    else
                    {
                        LogHelper.Error($"恢复上次系统模式失败: {lastMode}，保持当前模式");
                    }

                    return switchResult;
                }
                catch (Exception ex)
                {
                    LogHelper.Error("恢复上次系统模式时发生异常", ex);
                    return false;
                }
            }, false, "恢复上次系统模式");
        }

        /// <summary>
        /// 保存当前系统模式到配置文件
        /// </summary>
        /// <returns>保存结果</returns>
        private async Task<bool> SaveCurrentSystemModeAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    var systemConfig = Settings.Settings.Current;
                    if (systemConfig?.System == null)
                    {
                        LogHelper.Error("系统配置对象为空，无法保存系统模式");
                        return false;
                    }

                    string currentModeString = _currentMode.ToString();
                    systemConfig.System.LastSystemMode = currentModeString;

                    Settings.Settings.Save();
                    bool saveResult = true;
                    if (saveResult)
                    {
                        LogHelper.Info($"系统模式已保存到配置文件: {currentModeString}");
                    }
                    else
                    {
                        LogHelper.Error($"保存系统模式到配置文件失败: {currentModeString}");
                    }

                    return saveResult;
                }
                catch (Exception ex)
                {
                    LogHelper.Error("保存系统模式到配置文件时发生异常", ex);
                    return false;
                }
            });
        }
        #endregion

        #region 安全管理器相关方法
        /// <summary>
        /// 获取安全管理器是否正在运行
        /// </summary>
        public bool IsSafetyManagerRunning
        {
            get { return _safetyManager?.IsRunning ?? false; }
        }

        /// <summary>
        /// 获取当前安全状态
        /// </summary>
        /// <returns>安全状态</returns>
        public SafetyState GetCurrentSafetyState()
        {
            return _safetyManager?.CurrentSafetyState ?? SafetyState.SystemError;
        }

        /// <summary>
        /// 获取系统是否处于安全状态
        /// </summary>
        /// <returns>是否安全</returns>
        public bool IsSystemSafe()
        {
            return _safetyManager?.IsSystemSafe ?? false;
        }

        /// <summary>
        /// 手动启动安全管理器（仅在手动模式下可用）
        /// </summary>
        /// <returns>启动结果</returns>
        public async Task<bool> StartSafetyManagerManuallyAsync()
        {
            if (_currentMode != SystemMode.Manual)
            {
                LogHelper.Warning("只能在手动模式下手动启动安全管理器");
                return false;
            }

            if (_safetyManager == null)
            {
                LogHelper.Error("安全管理器未初始化");
                return false;
            }

            if (_safetyManager.IsRunning)
            {
                LogHelper.Info("安全管理器已在运行");
                return true;
            }

            LogHelper.Info("手动启动安全管理器...");
            return await _safetyManager.StartAsync();
        }

        /// <summary>
        /// 手动停止安全管理器（仅在手动模式下可用）
        /// </summary>
        /// <returns>停止结果</returns>
        public async Task<bool> StopSafetyManagerManuallyAsync()
        {
            if (_currentMode != SystemMode.Manual)
            {
                LogHelper.Warning("只能在手动模式下手动停止安全管理器");
                return false;
            }

            if (_safetyManager == null)
            {
                LogHelper.Error("安全管理器未初始化");
                return false;
            }

            if (!_safetyManager.IsRunning)
            {
                LogHelper.Info("安全管理器已停止");
                return true;
            }

            LogHelper.Info("手动停止安全管理器...");
            return await _safetyManager.StopAsync();
        }

        /// <summary>
        /// 获取安全管理器实例（用于高级操作）
        /// </summary>
        /// <returns>安全管理器实例</returns>
        public SafetyManager GetSafetyManager()
        {
            return _safetyManager;
        }

        /// <summary>
        /// 切换到手动模式（别名方法）
        /// </summary>
        /// <returns>切换结果</returns>
        public async Task<bool> SwitchToManualAsync()
        {
            return await SwitchToManualModeAsync();
        }

        /// <summary>
        /// 切换到自动模式（别名方法）
        /// </summary>
        /// <returns>切换结果</returns>
        public async Task<bool> SwitchToAutomaticAsync()
        {
            return await SwitchToAutomaticModeAsync();
        }
        #endregion

        #region 私有辅助方法
        /// <summary>
        /// 生成详细的错误信息和用户建议
        /// </summary>
        private string GenerateDetailedErrorMessage(string baseMessage, Exception ex)
        {
            var errorDetails = new List<string>();
            errorDetails.Add($"错误描述: {baseMessage}");
            errorDetails.Add($"异常类型: {ex.GetType().Name}");
            errorDetails.Add($"异常信息: {ex.Message}");

            // 根据异常类型提供具体建议
            if (ex.Message.Contains("DMC1000B"))
            {
                errorDetails.Add("💡 建议: 请检查DMC1000B控制卡连接");
                errorDetails.Add("🔧 解决方案: 1) 检查USB连接 2) 重启控制卡 3) 重新安装驱动");
            }
            else if (ex.Message.Contains("IO管理器"))
            {
                errorDetails.Add("💡 建议: IO管理器初始化失败，请检查硬件连接");
                errorDetails.Add("🔧 解决方案: 1) 检查控制卡状态 2) 重启程序 3) 联系技术支持");
            }
            else if (ex.Message.Contains("安全管理器"))
            {
                errorDetails.Add("💡 建议: 安全管理器启动失败，系统将在降级模式下运行");
                errorDetails.Add("🔧 解决方案: 1) 检查IO设备连接 2) 重启程序 3) 可继续使用基础功能");
            }
            else
            {
                errorDetails.Add("💡 建议: 请检查系统配置和硬件连接");
                errorDetails.Add("🔧 解决方案: 1) 重启程序 2) 检查日志文件 3) 联系技术支持");
            }

            errorDetails.Add("📋 当前状态: 系统已回退到手动模式，可继续使用手动功能");

            return string.Join("\n", errorDetails);
        }

        /// <summary>
        /// 生成系统检查错误信息
        /// </summary>
        private string GenerateSystemCheckErrorMessage(InitializationReport report)
        {
            var errorDetails = new List<string>();
            errorDetails.Add("🚫 系统模式切换失败");
            errorDetails.Add("");

            // 关键模块问题
            var failedCritical = report.CriticalModules.Where(m => !m.IsInitialized).ToList();
            if (failedCritical.Any())
            {
                errorDetails.Add("❌ 关键模块故障（必须修复）:");
                foreach (var module in failedCritical)
                {
                    errorDetails.Add($"   • {module.Description}: {module.ErrorMessage ?? "初始化失败"}");
                }
                errorDetails.Add("");
            }

            // 重要模块问题
            var failedImportant = report.ImportantModules.Where(m => !m.IsInitialized).ToList();
            if (failedImportant.Any())
            {
                errorDetails.Add("⚠️ 重要模块故障（建议修复）:");
                foreach (var module in failedImportant)
                {
                    errorDetails.Add($"   • {module.Description}: {module.ErrorMessage ?? "初始化失败"}");
                }
                errorDetails.Add("");
            }

            // 用户操作建议
            if (report.UserActions.Any())
            {
                errorDetails.Add("🔧 解决建议:");
                foreach (var action in report.UserActions)
                {
                    errorDetails.Add($"   • {action}");
                }
                errorDetails.Add("");
            }

            errorDetails.Add($"📊 当前系统状态: 仅支持{report.RecommendedMode}模式运行");
            errorDetails.Add("📋 系统已保持在手动模式，可继续使用手动功能");

            return string.Join("\n", errorDetails);
        }
        #endregion
    }
}
