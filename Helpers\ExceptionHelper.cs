using System;
using System.Threading.Tasks;

namespace MyHMI.Helpers
{
    /// <summary>
    /// 异常处理辅助类
    /// 提供统一的异常处理和重试机制
    /// </summary>
    public static class ExceptionHelper
    {
        /// <summary>
        /// 安全执行操作，捕获并记录异常
        /// </summary>
        /// <param name="action">要执行的操作</param>
        /// <param name="operationName">操作名称（用于日志）</param>
        /// <param name="showMessageBox">是否显示错误消息框</param>
        /// <returns>是否执行成功</returns>
        public static bool SafeExecute(Action action, string operationName = "未知操作", bool showMessageBox = false)
        {
            try
            {
                action?.Invoke();
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"{operationName} 执行失败", ex);
                
                if (showMessageBox)
                {
                    UIHelper.ShowMessageBox($"{operationName} 执行失败：\n{ex.Message}", 
                        "错误", System.Windows.Forms.MessageBoxButtons.OK, 
                        System.Windows.Forms.MessageBoxIcon.Error);
                }
                
                return false;
            }
        }

        /// <summary>
        /// 安全执行操作，捕获并记录异常（带返回值）
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="func">要执行的函数</param>
        /// <param name="defaultValue">异常时的默认返回值</param>
        /// <param name="operationName">操作名称（用于日志）</param>
        /// <param name="showMessageBox">是否显示错误消息框</param>
        /// <returns>函数返回值或默认值</returns>
        public static T SafeExecute<T>(Func<T> func, T defaultValue = default(T), 
            string operationName = "未知操作", bool showMessageBox = false)
        {
            try
            {
                return func != null ? func() : defaultValue;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"{operationName} 执行失败", ex);
                
                if (showMessageBox)
                {
                    UIHelper.ShowMessageBox($"{operationName} 执行失败：\n{ex.Message}", 
                        "错误", System.Windows.Forms.MessageBoxButtons.OK, 
                        System.Windows.Forms.MessageBoxIcon.Error);
                }
                
                return defaultValue;
            }
        }

        /// <summary>
        /// 异步安全执行操作
        /// </summary>
        /// <param name="asyncAction">要执行的异步操作</param>
        /// <param name="operationName">操作名称（用于日志）</param>
        /// <param name="showMessageBox">是否显示错误消息框</param>
        /// <returns>是否执行成功</returns>
        public static async Task<bool> SafeExecuteAsync(Func<Task> asyncAction, 
            string operationName = "未知异步操作", bool showMessageBox = false)
        {
            try
            {
                if (asyncAction != null)
                {
                    await asyncAction();
                }
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"{operationName} 执行失败", ex);
                
                if (showMessageBox)
                {
                    UIHelper.ShowMessageBox($"{operationName} 执行失败：\n{ex.Message}", 
                        "错误", System.Windows.Forms.MessageBoxButtons.OK, 
                        System.Windows.Forms.MessageBoxIcon.Error);
                }
                
                return false;
            }
        }

        /// <summary>
        /// 异步安全执行操作（带返回值）
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="asyncFunc">要执行的异步函数</param>
        /// <param name="defaultValue">异常时的默认返回值</param>
        /// <param name="operationName">操作名称（用于日志）</param>
        /// <param name="showMessageBox">是否显示错误消息框</param>
        /// <returns>函数返回值或默认值</returns>
        public static async Task<T> SafeExecuteAsync<T>(Func<Task<T>> asyncFunc, T defaultValue = default(T), 
            string operationName = "未知异步操作", bool showMessageBox = false)
        {
            try
            {
                return asyncFunc != null ? await asyncFunc() : defaultValue;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"{operationName} 执行失败", ex);
                
                if (showMessageBox)
                {
                    UIHelper.ShowMessageBox($"{operationName} 执行失败：\n{ex.Message}", 
                        "错误", System.Windows.Forms.MessageBoxButtons.OK, 
                        System.Windows.Forms.MessageBoxIcon.Error);
                }
                
                return defaultValue;
            }
        }

        /// <summary>
        /// 带重试机制的安全执行
        /// </summary>
        /// <param name="action">要执行的操作</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <param name="retryDelayMs">重试间隔（毫秒）</param>
        /// <param name="operationName">操作名称（用于日志）</param>
        /// <returns>是否执行成功</returns>
        public static bool SafeExecuteWithRetry(Action action, int maxRetries = 3, 
            int retryDelayMs = 1000, string operationName = "未知操作")
        {
            int attempts = 0;
            
            while (attempts <= maxRetries)
            {
                try
                {
                    action?.Invoke();
                    
                    if (attempts > 0)
                    {
                        LogHelper.Info($"{operationName} 在第 {attempts + 1} 次尝试后成功");
                    }
                    
                    return true;
                }
                catch (Exception ex)
                {
                    attempts++;
                    
                    if (attempts <= maxRetries)
                    {
                        LogHelper.Warning($"{operationName} 第 {attempts} 次尝试失败，{retryDelayMs}ms后重试: {ex.Message}");
                        System.Threading.Thread.Sleep(retryDelayMs);
                    }
                    else
                    {
                        LogHelper.Error($"{operationName} 在 {maxRetries + 1} 次尝试后最终失败", ex);
                    }
                }
            }
            
            return false;
        }

        /// <summary>
        /// 带重试机制的异步安全执行
        /// </summary>
        /// <param name="asyncAction">要执行的异步操作</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <param name="retryDelayMs">重试间隔（毫秒）</param>
        /// <param name="operationName">操作名称（用于日志）</param>
        /// <returns>是否执行成功</returns>
        public static async Task<bool> SafeExecuteWithRetryAsync(Func<Task> asyncAction, 
            int maxRetries = 3, int retryDelayMs = 1000, string operationName = "未知异步操作")
        {
            int attempts = 0;
            
            while (attempts <= maxRetries)
            {
                try
                {
                    if (asyncAction != null)
                    {
                        await asyncAction();
                    }
                    
                    if (attempts > 0)
                    {
                        LogHelper.Info($"{operationName} 在第 {attempts + 1} 次尝试后成功");
                    }
                    
                    return true;
                }
                catch (Exception ex)
                {
                    attempts++;
                    
                    if (attempts <= maxRetries)
                    {
                        LogHelper.Warning($"{operationName} 第 {attempts} 次尝试失败，{retryDelayMs}ms后重试: {ex.Message}");
                        await Task.Delay(retryDelayMs);
                    }
                    else
                    {
                        LogHelper.Error($"{operationName} 在 {maxRetries + 1} 次尝试后最终失败", ex);
                    }
                }
            }
            
            return false;
        }
    }
}
