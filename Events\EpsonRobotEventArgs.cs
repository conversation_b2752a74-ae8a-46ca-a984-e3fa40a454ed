using System;
using MyHMI.Models;

namespace MyHMI.Events
{
    /// <summary>
    /// Epson机器人连接状态变化事件参数
    /// </summary>
    public class EpsonRobotConnectionStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 连接类型（启动/停止连接 或 数据收发连接）
        /// </summary>
        public string ConnectionType { get; set; }

        /// <summary>
        /// 连接状态
        /// </summary>
        public CommunicationStatus Status { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string StatusDescription { get; set; }

        /// <summary>
        /// 描述（兼容性属性）
        /// </summary>
        public string Description => StatusDescription;

        /// <summary>
        /// 错误信息（如果有）
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="connectionType">连接类型</param>
        /// <param name="status">连接状态</param>
        /// <param name="statusDescription">状态描述</param>
        /// <param name="errorMessage">错误信息</param>
        public EpsonRobotConnectionStatusChangedEventArgs(string connectionType, CommunicationStatus status, 
            string statusDescription = "", string errorMessage = "")
        {
            ConnectionType = connectionType ?? string.Empty;
            Status = status;
            StatusDescription = statusDescription ?? string.Empty;
            ErrorMessage = errorMessage ?? string.Empty;
            EventTime = DateTime.Now;
        }
    }

    /// <summary>
    /// Epson机器人响应接收事件参数
    /// </summary>
    public class EpsonRobotResponseReceivedEventArgs : EventArgs
    {
        /// <summary>
        /// 机器人响应
        /// </summary>
        public EpsonRobotResponse Response { get; set; }

        /// <summary>
        /// 连接类型（启动/停止连接 或 数据收发连接）
        /// </summary>
        public string ConnectionType { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="response">机器人响应</param>
        /// <param name="connectionType">连接类型</param>
        public EpsonRobotResponseReceivedEventArgs(EpsonRobotResponse response, string connectionType = "")
        {
            Response = response;
            ConnectionType = connectionType ?? string.Empty;
            EventTime = DateTime.Now;
        }
    }

    /// <summary>
    /// Epson机器人命令发送事件参数
    /// </summary>
    public class EpsonRobotCommandSentEventArgs : EventArgs
    {
        /// <summary>
        /// 发送的命令
        /// </summary>
        public EpsonRobotCommand Command { get; set; }

        /// <summary>
        /// 连接类型（启动/停止连接 或 数据收发连接）
        /// </summary>
        public string ConnectionType { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="command">发送的命令</param>
        /// <param name="connectionType">连接类型</param>
        public EpsonRobotCommandSentEventArgs(EpsonRobotCommand command, string connectionType = "")
        {
            Command = command;
            ConnectionType = connectionType ?? string.Empty;
            EventTime = DateTime.Now;
        }
    }

    /// <summary>
    /// Epson机器人状态变化事件参数
    /// </summary>
    public class EpsonRobotStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 机器人状态
        /// </summary>
        public EpsonRobotStatus Status { get; set; }

        /// <summary>
        /// 上一个状态
        /// </summary>
        public EpsonRobotStatus PreviousStatus { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="status">当前状态</param>
        /// <param name="previousStatus">上一个状态</param>
        public EpsonRobotStatusChangedEventArgs(EpsonRobotStatus status, EpsonRobotStatus previousStatus = null)
        {
            Status = status;
            PreviousStatus = previousStatus;
            EventTime = DateTime.Now;
        }
    }

    /// <summary>
    /// Epson机器人错误事件参数
    /// </summary>
    public class EpsonRobotErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 错误类型
        /// </summary>
        public EpsonRobotErrorType ErrorType { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public int ErrorCode { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 连接类型（启动/停止连接 或 数据收发连接）
        /// </summary>
        public string ConnectionType { get; set; }

        /// <summary>
        /// 是否需要重连
        /// </summary>
        public bool RequiresReconnect { get; set; }

        /// <summary>
        /// 是否为严重错误
        /// </summary>
        public bool IsCritical { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="errorType">错误类型</param>
        /// <param name="errorMessage">错误消息</param>
        /// <param name="connectionType">连接类型</param>
        /// <param name="requiresReconnect">是否需要重连</param>
        /// <param name="errorCode">错误代码</param>
        /// <param name="isCritical">是否为严重错误</param>
        public EpsonRobotErrorEventArgs(EpsonRobotErrorType errorType, string errorMessage,
            string connectionType = "", bool requiresReconnect = false, int errorCode = 0, bool isCritical = false)
        {
            ErrorType = errorType;
            ErrorCode = errorCode;
            ErrorMessage = errorMessage ?? string.Empty;
            ConnectionType = connectionType ?? string.Empty;
            RequiresReconnect = requiresReconnect;
            IsCritical = isCritical;
            EventTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 特殊命令接收事件参数（用于自动化流程）
    /// </summary>
    public class SpecialCommandReceivedEventArgs : EventArgs
    {
        /// <summary>
        /// 特殊命令类型
        /// </summary>
        public SpecialCommandType CommandType { get; set; }

        /// <summary>
        /// 命令字符串
        /// </summary>
        public string CommandString { get; set; }

        /// <summary>
        /// 原始消息（兼容性属性）
        /// </summary>
        public string RawMessage => CommandString;

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="commandType">特殊命令类型</param>
        /// <param name="commandString">命令字符串</param>
        public SpecialCommandReceivedEventArgs(SpecialCommandType commandType, string commandString)
        {
            CommandType = commandType;
            CommandString = commandString ?? string.Empty;
            EventTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 机器人启动成功事件参数
    /// </summary>
    public class EpsonRobotStartedEventArgs : EventArgs
    {
        /// <summary>
        /// 启动是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 启动消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="isSuccess">是否成功</param>
        /// <param name="message">消息</param>
        public EpsonRobotStartedEventArgs(bool isSuccess, string message = "")
        {
            IsSuccess = isSuccess;
            Message = message ?? string.Empty;
            EventTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 自动化流程状态变化事件参数
    /// </summary>
    public class AutomationStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 是否正在扫描
        /// </summary>
        public bool IsScanning { get; set; }

        /// <summary>
        /// 是否等待手动输入
        /// </summary>
        public bool IsWaitingForManualInput { get; set; }

        /// <summary>
        /// 当前接收到的特殊命令
        /// </summary>
        public SpecialCommandType? CurrentSpecialCommand { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string StatusDescription { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="isScanning">是否正在扫描</param>
        /// <param name="isWaitingForManualInput">是否等待手动输入</param>
        /// <param name="currentSpecialCommand">当前特殊命令</param>
        /// <param name="statusDescription">状态描述</param>
        public AutomationStatusChangedEventArgs(bool isScanning, bool isWaitingForManualInput, 
            SpecialCommandType? currentSpecialCommand = null, string statusDescription = "")
        {
            IsScanning = isScanning;
            IsWaitingForManualInput = isWaitingForManualInput;
            CurrentSpecialCommand = currentSpecialCommand;
            StatusDescription = statusDescription ?? string.Empty;
            EventTime = DateTime.Now;
        }
    }

    /// <summary>
    /// Epson机器人错误类型枚举
    /// </summary>
    public enum EpsonRobotErrorType
    {
        /// <summary>
        /// 连接错误
        /// </summary>
        ConnectionError,

        /// <summary>
        /// 通信超时
        /// </summary>
        CommunicationTimeout,

        /// <summary>
        /// 命令错误
        /// </summary>
        CommandError,

        /// <summary>
        /// 登录失败
        /// </summary>
        LoginFailed,

        /// <summary>
        /// 机器人错误
        /// </summary>
        RobotError,

        /// <summary>
        /// 解析错误
        /// </summary>
        ParseError,

        /// <summary>
        /// 未知错误
        /// </summary>
        UnknownError
    }
}
