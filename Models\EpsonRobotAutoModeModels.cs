using System;
using System.Collections.Generic;

namespace MyHMI.Models
{
    /// <summary>
    /// Epson机器人自动模式状态枚举
    /// </summary>
    public enum EpsonAutoModeState
    {
        /// <summary>
        /// 空闲状态 - 控制器未启动或已停止
        /// </summary>
        Idle = 0,

        /// <summary>
        /// 初始化中 - 正在连接机器人和初始化依赖管理器
        /// </summary>
        Initializing = 1,

        /// <summary>
        /// 运行中 - 正常处理机器人消息和业务逻辑
        /// </summary>
        Running = 2,

        /// <summary>
        /// 暂停 - 暂时停止处理，可恢复运行
        /// </summary>
        Paused = 3,

        /// <summary>
        /// 错误状态 - 发生错误，需要处理或恢复
        /// </summary>
        Error = 4,

        /// <summary>
        /// 停止中 - 正在停止并清理资源
        /// </summary>
        Stopping = 5
    }

    /// <summary>
    /// 机器人工作流状态枚举
    /// </summary>
    public enum RobotWorkflowState
    {
        /// <summary>
        /// 准备就绪 - 等待工作指令
        /// </summary>
        Ready = 0,

        /// <summary>
        /// 等待取料权限 - 已发送GETPICK，等待权限响应
        /// </summary>
        WaitingPickPermission = 1,

        /// <summary>
        /// 取料中 - 已获得权限，正在执行取料操作
        /// </summary>
        Picking = 2,

        /// <summary>
        /// 数据传输中 - 正在发送扫码数据给机器人
        /// </summary>
        DataTransmitting = 3,

        /// <summary>
        /// 等待NG放置权限 - 已发送GETNGPUT，等待权限响应
        /// </summary>
        WaitingNGPutPermission = 4,

        /// <summary>
        /// NG放置中 - 正在执行NG品放置操作
        /// </summary>
        NGPutting = 5,

        /// <summary>
        /// 等待OK放置权限 - 已发送GETOKPUT，等待权限响应
        /// </summary>
        WaitingOKPutPermission = 6,

        /// <summary>
        /// OK放置中 - 正在执行OK品放置操作
        /// </summary>
        OKPutting = 7,

        /// <summary>
        /// 复位中 - 正在复位到位置1
        /// </summary>
        Resetting = 8,

        /// <summary>
        /// 处理中 - 执行其他处理操作
        /// </summary>
        Processing = 9,

        /// <summary>
        /// 错误状态 - 工作流程出现错误
        /// </summary>
        Error = 10
    }

    /// <summary>
    /// 机器人消息类型枚举
    /// </summary>
    public enum RobotMessageType
    {
        /// <summary>
        /// 获取取料权限
        /// </summary>
        GETPICK,

        /// <summary>
        /// 输入取料确认
        /// </summary>
        INPICK,

        /// <summary>
        /// 获取NG放置权限
        /// </summary>
        GETNGPUT,

        /// <summary>
        /// NG放置满确认
        /// </summary>
        NGPUTFULL,

        /// <summary>
        /// 获取OK放置权限
        /// </summary>
        GETOKPUT,

        /// <summary>
        /// 未知消息类型
        /// </summary>
        Unknown
    }

    /// <summary>
    /// 控制器响应消息类型枚举
    /// </summary>
    public enum ControllerResponseType
    {
        /// <summary>
        /// 允许取料
        /// </summary>
        ALLOWPICK,

        /// <summary>
        /// 拒绝取料
        /// </summary>
        DENYPICK,

        /// <summary>
        /// 允许NG放置
        /// </summary>
        ALLOWNGPUT,

        /// <summary>
        /// 拒绝NG放置
        /// </summary>
        DENYNGPUT,

        /// <summary>
        /// 允许OK放置
        /// </summary>
        ALLOWOKPUT,

        /// <summary>
        /// 拒绝OK放置
        /// </summary>
        DENYOKPUT,

        /// <summary>
        /// 重置NG放置
        /// </summary>
        RESETNGPUT,

        /// <summary>
        /// 扫码数据字符串
        /// </summary>
        SCANDATA
    }

    /// <summary>
    /// 机器人消息类
    /// </summary>
    public class RobotMessage
    {
        /// <summary>
        /// 消息ID
        /// </summary>
        public string MessageId { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 机器人ID (1或2)
        /// </summary>
        public int RobotId { get; set; }

        /// <summary>
        /// 消息类型
        /// </summary>
        public RobotMessageType MessageType { get; set; }

        /// <summary>
        /// 原始消息内容
        /// </summary>
        public string RawMessage { get; set; } = string.Empty;

        /// <summary>
        /// 消息参数
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 接收时间
        /// </summary>
        public DateTime ReceivedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否已处理
        /// </summary>
        public bool IsProcessed { get; set; } = false;

        /// <summary>
        /// 处理结果
        /// </summary>
        public string ProcessResult { get; set; } = string.Empty;

        /// <summary>
        /// 构造函数
        /// </summary>
        public RobotMessage() { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="robotId">机器人ID</param>
        /// <param name="messageType">消息类型</param>
        /// <param name="rawMessage">原始消息</param>
        public RobotMessage(int robotId, RobotMessageType messageType, string rawMessage)
        {
            RobotId = robotId;
            MessageType = messageType;
            RawMessage = rawMessage ?? string.Empty;
        }

        /// <summary>
        /// 从原始消息解析消息类型
        /// </summary>
        /// <param name="rawMessage">原始消息</param>
        /// <returns>消息类型</returns>
        public static RobotMessageType ParseMessageType(string rawMessage)
        {
            if (string.IsNullOrEmpty(rawMessage))
                return RobotMessageType.Unknown;

            if (rawMessage.Contains("GETPICK"))
                return RobotMessageType.GETPICK;
            if (rawMessage.Contains("INPICK"))
                return RobotMessageType.INPICK;
            if (rawMessage.Contains("GETNGPUT"))
                return RobotMessageType.GETNGPUT;
            if (rawMessage.Contains("NGPUTFULL"))
                return RobotMessageType.NGPUTFULL;
            if (rawMessage.Contains("GETOKPUT"))
                return RobotMessageType.GETOKPUT;

            return RobotMessageType.Unknown;
        }
    }

    /// <summary>
    /// 控制器响应消息类
    /// </summary>
    public class ControllerResponse
    {
        /// <summary>
        /// 响应ID
        /// </summary>
        public string ResponseId { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 目标机器人ID
        /// </summary>
        public int TargetRobotId { get; set; }

        /// <summary>
        /// 响应类型
        /// </summary>
        public ControllerResponseType ResponseType { get; set; }

        /// <summary>
        /// 响应数据
        /// </summary>
        public string ResponseData { get; set; } = string.Empty;

        /// <summary>
        /// 原始请求消息ID
        /// </summary>
        public string OriginalMessageId { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否已发送
        /// </summary>
        public bool IsSent { get; set; } = false;

        /// <summary>
        /// 发送时间
        /// </summary>
        public DateTime? SentTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ControllerResponse() { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="targetRobotId">目标机器人ID</param>
        /// <param name="responseType">响应类型</param>
        /// <param name="responseData">响应数据</param>
        public ControllerResponse(int targetRobotId, ControllerResponseType responseType, string responseData = "")
        {
            TargetRobotId = targetRobotId;
            ResponseType = responseType;
            ResponseData = responseData ?? string.Empty;
        }

        /// <summary>
        /// 获取响应字符串
        /// </summary>
        /// <returns>响应字符串</returns>
        public string GetResponseString()
        {
            switch (ResponseType)
            {
                case ControllerResponseType.SCANDATA:
                    return ResponseData; // 扫码数据直接返回
                default:
                    return ResponseType.ToString(); // 其他类型返回枚举名称
            }
        }
    }
}
