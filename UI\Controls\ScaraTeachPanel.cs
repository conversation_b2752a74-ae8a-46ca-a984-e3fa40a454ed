using System;
using System.Drawing;
using System.Windows.Forms;
using MyHMI.Helpers;

namespace MyHMI.UI.Controls
{
    /// <summary>
    /// Scara示教功能面板 - 按HTML原型设计
    /// </summary>
    public partial class ScaraTeachPanel : UserControl
    {
        #region 私有字段
        
        private Panel _mainPanel;
        private Label _titleLabel;
        private Panel _homeGroup;
        private Panel _axisControlGroup;
        private Panel _coordinateGroup;
        private Panel _grabGroup;
        
        #endregion

        #region 构造函数
        
        public ScaraTeachPanel()
        {
            InitializeComponent();
            InitializeInterface();
        }
        
        #endregion

        #region 界面初始化
        
        /// <summary>
        /// 初始化界面 - 按HTML原型精确设计
        /// </summary>
        private void InitializeInterface()
        {
            try
            {
                // 设置面板属性 - 按HTML原型 660x840 (700-40padding)
                this.Size = new Size(660, 840);
                this.BackColor = ColorTranslator.FromHtml("#34495e");
                this.Dock = DockStyle.Fill;
                this.Padding = new Padding(0);

                // 创建主面板 - 按HTML原型padding: 20px
                CreateMainPanel();
                
                // 创建标题 - 按HTML原型样式
                CreateTitle();
                
                // 创建回零操作组 - 按HTML原型样式
                CreateHomeGroup();
                
                // 创建四轴独立控制组 - 按HTML原型样式
                CreateAxisControlGroup();
                
                // 创建坐标设置组 - 按HTML原型样式
                CreateCoordinateGroup();
                
                // 创建抓取参数组 - 按HTML原型样式
                CreateGrabGroup();

                LogHelper.Info("Scara示教功能面板初始化完成 - 按HTML原型设计");
            }
            catch (Exception ex)
            {
                LogHelper.Error("Scara示教功能面板初始化失败", ex);
            }
        }
        
        /// <summary>
        /// 创建主面板 - 按HTML原型
        /// </summary>
        private void CreateMainPanel()
        {
            _mainPanel = new Panel
            {
                Size = new Size(660, 840),
                Location = new Point(0, 0),
                BackColor = ColorTranslator.FromHtml("#34495e"),
                Padding = new Padding(20), // 按HTML原型 padding: 20px
                Dock = DockStyle.Fill
            };
            
            this.Controls.Add(_mainPanel);
        }
        
        /// <summary>
        /// 创建标题 - 按HTML原型样式
        /// </summary>
        private void CreateTitle()
        {
            _titleLabel = new Label
            {
                Text = "示教功能",
                Font = new Font("微软雅黑", 20F, FontStyle.Bold), // 按HTML原型 font-size: 20px
                ForeColor = ColorTranslator.FromHtml("#3498db"),   // 按HTML原型 color: #3498db
                Size = new Size(300, 30),
                Location = new Point(0, 0),
                AutoSize = true
            };
            
            _mainPanel.Controls.Add(_titleLabel);
        }
        
        /// <summary>
        /// 创建回零操作组 - 按HTML原型样式
        /// </summary>
        private void CreateHomeGroup()
        {
            _homeGroup = new Panel
            {
                Size = new Size(600, 80), // 调整尺寸确保不超出功能区域
                Location = new Point(0, 50), // 标题下方20px间距
                BackColor = ColorTranslator.FromHtml("#2c3e50"), // 按HTML原型
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15) // 按HTML原型 padding
            };
            
            // 设置边框颜色 - 按HTML原型 border: 1px solid #4a5661
            _homeGroup.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _homeGroup.Width - 1, _homeGroup.Height - 1);
                }
            };
            
            // 创建组标题 - 按HTML原型 h3
            var groupTitle = new Label
            {
                Text = "回零操作",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold), // 按HTML原型 h3
                ForeColor = Color.White,
                Size = new Size(200, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };
            
            // 回零按钮组 - 按HTML原型
            var btnAllHome = CreateButton("全轴回零", new Point(0, 35), ColorTranslator.FromHtml("#f39c12"));
            var btnAxis1Home = CreateButton("轴1回零", new Point(120, 35), ColorTranslator.FromHtml("#3498db"));
            var btnAxis2Home = CreateButton("轴2回零", new Point(240, 35), ColorTranslator.FromHtml("#3498db"));
            var btnAxis3Home = CreateButton("轴3回零", new Point(360, 35), ColorTranslator.FromHtml("#3498db"));
            var btnAxis4Home = CreateButton("轴4回零", new Point(480, 35), ColorTranslator.FromHtml("#3498db"));
            
            _homeGroup.Controls.Add(groupTitle);
            _homeGroup.Controls.Add(btnAllHome);
            _homeGroup.Controls.Add(btnAxis1Home);
            _homeGroup.Controls.Add(btnAxis2Home);
            _homeGroup.Controls.Add(btnAxis3Home);
            _homeGroup.Controls.Add(btnAxis4Home);
            
            _mainPanel.Controls.Add(_homeGroup);
        }
        
        /// <summary>
        /// 创建四轴独立控制组 - 按HTML原型样式
        /// </summary>
        private void CreateAxisControlGroup()
        {
            _axisControlGroup = new Panel
            {
                Size = new Size(600, 300), // 调整尺寸确保不超出功能区域
                Location = new Point(0, 150), // 回零组下方20px间距
                BackColor = ColorTranslator.FromHtml("#2c3e50"), // 按HTML原型
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15) // 按HTML原型 padding
            };

            // 设置边框颜色 - 按HTML原型 border: 1px solid #4a5661
            _axisControlGroup.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _axisControlGroup.Width - 1, _axisControlGroup.Height - 1);
                }
            };

            // 创建组标题 - 按HTML原型 h3
            var groupTitle = new Label
            {
                Text = "四轴独立控制",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold), // 按HTML原型 h3
                ForeColor = Color.White,
                Size = new Size(200, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };

            // 创建四个轴控制面板 - 按HTML原型 grid布局
            CreateAxisPanel(_axisControlGroup, "轴1(X轴)", "100", "500", "10", "mm/s", "mm/s²", "mm", new Point(0, 35));
            CreateAxisPanel(_axisControlGroup, "轴2(Y轴)", "100", "500", "10", "mm/s", "mm/s²", "mm", new Point(285, 35));
            CreateAxisPanel(_axisControlGroup, "轴3(Z轴)", "50", "200", "5", "mm/s", "mm/s²", "mm", new Point(0, 165));
            CreateAxisPanel(_axisControlGroup, "轴4(R轴)", "180", "360", "10", "°/s", "°/s²", "°", new Point(285, 165));

            _axisControlGroup.Controls.Add(groupTitle);
            _mainPanel.Controls.Add(_axisControlGroup);
        }

        /// <summary>
        /// 创建单个轴控制面板 - 按HTML原型样式
        /// </summary>
        private void CreateAxisPanel(Panel parent, string axisName, string speed, string accel, string distance, string speedUnit, string accelUnit, string distUnit, Point location)
        {
            var axisPanel = new Panel
            {
                Size = new Size(270, 120), // 按HTML原型调整尺寸
                Location = location,
                BackColor = ColorTranslator.FromHtml("#34495e"),
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(10)
            };

            // 设置边框颜色 - 按HTML原型 border: 1px solid #4a5661
            axisPanel.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, axisPanel.Width - 1, axisPanel.Height - 1);
                }
            };

            // 轴标题 - 按HTML原型 h4
            var axisTitle = new Label
            {
                Text = axisName,
                Font = new Font("微软雅黑", 14F, FontStyle.Bold), // 按HTML原型 h4
                ForeColor = Color.White,
                Size = new Size(200, 20),
                Location = new Point(0, 0),
                AutoSize = true
            };

            // 速度输入 - 按HTML原型
            CreateAxisInputGroup(axisPanel, "速度:", speed, speedUnit, 25);

            // 加速度输入 - 按HTML原型
            CreateAxisInputGroup(axisPanel, "加速度:", accel, accelUnit, 50);

            // 点动距离输入 - 按HTML原型
            CreateAxisInputGroup(axisPanel, "点动距离:", distance, distUnit, 75);

            // 控制按钮 - 按HTML原型
            var btnForward = CreateSmallButton("正转", new Point(0, 95), ColorTranslator.FromHtml("#3498db"));
            var btnReverse = CreateSmallButton("反转", new Point(80, 95), ColorTranslator.FromHtml("#3498db"));

            axisPanel.Controls.Add(axisTitle);
            axisPanel.Controls.Add(btnForward);
            axisPanel.Controls.Add(btnReverse);

            parent.Controls.Add(axisPanel);
        }

        /// <summary>
        /// 创建轴输入组 - 按HTML原型样式
        /// </summary>
        private void CreateAxisInputGroup(Panel parent, string labelText, string defaultValue, string unit, int yOffset)
        {
            var label = new Label
            {
                Text = labelText,
                Font = new Font("微软雅黑", 12F), // 按HTML原型
                ForeColor = Color.White,
                Size = new Size(60, 20),
                Location = new Point(0, yOffset),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var textBox = new TextBox
            {
                Text = defaultValue,
                Font = new Font("微软雅黑", 12F), // 按HTML原型
                Size = new Size(80, 20), // 按HTML原型 width: 80px
                Location = new Point(65, yOffset),
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var unitLabel = new Label
            {
                Text = unit,
                Font = new Font("微软雅黑", 12F), // 按HTML原型
                ForeColor = Color.White,
                Size = new Size(50, 20),
                Location = new Point(150, yOffset),
                TextAlign = ContentAlignment.MiddleLeft
            };

            parent.Controls.Add(label);
            parent.Controls.Add(textBox);
            parent.Controls.Add(unitLabel);
        }

        /// <summary>
        /// 创建坐标设置组 - 按HTML原型样式
        /// </summary>
        private void CreateCoordinateGroup()
        {
            _coordinateGroup = new Panel
            {
                Size = new Size(600, 150), // 调整尺寸确保不超出功能区域
                Location = new Point(0, 470), // 四轴控制组下方20px间距
                BackColor = ColorTranslator.FromHtml("#2c3e50"), // 按HTML原型
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15) // 按HTML原型 padding
            };

            // 设置边框颜色 - 按HTML原型 border: 1px solid #4a5661
            _coordinateGroup.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _coordinateGroup.Width - 1, _coordinateGroup.Height - 1);
                }
            };

            // 创建组标题 - 按HTML原型 h3
            var groupTitle = new Label
            {
                Text = "坐标设置",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold), // 按HTML原型 h3
                ForeColor = Color.White,
                Size = new Size(200, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };

            // 创建左右J2节点 - 按HTML原型 grid布局
            CreateCoordinatePanel(_coordinateGroup, "左J2节点", "100", "200", "50", "0", new Point(0, 35));
            CreateCoordinatePanel(_coordinateGroup, "右J2节点", "300", "200", "50", "0", new Point(285, 35));

            _coordinateGroup.Controls.Add(groupTitle);
            _mainPanel.Controls.Add(_coordinateGroup);
        }

        /// <summary>
        /// 创建坐标面板 - 按HTML原型样式
        /// </summary>
        private void CreateCoordinatePanel(Panel parent, string nodeName, string x, string y, string z, string r, Point location)
        {
            var coordPanel = new Panel
            {
                Size = new Size(270, 100), // 按HTML原型调整尺寸
                Location = location,
                BackColor = ColorTranslator.FromHtml("#34495e"),
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(10)
            };

            // 节点标题 - 按HTML原型 h4
            var nodeTitle = new Label
            {
                Text = nodeName,
                Font = new Font("微软雅黑", 14F, FontStyle.Bold), // 按HTML原型 h4
                ForeColor = Color.White,
                Size = new Size(200, 20),
                Location = new Point(0, 0),
                AutoSize = true
            };

            // 坐标输入 - 按HTML原型
            CreateCoordInputGroup(coordPanel, "X:", x, "Y:", y, 25);
            CreateCoordInputGroup(coordPanel, "Z:", z, "R:", r, 50);

            // 操作按钮 - 按HTML原型
            var btnSave = CreateSmallButton("保存", new Point(0, 75), ColorTranslator.FromHtml("#27ae60"));
            var btnMoveTo = CreateSmallButton("移动到", new Point(80, 75), ColorTranslator.FromHtml("#3498db"));

            coordPanel.Controls.Add(nodeTitle);
            coordPanel.Controls.Add(btnSave);
            coordPanel.Controls.Add(btnMoveTo);

            parent.Controls.Add(coordPanel);
        }

        /// <summary>
        /// 创建坐标输入组 - 按HTML原型样式
        /// </summary>
        private void CreateCoordInputGroup(Panel parent, string label1, string value1, string label2, string value2, int yOffset)
        {
            var lbl1 = new Label
            {
                Text = label1,
                Font = new Font("微软雅黑", 12F), // 按HTML原型
                ForeColor = Color.White,
                Size = new Size(20, 20),
                Location = new Point(0, yOffset),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var txt1 = new TextBox
            {
                Text = value1,
                Font = new Font("微软雅黑", 12F), // 按HTML原型
                Size = new Size(60, 20), // 按HTML原型 width: 60px
                Location = new Point(25, yOffset),
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var lbl2 = new Label
            {
                Text = label2,
                Font = new Font("微软雅黑", 12F), // 按HTML原型
                ForeColor = Color.White,
                Size = new Size(20, 20),
                Location = new Point(100, yOffset),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var txt2 = new TextBox
            {
                Text = value2,
                Font = new Font("微软雅黑", 12F), // 按HTML原型
                Size = new Size(60, 20), // 按HTML原型 width: 60px
                Location = new Point(125, yOffset),
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            parent.Controls.Add(lbl1);
            parent.Controls.Add(txt1);
            parent.Controls.Add(lbl2);
            parent.Controls.Add(txt2);
        }

        /// <summary>
        /// 创建抓取参数组 - 按HTML原型样式
        /// </summary>
        private void CreateGrabGroup()
        {
            _grabGroup = new Panel
            {
                Size = new Size(600, 120), // 调整尺寸确保不超出功能区域
                Location = new Point(0, 640), // 坐标组下方20px间距
                BackColor = ColorTranslator.FromHtml("#2c3e50"), // 按HTML原型
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15) // 按HTML原型 padding
            };

            // 设置边框颜色 - 按HTML原型 border: 1px solid #4a5661
            _grabGroup.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _grabGroup.Width - 1, _grabGroup.Height - 1);
                }
            };

            // 创建组标题 - 按HTML原型 h3
            var groupTitle = new Label
            {
                Text = "抓取参数",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold), // 按HTML原型 h3
                ForeColor = Color.White,
                Size = new Size(200, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };

            // 抓取参数输入 - 按HTML原型
            CreateGrabInputGroup(_grabGroup, "抓取高度:", "80", "mm", 35);
            CreateGrabInputGroup(_grabGroup, "抓取速度:", "50", "mm/s", 60);
            CreateGrabInputGroup(_grabGroup, "放置高度:", "100", "mm", 85);

            _grabGroup.Controls.Add(groupTitle);
            _mainPanel.Controls.Add(_grabGroup);
        }

        /// <summary>
        /// 创建抓取输入组 - 按HTML原型样式
        /// </summary>
        private void CreateGrabInputGroup(Panel parent, string labelText, string defaultValue, string unit, int yOffset)
        {
            var label = new Label
            {
                Text = labelText,
                Font = new Font("微软雅黑", 14F), // 按HTML原型
                ForeColor = Color.White,
                Size = new Size(100, 25),
                Location = new Point(0, yOffset),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var textBox = new TextBox
            {
                Text = defaultValue,
                Font = new Font("微软雅黑", 14F), // 按HTML原型
                Size = new Size(150, 25), // 按HTML原型
                Location = new Point(110, yOffset),
                BackColor = ColorTranslator.FromHtml("#34495e"),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var unitLabel = new Label
            {
                Text = unit,
                Font = new Font("微软雅黑", 14F), // 按HTML原型
                ForeColor = Color.White,
                Size = new Size(50, 25),
                Location = new Point(270, yOffset),
                TextAlign = ContentAlignment.MiddleLeft
            };

            parent.Controls.Add(label);
            parent.Controls.Add(textBox);
            parent.Controls.Add(unitLabel);
        }

        /// <summary>
        /// 创建按钮 - 按HTML原型样式
        /// </summary>
        private Button CreateButton(string text, Point location, Color backColor)
        {
            var button = new Button
            {
                Text = text,
                Location = location,
                Size = new Size(100, 35), // 按HTML原型调整按钮尺寸
                BackColor = backColor,
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 14F, FontStyle.Bold), // 按HTML原型字体
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false,
                Cursor = Cursors.Hand
            };

            // 设置按钮边框 - 按HTML原型
            button.FlatAppearance.BorderSize = 1;
            button.FlatAppearance.BorderColor = ColorTranslator.FromHtml("#4a5661");

            return button;
        }

        /// <summary>
        /// 创建小按钮 - 按HTML原型样式
        /// </summary>
        private Button CreateSmallButton(string text, Point location, Color backColor)
        {
            var button = new Button
            {
                Text = text,
                Location = location,
                Size = new Size(70, 25), // 按HTML原型小按钮尺寸
                BackColor = backColor,
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 12F, FontStyle.Bold), // 按HTML原型 font-size: 12px
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false,
                Cursor = Cursors.Hand
            };

            // 设置按钮边框 - 按HTML原型
            button.FlatAppearance.BorderSize = 1;
            button.FlatAppearance.BorderColor = ColorTranslator.FromHtml("#4a5661");

            return button;
        }

        #endregion
    }
}
