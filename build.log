生成启动时间为 2025/9/26 19:31:03。
日志记录详细程度设置为: Detailed。     0>进程 = "C:\Program Files\dotnet\dotnet.exe"
       MSBuild 可执行文件路径 = "C:\Program Files\dotnet\sdk\9.0.305\MSBuild.dll"
       命令行参数 = "C:\Program Files\dotnet\sdk\9.0.305\MSBuild.dll -maxcpucount -verbosity:m -tlp:default=auto MyHMI.sln -p:Configuration=Debug -p:Platform=x64 -flp:logfile=build.log -distributedlogger:Microsoft.DotNet.Tools.MSBuild.MSBuildLogger,C:\Program Files\dotnet\sdk\9.0.305\dotnet.dll*Microsoft.DotNet.Tools.MSBuild.MSBuildForwardingLogger,C:\Program Files\dotnet\sdk\9.0.305\dotnet.dll"
       当前目录 = "E:\projects\C#_projects\HR2"
       MSBuild 版本 = "17.14.21+8929ca9e3"
       基于 Windows 注册表项 LongPathsEnabled，LongPaths 功能为 已启用。
       E:\projects\C#_projects\HR2\MyHMI.sln.metaproj : message : 值为“C:\Program Files\dotnet\sdk\9.0.305\”的属性“MSBuildExtensionsPath”从环境中展开。
       在 Evaluation 期间加载的程序集: System.Threading.Tasks.Parallel, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a (位置: C:\Program Files\dotnet\shared\Microsoft.NETCore.App\9.0.9\System.Threading.Tasks.Parallel.dll，MVID: 79c78d08-12eb-471a-8ca8-a1f78148e163，AssemblyLoadContext: Default)
       C:\Program Files\dotnet\sdk\9.0.305\NuGet.targets(112,5): message : 在 C:\Program Files\dotnet\sdk\9.0.305\NuGet.targets (112,5) 处重新分配属性: $(_GenerateRestoreGraphProjectEntryInputProperties)=“
       C:\Program Files\dotnet\sdk\9.0.305\NuGet.targets(112,5): message :       ExcludeRestorePackageImports=true;
       C:\Program Files\dotnet\sdk\9.0.305\NuGet.targets(112,5): message :       _RestoreSolutionFileUsed=true;
       C:\Program Files\dotnet\sdk\9.0.305\NuGet.targets(112,5): message :       SolutionDir=E:\projects\C#_projects\HR2\;
       C:\Program Files\dotnet\sdk\9.0.305\NuGet.targets(112,5): message :       SolutionName=MyHMI;
       C:\Program Files\dotnet\sdk\9.0.305\NuGet.targets(112,5): message :       SolutionFileName=MyHMI.sln;
       C:\Program Files\dotnet\sdk\9.0.305\NuGet.targets(112,5): message :       SolutionPath=E:\projects\C#_projects\HR2\MyHMI.sln;
       C:\Program Files\dotnet\sdk\9.0.305\NuGet.targets(112,5): message :       SolutionExt=.sln;
       C:\Program Files\dotnet\sdk\9.0.305\NuGet.targets(112,5): message :     ”(先前值:“ExcludeRestorePackageImports=true”)
       在 Evaluation 期间加载的程序集: System.Numerics.Vectors, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a (位置: C:\Program Files\dotnet\shared\Microsoft.NETCore.App\9.0.9\System.Numerics.Vectors.dll，MVID: 7b94f7f5-9631-4b1e-8864-5debf1c50aaa，AssemblyLoadContext: Default)
       项目中不存在 BeforeTargets 特性中的“C:\Program Files\dotnet\sdk\9.0.305\Current\SolutionFile\ImportAfter\Microsoft.NET.Sdk.Solution.targets (33,11)”位置列出的目标“Store”，将忽略该目标。
       项目中不存在 BeforeTargets 特性中的“C:\Program Files\dotnet\sdk\9.0.305\Current\SolutionFile\ImportAfter\Microsoft.NET.Sdk.Solution.targets (33,11)”位置列出的目标“VSTest”，将忽略该目标。
       项目中不存在 BeforeTargets 特性中的“C:\Program Files\dotnet\sdk\9.0.305\NuGet.targets (711,11)”位置列出的目标“_CheckForInvalidConfigurationAndPlatform”，将忽略该目标。
     1>节点 1 上的项目“E:\projects\C#_projects\HR2\MyHMI.sln”(默认目标)。
     1>来自项目“E:\projects\C#_projects\HR2\MyHMI.sln”的文件“E:\projects\C#_projects\HR2\MyHMI.sln.metaproj”中的目标“ValidateSolutionConfiguration”(入口点):
     1>由于 false 条件，已跳过任务“Error”；(('$(CurrentSolutionConfigurationContents)' == '') and ('$(SkipInvalidConfigurations)' != 'true'))计算为(('<SolutionConfiguration>
  <ProjectConfiguration Project="{12345678-1234-5678-9ABC-123456789ABC}" AbsolutePath="E:\projects\C#_projects\HR2\MyHMI.csproj" BuildProjectInSolution="True">Debug|x64</ProjectConfiguration>
</SolutionConfiguration>' == '') and ('' != 'true'))。
       由于 false 条件，已跳过任务“Warning”；(('$(CurrentSolutionConfigurationContents)' == '') and ('$(SkipInvalidConfigurations)' == 'true'))计算为(('<SolutionConfiguration>
         <ProjectConfiguration Project="{12345678-1234-5678-9ABC-123456789ABC}" AbsolutePath="E:\projects\C#_projects\HR2\MyHMI.csproj" BuildProjectInSolution="True">Debug|x64</ProjectConfiguration>
       </SolutionConfiguration>' == '') and ('' == 'true'))。
       正在使用程序集“Microsoft.Build.Tasks.Core, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”中的“Message”任务。
       任务“Message”
         正在生成解决方案配置“Debug|x64”。
       已完成执行任务“Message”的操作。
     1>已完成在项目“MyHMI.sln”中生成目标“ValidateSolutionConfiguration”的操作。
     1>来自项目“E:\projects\C#_projects\HR2\MyHMI.sln”的文件“E:\projects\C#_projects\HR2\MyHMI.sln.metaproj”中的目标“ValidateToolsVersions”(入口点):
       由于 false 条件，已跳过任务“Error”；('$(MSBuildToolsVersion)' == '2.0' and ('$(ProjectToolsVersion)' != '2.0' and '$(ProjectToolsVersion)' != ''))计算为('Current' == '2.0' and ('' != '2.0' and '' != ''))。
     1>已完成在项目“MyHMI.sln”中生成目标“ValidateToolsVersions”的操作。
     1>来自项目“E:\projects\C#_projects\HR2\MyHMI.sln”的文件“E:\projects\C#_projects\HR2\MyHMI.sln.metaproj”中的目标“ValidateProjects”(入口点):
     1>已完成在项目“MyHMI.sln”中生成目标“ValidateProjects”的操作。
       由于条件的计算结果为 false，已跳过目标“_CheckForSolutionLevelRuntimeIdentifier”；('$(RuntimeIdentifier)' != '')的计算结果为('' != '')。
       由于条件的计算结果为 false，已跳过目标“_CheckForSolutionLevelOutputPath”；('$(_CommandLineDefinedOutputPath)' == 'true')的计算结果为('' == 'true')。
     1>来自项目“E:\projects\C#_projects\HR2\MyHMI.sln”的文件“E:\projects\C#_projects\HR2\MyHMI.sln.metaproj”中的目标“Build”(入口点):
       正在使用程序集“Microsoft.Build.Tasks.Core, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”中的“MSBuild”任务。
       任务“MSBuild”
         全局属性:
           BuildingSolutionFile=true
           CurrentSolutionConfigurationContents=<SolutionConfiguration>
           <ProjectConfiguration Project="{12345678-1234-5678-9ABC-123456789ABC}" AbsolutePath="E:\projects\C#_projects\HR2\MyHMI.csproj" BuildProjectInSolution="True">Debug|x64</ProjectConfiguration>
         </SolutionConfiguration>
           SolutionDir=E:\projects\C#_projects\HR2\
           SolutionExt=.sln
           SolutionFileName=MyHMI.sln
           SolutionName=MyHMI
           SolutionPath=E:\projects\C#_projects\HR2\MyHMI.sln
         项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的其他属性:
           Configuration=Debug
           Platform=x64
     0>E:\projects\C#_projects\HR2\MyHMI.csproj(3,92): message : 值为“C:\Program Files\dotnet\sdk\9.0.305\”的属性“MSBuildExtensionsPath”从环境中展开。
       C:\Program Files\dotnet\sdk\9.0.305\Current\Microsoft.Common.props(60,5): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Current\Microsoft.Common.props (60,5) 处重新分配属性: $(MSBuildProjectExtensionsPath)=“E:\projects\C#_projects\HR2\obj\”(先前值:“obj\”)
       C:\Program Files\dotnet\sdk\9.0.305\Current\Microsoft.Common.props(84,119): message : 值为“C:\Users\<USER>\AppData\Local\Microsoft\MSBuild”的属性“MSBuildUserExtensionsPath”从环境中展开。
       “Configuration”属性是全局属性，不能修改。
       “Platform”属性是全局属性，不能修改。
       “Configuration”属性是全局属性，不能修改。
       “Platform”属性是全局属性，不能修改。
       C:\Program Files\dotnet\sdk\9.0.305\Roslyn\Microsoft.CSharp.Core.targets(200,5): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Roslyn\Microsoft.CSharp.Core.targets (200,5) 处重新分配属性: $(CommandLineArgsForDesignTimeEvaluation)=“-langversion:7.3 -define:DEBUG;TRACE”(先前值:“-langversion:7.3”)
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.props(117,5): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.props (117,5) 处重新分配属性: $(AvailablePlatforms)=“Any CPU,x86,x64,ARM64”(先前值:“Any CPU,x86,x64”)
       “Platform”属性是全局属性，不能修改。
       “Configuration”属性是全局属性，不能修改。
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(175,5): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets (175,5) 处重新分配属性: $(_DebugSymbolsProduced)=“true”(先前值:“false”)
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(185,5): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets (185,5) 处重新分配属性: $(_DocumentationFileProduced)=“false”(先前值:“true”)
       “SolutionName”属性是全局属性，不能修改。
       “SolutionFileName”属性是全局属性，不能修改。
       “SolutionPath”属性是全局属性，不能修改。
       “SolutionDir”属性是全局属性，不能修改。
       “SolutionExt”属性是全局属性，不能修改。
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(393,5): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets (393,5) 处重新分配属性: $(MSBuildCopyMarkerName)=“MyHMI.csproj.Up2Date”(先前值:“MyHMI.csproj”)
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(528,5): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets (528,5) 处重新分配属性: $(ProcessorArchitecture)=“amd64”(先前值:“x64”)
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(580,5): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets (580,5) 处重新分配属性: $(DelaySign)=“”(先前值:“false”)
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(649,5): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets (649,5) 处重新分配属性: $(AssemblySearchPaths)=“{CandidateAssemblyFiles};”(先前值:“{CandidateAssemblyFiles}”)
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(650,5): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets (650,5) 处重新分配属性: $(AssemblySearchPaths)=“{CandidateAssemblyFiles};;{HintPathFromItem}”(先前值:“{CandidateAssemblyFiles};”)
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(651,5): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets (651,5) 处重新分配属性: $(AssemblySearchPaths)=“{CandidateAssemblyFiles};;{HintPathFromItem};{TargetFrameworkDirectory}”(先前值:“{CandidateAssemblyFiles};;{HintPathFromItem}”)
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(652,5): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets (652,5) 处重新分配属性: $(AssemblySearchPaths)=“{CandidateAssemblyFiles};;{HintPathFromItem};{TargetFrameworkDirectory};”(先前值:“{CandidateAssemblyFiles};;{HintPathFromItem};{TargetFrameworkDirectory}”)
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(653,5): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets (653,5) 处重新分配属性: $(AssemblySearchPaths)=“{CandidateAssemblyFiles};;{HintPathFromItem};{TargetFrameworkDirectory};;{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}”(先前值:“{CandidateAssemblyFiles};;{HintPathFromItem};{TargetFrameworkDirectory};”)
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(654,5): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets (654,5) 处重新分配属性: $(AssemblySearchPaths)=“{CandidateAssemblyFiles};;{HintPathFromItem};{TargetFrameworkDirectory};;{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx};{AssemblyFolders}”(先前值:“{CandidateAssemblyFiles};;{HintPathFromItem};{TargetFrameworkDirectory};;{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}”)
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(655,5): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets (655,5) 处重新分配属性: $(AssemblySearchPaths)=“{CandidateAssemblyFiles};;{HintPathFromItem};{TargetFrameworkDirectory};;{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx};{AssemblyFolders};{GAC}”(先前值:“{CandidateAssemblyFiles};;{HintPathFromItem};{TargetFrameworkDirectory};;{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx};{AssemblyFolders}”)
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(656,5): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets (656,5) 处重新分配属性: $(AssemblySearchPaths)=“{CandidateAssemblyFiles};;{HintPathFromItem};{TargetFrameworkDirectory};;{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx};{AssemblyFolders};{GAC};{RawFileName}”(先前值:“{CandidateAssemblyFiles};;{HintPathFromItem};{TargetFrameworkDirectory};;{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx};{AssemblyFolders};{GAC}”)
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(657,5): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets (657,5) 处重新分配属性: $(AssemblySearchPaths)=“{CandidateAssemblyFiles};;{HintPathFromItem};{TargetFrameworkDirectory};;{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx};{AssemblyFolders};{GAC};{RawFileName};bin\x64\Debug\”(先前值:“{CandidateAssemblyFiles};;{HintPathFromItem};{TargetFrameworkDirectory};;{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx};{AssemblyFolders};{GAC};{RawFileName}”)
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(2619,5): message : 值为“C:\Users\<USER>\AppData\Local”的属性“LOCALAPPDATA”从环境中展开。
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(3947,5): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets (3947,5) 处重新分配属性: $(_SGenGenerateSerializationAssembliesConfig)=“Auto”(先前值:“”)
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(3948,5): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets (3948,5) 处重新分配属性: $(_SGenGenerateSerializationAssembliesConfig)=“Off”(先前值:“Auto”)
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(5159,5): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets (5159,5) 处重新分配属性: $(_TargetsThatPrepareProjectReferences)=“
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(5159,5): message :       AssignProjectConfiguration;
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(5159,5): message :       _SplitProjectReferencesByFileExistence
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(5159,5): message :     ”(先前值:“_SplitProjectReferencesByFileExistence”)
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(6954,5): message : 值为“C:\Program Files\dotnet\sdk\9.0.305”的属性“MSBuildExtensionsPath32”从环境中展开。
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets (74,5) 处重新分配属性: $(ResolveReferencesDependsOn)=“
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :       
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :       BeforeResolveReferences;
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :       AssignProjectConfiguration;
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :       ResolveProjectReferences;
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :       FindInvalidProjectReferences;
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :       ResolveNativeReferences;
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :       ResolveAssemblyReferences;
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :       GenerateBindingRedirects;
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :       GenerateBindingRedirectsUpdateAppConfig;
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :       ResolveComReferences;
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :       AfterResolveReferences
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :     ;
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :       ImplicitlyExpandDesignTimeFacades
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :     ”(先前值:“
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :       BeforeResolveReferences;
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :       AssignProjectConfiguration;
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :       ResolveProjectReferences;
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :       FindInvalidProjectReferences;
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :       ResolveNativeReferences;
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :       ResolveAssemblyReferences;
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :       GenerateBindingRedirects;
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :       GenerateBindingRedirectsUpdateAppConfig;
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :       ResolveComReferences;
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :       AfterResolveReferences
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets(74,5): message :     ”)
       正在使用项目“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets”中的目标“GetFrameworkPaths”重写项目“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“GetFrameworkPaths”。
       项目中不存在 AfterTargets 特性中的“C:\Program Files\dotnet\sdk\9.0.305\Microsoft\Microsoft.NET.Build.Extensions\Microsoft.NET.Build.Extensions.ConflictResolution.targets (27,11)”位置列出的目标“ResolveNuGetPackageAssets”，将忽略该目标。
     1>项目“E:\projects\C#_projects\HR2\MyHMI.sln”(1)正在节点 1 上生成“E:\projects\C#_projects\HR2\MyHMI.csproj”(2) (默认目标)。
     2>项目文件包含 ToolsVersion="15.0"。此工具集未知或缺失(在此情况下，您可以通过安装合适的 MSBuild 版本来解决此问题)，或者生成可能已被强制到特定的 ToolsVersion (出于策略原因)。将项目视为具有 ToolsVersion="Current"。有关详细信息，请参阅 http://go.microsoft.com/fwlink/?LinkId=293424。
       由于条件的计算结果为 false，已跳过目标“EnableIntermediateOutputPathMismatchWarning”；('$(RestoreProjectStyle)' == 'PackageReference')的计算结果为('' == 'PackageReference')。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“_CheckForInvalidConfigurationAndPlatform”(目标“BeforeBuild”依赖于它):
       由于 false 条件，已跳过任务“MSBuildInternalMessage”；('$(_InvalidConfigurationMessageSeverity)' != '')计算为('' != '')。
       正在使用程序集“Microsoft.Build.Tasks.Core, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”中的“Message”任务。
       任务“Message”
         Configuration=Debug
       已完成执行任务“Message”的操作。
       任务“Message”
         Platform=x64
       已完成执行任务“Message”的操作。
       由于 false 条件，已跳过任务“MSBuildInternalMessage”；('$(OutDir)' != '' and !HasTrailingSlash('$(OutDir)'))计算为('bin\x64\Debug\' != '' and !HasTrailingSlash('bin\x64\Debug\'))。
       由于 false 条件，已跳过任务“MSBuildInternalMessage”；('$(IntermediateOutputPath)' != '' and !HasTrailingSlash('$(IntermediateOutputPath)'))计算为('obj\x64\Debug\' != '' and !HasTrailingSlash('obj\x64\Debug\'))。
       由于 false 条件，已跳过任务“MSBuildInternalMessage”；('$(BaseIntermediateOutputPath)' != '' and !HasTrailingSlash('$(BaseIntermediateOutputPath)'))计算为('obj\' != '' and !HasTrailingSlash('obj\'))。
       由于 false 条件，已跳过任务“MSBuildInternalMessage”；( '$(Prefer32Bit)' == 'true' and '$(PreferNativeArm64)' == 'true' )计算为( 'true' == 'true' and '' == 'true' )。
       由于 false 条件，已跳过任务“MSBuildInternalMessage”；( '$(NoWin32Manifest)' == 'true' and '$(PreferNativeArm64)' == 'true' )计算为( '' == 'true' and '' == 'true' )。
       由于 false 条件，已跳过任务“MSBuildInternalMessage”；( '$(PreferNativeArm64)' == 'true' and '$(Platform)' != 'AnyCPU' )计算为( '' == 'true' and 'x64' != 'AnyCPU' )。
       由于 false 条件，已跳过任务“MSBuildInternalMessage”；( '$(_InitialMSBuildProjectExtensionsPath)' != '' And '$(MSBuildProjectExtensionsPath)' != '$(_InitialMSBuildProjectExtensionsPath)' )计算为( 'E:\projects\C#_projects\HR2\obj\' != '' And 'E:\projects\C#_projects\HR2\obj\' != 'E:\projects\C#_projects\HR2\obj\' )。
       由于 false 条件，已跳过任务“MSBuildInternalMessage”；( '$(EnableBaseIntermediateOutputPathMismatchWarning)' == 'true' And '$(_InitialBaseIntermediateOutputPath)' != '$(BaseIntermediateOutputPath)' And '$(BaseIntermediateOutputPath)' != '$(MSBuildProjectExtensionsPath)' )计算为( '' == 'true' And 'obj\' != 'obj\' And 'obj\' != 'E:\projects\C#_projects\HR2\obj\' )。
     2>已完成在项目“MyHMI.csproj”中生成目标“_CheckForInvalidConfigurationAndPlatform”的操作。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“BeforeBuild”(目标“Build”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“BeforeBuild”的操作。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“BuildOnlySettings”(目标“CoreBuild”依赖于它):
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(1168,7): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets (1168,7) 处重新分配属性: $(BuildingProject)=“true”(先前值:“false”) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>已完成在项目“MyHMI.csproj”中生成目标“BuildOnlySettings”的操作。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets”中的目标“GetFrameworkPaths”(目标“PrepareForBuild”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“GetFrameworkPaths”的操作。
       已跳过目标“GetFrameworkPaths”。以前的生成已成功。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“GetReferenceAssemblyPaths”(目标“PrepareForBuild”依赖于它):
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(1255,7): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets (1255,7) 处重新分配属性: $(TargetFrameworkDirectory)=“”(先前值:“v4.0.30319”) [E:\projects\C#_projects\HR2\MyHMI.csproj]
       正在使用程序集“Microsoft.Build.Tasks.Core, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”中的“GetReferenceAssemblyPaths”任务。
       任务“GetReferenceAssemblyPaths”
       已完成执行任务“GetReferenceAssemblyPaths”的操作。
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(1272,7): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets (1272,7) 处重新分配属性: $(TargetFrameworkDirectory)=“C:\Program Files %28x86%29\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\;;”(先前值:“v4.0.30319”) [E:\projects\C#_projects\HR2\MyHMI.csproj]
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(1293,7): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets (1293,7) 处重新分配属性: $(TargetFrameworkDirectory)=“C:\Program Files %28x86%29\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\;;;C:\Program Files %28x86%29\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\”(先前值:“v4.0.30319”) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>已完成在项目“MyHMI.csproj”中生成目标“GetReferenceAssemblyPaths”的操作。
       由于条件的计算结果为 false，已跳过目标“AssignLinkMetadata”；( '$(SynthesizeLinkMetadata)' == 'true' )的计算结果为( '' == 'true' )。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“PrepareForBuild”(目标“CoreBuild”依赖于它):
       正在使用程序集“Microsoft.Build.Tasks.Core, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”中的“FindAppConfigFile”任务。
       任务“FindAppConfigFile”
         已找到“App.config”。
       已完成执行任务“FindAppConfigFile”的操作。
       正在使用程序集“Microsoft.Build.Tasks.Core, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”中的“MakeDir”任务。
       任务“MakeDir”
       已完成执行任务“MakeDir”的操作。
     2>已完成在项目“MyHMI.csproj”中生成目标“PrepareForBuild”的操作。
       由于条件的计算结果为 false，已跳过目标“PreBuildEvent”；('$(PreBuildEvent)'!='')的计算结果为(''!='')。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“BeforeResolveReferences”(目标“ResolveReferences”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“BeforeResolveReferences”的操作。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“IgnoreJavaScriptOutputAssembly”(目标“AssignProjectConfiguration”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“IgnoreJavaScriptOutputAssembly”的操作。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“AssignProjectConfiguration”(目标“ResolveReferences”依赖于它):
       正在使用程序集“Microsoft.Build.Tasks.Core, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”中的“AssignProjectConfiguration”任务。
       任务“AssignProjectConfiguration”
       已完成执行任务“AssignProjectConfiguration”的操作。
     2>已完成在项目“MyHMI.csproj”中生成目标“AssignProjectConfiguration”的操作。
       已跳过目标“AssignProjectConfiguration”。以前的生成已成功。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“_SplitProjectReferencesByFileExistence”(目标“PrepareProjectReferences”依赖于它):
       由于 false 条件，已跳过任务“ResolveNonMSBuildProjectOutput”；('$(BuildingInsideVisualStudio)'=='true' and '@(ProjectReferenceWithConfiguration)'!='')计算为(''=='true' and ''!='')。
     2>已完成在项目“MyHMI.csproj”中生成目标“_SplitProjectReferencesByFileExistence”的操作。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“_AddOutputPathToGlobalPropertiesToRemove”(目标“_GetProjectReferenceTargetFrameworkProperties”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“_AddOutputPathToGlobalPropertiesToRemove”的操作。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“_GetProjectReferenceTargetFrameworkProperties”(目标“PrepareProjectReferences”依赖于它):
       正在使用程序集“Microsoft.Build.Tasks.Core, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”中的“MSBuild”任务。
       任务“MSBuild”
       已完成执行任务“MSBuild”的操作。
       由于 false 条件，已跳过任务“MSBuild”；('%(_MSBuildProjectReferenceExistent.SkipGetTargetFrameworkProperties)' != 'true' and '$(EnableDynamicPlatformResolution)' == 'true')计算为('' != 'true' and '' == 'true')。
       由于 false 条件，已跳过任务“GetReferenceNearestTargetFrameworkTask”；('@(_ProjectReferenceTargetFrameworkPossibilities)' != '' and '$(ReferringTargetFrameworkForProjectReferences)' != ''
                                                               And '$(GetReferenceNearestTargetFrameworkTaskSupportsTargetPlatformParameter)' == 'true' and '%(_ProjectReferenceTargetFrameworkPossibilities.IsVcxOrNativeProj)' != 'true')计算为('' != '' and '.NETFramework,Version=v4.8' != ''
                                                               And 'true' == 'true' and '' != 'true')。
       由于 false 条件，已跳过任务“GetReferenceNearestTargetFrameworkTask”；('@(_ProjectReferenceTargetFrameworkPossibilities)' != '' and '$(ReferringTargetFrameworkForProjectReferences)' != ''
                                                               And '$(GetReferenceNearestTargetFrameworkTaskSupportsTargetPlatformParameter)' != 'true' and '%(_ProjectReferenceTargetFrameworkPossibilities.IsVcxOrNativeProj)' != 'true')计算为('' != '' and '.NETFramework,Version=v4.8' != ''
                                                               And 'true' != 'true' and '' != 'true')。
       正在使用程序集“Microsoft.Build.Tasks.Core, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”中的“SetRidAgnosticValueForProjects”任务。
       任务“SetRidAgnosticValueForProjects”
       已完成执行任务“SetRidAgnosticValueForProjects”的操作。
     2>已完成在项目“MyHMI.csproj”中生成目标“_GetProjectReferenceTargetFrameworkProperties”的操作。
       由于条件的计算结果为 false，已跳过目标“_GetProjectReferencePlatformProperties”；('$(EnableDynamicPlatformResolution)' == 'true'
                            and '@(_MSBuildProjectReferenceExistent)' != '')的计算结果为('' == 'true'
                            and '' != '')。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“PrepareProjectReferences”(目标“ResolveProjectReferences”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“PrepareProjectReferences”的操作。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“ResolveProjectReferences”(目标“ResolveReferences”依赖于它):
       由于 false 条件，已跳过任务“MSBuild”；('%(_MSBuildProjectReferenceExistent.BuildReference)' == 'true' and '@(ProjectReferenceWithConfiguration)' != '' and ('$(BuildingInsideVisualStudio)' == 'true' or '$(BuildProjectReferences)' != 'true') and '$(VisualStudioVersion)' != '10.0' and '@(_MSBuildProjectReferenceExistent)' != '')计算为('' == 'true' and '' != '' and ('' == 'true' or 'true' != 'true') and '17.0' != '10.0' and '' != '')。
       由于 false 条件，已跳过任务“MSBuild”；('%(_MSBuildProjectReferenceExistent.BuildReference)' == 'true' and '@(ProjectReferenceWithConfiguration)' != '' and '$(BuildingInsideVisualStudio)' != 'true' and '$(BuildProjectReferences)' == 'true' and '@(_MSBuildProjectReferenceExistent)' != '')计算为('' == 'true' and '' != '' and '' != 'true' and 'true' == 'true' and '' != '')。
       由于 false 条件，已跳过任务“MSBuild”；('%(_MSBuildProjectReferenceExistent.BuildReference)' == 'true' and '@(ProjectReferenceWithConfiguration)' != '' and '$(BuildingProject)' == 'true' and '@(_MSBuildProjectReferenceExistent)' != '')计算为('' == 'true' and '' != '' and 'true' == 'true' and '' != '')。
       由于 false 条件，已跳过任务“MSBuildInternalMessage”；('$(_NonExistentProjectReferenceSeverity)' != '')计算为('' != '')。
     2>已完成在项目“MyHMI.csproj”中生成目标“ResolveProjectReferences”的操作。
       由于条件的计算结果为 false，已跳过目标“FindInvalidProjectReferences”；('$(FindInvalidProjectReferences)' == 'true')的计算结果为('' == 'true')。
       由于条件的计算结果为 false，已跳过目标“ResolveNativeReferences”；('@(NativeReference)'!='')的计算结果为(''!='')。
       已跳过目标“ResolveProjectReferences”。以前的生成已成功。
       由于条件的计算结果为 false，已跳过目标“FindInvalidProjectReferences”；('$(FindInvalidProjectReferences)' == 'true')的计算结果为('' == 'true')。
       已跳过目标“GetFrameworkPaths”。以前的生成已成功。
       已跳过目标“GetReferenceAssemblyPaths”。以前的生成已成功。
       已跳过目标“PrepareForBuild”。以前的生成已成功。
       由于条件的计算结果为 false，已跳过目标“GetInstalledSDKLocations”；('@(SDKReference)' != '')的计算结果为('' != '')。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“ResolveSDKReferences”(目标“ResolveAssemblyReferences”依赖于它):
       由于 false 条件，已跳过任务“ResolveSDKReference”；('@(SDKReference)'!='')计算为(''!='')。
     2>已完成在项目“MyHMI.csproj”中生成目标“ResolveSDKReferences”的操作。
       已跳过目标“ResolveSDKReferences”。以前的生成已成功。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“ExpandSDKReferences”(目标“ResolveAssemblyReferences”依赖于它):
       由于 false 条件，已跳过任务“GetSDKReferenceFiles”；('@(ResolvedSDKReference)'!='')计算为(''!='')。
     2>已完成在项目“MyHMI.csproj”中生成目标“ExpandSDKReferences”的操作。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft\Microsoft.NET.Build.Extensions\Microsoft.NET.Build.Extensions.NETFramework.targets”中的目标“ImplicitlyExpandNETStandardFacades”(目标“ResolveAssemblyReferences”依赖于它):
       由于 false 条件，已跳过任务“GetDependsOnNETStandard”；('$(_RunGetDependsOnNETStandard)' == 'true')计算为('' == 'true')。
       由于 false 条件，已跳过任务“NETBuildExtensionsError”；('$(DependsOnNETStandard)' == 'true' AND '$(NETStandardInbox)' != 'true' AND '$(_UsingOldSDK)' == 'true')计算为('' == 'true' AND 'true' != 'true' AND '' == 'true')。
       由于 false 条件，已跳过任务“AddFacadesToReferences”；('@(_NETStandardLibraryNETFrameworkLib)' != '')计算为('' != '')。
     2>已完成在项目“MyHMI.csproj”中生成目标“ImplicitlyExpandNETStandardFacades”的操作。
       已跳过目标“GetFrameworkPaths”。以前的生成已成功。
       已跳过目标“GetReferenceAssemblyPaths”。以前的生成已成功。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft\Microsoft.NET.Build.Extensions\Microsoft.NET.Build.Extensions.ConflictResolution.targets”中的目标“_HandlePackageFileConflicts”(目标“ResolveAssemblyReferences”依赖于它):
       正在使用程序集“C:\Program Files\dotnet\sdk\9.0.305\Microsoft\Microsoft.NET.Build.Extensions\\tools\net9.0\Microsoft.NET.Build.Extensions.Tasks.dll”中的“ResolvePackageFileConflicts”任务。
       任务“ResolvePackageFileConflicts”
       已完成执行任务“ResolvePackageFileConflicts”的操作。
     2>已完成在项目“MyHMI.csproj”中生成目标“_HandlePackageFileConflicts”的操作。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“ResolveAssemblyReferences”(目标“ResolveReferences”依赖于它):
       正在使用程序集“Microsoft.Build.Tasks.Core, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”中的“ResolveAssemblyReference”任务。
       任务“ResolveAssemblyReference”
         TargetFrameworkMoniker:
             .NETFramework,Version=v4.8
         TargetFrameworkMonikerDisplayName:
             .NET Framework 4.8
         TargetedRuntimeVersion:
             v
         Assemblies:
             System
             System.Configuration
             System.IO.Ports
             System.Xml.Linq
             System.Data.DataSetExtensions
             Microsoft.CSharp
             System.Data
             System.Deployment
             System.Drawing
             System.Net.Http
             System.Windows.Forms
             System.Xml
             Newtonsoft.Json
                 HintPath =“packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll”
             NLog
                 HintPath =“packages\NLog.5.2.8\lib\net46\NLog.dll”
             NModbus4
                 HintPath =“packages\NModbus4.2.1.0\lib\net40\NModbus4.dll”
             EPPlus
                 HintPath =“packages\EPPlus.*******\lib\net40\EPPlus.dll”
             CsvHelper
                 HintPath =“packages\CsvHelper.30.0.1\lib\net47\CsvHelper.dll”
             System.Core
         AssemblyFiles:
             C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\mscorlib.dll
         CandidateAssemblyFiles:
         TargetFrameworkDirectories:
             C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\,C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\
         NonCultureResourceDirectories:
             
         InstalledAssemblyTables:
         IgnoreInstalledAssemblyTable:
             False
         SearchPaths:
             {CandidateAssemblyFiles}
             {HintPathFromItem}
             {TargetFrameworkDirectory}
             {Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}
             {AssemblyFolders}
             {GAC}
             {RawFileName}
             bin\x64\Debug\
         AllowedAssemblyExtensions:
             .winmd
             .dll
             .exe
         AllowedRelatedFileExtensions:
             .pdb
             .xml
             .pri
             .dll.config
             .exe.config
         AppConfigFile:
             App.config
         AutoUnify:
             True
         EnableCustomCulture:
             False
         CopyLocalDependenciesWhenParentReferenceInGac:
             True
         FindDependencies:
             True
         TargetProcessorArchitecture:
             amd64
         StateFile:
             obj\x64\Debug\MyHMI.csproj.AssemblyReference.cache
         InstalledAssemblySubsetTables:
         IgnoreInstalledAssemblySubsetTable:
             False
         TargetFrameworkSubsets:
         FullTargetFrameworkSubsetNames:
             Full
         ProfileName:
             
         FullFrameworkFolders:
             C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\
         LatestTargetFrameworkDirectories:
         ProfileTablesLocation:
         主引用“mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089”。
             解析的文件路径为“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\mscorlib.dll”。
             在搜索路径位置“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\mscorlib.dll”找到引用。
             此引用不是“CopyLocal”，因为它是系统必备文件。
         主引用“System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089”。
             解析的文件路径为“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.dll”。
             在搜索路径位置“{TargetFrameworkDirectory}”找到引用。
                 用于 SearchPath“{TargetFrameworkDirectory}”。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.winmd”，但它不存在。
             此引用不是“CopyLocal”，因为它是系统必备文件。
         主引用“System.Configuration, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”。
             解析的文件路径为“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Configuration.dll”。
             在搜索路径位置“{TargetFrameworkDirectory}”找到引用。
                 用于 SearchPath“{TargetFrameworkDirectory}”。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Configuration.winmd”，但它不存在。
             此引用不是“CopyLocal”，因为它是系统必备文件。
         主引用“System.IO.Ports”。
     2>C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(2433,5): warning MSB3245: 未能解析此引用。未能找到程序集“System.IO.Ports”。请检查磁盘上是否存在该程序集。 如果您的代码需要此引用，则可能出现编译错误。 [E:\projects\C#_projects\HR2\MyHMI.csproj]
                 用于 SearchPath“{TargetFrameworkDirectory}”。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.IO.Ports.winmd”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.IO.Ports.dll”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.IO.Ports.exe”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.IO.Ports.winmd”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.IO.Ports.dll”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.IO.Ports.exe”，但它不存在。
                 用于 SearchPath“{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}”。
                 已考虑 AssemblyFoldersEx 位置。
                 用于 SearchPath“{GAC}”。
                 已考虑使用“{GAC}\System.IO.Ports.winmd”，但它不存在。
                 已考虑使用“{GAC}\System.IO.Ports.dll”，但它不存在。
                 已考虑使用“{GAC}\System.IO.Ports.exe”，但它不存在。
                 用于 SearchPath“{RawFileName}”。
                 已考虑将“System.IO.Ports”视为文件名，但它不存在。
                 用于 SearchPath“bin\x64\Debug\”。
                 已考虑使用“bin\x64\Debug\System.IO.Ports.winmd”，但它不存在。
                 已考虑使用“bin\x64\Debug\System.IO.Ports.dll”，但它不存在。
                 已考虑使用“bin\x64\Debug\System.IO.Ports.exe”，但它不存在。
         主引用“System.Xml.Linq, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089”。
             解析的文件路径为“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.Linq.dll”。
             在搜索路径位置“{TargetFrameworkDirectory}”找到引用。
                 用于 SearchPath“{TargetFrameworkDirectory}”。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.Linq.winmd”，但它不存在。
             此引用不是“CopyLocal”，因为它是系统必备文件。
         主引用“System.Data.DataSetExtensions, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089”。
             解析的文件路径为“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.DataSetExtensions.dll”。
             在搜索路径位置“{TargetFrameworkDirectory}”找到引用。
                 用于 SearchPath“{TargetFrameworkDirectory}”。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.DataSetExtensions.winmd”，但它不存在。
             此引用不是“CopyLocal”，因为它是系统必备文件。
         主引用“Microsoft.CSharp, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”。
             解析的文件路径为“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.CSharp.dll”。
             在搜索路径位置“{TargetFrameworkDirectory}”找到引用。
                 用于 SearchPath“{TargetFrameworkDirectory}”。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.CSharp.winmd”，但它不存在。
             此引用不是“CopyLocal”，因为它是系统必备文件。
         主引用“System.Data, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089”。
             解析的文件路径为“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.dll”。
             在搜索路径位置“{TargetFrameworkDirectory}”找到引用。
                 用于 SearchPath“{TargetFrameworkDirectory}”。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.winmd”，但它不存在。
             此引用不是“CopyLocal”，因为它是系统必备文件。
         主引用“System.Deployment, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”。
             解析的文件路径为“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Deployment.dll”。
             在搜索路径位置“{TargetFrameworkDirectory}”找到引用。
                 用于 SearchPath“{TargetFrameworkDirectory}”。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Deployment.winmd”，但它不存在。
             此引用不是“CopyLocal”，因为它是系统必备文件。
         主引用“System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”。
             解析的文件路径为“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Drawing.dll”。
             在搜索路径位置“{TargetFrameworkDirectory}”找到引用。
                 用于 SearchPath“{TargetFrameworkDirectory}”。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Drawing.winmd”，但它不存在。
             此引用不是“CopyLocal”，因为它是系统必备文件。
         主引用“System.Net.Http, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”。
             解析的文件路径为“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Net.Http.dll”。
             在搜索路径位置“{TargetFrameworkDirectory}”找到引用。
                 用于 SearchPath“{TargetFrameworkDirectory}”。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Net.Http.winmd”，但它不存在。
             此引用不是“CopyLocal”，因为它是系统必备文件。
         主引用“System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089”。
             解析的文件路径为“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Windows.Forms.dll”。
             在搜索路径位置“{TargetFrameworkDirectory}”找到引用。
                 用于 SearchPath“{TargetFrameworkDirectory}”。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Windows.Forms.winmd”，但它不存在。
             此引用不是“CopyLocal”，因为它是系统必备文件。
         主引用“System.Xml, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089”。
             解析的文件路径为“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.dll”。
             在搜索路径位置“{TargetFrameworkDirectory}”找到引用。
                 用于 SearchPath“{TargetFrameworkDirectory}”。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.winmd”，但它不存在。
             此引用不是“CopyLocal”，因为它是系统必备文件。
         主引用“Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed”。
             解析的文件路径为“E:\projects\C#_projects\HR2\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll”。
             在搜索路径位置“{HintPathFromItem}”找到引用。
             找到相关文件“E:\projects\C#_projects\HR2\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.xml”。
         主引用“NLog, Version=5.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c”。
             解析的文件路径为“E:\projects\C#_projects\HR2\packages\NLog.5.2.8\lib\net46\NLog.dll”。
             在搜索路径位置“{HintPathFromItem}”找到引用。
             找到相关文件“E:\projects\C#_projects\HR2\packages\NLog.5.2.8\lib\net46\NLog.xml”。
         主引用“NModbus4, Version=2.1.0.0, Culture=neutral, PublicKeyToken=null”。
             解析的文件路径为“E:\projects\C#_projects\HR2\packages\NModbus4.2.1.0\lib\net40\NModbus4.dll”。
             在搜索路径位置“{HintPathFromItem}”找到引用。
             找到相关文件“E:\projects\C#_projects\HR2\packages\NModbus4.2.1.0\lib\net40\NModbus4.xml”。
         主引用“EPPlus, Version=*******, Culture=neutral, PublicKeyToken=ea159fdaa78159a1”。
             解析的文件路径为“E:\projects\C#_projects\HR2\packages\EPPlus.*******\lib\net40\EPPlus.dll”。
             在搜索路径位置“{HintPathFromItem}”找到引用。
             找到相关文件“E:\projects\C#_projects\HR2\packages\EPPlus.*******\lib\net40\EPPlus.xml”。
         主引用“CsvHelper, Version=30.0.0.0, Culture=neutral, PublicKeyToken=8c4959082be5c823”。
             解析的文件路径为“E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47\CsvHelper.dll”。
             在搜索路径位置“{HintPathFromItem}”找到引用。
             找到相关文件“E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47\CsvHelper.xml”。
         主引用“System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089”。
             解析的文件路径为“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Core.dll”。
             在搜索路径位置“{TargetFrameworkDirectory}”找到引用。
                 用于 SearchPath“{TargetFrameworkDirectory}”。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Core.winmd”，但它不存在。
             此引用不是“CopyLocal”，因为它是系统必备文件。
         依赖项“System.Numerics, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089”。
             解析的文件路径为“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Numerics.dll”。
             在搜索路径位置“{TargetFrameworkDirectory}”找到引用。
                 对于 SearchPath“E:\projects\C#_projects\HR2\packages\Newtonsoft.Json.13.0.3\lib\net45”(通过引用程序集“E:\projects\C#_projects\HR2\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll”添加)。
                 已考虑使用“E:\projects\C#_projects\HR2\packages\Newtonsoft.Json.13.0.3\lib\net45\System.Numerics.winmd”，但它不存在。
                 已考虑使用“E:\projects\C#_projects\HR2\packages\Newtonsoft.Json.13.0.3\lib\net45\System.Numerics.dll”，但它不存在。
                 已考虑使用“E:\projects\C#_projects\HR2\packages\Newtonsoft.Json.13.0.3\lib\net45\System.Numerics.exe”，但它不存在。
                 用于 SearchPath“{TargetFrameworkDirectory}”。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Numerics.winmd”，但它不存在。
             “Newtonsoft.Json”所必需的。
             “CsvHelper”所必需的。
             此引用不是“CopyLocal”，因为它是系统必备文件。
         依赖项“System.Runtime.Serialization, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089”。
             解析的文件路径为“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Runtime.Serialization.dll”。
             在搜索路径位置“{TargetFrameworkDirectory}”找到引用。
                 对于 SearchPath“E:\projects\C#_projects\HR2\packages\Newtonsoft.Json.13.0.3\lib\net45”(通过引用程序集“E:\projects\C#_projects\HR2\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll”添加)。
                 已考虑使用“E:\projects\C#_projects\HR2\packages\Newtonsoft.Json.13.0.3\lib\net45\System.Runtime.Serialization.winmd”，但它不存在。
                 已考虑使用“E:\projects\C#_projects\HR2\packages\Newtonsoft.Json.13.0.3\lib\net45\System.Runtime.Serialization.dll”，但它不存在。
                 已考虑使用“E:\projects\C#_projects\HR2\packages\Newtonsoft.Json.13.0.3\lib\net45\System.Runtime.Serialization.exe”，但它不存在。
                 用于 SearchPath“{TargetFrameworkDirectory}”。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Runtime.Serialization.winmd”，但它不存在。
             “Newtonsoft.Json”所必需的。
             此引用不是“CopyLocal”，因为它是系统必备文件。
         统一依赖项“System.IO.Compression, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089”。
             使用此版本而不是“E:\projects\C#_projects\HR2\packages\NLog.5.2.8\lib\net46\NLog.dll”中的原始版本“4.0.0.0”，因为存在此框架文件的更新版本。
             解析的文件路径为“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.IO.Compression.dll”。
             在搜索路径位置“{TargetFrameworkDirectory}”找到引用。
                 对于 SearchPath“E:\projects\C#_projects\HR2\packages\NLog.5.2.8\lib\net46”(通过引用程序集“E:\projects\C#_projects\HR2\packages\NLog.5.2.8\lib\net46\NLog.dll”添加)。
                 已考虑使用“E:\projects\C#_projects\HR2\packages\NLog.5.2.8\lib\net46\System.IO.Compression.winmd”，但它不存在。
                 已考虑使用“E:\projects\C#_projects\HR2\packages\NLog.5.2.8\lib\net46\System.IO.Compression.dll”，但它不存在。
                 已考虑使用“E:\projects\C#_projects\HR2\packages\NLog.5.2.8\lib\net46\System.IO.Compression.exe”，但它不存在。
                 用于 SearchPath“{TargetFrameworkDirectory}”。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.IO.Compression.winmd”，但它不存在。
             “NLog”所必需的。
             此引用不是“CopyLocal”，因为它是系统必备文件。
         依赖项“System.Security, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”。
             解析的文件路径为“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Security.dll”。
             在搜索路径位置“{TargetFrameworkDirectory}”找到引用。
                 对于 SearchPath“E:\projects\C#_projects\HR2\packages\EPPlus.*******\lib\net40”(通过引用程序集“E:\projects\C#_projects\HR2\packages\EPPlus.*******\lib\net40\EPPlus.dll”添加)。
                 已考虑使用“E:\projects\C#_projects\HR2\packages\EPPlus.*******\lib\net40\System.Security.winmd”，但它不存在。
                 已考虑使用“E:\projects\C#_projects\HR2\packages\EPPlus.*******\lib\net40\System.Security.dll”，但它不存在。
                 已考虑使用“E:\projects\C#_projects\HR2\packages\EPPlus.*******\lib\net40\System.Security.exe”，但它不存在。
                 用于 SearchPath“{TargetFrameworkDirectory}”。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Security.winmd”，但它不存在。
             “EPPlus”所必需的。
             此引用不是“CopyLocal”，因为它是系统必备文件。
         依赖项“Microsoft.Bcl.AsyncInterfaces, Version=1.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51”。
             未能解析此引用。未能找到程序集“Microsoft.Bcl.AsyncInterfaces, Version=1.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51”。请检查磁盘上是否存在该程序集。 如果您的代码需要此引用，则可能出现编译错误。
                 对于 SearchPath“E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47”(通过引用程序集“E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47\CsvHelper.dll”添加)。
                 已考虑使用“E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47\Microsoft.Bcl.AsyncInterfaces.winmd”，但它不存在。
                 已考虑使用“E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47\Microsoft.Bcl.AsyncInterfaces.dll”，但它不存在。
                 已考虑使用“E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47\Microsoft.Bcl.AsyncInterfaces.exe”，但它不存在。
                 用于 SearchPath“{TargetFrameworkDirectory}”。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Bcl.AsyncInterfaces.winmd”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Bcl.AsyncInterfaces.dll”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Bcl.AsyncInterfaces.exe”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Bcl.AsyncInterfaces.winmd”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Bcl.AsyncInterfaces.dll”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Bcl.AsyncInterfaces.exe”，但它不存在。
                 用于 SearchPath“{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}”。
                 已考虑 AssemblyFoldersEx 位置。
                 用于 SearchPath“{GAC}”。
                 已考虑使用“{GAC}\Microsoft.Bcl.AsyncInterfaces.winmd”，但它不存在。
                 已考虑使用“{GAC}\Microsoft.Bcl.AsyncInterfaces.dll”，但它不存在。
                 已考虑使用“{GAC}\Microsoft.Bcl.AsyncInterfaces.exe”，但它不存在。
                 用于 SearchPath“bin\x64\Debug\”。
                 已考虑使用“bin\x64\Debug\Microsoft.Bcl.AsyncInterfaces.winmd”，但它不存在。
                 已考虑使用“bin\x64\Debug\Microsoft.Bcl.AsyncInterfaces.dll”，但它不存在。
                 已考虑使用“bin\x64\Debug\Microsoft.Bcl.AsyncInterfaces.exe”，但它不存在。
             “CsvHelper”所必需的。
         依赖项“System.Threading.Tasks.Extensions, Version=4.2.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51”。
             未能解析此引用。未能找到程序集“System.Threading.Tasks.Extensions, Version=4.2.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51”。请检查磁盘上是否存在该程序集。 如果您的代码需要此引用，则可能出现编译错误。
                 对于 SearchPath“E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47”(通过引用程序集“E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47\CsvHelper.dll”添加)。
                 已考虑使用“E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47\System.Threading.Tasks.Extensions.winmd”，但它不存在。
                 已考虑使用“E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47\System.Threading.Tasks.Extensions.dll”，但它不存在。
                 已考虑使用“E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47\System.Threading.Tasks.Extensions.exe”，但它不存在。
                 用于 SearchPath“{TargetFrameworkDirectory}”。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Threading.Tasks.Extensions.winmd”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Threading.Tasks.Extensions.dll”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Threading.Tasks.Extensions.exe”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.Threading.Tasks.Extensions.winmd”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.Threading.Tasks.Extensions.dll”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.Threading.Tasks.Extensions.exe”，但它不存在。
                 用于 SearchPath“{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}”。
                 已考虑 AssemblyFoldersEx 位置。
                 用于 SearchPath“{GAC}”。
                 已考虑使用“{GAC}\System.Threading.Tasks.Extensions.winmd”，但它不存在。
                 已考虑使用“{GAC}\System.Threading.Tasks.Extensions.dll”，但它不存在。
                 已考虑使用“{GAC}\System.Threading.Tasks.Extensions.exe”，但它不存在。
                 用于 SearchPath“bin\x64\Debug\”。
                 已考虑使用“bin\x64\Debug\System.Threading.Tasks.Extensions.winmd”，但它不存在。
                 已考虑使用“bin\x64\Debug\System.Threading.Tasks.Extensions.dll”，但它不存在。
                 已考虑使用“bin\x64\Debug\System.Threading.Tasks.Extensions.exe”，但它不存在。
             “CsvHelper”所必需的。
         依赖项“Microsoft.Bcl.HashCode, Version=1.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51”。
             解析的文件路径为“E:\projects\C#_projects\HR2\bin\x64\Debug\Microsoft.Bcl.HashCode.dll”。
             在搜索路径位置“bin\x64\Debug\”找到引用。
                 对于 SearchPath“E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47”(通过引用程序集“E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47\CsvHelper.dll”添加)。
                 已考虑使用“E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47\Microsoft.Bcl.HashCode.winmd”，但它不存在。
                 已考虑使用“E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47\Microsoft.Bcl.HashCode.dll”，但它不存在。
                 已考虑使用“E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47\Microsoft.Bcl.HashCode.exe”，但它不存在。
                 用于 SearchPath“{TargetFrameworkDirectory}”。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Bcl.HashCode.winmd”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Bcl.HashCode.dll”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Bcl.HashCode.exe”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Bcl.HashCode.winmd”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Bcl.HashCode.dll”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Bcl.HashCode.exe”，但它不存在。
                 用于 SearchPath“{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}”。
                 已考虑 AssemblyFoldersEx 位置。
                 用于 SearchPath“{GAC}”。
                 已考虑使用“{GAC}\Microsoft.Bcl.HashCode.winmd”，但它不存在。
                 已考虑使用“{GAC}\Microsoft.Bcl.HashCode.dll”，但它不存在。
                 已考虑使用“{GAC}\Microsoft.Bcl.HashCode.exe”，但它不存在。
                 用于 SearchPath“bin\x64\Debug\”。
                 已考虑使用“bin\x64\Debug\Microsoft.Bcl.HashCode.winmd”，但它不存在。
             “CsvHelper”所必需的。
         依赖项“System.Memory, Version=4.0.1.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51”。
             未能解析此引用。未能找到程序集“System.Memory, Version=4.0.1.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51”。请检查磁盘上是否存在该程序集。 如果您的代码需要此引用，则可能出现编译错误。
                 对于 SearchPath“E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47”(通过引用程序集“E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47\CsvHelper.dll”添加)。
                 已考虑使用“E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47\System.Memory.winmd”，但它不存在。
                 已考虑使用“E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47\System.Memory.dll”，但它不存在。
                 已考虑使用“E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47\System.Memory.exe”，但它不存在。
                 用于 SearchPath“{TargetFrameworkDirectory}”。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Memory.winmd”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Memory.dll”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Memory.exe”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.Memory.winmd”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.Memory.dll”，但它不存在。
                 已考虑使用“C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.Memory.exe”，但它不存在。
                 用于 SearchPath“{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}”。
                 已考虑 AssemblyFoldersEx 位置。
                 用于 SearchPath“{GAC}”。
                 已考虑使用“{GAC}\System.Memory.winmd”，但它不存在。
                 已考虑使用“{GAC}\System.Memory.dll”，但它不存在。
                 已考虑使用“{GAC}\System.Memory.exe”，但它不存在。
                 用于 SearchPath“bin\x64\Debug\”。
                 已考虑使用“bin\x64\Debug\System.Memory.winmd”，但它不存在。
                 已考虑使用“bin\x64\Debug\System.Memory.dll”，但它不存在。
                 已考虑使用“bin\x64\Debug\System.Memory.exe”，但它不存在。
             “CsvHelper”所必需的。
       已完成执行任务“ResolveAssemblyReference”的操作。
     2>已完成在项目“MyHMI.csproj”中生成目标“ResolveAssemblyReferences”的操作。
       由于条件的计算结果为 false，已跳过目标“GenerateBindingRedirects”；('$(AutoGenerateBindingRedirects)' == 'true' and '$(GenerateBindingRedirectsOutputType)' == 'true' and '@(SuggestedBindingRedirects)' != '' and '$(DesignTimeBuild)' != 'true' and '$(BuildingProject)' == 'true')的计算结果为('true' == 'true' and 'true' == 'true' and '' != '' and '' != 'true' and 'true' == 'true')。
       已跳过目标“ResolveAssemblyReferences”。以前的生成已成功。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft\Microsoft.NET.Build.Extensions\Microsoft.NET.Build.Extensions.NETFramework.targets”中的目标“_RemoveZipFileSuggestedRedirect”(目标“GenerateBindingRedirects”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“_RemoveZipFileSuggestedRedirect”的操作。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“GenerateBindingRedirectsUpdateAppConfig”(目标“ResolveReferences”依赖于它):
       C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(2579,7): message : 在 C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets (2579,7) 处重新分配属性: $(AppConfig)=“obj\x64\Debug\MyHMI.exe.config”(先前值:“App.config”) [E:\projects\C#_projects\HR2\MyHMI.csproj]
       正在使用程序集“Microsoft.Build.Tasks.Core, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”中的“Copy”任务。
       任务“Copy”
         正在将文件从“E:\projects\C#_projects\HR2\App.config”复制到“E:\projects\C#_projects\HR2\obj\x64\Debug\MyHMI.exe.config”。
       已完成执行任务“Copy”的操作。
       正在使用程序集“Microsoft.Build.Tasks.Core, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”中的“Touch”任务。
       任务“Touch”
         正在对“obj\x64\Debug\MyHMI.exe.config”执行 Touch 任务。
       已完成执行任务“Touch”的操作。
     2>已完成在项目“MyHMI.csproj”中生成目标“GenerateBindingRedirectsUpdateAppConfig”的操作。
       由于条件的计算结果为 false，已跳过目标“ResolveComReferences”；('@(COMReference)'!='' or '@(COMFileReference)'!='')的计算结果为(''!='' or ''!='')。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“AfterResolveReferences”(目标“ResolveReferences”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“AfterResolveReferences”的操作。
       已跳过目标“GetReferenceAssemblyPaths”。以前的生成已成功。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.NETFramework.CurrentVersion.targets”中的目标“ImplicitlyExpandDesignTimeFacades”(目标“ResolveReferences”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“ImplicitlyExpandDesignTimeFacades”的操作。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“ResolveReferences”(目标“CoreBuild”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“ResolveReferences”的操作。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Roslyn\Microsoft.Managed.Core.targets”中的目标“CopyAdditionalFiles”(目标“AssignTargetPaths”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“CopyAdditionalFiles”的操作。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“AssignTargetPaths”(目标“PrepareResourceNames”依赖于它):
       正在使用程序集“Microsoft.Build.Tasks.Core, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”中的“AssignTargetPath”任务。
       任务“AssignTargetPath”
       已完成执行任务“AssignTargetPath”的操作。
       任务“AssignTargetPath”
       已完成执行任务“AssignTargetPath”的操作。
       任务“AssignTargetPath”
       已完成执行任务“AssignTargetPath”的操作。
       任务“AssignTargetPath”
       已完成执行任务“AssignTargetPath”的操作。
       由于 false 条件，已跳过任务“AssignTargetPath”；('@(_DeploymentBaseManifestWithTargetPath)'=='' and '%(None.Extension)'=='.manifest')计算为(''=='' and '.settings'=='.manifest')。
       由于 false 条件，已跳过任务“AssignTargetPath”；('@(_DeploymentBaseManifestWithTargetPath)'=='' and '%(None.Extension)'=='.manifest')计算为(''=='' and '.config'=='.manifest')。
       由于 false 条件，已跳过任务“AssignTargetPath”；('@(_DeploymentBaseManifestWithTargetPath)'=='' and '%(None.Extension)'=='.manifest')计算为(''=='' and '.json'=='.manifest')。
     2>已完成在项目“MyHMI.csproj”中生成目标“AssignTargetPaths”的操作。
       已跳过目标“AssignTargetPaths”。以前的生成已成功。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“SplitResourcesByCulture”(目标“PrepareResourceNames”依赖于它):
       由于 false 条件，已跳过任务“MSBuildInternalMessage”；('@(ResxWithNoCulture)'!='')计算为(''!='')。
       由于 false 条件，已跳过任务“MSBuildInternalMessage”；('@(ResxWithCulture)'!='')计算为(''!='')。
       由于 false 条件，已跳过任务“MSBuildInternalMessage”；('@(NonResxWithCulture)'!='')计算为(''!='')。
       由于 false 条件，已跳过任务“MSBuildInternalMessage”；('@(NonResxWithNoCulture)'!='')计算为(''!='')。
       正在使用程序集“Microsoft.Build.Tasks.Core, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”中的“AssignCulture”任务。
       任务“AssignCulture”
         为文件“Properties\Resources.resx”指定了区域性“”。
         为文件“UI\MainForm.resx”指定了区域性“”。
         为文件“UI\Controls\CommunicationPanel.resx”指定了区域性“”。
         为文件“UI\Controls\IOControlPanel.resx”指定了区域性“”。
         为文件“UI\Controls\LogPanel.resx”指定了区域性“”。
         为文件“UI\Controls\MotorControlPanel.resx”指定了区域性“”。
         为文件“UI\Controls\StatisticsPanel.resx”指定了区域性“”。
         为文件“UI\Controls\VisionPanel.resx”指定了区域性“”。
         为文件“Testing\TestRunnerForm.resx”指定了区域性“”。
       已完成执行任务“AssignCulture”的操作。
     2>已完成在项目“MyHMI.csproj”中生成目标“SplitResourcesByCulture”的操作。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.CSharp.CurrentVersion.targets”中的目标“CreateManifestResourceNames”(目标“PrepareResourceNames”依赖于它):
       正在使用程序集“Microsoft.Build.Tasks.Core, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”中的“CreateCSharpManifestResourceName”任务。
       任务“CreateCSharpManifestResourceName”
         根命名空间为“MyHMI”。
         资源文件“Properties\Resources.resx”不依赖于其他任何文件。
         资源文件“Properties\Resources.resx”获得了清单资源名称“MyHMI.Properties.Resources”。
         资源文件“UI\MainForm.resx”依赖于“MainForm.cs”。
         资源文件“UI\MainForm.resx”获得了清单资源名称“MyHMI.UI.MainForm”。
         资源文件“UI\Controls\CommunicationPanel.resx”依赖于“CommunicationPanel.cs”。
         资源文件“UI\Controls\CommunicationPanel.resx”获得了清单资源名称“MyHMI.UI.Controls.CommunicationPanel”。
         资源文件“UI\Controls\IOControlPanel.resx”依赖于“IOControlPanel.cs”。
         资源文件“UI\Controls\IOControlPanel.resx”获得了清单资源名称“MyHMI.UI.Controls.IOControlPanel”。
         资源文件“UI\Controls\LogPanel.resx”依赖于“LogPanel.cs”。
         资源文件“UI\Controls\LogPanel.resx”获得了清单资源名称“MyHMI.UI.Controls.LogPanel”。
         资源文件“UI\Controls\MotorControlPanel.resx”依赖于“MotorControlPanel.cs”。
         资源文件“UI\Controls\MotorControlPanel.resx”获得了清单资源名称“MyHMI.UI.Controls.MotorControlPanel”。
         资源文件“UI\Controls\StatisticsPanel.resx”依赖于“StatisticsPanel.cs”。
         资源文件“UI\Controls\StatisticsPanel.resx”获得了清单资源名称“MyHMI.UI.Controls.StatisticsPanel”。
         资源文件“UI\Controls\VisionPanel.resx”依赖于“VisionPanel.cs”。
         资源文件“UI\Controls\VisionPanel.resx”获得了清单资源名称“MyHMI.UI.Controls.VisionPanel”。
         资源文件“Testing\TestRunnerForm.resx”依赖于“TestRunnerForm.cs”。
         资源文件“Testing\TestRunnerForm.resx”获得了清单资源名称“MyHMI.Testing.TestRunnerForm”。
       已完成执行任务“CreateCSharpManifestResourceName”的操作。
       由于 false 条件，已跳过任务“CreateCSharpManifestResourceName”；('%(EmbeddedResource.ManifestResourceName)' == '' and '%(EmbeddedResource.WithCulture)' == 'true' and '%(EmbeddedResource.Type)' == 'Non-Resx')计算为('' == '' and 'false' == 'true' and 'Resx' == 'Non-Resx')。
     2>已完成在项目“MyHMI.csproj”中生成目标“CreateManifestResourceNames”的操作。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“CreateCustomManifestResourceNames”(目标“PrepareResourceNames”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“CreateCustomManifestResourceNames”的操作。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“PrepareResourceNames”(目标“PrepareResources”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“PrepareResourceNames”的操作。
       已跳过目标“ResolveAssemblyReferences”。以前的生成已成功。
       已跳过目标“SplitResourcesByCulture”。以前的生成已成功。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“BeforeResGen”(目标“ResGen”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“BeforeResGen”的操作。
       已跳过目标“ResolveReferences”。以前的生成已成功。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“FindReferenceAssembliesForReferences”(目标“CoreResGen”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“FindReferenceAssembliesForReferences”的操作。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“CoreResGen”(目标“ResGen”依赖于它):
       正在使用程序集“Microsoft.Build.Tasks.Core, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”中的“GenerateResource”任务。
       任务“GenerateResource”
         没有相对于源文件而言已过期的资源。正在跳过资源生成操作。
       已完成执行任务“GenerateResource”的操作。
       由于 false 条件，已跳过任务“GenerateResource”；('%(EmbeddedResource.Type)' == 'Resx' and '%(EmbeddedResource.GenerateResource)' != 'false' and '$(GenerateResourceMSBuildRuntime)' == 'CLR2')计算为('Resx' == 'Resx' and '' != 'false' and '' == 'CLR2')。
     2>已完成在项目“MyHMI.csproj”中生成目标“CoreResGen”的操作。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“AfterResGen”(目标“ResGen”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“AfterResGen”的操作。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“ResGen”(目标“PrepareResources”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“ResGen”的操作。
       由于条件的计算结果为 false，已跳过目标“CompileLicxFiles”；('@(_LicxFile)'!='')的计算结果为(''!='')。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“PrepareResources”(目标“CoreBuild”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“PrepareResources”的操作。
       由于条件的计算结果为 false，已跳过目标“ResolveKeySource”；($(SignManifests) == 'true' or $(SignAssembly) == 'true')的计算结果为( == 'true' or  == 'true')。
       已跳过目标“ResolveReferences”。以前的生成已成功。
       由于条件的计算结果为 false，已跳过目标“ResolveKeySource”；($(SignManifests) == 'true' or $(SignAssembly) == 'true')的计算结果为( == 'true' or  == 'true')。
       由于条件的计算结果为 false，已跳过目标“ResolveComReferences”；('@(COMReference)'!='' or '@(COMFileReference)'!='')的计算结果为(''!='' or ''!='')。
       由于条件的计算结果为 false，已跳过目标“ResolveNativeReferences”；('@(NativeReference)'!='')的计算结果为(''!='')。
       由于条件的计算结果为 false，已跳过目标“_SetExternalWin32ManifestProperties”；('$(GenerateClickOnceManifests)'=='true' or '@(NativeReference)'!='' or '@(ResolvedIsolatedComModules)'!='')的计算结果为(''=='true' or ''!='' or ''!='')。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“_SetEmbeddedWin32ManifestProperties”(目标“SetWin32ManifestProperties”依赖于它):
       正在使用程序集“Microsoft.Build.Tasks.Core, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”中的“GetFrameworkPath”任务。
       任务“GetFrameworkPath”
       已完成执行任务“GetFrameworkPath”的操作。
     2>已完成在项目“MyHMI.csproj”中生成目标“_SetEmbeddedWin32ManifestProperties”的操作。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“SetWin32ManifestProperties”(目标“Compile”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“SetWin32ManifestProperties”的操作。
       由于条件的计算结果为 false，已跳过目标“_SetPreferNativeArm64Win32ManifestProperties”；( '$(PreferNativeArm64)'=='true' and '$(NoWin32Manifest)'!='true')的计算结果为( ''=='true' and ''!='true')。
       已跳过目标“FindReferenceAssembliesForReferences”。以前的生成已成功。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“_GenerateCompileInputs”(目标“Compile”依赖于它):
       由于 false 条件，已跳过任务“MSBuildInternalMessage”；('@(ManifestResourceWithNoCulture)'!='' and '%(ManifestResourceWithNoCulture.EmittedForCompatibilityOnly)'=='')计算为('obj\x64\Debug\MyHMI.Properties.Resources.resources;obj\x64\Debug\MyHMI.UI.MainForm.resources;obj\x64\Debug\MyHMI.UI.Controls.CommunicationPanel.resources;...'!='' and 'true'=='')。
       由于 false 条件，已跳过任务“MSBuildInternalMessage”；('@(ManifestNonResxWithNoCultureOnDisk)'!='' and '%(ManifestNonResxWithNoCultureOnDisk.EmittedForCompatibilityOnly)'=='')计算为(''!='' and ''=='')。
     2>已完成在项目“MyHMI.csproj”中生成目标“_GenerateCompileInputs”的操作。
       已跳过目标“PrepareForBuild”。以前的生成已成功。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Roslyn\Microsoft.Managed.Core.targets”中的目标“GenerateMSBuildEditorConfigFileShouldRun”(目标“GenerateMSBuildEditorConfigFile”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“GenerateMSBuildEditorConfigFileShouldRun”的操作。
       由于条件的计算结果为 false，已跳过目标“GenerateMSBuildEditorConfigFileCore”；('$(_GeneratedEditorConfigShouldRun)' == 'true')的计算结果为('' == 'true')。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Roslyn\Microsoft.Managed.Core.targets”中的目标“GenerateMSBuildEditorConfigFile”(目标“BeforeCompile”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“GenerateMSBuildEditorConfigFile”的操作。
       已跳过目标“PrepareForBuild”。以前的生成已成功。
       已跳过目标“GetReferenceAssemblyPaths”。以前的生成已成功。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.CSharp.CurrentVersion.targets”中的目标“_SetTargetFrameworkMonikerAttribute”(目标“GenerateTargetFrameworkMonikerAttribute”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“_SetTargetFrameworkMonikerAttribute”的操作。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“GenerateTargetFrameworkMonikerAttribute”(目标“BeforeCompile”依赖于它):
       正在跳过目标“GenerateTargetFrameworkMonikerAttribute”，因为所有输出文件相对于输入文件而言都是最新的。
       输入文件:C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.targets
       输出文件:obj\x64\Debug\.NETFramework,Version=v4.8.AssemblyAttributes.cs
     2>已完成在项目“MyHMI.csproj”中生成目标“GenerateTargetFrameworkMonikerAttribute”的操作。
       由于条件的计算结果为 false，已跳过目标“GenerateAdditionalSources”；('@(AssemblyAttributes)' != '' and '$(GenerateAdditionalSources)' == 'true')的计算结果为('' != '' and '' == 'true')。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“BeforeCompile”(目标“Compile”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“BeforeCompile”的操作。
       由于条件的计算结果为 false，已跳过目标“_TimeStampBeforeCompile”；('$(RunPostBuildEvent)'=='OnOutputUpdated' or ('$(RegisterForComInterop)'=='true' and '$(OutputType)'=='library'))的计算结果为(''=='OnOutputUpdated' or (''=='true' and 'WinExe'=='library'))。
       已跳过目标“ResolveAssemblyReferences”。以前的生成已成功。
       已跳过目标“_GenerateCompileInputs”。以前的生成已成功。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“_GenerateCompileDependencyCache”(目标“Compile”依赖于它):
       正在使用程序集“Microsoft.Build.Tasks.Core, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”中的“Hash”任务。
       任务“Hash”
       已完成执行任务“Hash”的操作。
       正在使用程序集“Microsoft.Build.Tasks.Core, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”中的“WriteLinesToFile”任务。
       任务“WriteLinesToFile”
         跳过写入到文件“obj\x64\Debug\MyHMI.csproj.CoreCompileInputs.cache”，因为内容不会改变。
       已完成执行任务“WriteLinesToFile”的操作。
     2>已完成在项目“MyHMI.csproj”中生成目标“_GenerateCompileDependencyCache”的操作。
       由于条件的计算结果为 false，已跳过目标“_ComputeNonExistentFileProperty”；(('$(BuildingInsideVisualStudio)' == 'true') and ('$(BuildingOutOfProcess)' != 'true') and (('$(BuildingProject)' == 'false') or ('$(UseHostCompilerIfAvailable)' == 'true')))的计算结果为(('' == 'true') and ('' != 'true') and (('true' == 'false') or ('true' == 'true')))。
       由于条件的计算结果为 false，已跳过目标“ResolveCodeAnalysisRuleSet”；('$(CodeAnalysisRuleSet)' != '')的计算结果为('' != '')。
       由于条件的计算结果为 false，已跳过目标“ShimReferencePathsWhenCommonTargetsDoesNotUnderstandReferenceAssemblies”；('@(ReferencePathWithRefAssemblies)' == '')的计算结果为('E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47\CsvHelper.dll;E:\projects\C#_projects\HR2\packages\EPPlus.*******\lib\net40\EPPlus.dll;C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.CSharp.dll;...' == '')。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Roslyn\Microsoft.Managed.Core.targets”中的目标“_BeforeVBCSCoreCompile”(目标“CoreCompile”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“_BeforeVBCSCoreCompile”的操作。
       由于条件的计算结果为 false，已跳过目标“ShimReferencePathsWhenCommonTargetsDoesNotUnderstandReferenceAssemblies”；('@(ReferencePathWithRefAssemblies)' == '')的计算结果为('E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47\CsvHelper.dll;E:\projects\C#_projects\HR2\packages\EPPlus.*******\lib\net40\EPPlus.dll;C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.CSharp.dll;...' == '')。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Roslyn\Microsoft.Managed.Core.targets”中的目标“_ComputeSkipAnalyzers”(目标“CoreCompile”依赖于它):
       由于 false 条件，已跳过任务“ShowMessageForImplicitlySkipAnalyzers”；('$(_ImplicitlySkipAnalyzers)' == 'true')计算为('' == 'true')。
     2>已完成在项目“MyHMI.csproj”中生成目标“_ComputeSkipAnalyzers”的操作。
       由于条件的计算结果为 false，已跳过目标“_SetPathMapFromSourceRoots”；('$(DeterministicSourcePaths)' == 'true')的计算结果为('' == 'true')。
       由于条件的计算结果为 false，已跳过目标“CreateCompilerGeneratedFilesOutputPath”；('$(EmitCompilerGeneratedFiles)' == 'true' and !('$(DesignTimeBuild)' == 'true' OR '$(BuildingProject)' != 'true'))的计算结果为('false' == 'true' and !('' == 'true' OR 'true' != 'true'))。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Roslyn\Microsoft.CSharp.Core.targets”中的目标“CoreCompile”(目标“Compile”依赖于它):
       正在完全生成目标“CoreCompile”。
       输入文件“Helpers\UIHelper.cs”比输出文件“obj\x64\Debug\MyHMI.exe”新。
       正在使用程序集“C:\Program Files\dotnet\sdk\9.0.305\Roslyn\Microsoft.Build.Tasks.CodeAnalysis.dll”中的“Csc”任务。
       任务“Csc”
         C:\Program Files\dotnet\dotnet.exe exec "C:\Program Files\dotnet\sdk\9.0.305\Roslyn\bincore\csc.dll" /noconfig /nowarn:1701,1702 /fullpaths /nostdlib+ /platform:x64 /errorreport:prompt /warn:4 /define:DEBUG;TRACE /highentropyva+ /reference:"E:\projects\C#_projects\HR2\packages\CsvHelper.30.0.1\lib\net47\CsvHelper.dll" /reference:"E:\projects\C#_projects\HR2\packages\EPPlus.*******\lib\net40\EPPlus.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.CSharp.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\mscorlib.dll" /reference:"E:\projects\C#_projects\HR2\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll" /reference:"E:\projects\C#_projects\HR2\packages\NLog.5.2.8\lib\net46\NLog.dll" /reference:"E:\projects\C#_projects\HR2\packages\NModbus4.2.1.0\lib\net40\NModbus4.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Configuration.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Core.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.DataSetExtensions.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Deployment.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Drawing.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Net.Http.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Windows.Forms.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.Linq.dll" /debug+ /debug:full /filealign:512 /optimize- /out:obj\x64\Debug\MyHMI.exe /subsystemversion:6.00 /resource:obj\x64\Debug\MyHMI.Properties.Resources.resources /resource:obj\x64\Debug\MyHMI.UI.MainForm.resources /resource:obj\x64\Debug\MyHMI.UI.Controls.CommunicationPanel.resources /resource:obj\x64\Debug\MyHMI.UI.Controls.IOControlPanel.resources /resource:obj\x64\Debug\MyHMI.UI.Controls.LogPanel.resources /resource:obj\x64\Debug\MyHMI.UI.Controls.MotorControlPanel.resources /resource:obj\x64\Debug\MyHMI.UI.Controls.StatisticsPanel.resources /resource:obj\x64\Debug\MyHMI.UI.Controls.VisionPanel.resources /resource:obj\x64\Debug\MyHMI.Testing.TestRunnerForm.resources /target:winexe /utf8output /deterministic+ /langversion:7.3 Program.cs Properties\AssemblyInfo.cs Config\SystemConfiguration.cs Events\CommunicationEventArgs.cs Events\EpsonRobotEventArgs.cs Events\IOEventArgs.cs Events\MotorEventArgs.cs Events\SafetyEventArgs.cs Events\SystemModeEventArgs.cs Helpers\ConfigHelper.cs Helpers\ExceptionHelper.cs Helpers\LogHelper.cs Helpers\UIHelper.cs Managers\ModbusTcpManager.cs Managers\MotorManager.cs Managers\DMC1000BCardManager.cs Managers\DMC1000BMotorManager.cs Managers\DMC1000BIOManager.cs Managers\csDmc1000.cs Managers\EpsonRobotManager.cs Managers\EpsonRobotManager2.cs Managers\SafetyManager.cs Managers\ScannerManager.cs Managers\MultiScannerManager.cs Managers\ScannerAutoModeManager.cs Managers\StartupSelfCheckManager.cs Managers\StatisticsManager.cs Managers\ScaraAutoModeController.cs Managers\ScaraCommunicationManager.cs Managers\SystemModeManager.cs Managers\VisionManager.cs Managers\WorkflowManager.cs Models\CommunicationModels.cs Models\ConfigModels.cs Models\EpsonRobotModels.cs Models\IOModels.cs Models\MotorModels.cs Models\ProductionModels.cs Models\SafetyModels.cs UI\MainForm.cs UI\MainForm.Designer.cs UI\Controls\CommunicationPanel.cs UI\Controls\CommunicationPanel.Designer.cs UI\Controls\IOControlPanel.cs UI\Controls\IOControlPanel.Designer.cs UI\Controls\LogPanel.cs UI\Controls\LogPanel.Designer.cs UI\Controls\MotorControlPanel.cs UI\Controls\MotorControlPanel.Designer.cs UI\Controls\StatisticsPanel.cs UI\Controls\StatisticsPanel.Designer.cs UI\Controls\VisionPanel.cs UI\Controls\VisionPanel.Designer.cs UI\Controls\VisionPositionPanel.cs UI\Controls\VisionPositionPanel.Designer.cs UI\Controls\VisionAlignPanel.cs UI\Controls\VisionAlignPanel.Designer.cs UI\Controls\MotorFlipPanel.cs UI\Controls\MotorFlipPanel.Designer.cs UI\Controls\MotorFlipTeachPanel.cs UI\Controls\MotorFlipTeachPanel.Designer.cs UI\Controls\MotorBeltPanel.cs UI\Controls\MotorBeltPanel.Designer.cs UI\Controls\Robot6AxisPanel.cs UI\Controls\Robot6AxisPanel.Designer.cs UI\Controls\Robot6AxisPanel2.cs UI\Controls\Robot6AxisPanel2.Designer.cs UI\Controls\ScannerControlPanel.cs UI\Controls\ScannerControlPanel.Designer.cs UI\Controls\ScaraCommPanel.cs UI\Controls\ScaraCommPanel.Designer.cs UI\Controls\ScaraTeachPanel.cs UI\Controls\ScaraTeachPanel.Designer.cs UI\Controls\IOReadPanel.cs UI\Controls\IOReadPanel.Designer.cs UI\Controls\IOWritePanel.cs UI\Controls\IOWritePanel.Designer.cs UI\Controls\ProductionLogPanel.cs UI\Controls\ProductionLogPanel.Designer.cs UI\Core\Adapters\PanelLifecycleAdapter.cs UI\Core\Adapters\VisionManagerPluginAdapter.cs UI\Core\Base\PanelLifecycleBase.cs UI\Core\Base\VisionPluginBase.cs UI\Core\Examples\SampleVisionPlugin.cs UI\Core\ExceptionHandlers\ApplicationLevelExceptionHandler.cs UI\Core\ExceptionHandlers\ComponentLevelExceptionHandler.cs UI\Core\ExceptionHandlers\FormLevelExceptionHandler.cs UI\Core\Extensions\IOControlPanelExtensions.cs UI\Core\Extensions\MainFormArchitectureExtensions.cs UI\Core\Extensions\MainFormExtensions.cs UI\Core\Extensions\MotorControlPanelExtensions.cs UI\Core\Extensions\MotorFlipPanelExtensions.cs UI\Core\Extensions\MotorFlipTeachPanelExtensions.cs UI\Core\Extensions\ScannerControlPanelExtensions.cs UI\Core\Extensions\VisionPanelExtensions.cs UI\Core\Hosts\VisionDisplayHost.cs UI\Core\Hosts\VisionHostImpl.cs UI\Core\Interfaces\IExceptionHandler.cs UI\Core\Interfaces\IPanelLifecycle.cs UI\Core\Interfaces\IResourceManager.cs UI\Core\Interfaces\ISystemHealthMonitor.cs UI\Core\Interfaces\IVisionHost.cs UI\Core\Interfaces\IVisionPlugin.cs UI\Core\Managers\DefaultPluginRegistrar.cs UI\Core\Managers\ExceptionHandlingManager.cs UI\Core\Managers\PanelLifecycleManager.cs UI\Core\Managers\UnifiedResourceManager.cs UI\Core\Managers\VisionDisplayHostManager.cs UI\Core\Managers\VisionPluginManager.cs UI\Core\Monitors\SystemHealthMonitor.cs UI\Core\Optimizers\BatchUpdateManager.cs UI\Core\Optimizers\MemoryOptimizer.cs UI\Core\Optimizers\SmartRefreshAlgorithm.cs UI\Core\Security\VisionPluginSandbox.cs UI\Core\Threading\AsyncOperationManager.cs UI\Core\Threading\UIThreadSafetyManager.cs Testing\MockIOManager.cs Testing\MockMotorManager.cs Testing\SimpleTestFramework.cs Testing\SystemTests.cs Testing\TestRunnerForm.cs Testing\TestRunnerForm.Designer.cs Properties\Resources.Designer.cs Properties\Settings.Designer.cs "obj\x64\Debug\.NETFramework,Version=v4.8.AssemblyAttributes.cs"
         在 TaskRun (Microsoft.CodeAnalysis.BuildTasks.Csc) 期间加载的程序集: System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a (位置: C:\Program Files\dotnet\shared\Microsoft.NETCore.App\9.0.9\System.Security.Principal.Windows.dll，MVID: 99f959ff-228e-4861-91ac-451356929c24，AssemblyLoadContext: Default)
         在 TaskRun (Microsoft.CodeAnalysis.BuildTasks.Csc) 期间加载的程序集: System.Security.Claims, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a (位置: C:\Program Files\dotnet\shared\Microsoft.NETCore.App\9.0.9\System.Security.Claims.dll，MVID: 8a0fc212-62a0-450f-97ca-191b68f788b2，AssemblyLoadContext: Default)
         在 TaskRun (Microsoft.CodeAnalysis.BuildTasks.Csc) 期间加载的程序集: System.IO.Pipes, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a (位置: C:\Program Files\dotnet\shared\Microsoft.NETCore.App\9.0.9\System.IO.Pipes.dll，MVID: 8712ec6e-81b6-4d46-b65c-ba990b76e9a4，AssemblyLoadContext: Default)
         在 TaskRun (Microsoft.CodeAnalysis.BuildTasks.Csc) 期间加载的程序集: System.Threading.Overlapped, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a (位置: C:\Program Files\dotnet\shared\Microsoft.NETCore.App\9.0.9\System.Threading.Overlapped.dll，MVID: 214a19b8-ec60-47fb-bafd-080f54265ed5，AssemblyLoadContext: Default)
         在 TaskRun (Microsoft.CodeAnalysis.BuildTasks.Csc) 期间加载的程序集: System.Security.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a (位置: C:\Program Files\dotnet\shared\Microsoft.NETCore.App\9.0.9\System.Security.AccessControl.dll，MVID: d06f7d91-72c5-489c-aefe-6387ae3e7957，AssemblyLoadContext: Default)
         Microsoft(R) Visual C# 编译器 版本 4.14.0-3.25413.5 (b828a8df)
         版权所有(C) Microsoft Corporation。保留所有权利。
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\PanelLifecycleAdapter.cs(36,42): error CS0115: “PanelLifecycleAdapter<T>.InitializeAsync()”: 没有找到适合的方法来重写 [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\PanelLifecycleAdapter.cs(80,42): error CS0115: “PanelLifecycleAdapter<T>.StartAsync()”: 没有找到适合的方法来重写 [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\PanelLifecycleAdapter.cs(123,42): error CS0115: “PanelLifecycleAdapter<T>.StopAsync()”: 没有找到适合的方法来重写 [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\PanelLifecycleAdapter.cs(160,42): error CS0115: “PanelLifecycleAdapter<T>.RefreshAsync()”: 没有找到适合的方法来重写 [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\PanelLifecycleAdapter.cs(190,42): error CS0115: “PanelLifecycleAdapter<T>.RestartAsync()”: 没有找到适合的方法来重写 [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\PanelLifecycleAdapter.cs(224,30): error CS0506: “PanelLifecycleAdapter<T>.Dispose()”: 继承成员“Component.Dispose()”未标记为 virtual、abstract 或 override，无法进行重写 [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\PanelLifecycleAdapter.cs(14,18): error CS0534: “PanelLifecycleAdapter<T>”不实现继承的抽象成员“PanelLifecycleBase.OnInitializeAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\PanelLifecycleAdapter.cs(14,18): error CS0534: “PanelLifecycleAdapter<T>”不实现继承的抽象成员“PanelLifecycleBase.OnSuspendedAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\PanelLifecycleAdapter.cs(14,18): error CS0534: “PanelLifecycleAdapter<T>”不实现继承的抽象成员“PanelLifecycleBase.OnDeactivatedAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\PanelLifecycleAdapter.cs(14,18): error CS0534: “PanelLifecycleAdapter<T>”不实现继承的抽象成员“PanelLifecycleBase.OnResumedAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\PanelLifecycleAdapter.cs(14,18): error CS0534: “PanelLifecycleAdapter<T>”不实现继承的抽象成员“PanelLifecycleBase.OnActivatedAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\PanelLifecycleAdapter.cs(14,18): error CS0534: “PanelLifecycleAdapter<T>”不实现继承的抽象成员“PanelLifecycleBase.OnDisposeAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(203,39): error CS0104: “VisionResult”是“MyHMI.UI.Core.Interfaces.VisionResult”和“MyHMI.Models.VisionResult”之间的不明确的引用 [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(233,39): error CS0246: 未能找到类型或命名空间名“VisionPluginStatus”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(394,51): error CS0104: “VisionErrorEventArgs”是“MyHMI.Events.VisionErrorEventArgs”和“MyHMI.UI.Core.Interfaces.VisionErrorEventArgs”之间的不明确的引用 [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(203,53): error CS0115: “VisionManagerPluginAdapter.OnProcessImageAsync(byte[])”: 没有找到适合的方法来重写 [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(233,59): error CS0115: “VisionManagerPluginAdapter.OnGetStatusAsync()”: 没有找到适合的方法来重写 [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(272,45): error CS0115: “VisionManagerPluginAdapter.OnUpdateConfigAsync(VisionPluginConfig)”: 没有找到适合的方法来重写 [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(16,18): error CS0534: “VisionManagerPluginAdapter”不实现继承的抽象成员“VisionPluginBase.SupportedCameraTypes.get” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(16,18): error CS0534: “VisionManagerPluginAdapter”不实现继承的抽象成员“VisionPluginBase.ProcessFrameAsync(string, byte[])” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(16,18): error CS0534: “VisionManagerPluginAdapter”不实现继承的抽象成员“VisionPluginBase.PluginId.get” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(16,18): error CS0534: “VisionManagerPluginAdapter”不实现继承的抽象成员“VisionPluginBase.UpdateDisplayControl(string, Control)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(16,18): error CS0534: “VisionManagerPluginAdapter”不实现继承的抽象成员“VisionPluginBase.CreateDisplayControl(string, Size)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(16,18): error CS0534: “VisionManagerPluginAdapter”不实现继承的抽象成员“VisionPluginBase.PluginVersion.get” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(16,18): error CS0534: “VisionManagerPluginAdapter”不实现继承的抽象成员“VisionPluginBase.Vendor.get” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(16,18): error CS0534: “VisionManagerPluginAdapter”不实现继承的抽象成员“VisionPluginBase.DisplayName.get” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(134,13): error CS0246: 未能找到类型或命名空间名“VisionProcessParameters”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(132,36): error CS0246: 未能找到类型或命名空间名“VisionProcessResult”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(326,47): error CS0246: 未能找到类型或命名空间名“VisionProcessResult”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(46,42): error CS0115: “SampleVisionPlugin.InitializeAsync(IVisionHost, CancellationToken)”: 没有找到适合的方法来重写 [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(77,42): error CS0115: “SampleVisionPlugin.StartAsync(CancellationToken)”: 没有找到适合的方法来重写 [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(104,42): error CS0115: “SampleVisionPlugin.StopAsync(CancellationToken)”: 没有找到适合的方法来重写 [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.OnStopAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.SupportedCameraTypes.get” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.ProcessFrameAsync(string, byte[])” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.OnDisposeAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.PluginId.get” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.UpdateDisplayControl(string, Control)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.CreateDisplayControl(string, Size)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.OnPauseAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.OnStartAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.PluginVersion.get” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.Vendor.get” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.DisplayName.get” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.OnResumeAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.OnInitializeAsync(IVisionHost, VisionPluginConfig)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(498,35): error CS0246: 未能找到类型或命名空间名“ComponentHealthChangedEventArgs”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(499,35): error CS0246: 未能找到类型或命名空间名“HealthAlertEventArgs”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(500,35): error CS0246: 未能找到类型或命名空间名“MonitoringStateChangedEventArgs”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(501,35): error CS0246: 未能找到类型或命名空间名“EmergencyRecoveryEventArgs”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(170,81): error CS0246: 未能找到类型或命名空间名“ComponentType”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(212,63): error CS0246: 未能找到类型或命名空间名“HealthStatus”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(262,16): error CS0246: 未能找到类型或命名空间名“SystemHealthStatus”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(331,28): error CS0246: 未能找到类型或命名空间名“HealthAlert”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(357,27): error CS0246: 未能找到类型或命名空间名“SystemHealthCheckResult”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(611,28): error CS0246: 未能找到类型或命名空间名“ComponentHealthCheckResult”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(660,28): error CS0246: 未能找到类型或命名空间名“ComponentHealthCheckResult”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(675,28): error CS0246: 未能找到类型或命名空间名“ComponentHealthCheckResult”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(690,28): error CS0246: 未能找到类型或命名空间名“ComponentHealthCheckResult”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(705,28): error CS0246: 未能找到类型或命名空间名“ComponentHealthCheckResult”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(720,28): error CS0246: 未能找到类型或命名空间名“ComponentHealthCheckResult”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(735,28): error CS0246: 未能找到类型或命名空间名“ComponentHealthCheckResult”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(750,62): error CS0246: 未能找到类型或命名空间名“HealthStatus”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(1091,80): error CS0246: 未能找到类型或命名空间名“HealthStatus”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(1104,45): error CS0246: 未能找到类型或命名空间名“HealthAlert”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.StartMonitoringAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.StopMonitoringAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.PauseMonitoringAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.ResumeMonitoringAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.CurrentState” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.IsMonitoring” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.PerformFullHealthCheckAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.PerformQuickHealthCheckAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.CheckComponentHealthAsync(string)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.GetCurrentHealthMetrics()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.GetHealthHistory(DateTime, DateTime)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.RegisterComponent(string, IComponentHealthChecker, ComponentMonitorConfig)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.GetRegisteredComponents()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.UpdateComponentConfig(string, ComponentMonitorConfig)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.SetHealthThreshold(string, HealthThreshold)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.GetHealthThreshold(string)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.GetAllThresholds()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.EnableAutoRecovery(string, Func<Task<bool>>)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.DisableAutoRecovery(string)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.TriggerComponentRecoveryAsync(string)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.TriggerSystemRecoveryAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.HealthStatusChanged” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.HealthWarning” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.HealthCritical” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.ComponentRecovered” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.SystemRecovered” [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Security\VisionPluginSandbox.cs(568,20): error CS0246: 未能找到类型或命名空间名“List<>”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
     2>E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(29,42): error CS0246: 未能找到类型或命名空间名“HealthAlert”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         CompilerServer: server - server processed compilation - MyHMI
       已完成执行任务“Csc”的操作 - 失败。
     2>已完成在项目“MyHMI.csproj”中生成目标“CoreCompile”的操作 - 失败。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“_CheckForCompileOutputs”(目标“_CleanGetCurrentAndPriorFileWrites”依赖于它):
     2>已完成在项目“MyHMI.csproj”中生成目标“_CheckForCompileOutputs”的操作。
       由于条件的计算结果为 false，已跳过目标“_SGenCheckForOutputs”；('$(_SGenGenerateSerializationAssembliesConfig)' == 'On' or ('@(WebReferenceUrl)'!='' and '$(_SGenGenerateSerializationAssembliesConfig)' == 'Auto'))的计算结果为('Off' == 'On' or (''!='' and 'Off' == 'Auto'))。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“_CleanGetCurrentAndPriorFileWrites”(目标“_CleanRecordFileWrites”依赖于它):
       正在使用程序集“Microsoft.Build.Tasks.Core, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”中的“ReadLinesFromFile”任务。
       任务“ReadLinesFromFile”
       已完成执行任务“ReadLinesFromFile”的操作。
       正在使用程序集“Microsoft.Build.Tasks.Core, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”中的“ConvertToAbsolutePath”任务。
       任务“ConvertToAbsolutePath”
       已完成执行任务“ConvertToAbsolutePath”的操作。
       正在使用程序集“Microsoft.Build.Tasks.Core, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”中的“FindUnderPath”任务。
       任务“FindUnderPath”
         比较路径为“E:\projects\C#_projects\HR2”。
       已完成执行任务“FindUnderPath”的操作。
       任务“FindUnderPath”
         比较路径为“bin\x64\Debug\”。
       已完成执行任务“FindUnderPath”的操作。
       任务“FindUnderPath”
         比较路径为“obj\x64\Debug\”。
       已完成执行任务“FindUnderPath”的操作。
       正在使用程序集“Microsoft.Build.Tasks.Core, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a”中的“RemoveDuplicates”任务。
       任务“RemoveDuplicates”
       已完成执行任务“RemoveDuplicates”的操作。
     2>已完成在项目“MyHMI.csproj”中生成目标“_CleanGetCurrentAndPriorFileWrites”的操作。
     2>来自项目“E:\projects\C#_projects\HR2\MyHMI.csproj”的文件“C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets”中的目标“_CleanRecordFileWrites”(目标“CoreBuild”依赖于它):
       任务“RemoveDuplicates”
       已完成执行任务“RemoveDuplicates”的操作。
       任务“MakeDir”
       已完成执行任务“MakeDir”的操作。
       任务“WriteLinesToFile”
         跳过写入到文件“obj\x64\Debug\MyHMI.csproj.FileListAbsolute.txt”，因为内容不会改变。
       已完成执行任务“WriteLinesToFile”的操作。
     2>已完成在项目“MyHMI.csproj”中生成目标“_CleanRecordFileWrites”的操作。
     2>已完成生成项目“E:\projects\C#_projects\HR2\MyHMI.csproj”(默认目标)的操作 - 失败。
     1>已完成执行任务“MSBuild”的操作 - 失败。
     1>已完成在项目“MyHMI.sln”中生成目标“Build”的操作 - 失败。
     1>已完成生成项目“E:\projects\C#_projects\HR2\MyHMI.sln”(默认目标)的操作 - 失败。

生成失败。

       “E:\projects\C#_projects\HR2\MyHMI.sln”(默认目标) (1) ->
       “E:\projects\C#_projects\HR2\MyHMI.csproj”(默认目标) (2) ->
       (ResolveAssemblyReferences 目标) -> 
         C:\Program Files\dotnet\sdk\9.0.305\Microsoft.Common.CurrentVersion.targets(2433,5): warning MSB3245: 未能解析此引用。未能找到程序集“System.IO.Ports”。请检查磁盘上是否存在该程序集。 如果您的代码需要此引用，则可能出现编译错误。 [E:\projects\C#_projects\HR2\MyHMI.csproj]


       “E:\projects\C#_projects\HR2\MyHMI.sln”(默认目标) (1) ->
       “E:\projects\C#_projects\HR2\MyHMI.csproj”(默认目标) (2) ->
       (CoreCompile 目标) -> 
         E:\projects\C#_projects\HR2\UI\Core\Adapters\PanelLifecycleAdapter.cs(36,42): error CS0115: “PanelLifecycleAdapter<T>.InitializeAsync()”: 没有找到适合的方法来重写 [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\PanelLifecycleAdapter.cs(80,42): error CS0115: “PanelLifecycleAdapter<T>.StartAsync()”: 没有找到适合的方法来重写 [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\PanelLifecycleAdapter.cs(123,42): error CS0115: “PanelLifecycleAdapter<T>.StopAsync()”: 没有找到适合的方法来重写 [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\PanelLifecycleAdapter.cs(160,42): error CS0115: “PanelLifecycleAdapter<T>.RefreshAsync()”: 没有找到适合的方法来重写 [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\PanelLifecycleAdapter.cs(190,42): error CS0115: “PanelLifecycleAdapter<T>.RestartAsync()”: 没有找到适合的方法来重写 [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\PanelLifecycleAdapter.cs(224,30): error CS0506: “PanelLifecycleAdapter<T>.Dispose()”: 继承成员“Component.Dispose()”未标记为 virtual、abstract 或 override，无法进行重写 [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\PanelLifecycleAdapter.cs(14,18): error CS0534: “PanelLifecycleAdapter<T>”不实现继承的抽象成员“PanelLifecycleBase.OnInitializeAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\PanelLifecycleAdapter.cs(14,18): error CS0534: “PanelLifecycleAdapter<T>”不实现继承的抽象成员“PanelLifecycleBase.OnSuspendedAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\PanelLifecycleAdapter.cs(14,18): error CS0534: “PanelLifecycleAdapter<T>”不实现继承的抽象成员“PanelLifecycleBase.OnDeactivatedAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\PanelLifecycleAdapter.cs(14,18): error CS0534: “PanelLifecycleAdapter<T>”不实现继承的抽象成员“PanelLifecycleBase.OnResumedAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\PanelLifecycleAdapter.cs(14,18): error CS0534: “PanelLifecycleAdapter<T>”不实现继承的抽象成员“PanelLifecycleBase.OnActivatedAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\PanelLifecycleAdapter.cs(14,18): error CS0534: “PanelLifecycleAdapter<T>”不实现继承的抽象成员“PanelLifecycleBase.OnDisposeAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(203,39): error CS0104: “VisionResult”是“MyHMI.UI.Core.Interfaces.VisionResult”和“MyHMI.Models.VisionResult”之间的不明确的引用 [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(233,39): error CS0246: 未能找到类型或命名空间名“VisionPluginStatus”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(394,51): error CS0104: “VisionErrorEventArgs”是“MyHMI.Events.VisionErrorEventArgs”和“MyHMI.UI.Core.Interfaces.VisionErrorEventArgs”之间的不明确的引用 [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(203,53): error CS0115: “VisionManagerPluginAdapter.OnProcessImageAsync(byte[])”: 没有找到适合的方法来重写 [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(233,59): error CS0115: “VisionManagerPluginAdapter.OnGetStatusAsync()”: 没有找到适合的方法来重写 [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(272,45): error CS0115: “VisionManagerPluginAdapter.OnUpdateConfigAsync(VisionPluginConfig)”: 没有找到适合的方法来重写 [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(16,18): error CS0534: “VisionManagerPluginAdapter”不实现继承的抽象成员“VisionPluginBase.SupportedCameraTypes.get” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(16,18): error CS0534: “VisionManagerPluginAdapter”不实现继承的抽象成员“VisionPluginBase.ProcessFrameAsync(string, byte[])” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(16,18): error CS0534: “VisionManagerPluginAdapter”不实现继承的抽象成员“VisionPluginBase.PluginId.get” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(16,18): error CS0534: “VisionManagerPluginAdapter”不实现继承的抽象成员“VisionPluginBase.UpdateDisplayControl(string, Control)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(16,18): error CS0534: “VisionManagerPluginAdapter”不实现继承的抽象成员“VisionPluginBase.CreateDisplayControl(string, Size)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(16,18): error CS0534: “VisionManagerPluginAdapter”不实现继承的抽象成员“VisionPluginBase.PluginVersion.get” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(16,18): error CS0534: “VisionManagerPluginAdapter”不实现继承的抽象成员“VisionPluginBase.Vendor.get” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Adapters\VisionManagerPluginAdapter.cs(16,18): error CS0534: “VisionManagerPluginAdapter”不实现继承的抽象成员“VisionPluginBase.DisplayName.get” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(134,13): error CS0246: 未能找到类型或命名空间名“VisionProcessParameters”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(132,36): error CS0246: 未能找到类型或命名空间名“VisionProcessResult”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(326,47): error CS0246: 未能找到类型或命名空间名“VisionProcessResult”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(46,42): error CS0115: “SampleVisionPlugin.InitializeAsync(IVisionHost, CancellationToken)”: 没有找到适合的方法来重写 [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(77,42): error CS0115: “SampleVisionPlugin.StartAsync(CancellationToken)”: 没有找到适合的方法来重写 [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(104,42): error CS0115: “SampleVisionPlugin.StopAsync(CancellationToken)”: 没有找到适合的方法来重写 [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.OnStopAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.SupportedCameraTypes.get” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.ProcessFrameAsync(string, byte[])” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.OnDisposeAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.PluginId.get” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.UpdateDisplayControl(string, Control)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.CreateDisplayControl(string, Size)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.OnPauseAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.OnStartAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.PluginVersion.get” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.Vendor.get” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.DisplayName.get” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.OnResumeAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Examples\SampleVisionPlugin.cs(17,18): error CS0534: “SampleVisionPlugin”不实现继承的抽象成员“VisionPluginBase.OnInitializeAsync(IVisionHost, VisionPluginConfig)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(498,35): error CS0246: 未能找到类型或命名空间名“ComponentHealthChangedEventArgs”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(499,35): error CS0246: 未能找到类型或命名空间名“HealthAlertEventArgs”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(500,35): error CS0246: 未能找到类型或命名空间名“MonitoringStateChangedEventArgs”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(501,35): error CS0246: 未能找到类型或命名空间名“EmergencyRecoveryEventArgs”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(170,81): error CS0246: 未能找到类型或命名空间名“ComponentType”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(212,63): error CS0246: 未能找到类型或命名空间名“HealthStatus”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(262,16): error CS0246: 未能找到类型或命名空间名“SystemHealthStatus”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(331,28): error CS0246: 未能找到类型或命名空间名“HealthAlert”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(357,27): error CS0246: 未能找到类型或命名空间名“SystemHealthCheckResult”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(611,28): error CS0246: 未能找到类型或命名空间名“ComponentHealthCheckResult”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(660,28): error CS0246: 未能找到类型或命名空间名“ComponentHealthCheckResult”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(675,28): error CS0246: 未能找到类型或命名空间名“ComponentHealthCheckResult”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(690,28): error CS0246: 未能找到类型或命名空间名“ComponentHealthCheckResult”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(705,28): error CS0246: 未能找到类型或命名空间名“ComponentHealthCheckResult”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(720,28): error CS0246: 未能找到类型或命名空间名“ComponentHealthCheckResult”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(735,28): error CS0246: 未能找到类型或命名空间名“ComponentHealthCheckResult”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(750,62): error CS0246: 未能找到类型或命名空间名“HealthStatus”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(1091,80): error CS0246: 未能找到类型或命名空间名“HealthStatus”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(1104,45): error CS0246: 未能找到类型或命名空间名“HealthAlert”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.StartMonitoringAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.StopMonitoringAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.PauseMonitoringAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.ResumeMonitoringAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.CurrentState” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.IsMonitoring” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.PerformFullHealthCheckAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.PerformQuickHealthCheckAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.CheckComponentHealthAsync(string)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.GetCurrentHealthMetrics()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.GetHealthHistory(DateTime, DateTime)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.RegisterComponent(string, IComponentHealthChecker, ComponentMonitorConfig)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.GetRegisteredComponents()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.UpdateComponentConfig(string, ComponentMonitorConfig)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.SetHealthThreshold(string, HealthThreshold)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.GetHealthThreshold(string)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.GetAllThresholds()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.EnableAutoRecovery(string, Func<Task<bool>>)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.DisableAutoRecovery(string)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.TriggerComponentRecoveryAsync(string)” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.TriggerSystemRecoveryAsync()” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.HealthStatusChanged” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.HealthWarning” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.HealthCritical” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.ComponentRecovered” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(17,40): error CS0535: “SystemHealthMonitor”不实现接口成员“ISystemHealthMonitor.SystemRecovered” [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Security\VisionPluginSandbox.cs(568,20): error CS0246: 未能找到类型或命名空间名“List<>”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]
         E:\projects\C#_projects\HR2\UI\Core\Monitors\SystemHealthMonitor.cs(29,42): error CS0246: 未能找到类型或命名空间名“HealthAlert”(是否缺少 using 指令或程序集引用?) [E:\projects\C#_projects\HR2\MyHMI.csproj]

    1 个警告
    93 个错误

已用时间 00:00:01.86
