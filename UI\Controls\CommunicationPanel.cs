using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using MyHMI.Managers;
using MyHMI.Helpers;
using MyHMI.Events;
using MyHMI.Models;

namespace MyHMI.UI.Controls
{
    /// <summary>
    /// 通信管理面板
    /// </summary>
    public partial class CommunicationPanel : UserControl
    {
        #region 私有字段
        private GroupBox _scannerGroupBox;
        private GroupBox _modbusGroupBox;
        private GroupBox _robotTcpGroupBox;
        
        private Label _scannerStatusLabel;
        private Button _scannerConnectButton;
        private Button _scannerDisconnectButton;
        
        private Label _modbusStatusLabel;
        private Button _modbusConnectButton;
        private Button _modbusDisconnectButton;
        
        private Label _robotTcpStatusLabel;
        private Button _robotTcpConnectButton;
        private Button _robotTcpDisconnectButton;
        
        private TextBox _communicationLogTextBox;
        #endregion

        #region 构造函数
        public CommunicationPanel()
        {
            InitializeComponent();
            InitializeUI();
        }
        #endregion

        #region 初始化方法
        /// <summary>
        /// 初始化UI组件
        /// </summary>
        private void InitializeUI()
        {
            this.Dock = DockStyle.Fill;
            this.BackColor = Color.White;

            CreateScannerGroup();
            CreateModbusGroup();
            CreateRobotTcpGroup();
            CreateCommunicationLog();
            
            LayoutControls();
        }

        /// <summary>
        /// 创建扫描枪组
        /// </summary>
        private void CreateScannerGroup()
        {
            _scannerGroupBox = new GroupBox
            {
                Text = "扫描枪通信",
                Font = new Font("微软雅黑", 10F, FontStyle.Bold),
                Size = new Size(300, 100),
                Location = new Point(10, 10)
            };

            _scannerStatusLabel = new Label
            {
                Text = "状态: 未连接",
                Size = new Size(200, 20),
                Location = new Point(20, 30),
                Font = new Font("微软雅黑", 9F)
            };

            _scannerConnectButton = new Button
            {
                Text = "连接",
                Size = new Size(60, 30),
                Location = new Point(20, 60),
                UseVisualStyleBackColor = true
            };
            _scannerConnectButton.Click += ScannerConnectButton_Click;

            _scannerDisconnectButton = new Button
            {
                Text = "断开",
                Size = new Size(60, 30),
                Location = new Point(90, 60),
                UseVisualStyleBackColor = true,
                Enabled = false
            };
            _scannerDisconnectButton.Click += ScannerDisconnectButton_Click;

            _scannerGroupBox.Controls.AddRange(new Control[] 
            { 
                _scannerStatusLabel, _scannerConnectButton, _scannerDisconnectButton 
            });

            this.Controls.Add(_scannerGroupBox);
        }

        /// <summary>
        /// 创建Modbus组
        /// </summary>
        private void CreateModbusGroup()
        {
            _modbusGroupBox = new GroupBox
            {
                Text = "Modbus TCP通信",
                Font = new Font("微软雅黑", 10F, FontStyle.Bold),
                Size = new Size(300, 100),
                Location = new Point(320, 10)
            };

            _modbusStatusLabel = new Label
            {
                Text = "状态: 未连接",
                Size = new Size(200, 20),
                Location = new Point(20, 30),
                Font = new Font("微软雅黑", 9F)
            };

            _modbusConnectButton = new Button
            {
                Text = "连接",
                Size = new Size(60, 30),
                Location = new Point(20, 60),
                UseVisualStyleBackColor = true
            };
            _modbusConnectButton.Click += ModbusConnectButton_Click;

            _modbusDisconnectButton = new Button
            {
                Text = "断开",
                Size = new Size(60, 30),
                Location = new Point(90, 60),
                UseVisualStyleBackColor = true,
                Enabled = false
            };
            _modbusDisconnectButton.Click += ModbusDisconnectButton_Click;

            _modbusGroupBox.Controls.AddRange(new Control[] 
            { 
                _modbusStatusLabel, _modbusConnectButton, _modbusDisconnectButton 
            });

            this.Controls.Add(_modbusGroupBox);
        }

        /// <summary>
        /// 创建Epson机器人TCP/IP组
        /// </summary>
        private void CreateRobotTcpGroup()
        {
            _robotTcpGroupBox = new GroupBox
            {
                Text = "Epson机器人TCP/IP通信",
                Font = new Font("微软雅黑", 10F, FontStyle.Bold),
                Size = new Size(300, 100),
                Location = new Point(630, 10)
            };

            _robotTcpStatusLabel = new Label
            {
                Text = "状态: 未连接",
                Size = new Size(200, 20),
                Location = new Point(20, 30),
                Font = new Font("微软雅黑", 9F)
            };

            _robotTcpConnectButton = new Button
            {
                Text = "连接",
                Size = new Size(60, 30),
                Location = new Point(20, 60),
                UseVisualStyleBackColor = true
            };
            _robotTcpConnectButton.Click += RobotTcpConnectButton_Click;

            _robotTcpDisconnectButton = new Button
            {
                Text = "断开",
                Size = new Size(60, 30),
                Location = new Point(90, 60),
                UseVisualStyleBackColor = true,
                Enabled = false
            };
            _robotTcpDisconnectButton.Click += RobotTcpDisconnectButton_Click;

            _robotTcpGroupBox.Controls.AddRange(new Control[] 
            { 
                _robotTcpStatusLabel, _robotTcpConnectButton, _robotTcpDisconnectButton 
            });

            this.Controls.Add(_robotTcpGroupBox);
        }

        /// <summary>
        /// 创建通信日志
        /// </summary>
        private void CreateCommunicationLog()
        {
            var logGroupBox = new GroupBox
            {
                Text = "通信日志",
                Font = new Font("微软雅黑", 10F, FontStyle.Bold),
                Size = new Size(920, 350),
                Location = new Point(10, 120)
            };

            _communicationLogTextBox = new TextBox
            {
                Size = new Size(900, 320),
                Location = new Point(10, 25),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                Font = new Font("Consolas", 9F),
                BackColor = Color.Black,
                ForeColor = Color.LightGreen
            };

            logGroupBox.Controls.Add(_communicationLogTextBox);
            this.Controls.Add(logGroupBox);
        }

        /// <summary>
        /// 布局控件
        /// </summary>
        private void LayoutControls()
        {
            this.MinimumSize = new Size(940, 480);
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 异步初始化面板
        /// </summary>
        public async Task InitializeAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                // 订阅通信状态变化事件
                ScannerManager.Instance.StatusChanged += ScannerManager_StatusChanged;
                ModbusTcpManager.Instance.StatusChanged += ModbusTcpManager_StatusChanged;
                EpsonRobotManager.Instance.ConnectionStatusChanged += EpsonRobotManager_ConnectionStatusChanged;

                // 更新初始状态
                await UpdateAllStatusAsync();

                LogHelper.Info("通信面板初始化完成");

                return true;
            }, false, "通信面板初始化");
        }

        /// <summary>
        /// 异步释放资源
        /// </summary>
        public async Task DisposeAsync()
        {
            await Task.Run(() =>
            {
                // 取消订阅事件
                ScannerManager.Instance.StatusChanged -= ScannerManager_StatusChanged;
                ModbusTcpManager.Instance.StatusChanged -= ModbusTcpManager_StatusChanged;
                EpsonRobotManager.Instance.ConnectionStatusChanged -= EpsonRobotManager_ConnectionStatusChanged;

                LogHelper.Info("通信面板资源释放完成");
            });
        }
        #endregion

        #region 事件处理器
        /// <summary>
        /// 扫描枪连接按钮点击事件
        /// </summary>
        private async void ScannerConnectButton_Click(object sender, EventArgs e)
        {
            await ConnectScannerAsync();
        }

        /// <summary>
        /// 扫描枪断开按钮点击事件
        /// </summary>
        private async void ScannerDisconnectButton_Click(object sender, EventArgs e)
        {
            await DisconnectScannerAsync();
        }

        /// <summary>
        /// Modbus连接按钮点击事件
        /// </summary>
        private async void ModbusConnectButton_Click(object sender, EventArgs e)
        {
            await ConnectModbusAsync();
        }

        /// <summary>
        /// Modbus断开按钮点击事件
        /// </summary>
        private async void ModbusDisconnectButton_Click(object sender, EventArgs e)
        {
            await DisconnectModbusAsync();
        }

        /// <summary>
        /// Epson机器人连接按钮点击事件
        /// </summary>
        private async void RobotTcpConnectButton_Click(object sender, EventArgs e)
        {
            await ConnectRobotTcpAsync();
        }

        /// <summary>
        /// Epson机器人断开按钮点击事件
        /// </summary>
        private async void RobotTcpDisconnectButton_Click(object sender, EventArgs e)
        {
            await DisconnectRobotTcpAsync();
        }

        /// <summary>
        /// 扫描枪状态变化事件处理
        /// </summary>
        private void ScannerManager_StatusChanged(object sender, Events.CommunicationStatusChangedEventArgs e)
        {
            UIHelper.SafeInvoke(() =>
            {
                UpdateScannerStatus(e.Status, e.Description);
                AppendLog($"[扫描枪] {e.Description}");
            });
        }

        /// <summary>
        /// Modbus状态变化事件处理
        /// </summary>
        private void ModbusTcpManager_StatusChanged(object sender, Events.CommunicationStatusChangedEventArgs e)
        {
            UIHelper.SafeInvoke(() =>
            {
                UpdateModbusStatus(e.Status, e.Description);
                AppendLog($"[Modbus] {e.Description}");
            });
        }

        /// <summary>
        /// Epson机器人连接状态变化事件处理
        /// </summary>
        private void EpsonRobotManager_ConnectionStatusChanged(object sender, Events.EpsonRobotConnectionStatusChangedEventArgs e)
        {
            UIHelper.SafeInvoke(() =>
            {
                UpdateRobotTcpStatus(e.Status, e.Description);
                AppendLog($"[Epson机器人-{e.ConnectionType}] {e.Description}");
            });
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 连接扫描枪
        /// </summary>
        private async Task ConnectScannerAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                bool result = await ScannerManager.Instance.ConnectAsync();
                if (!result)
                {
                    MessageBox.Show("扫描枪连接失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

                return true;
            }, false, "连接扫描枪");
        }

        /// <summary>
        /// 断开扫描枪
        /// </summary>
        private async Task DisconnectScannerAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                await ScannerManager.Instance.DisconnectAsync();

                return true;
            }, false, "断开扫描枪");
        }

        /// <summary>
        /// 连接Modbus
        /// </summary>
        private async Task ConnectModbusAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                bool result = await ModbusTcpManager.Instance.ConnectAsync();
                if (!result)
                {
                    MessageBox.Show("Modbus TCP连接失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

                return true;
            }, false, "连接Modbus TCP");
        }

        /// <summary>
        /// 断开Modbus
        /// </summary>
        private async Task DisconnectModbusAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                await ModbusTcpManager.Instance.DisconnectAsync();

                return true;
            }, false, "断开Modbus TCP");
        }

        /// <summary>
        /// 连接Epson机器人TCP/IP
        /// </summary>
        private async Task ConnectRobotTcpAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                // 初始化EpsonRobotManager
                bool initResult = await EpsonRobotManager.Instance.InitializeAsync();
                if (!initResult)
                {
                    MessageBox.Show("Epson机器人管理器初始化失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return true;
                }

                // 连接启动/停止TCP
                bool startStopResult = await EpsonRobotManager.Instance.ConnectStartStopAsync();
                if (!startStopResult)
                {
                    MessageBox.Show("Epson机器人启动/停止TCP连接失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return true;
                }

                // 连接数据收发TCP
                bool dataResult = await EpsonRobotManager.Instance.ConnectDataAsync();
                if (!dataResult)
                {
                    MessageBox.Show("Epson机器人数据收发TCP连接失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return true;
                }

                return true;
            }, false, "连接Epson机器人TCP/IP");
        }

        /// <summary>
        /// 断开Epson机器人TCP/IP
        /// </summary>
        private async Task DisconnectRobotTcpAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                await EpsonRobotManager.Instance.DisconnectAllAsync();

                return true;
            }, false, "断开Epson机器人TCP/IP");
        }

        /// <summary>
        /// 更新所有状态
        /// </summary>
        private async Task UpdateAllStatusAsync()
        {
            await Task.Run(() =>
            {
                UIHelper.SafeInvoke(() =>
                {
                    UpdateScannerStatus(ScannerManager.Instance.Status, "");
                    UpdateModbusStatus(ModbusTcpManager.Instance.Status, "");

                    // 获取Epson机器人的综合连接状态
                    var epsonStatus = GetEpsonRobotCombinedStatus();
                    UpdateRobotTcpStatus(epsonStatus, "");
                });
            });
        }

        /// <summary>
        /// 更新扫描枪状态
        /// </summary>
        private void UpdateScannerStatus(Models.CommunicationStatus status, string description)
        {
            _scannerStatusLabel.Text = $"状态: {GetStatusText(status)}";
            _scannerStatusLabel.ForeColor = GetStatusColor(status);
            
            _scannerConnectButton.Enabled = status != Models.CommunicationStatus.Connected;
            _scannerDisconnectButton.Enabled = status == Models.CommunicationStatus.Connected;
        }

        /// <summary>
        /// 更新Modbus状态
        /// </summary>
        private void UpdateModbusStatus(Models.CommunicationStatus status, string description)
        {
            _modbusStatusLabel.Text = $"状态: {GetStatusText(status)}";
            _modbusStatusLabel.ForeColor = GetStatusColor(status);
            
            _modbusConnectButton.Enabled = status != Models.CommunicationStatus.Connected;
            _modbusDisconnectButton.Enabled = status == Models.CommunicationStatus.Connected;
        }

        /// <summary>
        /// 更新机器人TCP/IP状态
        /// </summary>
        private void UpdateRobotTcpStatus(Models.CommunicationStatus status, string description)
        {
            _robotTcpStatusLabel.Text = $"状态: {GetStatusText(status)}";
            _robotTcpStatusLabel.ForeColor = GetStatusColor(status);
            
            _robotTcpConnectButton.Enabled = status != Models.CommunicationStatus.Connected;
            _robotTcpDisconnectButton.Enabled = status == Models.CommunicationStatus.Connected;
        }

        /// <summary>
        /// 获取状态文本
        /// </summary>
        private string GetStatusText(Models.CommunicationStatus status)
        {
            switch (status)
            {
                case Models.CommunicationStatus.Connected: return "已连接";
                case Models.CommunicationStatus.Connecting: return "连接中";
                case Models.CommunicationStatus.Disconnected: return "未连接";
                case Models.CommunicationStatus.Error: return "错误";
                default: return "未知";
            }
        }

        /// <summary>
        /// 获取状态颜色
        /// </summary>
        private Color GetStatusColor(Models.CommunicationStatus status)
        {
            switch (status)
            {
                case Models.CommunicationStatus.Connected: return Color.Green;
                case Models.CommunicationStatus.Connecting: return Color.Orange;
                case Models.CommunicationStatus.Disconnected: return Color.Black;
                case Models.CommunicationStatus.Error: return Color.Red;
                default: return Color.Gray;
            }
        }

        /// <summary>
        /// 获取Epson机器人的综合连接状态
        /// </summary>
        /// <returns>综合连接状态</returns>
        private Models.CommunicationStatus GetEpsonRobotCombinedStatus()
        {
            bool startStopConnected = EpsonRobotManager.Instance.IsStartStopConnected;
            bool dataConnected = EpsonRobotManager.Instance.IsDataConnected;

            // 如果两个连接都已连接，则状态为已连接
            if (startStopConnected && dataConnected)
            {
                return Models.CommunicationStatus.Connected;
            }
            // 如果有一个连接已连接，则状态为连接中
            else if (startStopConnected || dataConnected)
            {
                return Models.CommunicationStatus.Connecting;
            }
            // 如果都未连接，则状态为未连接
            else
            {
                return Models.CommunicationStatus.Disconnected;
            }
        }

        /// <summary>
        /// 添加日志
        /// </summary>
        private void AppendLog(string message)
        {
            string logEntry = $"[{DateTime.Now:HH:mm:ss}] {message}\r\n";
            _communicationLogTextBox.AppendText(logEntry);

            // 限制日志长度
            if (_communicationLogTextBox.Lines.Length > 1000)
            {
                var lines = _communicationLogTextBox.Lines;
                var newLines = new string[500];
                Array.Copy(lines, lines.Length - 500, newLines, 0, 500);
                _communicationLogTextBox.Lines = newLines;
            }
        }
        #endregion
    }
}
