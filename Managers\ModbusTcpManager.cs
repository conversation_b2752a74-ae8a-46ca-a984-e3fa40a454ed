using System;
using System.Net.Sockets;
using System.Threading.Tasks;
using MyHMI.Events;
using MyHMI.Models;
using MyHMI.Helpers;
using MyHMI.Settings;

namespace MyHMI.Managers
{
    /// <summary>
    /// Modbus TCP/IP管理器
    /// 负责SCARA机器人的Modbus TCP/IP通信，基于标准TCP/IP协议栈
    /// </summary>
    public class ModbusTcpManager
    {
        #region 单例模式
        private static readonly Lazy<ModbusTcpManager> _instance = new Lazy<ModbusTcpManager>(() => new ModbusTcpManager());
        public static ModbusTcpManager Instance => _instance.Value;
        private ModbusTcpManager() { }
        #endregion

        #region 事件定义
        /// <summary>
        /// 机器人响应接收事件
        /// </summary>
        public event EventHandler<RobotResponseReceivedEventArgs> ResponseReceived;

        /// <summary>
        /// 机器人错误事件
        /// </summary>
        public event EventHandler<RobotErrorEventArgs> RobotError;

        /// <summary>
        /// 通信状态变化事件
        /// </summary>
        public event EventHandler<CommunicationStatusChangedEventArgs> StatusChanged;
        #endregion

        #region 私有字段
        private TcpClient _tcpClient;
        private NetworkStream _networkStream;
        private bool _isInitialized = false;
        private CommunicationStatus _status = CommunicationStatus.Disconnected;
        private ModbusConfiguration _config;
        private readonly object _lockObject = new object();
        private int _transactionId = 0;
        #endregion

        #region 公共属性
        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// 当前连接状态
        /// </summary>
        public CommunicationStatus Status => _status;

        /// <summary>
        /// Modbus配置
        /// </summary>
        public ModbusConfiguration Configuration => _config;
        #endregion

        #region 初始化和释放
        /// <summary>
        /// 异步初始化Modbus TCP管理器
        /// </summary>
        /// <returns></returns>
        public async Task<bool> InitializeAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_isInitialized)
                {
                    LogHelper.Warning("ModbusTcpManager已经初始化，跳过重复初始化");
                    return true;
                }

                LogHelper.Info("开始初始化ModbusTcpManager...");

                // 加载配置
                LoadConfiguration();

                // 跳过连接验证 - 由第三方控制，不需要硬件连接验证
                LogHelper.Info("跳过Modbus TCP连接验证 - 由第三方控制");

                // 设置状态为已连接（模拟连接成功）
                UpdateStatus(CommunicationStatus.Connected, "Modbus TCP由第三方控制");

                _isInitialized = true;
                LogHelper.Info("ModbusTcpManager初始化完成");
                await Task.CompletedTask; // 消除CS1998警告
                return true;

            }, false, "ModbusTcpManager初始化");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public async Task DisposeAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info("开始释放ModbusTcpManager资源...");

                await DisconnectAsync();

                _isInitialized = false;
                LogHelper.Info("ModbusTcpManager资源释放完成");

                return true;
            }, false, "ModbusTcpManager资源释放");
        }
        #endregion

        #region 连接管理
        /// <summary>
        /// 异步连接Modbus TCP
        /// </summary>
        /// <returns></returns>
        public async Task<bool> ConnectAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_status == CommunicationStatus.Connected)
                {
                    LogHelper.Info("Modbus TCP已经连接");
                    return true;
                }

                UpdateStatus(CommunicationStatus.Connecting, "正在连接Modbus TCP...");

                lock (_lockObject)
                {
                    _tcpClient = new TcpClient();
                }

                // 设置连接超时
                var connectTask = _tcpClient.ConnectAsync(_config.TcpConfig.IPAddress, _config.TcpConfig.Port);
                var timeoutTask = Task.Delay(_config.TcpConfig.ConnectTimeout);

                var completedTask = await Task.WhenAny(connectTask, timeoutTask);
                
                if (completedTask == timeoutTask)
                {
                    throw new TimeoutException($"连接超时: {_config.TcpConfig.IPAddress}:{_config.TcpConfig.Port}");
                }

                if (connectTask.IsFaulted)
                {
                    throw connectTask.Exception?.InnerException ?? new Exception("连接失败");
                }

                lock (_lockObject)
                {
                    _networkStream = _tcpClient.GetStream();
                    _networkStream.ReadTimeout = _config.ReadTimeout;
                    _networkStream.WriteTimeout = _config.WriteTimeout;
                }

                UpdateStatus(CommunicationStatus.Connected, "Modbus TCP连接成功");
                LogHelper.Info($"Modbus TCP连接成功: {_config.TcpConfig.IPAddress}:{_config.TcpConfig.Port}");
                return true;

            }, false, "连接Modbus TCP");
        }

        /// <summary>
        /// 异步断开连接
        /// </summary>
        /// <returns></returns>
        public async Task<bool> DisconnectAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_status == CommunicationStatus.Disconnected)
                {
                    return true;
                }

                await Task.Run(() =>
                {
                    lock (_lockObject)
                    {
                        _networkStream?.Close();
                        _networkStream?.Dispose();
                        _networkStream = null;

                        _tcpClient?.Close();
                        _tcpClient?.Dispose();
                        _tcpClient = null;
                    }
                });

                UpdateStatus(CommunicationStatus.Disconnected, "Modbus TCP连接已断开");
                LogHelper.Info("Modbus TCP连接已断开");
                return true;

            }, false, "断开Modbus TCP连接");
        }

        /// <summary>
        /// 重新连接
        /// </summary>
        /// <returns></returns>
        public async Task<bool> ReconnectAsync()
        {
            LogHelper.Info("尝试重新连接Modbus TCP...");
            
            await DisconnectAsync();
            await Task.Delay(1000); // 等待1秒
            
            return await ConnectAsync();
        }
        #endregion

        #region Modbus操作
        /// <summary>
        /// 发送机器人指令
        /// </summary>
        /// <param name="command">机器人指令</param>
        /// <returns>是否成功发送</returns>
        public async Task<bool> SendRobotCommandAsync(RobotCommand command)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_status != CommunicationStatus.Connected)
                {
                    throw new InvalidOperationException("Modbus TCP未连接");
                }

                if (command == null)
                    throw new ArgumentNullException(nameof(command));

                LogHelper.Info($"发送机器人指令: {command.CommandType}, ID: {command.CommandId}");

                // 根据指令类型构建Modbus数据
                byte[] modbusData = BuildModbusCommand(command);
                
                // 发送数据
                await SendModbusDataAsync(modbusData);

                // 读取响应
                var response = await ReadModbusResponseAsync(command.CommandId);
                
                // 触发响应事件
                ResponseReceived?.Invoke(this, new RobotResponseReceivedEventArgs(response));

                return response.IsSuccess;

            }, false, $"发送机器人指令: {command?.CommandType}");
        }

        /// <summary>
        /// 写入保持寄存器
        /// </summary>
        /// <param name="startAddress">起始地址</param>
        /// <param name="values">数值数组</param>
        /// <returns>是否成功</returns>
        public async Task<bool> WriteHoldingRegistersAsync(ushort startAddress, ushort[] values)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_status != CommunicationStatus.Connected)
                {
                    throw new InvalidOperationException("Modbus TCP未连接");
                }

                LogHelper.Debug($"写入保持寄存器: 地址={startAddress}, 数量={values.Length}");

                // 构建Modbus写多个寄存器命令
                byte[] command = BuildWriteMultipleRegistersCommand(startAddress, values);
                
                // 发送命令
                await SendModbusDataAsync(command);

                // 读取响应
                byte[] response = await ReadModbusDataAsync();
                
                // 验证响应
                bool success = ValidateWriteResponse(response, startAddress, (ushort)values.Length);
                
                LogHelper.Debug($"写入保持寄存器{(success ? "成功" : "失败")}");
                return success;

            }, false, $"写入保持寄存器({startAddress})");
        }

        /// <summary>
        /// 读取保持寄存器
        /// </summary>
        /// <param name="startAddress">起始地址</param>
        /// <param name="quantity">读取数量</param>
        /// <returns>寄存器值数组</returns>
        public async Task<ushort[]> ReadHoldingRegistersAsync(ushort startAddress, ushort quantity)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_status != CommunicationStatus.Connected)
                {
                    throw new InvalidOperationException("Modbus TCP未连接");
                }

                LogHelper.Debug($"读取保持寄存器: 地址={startAddress}, 数量={quantity}");

                // 构建Modbus读寄存器命令
                byte[] command = BuildReadHoldingRegistersCommand(startAddress, quantity);
                
                // 发送命令
                await SendModbusDataAsync(command);

                // 读取响应
                byte[] response = await ReadModbusDataAsync();
                
                // 解析响应
                ushort[] values = ParseReadRegistersResponse(response);
                
                LogHelper.Debug($"读取保持寄存器成功，获得{values.Length}个值");
                return values;

            }, new ushort[0], $"读取保持寄存器({startAddress})");
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfiguration()
        {
            // 从新的Settings系统加载配置
            var communicationSettings = Settings.Settings.Current.Communication;

            _config = new ModbusConfiguration
            {
                SlaveId = (byte)communicationSettings.ModbusSlaveId,
                TcpConfig = new TcpConfiguration
                {
                    IPAddress = communicationSettings.ModbusTcpIP,
                    Port = communicationSettings.ModbusTcpPort,
                    ConnectTimeout = communicationSettings.ModbusConnectTimeout,
                    ReceiveTimeout = communicationSettings.ModbusReceiveTimeout,
                    SendTimeout = communicationSettings.ModbusSendTimeout
                },
                ReadTimeout = communicationSettings.ModbusReadTimeout,
                WriteTimeout = communicationSettings.ModbusWriteTimeout
            };

            LogHelper.Info($"Modbus TCP配置: {_config.TcpConfig.IPAddress}:{_config.TcpConfig.Port}, SlaveId: {_config.SlaveId}");
        }

        /// <summary>
        /// 更新连接状态
        /// </summary>
        /// <param name="status">新状态</param>
        /// <param name="description">状态描述</param>
        private void UpdateStatus(CommunicationStatus status, string description)
        {
            if (_status != status)
            {
                _status = status;
                LogHelper.Info($"Modbus TCP状态变更: {status} - {description}");
                StatusChanged?.Invoke(this, new CommunicationStatusChangedEventArgs("ModbusTCP", status, description));
            }
        }

        /// <summary>
        /// 构建机器人指令的Modbus数据
        /// </summary>
        /// <param name="command">机器人指令</param>
        /// <returns>Modbus数据</returns>
        private byte[] BuildModbusCommand(RobotCommand command)
        {
            // 模拟构建Modbus指令
            // 实际实现需要根据具体的机器人协议来构建

            ushort startAddress = 0;
            ushort[] values = new ushort[10]; // 示例：10个寄存器

            // 根据指令类型设置不同的寄存器值
            switch (command.CommandType)
            {
                case RobotCommandType.MoveTo:
                    startAddress = 100; // 运动指令寄存器起始地址
                    values[0] = 1; // 指令类型
                    // 坐标转换为寄存器值（简化处理）
                    for (int i = 0; i < Math.Min(6, command.Coordinates.Length); i++)
                    {
                        values[i + 1] = (ushort)(command.Coordinates[i] * 100); // 放大100倍存储
                    }
                    break;

                case RobotCommandType.Grip:
                    startAddress = 200;
                    values[0] = 2;
                    break;

                case RobotCommandType.Release:
                    startAddress = 200;
                    values[0] = 3;
                    break;

                default:
                    startAddress = 300;
                    values[0] = (ushort)command.CommandType;
                    break;
            }

            return BuildWriteMultipleRegistersCommand(startAddress, values);
        }

        /// <summary>
        /// 构建写多个寄存器的Modbus命令
        /// </summary>
        /// <param name="startAddress">起始地址</param>
        /// <param name="values">数值数组</param>
        /// <returns>Modbus命令</returns>
        private byte[] BuildWriteMultipleRegistersCommand(ushort startAddress, ushort[] values)
        {
            var command = new byte[13 + values.Length * 2];
            int index = 0;

            // MBAP Header
            command[index++] = (byte)(_transactionId >> 8);
            command[index++] = (byte)(_transactionId & 0xFF);
            _transactionId++;

            command[index++] = 0x00; // Protocol Identifier
            command[index++] = 0x00;

            ushort length = (ushort)(7 + values.Length * 2);
            command[index++] = (byte)(length >> 8);
            command[index++] = (byte)(length & 0xFF);

            command[index++] = _config.SlaveId; // Unit Identifier

            // PDU
            command[index++] = 0x10; // Function Code: Write Multiple Registers

            command[index++] = (byte)(startAddress >> 8);
            command[index++] = (byte)(startAddress & 0xFF);

            ushort quantity = (ushort)values.Length;
            command[index++] = (byte)(quantity >> 8);
            command[index++] = (byte)(quantity & 0xFF);

            command[index++] = (byte)(values.Length * 2); // Byte Count

            // Data
            foreach (ushort value in values)
            {
                command[index++] = (byte)(value >> 8);
                command[index++] = (byte)(value & 0xFF);
            }

            return command;
        }

        /// <summary>
        /// 构建读保持寄存器的Modbus命令
        /// </summary>
        /// <param name="startAddress">起始地址</param>
        /// <param name="quantity">读取数量</param>
        /// <returns>Modbus命令</returns>
        private byte[] BuildReadHoldingRegistersCommand(ushort startAddress, ushort quantity)
        {
            var command = new byte[12];
            int index = 0;

            // MBAP Header
            command[index++] = (byte)(_transactionId >> 8);
            command[index++] = (byte)(_transactionId & 0xFF);
            _transactionId++;

            command[index++] = 0x00; // Protocol Identifier
            command[index++] = 0x00;
            command[index++] = 0x00; // Length
            command[index++] = 0x06;
            command[index++] = _config.SlaveId; // Unit Identifier

            // PDU
            command[index++] = 0x03; // Function Code: Read Holding Registers
            command[index++] = (byte)(startAddress >> 8);
            command[index++] = (byte)(startAddress & 0xFF);
            command[index++] = (byte)(quantity >> 8);
            command[index++] = (byte)(quantity & 0xFF);

            return command;
        }

        /// <summary>
        /// 发送Modbus数据
        /// </summary>
        /// <param name="data">要发送的数据</param>
        /// <returns></returns>
        private async Task SendModbusDataAsync(byte[] data)
        {
            if (_networkStream == null || !_networkStream.CanWrite)
                throw new InvalidOperationException("网络流不可写");

            await _networkStream.WriteAsync(data, 0, data.Length);
            await _networkStream.FlushAsync();

            LogHelper.Debug($"发送Modbus数据: {BitConverter.ToString(data)}");
        }

        /// <summary>
        /// 读取Modbus数据
        /// </summary>
        /// <returns>接收到的数据</returns>
        private async Task<byte[]> ReadModbusDataAsync()
        {
            if (_networkStream == null || !_networkStream.CanRead)
                throw new InvalidOperationException("网络流不可读");

            var buffer = new byte[1024];
            int bytesRead = await _networkStream.ReadAsync(buffer, 0, buffer.Length);

            var result = new byte[bytesRead];
            Array.Copy(buffer, result, bytesRead);

            LogHelper.Debug($"接收Modbus数据: {BitConverter.ToString(result)}");
            return result;
        }

        /// <summary>
        /// 读取Modbus响应并构建机器人响应
        /// </summary>
        /// <param name="commandId">指令ID</param>
        /// <returns>机器人响应</returns>
        private async Task<RobotResponse> ReadModbusResponseAsync(string commandId)
        {
            try
            {
                byte[] responseData = await ReadModbusDataAsync();

                // 简化的响应解析
                bool isSuccess = responseData.Length > 7 && responseData[7] == 0x10; // 写寄存器成功响应

                return new RobotResponse
                {
                    CommandId = commandId,
                    IsSuccess = isSuccess,
                    Data = BitConverter.ToString(responseData),
                    ResponseTime = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                return new RobotResponse
                {
                    CommandId = commandId,
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    ResponseTime = DateTime.Now
                };
            }
        }

        /// <summary>
        /// 验证写操作响应
        /// </summary>
        /// <param name="response">响应数据</param>
        /// <param name="startAddress">起始地址</param>
        /// <param name="quantity">数量</param>
        /// <returns>是否成功</returns>
        private bool ValidateWriteResponse(byte[] response, ushort startAddress, ushort quantity)
        {
            if (response.Length < 12)
                return false;

            // 检查功能码
            if (response[7] != 0x10)
                return false;

            // 检查地址和数量
            ushort responseAddress = (ushort)((response[8] << 8) | response[9]);
            ushort responseQuantity = (ushort)((response[10] << 8) | response[11]);

            return responseAddress == startAddress && responseQuantity == quantity;
        }

        /// <summary>
        /// 解析读寄存器响应
        /// </summary>
        /// <param name="response">响应数据</param>
        /// <returns>寄存器值数组</returns>
        private ushort[] ParseReadRegistersResponse(byte[] response)
        {
            if (response.Length < 9)
                throw new InvalidOperationException("响应数据长度不足");

            if (response[7] != 0x03)
                throw new InvalidOperationException("功能码错误");

            byte byteCount = response[8];
            int registerCount = byteCount / 2;
            var values = new ushort[registerCount];

            for (int i = 0; i < registerCount; i++)
            {
                int dataIndex = 9 + i * 2;
                values[i] = (ushort)((response[dataIndex] << 8) | response[dataIndex + 1]);
            }

            return values;
        }
        #endregion
    }
}
