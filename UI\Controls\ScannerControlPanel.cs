using System;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using MyHMI.Helpers;
using MyHMI.Managers;
using MyHMI.Models;
using MyHMI.Settings;
using MyHMI.Events;

namespace MyHMI.UI.Controls
{
    /// <summary>
    /// 扫描器控制面板 - 支持3个独立的扫描枪控制模块
    /// </summary>
    public partial class ScannerControlPanel : UserControl
    {
        #region 私有字段
        private Panel _mainPanel;
        private Label _titleLabel;
        
        // 多扫描枪管理器
        private MultiScannerManager _multiScannerManager;
        
        // 扫描枪模块面板
        private Panel[] _scannerPanels = new Panel[3];
        private ScannerModuleControl[] _scannerModules = new ScannerModuleControl[3];
        #endregion

        #region 构造函数
        public ScannerControlPanel()
        {
            InitializeComponent();
            InitializeMultiScannerManager();
            InitializeInterface();
        }
        #endregion

        #region 初始化方法
        /// <summary>
        /// 初始化多扫描枪管理器
        /// </summary>
        private void InitializeMultiScannerManager()
        {
            try
            {
                _multiScannerManager = MultiScannerManager.Instance;
                LogHelper.Info("扫描器控制面板获取MultiScannerManager实例成功");
            }
            catch (Exception ex)
            {
                LogHelper.Error("扫描器控制面板获取MultiScannerManager实例失败", ex);
            }
        }

        /// <summary>
        /// 初始化界面
        /// </summary>
        private void InitializeInterface()
        {
            try
            {
                this.BackColor = ColorTranslator.FromHtml("#34495e");
                this.Dock = DockStyle.Fill;
                this.Padding = new Padding(0);

                // 创建主面板
                CreateMainPanel();

                // 创建标题
                CreateTitle();

                // 创建3个扫描枪模块
                CreateScannerModules();

                LogHelper.Info("扫描器控制面板初始化完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("扫描器控制面板初始化失败", ex);
            }
        }

        /// <summary>
        /// 创建主面板
        /// </summary>
        private void CreateMainPanel()
        {
            _mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = ColorTranslator.FromHtml("#34495e"),
                Padding = new Padding(20)
            };

            this.Controls.Add(_mainPanel);
        }

        /// <summary>
        /// 创建标题
        /// </summary>
        private void CreateTitle()
        {
            _titleLabel = new Label
            {
                Text = "扫描器控制",
                Font = new Font("微软雅黑", 20F, FontStyle.Bold),
                ForeColor = ColorTranslator.FromHtml("#3498db"),
                Size = new Size(300, 30),
                Location = new Point(0, 0),
                AutoSize = true
            };

            _mainPanel.Controls.Add(_titleLabel);
        }

        /// <summary>
        /// 创建3个扫描枪模块
        /// </summary>
        private void CreateScannerModules()
        {
            for (int i = 0; i < 3; i++)
            {
                int scannerId = i + 1;
                
                // 创建扫描枪模块面板 - 增加高度适应新布局
                _scannerPanels[i] = new Panel
                {
                    Size = new Size(650, 290),  // 增加宽度和高度
                    Location = new Point(0, 50 + i * 310), // 增加模块间隔
                    BackColor = ColorTranslator.FromHtml("#2c3e50"),
                    BorderStyle = BorderStyle.FixedSingle,
                    Padding = new Padding(15)
                };

                // 创建扫描枪模块控件
                _scannerModules[i] = new ScannerModuleControl(scannerId, _multiScannerManager)
                {
                    Dock = DockStyle.Fill
                };

                // 订阅串口状态变化事件
                _scannerModules[i].OnPortStatusChanged += RefreshAllScannerPorts;

                _scannerPanels[i].Controls.Add(_scannerModules[i]);
                _mainPanel.Controls.Add(_scannerPanels[i]);
            }
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 异步初始化扫描器管理器
        /// </summary>
        /// <returns></returns>
        public async Task InitializeAsync()
        {
            try
            {
                if (_multiScannerManager != null && !_multiScannerManager.IsInitialized)
                {
                    await _multiScannerManager.InitializeAsync();
                }

                // 初始化所有扫描枪模块
                for (int i = 0; i < 3; i++)
                {
                    await _scannerModules[i].InitializeAsync();
                }

                LogHelper.Info("扫描器控制面板异步初始化完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("扫描器控制面板异步初始化失败", ex);
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public async Task DisposeAsync()
        {
            try
            {
                // 释放所有扫描枪模块
                for (int i = 0; i < 3; i++)
                {
                    if (_scannerModules[i] != null)
                    {
                        await _scannerModules[i].DisposeAsync();
                    }
                }

                LogHelper.Info("扫描器控制面板资源释放完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("扫描器控制面板资源释放失败", ex);
            }
        }

        /// <summary>
        /// 刷新所有扫描枪的串口列表
        /// </summary>
        private void RefreshAllScannerPorts()
        {
            try
            {
                for (int i = 0; i < 3; i++)
                {
                    if (_scannerModules[i] != null)
                    {
                        _scannerModules[i].RefreshSerialPorts();
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("刷新所有扫描枪串口列表失败", ex);
            }
        }
        #endregion
    }

    /// <summary>
    /// 单个扫描枪模块控件
    /// </summary>
    public partial class ScannerModuleControl : UserControl
    {
        #region 私有字段
        private int _scannerId;
        private MultiScannerManager _multiScannerManager;
        private bool _isLoadingConfiguration = false; // 配置加载保护标志位
        
        // UI控件
        private Label _titleLabel;
        private ComboBox _portComboBox;
        private ComboBox _baudRateComboBox;
        private ComboBox _dataBitsComboBox;
        private ComboBox _parityComboBox;
        private ComboBox _stopBitsComboBox;
        private TextBox _sendDataTextBox;
        private Button _sendButton;
        private Button _connectButton;
        private Button _disconnectButton;
        private TextBox _receiveDataTextBox;
        private Label _statusLabel;

        // 串口状态变化事件
        public event Action OnPortStatusChanged;
        #endregion

        #region 构造函数
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="scannerId">扫描枪ID</param>
        /// <param name="multiScannerManager">多扫描枪管理器</param>
        public ScannerModuleControl(int scannerId, MultiScannerManager multiScannerManager)
        {
            _scannerId = scannerId;
            _multiScannerManager = multiScannerManager;
            
            InitializeComponent();
            InitializeInterface();
        }
        #endregion

        #region 初始化方法
        /// <summary>
        /// 初始化界面
        /// </summary>
        private void InitializeInterface()
        {
            try
            {
                this.BackColor = ColorTranslator.FromHtml("#2c3e50");
                this.Dock = DockStyle.Fill;

                CreateTitle();
                CreateSerialPortConfig();
                CreateDataControls();
                CreateStatusDisplay();

                LogHelper.Info($"扫描枪{_scannerId}模块界面初始化完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error($"扫描枪{_scannerId}模块界面初始化失败", ex);
            }
        }

        /// <summary>
        /// 创建标题
        /// </summary>
        private void CreateTitle()
        {
            _titleLabel = new Label
            {
                Text = $"{_scannerId}号扫描枪",
                Font = new Font("微软雅黑", 14F, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(150, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };

            this.Controls.Add(_titleLabel);
        }
        #endregion

        #region 异步方法
        /// <summary>
        /// 异步初始化 - 增加配置加载功能
        /// </summary>
        /// <returns></returns>
        public async Task InitializeAsync()
        {
            await Task.Run(() =>
            {
                LogHelper.Info($"开始初始化扫描枪{_scannerId}模块...");

                // 1. 刷新串口列表（不自动选择端口）
                LogHelper.Debug($"扫描枪{_scannerId}开始刷新串口列表...");
                RefreshSerialPorts();

                // 2. 从Settings加载配置到UI控件（智能端口选择）
                LogHelper.Debug($"扫描枪{_scannerId}开始加载保存的配置...");
                LoadConfigurationToUI();

                // 3. 订阅UI控件变更事件，实现实时保存
                LogHelper.Debug($"扫描枪{_scannerId}开始订阅配置变更事件...");
                SubscribeToConfigurationChanges();

                // 4. 订阅扫描器事件
                if (_multiScannerManager != null)
                {
                    _multiScannerManager.BarcodeScanned += OnBarcodeScanned;
                    _multiScannerManager.StatusChanged += OnStatusChanged;
                }

                LogHelper.Info($"扫描枪{_scannerId}模块初始化完成");
            });
        }

        /// <summary>
        /// 异步释放资源 - 增加配置事件取消订阅
        /// </summary>
        /// <returns></returns>
        public async Task DisposeAsync()
        {
            await Task.Run(() =>
            {
                // 1. 取消配置变更事件订阅
                UnsubscribeFromConfigurationChanges();

                // 2. 取消扫描器事件订阅
                if (_multiScannerManager != null)
                {
                    _multiScannerManager.BarcodeScanned -= OnBarcodeScanned;
                    _multiScannerManager.StatusChanged -= OnStatusChanged;
                }
            });
        }

        /// <summary>
        /// 取消订阅UI控件变更事件
        /// </summary>
        private void UnsubscribeFromConfigurationChanges()
        {
            try
            {
                // 取消所有配置控件的变更事件订阅
                if (_portComboBox != null)
                    _portComboBox.SelectedIndexChanged -= OnConfigurationChanged;
                if (_baudRateComboBox != null)
                    _baudRateComboBox.SelectedIndexChanged -= OnConfigurationChanged;
                if (_dataBitsComboBox != null)
                    _dataBitsComboBox.SelectedIndexChanged -= OnConfigurationChanged;
                if (_stopBitsComboBox != null)
                    _stopBitsComboBox.SelectedIndexChanged -= OnConfigurationChanged;
                if (_parityComboBox != null)
                    _parityComboBox.SelectedIndexChanged -= OnConfigurationChanged;

                LogHelper.Debug($"扫描枪{_scannerId}配置变更事件取消订阅完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error($"扫描枪{_scannerId}配置变更事件取消订阅失败", ex);
            }
        }
        #endregion

        #region 配置管理方法
        /// <summary>
        /// 从Settings加载配置到UI控件 - 添加配置加载保护机制
        /// </summary>
        private void LoadConfigurationToUI()
        {
            try
            {
                // 设置配置加载保护标志，防止触发参数保存事件
                _isLoadingConfiguration = true;

                var communicationSettings = Settings.Settings.Current.Communication;

                // 根据扫描器ID加载对应的配置
                switch (_scannerId)
                {
                    case 1:
                        LoadScannerConfiguration(
                            communicationSettings.MultiScanner1PortName,
                            communicationSettings.MultiScanner1BaudRate,
                            communicationSettings.MultiScanner1DataBits,
                            communicationSettings.MultiScanner1StopBits,
                            communicationSettings.MultiScanner1Parity
                        );
                        break;
                    case 2:
                        LoadScannerConfiguration(
                            communicationSettings.MultiScanner2PortName,
                            communicationSettings.MultiScanner2BaudRate,
                            communicationSettings.MultiScanner2DataBits,
                            communicationSettings.MultiScanner2StopBits,
                            communicationSettings.MultiScanner2Parity
                        );
                        break;
                    case 3:
                        LoadScannerConfiguration(
                            communicationSettings.MultiScanner3PortName,
                            communicationSettings.MultiScanner3BaudRate,
                            communicationSettings.MultiScanner3DataBits,
                            communicationSettings.MultiScanner3StopBits,
                            communicationSettings.MultiScanner3Parity
                        );
                        break;
                }

                LogHelper.Info($"扫描枪{_scannerId}配置加载到UI完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error($"扫描枪{_scannerId}配置加载到UI失败", ex);
            }
            finally
            {
                // 清除配置加载保护标志，恢复参数保存事件响应
                _isLoadingConfiguration = false;
            }
        }

        /// <summary>
        /// 加载具体的扫描器配置到UI控件 - 增强智能端口选择逻辑
        /// </summary>
        private void LoadScannerConfiguration(string portName, int baudRate, int dataBits, string stopBits, string parity)
        {
            // 智能端口选择逻辑
            if (!string.IsNullOrEmpty(portName))
            {
                // 检查保存的端口是否在可用端口列表中
                bool portFound = false;
                for (int i = 0; i < _portComboBox.Items.Count; i++)
                {
                    var item = _portComboBox.Items[i].ToString();
                    // 支持带状态的端口名称匹配（如"COM3（1号扫码器）"）
                    if (item == portName || item.StartsWith(portName + "（"))
                    {
                        _portComboBox.SelectedIndex = i;
                        portFound = true;
                        LogHelper.Info($"扫描枪{_scannerId}自动选择保存的端口: {portName} -> {item}");
                        break;
                    }
                }

                if (!portFound)
                {
                    // 保存的端口不可用，留空并记录警告
                    _portComboBox.SelectedIndex = -1;
                    LogHelper.Warning($"扫描枪{_scannerId}保存的端口{portName}不可用，当前可用端口: [{string.Join(", ", _portComboBox.Items.Cast<object>().Select(x => x.ToString()))}]，请重新选择端口");
                }
            }
            else
            {
                // 没有保存的端口配置，留空等待用户选择
                _portComboBox.SelectedIndex = -1;
                LogHelper.Info($"扫描枪{_scannerId}没有保存的端口配置，等待用户手动选择");
            }

            // 设置波特率
            if (_baudRateComboBox.Items.Contains(baudRate))
            {
                _baudRateComboBox.SelectedItem = baudRate;
            }

            // 设置数据位
            if (_dataBitsComboBox.Items.Contains(dataBits))
            {
                _dataBitsComboBox.SelectedItem = dataBits;
            }

            // 设置停止位
            if (!string.IsNullOrEmpty(stopBits) && _stopBitsComboBox.Items.Contains(stopBits))
            {
                _stopBitsComboBox.SelectedItem = stopBits;
            }

            // 设置校验位
            if (!string.IsNullOrEmpty(parity) && _parityComboBox.Items.Contains(parity))
            {
                _parityComboBox.SelectedItem = parity;
            }

            LogHelper.Debug($"扫描枪{_scannerId}配置加载完成: 端口={portName}({(_portComboBox.SelectedIndex >= 0 ? "已选择" : "未选择")}), 波特率={baudRate}, 数据位={dataBits}, 停止位={stopBits}, 校验位={parity}");
        }

        /// <summary>
        /// 订阅UI控件变更事件，实现实时配置保存
        /// </summary>
        private void SubscribeToConfigurationChanges()
        {
            // 订阅所有配置控件的变更事件
            _portComboBox.SelectedIndexChanged += OnConfigurationChanged;
            _baudRateComboBox.SelectedIndexChanged += OnConfigurationChanged;
            _dataBitsComboBox.SelectedIndexChanged += OnConfigurationChanged;
            _stopBitsComboBox.SelectedIndexChanged += OnConfigurationChanged;
            _parityComboBox.SelectedIndexChanged += OnConfigurationChanged;

            LogHelper.Debug($"扫描枪{_scannerId}配置变更事件订阅完成");
        }

        /// <summary>
        /// 配置变更事件处理 - 实时保存配置（添加配置加载保护）
        /// </summary>
        private async void OnConfigurationChanged(object sender, EventArgs e)
        {
            try
            {
                // 如果正在加载配置，忽略变更事件，防止意外保存
                if (_isLoadingConfiguration)
                {
                    LogHelper.Debug($"扫描枪{_scannerId}正在加载配置，忽略配置变更事件");
                    return;
                }

                LogHelper.Debug($"扫描枪{_scannerId}配置变更事件触发");

                // 延迟保存，避免频繁保存
                await Task.Delay(500);

                // 保存当前UI配置到Settings
                await SaveConfigurationFromUI();
            }
            catch (Exception ex)
            {
                LogHelper.Error($"扫描枪{_scannerId}配置变更处理失败", ex);
            }
        }

        /// <summary>
        /// 从UI控件保存配置到Settings
        /// </summary>
        private async Task SaveConfigurationFromUI()
        {
            await Task.Run(() =>
            {
                try
                {
                    var communicationSettings = Settings.Settings.Current.Communication;

                    // 获取当前UI控件的值
                    string portName = _portComboBox.SelectedItem?.ToString() ?? "";
                    int baudRate = (int)(_baudRateComboBox.SelectedItem ?? 115200);
                    int dataBits = (int)(_dataBitsComboBox.SelectedItem ?? 8);
                    string stopBits = _stopBitsComboBox.SelectedItem?.ToString() ?? "1";
                    string parity = _parityComboBox.SelectedItem?.ToString() ?? "无校验";

                    // 根据扫描器ID保存到对应的配置
                    switch (_scannerId)
                    {
                        case 1:
                            communicationSettings.MultiScanner1PortName = portName;
                            communicationSettings.MultiScanner1BaudRate = baudRate;
                            communicationSettings.MultiScanner1DataBits = dataBits;
                            communicationSettings.MultiScanner1StopBits = stopBits;
                            communicationSettings.MultiScanner1Parity = parity;
                            break;
                        case 2:
                            communicationSettings.MultiScanner2PortName = portName;
                            communicationSettings.MultiScanner2BaudRate = baudRate;
                            communicationSettings.MultiScanner2DataBits = dataBits;
                            communicationSettings.MultiScanner2StopBits = stopBits;
                            communicationSettings.MultiScanner2Parity = parity;
                            break;
                        case 3:
                            communicationSettings.MultiScanner3PortName = portName;
                            communicationSettings.MultiScanner3BaudRate = baudRate;
                            communicationSettings.MultiScanner3DataBits = dataBits;
                            communicationSettings.MultiScanner3StopBits = stopBits;
                            communicationSettings.MultiScanner3Parity = parity;
                            break;
                    }

                    // 保存到文件
                    bool saveSuccess = Settings.Settings.Save();

                    if (saveSuccess)
                    {
                        LogHelper.Info($"扫描枪{_scannerId}配置保存成功: {portName}, {baudRate}, {dataBits}, {stopBits}, {parity}");
                    }
                    else
                    {
                        LogHelper.Warning($"扫描枪{_scannerId}配置保存到文件失败: {portName}, {baudRate}, {dataBits}, {stopBits}, {parity}");
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"扫描枪{_scannerId}配置保存失败", ex);
                }
            });
        }

        /// <summary>
        /// 重置配置到默认值
        /// </summary>
        public async Task ResetConfigurationToDefault()
        {
            await Task.Run(() =>
            {
                try
                {
                    LogHelper.Info($"扫描枪{_scannerId}开始重置配置到默认值");

                    // 设置默认配置值
                    var defaultConfig = GetDefaultConfiguration();

                    // 更新UI控件
                    this.Invoke(new Action(() =>
                    {
                        LoadScannerConfiguration(
                            defaultConfig.PortName,
                            defaultConfig.BaudRate,
                            defaultConfig.DataBits,
                            defaultConfig.StopBits,
                            defaultConfig.Parity
                        );
                    }));

                    // 保存默认配置到Settings
                    var communicationSettings = Settings.Settings.Current.Communication;
                    switch (_scannerId)
                    {
                        case 1:
                            communicationSettings.MultiScanner1PortName = defaultConfig.PortName;
                            communicationSettings.MultiScanner1BaudRate = defaultConfig.BaudRate;
                            communicationSettings.MultiScanner1DataBits = defaultConfig.DataBits;
                            communicationSettings.MultiScanner1StopBits = defaultConfig.StopBits;
                            communicationSettings.MultiScanner1Parity = defaultConfig.Parity;
                            break;
                        case 2:
                            communicationSettings.MultiScanner2PortName = defaultConfig.PortName;
                            communicationSettings.MultiScanner2BaudRate = defaultConfig.BaudRate;
                            communicationSettings.MultiScanner2DataBits = defaultConfig.DataBits;
                            communicationSettings.MultiScanner2StopBits = defaultConfig.StopBits;
                            communicationSettings.MultiScanner2Parity = defaultConfig.Parity;
                            break;
                        case 3:
                            communicationSettings.MultiScanner3PortName = defaultConfig.PortName;
                            communicationSettings.MultiScanner3BaudRate = defaultConfig.BaudRate;
                            communicationSettings.MultiScanner3DataBits = defaultConfig.DataBits;
                            communicationSettings.MultiScanner3StopBits = defaultConfig.StopBits;
                            communicationSettings.MultiScanner3Parity = defaultConfig.Parity;
                            break;
                    }

                    Settings.Settings.Save();
                    LogHelper.Info($"扫描枪{_scannerId}配置重置完成");
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"扫描枪{_scannerId}配置重置失败", ex);
                }
            });
        }

        /// <summary>
        /// 获取默认配置
        /// </summary>
        private (string PortName, int BaudRate, int DataBits, string StopBits, string Parity) GetDefaultConfiguration()
        {
            return (
                PortName: $"COM{_scannerId}",  // 默认使用COM1, COM2, COM3
                BaudRate: 115200,
                DataBits: 8,
                StopBits: "1",
                Parity: "无校验"
            );
        }
        #endregion

        #region UI创建方法
        /// <summary>
        /// 创建串口配置控件
        /// </summary>
        private void CreateSerialPortConfig()
        {
            // === 第一行：串口、波特率、数据位 ===

            // 串口选择
            var portLabel = new Label
            {
                Text = "串口:",
                Font = new Font("微软雅黑", 10F),
                ForeColor = Color.White,
                Size = new Size(40, 20),
                Location = new Point(0, 35)
            };

            _portComboBox = new ComboBox
            {
                Font = new Font("微软雅黑", 10F),
                Size = new Size(90, 25),  // 增加宽度
                Location = new Point(45, 33),
                DropDownStyle = ComboBoxStyle.DropDownList,
                BackColor = ColorTranslator.FromHtml("#34495e"),
                ForeColor = Color.White
            };
            _portComboBox.DropDown += (s, e) => RefreshSerialPorts();

            // 波特率
            var baudLabel = new Label
            {
                Text = "波特率:",
                Font = new Font("微软雅黑", 10F),
                ForeColor = Color.White,
                Size = new Size(50, 20),
                Location = new Point(150, 35)
            };

            _baudRateComboBox = new ComboBox
            {
                Font = new Font("微软雅黑", 10F),
                Size = new Size(90, 25),  // 增加宽度
                Location = new Point(205, 33),
                DropDownStyle = ComboBoxStyle.DropDownList,
                BackColor = ColorTranslator.FromHtml("#34495e"),
                ForeColor = Color.White
            };
            // 从配置加载波特率选项
            var baudRates = new int[] { 9600, 19200, 38400, 57600, 115200 };
            _baudRateComboBox.Items.AddRange(baudRates.Cast<object>().ToArray());

            // 从配置加载默认波特率
            var defaultBaudRate = GetDefaultBaudRateFromConfig();
            _baudRateComboBox.SelectedItem = defaultBaudRate;

            // 数据位
            var dataBitsLabel = new Label
            {
                Text = "数据位:",
                Font = new Font("微软雅黑", 10F),
                ForeColor = Color.White,
                Size = new Size(50, 20),
                Location = new Point(310, 35)
            };

            _dataBitsComboBox = new ComboBox
            {
                Font = new Font("微软雅黑", 10F),
                Size = new Size(60, 25),  // 增加宽度
                Location = new Point(365, 33),
                DropDownStyle = ComboBoxStyle.DropDownList,
                BackColor = ColorTranslator.FromHtml("#34495e"),
                ForeColor = Color.White
            };
            _dataBitsComboBox.Items.AddRange(new object[] { 5, 6, 7, 8 });
            _dataBitsComboBox.SelectedItem = 8;

            // === 第二行：校验、停止位 ===

            // 奇偶校验
            var parityLabel = new Label
            {
                Text = "校验:",
                Font = new Font("微软雅黑", 10F),
                ForeColor = Color.White,
                Size = new Size(40, 20),
                Location = new Point(0, 70)  // 第二行
            };

            _parityComboBox = new ComboBox
            {
                Font = new Font("微软雅黑", 10F),
                Size = new Size(80, 25),  // 增加宽度
                Location = new Point(45, 68),  // 第二行
                DropDownStyle = ComboBoxStyle.DropDownList,
                BackColor = ColorTranslator.FromHtml("#34495e"),
                ForeColor = Color.White
            };
            _parityComboBox.Items.AddRange(new object[] { "无校验", "奇校验", "偶校验" });
            _parityComboBox.SelectedItem = "无校验";

            // 停止位
            var stopBitsLabel = new Label
            {
                Text = "停止位:",
                Font = new Font("微软雅黑", 10F),
                ForeColor = Color.White,
                Size = new Size(50, 20),
                Location = new Point(140, 70)  // 第二行
            };

            _stopBitsComboBox = new ComboBox
            {
                Font = new Font("微软雅黑", 10F),
                Size = new Size(70, 25),  // 适当宽度
                Location = new Point(195, 68),  // 第二行
                DropDownStyle = ComboBoxStyle.DropDownList,
                BackColor = ColorTranslator.FromHtml("#34495e"),
                ForeColor = Color.White
            };
            _stopBitsComboBox.Items.AddRange(new object[] { "1", "1.5", "2" });
            _stopBitsComboBox.SelectedItem = "1";

            this.Controls.AddRange(new Control[] {
                // 第一行控件
                portLabel, _portComboBox,
                baudLabel, _baudRateComboBox,
                dataBitsLabel, _dataBitsComboBox,
                // 第二行控件
                parityLabel, _parityComboBox,
                stopBitsLabel, _stopBitsComboBox
            });
        }

        /// <summary>
        /// 创建数据控件 - 调整位置适应新的两行布局
        /// </summary>
        private void CreateDataControls()
        {
            // 连接按钮 - 移到第二行右侧
            _connectButton = new Button
            {
                Text = "连接",
                Font = new Font("微软雅黑", 10F),
                Size = new Size(60, 30),
                Location = new Point(280, 68),  // 调整到第二行右侧
                BackColor = ColorTranslator.FromHtml("#27ae60"),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
            _connectButton.FlatAppearance.BorderSize = 0;
            _connectButton.Click += ConnectButton_Click;

            // 断开按钮
            _disconnectButton = new Button
            {
                Text = "断开",
                Font = new Font("微软雅黑", 10F),
                Size = new Size(60, 30),
                Location = new Point(350, 68),  // 调整到连接按钮右侧
                BackColor = ColorTranslator.FromHtml("#e74c3c"),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false,
                Enabled = false
            };
            _disconnectButton.FlatAppearance.BorderSize = 0;
            _disconnectButton.Click += DisconnectButton_Click;

            // 发送数据文本框 - 调整到第三行
            var sendLabel = new Label
            {
                Text = "发送数据:",
                Font = new Font("微软雅黑", 10F),
                ForeColor = Color.White,
                Size = new Size(70, 20),
                Location = new Point(0, 110)  // 第三行
            };

            _sendDataTextBox = new TextBox
            {
                Font = new Font("微软雅黑", 10F),
                Size = new Size(250, 25),  // 增加宽度
                Location = new Point(75, 108),  // 第三行
                BackColor = ColorTranslator.FromHtml("#34495e"),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // 发送按钮
            _sendButton = new Button
            {
                Text = "发送",
                Font = new Font("微软雅黑", 10F),
                Size = new Size(60, 30),
                Location = new Point(335, 105),  // 第三行
                BackColor = ColorTranslator.FromHtml("#3498db"),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false,
                Enabled = false
            };
            _sendButton.FlatAppearance.BorderSize = 0;
            _sendButton.Click += SendButton_Click;

            this.Controls.AddRange(new Control[] {
                _connectButton, _disconnectButton,
                sendLabel, _sendDataTextBox, _sendButton
            });
        }

        /// <summary>
        /// 创建状态显示 - 调整位置适应新布局
        /// </summary>
        private void CreateStatusDisplay()
        {
            // 状态标签 - 调整到第四行
            var statusTitleLabel = new Label
            {
                Text = "状态:",
                Font = new Font("微软雅黑", 10F),
                ForeColor = Color.White,
                Size = new Size(40, 20),
                Location = new Point(0, 150)  // 调整位置
            };

            _statusLabel = new Label
            {
                Text = "未连接",
                Font = new Font("微软雅黑", 10F),
                ForeColor = ColorTranslator.FromHtml("#e74c3c"),
                Size = new Size(100, 20),
                Location = new Point(45, 150)  // 调整位置
            };

            // 接收数据显示区域
            var receiveLabel = new Label
            {
                Text = "接收数据:",
                Font = new Font("微软雅黑", 10F),
                ForeColor = Color.White,
                Size = new Size(70, 20),
                Location = new Point(0, 180)  // 调整位置
            };

            _receiveDataTextBox = new TextBox
            {
                Font = new Font("微软雅黑", 9F),
                Size = new Size(520, 80),  // 增加宽度
                Location = new Point(75, 180),  // 调整位置
                BackColor = ColorTranslator.FromHtml("#34495e"),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true
            };

            this.Controls.AddRange(new Control[] {
                statusTitleLabel, _statusLabel,
                receiveLabel, _receiveDataTextBox
            });
        }
        #endregion

        #region 事件处理方法
        /// <summary>
        /// 连接按钮点击事件
        /// </summary>
        private async void ConnectButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (_multiScannerManager == null) return;

                // 获取配置
                var config = GetSerialPortConfiguration();
                if (config == null) return;

                // 设置配置并连接
                await _multiScannerManager.SetScannerConfigurationAsync(_scannerId, config);
                bool success = await _multiScannerManager.ConnectScannerAsync(_scannerId);

                if (success)
                {
                    _connectButton.Enabled = false;
                    _disconnectButton.Enabled = true;
                    _sendButton.Enabled = true;
                    UpdateStatus("已连接", ColorTranslator.FromHtml("#27ae60"));

                    // 连接成功后通知主面板刷新所有扫描枪的串口列表
                    OnPortStatusChanged?.Invoke();
                }
                else
                {
                    UpdateStatus("连接失败", ColorTranslator.FromHtml("#e74c3c"));
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"扫描枪{_scannerId}连接失败", ex);
                UpdateStatus("连接异常", ColorTranslator.FromHtml("#e74c3c"));
            }
        }

        /// <summary>
        /// 断开按钮点击事件
        /// </summary>
        private async void DisconnectButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (_multiScannerManager == null) return;

                bool success = await _multiScannerManager.DisconnectScannerAsync(_scannerId);

                if (success)
                {
                    _connectButton.Enabled = true;
                    _disconnectButton.Enabled = false;
                    _sendButton.Enabled = false;
                    UpdateStatus("未连接", ColorTranslator.FromHtml("#e74c3c"));

                    // 断开连接后通知主面板刷新所有扫描枪的串口列表
                    OnPortStatusChanged?.Invoke();
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"扫描枪{_scannerId}断开连接失败", ex);
            }
        }

        /// <summary>
        /// 发送按钮点击事件
        /// 增强发送功能，提供更详细的状态反馈
        /// </summary>
        private async void SendButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (_multiScannerManager == null || string.IsNullOrWhiteSpace(_sendDataTextBox.Text))
                    return;

                string data = _sendDataTextBox.Text.Trim();

                // 显示发送状态
                AppendReceiveData($"[发送] {data}");

                bool success = await _multiScannerManager.SendDataAsync(_scannerId, data);

                if (success)
                {
                    AppendReceiveData($"[发送成功] 数据已发送到扫描器");
                    _sendDataTextBox.Clear();

                    // 如果是触发命令，提示用户等待扫码
                    if (data.ToLower() == "start" || data.ToLower().Contains("trigger"))
                    {
                        AppendReceiveData($"[提示] 扫码器已触发，请扫描条码...");
                    }
                }
                else
                {
                    AppendReceiveData($"[发送失败] 无法发送数据到扫描器");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"扫描枪{_scannerId}发送数据失败", ex);
                AppendReceiveData($"[发送异常] {ex.Message}");
            }
        }

        /// <summary>
        /// 条码扫描事件处理 - 增加调试日志
        /// 增强接收数据显示，提供更详细的信息
        /// </summary>
        private void OnBarcodeScanned(object sender, MultiBarcodeScannedEventArgs e)
        {
            LogHelper.Debug($"ScannerControlPanel{_scannerId}收到条码事件: ScannerId={e.ScannerId}, Data={e.ScannerData.BarcodeData}");

            if (e.ScannerId == _scannerId)
            {
                LogHelper.Debug($"ScannerControlPanel{_scannerId}事件匹配，准备更新UI");

                this.Invoke(new Action(() =>
                {
                    string barcodeData = e.ScannerData.BarcodeData;
                    LogHelper.Debug($"ScannerControlPanel{_scannerId}开始更新UI，数据: {barcodeData}");

                    AppendReceiveData($"[接收] {barcodeData}");
                    AppendReceiveData($"[信息] 数据长度: {barcodeData.Length} 字符");

                    // 如果数据包含特殊字符，显示十六进制表示
                    if (barcodeData.Any(c => char.IsControl(c)))
                    {
                        string hexData = string.Join(" ", barcodeData.Select(c => $"{(int)c:X2}"));
                        AppendReceiveData($"[十六进制] {hexData}");
                    }

                    LogHelper.Info($"扫描枪{_scannerId}成功接收条码数据: {barcodeData}");
                    LogHelper.Debug($"ScannerControlPanel{_scannerId}UI更新完成");
                }));
            }
            else
            {
                LogHelper.Debug($"ScannerControlPanel{_scannerId}事件不匹配，忽略 (事件来自扫描枪{e.ScannerId})");
            }
        }

        /// <summary>
        /// 状态变化事件处理
        /// </summary>
        private void OnStatusChanged(object sender, MultiScannerStatusChangedEventArgs e)
        {
            if (e.ScannerId == _scannerId)
            {
                this.Invoke(new Action(() =>
                {
                    Color statusColor = e.Status == CommunicationStatus.Connected ?
                        ColorTranslator.FromHtml("#27ae60") : ColorTranslator.FromHtml("#e74c3c");

                    UpdateStatus(e.Description, statusColor);

                    // 更新按钮状态
                    bool isConnected = e.Status == CommunicationStatus.Connected;
                    _connectButton.Enabled = !isConnected;
                    _disconnectButton.Enabled = isConnected;
                    _sendButton.Enabled = isConnected;
                }));
            }
        }
        #endregion

        #region 辅助方法
        /// <summary>
        /// 刷新串口列表
        /// </summary>
        public void RefreshSerialPorts()
        {
            try
            {
                if (_multiScannerManager != null)
                {
                    // 使用带状态的串口列表
                    var ports = _multiScannerManager.GetAvailableSerialPortsWithStatus(_scannerId);
                    var currentSelection = _portComboBox.SelectedItem?.ToString();

                    _portComboBox.Items.Clear();
                    _portComboBox.Items.AddRange(ports);

                    // 尝试恢复之前的选择
                    if (!string.IsNullOrEmpty(currentSelection))
                    {
                        // 查找匹配的项（可能包含状态信息）
                        for (int i = 0; i < _portComboBox.Items.Count; i++)
                        {
                            var item = _portComboBox.Items[i].ToString();
                            if (item == currentSelection || item.StartsWith(currentSelection.Split('（')[0]))
                            {
                                _portComboBox.SelectedIndex = i;
                                break;
                            }
                        }
                    }

                    // 移除自动选择逻辑，让RefreshSerialPorts()只负责刷新端口列表
                    // 端口选择将由LoadConfigurationToUI()根据保存的配置进行智能选择
                    LogHelper.Debug($"扫描枪{_scannerId}端口列表刷新完成，共{ports.Length}个可用端口，等待配置加载进行端口选择");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"扫描枪{_scannerId}刷新串口列表失败", ex);
            }
        }

        /// <summary>
        /// 获取串口配置
        /// </summary>
        /// <returns></returns>
        private SerialPortConfiguration GetSerialPortConfiguration()
        {
            try
            {
                if (_portComboBox.SelectedItem == null)
                {
                    MessageBox.Show("请选择串口", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return null;
                }

                // 提取实际的串口名称（去除状态信息）
                var selectedPort = _portComboBox.SelectedItem.ToString();
                var actualPortName = selectedPort.Split('（')[0]; // 提取COM端口名称

                // 检查是否选择了被其他扫描枪占用的串口
                if (selectedPort.Contains("（") && !selectedPort.Contains($"（{_scannerId}号扫码器）"))
                {
                    MessageBox.Show("该串口已被其他扫描枪占用，请选择其他串口", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return null;
                }

                var config = new SerialPortConfiguration
                {
                    PortName = actualPortName,
                    BaudRate = (int)_baudRateComboBox.SelectedItem,
                    DataBits = (int)_dataBitsComboBox.SelectedItem,
                    StopBits = GetStopBits(_stopBitsComboBox.SelectedItem.ToString()),
                    Parity = GetParity(_parityComboBox.SelectedItem.ToString()),
                    ReadTimeout = 1000,
                    WriteTimeout = 1000
                };

                return config;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"扫描枪{_scannerId}获取串口配置失败", ex);
                return null;
            }
        }

        /// <summary>
        /// 获取停止位枚举值
        /// </summary>
        private System.IO.Ports.StopBits GetStopBits(string stopBitsText)
        {
            switch (stopBitsText)
            {
                case "1": return System.IO.Ports.StopBits.One;
                case "1.5": return System.IO.Ports.StopBits.OnePointFive;
                case "2": return System.IO.Ports.StopBits.Two;
                default: return System.IO.Ports.StopBits.One;
            }
        }

        /// <summary>
        /// 获取奇偶校验枚举值
        /// </summary>
        private System.IO.Ports.Parity GetParity(string parityText)
        {
            switch (parityText)
            {
                case "无校验": return System.IO.Ports.Parity.None;
                case "奇校验": return System.IO.Ports.Parity.Odd;
                case "偶校验": return System.IO.Ports.Parity.Even;
                default: return System.IO.Ports.Parity.None;
            }
        }

        /// <summary>
        /// 更新状态显示
        /// </summary>
        private void UpdateStatus(string status, Color color)
        {
            _statusLabel.Text = status;
            _statusLabel.ForeColor = color;
        }

        /// <summary>
        /// 追加接收数据显示
        /// </summary>
        private void AppendReceiveData(string data)
        {
            string timeStamp = DateTime.Now.ToString("HH:mm:ss");
            string message = $"[{timeStamp}] {data}\r\n";

            _receiveDataTextBox.AppendText(message);
            _receiveDataTextBox.ScrollToCaret();
        }

        /// <summary>
        /// 从配置获取默认波特率
        /// </summary>
        /// <returns>默认波特率</returns>
        private int GetDefaultBaudRateFromConfig()
        {
            try
            {
                // 从新的Settings系统获取配置
                var communicationSettings = Settings.Settings.Current.Communication;

                // 根据扫码器ID获取对应的配置
                switch (_scannerId)
                {
                    case 1:
                        return communicationSettings.MultiScanner1BaudRate;
                    case 2:
                        return communicationSettings.MultiScanner2BaudRate;
                    case 3:
                        return communicationSettings.MultiScanner3BaudRate;
                    default:
                        return 115200;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Warning($"从Settings系统获取扫码器{_scannerId}默认波特率失败，使用默认值115200: {ex.Message}");
                return 115200;
            }
        }
        #endregion
    }
}
