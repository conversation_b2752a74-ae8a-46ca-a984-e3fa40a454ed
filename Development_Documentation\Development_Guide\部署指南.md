# MyHMI 上位机控制系统 - 部署指南

## 部署概述

本指南详细说明了 MyHMI 上位机控制系统的打包、部署和发布流程，包括开发环境部署、测试环境部署和生产环境部署。

## 系统要求

### 目标系统要求
- **操作系统**: Windows 10/11 或 Windows Server 2016/2019/2022
- **架构**: x86 或 x64
- **.NET Framework**: 4.8 或更高版本
- **内存**: 最少 4GB RAM（推荐 8GB 或更多）
- **硬盘**: 至少 1GB 可用空间
- **权限**: 管理员权限（用于硬件访问）

### 硬件要求
- **运动控制卡**: 雷赛 DMC1000 系列
- **相机**: 支持 DirectShow 或厂商 SDK 的工业相机
- **通信接口**: 以太网口（Modbus TCP）、串口（扫描枪）
- **IO 接口**: 数字 IO 接口

## 编译和打包

### 1. Release 编译

#### Visual Studio 编译
```
1. 打开 MyHMI.sln
2. 选择 "Release" 配置
3. 选择 "Any CPU" 平台
4. 菜单: 生成 → 重新生成解决方案
5. 确认编译无错误和警告
```

#### 命令行编译
```bash
# 使用 MSBuild 编译
msbuild MyHMI.sln /p:Configuration=Release /p:Platform="Any CPU" /p:OutputPath=".\bin\Release\"

# 或使用 Visual Studio Developer Command Prompt
devenv MyHMI.sln /rebuild Release
```

### 2. 输出文件检查

编译完成后，检查 `bin\Release` 目录包含以下文件：
```
bin\Release\
├── MyHMI.exe                      # 主程序
├── MyHMI.exe.config               # 配置文件
├── MyHMI.pdb                      # 调试符号文件（可选）
├── NLog.dll                       # 日志组件
├── Newtonsoft.Json.dll            # JSON 处理组件
├── NModbus4.dll                   # Modbus 通信组件
├── EPPlus.dll                     # Excel 处理组件
├── CsvHelper.dll                  # CSV 处理组件
└── [其他依赖 DLL 文件]
```

### 3. 创建部署包

#### 方法一：手动打包
```bash
# 创建部署目录
mkdir MyHMI_Deploy
mkdir MyHMI_Deploy\Config
mkdir MyHMI_Deploy\Data
mkdir MyHMI_Deploy\Logs
mkdir MyHMI_Deploy\References

# 复制程序文件
copy bin\Release\*.exe MyHMI_Deploy\
copy bin\Release\*.dll MyHMI_Deploy\
copy bin\Release\*.config MyHMI_Deploy\

# 复制配置文件
copy Config\*.json MyHMI_Deploy\Config\

# 复制第三方库
copy References\*.dll MyHMI_Deploy\References\

# 创建压缩包
powershell Compress-Archive -Path MyHMI_Deploy -DestinationPath MyHMI_v1.0.0.zip
```

#### 方法二：使用部署脚本
```batch
@echo off
echo 开始创建 MyHMI 部署包...

set VERSION=1.0.0
set DEPLOY_DIR=MyHMI_Deploy_%VERSION%
set SOURCE_DIR=bin\Release

REM 清理旧的部署目录
if exist %DEPLOY_DIR% rmdir /s /q %DEPLOY_DIR%

REM 创建部署目录结构
mkdir %DEPLOY_DIR%
mkdir %DEPLOY_DIR%\Config
mkdir %DEPLOY_DIR%\Data
mkdir %DEPLOY_DIR%\Logs
mkdir %DEPLOY_DIR%\References

REM 复制程序文件
copy %SOURCE_DIR%\*.exe %DEPLOY_DIR%\
copy %SOURCE_DIR%\*.dll %DEPLOY_DIR%\
copy %SOURCE_DIR%\*.config %DEPLOY_DIR%\

REM 复制配置文件
copy Config\SystemConfig.json %DEPLOY_DIR%\Config\

REM 复制第三方库
if exist References\*.dll copy References\*.dll %DEPLOY_DIR%\References\

REM 创建安装脚本
echo @echo off > %DEPLOY_DIR%\Install.bat
echo echo 安装 MyHMI 系统... >> %DEPLOY_DIR%\Install.bat
echo echo 请确保已安装 .NET Framework 4.8 >> %DEPLOY_DIR%\Install.bat
echo pause >> %DEPLOY_DIR%\Install.bat

REM 创建启动脚本
echo @echo off > %DEPLOY_DIR%\Start.bat
echo cd /d %%~dp0 >> %DEPLOY_DIR%\Start.bat
echo MyHMI.exe >> %DEPLOY_DIR%\Start.bat

echo 部署包创建完成: %DEPLOY_DIR%
pause
```

## 安装部署

### 1. 前置条件检查

#### .NET Framework 检查脚本
```batch
@echo off
echo 检查 .NET Framework 4.8...

reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full" /v Release | find "528040" >nul
if %errorlevel% == 0 (
    echo .NET Framework 4.8 已安装
) else (
    echo 错误: 需要安装 .NET Framework 4.8
    echo 请从以下地址下载安装:
    echo https://dotnet.microsoft.com/download/dotnet-framework/net48
    pause
    exit /b 1
)
```

#### 权限检查
```csharp
// 在程序启动时检查管理员权限
public static bool IsRunAsAdministrator()
{
    var identity = WindowsIdentity.GetCurrent();
    var principal = new WindowsPrincipal(identity);
    return principal.IsInRole(WindowsBuiltInRole.Administrator);
}

// 如果需要管理员权限，重新启动程序
if (!IsRunAsAdministrator())
{
    var processInfo = new ProcessStartInfo
    {
        FileName = Application.ExecutablePath,
        UseShellExecute = true,
        Verb = "runas"
    };
    
    try
    {
        Process.Start(processInfo);
        Application.Exit();
    }
    catch (Exception ex)
    {
        MessageBox.Show("需要管理员权限才能运行此程序", "权限错误", 
            MessageBoxButtons.OK, MessageBoxIcon.Error);
    }
}
```

### 2. 标准安装流程

#### 安装脚本 (Install.bat)
```batch
@echo off
echo ========================================
echo MyHMI 上位机控制系统安装程序
echo 版本: 1.0.0
echo ========================================

REM 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 需要管理员权限
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

REM 设置安装目录
set INSTALL_DIR=C:\Program Files\MyHMI
echo 安装目录: %INSTALL_DIR%

REM 创建安装目录
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"
if not exist "%INSTALL_DIR%\Config" mkdir "%INSTALL_DIR%\Config"
if not exist "%INSTALL_DIR%\Data" mkdir "%INSTALL_DIR%\Data"
if not exist "%INSTALL_DIR%\Logs" mkdir "%INSTALL_DIR%\Logs"

REM 复制文件
echo 正在复制程序文件...
copy *.exe "%INSTALL_DIR%\"
copy *.dll "%INSTALL_DIR%\"
copy *.config "%INSTALL_DIR%\"
copy Config\*.json "%INSTALL_DIR%\Config\"

REM 创建桌面快捷方式
echo 创建桌面快捷方式...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\MyHMI.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\MyHMI.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Save()"

REM 创建开始菜单快捷方式
echo 创建开始菜单快捷方式...
if not exist "%APPDATA%\Microsoft\Windows\Start Menu\Programs\MyHMI" mkdir "%APPDATA%\Microsoft\Windows\Start Menu\Programs\MyHMI"
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%APPDATA%\Microsoft\Windows\Start Menu\Programs\MyHMI\MyHMI.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\MyHMI.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Save()"

echo 安装完成！
echo 程序已安装到: %INSTALL_DIR%
echo 桌面快捷方式已创建
pause
```

### 3. 绿色版部署

对于不需要安装的绿色版部署：

#### 部署步骤
```
1. 解压部署包到目标目录
2. 确保目录具有读写权限
3. 运行 MyHMI.exe
```

#### 目录权限设置
```batch
REM 设置目录权限（需要管理员权限）
icacls "C:\MyHMI" /grant Users:(OI)(CI)F /T
```

## 配置部署

### 1. 配置文件部署

#### 生产环境配置
```json
{
  "System": {
    "Name": "生产环境-上位机控制系统",
    "StartupMode": "Normal",
    "DebugMode": false
  },
  "Logging": {
    "LogLevel": "Info",
    "EnableConsoleLog": false,
    "EnableFileLog": true
  },
  "IO": {
    "CardType": "DMC1000",
    "CardIndex": 0
  },
  "Communication": {
    "ModbusTcp": {
      "IPAddress": "*************",
      "Port": 502
    }
  }
}
```

#### 测试环境配置
```json
{
  "System": {
    "Name": "测试环境-上位机控制系统",
    "StartupMode": "Test",
    "DebugMode": true
  },
  "Logging": {
    "LogLevel": "Debug",
    "EnableConsoleLog": true,
    "EnableFileLog": true
  }
}
```

### 2. 环境变量配置

#### 系统环境变量
```batch
REM 设置系统环境变量
setx MYHMI_HOME "C:\Program Files\MyHMI" /M
setx MYHMI_CONFIG_PATH "C:\Program Files\MyHMI\Config\SystemConfig.json" /M
setx MYHMI_LOG_PATH "C:\Program Files\MyHMI\Logs" /M
```

#### 用户环境变量
```batch
REM 设置用户环境变量
setx MYHMI_USER_DATA "%APPDATA%\MyHMI"
```

## 服务部署

### 1. Windows 服务安装

如果需要将 MyHMI 作为 Windows 服务运行：

#### 服务安装脚本
```batch
@echo off
echo 安装 MyHMI Windows 服务...

REM 使用 sc 命令创建服务
sc create "MyHMI Service" binPath= "C:\Program Files\MyHMI\MyHMI.exe" start= auto
sc description "MyHMI Service" "MyHMI 上位机控制系统服务"

REM 启动服务
sc start "MyHMI Service"

echo 服务安装完成
pause
```

#### 服务卸载脚本
```batch
@echo off
echo 卸载 MyHMI Windows 服务...

REM 停止服务
sc stop "MyHMI Service"

REM 删除服务
sc delete "MyHMI Service"

echo 服务卸载完成
pause
```

### 2. 自启动配置

#### 注册表自启动
```batch
REM 添加到注册表自启动
reg add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "MyHMI" /t REG_SZ /d "C:\Program Files\MyHMI\MyHMI.exe" /f
```

#### 任务计划程序自启动
```batch
REM 创建任务计划
schtasks /create /tn "MyHMI AutoStart" /tr "C:\Program Files\MyHMI\MyHMI.exe" /sc onstart /ru SYSTEM
```

## 更新部署

### 1. 增量更新

#### 更新脚本
```batch
@echo off
echo MyHMI 系统更新程序

REM 停止程序
taskkill /f /im MyHMI.exe 2>nul

REM 备份当前版本
if exist "C:\Program Files\MyHMI\MyHMI.exe" (
    copy "C:\Program Files\MyHMI\MyHMI.exe" "C:\Program Files\MyHMI\MyHMI.exe.bak"
)

REM 复制新版本文件
copy *.exe "C:\Program Files\MyHMI\"
copy *.dll "C:\Program Files\MyHMI\"

REM 更新配置文件（如果需要）
if exist "Config\SystemConfig.json" (
    copy "Config\SystemConfig.json" "C:\Program Files\MyHMI\Config\"
)

echo 更新完成
pause
```

### 2. 版本管理

#### 版本信息文件 (version.json)
```json
{
  "version": "1.0.0",
  "buildDate": "2024-01-15",
  "buildNumber": "20240115001",
  "releaseNotes": [
    "初始版本发布",
    "支持基本的IO控制功能",
    "支持电机控制功能",
    "支持Modbus TCP通信"
  ]
}
```

#### 版本检查代码
```csharp
public class VersionManager
{
    public static Version GetCurrentVersion()
    {
        return Assembly.GetExecutingAssembly().GetName().Version;
    }
    
    public static bool CheckForUpdates(string updateServerUrl)
    {
        try
        {
            // 从服务器获取最新版本信息
            var latestVersion = GetLatestVersionFromServer(updateServerUrl);
            var currentVersion = GetCurrentVersion();
            
            return latestVersion > currentVersion;
        }
        catch (Exception ex)
        {
            LogHelper.Error("检查更新失败", ex);
            return false;
        }
    }
}
```

## 卸载程序

### 卸载脚本 (Uninstall.bat)
```batch
@echo off
echo ========================================
echo MyHMI 系统卸载程序
echo ========================================

REM 确认卸载
set /p confirm=确定要卸载 MyHMI 系统吗？(Y/N): 
if /i not "%confirm%"=="Y" goto :end

REM 停止程序和服务
echo 正在停止程序...
taskkill /f /im MyHMI.exe 2>nul
sc stop "MyHMI Service" 2>nul
sc delete "MyHMI Service" 2>nul

REM 删除快捷方式
echo 删除快捷方式...
del "%USERPROFILE%\Desktop\MyHMI.lnk" 2>nul
rmdir /s /q "%APPDATA%\Microsoft\Windows\Start Menu\Programs\MyHMI" 2>nul

REM 删除注册表项
echo 清理注册表...
reg delete "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "MyHMI" /f 2>nul

REM 删除程序文件
echo 删除程序文件...
rmdir /s /q "C:\Program Files\MyHMI"

REM 删除用户数据（可选）
set /p deleteData=是否删除用户数据和日志？(Y/N): 
if /i "%deleteData%"=="Y" (
    rmdir /s /q "%APPDATA%\MyHMI" 2>nul
)

echo 卸载完成！
:end
pause
```

## 部署验证

### 1. 功能验证清单
```
□ 程序能够正常启动
□ 配置文件加载正常
□ 日志系统工作正常
□ 硬件设备连接正常
□ 网络通信功能正常
□ 用户界面显示正常
□ 基本功能操作正常
```

### 2. 自动化验证脚本
```batch
@echo off
echo MyHMI 部署验证程序

REM 检查程序文件
if not exist "MyHMI.exe" (
    echo 错误: 主程序文件不存在
    goto :error
)

REM 检查配置文件
if not exist "Config\SystemConfig.json" (
    echo 错误: 配置文件不存在
    goto :error
)

REM 检查依赖库
if not exist "NLog.dll" (
    echo 错误: 日志组件不存在
    goto :error
)

REM 启动程序进行测试
echo 启动程序进行验证...
start MyHMI.exe

echo 验证完成，请检查程序是否正常运行
goto :end

:error
echo 验证失败，请检查部署包完整性
pause
exit /b 1

:end
pause
```

## 故障排除

### 常见部署问题

#### 1. .NET Framework 版本问题
```
错误: 应用程序无法启动，因为缺少 .NET Framework 4.8
解决: 安装 .NET Framework 4.8 Runtime
```

#### 2. 权限问题
```
错误: 无法访问硬件设备
解决: 以管理员身份运行程序
```

#### 3. 依赖库缺失
```
错误: 无法加载文件或程序集 'XXX.dll'
解决: 确保所有依赖库都在程序目录中
```

#### 4. 配置文件问题
```
错误: 配置文件格式错误
解决: 检查 JSON 格式是否正确，使用 JSON 验证工具
```

通过遵循本部署指南，可以确保 MyHMI 系统在各种环境中的成功部署和稳定运行。
