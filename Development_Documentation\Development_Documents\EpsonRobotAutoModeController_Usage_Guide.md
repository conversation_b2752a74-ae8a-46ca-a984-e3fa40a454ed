# Epson机器人自动模式控制器使用指南

## 概述

EpsonRobotAutoModeController是一个专门为管理两台Epson六轴机器人自动模式通信而设计的控制器类。它基于现有的HR2项目架构，实现了完整的TCP通信、IO控制、扫码数据处理等功能。

## 主要功能

### 1. 自动模式通信管理
- 管理两台机器人的TCP连接（控制端口和数据端口）
- 自动登录和启动机器人
- 实时监听数据端口消息
- 异步处理和响应机器人请求

### 2. 业务逻辑处理
- **取料权限控制**：处理GETPICK消息，检查扫码器状态
- **取料确认处理**：处理INPICK消息，执行IO控制和数据传输
- **NG品放置控制**：处理GETNGPUT和NGPUTFULL消息
- **OK品放置控制**：处理GETOKPUT消息
- **状态监控和复位**：完整的状态管理和复位逻辑

### 3. 硬件集成
- **IO控制**：集成DMC1000BIOManager进行IO读写操作
- **扫码器管理**：集成MultiScannerManager获取扫码数据
- **状态监控**：实时监控IO状态变化

## 使用方法

### 1. 获取控制器实例

```csharp
// 获取单例实例
var controller = EpsonRobotAutoModeController.Instance;
```

### 2. 配置控制器

```csharp
// 创建配置
var config = new EpsonAutoModeConfiguration
{
    Robot1Id = 1,
    Robot2Id = 2,
    IOMapping = new IOMapping
    {
        Robot1OutputIO = "O0001",
        Robot1InputIO = "I0006",
        Robot1NGStatusIO = "I0104",
        Robot2OutputIO = "O0003",
        Robot2InputIO = "I0010",
        Robot2NGStatusIO = "I0105",
        OKPutStatusIO = "I0106"
    },
    ScannerMapping = new ScannerMapping
    {
        Scanner1Id = 1, // 左扫码器
        Scanner2Id = 2, // 右扫码器
        Scanner3Id = 3  // 中间扫码器
    },
    AutoReconnect = true,
    AutoRecover = true
};

// 初始化控制器
bool success = await controller.InitializeAsync(config);
```

### 3. 启动自动模式

```csharp
// 启动自动模式
bool started = await controller.StartAutoModeAsync();
if (started)
{
    Console.WriteLine("自动模式启动成功");
}
```

### 4. 监听事件

```csharp
// 订阅状态变化事件
controller.StateChanged += (sender, e) =>
{
    Console.WriteLine($"控制器状态变化: {e.OldState} -> {e.NewState}");
};

// 订阅机器人状态变化事件
controller.RobotStateChanged += (sender, e) =>
{
    Console.WriteLine($"机器人{e.RobotId}状态变化: {e.OldState} -> {e.NewState}");
};

// 订阅消息接收事件
controller.MessageReceived += (sender, e) =>
{
    Console.WriteLine($"收到机器人{e.RobotId}消息: {e.MessageType} - {e.MessageContent}");
};

// 订阅响应发送事件
controller.ResponseSent += (sender, e) =>
{
    Console.WriteLine($"向机器人{e.RobotId}发送响应: {e.ResponseType} - {e.Success}");
};

// 订阅错误事件
controller.ErrorOccurred += (sender, e) =>
{
    Console.WriteLine($"发生错误: {e.ErrorMessage} - {e.Context}");
};
```

### 5. 停止自动模式

```csharp
// 停止自动模式
await controller.StopAutoModeAsync();
```

### 6. 释放资源

```csharp
// 释放控制器资源
controller.Dispose();
```

## 配置参数说明

### 基础配置
- `Robot1Id`/`Robot2Id`: 机器人ID标识
- `MainLoopDelayMs`: 主控制循环延迟时间（默认100ms）
- `ResetDelayMs`: 复位延迟时间（默认2000ms）
- `IOOperationDelayMs`: IO操作延迟时间（默认50ms）

### IO映射配置
- `Robot1OutputIO`/`Robot2OutputIO`: 机器人输出IO点位
- `Robot1InputIO`/`Robot2InputIO`: 机器人输入IO点位
- `Robot1NGStatusIO`/`Robot2NGStatusIO`: NG状态IO点位
- `OKPutStatusIO`: OK品放置状态IO点位（共用）

### 扫码器映射配置
- `Scanner1Id`: 左扫码器ID
- `Scanner2Id`: 右扫码器ID
- `Scanner3Id`: 中间扫码器ID

### 监控配置
- `NGStatusMonitorTimeoutMs`: NG状态监控超时时间（默认30秒）
- `NGStatusCheckIntervalMs`: NG状态检查间隔（默认100ms）
- `NGStatusStableTimeMs`: NG状态稳定时间（默认1秒）

### 错误处理配置
- `ConnectionCheckIntervalMs`: 连接检查间隔（默认30秒）
- `AutoReconnect`: 是否启用自动重连（默认true）
- `AutoRecover`: 是否启用自动错误恢复（默认true）
- `AutoRecoveryDelayMs`: 自动恢复延迟时间（默认5秒）

## 业务流程说明

### 1. GETPICK流程（取料权限请求）
1. 机器人发送"GETPICK"消息
2. 控制器检查对应扫码器是否有数据
3. 有数据：发送"ALLOWPICK"，无数据：发送"DENYPICK"

### 2. INPICK流程（取料确认）
1. 机器人发送"INPICK"消息
2. 控制器设置对应输出IO为0
3. 等待100ms
4. 检查对应输入IO状态
5. 如果状态为1，发送格式化的扫码数据

### 3. GETNGPUT流程（NG品放置权限请求）
1. 机器人发送"GETNGPUT"消息
2. 控制器检查对应NG状态IO
3. IO为0：发送"ALLOWNGPUT"，IO为1：发送"DENYNGPUT"

### 4. NGPUTFULL流程（NG品放置完成确认）
1. 机器人发送"NGPUTFULL"消息
2. 控制器监控NG状态IO变化
3. 检测完整变化周期后发送"RESETNGPUT"

### 5. GETOKPUT流程（OK品放置权限请求）
1. 机器人发送"GETOKPUT"消息
2. 控制器检查I0106状态
3. IO为0：发送"ALLOWOKPUT"，IO为1：发送"DENYOKPUT"

## 注意事项

### 1. 初始化顺序
- 必须先初始化配置，再启动自动模式
- 确保机器人管理器、IO管理器、扫码器管理器已正确初始化

### 2. 线程安全
- 控制器使用异步处理，支持多线程环境
- 所有公共方法都是线程安全的

### 3. 错误处理
- 控制器具有自动重连和错误恢复功能
- 建议监听ErrorOccurred事件处理异常情况

### 4. 资源管理
- 使用完毕后必须调用Dispose()释放资源
- 控制器会自动清理连接和线程资源

### 5. 日志记录
- 控制器提供详细的中文日志记录
- 建议配置LogHelper以便调试和问题定位

## 故障排除

### 1. 连接问题
- 检查机器人IP地址和端口配置
- 确认网络连接正常
- 查看连接日志确定具体错误

### 2. IO操作问题
- 检查IO点位配置是否正确
- 确认DMC1000BIOManager初始化成功
- 查看IO操作日志

### 3. 扫码器问题
- 检查扫码器ID映射配置
- 确认MultiScannerManager初始化成功
- 查看扫码器操作日志

### 4. 消息处理问题
- 检查消息格式是否正确
- 查看消息处理日志
- 确认状态机状态正确

## 版本信息

- **版本**: 1.0.0
- **开发日期**: 2025年1月
- **兼容性**: 基于HR2项目架构
- **依赖**: EpsonRobotManager, DMC1000BIOManager, MultiScannerManager
