# 全局启动、暂停、停止、复位功能说明文档

## 📋 文档概述

本文档详细说明了HR2自动化系统的全局控制功能实现逻辑、核心代码位置以及扩展指南。

**版本**: v1.0  
**更新日期**: 2025-09-28  
**适用系统**: HR2自动化生产线控制系统

## 🏗️ 系统架构

### 控制层次结构
```
UI层（主界面全局按钮）
    ↓
WorkflowManager（统一控制协调器）
    ↓
├── BeltMotorAutoModeController（皮带电机自动模式）
├── ScaraAutoModeController（SCARA翻转电机自动模式）
├── EpsonRobotAutoModeController（6轴机器人自动模式）
└── ScannerAutoModeManager（扫码器自动模式）
```

### 设计原则
1. **统一入口**: 所有全局控制操作都通过WorkflowManager进行
2. **状态驱动**: 基于WorkflowState枚举进行状态管理
3. **智能交互**: 根据当前状态智能选择操作类型
4. **安全控制**: 通过访问权限防止误操作

## 🎯 核心功能实现

### 1. 全局启动功能

#### 实现逻辑
- **智能启动**: 根据当前状态自动选择启动或恢复操作
- **状态检测**: Paused状态→恢复操作，Idle状态→正常启动
- **序列执行**: 按照固定顺序启动各子系统

#### 核心代码位置
**文件**: `Managers/WorkflowManager.cs`
**方法**: `StartWorkflowAsync(string productId = "")`

```csharp
public async Task<bool> StartWorkflowAsync(string productId = "")
{
    // 智能启动逻辑：如果当前是暂停状态，则发送恢复命令
    if (_currentState == WorkflowState.Paused)
    {
        LogHelper.Info("检测到暂停状态，执行恢复操作");
        return await ResumeWorkflowAsync();
    }
    else if (_currentState != WorkflowState.Idle)
    {
        LogHelper.Warning($"工作流正在运行中，当前状态: {_currentState}");
        return false;
    }
    
    // 执行正常启动序列
    return await ExecuteWorkflowStartSequenceAsync(productId);
}
```

#### 启动序列
1. **步骤1**: 初始化所有AutoMode控制器
2. **步骤2**: 启动扫码器自动模式
3. **步骤3**: 启动皮带电机控制器
4. **步骤4**: 启动6轴机器人控制器
5. **步骤5**: 启动SCARA自动模式控制器
6. **步骤6**: 等待系统稳定

### 2. 全局暂停功能

#### 实现逻辑
- **统一暂停**: 同时暂停所有子系统
- **状态保存**: 保存当前状态以便恢复
- **状态切换**: 设置为Paused状态

#### 核心代码位置
**文件**: `Managers/WorkflowManager.cs`
**方法**: `PauseWorkflowAsync()`

```csharp
public async Task<bool> PauseWorkflowAsync()
{
    // 暂停各AutoMode控制器
    await PauseAllAutoModeControllersAsync();
    
    // 保存当前状态并切换到暂停状态
    _previousState = _currentState;
    ChangeState(WorkflowState.Paused);
    
    return true;
}
```

#### 暂停操作
- **皮带电机**: 调用`BeltMotorAutoModeController.StopAsync()`
- **SCARA**: 调用`ScaraAutoModeController.StopAsync()`
- **6轴机器人**: 调用`EpsonRobotAutoModeController.PauseAsync()`（发送$Pause命令）
- **扫码器**: 调用`ScannerAutoModeManager.StopAutoModeAsync()`

### 3. 全局恢复功能

#### 实现逻辑
- **状态检测**: 仅在Paused状态下执行恢复
- **统一恢复**: 同时恢复所有子系统
- **状态恢复**: 恢复到暂停前的状态

#### 核心代码位置
**文件**: `Managers/WorkflowManager.cs`
**方法**: `ResumeWorkflowAsync()`

```csharp
public async Task<bool> ResumeWorkflowAsync()
{
    if (_currentState != WorkflowState.Paused)
    {
        LogHelper.Info("工作流不在暂停状态，无需恢复");
        return true;
    }
    
    // 恢复各AutoMode控制器
    await ResumeAllAutoModeControllersAsync();
    
    // 恢复到之前的状态
    if (_previousState != WorkflowState.Idle)
    {
        ChangeState(_previousState);
    }
    
    return true;
}
```

### 4. 全局停止功能

#### 实现逻辑
- **统一停止**: 同时停止所有子系统
- **状态重置**: 设置为Idle状态
- **资源清理**: 清理运行时资源

#### 核心代码位置
**文件**: `Managers/WorkflowManager.cs`
**方法**: `StopWorkflowAsync()`

```csharp
public async Task<bool> StopWorkflowAsync()
{
    // 停止各AutoMode控制器
    await StopAllAutoModeControllersAsync();
    
    ChangeState(WorkflowState.Idle);
    return true;
}
```

### 5. 全局复位功能

#### 实现逻辑
- **先停止**: 确保所有子系统停止
- **统一重置**: 重置所有子系统状态
- **状态清理**: 设置为Idle状态

#### 核心代码位置
**文件**: `Managers/WorkflowManager.cs`
**方法**: `ResetWorkflowAsync()`

```csharp
public async Task<bool> ResetWorkflowAsync()
{
    // 停止工作流
    await StopWorkflowAsync();
    
    // 重置各AutoMode控制器
    await ResetAllAutoModeControllersAsync();
    
    ChangeState(WorkflowState.Idle);
    return true;
}
```

## 📊 状态管理

### WorkflowState枚举
**文件**: `Models/CommunicationModels.cs`

```csharp
public enum WorkflowState
{
    Idle,           // 空闲状态
    WaitingForScan, // 等待扫描
    VisionDetecting,// 视觉检测中
    MotorMoving,    // 电机运动中
    RobotOperating, // 机器人操作中
    Paused,         // 暂停状态
    Error           // 错误状态
}
```

### 状态转换图
```
Idle ←→ WaitingForScan ←→ MotorMoving ←→ RobotOperating ←→ VisionDetecting
  ↕                                                                    ↕
Paused ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←
  ↕                                                                    ↕
Error ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←
```

## 🔧 子系统集成方法

### 现有子系统
1. **BeltMotorAutoModeController**: 皮带电机自动模式控制
2. **ScaraAutoModeController**: SCARA翻转电机自动模式控制
3. **EpsonRobotAutoModeController**: 6轴机器人自动模式控制
4. **ScannerAutoModeManager**: 扫码器自动模式管理

### 各子系统控制方法映射

| 子系统 | 启动方法 | 暂停方法 | 恢复方法 | 停止方法 | 重置方法 |
|--------|----------|----------|----------|----------|----------|
| 皮带电机 | `StartAsync()` | `StopAsync()` | `StartAsync()` | `StopAsync()` | `ResetAsync()` |
| SCARA | `StartAsync()` | `StopAsync()` | `StartAsync()` | `StopAsync()` | `ResetAsync()` |
| 6轴机器人 | `StartAsync()` | `PauseAsync()`* | `ResumeAsync()`* | `StopAsync()` | `ResetAsync()` |
| 扫码器 | `StartAutoModeAsync()` | `StopAutoModeAsync()` | `StartAutoModeAsync()` | `StopAutoModeAsync()` | 重启序列 |

**注**: *标记的方法为internal访问权限，仅WorkflowManager可调用

## 📖 新增子系统指南

### 步骤1: 实现标准接口
新的子系统控制器应实现以下标准方法：

```csharp
public class NewSubsystemController
{
    public async Task<bool> StartAsync() { /* 启动逻辑 */ }
    public async Task<bool> StopAsync() { /* 停止逻辑 */ }
    public async Task<bool> ResetAsync() { /* 重置逻辑 */ }
    
    // 可选：如果支持暂停/恢复
    internal async Task<bool> PauseAsync() { /* 暂停逻辑 */ }
    internal async Task<bool> ResumeAsync() { /* 恢复逻辑 */ }
    
    public bool IsRunning { get; } // 运行状态属性
}
```

### 步骤2: 在WorkflowManager中添加引用
**文件**: `Managers/WorkflowManager.cs`

```csharp
// 1. 添加私有字段
private NewSubsystemController _newSubsystemController;

// 2. 在InitializeAutoModeControllersAsync中初始化
private async Task InitializeAutoModeControllersAsync()
{
    // ... 现有代码 ...
    _newSubsystemController = NewSubsystemController.Instance;
}
```

### 步骤3: 集成到控制方法中

#### 启动序列集成
```csharp
private async Task<bool> ExecuteWorkflowStartSequenceAsync(string productId)
{
    // ... 现有启动步骤 ...
    
    // 新增步骤: 启动新子系统
    LogHelper.Info("步骤X: 启动新子系统");
    if (_newSubsystemController != null)
    {
        bool result = await _newSubsystemController.StartAsync();
        if (!result)
        {
            LogHelper.Error("新子系统启动失败");
            return false;
        }
    }
}
```

#### 暂停方法集成
```csharp
private async Task PauseAllAutoModeControllersAsync()
{
    // ... 现有暂停逻辑 ...
    
    // 暂停新子系统
    if (_newSubsystemController != null && _newSubsystemController.IsRunning)
    {
        await _newSubsystemController.PauseAsync(); // 或StopAsync()
        LogHelper.Info("新子系统已暂停");
    }
}
```

#### 恢复方法集成
```csharp
private async Task ResumeAllAutoModeControllersAsync()
{
    // ... 现有恢复逻辑 ...
    
    // 恢复新子系统
    if (_newSubsystemController != null && !_newSubsystemController.IsRunning)
    {
        await _newSubsystemController.ResumeAsync(); // 或StartAsync()
        LogHelper.Info("新子系统已恢复");
    }
}
```

#### 停止方法集成
```csharp
private async Task StopAllAutoModeControllersAsync()
{
    // ... 现有停止逻辑 ...
    
    // 停止新子系统
    if (_newSubsystemController != null)
    {
        await _newSubsystemController.StopAsync();
    }
}
```

#### 重置方法集成
```csharp
private async Task ResetAllAutoModeControllersAsync()
{
    // ... 现有重置逻辑 ...
    
    // 重置新子系统
    if (_newSubsystemController != null)
    {
        await _newSubsystemController.ResetAsync();
    }
}
```

### 步骤4: 事件订阅（可选）
如果新子系统有状态变化事件：

```csharp
private async Task SubscribeToManagerEventsAsync()
{
    // ... 现有事件订阅 ...
    
    // 订阅新子系统事件
    if (_newSubsystemController != null)
    {
        _newSubsystemController.StateChanged += OnNewSubsystemStateChanged;
    }
}

private void OnNewSubsystemStateChanged(object sender, StateChangedEventArgs e)
{
    LogHelper.Info($"新子系统状态变化: {e.OldState} -> {e.NewState}");
    // 处理状态变化逻辑
}
```

## ⚠️ 注意事项

### 1. 访问权限控制
- **public方法**: 可以被WorkflowManager和外部调用（如StartAsync, StopAsync, ResetAsync）
- **internal方法**: 仅能被WorkflowManager调用（如PauseAsync, ResumeAsync）
- 避免在UI层直接调用子系统控制器方法

### 2. 异常处理
- 所有控制方法都应使用`ExceptionHelper.SafeExecuteAsync`包装
- 失败时应记录详细日志并返回false
- 不应抛出未处理的异常

### 3. 状态一致性
- 子系统状态变化应及时反映到WorkflowManager
- 使用事件机制通知状态变化
- 避免状态不同步的情况

### 4. 线程安全
- 所有控制方法都应是线程安全的
- 使用适当的同步机制（如SemaphoreSlim）
- 避免死锁和竞态条件

## 📞 技术支持

如需技术支持或有疑问，请参考：
- 开发日志: `Development_Documents/Development_Logs/`
- 架构文档: `Development_Documents/WorkflowManager_Architecture.md`
- API文档: `Development_Documents/API_Documentation.md`

## 🚀 UI集成示例

### 主界面按钮事件处理
**文件**: `UI/MainForm.cs`

```csharp
// 全局启动按钮
private async void GlobalStartBtn_Click(object sender, EventArgs e)
{
    try
    {
        bool result = await WorkflowManager.Instance.StartWorkflowAsync();
        if (result)
        {
            UpdateUIStatus("系统启动成功");
        }
        else
        {
            UpdateUIStatus("系统启动失败");
        }
    }
    catch (Exception ex)
    {
        LogHelper.Error("启动系统时发生异常", ex);
        MessageBox.Show($"启动失败: {ex.Message}");
    }
}

// 全局暂停按钮
private async void GlobalPauseBtn_Click(object sender, EventArgs e)
{
    bool result = await WorkflowManager.Instance.PauseWorkflowAsync();
    UpdateUIStatus(result ? "系统已暂停" : "暂停失败");
}

// 全局停止按钮
private async void GlobalStopBtn_Click(object sender, EventArgs e)
{
    bool result = await WorkflowManager.Instance.StopWorkflowAsync();
    UpdateUIStatus(result ? "系统已停止" : "停止失败");
}

// 全局复位按钮
private async void GlobalResetBtn_Click(object sender, EventArgs e)
{
    bool result = await WorkflowManager.Instance.ResetWorkflowAsync();
    UpdateUIStatus(result ? "系统已复位" : "复位失败");
}
```

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 启动失败
**症状**: StartWorkflowAsync返回false
**可能原因**:
- 某个子系统初始化失败
- 系统处于非Idle/Paused状态
- 硬件连接问题

**排查步骤**:
1. 检查日志文件中的错误信息
2. 验证各子系统的IsInitialized状态
3. 检查硬件连接状态

#### 2. 暂停/恢复不生效
**症状**: 暂停后系统仍在运行，或恢复后系统无响应
**可能原因**:
- 子系统不支持暂停/恢复功能
- 状态同步问题
- 网络通信异常

**排查步骤**:
1. 检查各子系统的暂停/恢复方法实现
2. 验证WorkflowState状态转换
3. 检查网络连接（特别是6轴机器人TCP连接）

#### 3. 状态不一致
**症状**: UI显示状态与实际运行状态不符
**可能原因**:
- 事件订阅缺失
- 状态更新延迟
- 异常处理不当

**排查步骤**:
1. 检查事件订阅是否正确
2. 验证ChangeState方法调用
3. 检查异常处理逻辑

### 调试技巧

#### 1. 日志分析
- 启用详细日志级别
- 关注WorkflowManager相关日志
- 检查各子系统的状态变化日志

#### 2. 状态监控
```csharp
// 添加状态监控事件
WorkflowManager.Instance.WorkflowStateChanged += (sender, e) =>
{
    Console.WriteLine($"工作流状态变化: {e.OldState} -> {e.NewState}");
};
```

#### 3. 性能监控
- 监控各控制方法的执行时间
- 检查内存使用情况
- 观察线程状态

## 📈 性能优化建议

### 1. 异步操作优化
- 使用`ConfigureAwait(false)`避免死锁
- 合理使用并发控制（SemaphoreSlim）
- 避免不必要的异步等待

### 2. 资源管理
- 及时释放不需要的资源
- 使用using语句管理IDisposable对象
- 定期清理缓存数据

### 3. 网络通信优化
- 实现连接池管理
- 添加重试机制
- 优化超时设置

## 🔒 安全考虑

### 1. 权限控制
- 确保internal方法不被外部调用
- 实现操作权限验证
- 记录所有控制操作的审计日志

### 2. 异常安全
- 所有控制方法都应有完整的异常处理
- 避免异常导致系统状态不一致
- 实现异常恢复机制

### 3. 硬件安全
- 实现紧急停止功能
- 添加硬件状态检测
- 确保安全距离和防护措施

---
