# EpsonRobotAutoModeController 架构设计

## 1. 总体架构

### 1.1 设计原则
- **协调器模式**: 不重复实现TCP通信，而是协调现有管理器
- **事件驱动**: 基于事件响应实现业务逻辑
- **状态机管理**: 清晰的状态转换和业务流程控制
- **线程安全**: 独立线程运行，确保并发安全
- **可扩展性**: 支持未来功能扩展和配置调整

### 1.2 系统组件关系
```
EpsonRobotAutoModeController (协调器)
├── EpsonRobotManager (机器人1通信)
├── EpsonRobotManager2 (机器人2通信)  
├── DMC1000BIOManager (IO控制)
├── MultiScannerManager (扫码器管理)
└── ScannerAutoModeManager (扫码器自动模式)
```

## 2. 类结构设计

### 2.1 主控制器类
```csharp
public class EpsonRobotAutoModeController
{
    // 单例模式
    private static readonly Lazy<EpsonRobotAutoModeController> _instance;
    public static EpsonRobotAutoModeController Instance { get; }
    
    // 状态管理
    private EpsonAutoModeState _currentState;
    private readonly object _stateLock = new object();
    
    // 线程控制
    private CancellationTokenSource _cancellationTokenSource;
    private Task _autoModeTask;
    private bool _isRunning;
    
    // 管理器依赖
    private EpsonRobotManager _robot1Manager;
    private EpsonRobotManager2 _robot2Manager;
    private DMC1000BIOManager _ioManager;
    private MultiScannerManager _scannerManager;
    
    // 业务状态跟踪
    private Dictionary<int, RobotWorkflowState> _robotStates;
    private Dictionary<int, string> _pendingMessages;
}
```

### 2.2 状态机设计
```csharp
public enum EpsonAutoModeState
{
    Idle,           // 空闲状态
    Initializing,   // 初始化中
    Running,        // 运行中
    Paused,         // 暂停
    Error,          // 错误状态
    Stopping        // 停止中
}

public enum RobotWorkflowState
{
    Ready,          // 准备就绪
    WaitingPick,    // 等待取料权限
    Picking,        // 取料中
    WaitingNGPut,   // 等待NG放置权限
    WaitingOKPut,   // 等待OK放置权限
    Processing,     // 处理中
    Resetting       // 复位中
}
```

## 3. 事件系统设计

### 3.1 事件定义
```csharp
// 状态变化事件
public event EventHandler<AutoModeStateChangedEventArgs> StateChanged;

// 机器人消息接收事件
public event EventHandler<RobotMessageReceivedEventArgs> MessageReceived;

// 工作流程完成事件
public event EventHandler<WorkflowCompletedEventArgs> WorkflowCompleted;

// 错误事件
public event EventHandler<AutoModeErrorEventArgs> ErrorOccurred;
```

### 3.2 事件参数类
```csharp
public class AutoModeStateChangedEventArgs : EventArgs
{
    public EpsonAutoModeState OldState { get; set; }
    public EpsonAutoModeState NewState { get; set; }
    public string Message { get; set; }
    public DateTime ChangeTime { get; set; }
}

public class RobotMessageReceivedEventArgs : EventArgs
{
    public int RobotId { get; set; }
    public string MessageType { get; set; }
    public string MessageContent { get; set; }
    public DateTime ReceivedTime { get; set; }
}
```

## 4. 业务逻辑流程

### 4.1 初始化流程
```
1. 检查依赖管理器状态
2. 连接两台机器人主端口
3. 执行登录和启动操作
4. 连接数据端口
5. 订阅特殊命令事件
6. 启动监听线程
```

### 4.2 取料权限控制流程
```
接收GETPICK → 识别机器人ID → 检查对应扫码器状态 → 发送ALLOWPICK/DENYPICK
```

### 4.3 数据传输流程
```
接收INPICK → 控制IO输出 → 等待1000ms → 检查IO状态 → 获取扫码数据 → 发送数据
```

### 4.4 NG品处理流程
```
接收GETNGPUT → 检查IO状态 → 发送权限响应
接收NGPUTFULL → 监控IO状态变化 → 检测完整周期 → 发送RESETNGPUT
```

## 5. 线程管理设计

### 5.1 主控制线程
```csharp
private async Task AutoModeMainLoop(CancellationToken cancellationToken)
{
    while (!cancellationToken.IsCancellationRequested)
    {
        try
        {
            // 处理待处理的消息队列
            await ProcessPendingMessages();
            
            // 检查机器人状态
            await CheckRobotStates();
            
            // 执行定期维护任务
            await PerformMaintenance();
            
            await Task.Delay(100, cancellationToken);
        }
        catch (Exception ex)
        {
            HandleError(ex);
        }
    }
}
```

### 5.2 消息处理机制
```csharp
private readonly ConcurrentQueue<RobotMessage> _messageQueue = new ConcurrentQueue<RobotMessage>();

private async Task ProcessPendingMessages()
{
    while (_messageQueue.TryDequeue(out RobotMessage message))
    {
        await ProcessRobotMessage(message);
    }
}
```

## 6. 配置管理

### 6.1 配置类设计
```csharp
public class EpsonAutoModeConfiguration
{
    public int Robot1Id { get; set; } = 1;
    public int Robot2Id { get; set; } = 2;
    public int IOOperationDelayMs { get; set; } = 1000;
    public int ResetDelayMs { get; set; } = 1000;
    public int MessageTimeoutMs { get; set; } = 30000;
    public bool EnableAutoRecovery { get; set; } = true;
    public Dictionary<string, string> IOMapping { get; set; }
    public Dictionary<int, int> ScannerMapping { get; set; }
}
```

## 7. 错误处理策略

### 7.1 错误分类
- **通信错误**: TCP连接断开、消息发送失败
- **IO错误**: IO点位读写失败
- **业务逻辑错误**: 状态不匹配、超时等
- **系统错误**: 内存不足、线程异常等

### 7.2 恢复策略
```csharp
private async Task<bool> AttemptErrorRecovery(Exception error)
{
    switch (error)
    {
        case CommunicationException:
            return await RecoverCommunication();
        case IOException:
            return await RecoverIOOperation();
        case TimeoutException:
            return await RecoverFromTimeout();
        default:
            return await PerformGeneralRecovery();
    }
}
```

## 8. 性能监控

### 8.1 关键指标
- 消息处理延迟
- IO操作响应时间
- 错误发生频率
- 内存使用情况
- 线程状态监控

### 8.2 监控实现
```csharp
public class PerformanceMonitor
{
    private readonly Dictionary<string, TimeSpan> _operationDurations;
    private readonly Dictionary<string, int> _operationCounts;
    private readonly Dictionary<string, int> _errorCounts;
    
    public void RecordOperation(string operationName, TimeSpan duration);
    public void RecordError(string errorType);
    public PerformanceReport GenerateReport();
}
```

## 9. 消息映射和处理

### 9.1 消息类型映射
```csharp
public static class MessageTypes
{
    // 机器人发送的消息
    public const string GET_PICK = "GETPICK";
    public const string IN_PICK = "INPICK";
    public const string GET_NG_PUT = "GETNGPUT";
    public const string NG_PUT_FULL = "NGPUTFULL";
    public const string GET_OK_PUT = "GETOKPUT";

    // 控制器响应的消息
    public const string ALLOW_PICK = "ALLOWPICK";
    public const string DENY_PICK = "DENYPICK";
    public const string ALLOW_NG_PUT = "ALLOWNGPUT";
    public const string DENY_NG_PUT = "DENYNGPUT";
    public const string ALLOW_OK_PUT = "ALLOWOKPUT";
    public const string DENY_OK_PUT = "DENYOKPUT";
    public const string RESET_NG_PUT = "RESETNGPUT";
}
```

### 9.2 IO点位映射
```csharp
public static class IOMapping
{
    // 机器人1相关IO
    public const string ROBOT1_OUTPUT = "O0001";  // 机器人1输出控制
    public const string ROBOT1_INPUT = "I0006";   // 机器人1输入检测
    public const string ROBOT1_NG_STATUS = "I0104"; // 机器人1 NG状态

    // 机器人2相关IO
    public const string ROBOT2_OUTPUT = "O0003";  // 机器人2输出控制
    public const string ROBOT2_INPUT = "I0010";   // 机器人2输入检测
    public const string ROBOT2_NG_STATUS = "I0105"; // 机器人2 NG状态

    // 共用IO
    public const string OK_PUT_STATUS = "I0106";  // OK品放置状态
}
```

### 9.3 扫码器映射
```csharp
public static class ScannerMapping
{
    // 系统中的3个扫码器
    public const int SCANNER_1 = 1;  // 1号扫码器（对应机器人1）
    public const int SCANNER_2 = 2;  // 2号扫码器（对应机器人2）
    public const int SCANNER_3 = 3;  // 3号扫码器（备用或特殊用途）
}
```

## 10. 实现优先级

### 10.1 第一优先级（核心功能）
1. 基础框架和单例模式
2. 初始化连接流程
3. 取料权限控制逻辑
4. 基础消息处理机制

### 10.2 第二优先级（完整业务）
1. 数据传输逻辑
2. NG品处理流程
3. OK品处理流程
4. 复位操作逻辑

### 10.3 第三优先级（增强功能）
1. 错误处理和恢复
2. 性能监控
3. 配置管理
4. 日志记录优化

## 11. 测试策略

### 11.1 单元测试
- 消息解析和处理逻辑
- 状态机转换逻辑
- IO操作模拟测试
- 错误处理机制测试

### 11.2 集成测试
- 与现有管理器的集成
- 完整业务流程测试
- 并发处理能力测试
- 长时间运行稳定性测试

### 11.3 模拟测试环境
```csharp
public class MockEpsonRobotManager : IEpsonRobotManager
{
    public Task<bool> SendDataAsync(string data, string connectionType);
    public event EventHandler<SpecialCommandReceivedEventArgs> SpecialCommandReceived;
}
```

---

**设计完成时间**: 2025年1月26日
**架构状态**: 完成 ✅
**下一步**: 创建数据模型和事件定义
