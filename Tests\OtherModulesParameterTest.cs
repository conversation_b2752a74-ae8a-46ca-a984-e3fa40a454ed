using System;
using System.Threading.Tasks;
using MyHMI.Settings;
using MyHMI.Helpers;

namespace MyHMI.Tests
{
    /// <summary>
    /// 其他模块参数管理测试
    /// </summary>
    public static class OtherModulesParameterTest
    {
        /// <summary>
        /// 运行其他模块参数管理测试
        /// </summary>
        public static async Task RunOtherModulesParameterTestAsync()
        {
            try
            {
                LogHelper.Info("开始其他模块参数管理测试...");

                // 测试1：验证Vision模块参数
                await TestVisionParameters();

                // 测试2：验证IO模块参数
                await TestIOParameters();

                // 测试3：验证System模块参数
                await TestSystemParameters();

                // 测试4：验证Statistics模块参数
                await TestStatisticsParameters();

                // 测试5：验证Workflow模块参数
                await TestWorkflowParameters();

                // 测试6：验证UI模块参数
                await TestUIParameters();

                LogHelper.Info("其他模块参数管理测试完成，所有测试通过！");
            }
            catch (Exception ex)
            {
                LogHelper.Error("其他模块参数管理测试失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 测试Vision模块参数
        /// </summary>
        private static async Task TestVisionParameters()
        {
            LogHelper.Info("测试1：Vision模块参数...");

            await Task.Run(() =>
            {
                var visionSettings = Settings.Settings.Current.Vision;
                
                if (visionSettings == null)
                    throw new Exception("Vision设置为null");

                // 验证摄像头参数
                if (visionSettings.CameraIndex < 0)
                    throw new Exception("摄像头索引无效");

                if (visionSettings.ImageWidth <= 0)
                    throw new Exception("图像宽度无效");

                if (visionSettings.ImageHeight <= 0)
                    throw new Exception("图像高度无效");

                if (visionSettings.DetectionInterval <= 0)
                    throw new Exception("检测间隔无效");

                if (visionSettings.ConfidenceThreshold <= 0 || visionSettings.ConfidenceThreshold > 1)
                    throw new Exception("置信度阈值无效");

                LogHelper.Info($"✓ 摄像头索引: {visionSettings.CameraIndex}");
                LogHelper.Info($"✓ 图像尺寸: {visionSettings.ImageWidth}x{visionSettings.ImageHeight}");
                LogHelper.Info($"✓ 检测间隔: {visionSettings.DetectionInterval}ms");
                LogHelper.Info($"✓ 置信度阈值: {visionSettings.ConfidenceThreshold}");
                LogHelper.Info($"✓ 最大检测时间: {visionSettings.MaxDetectionTime}ms");
                LogHelper.Info($"✓ 保存图像: {visionSettings.SaveImages}");
                LogHelper.Info($"✓ 图像保存路径: {visionSettings.ImageSavePath}");
                LogHelper.Info($"✓ 配置文件路径: {visionSettings.ConfigPath}");
            });

            LogHelper.Info("✓ Vision模块参数测试通过");
        }

        /// <summary>
        /// 测试IO模块参数
        /// </summary>
        private static async Task TestIOParameters()
        {
            LogHelper.Info("测试2：IO模块参数...");

            await Task.Run(() =>
            {
                var ioSettings = Settings.Settings.Current.IO;
                
                if (ioSettings == null)
                    throw new Exception("IO设置为null");

                // 验证IO通道参数
                if (ioSettings.InputChannels <= 0)
                    throw new Exception("输入通道数无效");

                if (ioSettings.OutputChannels <= 0)
                    throw new Exception("输出通道数无效");

                if (ioSettings.ScanInterval <= 0)
                    throw new Exception("扫描间隔无效");

                LogHelper.Info($"✓ 输入通道数: {ioSettings.InputChannels}");
                LogHelper.Info($"✓ 输出通道数: {ioSettings.OutputChannels}");
                LogHelper.Info($"✓ 扫描间隔: {ioSettings.ScanInterval}ms");
                LogHelper.Info($"✓ 启用监控: {ioSettings.EnableMonitoring}");
                LogHelper.Info($"✓ 监控间隔: {ioSettings.MonitoringInterval}ms");
                LogHelper.Info($"✓ 启用日志: {ioSettings.EnableLogging}");
                LogHelper.Info($"✓ 日志路径: {ioSettings.LogPath}");
            });

            LogHelper.Info("✓ IO模块参数测试通过");
        }

        /// <summary>
        /// 测试System模块参数
        /// </summary>
        private static async Task TestSystemParameters()
        {
            LogHelper.Info("测试3：System模块参数...");

            await Task.Run(() =>
            {
                var systemSettings = Settings.Settings.Current.System;
                
                if (systemSettings == null)
                    throw new Exception("System设置为null");

                // 验证系统基本参数
                if (string.IsNullOrEmpty(systemSettings.Name))
                    throw new Exception("系统名称为空");

                if (string.IsNullOrEmpty(systemSettings.Version))
                    throw new Exception("系统版本为空");

                if (systemSettings.AutoSaveInterval <= 0)
                    throw new Exception("自动保存间隔无效");

                if (systemSettings.MotorMonitorInterval <= 0)
                    throw new Exception("电机监控间隔无效");

                if (systemSettings.MaxMotorCount <= 0)
                    throw new Exception("最大电机数量无效");

                LogHelper.Info($"✓ 系统名称: {systemSettings.Name}");
                LogHelper.Info($"✓ 系统版本: {systemSettings.Version}");
                LogHelper.Info($"✓ 最后系统模式: {systemSettings.LastSystemMode}");
                LogHelper.Info($"✓ 启用日志: {systemSettings.LoggingEnabled}");
                LogHelper.Info($"✓ 日志路径: {systemSettings.LogPath}");
                LogHelper.Info($"✓ 自动保存间隔: {systemSettings.AutoSaveInterval}s");
                LogHelper.Info($"✓ 电机监控间隔: {systemSettings.MotorMonitorInterval}ms");
                LogHelper.Info($"✓ 最大电机数量: {systemSettings.MaxMotorCount}");

                // 验证安全管理器参数
                LogHelper.Info($"✓ 安全监控间隔: {systemSettings.SafetyMonitoringInterval}ms");
                LogHelper.Info($"✓ 安全防抖时间: {systemSettings.SafetyDebounceTime}ms");
                LogHelper.Info($"✓ 安全详细日志: {systemSettings.SafetyVerboseLogging}");
                LogHelper.Info($"✓ 安全紧急恢复延迟: {systemSettings.SafetyEmergencyRecoveryDelay}ms");
                LogHelper.Info($"✓ 安全指示灯切换延迟: {systemSettings.SafetyIndicatorSwitchDelay}ms");
                LogHelper.Info($"✓ 安全最大错误重试: {systemSettings.SafetyMaxErrorRetryCount}");
            });

            LogHelper.Info("✓ System模块参数测试通过");
        }

        /// <summary>
        /// 测试Statistics模块参数
        /// </summary>
        private static async Task TestStatisticsParameters()
        {
            LogHelper.Info("测试4：Statistics模块参数...");

            await Task.Run(() =>
            {
                var statsSettings = Settings.Settings.Current.Statistics;
                
                if (statsSettings == null)
                    throw new Exception("Statistics设置为null");

                // 验证统计参数
                if (statsSettings.DataRetentionDays <= 0)
                    throw new Exception("数据保留天数无效");

                if (statsSettings.ExportInterval <= 0)
                    throw new Exception("导出间隔无效");

                if (statsSettings.CacheSize <= 0)
                    throw new Exception("缓存大小无效");

                LogHelper.Info($"✓ 数据保留天数: {statsSettings.DataRetentionDays}");
                LogHelper.Info($"✓ 自动导出: {statsSettings.AutoExport}");
                LogHelper.Info($"✓ 导出间隔: {statsSettings.ExportInterval}小时");
                LogHelper.Info($"✓ 导出路径: {statsSettings.ExportPath}");
                LogHelper.Info($"✓ 缓存大小: {statsSettings.CacheSize}");
                LogHelper.Info($"✓ 启用实时统计: {statsSettings.EnableRealTimeStats}");
                LogHelper.Info($"✓ 统计更新间隔: {statsSettings.StatsUpdateInterval}ms");
            });

            LogHelper.Info("✓ Statistics模块参数测试通过");
        }

        /// <summary>
        /// 测试Workflow模块参数
        /// </summary>
        private static async Task TestWorkflowParameters()
        {
            LogHelper.Info("测试5：Workflow模块参数...");

            await Task.Run(() =>
            {
                var workflowSettings = Settings.Settings.Current.Workflow;
                
                if (workflowSettings == null)
                    throw new Exception("Workflow设置为null");

                // 验证工作流参数
                if (workflowSettings.MaxConcurrentTasks <= 0)
                    throw new Exception("最大并发任务数无效");

                if (workflowSettings.TaskTimeout <= 0)
                    throw new Exception("任务超时无效");

                if (workflowSettings.RetryCount < 0)
                    throw new Exception("重试次数无效");

                if (workflowSettings.RetryDelay <= 0)
                    throw new Exception("重试延迟无效");

                LogHelper.Info($"✓ 启用自动模式: {workflowSettings.EnableAutoMode}");
                LogHelper.Info($"✓ 最大并发任务: {workflowSettings.MaxConcurrentTasks}");
                LogHelper.Info($"✓ 任务超时: {workflowSettings.TaskTimeout}ms");
                LogHelper.Info($"✓ 重试次数: {workflowSettings.RetryCount}");
                LogHelper.Info($"✓ 重试延迟: {workflowSettings.RetryDelay}ms");
                LogHelper.Info($"✓ 启用错误恢复: {workflowSettings.EnableErrorRecovery}");
                LogHelper.Info($"✓ 工作流配置路径: {workflowSettings.WorkflowConfigPath}");
            });

            LogHelper.Info("✓ Workflow模块参数测试通过");
        }

        /// <summary>
        /// 测试UI模块参数
        /// </summary>
        private static async Task TestUIParameters()
        {
            LogHelper.Info("测试6：UI模块参数...");

            await Task.Run(() =>
            {
                var uiSettings = Settings.Settings.Current.UI;
                
                if (uiSettings == null)
                    throw new Exception("UI设置为null");

                // 验证UI参数
                if (uiSettings.RefreshInterval <= 0)
                    throw new Exception("刷新间隔无效");

                if (uiSettings.MaxLogLines <= 0)
                    throw new Exception("最大日志行数无效");

                LogHelper.Info($"✓ 启用自动刷新: {uiSettings.EnableAutoRefresh}");
                LogHelper.Info($"✓ 刷新间隔: {uiSettings.RefreshInterval}ms");
                LogHelper.Info($"✓ 显示详细信息: {uiSettings.ShowDetailedInfo}");
                LogHelper.Info($"✓ 启用声音提示: {uiSettings.EnableSoundAlerts}");
                LogHelper.Info($"✓ 最大日志行数: {uiSettings.MaxLogLines}");
                LogHelper.Info($"✓ 启用状态栏: {uiSettings.EnableStatusBar}");
                LogHelper.Info($"✓ 主题: {uiSettings.Theme}");
                LogHelper.Info($"✓ 语言: {uiSettings.Language}");
            });

            LogHelper.Info("✓ UI模块参数测试通过");
        }
    }
}
