using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

using MyHMI.Helpers;
using MyHMI.Events;
using MyHMI.Models;

namespace MyHMI.Testing
{
    /// <summary>
    /// 测试运行器窗体
    /// </summary>
    public partial class TestRunnerForm : Form
    {
        #region 私有字段
        private Button _runAllTestsButton;
        private Button _runMockTestsButton;
        private Button _runConfigTestsButton;
        private Button _runHelperTestsButton;
        private RichTextBox _resultTextBox;
        private ProgressBar _progressBar;
        private Label _statusLabel;
        private bool _isRunning = false;
        #endregion

        #region 构造函数
        public TestRunnerForm()
        {
            InitializeComponent();
            InitializeUI();
        }
        #endregion

        #region 初始化方法
        /// <summary>
        /// 初始化UI组件
        /// </summary>
        private void InitializeUI()
        {
            // 设置窗体属性
            this.Text = "系统测试运行器";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(600, 400);

            // 创建控件
            CreateControls();
            LayoutControls();
        }

        /// <summary>
        /// 创建控件
        /// </summary>
        private void CreateControls()
        {
            // 按钮组
            var buttonPanel = new Panel
            {
                Height = 50,
                Dock = DockStyle.Top,
                BackColor = Color.LightGray
            };

            _runAllTestsButton = new Button
            {
                Text = "运行所有测试",
                Location = new Point(10, 10),
                Size = new Size(120, 30),
                UseVisualStyleBackColor = true
            };
            _runAllTestsButton.Click += RunAllTestsButton_Click;

            _runMockTestsButton = new Button
            {
                Text = "Mock管理器测试",
                Location = new Point(140, 10),
                Size = new Size(120, 30),
                UseVisualStyleBackColor = true
            };
            _runMockTestsButton.Click += RunMockTestsButton_Click;

            _runConfigTestsButton = new Button
            {
                Text = "配置系统测试",
                Location = new Point(270, 10),
                Size = new Size(120, 30),
                UseVisualStyleBackColor = true
            };
            _runConfigTestsButton.Click += RunConfigTestsButton_Click;

            _runHelperTestsButton = new Button
            {
                Text = "辅助类测试",
                Location = new Point(400, 10),
                Size = new Size(120, 30),
                UseVisualStyleBackColor = true
            };
            _runHelperTestsButton.Click += RunHelperTestsButton_Click;

            buttonPanel.Controls.AddRange(new Control[] 
            { 
                _runAllTestsButton, _runMockTestsButton, _runConfigTestsButton, _runHelperTestsButton 
            });

            // 状态栏
            var statusPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Bottom,
                BackColor = Color.LightGray
            };

            _progressBar = new ProgressBar
            {
                Location = new Point(10, 10),
                Size = new Size(760, 20),
                Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top
            };

            _statusLabel = new Label
            {
                Text = "就绪",
                Location = new Point(10, 35),
                Size = new Size(760, 20),
                Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top
            };

            statusPanel.Controls.AddRange(new Control[] { _progressBar, _statusLabel });

            // 结果显示区
            _resultTextBox = new RichTextBox
            {
                Dock = DockStyle.Fill,
                ReadOnly = true,
                Font = new Font("Consolas", 9F),
                BackColor = Color.Black,
                ForeColor = Color.LightGreen,
                ScrollBars = RichTextBoxScrollBars.Vertical
            };

            // 添加到窗体
            this.Controls.Add(_resultTextBox);
            this.Controls.Add(statusPanel);
            this.Controls.Add(buttonPanel);
        }

        /// <summary>
        /// 布局控件
        /// </summary>
        private void LayoutControls()
        {
            // 控件已通过Dock属性自动布局
        }
        #endregion

        #region 事件处理器
        /// <summary>
        /// 运行所有测试按钮点击事件
        /// </summary>
        private async void RunAllTestsButton_Click(object sender, EventArgs e)
        {
            await RunTestsAsync("所有测试", async () => await SystemTests.RunAllSystemTestsAsync());
        }

        /// <summary>
        /// 运行Mock测试按钮点击事件
        /// </summary>
        private async void RunMockTestsButton_Click(object sender, EventArgs e)
        {
            await RunTestsAsync("Mock管理器测试", async () =>
            {
                var framework = new SimpleTestFramework();
                
                // 只添加Mock管理器相关测试
                framework.AddTest("MockIOManager初始化测试", async () =>
                {
                    var mockIO = MockIOManager.Instance;
                    bool result = await mockIO.InitializeAsync();
                    SimpleTestFramework.AssertTrue(result, "MockIOManager初始化应该成功");
                });

                framework.AddTest("MockMotorManager初始化测试", async () =>
                {
                    var mockMotor = MockMotorManager.Instance;
                    bool result = await mockMotor.InitializeAsync();
                    SimpleTestFramework.AssertTrue(result, "MockMotorManager初始化应该成功");
                });

                return await framework.RunAllTestsAsync();
            });
        }

        /// <summary>
        /// 运行配置测试按钮点击事件
        /// </summary>
        private async void RunConfigTestsButton_Click(object sender, EventArgs e)
        {
            await RunTestsAsync("配置系统测试", async () =>
            {
                var framework = new SimpleTestFramework();
                
                framework.AddTest("配置系统基本功能测试", async () =>
                {
                    Settings.Settings.Load();
                    var config = Settings.Settings.Current;
                    SimpleTestFramework.AssertTrue(config != null,
                        "配置系统应该能够加载配置");
                    await Task.CompletedTask;
                });

                return await framework.RunAllTestsAsync();
            });
        }

        /// <summary>
        /// 运行辅助类测试按钮点击事件
        /// </summary>
        private async void RunHelperTestsButton_Click(object sender, EventArgs e)
        {
            await RunTestsAsync("辅助类测试", async () =>
            {
                var framework = new SimpleTestFramework();
                
                framework.AddTest("LogHelper基本功能测试", () =>
                {
                    LogHelper.Info("测试日志记录");
                    SimpleTestFramework.AssertTrue(true, "日志记录应该成功");
                });

                framework.AddTest("ExceptionHelper安全执行测试", async () =>
                {
                    bool result = await ExceptionHelper.SafeExecuteAsync(async () =>
                    {
                        await Task.Delay(10);
                        return true;
                    }, false, "测试操作");

                    SimpleTestFramework.AssertTrue(result, "正常操作应该成功");
                });

                return await framework.RunAllTestsAsync();
            });
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 运行测试
        /// </summary>
        /// <param name="testName">测试名称</param>
        /// <param name="testAction">测试动作</param>
        /// <returns></returns>
        private async Task RunTestsAsync(string testName, Func<Task<TestResult>> testAction)
        {
            if (_isRunning)
            {
                MessageBox.Show("测试正在运行中，请等待完成", "提示", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                _isRunning = true;
                SetButtonsEnabled(false);
                
                _statusLabel.Text = $"正在运行{testName}...";
                _progressBar.Style = ProgressBarStyle.Marquee;
                _resultTextBox.Clear();
                
                AppendResult($"=== 开始运行{testName} ===", Color.Yellow);
                AppendResult($"开始时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}", Color.Gray);
                AppendResult("", Color.White);

                var result = await testAction();

                // 显示结果
                DisplayTestResult(result);

                _statusLabel.Text = $"{testName}完成 - 通过: {result.PassedTests}, 失败: {result.FailedTests}, 跳过: {result.SkippedTests}";
            }
            catch (Exception ex)
            {
                AppendResult($"测试运行异常: {ex.Message}", Color.Red);
                LogHelper.Error($"{testName}运行异常", ex);
                _statusLabel.Text = $"{testName}运行异常";
            }
            finally
            {
                _progressBar.Style = ProgressBarStyle.Blocks;
                _progressBar.Value = 0;
                SetButtonsEnabled(true);
                _isRunning = false;
            }
        }

        /// <summary>
        /// 显示测试结果
        /// </summary>
        /// <param name="result">测试结果</param>
        private void DisplayTestResult(TestResult result)
        {
            // 显示摘要
            AppendResult("=== 测试摘要 ===", Color.Cyan);
            AppendResult($"总测试数: {result.TotalTests}", Color.White);
            AppendResult($"通过: {result.PassedTests}", Color.Green);
            AppendResult($"失败: {result.FailedTests}", result.FailedTests > 0 ? Color.Red : Color.White);
            AppendResult($"跳过: {result.SkippedTests}", result.SkippedTests > 0 ? Color.Yellow : Color.White);
            AppendResult($"总耗时: {result.TotalTime.TotalMilliseconds:F1}ms", Color.White);
            AppendResult($"成功率: {(result.PassedTests * 100.0 / result.TotalTests):F1}%", Color.White);
            AppendResult("", Color.White);

            // 显示详细结果
            AppendResult("=== 详细结果 ===", Color.Cyan);
            foreach (var testCase in result.TestCaseResults)
            {
                Color statusColor = testCase.Status == TestStatus.Passed ? Color.Green :
                                   testCase.Status == TestStatus.Failed ? Color.Red : Color.Yellow;
                
                string statusText = testCase.Status == TestStatus.Passed ? "PASS" :
                                   testCase.Status == TestStatus.Failed ? "FAIL" : "SKIP";

                AppendResult($"[{statusText}] {testCase.Name} ({testCase.ExecutionTime.TotalMilliseconds:F1}ms)", statusColor);
                
                if (testCase.Status == TestStatus.Failed)
                {
                    AppendResult($"    错误: {testCase.Message}", Color.Red);
                }
                else if (testCase.Status == TestStatus.Skipped)
                {
                    AppendResult($"    原因: {testCase.Message}", Color.Yellow);
                }
            }

            AppendResult("", Color.White);
            AppendResult($"完成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}", Color.Gray);
        }

        /// <summary>
        /// 添加结果文本
        /// </summary>
        /// <param name="text">文本</param>
        /// <param name="color">颜色</param>
        private void AppendResult(string text, Color color)
        {
            _resultTextBox.SelectionStart = _resultTextBox.TextLength;
            _resultTextBox.SelectionLength = 0;
            _resultTextBox.SelectionColor = color;
            _resultTextBox.AppendText(text + Environment.NewLine);
            _resultTextBox.ScrollToCaret();
        }

        /// <summary>
        /// 设置按钮启用状态
        /// </summary>
        /// <param name="enabled">是否启用</param>
        private void SetButtonsEnabled(bool enabled)
        {
            _runAllTestsButton.Enabled = enabled;
            _runMockTestsButton.Enabled = enabled;
            _runConfigTestsButton.Enabled = enabled;
            _runHelperTestsButton.Enabled = enabled;
        }
        #endregion
    }
}
