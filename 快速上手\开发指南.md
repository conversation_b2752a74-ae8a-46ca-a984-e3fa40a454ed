# HR2项目开发指南

## 📋 概述

本指南提供HR2项目的开发规范、最佳实践和扩展方法，帮助开发者快速上手并遵循项目的架构设计原则。

## 🏗️ 开发环境搭建

### 必需软件
- **Visual Studio 2019+**: 推荐使用最新版本
- **.NET Framework 4.8**: 目标框架版本
- **Git**: 版本控制工具
- **NuGet**: 包管理器（VS内置）

### 硬件环境（可选）
- **DMC1000B控制卡**: 电机和IO控制
- **Epson机器人**: TCP/IP通信测试
- **工业相机**: 视觉系统测试
- **扫码器**: 串口通信测试

### 项目配置
1. 克隆项目到本地
2. 使用Visual Studio打开`MyHMI.sln`
3. 还原NuGet包
4. 设置启动项目为`MyHMI`
5. 选择x64平台编译

## 📐 架构设计原则

### 1. 单一职责原则
每个类只负责一个特定功能：
```csharp
// ✅ 正确：专门的电机管理器
public class DMC1000BMotorManager
{
    // 只负责电机控制相关功能
}

// ❌ 错误：混合多种职责
public class MotorAndIOManager
{
    // 同时负责电机和IO，违反单一职责
}
```

### 2. 事件驱动通信
模块间通过事件进行松耦合通信：
```csharp
// ✅ 正确：事件驱动
public class MotorManager
{
    public event EventHandler<MotorStatusEventArgs> StatusChanged;
    
    private void OnStatusChanged(MotorStatusEventArgs args)
    {
        StatusChanged?.Invoke(this, args);
    }
}

// ❌ 错误：直接调用
public class MotorManager
{
    private UIForm _form; // 直接依赖UI
    
    private void UpdateUI()
    {
        _form.UpdateMotorStatus(); // 紧耦合
    }
}
```

### 3. 异步编程模式
所有IO操作必须使用异步方法：
```csharp
// ✅ 正确：异步操作
public async Task<bool> ReadInputAsync(string ioNumber)
{
    return await ExceptionHelper.SafeExecuteAsync(async () =>
    {
        // 异步IO操作
        return await SomeIOOperation();
    }, false, "读取IO");
}

// ❌ 错误：同步阻塞
public bool ReadInput(string ioNumber)
{
    // 同步操作会阻塞UI线程
    return SomeIOOperation();
}
```

## 🔧 添加新硬件设备

### 步骤1：创建管理器类
```csharp
// 文件：Managers/NewDeviceManager.cs
namespace MyHMI.Managers
{
    public class NewDeviceManager
    {
        #region 单例模式
        private static readonly Lazy<NewDeviceManager> _instance = 
            new Lazy<NewDeviceManager>(() => new NewDeviceManager());
        public static NewDeviceManager Instance => _instance.Value;
        private NewDeviceManager() { }
        #endregion

        #region 私有字段
        private bool _isInitialized = false;
        private bool _isConnected = false;
        #endregion

        #region 公共属性
        public bool IsInitialized => _isInitialized;
        public bool IsConnected => _isConnected;
        #endregion

        #region 事件定义
        public event EventHandler<DeviceStatusEventArgs> StatusChanged;
        public event EventHandler<DeviceErrorEventArgs> ErrorOccurred;
        #endregion

        #region 初始化和释放
        public async Task<bool> InitializeAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_isInitialized)
                {
                    LogHelper.Warning("NewDeviceManager已经初始化");
                    return true;
                }

                LogHelper.Info("开始初始化NewDeviceManager...");
                
                // 设备初始化逻辑
                await InitializeDevice();
                
                _isInitialized = true;
                LogHelper.Info("NewDeviceManager初始化完成");
                return true;

            }, false, "NewDeviceManager初始化");
        }

        public async Task DisposeAsync()
        {
            // 清理资源
        }
        #endregion

        #region 设备操作
        public async Task<bool> ConnectAsync()
        {
            // 连接设备
        }

        public async Task<bool> DisconnectAsync()
        {
            // 断开连接
        }
        #endregion

        #region 私有方法
        private async Task InitializeDevice()
        {
            // 具体初始化逻辑
        }
        #endregion
    }
}
```

### 步骤2：创建数据模型
```csharp
// 文件：Models/NewDeviceModels.cs
namespace MyHMI.Models
{
    public class NewDeviceConfiguration
    {
        public string DeviceAddress { get; set; }
        public int Port { get; set; }
        public int Timeout { get; set; }
    }

    public class NewDeviceStatus
    {
        public bool IsOnline { get; set; }
        public string StatusMessage { get; set; }
        public DateTime LastUpdateTime { get; set; }
    }
}
```

### 步骤3：创建事件参数
```csharp
// 文件：Events/NewDeviceEventArgs.cs
namespace MyHMI.Events
{
    public class DeviceStatusEventArgs : EventArgs
    {
        public string DeviceId { get; }
        public NewDeviceStatus Status { get; }
        public DateTime Timestamp { get; }

        public DeviceStatusEventArgs(string deviceId, NewDeviceStatus status)
        {
            DeviceId = deviceId;
            Status = status;
            Timestamp = DateTime.Now;
        }
    }
}
```

### 步骤4：创建UI控件
```csharp
// 文件：UI/Controls/NewDevicePanel.cs
namespace MyHMI.UI.Controls
{
    public partial class NewDevicePanel : UserControl
    {
        private NewDeviceManager _deviceManager;

        public NewDevicePanel()
        {
            InitializeComponent();
            InitializeDeviceManager();
        }

        private void InitializeDeviceManager()
        {
            _deviceManager = NewDeviceManager.Instance;
            _deviceManager.StatusChanged += OnDeviceStatusChanged;
        }

        private void OnDeviceStatusChanged(object sender, DeviceStatusEventArgs e)
        {
            // 确保在UI线程中执行
            if (this.InvokeRequired)
            {
                this.Invoke(new Action<object, DeviceStatusEventArgs>(OnDeviceStatusChanged), sender, e);
                return;
            }

            // 更新UI显示
            UpdateDeviceStatus(e.Status);
        }

        private void UpdateDeviceStatus(NewDeviceStatus status)
        {
            // UI更新逻辑
        }
    }
}
```

### 步骤5：集成到WorkflowManager
```csharp
// 在WorkflowManager中添加新设备
public class WorkflowManager
{
    private NewDeviceManager _newDeviceManager;

    private Task InitializeAutoModeControllersAsync()
    {
        // 获取新设备管理器实例
        _newDeviceManager = NewDeviceManager.Instance;
        
        // 其他初始化逻辑...
    }

    private Task SubscribeToManagerEventsAsync()
    {
        // 订阅新设备事件
        if (_newDeviceManager != null)
        {
            _newDeviceManager.StatusChanged += OnNewDeviceStatusChanged;
            _newDeviceManager.ErrorOccurred += OnNewDeviceErrorOccurred;
        }
        
        // 其他事件订阅...
    }
}
```

## 🎨 添加新UI功能

### 创建用户控件
```csharp
public partial class NewFunctionPanel : UserControl
{
    public NewFunctionPanel()
    {
        InitializeComponent();
        InitializeEvents();
    }

    private void InitializeEvents()
    {
        // 订阅相关Manager的事件
        SomeManager.Instance.SomeEvent += OnSomeEvent;
    }

    private void OnSomeEvent(object sender, SomeEventArgs e)
    {
        // 线程安全的UI更新
        UIHelper.InvokeOnUIThread(this, () =>
        {
            // 更新UI逻辑
        });
    }
}
```

### 集成到MainForm
```csharp
// 在MainForm的_tabConfig中添加新功能
private readonly Dictionary<string, (string id, string name)[]> _tabConfig = 
    new Dictionary<string, (string, string)[]>
{
    ["newmodule"] = new[] { ("newfunction", "新功能") },
    // 其他配置...
};

// 在CreateFunctionPanel中添加创建逻辑
private UserControl CreateFunctionPanel(string panelId)
{
    switch (panelId)
    {
        case "newfunction":
            return new NewFunctionPanel();
        // 其他case...
    }
}
```

## 📊 数据持久化

### 配置数据
```csharp
// 在Settings.cs中添加新配置
public class Settings
{
    public NewDeviceConfiguration NewDevice { get; set; } = new NewDeviceConfiguration();
    
    // 保存配置
    public static void Save()
    {
        try
        {
            string json = JsonConvert.SerializeObject(Current, Formatting.Indented);
            File.WriteAllText(ConfigFilePath, json);
        }
        catch (Exception ex)
        {
            LogHelper.Error("保存配置失败", ex);
        }
    }
}
```

### 生产数据
```csharp
// 扩展ProductionRecord
public class ProductionRecord
{
    // 添加新字段
    public NewDeviceData NewDeviceInfo { get; set; }
    
    // 更新统计逻辑
    public void UpdateStatistics(ProductionStatistics stats)
    {
        // 统计逻辑
    }
}
```

## 🧪 测试开发

### 单元测试
```csharp
// 文件：Testing/NewDeviceManager_Test.cs
public class NewDeviceManager_Test
{
    [Test]
    public async Task InitializeAsync_ShouldReturnTrue_WhenSuccessful()
    {
        // Arrange
        var manager = NewDeviceManager.Instance;
        
        // Act
        bool result = await manager.InitializeAsync();
        
        // Assert
        Assert.IsTrue(result);
        Assert.IsTrue(manager.IsInitialized);
    }
}
```

### 集成测试
```csharp
public class NewDeviceIntegration_Test
{
    [Test]
    public async Task WorkflowIntegration_ShouldWork()
    {
        // 测试新设备与WorkflowManager的集成
    }
}
```

## 🔍 调试技巧

### 日志调试
```csharp
// 使用不同级别的日志
LogHelper.Debug("详细调试信息");
LogHelper.Info("一般信息");
LogHelper.Warning("警告信息");
LogHelper.Error("错误信息", exception);
```

### 脱机模式
```csharp
// 支持无硬件的开发调试
public async Task<bool> ReadInputAsync(string ioNumber)
{
    if (!DMC1000BCardManager.Instance.IsCardAvailable())
    {
        // 脱机模式：返回模拟数据
        return SimulateIOState(ioNumber);
    }
    
    // 正常硬件操作
    return await ReadFromHardware(ioNumber);
}
```

## 📋 代码规范

### 命名规范
- **类名**: PascalCase，如`MotorManager`
- **方法名**: PascalCase，如`InitializeAsync`
- **属性名**: PascalCase，如`IsInitialized`
- **字段名**: camelCase，如`_isInitialized`
- **常量名**: UPPER_CASE，如`MAX_RETRY_COUNT`

### 注释规范
```csharp
/// <summary>
/// 异步初始化电机管理器
/// </summary>
/// <param name="motorId">电机ID</param>
/// <returns>初始化结果</returns>
public async Task<bool> InitializeMotorAsync(int motorId)
{
    // 实现逻辑
}
```

### 异常处理
```csharp
// 使用SafeExecuteAsync包装所有可能抛异常的操作
public async Task<bool> SomeOperationAsync()
{
    return await ExceptionHelper.SafeExecuteAsync(async () =>
    {
        // 可能抛异常的操作
        return await RiskyOperation();
    }, false, "操作名称");
}
```

## 🚀 性能优化

### UI优化
- 使用面板缓存避免重复创建
- 启用双缓冲减少闪烁
- 异步更新避免阻塞UI线程

### 内存管理
- 及时释放不需要的资源
- 使用using语句管理IDisposable对象
- 避免事件订阅导致的内存泄漏

### 并发控制
- 使用SemaphoreSlim进行异步同步
- 避免死锁和竞态条件
- 合理设置超时时间

遵循这些开发指南，可以确保新功能与现有架构保持一致，维护代码的高质量和可维护性。
