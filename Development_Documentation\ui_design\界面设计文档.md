# 界面设计文档

## 系统概述
- **系统名称**：视觉机器人检测系统
- **界面分辨率**：1920 × 1080（固定分辨率）
- **设计方案**：嵌入式面板方案，除系统菜单功能、错误提示等采用弹窗，其他功能均使用面板显示
- **设计风格**：现代化工业风格，深色主题配色

## 界面布局结构
- **顶部区域**：100px高度，包含系统标题和系统菜单栏
- **主要区域**：920px高度，包含左侧菜单(200px)、功能显示区(700px)、右侧区域(1020px)
- **底部状态栏**：60px高度，显示系统运行状态

## 系统菜单栏
- **系统日志**：100px宽度，位置x=20
- **安全设置**：100px宽度，位置x=130
- **外部设备状态**：140px宽度，位置x=240（调整宽度以完整显示文本）
- **退出系统**：120px宽度，位置x=1780

## 一级功能菜单对应二级功能菜单
**说明**：点击一级功能菜单控件，切换到二级菜单对应该控件的子控件菜单

### 功能菜单设计

#### 1. 视觉控制
* **二级功能菜单：定位相机控制**
    * **面板尺寸**：660×840px，深色工业主题
    * **功能区域**：
        - 标题区域：显示"定位相机控制"
        - 参数设置区域：相机参数配置
        - 控制操作区域：相机控制按钮
        - 状态显示区域：相机连接和工作状态
    * **控件布局**：按HTML原型精确布局，统一间距和字体

* **二级功能菜单：对位相机控制**
    * **面板尺寸**：660×840px，深色工业主题
    * **功能区域**：
        - 标题区域：显示"对位相机控制"
        - 参数设置区域：相机参数配置
        - 控制操作区域：相机控制按钮
        - 状态显示区域：相机连接和工作状态
    * **控件布局**：与定位相机控制保持一致的设计风格

#### 2. 电机控制
* **二级功能菜单：翻转电机控制**
    * **面板尺寸**：660×840px，深色工业主题
    * **布局设计**：左右两个翻转电机垂直布局（上下排列）
    * **电机参数**（角度控制）：
        - 脉冲当量：pulse/°（角度单位）
        - 最大加速度：°/s²（角度加速度）
        - 运行速度：°/s（角度速度）
        - 当前角度：°（实时角度显示）
        - 目标角度：°（目标角度设置）
    * **控制功能**：
        - 基本控制：正转、反转、回原点
        - 位置保存：保存位置1、保存位置2、保存位置3
        - 位置移动：移动到位置1、移动到位置2、移动到位置3
    * **控件布局**：每个电机独立参数区域和控制区域

* **二级功能菜单：皮带电机控制**
    * **面板尺寸**：660×840px，深色工业主题
    * **双电机设计**：输入皮带电机和输出皮带电机，垂直布局
    * **电机参数**（每个电机独立设置）：
        - 脉冲当量：pulse/m（线性单位）
        - 最大加速度：mm/s²（线性加速度）
        - 运行速度：mm/s（线性速度）
        - 点动移动距离：mm（点动距离）
    * **控制功能**（每个电机独立控制）：
        - 点动控制：正转点动、反转点动
        - 连续运转：连续正转、连续反转
        - 停止控制：停止按钮
    * **移除功能**：已删除位置控制模块（按用户要求）
    * **控件布局**：两个电机模块垂直排列，保持统一的设计风格

#### 3. 6轴机器人控制
* **二级功能菜单：机器人控制**
    * **面板尺寸**：660×840px，深色工业主题
    * **功能区域**：
        - 通信设置区域：TCP/IP协议配置
        - 控制操作区域：启动、急停、复位按钮
        - 数据发送区域：指令输入框和发送按钮
        - 通信监控区域：实时通信状态显示
    * **控件布局**：按HTML原型的功能区域布局设计

#### 4. Scara控制
* **二级功能菜单：Scara通信管理**
    * **面板尺寸**：660×840px，深色工业主题
    * **功能区域**：
        - 通信协议设置：Modbus TCP配置
        - 连接控制区域：连接、断开按钮
        - 通信监控区域：实时通信状态和数据显示
    * **控件布局**：确保通信管理功能的界面布局符合原型设计

* **二级功能菜单：示教功能**
    * **面板尺寸**：660×840px，深色工业主题
    * **功能区域**：
        - 回零功能：全轴回零和单轴回零控制
        - 四轴控制：X/Y/Z/R轴独立参数设置和点动控制
        - 坐标设置：J2节点（左右）、放置位置（两个）、对位相机位置
        - 抓取参数：抓取高度、抓取速度、放置高度设置
    * **控件布局**：按HTML原型的功能区域布局设计

#### 5. IO控制
* **二级功能菜单：读取IO**
    * **面板尺寸**：660×840px，深色工业主题
    * **IO通道**：I0001-I0016（16个输入通道）
    * **显示方式**：4列网格布局，每个IO状态用指示器显示
    * **状态指示**：绿色（ON）/灰色（OFF）状态指示器
    * **控件布局**：按HTML原型的IO状态显示布局
    * **参考文档**：根据"端口定义文件.md"实现IO状态控件

* **二级功能菜单：写入IO**
    * **面板尺寸**：660×840px，深色工业主题
    * **IO通道**：O0001-O0012（12个输出通道）
    * **控制方式**：4列网格布局，每个IO用控制按钮操作
    * **按钮状态**：蓝色（ON）/灰色（OFF）切换按钮
    * **控件布局**：按HTML原型的IO控制布局
    * **参考文档**：根据"端口定义文件.md"实现IO控制控件

#### 6. 生产日志管理
* **二级功能菜单：生产日志管理**
    * **面板尺寸**：660×840px，深色工业主题
    * **功能区域**：
        - 时间选择区域：月、周、日时间段选择
        - 数据显示区域：总产量、OK量、NG量统计显示
        - 数据操作区域：删除、导出功能按钮
        - 统计图表区域：生产数据可视化显示
    * **控件布局**：按HTML原型的功能区域布局设计

## 界面区域详细设计

### 功能显示区
- **区域尺寸**：700×880px（主要功能显示区域）
- **设计方案**：根据功能菜单选择动态加载对应的控件面板
- **面板切换**：采用嵌入式面板方案，无弹窗设计
- **统一样式**：所有面板采用660×840px尺寸，深色工业主题

### 工业相机显示区
- **相机1显示区**：右侧上方区域，黑色背景空白，预留后期开发
- **相机2显示区**：右侧中间区域，黑色背景空白，预留后期开发
- **显示方式**：实时图像显示，支持图像处理结果叠加

### 生产日志显示区
- **区域位置**：右侧下方区域
- **显示内容**：实时生产数据、统计信息
- **数据更新**：实时更新生产状态和计数信息
- **预留开发**：后期完善数据显示功能

### 控制按钮区
- **按钮组合**：开始、复位、暂停、停止控件
- **按钮样式**：现代化工业风格，颜色区分功能
- **布局方式**：水平排列，统一尺寸和间距

### 状态栏
- **区域高度**：60px，位于界面底部
- **显示内容**：
  - 机器人运行状态：执行中、暂停中、停止（状态指示灯）
  - 左右翻转电机状态：执行中（红色）、空闲中（绿色）
  - 系统时间和连接状态显示
- **样式设计**：深色背景，状态信息清晰可见

## 设计规范

### 颜色配比（现代化工业风格）
- **主背景色**：#34495e（深蓝灰）
- **面板背景色**：#2c3e50（深色面板）
- **边框颜色**：#4a5661（边框线条）
- **主题色**：#3498db（蓝色，用于标题和重要按钮）
- **成功色**：#27ae60（绿色，用于正常状态）
- **警告色**：#f39c12（橙色，用于警告状态）
- **危险色**：#e74c3c（红色，用于错误和停止）
- **文本色**：#ffffff（白色文本）
- **辅助文本色**：#95a5a6（灰色辅助文本）

### 字体规范
- **主标题**：微软雅黑，20px，粗体
- **副标题**：微软雅黑，16px，粗体
- **分组标题**：微软雅黑，14px，粗体
- **正文内容**：微软雅黑，12px，常规
- **按钮文字**：微软雅黑，12px，粗体

### 控件尺寸规范
- **主面板**：660×840px
- **按钮标准尺寸**：100×35px
- **输入框标准尺寸**：80×25px
- **标签标准尺寸**：120×25px
- **统一内边距**：20px
- **控件间距**：10px（小间距）、20px（大间距）

### 布局原则
- **精确定位**：使用绝对定位确保控件位置精确
- **统一间距**：保持一致的内边距和控件间距
- **功能分组**：相关功能控件进行分组显示
- **视觉层次**：通过颜色、字体大小建立清晰的视觉层次
- **响应式设计**：固定1920×1080分辨率，确保显示一致性