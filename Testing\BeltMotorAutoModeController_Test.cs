using System;
using System.Threading.Tasks;
using MyHMI.Managers;
using MyHMI.Helpers;

namespace MyHMI.Testing
{
    /// <summary>
    /// BeltMotorAutoModeController 独立功能测试类
    /// </summary>
    public static class BeltMotorAutoModeControllerTest
    {
        /// <summary>
        /// 执行完整的BeltMotorAutoModeController功能测试
        /// </summary>
        /// <returns>测试结果</returns>
        public static async Task<bool> RunCompleteTestAsync()
        {
            LogHelper.Info("=== 开始BeltMotorAutoModeController独立功能测试 ===");

            try
            {
                // 测试1: 单例模式测试
                bool singletonTest = await TestSingletonPatternAsync();
                if (!singletonTest)
                {
                    LogHelper.Error("单例模式测试失败");
                    return false;
                }

                // 测试2: 初始化测试
                bool initTest = await TestInitializationAsync();
                if (!initTest)
                {
                    LogHelper.Error("初始化测试失败");
                    return false;
                }

                // 测试3: 启动停止测试
                bool startStopTest = await TestStartStopAsync();
                if (!startStopTest)
                {
                    LogHelper.Error("启动停止测试失败");
                    return false;
                }

                // 测试4: 重置测试
                bool resetTest = await TestResetAsync();
                if (!resetTest)
                {
                    LogHelper.Error("重置测试失败");
                    return false;
                }

                // 测试5: 手动皮带控制测试
                bool manualBeltTest = await TestManualBeltControlAsync();
                if (!manualBeltTest)
                {
                    LogHelper.Error("手动皮带控制测试失败");
                    return false;
                }

                LogHelper.Info("=== BeltMotorAutoModeController独立功能测试全部通过 ===");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("BeltMotorAutoModeController测试过程中发生异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 测试单例模式
        /// </summary>
        private static async Task<bool> TestSingletonPatternAsync()
        {
            LogHelper.Info("测试1: 单例模式测试");

            try
            {
                var instance1 = BeltMotorAutoModeController.Instance;
                var instance2 = BeltMotorAutoModeController.Instance;

                if (instance1 == null || instance2 == null)
                {
                    LogHelper.Error("单例实例创建失败");
                    return false;
                }

                if (!ReferenceEquals(instance1, instance2))
                {
                    LogHelper.Error("单例模式验证失败：两个实例不相同");
                    return false;
                }

                LogHelper.Info("单例模式测试通过");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("单例模式测试异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 测试初始化功能
        /// </summary>
        private static async Task<bool> TestInitializationAsync()
        {
            LogHelper.Info("测试2: 初始化功能测试");

            try
            {
                var controller = BeltMotorAutoModeController.Instance;

                // 检查初始状态
                if (controller.IsInitialized)
                {
                    LogHelper.Warning("控制器已经初始化，重置后再测试");
                    await controller.ResetAsync();
                }

                // 执行初始化
                bool initResult = await controller.InitializeAsync();
                if (!initResult)
                {
                    LogHelper.Error("初始化返回失败");
                    return false;
                }

                // 验证初始化状态
                if (!controller.IsInitialized)
                {
                    LogHelper.Error("初始化后状态检查失败");
                    return false;
                }

                if (controller.CurrentState != BeltMotorState.Idle)
                {
                    LogHelper.Error($"初始化后状态错误，期望Idle，实际{controller.CurrentState}");
                    return false;
                }

                // 测试重复初始化
                bool repeatInitResult = await controller.InitializeAsync();
                if (!repeatInitResult)
                {
                    LogHelper.Error("重复初始化测试失败");
                    return false;
                }

                LogHelper.Info("初始化功能测试通过");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("初始化功能测试异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 测试启动停止功能
        /// </summary>
        private static async Task<bool> TestStartStopAsync()
        {
            LogHelper.Info("测试3: 启动停止功能测试");

            try
            {
                var controller = BeltMotorAutoModeController.Instance;

                // 确保已初始化
                if (!controller.IsInitialized)
                {
                    await controller.InitializeAsync();
                }

                // 测试启动
                bool startResult = await controller.StartAsync();
                if (!startResult)
                {
                    LogHelper.Error("启动功能测试失败");
                    return false;
                }

                // 验证启动状态
                if (!controller.IsRunning)
                {
                    LogHelper.Error("启动后运行状态检查失败");
                    return false;
                }

                if (controller.CurrentState != BeltMotorState.Running)
                {
                    LogHelper.Error($"启动后状态错误，期望Running，实际{controller.CurrentState}");
                    return false;
                }

                // 等待一段时间让控制循环运行
                await Task.Delay(2000);

                // 测试停止
                bool stopResult = await controller.StopAsync();
                if (!stopResult)
                {
                    LogHelper.Error("停止功能测试失败");
                    return false;
                }

                // 验证停止状态
                if (controller.IsRunning)
                {
                    LogHelper.Error("停止后运行状态检查失败");
                    return false;
                }

                if (controller.CurrentState != BeltMotorState.Idle)
                {
                    LogHelper.Error($"停止后状态错误，期望Idle，实际{controller.CurrentState}");
                    return false;
                }

                LogHelper.Info("启动停止功能测试通过");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("启动停止功能测试异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 测试重置功能
        /// </summary>
        private static async Task<bool> TestResetAsync()
        {
            LogHelper.Info("测试4: 重置功能测试");

            try
            {
                var controller = BeltMotorAutoModeController.Instance;

                // 启动控制器
                await controller.InitializeAsync();
                await controller.StartAsync();

                // 执行重置
                bool resetResult = await controller.ResetAsync();
                if (!resetResult)
                {
                    LogHelper.Error("重置功能测试失败");
                    return false;
                }

                // 验证重置状态
                if (controller.IsRunning)
                {
                    LogHelper.Error("重置后运行状态检查失败");
                    return false;
                }

                if (controller.CurrentState != BeltMotorState.Idle)
                {
                    LogHelper.Error($"重置后状态错误，期望Idle，实际{controller.CurrentState}");
                    return false;
                }

                LogHelper.Info("重置功能测试通过");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("重置功能测试异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 测试手动皮带控制功能
        /// </summary>
        private static async Task<bool> TestManualBeltControlAsync()
        {
            LogHelper.Info("测试5: 手动皮带控制功能测试");

            try
            {
                var controller = BeltMotorAutoModeController.Instance;

                // 确保已初始化
                if (!controller.IsInitialized)
                {
                    await controller.InitializeAsync();
                }

                // 测试手动启动输出皮带
                LogHelper.Info("测试手动启动输出皮带");
                bool startOutputResult = await controller.StartOutputBeltAsync();
                // 注意：在脱机模式下可能返回false，这是正常的
                LogHelper.Info($"手动启动输出皮带结果: {startOutputResult}");

                // 等待一段时间
                await Task.Delay(1000);

                // 测试手动停止输出皮带
                LogHelper.Info("测试手动停止输出皮带");
                bool stopOutputResult = await controller.StopOutputBeltAsync();
                LogHelper.Info($"手动停止输出皮带结果: {stopOutputResult}");

                LogHelper.Info("手动皮带控制功能测试完成");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("手动皮带控制功能测试异常", ex);
                return false;
            }
        }
    }
}
