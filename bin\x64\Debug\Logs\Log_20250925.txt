[2025-09-25 14:31:51.414] [INFO] 程序启动开始
[2025-09-25 14:31:51.416] [INFO] 配置系统初始化成功
[2025-09-25 14:31:51.649] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-25 14:31:51.652] [INFO] 配置系统初始化完成
[2025-09-25 14:31:51.653] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-25 14:31:51.691] [INFO] 开始初始化各个Manager...
[2025-09-25 14:31:51.692] [INFO] 初始化基础Manager...
[2025-09-25 14:31:51.701] [INFO] IO状态缓存初始化完成
[2025-09-25 14:31:51.710] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-25 14:31:51.710] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-25 14:31:51.835] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-25 14:31:51.835] [ERROR] DMC1000B控制卡初始化失败
[2025-09-25 14:31:51.835] [WARN] DMC1000BIO管理器初始化失败
[2025-09-25 14:31:51.841] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-25 14:31:51.841] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-25 14:31:51.841] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-25 14:31:51.842] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-25 14:31:51.988] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-25 14:31:52.008] [WARN] DMC1000B电机管理器初始化失败
[2025-09-25 14:31:52.008] [INFO] 初始化系统模式管理器...
[2025-09-25 14:31:52.012] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-25 14:31:52.013] [INFO] 当前模式已经是 Manual，无需切换
[2025-09-25 14:31:52.014] [INFO] 开始初始化MotorManager...
[2025-09-25 14:31:52.016] [INFO] 模拟初始化运动控制卡
[2025-09-25 14:31:52.223] [INFO] 加载了8个电机的默认配置
[2025-09-25 14:31:52.225] [INFO] 电机监控任务已启动
[2025-09-25 14:31:52.225] [INFO] MotorManager初始化完成
[2025-09-25 14:31:52.225] [INFO] 初始化通信Manager...
[2025-09-25 14:31:52.227] [INFO] 电机监控循环开始
[2025-09-25 14:31:52.228] [INFO] 开始初始化ScannerManager...
[2025-09-25 14:31:52.230] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-25 14:31:52.235] [INFO] 串口初始化完成
[2025-09-25 14:31:52.249] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-25 14:31:52.253] [INFO] 扫描枪连接成功: COM1
[2025-09-25 14:31:52.253] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-25 14:31:52.253] [INFO] ScannerManager初始化完成
[2025-09-25 14:31:52.256] [INFO] 开始初始化ModbusTcpManager...
[2025-09-25 14:31:52.258] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-25 14:31:52.262] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-25 14:31:57.312] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-25 14:31:57.314] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-25 14:31:57.314] [INFO] ModbusTcpManager初始化完成
[2025-09-25 14:31:57.318] [INFO] 开始初始化EpsonRobotManager...
[2025-09-25 14:31:57.318] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-25 14:31:57.319] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-25 14:31:57.320] [INFO] EpsonRobotManager初始化完成
[2025-09-25 14:31:57.320] [INFO] 初始化视觉Manager...
[2025-09-25 14:31:57.322] [INFO] 开始初始化VisionManager...
[2025-09-25 14:31:57.323] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-25 14:31:57.324] [INFO] 模拟初始化相机，索引: 0
[2025-09-25 14:31:57.836] [INFO] 相机初始化成功
[2025-09-25 14:31:57.837] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-25 14:31:57.838] [INFO] 视觉配置加载完成
[2025-09-25 14:31:57.838] [INFO] VisionManager初始化完成
[2025-09-25 14:31:57.838] [INFO] 初始化数据Manager...
[2025-09-25 14:31:57.841] [INFO] 开始初始化StatisticsManager...
[2025-09-25 14:31:57.842] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-25 14:31:57.846] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-25 14:31:57.846] [INFO] 历史数据加载完成
[2025-09-25 14:31:57.846] [INFO] 自动保存任务已启动
[2025-09-25 14:31:57.846] [INFO] StatisticsManager初始化完成
[2025-09-25 14:31:57.847] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-25 14:31:57.847] [INFO] 所有Manager初始化完成
[2025-09-25 14:31:57.848] [INFO] 自动保存循环开始
[2025-09-25 14:31:57.913] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-25 14:31:57.913] [INFO] 主界面布局创建完成
[2025-09-25 14:31:57.915] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-25 14:32:00.389] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-25 14:32:00.390] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-25 14:32:00.395] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-25 14:32:00.396] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-25 14:32:01.381] [INFO] 翻转电机控制面板资源释放完成
[2025-09-25 14:32:01.388] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-25 14:32:01.389] [INFO] Epson机器人管理器初始化完成
[2025-09-25 14:32:01.428] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
