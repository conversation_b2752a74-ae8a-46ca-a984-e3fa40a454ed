现在开始编写自动化流程
## 开机自检模式
程序打开后自动切换到上一次退出程序时的模式（调试模式或自动模式）
进入自动化工作流时，自动开启开启以下功能：
1. 运动控制卡（dmc1000b）开启初始化，程序运行时，控制卡初始化应该一直保持，程序退出时才关闭初始化
2. 左/右翻转电机自动回零，并移动到位置1
3. 自动对2台6轴机器人连接主端口，启动机器人，激活数据端口（数据传输端口）


## 皮带电机（需要独立线程）
1. 输入/输出皮带电机控制参数由皮带电机面板的ui界面设置，**注意**：需要排除硬编码和参数映射逻辑以及参数永久保存逻辑，确保参数正确传导
2. 输入皮带电机控制逻辑：
  * 输入皮带控制电机有一个传感器：I0004
  * 输入皮带逻辑：
    - 扫描输入传感器状态
    - 当传感器输入为0时，皮带转动
    - 当传感器输入为1时，皮带停止，为0时，皮带继续转动
2. 输出皮带电机控制逻辑：
  * 输出皮带控制电机有一个传感器：I0106
  * 输出皮带逻辑：
    - 扫描输入传感器状态
    - 当传感器输入为0时，皮带停止
    - 当传感器输入为1时，皮带转动，为0时，皮带继续停止

## 左/右翻转电机（scara通信部分）自动模式（独立线程）
   * **说明**：scara动作由第三方开发，利用通信字段与本系统配合自动模式运行，所以开始开发前，先定义一个全局静态类，用于存储各模块的通信字段
### 通信字段定义：
   * 左电机是否准备完毕: bool L_moto_ready ; 右电机是否准备完毕: bool R_moto_ready
   * 左位置是否放置到位: bool L_position_Arrived ; 右电机空闲:bool R_position_Arrived
   * 左夹爪是否夹紧：bool L_gripper_ok ; 右电机空闲: bool R_gripper_ok
   * 左角度矫正是否完成：bool L_Angle_ok ; 右电机空闲: bool R_Angle_ok
   * 左边机器人是否到达安全距离：bool L_safe_ok ; 左边机器人是否到达安全距离：bool L_safe_ok
   * 1号扫描器是否获得数据：bool L_dataget_ok;2号扫描器是否获得数据：bool R_dataget_ok；3号扫描器是否获得数据：bool ML_dataget_ok和bool MR_dataget_ok
   * 左翻转电机是否完成所有工作：bool L_moto_finish；右翻转电机是否完成所有工作：bool R_moto_finish
   * bool all_save，触发停止时，置0，启动和运行时时，值1
### 运行逻辑：
1. 左/右翻转电机回原点，然后到达1号位置（开启自动模式后自动执行，已完成代码开发），bool L_moto_ready和bool R_moto_ready的值置1
2. 左翻转电机等待bool L_position_Arrived的值置1，扫描到该值置1后，O0001（左夹爪气缸）输出1（ture）,等待100ms，扫描I0005，判断状态是否为1，是则bool L_gripper_ok置1，否则输出"左夹爪错误";
    右翻转电机等待bool R_position_Arrived的值置1，扫描到该值置1后，O0003（右夹爪气缸）输出1（ture）,等待100ms，扫描I0009，判断状态是否为1，是则bool R_gripper_ok置1，否则输出"右夹爪错误";
3. 左翻转电机等待bool L_Angle_ok的值置1，扫描到该值置1后，扫描等待bool L_safe_ok的值置1;
    右翻转电机等待bool R_Angle_ok的值置1，扫描到该值置1后，扫描等待bool R_safe_ok的值置1;
4. bool L_safe_ok的值置1后，电机移动到位置2，O0002（左顶料气缸）输出1（ture）,等待100ms，扫描I0007，判断状态是否为1，是则移动到位置3，否则输出"左顶料错误"
    bool R_safe_ok的值置1后，电机移动到位置2，O0004（右顶料气缸）输出1（ture）,等待100ms，扫描I0011，判断状态是否为1，是则移动到位置3，否则输出"右顶料错误"
5. 到达位置3后，O0002（左顶料气缸）输出0（false）,等待100ms，扫描I0008，判断状态是否为1，是则移动到位置4，否则输出"左退料错误"
    到达位置3后，O0004（右顶料气缸）输出0（false）,等待100ms，扫描I0012，判断状态是否为1，是则移动到位置4，否则输出"右退料错误"
6. 左翻转电机到达4位置后，bool L_dataget_ok置1；
    右翻转电机到达4位置后，bool R_dataget_ok置1
   * **注意**：以上所有逻辑所有扫描到的静态字段为1时，执行完该条件动作后，将该静态字段复位为0


## 扫码器自动模式功能
 **说明**本项目的有3个扫码器，1号对应左扫码器，2号对应右扫码枪，3号对应中间扫码器。
 **使用方法**：3个扫码器均使用串口自定义协议通讯，已设定使用串口发送"start"触发扫码，再ui界面中可以分别选择对应的端口，设置相关的参数
 ### 扫码器自动模式开发任务
- 目前扫码器的逻辑存在问题，需要检测其ui控件是否存在硬编码，控件输入参数与业务参数引用没有问题，业务代码是否有问题，然后修正，确保正常能正常使用
- 需要确保程序启动时，需要自动选择的上一次选择端口号，永久保存上一次程序运行时输入的参数
### 自动模式逻辑
1. 程序启动时，需要自动连接3个串口端口，并分别发送“hello”给3个串口，若串口回复“world”则表示窗口通讯正常
2. 3号扫码器等待bool ML_dataget_ok的值置1，置1后，发送触发3号扫码器扫码信号，将扫码枪返回发产品信息保存在中间向左扫码器字符串中;
   3号扫码器等待bool MR_dataget_ok的值置1，置1后，发送触发3号扫码器扫码信号，将扫码枪返回发产品信息保存在中间向右扫码器字符串中
3. 1号扫码器等待bool L_dataget_ok的值置1，置1后，发送触发3号扫码器扫码信号，将扫码枪返回发产品信息保存在左扫码器字符串中
4. 2号扫码器等待bool R_dataget_ok的值置1，置1后，发送触发2号扫码器扫码信号，将扫码枪返回发产品信息保存在右间扫码器字符串中

## 左/右翻转电机（六轴机器人通信部分）自动模式（独立线程）
**说明**这部分通信直接用于本程序的架构进行，旨在通过tcp协议，与2个6轴机器人（epson机器人）进行通信，控制机器人执行步骤.
### 运行逻辑
**说明：** 两个机器人端为服务器端，本项目程序为客户端，程序启动时，必须分别连接这两个主端口（6轴机器人指令收发的端口），然后自动登录并发送启动指令，最后连接数据端口（6轴机器人数据收发的端口）
1. 扫描机器人和机器人2的数据端口
2. 扫描到机器人1发送“GETPICK”字符串数据时，表示6轴机器人1在询问是否允许取料，程序判断1号扫码器是否完成扫码，是则发送“ALLOWPICK”给该机器人，否则发送“DENYPICK”给该机器人；
    扫描到机器人2发送“GETPICK”字符串数据时，表示6轴机器人2在询问是否允许取料，程序判断2号扫码器是否完成扫码，是则发送“ALLOWPICK”给该机器人，否则发送“DENYPICK”给该机器人
3. 程序发送“ALLOWPICK”数据给机器人1/机器人2后，等待机器人1/机器人2发送“INPICK”字符串数据过来；
4. 程序收到机器人1“INPICK”字符串数据后，O0001输出0，等待100ms，扫描I0006，判断状态是否为1 则发送“中间向左扫码器字符串,左扫码器字符串”给机器人1；
   程序收到机器人2“INPICK”字符串数据后，O0003输出0，等待100ms，扫描I0010，判断状态是否为1 则发送“中间向右扫码器字符串,右扫码器字符串”给机器人2
5. 程序发送完数据后，等待1s，然后自动复位到位置1，并置bool L_moto_ready/bool R_moto_ready的值为1
6. 当机器人1发送“GETNGPUT”,程序扫描I0104，若I0104为1，发送ALLOWNGPUT给机器人1。若I0104为0，发送DENYNGPUT给机器人1；
    当机器人2发送“GETNGPUT”,程序扫描I0105，若I0105为1，发送ALLOWNGPUT给机器人2。若I0105为0，发送DENYNGPUT给机器人2
7. 当机器人1发送“NGPUTFULL”给程序，程序扫描I0104由1变为0，再又0变为1后，发送RESETNGPUT给机器人1；
    当机器人1发送“NGPUTFULL”给程序，程序扫描I0104由1变为0，再又0变为1后，发送RESETNGPUT给机器人1
8. 当机器人1/机器人2发送“GETOKPUT”,程序扫描I0106，若I0106为0，发送ALLOWOKPUT给机器人1/机器人2。若I0106为1，发送DENYOKPUT给机器人1/机器人2；
本项目已经开发完成的2台六轴机器人（epson机器人）通信，但没有自动模式收发信息，请查看已开发的功能，创建新的自动模式控制器类来管理六轴机器人通信自动化模式运行。
注意，两台机器人的主端口值负责连接，登录，启动，停止等指令（这部分已开发），以上字符串数据收发功能，均由他们的数据端口收发

## 安全管理（独立线程）
### 紧急停止
1. 若I0102/I0103为1或I0101为0,紧急机器人紧急停止
2. I0001状态，若有电平变化，0-1或1-0,为启动机器人
3. I0002状态，若有电平变化，0-1或1-0,为停止程序和机器人所有动作，再次点击启动后，程序复位，并继续程序执行
4. I0003状态为1，紧急停止程序所有操作，需要成为为0时，才可以再次启动程序
### 指示灯
1. 程序在调试模式，停止时，黄灯亮（O0010输出为1）
2. 程序正在自动模式正常运行时，绿灯亮（O0009输出为1）
3. 程序出现报错，紧急停止时，红灯亮（O0008输出为1）
4. 以上状态切换时，先把原来的状态置0，再把新状态置1

