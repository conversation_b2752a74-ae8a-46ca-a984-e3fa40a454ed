# MyHMI 上位机控制系统 - 项目结构说明

## 概述

MyHMI 是一个基于 WinForms 和 .NET Framework 4.8 的上位机控制系统，采用模块化架构设计，支持多种硬件设备的集成控制。

## 项目根目录结构

```
MyHMI/
├── App.config                     # 应用程序配置文件
├── MyHMI.sln                      # Visual Studio 解决方案文件
├── MyHMI.csproj                   # 项目文件
├── Program.cs                     # 程序入口点
├── packages.config                # NuGet 包配置文件
├── bin/                           # 编译输出目录
├── obj/                           # 编译中间文件目录
├── packages/                      # NuGet 包目录
└── [各功能模块目录]
```

## 核心功能模块目录

### 1. Config/ - 配置管理模块
```
Config/
├── SystemConfig.json              # JSON 格式系统配置文件
└── SystemConfiguration.cs         # 配置管理类
```

**职责**：
- 管理系统配置参数
- 提供配置文件的加载、保存和重新加载功能
- 支持 JSON 格式配置文件
- 实现单例模式，全局配置访问

### 2. Managers/ - 核心管理器模块
```
Managers/
├── IOManager.cs                   # IO 控制管理器
├── MotorManager.cs                # 电机控制管理器
├── ModbusTcpManager.cs            # Modbus TCP 通信管理器
├── RobotTcpManager.cs             # 机器人 TCP 通信管理器
├── ScannerManager.cs              # 扫描枪管理器
├── StatisticsManager.cs           # 统计数据管理器
├── VisionManager.cs               # 视觉系统管理器
└── WorkflowManager.cs             # 工作流管理器
```

**职责**：
- 各类硬件设备的控制和管理
- 实现设备的初始化、操作和状态监控
- 提供异步操作接口
- 实现事件驱动的状态通知
- 采用单例模式确保全局唯一性

### 3. Models/ - 数据模型模块
```
Models/
├── CommunicationModels.cs         # 通信相关数据模型
├── ConfigModels.cs                # 配置相关数据模型
├── MotorModels.cs                 # 电机相关数据模型
└── ProductionModels.cs            # 生产相关数据模型
```

**职责**：
- 定义系统中使用的数据结构
- 提供强类型的数据模型
- 支持数据序列化和反序列化
- 包含业务逻辑相关的数据验证

### 4. Events/ - 事件参数模块
```
Events/
├── CommunicationEventArgs.cs      # 通信事件参数
├── IOEventArgs.cs                 # IO 事件参数
└── MotorEventArgs.cs              # 电机事件参数
```

**职责**：
- 定义各种事件的参数类
- 支持事件驱动架构
- 提供类型安全的事件数据传递
- 实现模块间的解耦通信

### 5. Helpers/ - 辅助工具模块
```
Helpers/
├── ConfigHelper.cs                # 配置辅助类
├── ExceptionHelper.cs             # 异常处理辅助类
├── LogHelper.cs                   # 日志辅助类
└── UIHelper.cs                    # UI 辅助类
```

**职责**：
- 提供通用的辅助功能
- 封装常用操作，提高代码复用性
- 实现线程安全的操作
- 提供统一的错误处理和日志记录

### 6. UI/ - 用户界面模块
```
UI/
├── MainForm.cs                    # 主窗体
├── MainForm.Designer.cs           # 主窗体设计器文件
└── Controls/                      # 用户控件目录
    ├── CommunicationPanel.cs      # 通信控制面板
    ├── IOControlPanel.cs          # IO 控制面板
    ├── LogPanel.cs                # 日志显示面板
    ├── MotorControlPanel.cs       # 电机控制面板
    ├── StatisticsPanel.cs         # 统计数据面板
    └── VisionPanel.cs             # 视觉系统面板
```

**职责**：
- 提供用户交互界面
- 实现模块化的 UI 组件
- 支持实时数据显示和控制
- 实现线程安全的 UI 更新

### 7. Testing/ - 测试模块
```
Testing/
├── MockIOManager.cs               # IO 管理器 Mock 类
├── MockMotorManager.cs            # 电机管理器 Mock 类
├── SimpleTestFramework.cs         # 简单测试框架
├── SystemTests.cs                 # 系统测试用例
├── TestRunnerForm.cs              # 测试运行器窗体
└── TestRunnerForm.Designer.cs     # 测试运行器设计器文件
```

**职责**：
- 提供无硬件环境下的测试支持
- 实现 Mock 对象模拟硬件行为
- 提供单元测试框架
- 支持自动化测试和验证

### 8. Properties/ - 项目属性模块
```
Properties/
├── AssemblyInfo.cs                # 程序集信息
├── Resources.resx                 # 资源文件
├── Resources.Designer.cs          # 资源设计器文件
├── Settings.settings              # 应用程序设置
└── Settings.Designer.cs           # 设置设计器文件
```

**职责**：
- 管理程序集元数据
- 存储应用程序资源
- 管理用户设置和配置

### 9. 其他目录

#### Data/ - 数据存储目录
- 存储生产数据、统计信息等持久化数据
- 支持 JSON、CSV、Excel 等多种格式

#### Logs/ - 日志目录
- 存储系统运行日志
- 按日期和级别分类存储
- 支持日志轮转和清理

#### References/ - 引用目录
- 存储第三方 DLL 文件
- 硬件厂商提供的 SDK 库

## 架构特点

### 1. 模块化设计
- 每个功能模块独立开发和维护
- 模块间通过接口和事件进行通信
- 支持模块的独立测试和部署

### 2. 事件驱动架构
- 采用发布-订阅模式
- 实现模块间的松耦合
- 支持异步事件处理

### 3. 单例模式
- 所有 Manager 类采用单例模式
- 确保全局状态的一致性
- 简化对象管理和资源控制

### 4. 异步编程
- 大量使用 async/await 模式
- 避免 UI 线程阻塞
- 提高系统响应性能

### 5. 线程安全
- 实现线程安全的 UI 更新
- 使用锁机制保护共享资源
- 支持多线程并发操作

## 依赖关系

```
UI Layer (MainForm, UserControls)
    ↓
Manager Layer (各种 Manager 类)
    ↓
Helper Layer (辅助类)
    ↓
Model Layer (数据模型)
    ↓
Event Layer (事件参数)
```

## 扩展指南

### 添加新的硬件管理器
1. 在 `Managers/` 目录创建新的管理器类
2. 实现单例模式和异步接口
3. 在 `Models/` 中定义相关数据模型
4. 在 `Events/` 中定义事件参数
5. 在 `UI/Controls/` 中创建对应的控制面板
6. 在 `WorkflowManager` 中集成新管理器

### 添加新的数据模型
1. 在 `Models/` 目录创建新的模型类
2. 实现必要的序列化接口
3. 在配置文件中添加相关配置项
4. 更新相关的管理器类

### 添加新的 UI 组件
1. 在 `UI/Controls/` 目录创建新的 UserControl
2. 实现数据绑定和事件处理
3. 在 `MainForm` 中集成新组件
4. 确保线程安全的 UI 更新

这个项目结构设计充分考虑了可维护性、可扩展性和可测试性，为上位机控制系统的开发提供了坚实的基础。
