# 皮带电机自动控制功能实现日志

## 项目信息
- **开发日期**: 2025-09-24
- **开发人员**: AI Assistant
- **任务描述**: 实现皮带电机的自动模式流程，包括独立线程控制和传感器逻辑
- **项目路径**: E:\projects\C#_projects\HR2

## 需求分析

### 用户需求
1. **输入/输出皮带电机控制参数**：由皮带电机面板的UI界面设置
2. **排除硬编码和参数映射逻辑**：确保参数正确传导
3. **参数永久保存逻辑**：确保参数持久化
4. **输入皮带电机控制逻辑**：
   - 传感器：I0004
   - 当传感器输入为0时，皮带转动
   - 当传感器输入为1时，皮带停止
   - 为0时，皮带继续转动
5. **输出皮带电机控制逻辑**：
   - 传感器：I0106
   - 当传感器输入为0时，皮带停止
6. **独立线程控制**：需要独立线程运行

## 解决方案设计

### 1. 架构设计
创建`BeltMotorAutoControlManager`单例管理器，负责：
- 独立线程控制两个皮带电机
- 实时监控传感器状态
- 根据传感器状态自动控制电机启停
- 与现有的DMC1000BMotorManager和DMC1000BIOManager集成

### 2. 技术实现方案

#### 2.1 管理器架构
```csharp
public class BeltMotorAutoControlManager
{
    // 单例模式
    public static BeltMotorAutoControlManager Instance { get; }
    
    // 线程安全控制
    private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);
    private CancellationTokenSource _cancellationTokenSource;
    
    // 独立控制线程
    private Task _inputBeltControlTask;
    private Task _outputBeltControlTask;
}
```

#### 2.2 传感器监控逻辑
**输入皮带控制逻辑**：
```csharp
// 传感器I0004状态监控
bool currentSensorState = await _ioManager.ReadInputAsync("I0004");

if (currentSensorState == false && !isMotorRunning) {
    // 传感器为0，启动皮带
    await _motorManager.BeltMotorContinuousRunAsync(INPUT_BELT_AXIS, true);
    isMotorRunning = true;
}
else if (currentSensorState == true && isMotorRunning) {
    // 传感器为1，停止皮带
    await _motorManager.StopMotorAsync(INPUT_BELT_AXIS);
    isMotorRunning = false;
}
```

**输出皮带控制逻辑**：
```csharp
// 传感器I0106状态监控
bool currentSensorState = await _ioManager.ReadInputAsync("I0106");

if (currentSensorState == false && isMotorRunning) {
    // 传感器为0，停止皮带
    await _motorManager.StopMotorAsync(OUTPUT_BELT_AXIS);
    isMotorRunning = false;
}
```

#### 2.3 线程控制设计
- **独立线程**：每个皮带电机使用独立的控制线程
- **取消机制**：使用CancellationToken支持优雅停止
- **异常处理**：完整的异常处理和恢复机制
- **状态监控**：实时监控电机运行状态

## 实现细节

### 1. 新增文件

#### BeltMotorAutoControlManager.cs
**位置**：`Managers/BeltMotorAutoControlManager.cs`
**功能**：皮带电机自动控制核心管理器

**主要方法**：
- `StartAutoControlAsync()`: 启动自动控制
- `StopAutoControlAsync()`: 停止自动控制
- `InputBeltControlLoopAsync()`: 输入皮带控制循环
- `OutputBeltControlLoopAsync()`: 输出皮带控制循环
- `StartOutputBeltAsync()`: 手动启动输出皮带
- `StopOutputBeltAsync()`: 手动停止输出皮带

**技术特点**：
- 单例模式确保全局唯一实例
- SemaphoreSlim提供线程安全控制
- 独立线程避免阻塞主线程
- 完整的异常处理和日志记录

### 2. 修改文件

#### StartupSelfCheckManager.cs
**修改内容**：
1. 在SelfCheckResult中添加`BeltMotorAutoControlSuccess`字段
2. 在开机自检流程中添加步骤4：启动皮带电机自动控制
3. 添加`StartBeltMotorAutoControlAsync()`方法
4. 更新结果摘要显示

**集成逻辑**：
```csharp
// 步骤4：启动皮带电机自动控制
await StartBeltMotorAutoControlAsync(result);

private async Task StartBeltMotorAutoControlAsync(SelfCheckResult result)
{
    var beltControlManager = BeltMotorAutoControlManager.Instance;
    bool startResult = await beltControlManager.StartAutoControlAsync();
    result.BeltMotorAutoControlSuccess = startResult;
}
```

#### MyHMI.csproj
**修改内容**：添加新的管理器文件到编译列表

### 3. 参数传导验证

#### UI参数获取逻辑
**MotorBeltPanel.cs**中的参数获取方法：
```csharp
private BeltMotorParams GetInputBeltParamsFromUI()
{
    return new BeltMotorParams
    {
        MotorName = "输入皮带电机",
        PulseEquivalent = ParseDoubleFromTextBox(_inputPulseEquivalentTextBox, 0.01),
        MaxSpeed = ParseDoubleFromTextBox(_inputRunSpeedTextBox, 100),
        StartSpeed = ParseDoubleFromTextBox(_inputRunSpeedTextBox, 100) * 0.1,
        Acceleration = ParseDoubleFromTextBox(_inputMaxAccelerationTextBox, 500),
        JogDistance = ParseDoubleFromTextBox(_inputJogDistanceTextBox, 10)
    };
}
```

**验证结果**：
- ✅ 脉冲当量：从UI文本框读取，无硬编码
- ✅ 最大速度：从UI文本框读取，无硬编码
- ✅ 加速度：从UI文本框读取，无硬编码
- ✅ 点动距离：从UI文本框读取，无硬编码
- ⚠️ 起始速度：设为最大速度的10%（合理的设计选择）

#### 参数持久化验证
**DMC1000BMotorManager.cs**中的保存逻辑：
```csharp
public async Task<bool> SetBeltMotorParamsAsync(short axis, BeltMotorParams parameters)
{
    // 保存到内存
    lock (_motorLock) {
        _beltMotorParams[axis] = parameters;
    }
    
    // 异步保存到配置文件
    _ = Task.Run(async () => await SaveBeltMotorConfigAsync());
}
```

**验证结果**：
- ✅ 参数设置时自动保存到配置文件
- ✅ 程序启动时自动从配置文件加载
- ✅ 支持JSON格式持久化存储

## 控制流程

### 1. 启动流程
1. **系统模式切换到自动模式**
2. **开机自检执行**：
   - DMC1000B控制卡初始化
   - 翻转电机归零和移动
   - 双机器人连接
   - **皮带电机自动控制启动** ← 新增步骤
3. **皮带电机自动控制启动**：
   - 初始化依赖管理器
   - 启动输入皮带控制线程
   - 启动输出皮带控制线程

### 2. 运行流程
**输入皮带控制线程**：
```
循环监控I0004传感器 → 状态变化检测 → 电机控制决策 → 执行控制命令
```

**输出皮带控制线程**：
```
循环监控I0106传感器 → 状态变化检测 → 电机停止决策 → 执行停止命令
```

### 3. 停止流程
1. **发送取消信号**
2. **等待线程完成**
3. **停止所有皮带电机**
4. **清理资源**

## 技术特点

### 1. 线程安全
- 使用SemaphoreSlim进行异步线程同步
- 避免传统lock在异步方法中的问题
- 支持超时机制防止死锁

### 2. 异常处理
- 每个控制循环都有完整的异常处理
- 异常后自动恢复机制
- 详细的日志记录

### 3. 资源管理
- 使用CancellationToken支持优雅停止
- 确保线程结束时电机停止
- 自动清理资源

### 4. 脱机测试支持
- 硬件未连接时记录警告但不阻止流程
- 支持调试阶段的脱机测试
- 与开机自检的脱机测试模式一致

## 编译验证
- ✅ 编译成功，无语法错误
- ✅ 所有依赖正确引用
- ✅ 项目文件正确更新

## 测试建议

### 1. 单元测试
- 测试传感器状态变化的响应
- 测试电机启停控制逻辑
- 测试异常情况的处理

### 2. 集成测试
- 测试与开机自检流程的集成
- 测试与现有电机管理器的协作
- 测试脱机模式和在线模式

### 3. 压力测试
- 长时间运行稳定性测试
- 频繁传感器状态变化测试
- 并发访问安全性测试

**最终状态**：✅ 皮带电机自动控制功能已完整实现，支持独立线程控制和传感器逻辑
