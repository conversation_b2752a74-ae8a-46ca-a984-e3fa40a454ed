[2025-09-23 11:07:14.556] [INFO] 程序启动开始
[2025-09-23 11:07:14.558] [INFO] 配置系统初始化成功
[2025-09-23 11:07:14.780] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-23 11:07:14.780] [INFO] 配置系统初始化完成
[2025-09-23 11:07:14.780] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-23 11:07:14.810] [INFO] 开始初始化各个Manager...
[2025-09-23 11:07:14.810] [INFO] 初始化基础Manager...
[2025-09-23 11:07:14.817] [INFO] IO状态缓存初始化完成
[2025-09-23 11:07:14.822] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 11:07:14.823] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 11:07:14.824] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 11:07:14.825] [ERROR] DMC1000B控制卡初始化失败
[2025-09-23 11:07:14.825] [WARN] DMC1000BIO管理器初始化失败
[2025-09-23 11:07:14.829] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-23 11:07:14.829] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 11:07:14.829] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 11:07:14.830] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 11:07:14.876] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__42_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 130
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 11:07:14.890] [WARN] DMC1000B电机管理器初始化失败
[2025-09-23 11:07:14.890] [INFO] 初始化系统模式管理器...
[2025-09-23 11:07:14.895] [INFO] 开始初始化MotorManager...
[2025-09-23 11:07:14.897] [INFO] 模拟初始化运动控制卡
[2025-09-23 11:07:15.112] [INFO] 加载了8个电机的默认配置
[2025-09-23 11:07:15.113] [INFO] 电机监控任务已启动
[2025-09-23 11:07:15.113] [INFO] MotorManager初始化完成
[2025-09-23 11:07:15.114] [INFO] 初始化通信Manager...
[2025-09-23 11:07:15.115] [INFO] 电机监控循环开始
[2025-09-23 11:07:15.116] [INFO] 开始初始化ScannerManager...
[2025-09-23 11:07:15.120] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-23 11:07:15.122] [INFO] 串口初始化完成
[2025-09-23 11:07:15.124] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-23 11:07:15.127] [INFO] 扫描枪连接成功: COM1
[2025-09-23 11:07:15.128] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-23 11:07:15.128] [INFO] ScannerManager初始化完成
[2025-09-23 11:07:15.130] [INFO] 开始初始化ModbusTcpManager...
[2025-09-23 11:07:15.132] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-23 11:07:15.134] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-23 11:07:20.188] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 11:07:20.190] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-23 11:07:20.191] [INFO] ModbusTcpManager初始化完成
[2025-09-23 11:07:20.195] [INFO] 开始初始化EpsonRobotManager...
[2025-09-23 11:07:20.197] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-23 11:07:20.197] [INFO] EpsonRobotManager初始化完成
[2025-09-23 11:07:20.198] [INFO] 初始化视觉Manager...
[2025-09-23 11:07:20.201] [INFO] 开始初始化VisionManager...
[2025-09-23 11:07:20.201] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-23 11:07:20.203] [INFO] 模拟初始化相机，索引: 0
[2025-09-23 11:07:20.711] [INFO] 相机初始化成功
[2025-09-23 11:07:20.712] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-23 11:07:20.712] [INFO] 视觉配置加载完成
[2025-09-23 11:07:20.713] [INFO] VisionManager初始化完成
[2025-09-23 11:07:20.713] [INFO] 初始化数据Manager...
[2025-09-23 11:07:20.715] [INFO] 开始初始化StatisticsManager...
[2025-09-23 11:07:20.720] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-23 11:07:20.724] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-23 11:07:20.725] [INFO] 历史数据加载完成
[2025-09-23 11:07:20.725] [INFO] 自动保存任务已启动
[2025-09-23 11:07:20.725] [INFO] StatisticsManager初始化完成
[2025-09-23 11:07:20.725] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-23 11:07:20.725] [INFO] 所有Manager初始化完成
[2025-09-23 11:07:20.727] [INFO] 自动保存循环开始
[2025-09-23 11:07:20.776] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 11:07:20.776] [INFO] 主界面布局创建完成
[2025-09-23 11:07:20.778] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-23 11:07:22.378] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 11:07:22.378] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 11:07:22.471] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 11:07:22.536] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 11:07:22.541] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 11:07:22.541] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 11:07:36.778] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 11:07:36.785] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 11:07:37.718] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 11:07:37.718] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 11:07:37.775] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 11:07:37.839] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 11:07:37.840] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 11:07:37.840] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 11:07:38.271] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 11:07:38.277] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 11:07:38.278] [INFO] Epson机器人管理器初始化完成
[2025-09-23 11:07:38.309] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-23 11:07:39.215] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 11:07:39.216] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 11:07:39.272] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 11:07:39.330] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 11:07:39.331] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 11:07:39.331] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 11:07:42.823] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 11:07:42.827] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 11:07:42.827] [INFO] Epson机器人管理器初始化完成
[2025-09-23 11:07:42.830] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-23 11:07:43.752] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-23 11:07:45.056] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 11:07:45.056] [INFO] Epson机器人管理器初始化完成
[2025-09-23 11:07:45.060] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-23 11:08:15.885] [INFO] 程序启动开始
[2025-09-23 11:08:15.887] [INFO] 配置系统初始化成功
[2025-09-23 11:08:15.951] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-23 11:08:15.951] [INFO] 配置系统初始化完成
[2025-09-23 11:08:15.952] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-23 11:08:15.975] [INFO] 开始初始化各个Manager...
[2025-09-23 11:08:15.975] [INFO] 初始化基础Manager...
[2025-09-23 11:08:15.980] [INFO] IO状态缓存初始化完成
[2025-09-23 11:08:15.985] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 11:08:15.985] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 11:08:15.987] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 11:08:15.987] [ERROR] DMC1000B控制卡初始化失败
[2025-09-23 11:08:15.987] [WARN] DMC1000BIO管理器初始化失败
[2025-09-23 11:08:15.991] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-23 11:08:15.991] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 11:08:15.992] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 11:08:15.992] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 11:08:16.028] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__42_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 130
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 11:08:16.041] [WARN] DMC1000B电机管理器初始化失败
[2025-09-23 11:08:16.041] [INFO] 初始化系统模式管理器...
[2025-09-23 11:08:16.045] [INFO] 开始初始化MotorManager...
[2025-09-23 11:08:16.047] [INFO] 模拟初始化运动控制卡
[2025-09-23 11:08:16.258] [INFO] 加载了8个电机的默认配置
[2025-09-23 11:08:16.259] [INFO] 电机监控任务已启动
[2025-09-23 11:08:16.259] [INFO] MotorManager初始化完成
[2025-09-23 11:08:16.260] [INFO] 初始化通信Manager...
[2025-09-23 11:08:16.261] [INFO] 电机监控循环开始
[2025-09-23 11:08:16.263] [INFO] 开始初始化ScannerManager...
[2025-09-23 11:08:16.267] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-23 11:08:16.270] [INFO] 串口初始化完成
[2025-09-23 11:08:16.272] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-23 11:08:16.275] [INFO] 扫描枪连接成功: COM1
[2025-09-23 11:08:16.275] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-23 11:08:16.276] [INFO] ScannerManager初始化完成
[2025-09-23 11:08:16.278] [INFO] 开始初始化ModbusTcpManager...
[2025-09-23 11:08:16.279] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-23 11:08:16.281] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-23 11:08:21.331] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 11:08:21.331] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-23 11:08:21.331] [INFO] ModbusTcpManager初始化完成
[2025-09-23 11:08:21.334] [INFO] 开始初始化EpsonRobotManager...
[2025-09-23 11:08:21.336] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-23 11:08:21.336] [INFO] EpsonRobotManager初始化完成
[2025-09-23 11:08:21.336] [INFO] 初始化视觉Manager...
[2025-09-23 11:08:21.339] [INFO] 开始初始化VisionManager...
[2025-09-23 11:08:21.340] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-23 11:08:21.341] [INFO] 模拟初始化相机，索引: 0
[2025-09-23 11:08:21.860] [INFO] 相机初始化成功
[2025-09-23 11:08:21.861] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-23 11:08:21.861] [INFO] 视觉配置加载完成
[2025-09-23 11:08:21.861] [INFO] VisionManager初始化完成
[2025-09-23 11:08:21.862] [INFO] 初始化数据Manager...
[2025-09-23 11:08:21.864] [INFO] 开始初始化StatisticsManager...
[2025-09-23 11:08:21.864] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-23 11:08:21.872] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-23 11:08:21.872] [INFO] 历史数据加载完成
[2025-09-23 11:08:21.872] [INFO] 自动保存任务已启动
[2025-09-23 11:08:21.873] [INFO] StatisticsManager初始化完成
[2025-09-23 11:08:21.873] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-23 11:08:21.873] [INFO] 所有Manager初始化完成
[2025-09-23 11:08:21.874] [INFO] 自动保存循环开始
[2025-09-23 11:08:21.907] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 11:08:21.907] [INFO] 主界面布局创建完成
[2025-09-23 11:08:21.908] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-23 15:48:42.788] [INFO] 程序启动开始
[2025-09-23 15:48:42.790] [INFO] 配置系统初始化成功
[2025-09-23 15:48:42.961] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-23 15:48:42.961] [INFO] 配置系统初始化完成
[2025-09-23 15:48:42.962] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-23 15:48:42.985] [INFO] 开始初始化各个Manager...
[2025-09-23 15:48:42.985] [INFO] 初始化基础Manager...
[2025-09-23 15:48:42.992] [INFO] IO状态缓存初始化完成
[2025-09-23 15:48:42.999] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 15:48:42.999] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 15:48:43.071] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 15:48:43.072] [ERROR] DMC1000B控制卡初始化失败
[2025-09-23 15:48:43.072] [WARN] DMC1000BIO管理器初始化失败
[2025-09-23 15:48:43.077] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-23 15:48:43.077] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 15:48:43.077] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 15:48:43.078] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 15:48:43.126] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__42_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 130
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 15:48:43.149] [WARN] DMC1000B电机管理器初始化失败
[2025-09-23 15:48:43.149] [INFO] 初始化系统模式管理器...
[2025-09-23 15:48:43.156] [INFO] 开始初始化MotorManager...
[2025-09-23 15:48:43.158] [INFO] 模拟初始化运动控制卡
[2025-09-23 15:48:43.375] [INFO] 加载了8个电机的默认配置
[2025-09-23 15:48:43.376] [INFO] 电机监控任务已启动
[2025-09-23 15:48:43.377] [INFO] MotorManager初始化完成
[2025-09-23 15:48:43.377] [INFO] 初始化通信Manager...
[2025-09-23 15:48:43.378] [INFO] 电机监控循环开始
[2025-09-23 15:48:43.461] [INFO] 开始初始化ScannerManager...
[2025-09-23 15:48:43.464] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-23 15:48:43.466] [INFO] 串口初始化完成
[2025-09-23 15:48:43.468] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-23 15:48:43.472] [INFO] 扫描枪连接成功: COM1
[2025-09-23 15:48:43.472] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-23 15:48:43.473] [INFO] ScannerManager初始化完成
[2025-09-23 15:48:43.475] [INFO] 开始初始化ModbusTcpManager...
[2025-09-23 15:48:43.478] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-23 15:48:43.480] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-23 15:48:48.532] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 15:48:48.533] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-23 15:48:48.533] [INFO] ModbusTcpManager初始化完成
[2025-09-23 15:48:48.537] [INFO] 开始初始化EpsonRobotManager...
[2025-09-23 15:48:48.538] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-23 15:48:48.539] [INFO] EpsonRobotManager初始化完成
[2025-09-23 15:48:48.539] [INFO] 初始化视觉Manager...
[2025-09-23 15:48:48.541] [INFO] 开始初始化VisionManager...
[2025-09-23 15:48:48.542] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-23 15:48:48.543] [INFO] 模拟初始化相机，索引: 0
[2025-09-23 15:48:49.049] [INFO] 相机初始化成功
[2025-09-23 15:48:49.050] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-23 15:48:49.050] [INFO] 视觉配置加载完成
[2025-09-23 15:48:49.050] [INFO] VisionManager初始化完成
[2025-09-23 15:48:49.051] [INFO] 初始化数据Manager...
[2025-09-23 15:48:49.054] [INFO] 开始初始化StatisticsManager...
[2025-09-23 15:48:49.054] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-23 15:48:49.061] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-23 15:48:49.062] [INFO] 历史数据加载完成
[2025-09-23 15:48:49.062] [INFO] 自动保存任务已启动
[2025-09-23 15:48:49.062] [INFO] StatisticsManager初始化完成
[2025-09-23 15:48:49.062] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-23 15:48:49.063] [INFO] 所有Manager初始化完成
[2025-09-23 15:48:49.064] [INFO] 自动保存循环开始
[2025-09-23 15:48:49.120] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 15:48:49.120] [INFO] 主界面布局创建完成
[2025-09-23 15:48:49.122] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-23 15:49:53.343] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 15:49:53.344] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 15:49:53.425] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 15:49:53.480] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 15:49:53.485] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 15:49:53.485] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 15:49:54.813] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 15:49:54.820] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 15:49:54.820] [INFO] Epson机器人管理器初始化完成
[2025-09-23 15:49:54.850] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-23 15:49:55.850] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 15:49:55.850] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 15:49:55.906] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 15:49:55.964] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 15:49:55.965] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 15:49:55.965] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 15:49:57.596] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 15:49:57.601] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 15:49:58.374] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-23 15:50:06.325] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 15:50:06.325] [INFO] Epson机器人管理器初始化完成
[2025-09-23 15:50:06.328] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-23 15:50:08.749] [INFO] 扫描枪1默认配置: COM1, 115200, 8, One, None
[2025-09-23 15:50:08.749] [INFO] 扫描枪2默认配置: COM2, 115200, 8, One, None
[2025-09-23 15:50:08.750] [INFO] 扫描枪3默认配置: COM3, 115200, 8, One, None
[2025-09-23 15:50:08.750] [INFO] 扫描器控制面板获取MultiScannerManager实例成功
[2025-09-23 15:50:08.767] [INFO] 扫描枪1模块界面初始化完成
[2025-09-23 15:50:08.770] [INFO] 扫描枪2模块界面初始化完成
[2025-09-23 15:50:08.773] [INFO] 扫描枪3模块界面初始化完成
[2025-09-23 15:50:08.773] [INFO] 扫描器控制面板初始化完成
[2025-09-23 15:50:18.141] [INFO] 扫描枪1串口初始化完成
[2025-09-23 15:50:18.142] [INFO] 扫描枪1配置更新: COM7, 115200
[2025-09-23 15:50:18.144] [INFO] 扫描枪1状态变更: Connecting - 正在连接扫描枪1...
[2025-09-23 15:50:47.759] [ERROR] 连接扫描枪1 执行失败
异常详情: 对端口“COM7”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerInstance.<ConnectAsync>b__22_1() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 490
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerInstance.<<ConnectAsync>b__22_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 484
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 15:50:54.504] [INFO] 扫描枪1串口初始化完成
[2025-09-23 15:50:54.505] [INFO] 扫描枪1配置更新: COM6, 115200
[2025-09-23 15:50:54.506] [INFO] 扫描枪1连接成功: COM6
[2025-09-23 15:50:54.508] [INFO] 扫描枪1状态变更: Connected - 扫描枪1连接成功
[2025-09-23 15:51:02.963] [INFO] 扫描枪1发送数据: 你好
[2025-09-23 15:51:40.401] [INFO] 扫描枪1发送数据: start
[2025-09-23 15:51:57.085] [INFO] 扫描枪1接收到数据: ok
[2025-09-23 15:51:59.865] [INFO] 扫描枪1接收到数据: ok
[2025-09-23 15:52:06.921] [INFO] 扫描枪1接收到数据: ok
[2025-09-23 15:52:35.154] [INFO] 扫描枪1连接已断开
[2025-09-23 15:52:35.156] [INFO] 扫描枪1状态变更: Disconnected - 扫描枪1连接已断开
[2025-09-23 15:52:36.049] [INFO] 扫描枪2串口初始化完成
[2025-09-23 15:52:36.049] [INFO] 扫描枪2配置更新: COM6, 115200
[2025-09-23 15:52:36.049] [INFO] 扫描枪2状态变更: Connecting - 正在连接扫描枪2...
[2025-09-23 15:52:36.050] [INFO] 扫描枪2连接成功: COM6
[2025-09-23 15:52:36.051] [INFO] 扫描枪2状态变更: Connected - 扫描枪2连接成功
[2025-09-23 15:52:42.914] [INFO] 扫描枪2发送数据: start
[2025-09-23 16:22:59.052] [INFO] 程序启动开始
[2025-09-23 16:22:59.054] [INFO] 配置系统初始化成功
[2025-09-23 16:22:59.108] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-23 16:22:59.109] [INFO] 配置系统初始化完成
[2025-09-23 16:22:59.109] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-23 16:22:59.132] [INFO] 开始初始化各个Manager...
[2025-09-23 16:22:59.132] [INFO] 初始化基础Manager...
[2025-09-23 16:22:59.137] [INFO] IO状态缓存初始化完成
[2025-09-23 16:22:59.142] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 16:22:59.142] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 16:22:59.144] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 16:22:59.144] [ERROR] DMC1000B控制卡初始化失败
[2025-09-23 16:22:59.144] [WARN] DMC1000BIO管理器初始化失败
[2025-09-23 16:22:59.148] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-23 16:22:59.148] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 16:22:59.149] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 16:22:59.149] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 16:22:59.184] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__42_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 130
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 16:22:59.197] [WARN] DMC1000B电机管理器初始化失败
[2025-09-23 16:22:59.197] [INFO] 初始化系统模式管理器...
[2025-09-23 16:22:59.200] [INFO] 开始初始化MotorManager...
[2025-09-23 16:22:59.202] [INFO] 模拟初始化运动控制卡
[2025-09-23 16:22:59.421] [INFO] 加载了8个电机的默认配置
[2025-09-23 16:22:59.422] [INFO] 电机监控任务已启动
[2025-09-23 16:22:59.423] [INFO] MotorManager初始化完成
[2025-09-23 16:22:59.423] [INFO] 初始化通信Manager...
[2025-09-23 16:22:59.424] [INFO] 电机监控循环开始
[2025-09-23 16:22:59.426] [INFO] 开始初始化ScannerManager...
[2025-09-23 16:22:59.428] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-23 16:22:59.464] [INFO] 串口初始化完成
[2025-09-23 16:22:59.467] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-23 16:22:59.469] [INFO] 扫描枪连接成功: COM1
[2025-09-23 16:22:59.470] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-23 16:22:59.471] [INFO] ScannerManager初始化完成
[2025-09-23 16:22:59.473] [INFO] 开始初始化ModbusTcpManager...
[2025-09-23 16:22:59.475] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-23 16:22:59.478] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-23 16:23:04.553] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 16:23:04.555] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-23 16:23:04.555] [INFO] ModbusTcpManager初始化完成
[2025-09-23 16:23:04.558] [INFO] 开始初始化EpsonRobotManager...
[2025-09-23 16:23:04.560] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-23 16:23:04.560] [INFO] EpsonRobotManager初始化完成
[2025-09-23 16:23:04.560] [INFO] 初始化视觉Manager...
[2025-09-23 16:23:04.563] [INFO] 开始初始化VisionManager...
[2025-09-23 16:23:04.563] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-23 16:23:04.564] [INFO] 模拟初始化相机，索引: 0
[2025-09-23 16:23:05.079] [INFO] 相机初始化成功
[2025-09-23 16:23:05.084] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-23 16:23:05.086] [INFO] 视觉配置加载完成
[2025-09-23 16:23:05.087] [INFO] VisionManager初始化完成
[2025-09-23 16:23:05.088] [INFO] 初始化数据Manager...
[2025-09-23 16:23:05.102] [INFO] 开始初始化StatisticsManager...
[2025-09-23 16:23:05.104] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-23 16:23:05.113] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-23 16:23:05.118] [INFO] 历史数据加载完成
[2025-09-23 16:23:05.119] [INFO] 自动保存任务已启动
[2025-09-23 16:23:05.119] [INFO] StatisticsManager初始化完成
[2025-09-23 16:23:05.120] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-23 16:23:05.121] [INFO] 所有Manager初始化完成
[2025-09-23 16:23:05.123] [INFO] 自动保存循环开始
[2025-09-23 16:23:05.162] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 16:23:05.162] [INFO] 主界面布局创建完成
[2025-09-23 16:23:05.163] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-23 16:23:07.246] [INFO] 对位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 16:23:07.716] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 16:23:08.649] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 16:23:08.649] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 16:23:08.725] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 16:23:08.782] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 16:23:08.786] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 16:23:08.786] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 16:23:09.023] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 16:23:09.027] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 16:23:09.720] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 16:23:09.721] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 16:23:09.777] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 16:23:09.834] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 16:23:09.835] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 16:23:09.835] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 16:23:10.134] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 16:23:10.140] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 16:23:10.141] [INFO] Epson机器人管理器初始化完成
[2025-09-23 16:23:10.150] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-23 16:23:11.366] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-23 16:23:11.823] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 16:23:11.824] [INFO] Epson机器人管理器初始化完成
[2025-09-23 16:23:11.826] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-23 16:23:14.837] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 16:23:14.837] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 16:23:14.894] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 16:23:14.948] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 16:23:14.949] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 16:23:14.949] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 16:45:50.366] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 17:09:04.115] [INFO] 程序启动开始
[2025-09-23 17:09:04.117] [INFO] 配置系统初始化成功
[2025-09-23 17:09:04.170] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-23 17:09:04.170] [INFO] 配置系统初始化完成
[2025-09-23 17:09:04.171] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-23 17:09:04.203] [INFO] 开始初始化各个Manager...
[2025-09-23 17:09:04.203] [INFO] 初始化基础Manager...
[2025-09-23 17:09:04.210] [INFO] IO状态缓存初始化完成
[2025-09-23 17:09:04.216] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 17:09:04.217] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 17:09:04.219] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 17:09:04.219] [ERROR] DMC1000B控制卡初始化失败
[2025-09-23 17:09:04.219] [WARN] DMC1000BIO管理器初始化失败
[2025-09-23 17:09:04.224] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-23 17:09:04.225] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 17:09:04.225] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 17:09:04.225] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 17:09:04.267] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__42_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 130
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 17:09:04.281] [WARN] DMC1000B电机管理器初始化失败
[2025-09-23 17:09:04.282] [INFO] 初始化系统模式管理器...
[2025-09-23 17:09:04.285] [INFO] 开始初始化MotorManager...
[2025-09-23 17:09:04.287] [INFO] 模拟初始化运动控制卡
[2025-09-23 17:09:04.504] [INFO] 加载了8个电机的默认配置
[2025-09-23 17:09:04.505] [INFO] 电机监控任务已启动
[2025-09-23 17:09:04.505] [INFO] MotorManager初始化完成
[2025-09-23 17:09:04.506] [INFO] 初始化通信Manager...
[2025-09-23 17:09:04.508] [INFO] 电机监控循环开始
[2025-09-23 17:09:04.511] [INFO] 开始初始化ScannerManager...
[2025-09-23 17:09:04.515] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-23 17:09:04.517] [INFO] 串口初始化完成
[2025-09-23 17:09:04.522] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-23 17:09:04.528] [INFO] 扫描枪连接成功: COM1
[2025-09-23 17:09:04.529] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-23 17:09:04.529] [INFO] ScannerManager初始化完成
[2025-09-23 17:09:04.531] [INFO] 开始初始化ModbusTcpManager...
[2025-09-23 17:09:04.533] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-23 17:09:04.536] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-23 17:09:09.589] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 17:09:09.590] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-23 17:09:09.590] [INFO] ModbusTcpManager初始化完成
[2025-09-23 17:09:09.594] [INFO] 开始初始化EpsonRobotManager...
[2025-09-23 17:09:09.595] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-23 17:09:09.595] [INFO] EpsonRobotManager初始化完成
[2025-09-23 17:09:09.595] [INFO] 初始化视觉Manager...
[2025-09-23 17:09:09.598] [INFO] 开始初始化VisionManager...
[2025-09-23 17:09:09.598] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-23 17:09:09.599] [INFO] 模拟初始化相机，索引: 0
[2025-09-23 17:09:10.119] [INFO] 相机初始化成功
[2025-09-23 17:09:10.124] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-23 17:09:10.125] [INFO] 视觉配置加载完成
[2025-09-23 17:09:10.127] [INFO] VisionManager初始化完成
[2025-09-23 17:09:10.128] [INFO] 初始化数据Manager...
[2025-09-23 17:09:10.140] [INFO] 开始初始化StatisticsManager...
[2025-09-23 17:09:10.143] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-23 17:09:10.151] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-23 17:09:10.152] [INFO] 历史数据加载完成
[2025-09-23 17:09:10.152] [INFO] 自动保存任务已启动
[2025-09-23 17:09:10.160] [INFO] StatisticsManager初始化完成
[2025-09-23 17:09:10.160] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-23 17:09:10.160] [INFO] 所有Manager初始化完成
[2025-09-23 17:09:10.163] [INFO] 自动保存循环开始
[2025-09-23 17:09:10.217] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 17:09:10.217] [INFO] 主界面布局创建完成
[2025-09-23 17:09:10.219] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-23 17:09:13.576] [INFO] 对位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 17:09:14.414] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 17:09:16.119] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 17:09:16.120] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 17:09:16.197] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 17:09:16.256] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 17:09:16.261] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 17:09:16.261] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 17:09:19.399] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 17:09:19.402] [INFO] 翻转电机示教面板业务逻辑初始化完成
[2025-09-23 17:09:19.412] [INFO] 翻转电机示教面板初始化完成 - 按HTML原型设计
[2025-09-23 17:09:25.126] [INFO] 翻转电机示教面板资源释放完成
[2025-09-23 17:09:25.145] [INFO] 皮带电机控制面板初始化完成 - 双电机设计
[2025-09-23 17:09:25.145] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 17:09:25.152] [INFO] 皮带电机轴3参数设置成功: 脉冲当量=0.01pulse/mm, 最大速度=100mm/s
[2025-09-23 17:09:25.153] [INFO] 皮带电机轴2参数设置成功: 脉冲当量=0.01pulse/mm, 最大速度=100mm/s
[2025-09-23 17:09:25.156] [INFO] 电机参数显示刷新完成
[2025-09-23 17:09:25.157] [INFO] 皮带电机参数初始化完成
[2025-09-23 17:09:25.157] [INFO] 皮带电机控制面板业务逻辑初始化完成
[2025-09-23 17:09:27.623] [INFO] 皮带电机控制面板资源释放完成
[2025-09-23 17:09:27.629] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 17:09:27.629] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 17:09:27.686] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 17:09:27.738] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 17:09:27.739] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 17:09:27.739] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 17:09:37.102] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 17:09:37.106] [INFO] 翻转电机示教面板业务逻辑初始化完成
[2025-09-23 17:09:37.107] [INFO] 翻转电机示教面板初始化完成 - 按HTML原型设计
[2025-09-23 17:09:38.543] [INFO] 翻转电机示教面板资源释放完成
[2025-09-23 17:09:38.551] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 17:09:38.552] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 17:09:38.606] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 17:09:38.661] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 17:09:38.661] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 17:09:38.661] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 17:09:39.246] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 17:09:39.250] [INFO] 翻转电机示教面板业务逻辑初始化完成
[2025-09-23 17:09:39.251] [INFO] 翻转电机示教面板初始化完成 - 按HTML原型设计
[2025-09-23 17:13:37.542] [INFO] 翻转电机示教面板资源释放完成
[2025-09-23 22:00:26.488] [INFO] 程序启动开始
[2025-09-23 22:00:26.491] [INFO] 配置系统初始化成功
[2025-09-23 22:00:26.554] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-23 22:00:26.555] [INFO] 配置系统初始化完成
[2025-09-23 22:00:26.555] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-23 22:00:26.583] [INFO] 开始初始化各个Manager...
[2025-09-23 22:00:26.584] [INFO] 初始化基础Manager...
[2025-09-23 22:00:26.590] [INFO] IO状态缓存初始化完成
[2025-09-23 22:00:26.598] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 22:00:26.598] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 22:00:26.600] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 22:00:26.600] [ERROR] DMC1000B控制卡初始化失败
[2025-09-23 22:00:26.600] [WARN] DMC1000BIO管理器初始化失败
[2025-09-23 22:00:26.605] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-23 22:00:26.605] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 22:00:26.606] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 22:00:26.606] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 22:00:26.646] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 22:00:26.660] [WARN] DMC1000B电机管理器初始化失败
[2025-09-23 22:00:26.660] [INFO] 初始化系统模式管理器...
[2025-09-23 22:00:26.664] [INFO] 开始初始化MotorManager...
[2025-09-23 22:00:26.666] [INFO] 模拟初始化运动控制卡
[2025-09-23 22:00:26.887] [INFO] 加载了8个电机的默认配置
[2025-09-23 22:00:26.890] [INFO] 电机监控任务已启动
[2025-09-23 22:00:26.891] [INFO] MotorManager初始化完成
[2025-09-23 22:00:26.891] [INFO] 初始化通信Manager...
[2025-09-23 22:00:26.894] [INFO] 电机监控循环开始
[2025-09-23 22:00:26.899] [INFO] 开始初始化ScannerManager...
[2025-09-23 22:00:26.907] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-23 22:00:26.950] [INFO] 串口初始化完成
[2025-09-23 22:00:26.951] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-23 22:00:26.955] [INFO] 扫描枪连接成功: COM1
[2025-09-23 22:00:26.955] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-23 22:00:26.955] [INFO] ScannerManager初始化完成
[2025-09-23 22:00:26.958] [INFO] 开始初始化ModbusTcpManager...
[2025-09-23 22:00:26.959] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-23 22:00:26.962] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-23 22:00:32.018] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 22:00:32.019] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-23 22:00:32.019] [INFO] ModbusTcpManager初始化完成
[2025-09-23 22:00:32.024] [INFO] 开始初始化EpsonRobotManager...
[2025-09-23 22:00:32.025] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-23 22:00:32.027] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-23 22:00:32.027] [INFO] EpsonRobotManager初始化完成
[2025-09-23 22:00:32.027] [INFO] 初始化视觉Manager...
[2025-09-23 22:00:32.031] [INFO] 开始初始化VisionManager...
[2025-09-23 22:00:32.031] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-23 22:00:32.032] [INFO] 模拟初始化相机，索引: 0
[2025-09-23 22:00:32.626] [INFO] 相机初始化成功
[2025-09-23 22:00:32.628] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-23 22:00:32.629] [INFO] 视觉配置加载完成
[2025-09-23 22:00:32.629] [INFO] VisionManager初始化完成
[2025-09-23 22:00:32.629] [INFO] 初始化数据Manager...
[2025-09-23 22:00:32.632] [INFO] 开始初始化StatisticsManager...
[2025-09-23 22:00:32.633] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-23 22:00:32.638] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-23 22:00:32.638] [INFO] 历史数据加载完成
[2025-09-23 22:00:32.638] [INFO] 自动保存任务已启动
[2025-09-23 22:00:32.638] [INFO] StatisticsManager初始化完成
[2025-09-23 22:00:32.639] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-23 22:00:32.639] [INFO] 所有Manager初始化完成
[2025-09-23 22:00:32.640] [INFO] 自动保存循环开始
[2025-09-23 22:00:32.698] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:00:32.698] [INFO] 主界面布局创建完成
[2025-09-23 22:00:32.699] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-23 22:00:35.801] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:00:35.802] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 22:00:35.934] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass48_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 199
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 22:00:35.992] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass48_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 199
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 22:00:35.997] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 22:00:35.997] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 22:00:37.555] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 22:00:37.562] [INFO] 翻转电机示教面板业务逻辑初始化完成
[2025-09-23 22:00:37.578] [INFO] 翻转电机示教面板初始化完成 - 按HTML原型设计
[2025-09-23 22:00:39.377] [INFO] 翻转电机示教面板资源释放完成
[2025-09-23 22:00:39.408] [INFO] 皮带电机控制面板初始化完成 - 双电机设计
[2025-09-23 22:00:39.408] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 22:00:39.416] [INFO] 皮带电机轴3参数设置成功: 脉冲当量=0.01mm/pulse, 最大速度=100mm/s
[2025-09-23 22:00:39.416] [INFO] 皮带电机轴2参数设置成功: 脉冲当量=0.01mm/pulse, 最大速度=100mm/s
[2025-09-23 22:00:39.418] [INFO] 电机参数显示刷新完成
[2025-09-23 22:00:39.418] [INFO] 皮带电机参数初始化完成
[2025-09-23 22:00:39.418] [INFO] 皮带电机控制面板业务逻辑初始化完成
[2025-09-23 22:00:39.438] [INFO] 系统配置保存成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-23 22:00:39.438] [INFO] 皮带电机配置保存成功
[2025-09-23 22:00:39.439] [INFO] 系统配置保存成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-23 22:00:39.439] [INFO] 皮带电机配置保存成功
[2025-09-23 22:00:42.929] [INFO] 皮带电机控制面板资源释放完成
[2025-09-23 22:00:42.932] [INFO] 翻转电机示教面板业务逻辑初始化完成
[2025-09-23 22:00:42.933] [INFO] 翻转电机示教面板初始化完成 - 按HTML原型设计
[2025-09-23 22:00:44.448] [ERROR] 翻转电机轴0回零 执行失败
异常详情: 翻转电机轴0参数未设置
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass50_0.<<FlipMotorHomeAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 253
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 22:00:48.841] [INFO] 翻转电机示教面板资源释放完成
[2025-09-23 22:00:48.847] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:00:48.847] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 22:00:48.904] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass48_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 199
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 22:00:48.958] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass48_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 199
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 22:00:48.960] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 22:00:48.960] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 22:04:32.732] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 22:17:52.806] [INFO] 程序启动开始
[2025-09-23 22:17:52.808] [INFO] 配置系统初始化成功
[2025-09-23 22:17:52.867] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-23 22:17:52.867] [INFO] 配置系统初始化完成
[2025-09-23 22:17:52.868] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-23 22:17:52.894] [INFO] 开始初始化各个Manager...
[2025-09-23 22:17:52.895] [INFO] 初始化基础Manager...
[2025-09-23 22:17:52.900] [INFO] IO状态缓存初始化完成
[2025-09-23 22:17:52.907] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 22:17:52.907] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 22:17:52.909] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 22:17:52.909] [ERROR] DMC1000B控制卡初始化失败
[2025-09-23 22:17:52.909] [WARN] DMC1000BIO管理器初始化失败
[2025-09-23 22:17:52.913] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-23 22:17:52.913] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 22:17:52.913] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 22:17:52.914] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 22:17:52.952] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 22:17:52.968] [WARN] DMC1000B电机管理器初始化失败
[2025-09-23 22:17:52.968] [INFO] 初始化系统模式管理器...
[2025-09-23 22:17:52.972] [INFO] 开始初始化MotorManager...
[2025-09-23 22:17:52.974] [INFO] 模拟初始化运动控制卡
[2025-09-23 22:17:53.185] [INFO] 加载了8个电机的默认配置
[2025-09-23 22:17:53.186] [INFO] 电机监控任务已启动
[2025-09-23 22:17:53.187] [INFO] MotorManager初始化完成
[2025-09-23 22:17:53.187] [INFO] 初始化通信Manager...
[2025-09-23 22:17:53.189] [INFO] 电机监控循环开始
[2025-09-23 22:17:53.190] [INFO] 开始初始化ScannerManager...
[2025-09-23 22:17:53.193] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-23 22:17:53.196] [INFO] 串口初始化完成
[2025-09-23 22:17:53.199] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-23 22:17:53.203] [INFO] 扫描枪连接成功: COM1
[2025-09-23 22:17:53.203] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-23 22:17:53.204] [INFO] ScannerManager初始化完成
[2025-09-23 22:17:53.207] [INFO] 开始初始化ModbusTcpManager...
[2025-09-23 22:17:53.209] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-23 22:17:53.212] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-23 22:17:58.267] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 22:17:58.268] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-23 22:17:58.268] [INFO] ModbusTcpManager初始化完成
[2025-09-23 22:17:58.270] [INFO] 开始初始化EpsonRobotManager...
[2025-09-23 22:17:58.271] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-23 22:17:58.272] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-23 22:17:58.272] [INFO] EpsonRobotManager初始化完成
[2025-09-23 22:17:58.273] [INFO] 初始化视觉Manager...
[2025-09-23 22:17:58.275] [INFO] 开始初始化VisionManager...
[2025-09-23 22:17:58.275] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-23 22:17:58.276] [INFO] 模拟初始化相机，索引: 0
[2025-09-23 22:17:58.792] [INFO] 相机初始化成功
[2025-09-23 22:17:58.797] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-23 22:17:58.797] [INFO] 视觉配置加载完成
[2025-09-23 22:17:58.799] [INFO] VisionManager初始化完成
[2025-09-23 22:17:58.801] [INFO] 初始化数据Manager...
[2025-09-23 22:17:58.815] [INFO] 开始初始化StatisticsManager...
[2025-09-23 22:17:58.820] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-23 22:17:58.838] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-23 22:17:58.840] [INFO] 历史数据加载完成
[2025-09-23 22:17:58.841] [INFO] 自动保存任务已启动
[2025-09-23 22:17:58.842] [INFO] StatisticsManager初始化完成
[2025-09-23 22:17:58.842] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-23 22:17:58.843] [INFO] 所有Manager初始化完成
[2025-09-23 22:17:58.846] [INFO] 自动保存循环开始
[2025-09-23 22:17:58.894] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:17:58.895] [INFO] 主界面布局创建完成
[2025-09-23 22:17:58.896] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-23 22:18:00.677] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:18:00.678] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 22:18:00.755] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass48_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 199
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 22:18:00.812] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass48_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 199
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 22:18:00.817] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 22:18:00.817] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 22:18:02.739] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 22:18:02.743] [INFO] 翻转电机示教面板业务逻辑初始化完成
[2025-09-23 22:18:02.750] [INFO] 翻转电机示教面板初始化完成 - 按HTML原型设计
[2025-09-23 22:18:04.074] [INFO] 翻转电机示教面板资源释放完成
[2025-09-23 22:18:04.083] [INFO] 皮带电机控制面板初始化完成 - 双电机设计
[2025-09-23 22:18:04.084] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 22:18:04.090] [INFO] 皮带电机轴3参数设置成功: 脉冲当量=0.01mm/pulse, 最大速度=100mm/s
[2025-09-23 22:18:04.091] [INFO] 皮带电机轴2参数设置成功: 脉冲当量=0.01mm/pulse, 最大速度=100mm/s
[2025-09-23 22:18:04.095] [INFO] 电机参数显示刷新完成
[2025-09-23 22:18:04.096] [INFO] 皮带电机参数初始化完成
[2025-09-23 22:18:04.096] [INFO] 皮带电机控制面板业务逻辑初始化完成
[2025-09-23 22:18:04.109] [INFO] 系统配置保存成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-23 22:18:04.109] [INFO] 皮带电机配置保存成功
[2025-09-23 22:18:04.110] [INFO] 系统配置保存成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-23 22:18:04.110] [INFO] 皮带电机配置保存成功
[2025-09-23 22:18:05.749] [INFO] 皮带电机控制面板资源释放完成
[2025-09-23 22:18:05.756] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 22:18:05.756] [INFO] Epson机器人管理器初始化完成
[2025-09-23 22:18:05.783] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:18:09.030] [INFO] 扫描枪1默认配置: COM1, 115200, 8, One, None
[2025-09-23 22:18:09.031] [INFO] 扫描枪2默认配置: COM2, 115200, 8, One, None
[2025-09-23 22:18:09.031] [INFO] 扫描枪3默认配置: COM3, 115200, 8, One, None
[2025-09-23 22:18:09.031] [INFO] 扫描器控制面板获取MultiScannerManager实例成功
[2025-09-23 22:18:09.050] [INFO] 扫描枪1模块界面初始化完成
[2025-09-23 22:18:09.053] [INFO] 扫描枪2模块界面初始化完成
[2025-09-23 22:18:09.055] [INFO] 扫描枪3模块界面初始化完成
[2025-09-23 22:18:09.056] [INFO] 扫描器控制面板初始化完成
[2025-09-23 22:18:15.249] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-23 22:18:19.485] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
[2025-09-23 22:18:22.835] [INFO] 生产日志管理面板初始化完成 - 按HTML原型设计
[2025-09-23 22:18:24.933] [ERROR] IO管理器初始化超时，无法启动IO监控
[2025-09-23 22:18:25.021] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
[2025-09-23 22:18:25.833] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-23 22:18:30.496] [ERROR] IO管理器初始化超时，无法启动IO监控
[2025-09-23 22:18:31.262] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 22:18:31.262] [INFO] Epson机器人管理器初始化完成
[2025-09-23 22:18:31.265] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:18:32.917] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:18:32.918] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 22:18:32.975] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass48_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 199
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 22:18:33.033] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass48_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 199
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 22:18:33.033] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 22:18:33.033] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 22:18:33.605] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 22:18:33.608] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 22:18:33.608] [INFO] Epson机器人管理器初始化完成
[2025-09-23 22:18:33.611] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:19:34.275] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 22:19:34.279] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-23 22:19:34.284] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-23 22:19:39.372] [ERROR] 连接启动/停止TCP/IP 执行失败
异常详情: 控制端口连接超时: *************:5000
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<<ConnectStartStopAsync>b__71_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 242
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 22:20:20.730] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 22:20:20.731] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-23 22:20:20.731] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-23 22:20:25.801] [ERROR] 连接启动/停止TCP/IP 执行失败
异常详情: 控制端口连接超时: *************:5000
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<<ConnectStartStopAsync>b__71_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 242
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 22:27:10.440] [INFO] 程序启动开始
[2025-09-23 22:27:10.442] [INFO] 配置系统初始化成功
[2025-09-23 22:27:10.512] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-23 22:27:10.512] [INFO] 配置系统初始化完成
[2025-09-23 22:27:10.512] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-23 22:27:10.536] [INFO] 开始初始化各个Manager...
[2025-09-23 22:27:10.537] [INFO] 初始化基础Manager...
[2025-09-23 22:27:10.541] [INFO] IO状态缓存初始化完成
[2025-09-23 22:27:10.547] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 22:27:10.547] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 22:27:10.548] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 22:27:10.548] [ERROR] DMC1000B控制卡初始化失败
[2025-09-23 22:27:10.549] [WARN] DMC1000BIO管理器初始化失败
[2025-09-23 22:27:10.552] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-23 22:27:10.553] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 22:27:10.553] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 22:27:10.553] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 22:27:10.601] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 22:27:10.614] [WARN] DMC1000B电机管理器初始化失败
[2025-09-23 22:27:10.614] [INFO] 初始化系统模式管理器...
[2025-09-23 22:27:10.618] [INFO] 开始初始化MotorManager...
[2025-09-23 22:27:10.620] [INFO] 模拟初始化运动控制卡
[2025-09-23 22:27:10.838] [INFO] 加载了8个电机的默认配置
[2025-09-23 22:27:10.839] [INFO] 电机监控任务已启动
[2025-09-23 22:27:10.839] [INFO] MotorManager初始化完成
[2025-09-23 22:27:10.839] [INFO] 初始化通信Manager...
[2025-09-23 22:27:10.842] [INFO] 开始初始化ScannerManager...
[2025-09-23 22:27:10.843] [INFO] 电机监控循环开始
[2025-09-23 22:27:10.844] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-23 22:27:10.847] [INFO] 串口初始化完成
[2025-09-23 22:27:10.924] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-23 22:27:10.955] [INFO] 扫描枪连接成功: COM1
[2025-09-23 22:27:10.956] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-23 22:27:10.959] [INFO] ScannerManager初始化完成
[2025-09-23 22:27:10.964] [INFO] 开始初始化ModbusTcpManager...
[2025-09-23 22:27:10.966] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-23 22:27:10.970] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-23 22:27:16.052] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 22:27:16.053] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-23 22:27:16.053] [INFO] ModbusTcpManager初始化完成
[2025-09-23 22:27:16.057] [INFO] 开始初始化EpsonRobotManager...
[2025-09-23 22:27:16.058] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-23 22:27:16.059] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-23 22:27:16.059] [INFO] EpsonRobotManager初始化完成
[2025-09-23 22:27:16.059] [INFO] 初始化视觉Manager...
[2025-09-23 22:27:16.062] [INFO] 开始初始化VisionManager...
[2025-09-23 22:27:16.062] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-23 22:27:16.064] [INFO] 模拟初始化相机，索引: 0
[2025-09-23 22:27:16.577] [INFO] 相机初始化成功
[2025-09-23 22:27:16.582] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-23 22:27:16.582] [INFO] 视觉配置加载完成
[2025-09-23 22:27:16.583] [INFO] VisionManager初始化完成
[2025-09-23 22:27:16.584] [INFO] 初始化数据Manager...
[2025-09-23 22:27:16.595] [INFO] 开始初始化StatisticsManager...
[2025-09-23 22:27:16.597] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-23 22:27:16.605] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-23 22:27:16.606] [INFO] 历史数据加载完成
[2025-09-23 22:27:16.607] [INFO] 自动保存任务已启动
[2025-09-23 22:27:16.608] [INFO] StatisticsManager初始化完成
[2025-09-23 22:27:16.608] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-23 22:27:16.609] [INFO] 所有Manager初始化完成
[2025-09-23 22:27:16.613] [INFO] 自动保存循环开始
[2025-09-23 22:27:16.670] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:27:16.670] [INFO] 主界面布局创建完成
[2025-09-23 22:27:16.671] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-23 22:27:20.928] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 22:27:20.929] [INFO] Epson机器人管理器初始化完成
[2025-09-23 22:27:20.943] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:45:22.188] [INFO] 程序启动开始
[2025-09-23 22:45:22.190] [INFO] 配置系统初始化成功
[2025-09-23 22:45:22.252] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-23 22:45:22.252] [INFO] 配置系统初始化完成
[2025-09-23 22:45:22.253] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-23 22:45:22.278] [INFO] 开始初始化各个Manager...
[2025-09-23 22:45:22.279] [INFO] 初始化基础Manager...
[2025-09-23 22:45:22.285] [INFO] IO状态缓存初始化完成
[2025-09-23 22:45:22.291] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 22:45:22.291] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 22:45:22.293] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 22:45:22.293] [ERROR] DMC1000B控制卡初始化失败
[2025-09-23 22:45:22.293] [WARN] DMC1000BIO管理器初始化失败
[2025-09-23 22:45:22.297] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-23 22:45:22.297] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 22:45:22.297] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 22:45:22.298] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 22:45:22.434] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 22:45:22.449] [WARN] DMC1000B电机管理器初始化失败
[2025-09-23 22:45:22.449] [INFO] 初始化系统模式管理器...
[2025-09-23 22:45:22.454] [INFO] 开始初始化MotorManager...
[2025-09-23 22:45:22.456] [INFO] 模拟初始化运动控制卡
[2025-09-23 22:45:22.669] [INFO] 加载了8个电机的默认配置
[2025-09-23 22:45:22.671] [INFO] 电机监控任务已启动
[2025-09-23 22:45:22.671] [INFO] MotorManager初始化完成
[2025-09-23 22:45:22.672] [INFO] 初始化通信Manager...
[2025-09-23 22:45:22.673] [INFO] 电机监控循环开始
[2025-09-23 22:45:22.675] [INFO] 开始初始化ScannerManager...
[2025-09-23 22:45:22.715] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-23 22:45:22.743] [INFO] 串口初始化完成
[2025-09-23 22:45:22.745] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-23 22:45:22.749] [INFO] 扫描枪连接成功: COM1
[2025-09-23 22:45:22.749] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-23 22:45:22.750] [INFO] ScannerManager初始化完成
[2025-09-23 22:45:22.752] [INFO] 开始初始化ModbusTcpManager...
[2025-09-23 22:45:22.754] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-23 22:45:22.757] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-23 22:45:27.833] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 22:45:27.834] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-23 22:45:27.835] [INFO] ModbusTcpManager初始化完成
[2025-09-23 22:45:27.837] [INFO] 开始初始化EpsonRobotManager...
[2025-09-23 22:45:27.838] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-23 22:45:27.839] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-23 22:45:27.840] [INFO] EpsonRobotManager初始化完成
[2025-09-23 22:45:27.840] [INFO] 初始化视觉Manager...
[2025-09-23 22:45:27.842] [INFO] 开始初始化VisionManager...
[2025-09-23 22:45:27.842] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-23 22:45:27.843] [INFO] 模拟初始化相机，索引: 0
[2025-09-23 22:45:28.364] [INFO] 相机初始化成功
[2025-09-23 22:45:28.365] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-23 22:45:28.366] [INFO] 视觉配置加载完成
[2025-09-23 22:45:28.366] [INFO] VisionManager初始化完成
[2025-09-23 22:45:28.366] [INFO] 初始化数据Manager...
[2025-09-23 22:45:28.369] [INFO] 开始初始化StatisticsManager...
[2025-09-23 22:45:28.370] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-23 22:45:28.374] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-23 22:45:28.374] [INFO] 历史数据加载完成
[2025-09-23 22:45:28.374] [INFO] 自动保存任务已启动
[2025-09-23 22:45:28.374] [INFO] StatisticsManager初始化完成
[2025-09-23 22:45:28.375] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-23 22:45:28.375] [INFO] 所有Manager初始化完成
[2025-09-23 22:45:28.376] [INFO] 自动保存循环开始
[2025-09-23 22:45:28.416] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:45:28.417] [INFO] 主界面布局创建完成
[2025-09-23 22:45:28.418] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-23 22:45:30.071] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:45:30.072] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 22:45:30.077] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 22:45:30.077] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 22:45:32.640] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 22:45:32.645] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 22:45:32.645] [INFO] Epson机器人管理器初始化完成
[2025-09-23 22:45:32.659] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:46:01.406] [INFO] 开始连接控制端口 - IP: ************, 端口: 6000
[2025-09-23 22:46:01.407] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 22:46:01.410] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-23 22:46:01.412] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-23 22:46:06.544] [ERROR] 连接启动/停止TCP/IP 执行失败
异常详情: 控制端口连接超时: *************:5000
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<<ConnectStartStopAsync>b__71_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 242
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 22:51:15.345] [INFO] 程序启动开始
[2025-09-23 22:51:15.347] [INFO] 配置系统初始化成功
[2025-09-23 22:51:15.409] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-23 22:51:15.409] [INFO] 配置系统初始化完成
[2025-09-23 22:51:15.410] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-23 22:51:15.430] [INFO] 开始初始化各个Manager...
[2025-09-23 22:51:15.431] [INFO] 初始化基础Manager...
[2025-09-23 22:51:15.435] [INFO] IO状态缓存初始化完成
[2025-09-23 22:51:15.441] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 22:51:15.441] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 22:51:15.443] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 22:51:15.443] [ERROR] DMC1000B控制卡初始化失败
[2025-09-23 22:51:15.443] [WARN] DMC1000BIO管理器初始化失败
[2025-09-23 22:51:15.447] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-23 22:51:15.447] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 22:51:15.448] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 22:51:15.448] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 22:51:15.498] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 22:51:15.514] [WARN] DMC1000B电机管理器初始化失败
[2025-09-23 22:51:15.515] [INFO] 初始化系统模式管理器...
[2025-09-23 22:51:15.519] [INFO] 开始初始化MotorManager...
[2025-09-23 22:51:15.522] [INFO] 模拟初始化运动控制卡
[2025-09-23 22:51:15.744] [INFO] 加载了8个电机的默认配置
[2025-09-23 22:51:15.746] [INFO] 电机监控任务已启动
[2025-09-23 22:51:15.747] [INFO] MotorManager初始化完成
[2025-09-23 22:51:15.763] [INFO] 电机监控循环开始
[2025-09-23 22:51:15.765] [INFO] 初始化通信Manager...
[2025-09-23 22:51:15.771] [INFO] 开始初始化ScannerManager...
[2025-09-23 22:51:15.776] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-23 22:51:15.778] [INFO] 串口初始化完成
[2025-09-23 22:51:15.781] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-23 22:51:15.806] [INFO] 扫描枪连接成功: COM1
[2025-09-23 22:51:15.807] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-23 22:51:15.808] [INFO] ScannerManager初始化完成
[2025-09-23 22:51:15.812] [INFO] 开始初始化ModbusTcpManager...
[2025-09-23 22:51:15.814] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-23 22:51:15.821] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-23 22:51:20.892] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 22:51:20.893] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-23 22:51:20.893] [INFO] ModbusTcpManager初始化完成
[2025-09-23 22:51:20.896] [INFO] 开始初始化EpsonRobotManager...
[2025-09-23 22:51:20.896] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-23 22:51:20.898] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-23 22:51:20.898] [INFO] EpsonRobotManager初始化完成
[2025-09-23 22:51:20.898] [INFO] 初始化视觉Manager...
[2025-09-23 22:51:20.900] [INFO] 开始初始化VisionManager...
[2025-09-23 22:51:20.901] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-23 22:51:20.902] [INFO] 模拟初始化相机，索引: 0
[2025-09-23 22:51:21.416] [INFO] 相机初始化成功
[2025-09-23 22:51:21.417] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-23 22:51:21.417] [INFO] 视觉配置加载完成
[2025-09-23 22:51:21.417] [INFO] VisionManager初始化完成
[2025-09-23 22:51:21.418] [INFO] 初始化数据Manager...
[2025-09-23 22:51:21.420] [INFO] 开始初始化StatisticsManager...
[2025-09-23 22:51:21.421] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-23 22:51:21.424] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-23 22:51:21.424] [INFO] 历史数据加载完成
[2025-09-23 22:51:21.425] [INFO] 自动保存任务已启动
[2025-09-23 22:51:21.426] [INFO] StatisticsManager初始化完成
[2025-09-23 22:51:21.427] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-23 22:51:21.428] [INFO] 所有Manager初始化完成
[2025-09-23 22:51:21.430] [INFO] 自动保存循环开始
[2025-09-23 22:51:21.489] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:51:21.489] [INFO] 主界面布局创建完成
[2025-09-23 22:51:21.490] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-23 22:51:26.369] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:51:26.370] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 22:51:26.376] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 22:51:26.376] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 22:51:27.530] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 22:51:27.538] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 22:51:27.539] [INFO] Epson机器人管理器初始化完成
[2025-09-23 22:51:27.556] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:51:30.840] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-23 22:51:31.208] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 22:51:31.209] [INFO] Epson机器人管理器初始化完成
[2025-09-23 22:51:31.214] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:51:36.941] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:51:36.942] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 22:51:36.942] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 22:51:36.942] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 22:51:37.423] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 22:51:37.427] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:51:38.354] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:51:38.355] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 22:51:38.355] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 22:51:38.355] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 22:51:40.664] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 22:51:40.668] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 22:51:40.668] [INFO] Epson机器人管理器初始化完成
[2025-09-23 22:51:40.671] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:51:41.356] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:51:41.356] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 22:51:41.356] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 22:51:41.357] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 22:51:58.344] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 22:51:58.347] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 22:51:58.347] [INFO] Epson机器人管理器初始化完成
[2025-09-23 22:51:58.350] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:51:58.964] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:51:58.965] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 22:51:58.965] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 22:51:58.965] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 22:51:59.511] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 22:51:59.515] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:52:00.323] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:52:00.323] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 22:52:00.324] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 22:52:00.324] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 22:52:01.367] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 22:52:01.371] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 22:52:01.371] [INFO] Epson机器人管理器初始化完成
[2025-09-23 22:52:01.376] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:52:01.884] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 22:52:01.885] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 22:52:01.885] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 22:52:01.885] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 22:53:00.181] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 22:53:00.184] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 22:53:00.185] [INFO] Epson机器人管理器初始化完成
[2025-09-23 22:53:00.188] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-23 23:43:20.944] [INFO] 程序启动开始
[2025-09-23 23:43:20.946] [INFO] 配置系统初始化成功
[2025-09-23 23:43:21.000] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-23 23:43:21.001] [INFO] 配置系统初始化完成
[2025-09-23 23:43:21.001] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-23 23:43:21.031] [INFO] 开始初始化各个Manager...
[2025-09-23 23:43:21.031] [INFO] 初始化基础Manager...
[2025-09-23 23:43:21.038] [INFO] IO状态缓存初始化完成
[2025-09-23 23:43:21.044] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 23:43:21.045] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 23:43:21.046] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 23:43:21.046] [ERROR] DMC1000B控制卡初始化失败
[2025-09-23 23:43:21.046] [WARN] DMC1000BIO管理器初始化失败
[2025-09-23 23:43:21.051] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-23 23:43:21.051] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 23:43:21.051] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 23:43:21.052] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 23:43:21.090] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 23:43:21.110] [WARN] DMC1000B电机管理器初始化失败
[2025-09-23 23:43:21.110] [INFO] 初始化系统模式管理器...
[2025-09-23 23:43:21.114] [INFO] 开始初始化MotorManager...
[2025-09-23 23:43:21.142] [INFO] 模拟初始化运动控制卡
[2025-09-23 23:43:21.363] [INFO] 加载了8个电机的默认配置
[2025-09-23 23:43:21.366] [INFO] 电机监控任务已启动
[2025-09-23 23:43:21.366] [INFO] MotorManager初始化完成
[2025-09-23 23:43:21.366] [INFO] 初始化通信Manager...
[2025-09-23 23:43:21.371] [INFO] 开始初始化ScannerManager...
[2025-09-23 23:43:21.372] [INFO] 电机监控循环开始
[2025-09-23 23:43:21.379] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-23 23:43:21.382] [INFO] 串口初始化完成
[2025-09-23 23:43:21.387] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-23 23:43:21.422] [INFO] 扫描枪连接成功: COM1
[2025-09-23 23:43:21.422] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-23 23:43:21.422] [INFO] ScannerManager初始化完成
[2025-09-23 23:43:21.425] [INFO] 开始初始化ModbusTcpManager...
[2025-09-23 23:43:21.427] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-23 23:43:21.430] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-23 23:43:26.505] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 23:43:26.506] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-23 23:43:26.506] [INFO] ModbusTcpManager初始化完成
[2025-09-23 23:43:26.509] [INFO] 开始初始化EpsonRobotManager...
[2025-09-23 23:43:26.510] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-23 23:43:26.511] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-23 23:43:26.511] [INFO] EpsonRobotManager初始化完成
[2025-09-23 23:43:26.511] [INFO] 初始化视觉Manager...
[2025-09-23 23:43:26.514] [INFO] 开始初始化VisionManager...
[2025-09-23 23:43:26.514] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-23 23:43:26.515] [INFO] 模拟初始化相机，索引: 0
[2025-09-23 23:43:27.033] [INFO] 相机初始化成功
[2025-09-23 23:43:27.038] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-23 23:43:27.039] [INFO] 视觉配置加载完成
[2025-09-23 23:43:27.041] [INFO] VisionManager初始化完成
[2025-09-23 23:43:27.042] [INFO] 初始化数据Manager...
[2025-09-23 23:43:27.057] [INFO] 开始初始化StatisticsManager...
[2025-09-23 23:43:27.060] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-23 23:43:27.071] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-23 23:43:27.071] [INFO] 历史数据加载完成
[2025-09-23 23:43:27.072] [INFO] 自动保存任务已启动
[2025-09-23 23:43:27.072] [INFO] StatisticsManager初始化完成
[2025-09-23 23:43:27.073] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-23 23:43:27.074] [INFO] 所有Manager初始化完成
[2025-09-23 23:43:27.076] [INFO] 自动保存循环开始
[2025-09-23 23:43:27.133] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 23:43:27.133] [INFO] 主界面布局创建完成
[2025-09-23 23:43:27.134] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-23 23:43:29.154] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 23:43:29.155] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 23:43:29.160] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 23:43:29.160] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 23:43:29.571] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 23:43:29.577] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 23:43:29.577] [INFO] Epson机器人管理器初始化完成
[2025-09-23 23:43:29.609] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-23 23:43:42.473] [INFO] 开始连接控制端口 - IP: ************, 端口: 6000
[2025-09-23 23:43:42.474] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 23:43:42.477] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-23 23:43:42.482] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-23 23:43:47.597] [ERROR] 连接启动/停止TCP/IP 执行失败
异常详情: 控制端口连接超时: *************:5000
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<<ConnectStartStopAsync>b__71_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 242
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 23:44:11.578] [INFO] 开始连接数据端口 - IP: ************, 端口: 5000
[2025-09-23 23:44:11.578] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 23:44:11.580] [INFO] Epson机器人Data连接状态变更: Connecting - 正在连接数据收发TCP/IP...
[2025-09-23 23:44:11.582] [INFO] 连接状态变化: Data连接中... - 正在连接数据收发TCP/IP...
[2025-09-23 23:44:16.676] [ERROR] 连接数据收发TCP/IP 执行失败
异常详情: 数据端口连接超时: *************:5001
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<<ConnectDataAsync>b__72_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 298
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 23:44:30.786] [INFO] 开始连接Epson机器人 - IP: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-23 23:44:30.787] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 23:44:30.787] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-23 23:44:30.788] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-23 23:44:35.881] [ERROR] 连接启动/停止TCP/IP 执行失败
异常详情: 控制端口连接超时: *************:5000
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<<ConnectStartStopAsync>b__71_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 242
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 23:49:47.874] [INFO] 程序启动开始
[2025-09-23 23:49:47.876] [INFO] 配置系统初始化成功
[2025-09-23 23:49:47.934] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-23 23:49:47.935] [INFO] 配置系统初始化完成
[2025-09-23 23:49:47.935] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-23 23:49:47.958] [INFO] 开始初始化各个Manager...
[2025-09-23 23:49:47.959] [INFO] 初始化基础Manager...
[2025-09-23 23:49:47.964] [INFO] IO状态缓存初始化完成
[2025-09-23 23:49:47.969] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 23:49:47.969] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 23:49:47.971] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 23:49:47.971] [ERROR] DMC1000B控制卡初始化失败
[2025-09-23 23:49:47.971] [WARN] DMC1000BIO管理器初始化失败
[2025-09-23 23:49:47.975] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-23 23:49:47.976] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 23:49:47.976] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 23:49:47.976] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 23:49:48.013] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 23:49:48.027] [WARN] DMC1000B电机管理器初始化失败
[2025-09-23 23:49:48.027] [INFO] 初始化系统模式管理器...
[2025-09-23 23:49:48.032] [INFO] 开始初始化MotorManager...
[2025-09-23 23:49:48.035] [INFO] 模拟初始化运动控制卡
[2025-09-23 23:49:48.243] [INFO] 加载了8个电机的默认配置
[2025-09-23 23:49:48.245] [INFO] 电机监控任务已启动
[2025-09-23 23:49:48.246] [INFO] MotorManager初始化完成
[2025-09-23 23:49:48.248] [INFO] 初始化通信Manager...
[2025-09-23 23:49:48.255] [INFO] 电机监控循环开始
[2025-09-23 23:49:48.297] [INFO] 开始初始化ScannerManager...
[2025-09-23 23:49:48.302] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-23 23:49:48.305] [INFO] 串口初始化完成
[2025-09-23 23:49:48.307] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-23 23:49:48.313] [INFO] 扫描枪连接成功: COM1
[2025-09-23 23:49:48.313] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-23 23:49:48.314] [INFO] ScannerManager初始化完成
[2025-09-23 23:49:48.318] [INFO] 开始初始化ModbusTcpManager...
[2025-09-23 23:49:48.320] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-23 23:49:48.324] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-23 23:49:53.443] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 23:49:53.443] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-23 23:49:53.444] [INFO] ModbusTcpManager初始化完成
[2025-09-23 23:49:53.446] [INFO] 开始初始化EpsonRobotManager...
[2025-09-23 23:49:53.447] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-23 23:49:53.448] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-23 23:49:53.448] [INFO] EpsonRobotManager初始化完成
[2025-09-23 23:49:53.449] [INFO] 初始化视觉Manager...
[2025-09-23 23:49:53.451] [INFO] 开始初始化VisionManager...
[2025-09-23 23:49:53.451] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-23 23:49:53.453] [INFO] 模拟初始化相机，索引: 0
[2025-09-23 23:49:53.963] [INFO] 相机初始化成功
[2025-09-23 23:49:53.965] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-23 23:49:53.965] [INFO] 视觉配置加载完成
[2025-09-23 23:49:53.965] [INFO] VisionManager初始化完成
[2025-09-23 23:49:53.966] [INFO] 初始化数据Manager...
[2025-09-23 23:49:53.969] [INFO] 开始初始化StatisticsManager...
[2025-09-23 23:49:53.970] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-23 23:49:53.976] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-23 23:49:53.976] [INFO] 历史数据加载完成
[2025-09-23 23:49:53.977] [INFO] 自动保存任务已启动
[2025-09-23 23:49:53.977] [INFO] StatisticsManager初始化完成
[2025-09-23 23:49:53.978] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-23 23:49:53.978] [INFO] 所有Manager初始化完成
[2025-09-23 23:49:53.980] [INFO] 自动保存循环开始
[2025-09-23 23:49:54.019] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 23:49:54.020] [INFO] 主界面布局创建完成
[2025-09-23 23:49:54.021] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-23 23:49:55.377] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 23:49:55.378] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 23:49:55.383] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 23:49:55.383] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 23:49:57.595] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 23:49:57.601] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 23:49:57.601] [INFO] Epson机器人管理器初始化完成
[2025-09-23 23:49:57.614] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-23 23:49:58.411] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 23:49:58.411] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 23:49:58.412] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 23:49:58.413] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-23 23:50:22.879] [INFO] 翻转电机控制面板资源释放完成
[2025-09-23 23:57:55.139] [INFO] 程序启动开始
[2025-09-23 23:57:55.141] [INFO] 配置系统初始化成功
[2025-09-23 23:57:55.203] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-23 23:57:55.203] [INFO] 配置系统初始化完成
[2025-09-23 23:57:55.203] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-23 23:57:55.229] [INFO] 开始初始化各个Manager...
[2025-09-23 23:57:55.229] [INFO] 初始化基础Manager...
[2025-09-23 23:57:55.235] [INFO] IO状态缓存初始化完成
[2025-09-23 23:57:55.241] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 23:57:55.242] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 23:57:55.244] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 23:57:55.244] [ERROR] DMC1000B控制卡初始化失败
[2025-09-23 23:57:55.244] [WARN] DMC1000BIO管理器初始化失败
[2025-09-23 23:57:55.250] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-23 23:57:55.250] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-23 23:57:55.250] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-23 23:57:55.252] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-23 23:57:55.292] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 23:57:55.306] [WARN] DMC1000B电机管理器初始化失败
[2025-09-23 23:57:55.306] [INFO] 初始化系统模式管理器...
[2025-09-23 23:57:55.310] [INFO] 开始初始化MotorManager...
[2025-09-23 23:57:55.313] [INFO] 模拟初始化运动控制卡
[2025-09-23 23:57:55.532] [INFO] 加载了8个电机的默认配置
[2025-09-23 23:57:55.533] [INFO] 电机监控任务已启动
[2025-09-23 23:57:55.533] [INFO] MotorManager初始化完成
[2025-09-23 23:57:55.534] [INFO] 初始化通信Manager...
[2025-09-23 23:57:55.541] [INFO] 开始初始化ScannerManager...
[2025-09-23 23:57:55.542] [INFO] 电机监控循环开始
[2025-09-23 23:57:55.545] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-23 23:57:55.559] [INFO] 串口初始化完成
[2025-09-23 23:57:55.574] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-23 23:57:55.611] [INFO] 扫描枪连接成功: COM1
[2025-09-23 23:57:55.611] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-23 23:57:55.612] [INFO] ScannerManager初始化完成
[2025-09-23 23:57:55.615] [INFO] 开始初始化ModbusTcpManager...
[2025-09-23 23:57:55.618] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-23 23:57:55.621] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-23 23:58:00.721] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 23:58:00.724] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-23 23:58:00.724] [INFO] ModbusTcpManager初始化完成
[2025-09-23 23:58:00.727] [INFO] 开始初始化EpsonRobotManager...
[2025-09-23 23:58:00.728] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-23 23:58:00.729] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-23 23:58:00.729] [INFO] EpsonRobotManager初始化完成
[2025-09-23 23:58:00.729] [INFO] 初始化视觉Manager...
[2025-09-23 23:58:00.731] [INFO] 开始初始化VisionManager...
[2025-09-23 23:58:00.732] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-23 23:58:00.733] [INFO] 模拟初始化相机，索引: 0
[2025-09-23 23:58:01.243] [INFO] 相机初始化成功
[2025-09-23 23:58:01.248] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-23 23:58:01.249] [INFO] 视觉配置加载完成
[2025-09-23 23:58:01.250] [INFO] VisionManager初始化完成
[2025-09-23 23:58:01.251] [INFO] 初始化数据Manager...
[2025-09-23 23:58:01.263] [INFO] 开始初始化StatisticsManager...
[2025-09-23 23:58:01.265] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-23 23:58:01.273] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-23 23:58:01.274] [INFO] 历史数据加载完成
[2025-09-23 23:58:01.275] [INFO] 自动保存任务已启动
[2025-09-23 23:58:01.275] [INFO] StatisticsManager初始化完成
[2025-09-23 23:58:01.276] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-23 23:58:01.276] [INFO] 所有Manager初始化完成
[2025-09-23 23:58:01.279] [INFO] 自动保存循环开始
[2025-09-23 23:58:01.329] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 23:58:01.329] [INFO] 主界面布局创建完成
[2025-09-23 23:58:01.331] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-23 23:58:04.980] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 23:58:04.981] [INFO] Epson机器人管理器初始化完成
[2025-09-23 23:58:04.995] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-23 23:58:33.386] [INFO] 开始连接控制端口 - IP: ************, 端口: 6000
[2025-09-23 23:58:33.387] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 23:58:33.390] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-23 23:58:33.392] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-23 23:58:38.505] [ERROR] 连接启动/停止TCP/IP 执行失败
异常详情: 控制端口连接超时: *************:5000
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<<ConnectStartStopAsync>b__71_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 242
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 23:59:09.840] [INFO] 开始连接控制端口 - IP: ************, 端口: 6000
[2025-09-23 23:59:09.840] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-23 23:59:09.841] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-23 23:59:09.841] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-23 23:59:14.956] [ERROR] 连接启动/停止TCP/IP 执行失败
异常详情: 控制端口连接超时: *************:5000
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<<ConnectStartStopAsync>b__71_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 242
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-23 23:59:42.716] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-23 23:59:42.718] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-23 23:59:42.724] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-23 23:59:42.724] [INFO] 翻转电机控制面板业务逻辑初始化完成
