[2025-09-26 15:17:28.697] [INFO] 程序启动开始
[2025-09-26 15:17:28.698] [INFO] 配置系统初始化成功
[2025-09-26 15:17:28.859] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-26 15:17:28.859] [INFO] 配置系统初始化完成
[2025-09-26 15:17:28.859] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-26 15:17:28.868] [INFO] 开始初始化各个Manager...
[2025-09-26 15:17:28.869] [INFO] 初始化DMC1000B控制卡...
[2025-09-26 15:17:28.872] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-26 15:17:28.952] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-26 15:17:28.953] [WARN] DMC1000B控制卡初始化失败，相关功能可能无法正常工作
[2025-09-26 15:17:28.953] [INFO] 初始化基础Manager...
[2025-09-26 15:17:28.957] [INFO] IO状态缓存初始化完成
[2025-09-26 15:17:28.959] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-26 15:17:28.959] [WARN] DMC1000BIO管理器初始化失败
[2025-09-26 15:17:28.963] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-26 15:17:28.963] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 136
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-26 15:17:28.992] [WARN] DMC1000B电机管理器初始化失败
[2025-09-26 15:17:28.992] [INFO] 初始化系统模式管理器...
[2025-09-26 15:17:29.001] [INFO] 安全管理器实例已创建
[2025-09-26 15:17:29.005] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-26 15:17:29.005] [INFO] 开始初始化MotorManager...
[2025-09-26 15:17:29.005] [INFO] 当前模式已经是 Manual，无需切换
[2025-09-26 15:17:29.007] [INFO] 模拟初始化运动控制卡
[2025-09-26 15:17:29.211] [INFO] 加载了8个电机的默认配置
[2025-09-26 15:17:29.212] [INFO] 电机监控任务已启动
[2025-09-26 15:17:29.212] [INFO] MotorManager初始化完成
[2025-09-26 15:17:29.212] [INFO] 初始化通信Manager...
[2025-09-26 15:17:29.214] [INFO] 电机监控循环开始
[2025-09-26 15:17:29.216] [INFO] 开始初始化ScannerManager...
[2025-09-26 15:17:29.217] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-26 15:17:29.219] [INFO] 串口初始化完成
[2025-09-26 15:17:29.221] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-26 15:17:29.223] [INFO] 扫描枪连接成功: COM1
[2025-09-26 15:17:29.223] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-26 15:17:29.223] [INFO] ScannerManager初始化完成
[2025-09-26 15:17:29.227] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-26 15:17:29.227] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-26 15:17:29.228] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-26 15:17:29.229] [INFO] SCARA通信管理器已初始化
[2025-09-26 15:17:29.230] [INFO] ScannerAutoModeManager管理器引用初始化完成
[2025-09-26 15:17:29.232] [INFO] 开始初始化ScannerAutoModeManager...
[2025-09-26 15:17:29.234] [INFO] 开始初始化MultiScannerManager...
[2025-09-26 15:17:29.235] [INFO] 开始初始化扫描枪1...
[2025-09-26 15:17:29.236] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-26 15:17:29.237] [INFO] 扫描枪1串口初始化完成
[2025-09-26 15:17:29.237] [INFO] 扫描枪1初始化完成
[2025-09-26 15:17:29.237] [INFO] 开始初始化扫描枪2...
[2025-09-26 15:17:29.237] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-26 15:17:29.238] [INFO] 扫描枪2串口初始化完成
[2025-09-26 15:17:29.238] [INFO] 扫描枪2初始化完成
[2025-09-26 15:17:29.238] [INFO] 开始初始化扫描枪3...
[2025-09-26 15:17:29.238] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-26 15:17:29.238] [INFO] 扫描枪3串口初始化完成
[2025-09-26 15:17:29.238] [INFO] 扫描枪3初始化完成
[2025-09-26 15:17:29.238] [INFO] MultiScannerManager初始化完成
[2025-09-26 15:17:29.240] [INFO] 开始自动连接并验证所有扫码器...
[2025-09-26 15:17:29.240] [INFO] 正在连接扫码器1...
[2025-09-26 15:17:29.242] [INFO] 扫描枪1状态变更: Connecting - 正在连接扫描枪1...
[2025-09-26 15:17:29.244] [INFO] 扫码器1状态变化: Connecting - 正在连接扫描枪1...
[2025-09-26 15:17:29.251] [ERROR] 连接扫描枪1 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerInstance.<ConnectAsync>b__28_1() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 743
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerInstance.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 737
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-26 15:17:29.257] [WARN] 扫码器1连接失败
[2025-09-26 15:17:29.759] [INFO] 正在连接扫码器2...
[2025-09-26 15:17:29.759] [INFO] 扫描枪2状态变更: Connecting - 正在连接扫描枪2...
[2025-09-26 15:17:29.759] [INFO] 扫码器2状态变化: Connecting - 正在连接扫描枪2...
[2025-09-26 15:17:29.760] [INFO] 扫描枪2连接成功: COM2
[2025-09-26 15:17:29.760] [INFO] 扫描枪2状态变更: Connected - 扫描枪2连接成功
[2025-09-26 15:17:29.760] [INFO] 扫码器2状态变化: Connected - 扫描枪2连接成功
[2025-09-26 15:17:29.760] [INFO] 扫码器2连接成功
[2025-09-26 15:17:29.764] [INFO] 扫描枪2发送数据: hello
[2025-09-26 15:17:30.780] [INFO] 扫码器2hello验证完成
[2025-09-26 15:17:30.780] [INFO] 扫码器2通信验证成功
[2025-09-26 15:17:31.295] [INFO] 正在连接扫码器3...
[2025-09-26 15:17:31.295] [INFO] 扫描枪3状态变更: Connecting - 正在连接扫描枪3...
[2025-09-26 15:17:31.295] [INFO] 扫码器3状态变化: Connecting - 正在连接扫描枪3...
[2025-09-26 15:17:31.298] [ERROR] 连接扫描枪3 执行失败
异常详情: 端口“COM3”不存在。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerInstance.<ConnectAsync>b__28_1() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 743
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerInstance.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 737
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-26 15:17:31.298] [WARN] 扫码器3连接失败
[2025-09-26 15:17:31.810] [WARN] 部分扫码器连接或验证失败，但系统将继续运行
[2025-09-26 15:17:31.810] [INFO] ScannerAutoModeManager初始化完成
[2025-09-26 15:17:31.812] [INFO] 开始初始化ModbusTcpManager...
[2025-09-26 15:17:31.813] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-26 15:17:31.816] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-26 15:17:36.826] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-26 15:17:36.828] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-26 15:17:36.829] [INFO] ModbusTcpManager初始化完成
[2025-09-26 15:17:36.831] [INFO] 开始初始化EpsonRobotManager...
[2025-09-26 15:17:36.831] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-26 15:17:36.832] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-26 15:17:36.832] [INFO] EpsonRobotManager初始化完成
[2025-09-26 15:17:36.833] [INFO] 初始化视觉Manager...
[2025-09-26 15:17:36.835] [INFO] 开始初始化VisionManager...
[2025-09-26 15:17:36.835] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-26 15:17:36.836] [INFO] 模拟初始化相机，索引: 0
[2025-09-26 15:17:37.342] [INFO] 相机初始化成功
[2025-09-26 15:17:37.343] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-26 15:17:37.343] [INFO] 视觉配置加载完成
[2025-09-26 15:17:37.343] [INFO] VisionManager初始化完成
[2025-09-26 15:17:37.343] [INFO] 初始化数据Manager...
[2025-09-26 15:17:37.346] [INFO] 开始初始化StatisticsManager...
[2025-09-26 15:17:37.346] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-26 15:17:37.349] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-26 15:17:37.349] [INFO] 历史数据加载完成
[2025-09-26 15:17:37.349] [INFO] 自动保存任务已启动
[2025-09-26 15:17:37.349] [INFO] StatisticsManager初始化完成
[2025-09-26 15:17:37.349] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-26 15:17:37.349] [INFO] 所有Manager初始化完成
[2025-09-26 15:17:37.351] [INFO] 自动保存循环开始
[2025-09-26 15:17:37.413] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-26 15:17:37.414] [INFO] 主界面布局创建完成
[2025-09-26 15:17:37.415] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-26 15:17:43.323] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-26 15:17:43.323] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-26 15:17:43.327] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-26 15:17:43.327] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-26 15:17:44.777] [INFO] 翻转电机控制面板资源释放完成
[2025-09-26 15:17:44.782] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-26 15:17:44.782] [INFO] Epson机器人管理器初始化完成
[2025-09-26 15:17:44.816] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-26 15:17:45.453] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-26 15:17:45.454] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-26 15:17:45.454] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-26 15:17:45.454] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-26 15:17:46.205] [INFO] 翻转电机控制面板资源释放完成
[2025-09-26 15:17:46.209] [INFO] 翻转电机示教面板业务逻辑初始化完成
[2025-09-26 15:17:46.219] [INFO] 翻转电机示教面板初始化完成 - 按HTML原型设计
[2025-09-26 15:17:51.396] [INFO] 翻转电机示教面板资源释放完成
[2025-09-26 15:17:51.400] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-26 15:17:51.401] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-26 15:17:51.401] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-26 15:17:51.401] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-26 15:17:52.816] [INFO] 翻转电机控制面板资源释放完成
[2025-09-26 15:17:52.819] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-26 15:17:52.819] [INFO] Epson机器人管理器初始化完成
[2025-09-26 15:17:52.821] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-26 15:17:53.435] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-26 15:17:53.435] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-26 15:17:53.436] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-26 15:17:53.436] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-26 15:17:53.904] [INFO] 翻转电机控制面板资源释放完成
[2025-09-26 15:17:53.909] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-26 15:17:54.530] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-26 15:17:54.530] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-26 15:17:54.530] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-26 15:17:54.532] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-26 15:17:55.042] [INFO] 翻转电机控制面板资源释放完成
[2025-09-26 15:17:55.047] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-26 15:17:55.047] [INFO] Epson机器人管理器初始化完成
[2025-09-26 15:17:55.050] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-26 15:17:55.632] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-26 15:17:56.155] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
[2025-09-26 15:17:56.871] [INFO] 生产日志管理面板初始化完成 - 按HTML原型设计
[2025-09-26 15:17:57.634] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-26 15:17:57.634] [INFO] Epson机器人管理器初始化完成
[2025-09-26 15:17:57.638] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-26 15:17:58.129] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-26 15:17:58.129] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-26 15:17:58.129] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-26 15:17:58.129] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-26 15:17:59.153] [INFO] 翻转电机控制面板资源释放完成
[2025-09-26 15:17:59.159] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-26 15:18:00.042] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-26 15:18:00.042] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-26 15:18:00.043] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-26 15:18:00.043] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-26 15:18:00.761] [INFO] 翻转电机控制面板资源释放完成
[2025-09-26 15:18:00.764] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-26 15:18:00.764] [INFO] Epson机器人管理器初始化完成
[2025-09-26 15:18:00.766] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-26 15:18:01.684] [ERROR] IO管理器初始化超时，无法启动IO监控
[2025-09-26 15:18:02.187] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-26 15:18:02.865] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
[2025-09-26 15:18:03.562] [INFO] 生产日志管理面板初始化完成 - 按HTML原型设计
[2025-09-26 15:18:05.181] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-26 15:18:05.181] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-26 15:18:05.181] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-26 15:18:05.181] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-26 15:18:06.440] [INFO] 翻转电机控制面板资源释放完成
[2025-09-26 15:18:06.444] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-26 15:18:06.444] [INFO] Epson机器人管理器初始化完成
[2025-09-26 15:18:06.447] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-26 15:18:08.353] [ERROR] IO管理器初始化超时，无法启动IO监控
[2025-09-26 15:45:12.159] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-26 15:45:12.164] [INFO] 开始按顺序释放各个Manager资源...
[2025-09-26 15:45:12.164] [INFO] 释放工作流管理器资源...
[2025-09-26 15:45:12.166] [INFO] 开始释放WorkflowManager资源...
[2025-09-26 15:45:12.167] [WARN] 皮带电机自动控制未在运行
[2025-09-26 15:45:12.168] [INFO] 工作流事件取消订阅完成
[2025-09-26 15:45:12.169] [INFO] WorkflowManager资源释放完成
[2025-09-26 15:45:12.169] [INFO] 工作流管理器资源释放完成
[2025-09-26 15:45:12.169] [INFO] 释放启动自检管理器资源...
[2025-09-26 15:45:12.170] [INFO] 开始释放启动自检管理器资源...
[2025-09-26 15:45:12.170] [INFO] 启动自检管理器资源释放完成
[2025-09-26 15:45:12.170] [INFO] 启动自检管理器资源释放完成
[2025-09-26 15:45:12.170] [INFO] 释放DMC1000B电机管理器资源...
[2025-09-26 15:45:12.172] [INFO] 开始释放DMC1000B资源...
[2025-09-26 15:45:12.178] [INFO] 停止所有电机部分失败
[2025-09-26 15:45:12.179] [INFO] DMC1000B资源释放完成
[2025-09-26 15:45:12.179] [INFO] DMC1000B电机管理器资源释放完成
[2025-09-26 15:45:12.179] [INFO] 释放DMC1000BIO管理器资源...
[2025-09-26 15:45:12.182] [INFO] DMC1000BIO管理器资源释放完成
[2025-09-26 15:45:12.182] [INFO] 释放其他Manager资源...
[2025-09-26 15:45:12.183] [INFO] 开始释放MotorManager资源...
[2025-09-26 15:45:12.187] [INFO] 电机0停止运动
[2025-09-26 15:45:12.187] [INFO] 电机6停止运动
[2025-09-26 15:45:12.187] [INFO] 电机7停止运动
[2025-09-26 15:45:12.188] [INFO] 电机2停止运动
[2025-09-26 15:45:12.188] [INFO] 电机5停止运动
[2025-09-26 15:45:12.188] [INFO] 电机1停止运动
[2025-09-26 15:45:12.188] [INFO] 电机3停止运动
[2025-09-26 15:45:12.188] [INFO] 电机4停止运动
[2025-09-26 15:45:12.188] [INFO] 所有电机已停止
[2025-09-26 15:45:12.189] [INFO] 电机监控循环被取消
[2025-09-26 15:45:12.189] [INFO] 电机监控循环结束
[2025-09-26 15:45:12.189] [INFO] 电机监控任务已停止
[2025-09-26 15:45:12.190] [INFO] 模拟释放运动控制卡资源
[2025-09-26 15:45:12.190] [INFO] MotorManager资源释放完成
[2025-09-26 15:45:12.191] [INFO] 开始释放ScannerManager资源...
[2025-09-26 15:45:12.216] [INFO] 扫描枪连接已断开
[2025-09-26 15:45:12.216] [INFO] 扫描枪状态变更: Disconnected - 扫描枪连接已断开
[2025-09-26 15:45:12.216] [INFO] ScannerManager资源释放完成
[2025-09-26 15:45:12.218] [INFO] 开始释放ModbusTcpManager资源...
[2025-09-26 15:45:12.221] [INFO] Modbus TCP状态变更: Disconnected - Modbus TCP连接已断开
[2025-09-26 15:45:12.221] [INFO] Modbus TCP连接已断开
[2025-09-26 15:45:12.221] [INFO] ModbusTcpManager资源释放完成
[2025-09-26 15:45:12.223] [INFO] 开始释放EpsonRobotManager资源...
[2025-09-26 15:45:12.227] [INFO] EpsonRobotManager资源释放完成
[2025-09-26 15:45:12.229] [INFO] 开始释放EpsonRobotManager2资源...
[2025-09-26 15:45:12.233] [INFO] EpsonRobotManager2资源释放完成
[2025-09-26 15:45:12.235] [INFO] 开始释放VisionManager资源...
[2025-09-26 15:45:12.236] [INFO] 模拟释放相机资源
[2025-09-26 15:45:12.237] [INFO] VisionManager资源释放完成
[2025-09-26 15:45:12.238] [INFO] 开始释放StatisticsManager资源...
[2025-09-26 15:45:12.238] [INFO] 自动保存循环被取消
[2025-09-26 15:45:12.239] [INFO] 自动保存循环结束
[2025-09-26 15:45:12.239] [INFO] 自动保存任务已停止
[2025-09-26 15:45:12.239] [INFO] StatisticsManager资源释放完成
[2025-09-26 15:45:12.239] [INFO] 其他Manager资源释放完成
[2025-09-26 15:45:12.239] [INFO] 释放DMC1000B控制卡资源...
[2025-09-26 15:45:12.240] [INFO] DMC1000B控制卡未初始化，无需释放
[2025-09-26 15:45:12.240] [INFO] DMC1000B控制卡资源释放完成
[2025-09-26 15:45:12.241] [INFO] 所有Manager资源释放流程完成
[2025-09-26 15:45:12.241] [INFO] 所有资源释放完成，程序即将退出
[2025-09-26 15:45:12.242] [INFO] 程序正在关闭，开始释放所有Manager资源...
