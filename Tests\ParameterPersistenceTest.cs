using System;
using System.IO;
using System.Threading.Tasks;
using MyHMI.Settings;
using MyHMI.Helpers;

namespace MyHMI.Tests
{
    /// <summary>
    /// 参数持久化和程序重启恢复测试
    /// </summary>
    public static class ParameterPersistenceTest
    {
        /// <summary>
        /// 运行参数持久化测试
        /// </summary>
        public static async Task RunParameterPersistenceTestAsync()
        {
            try
            {
                LogHelper.Info("开始参数持久化和程序重启恢复测试...");

                // 测试1：验证设置文件创建
                await TestSettingsFileCreation();

                // 测试2：验证参数保存功能
                await TestParameterSaving();

                // 测试3：验证参数加载功能
                await TestParameterLoading();

                // 测试4：验证参数完整性
                await TestParameterIntegrity();

                // 测试5：验证序列化稳定性
                await TestSerializationStability();

                // 测试6：验证错误恢复机制
                await TestErrorRecovery();

                LogHelper.Info("参数持久化和程序重启恢复测试完成，所有测试通过！");
            }
            catch (Exception ex)
            {
                LogHelper.Error("参数持久化测试失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 测试设置文件创建
        /// </summary>
        private static async Task TestSettingsFileCreation()
        {
            LogHelper.Info("测试1：设置文件创建...");

            await Task.Run(() =>
            {
                // 获取设置文件路径
                string settingsPath = Settings.Settings.GetSettingsFilePath();
                
                if (string.IsNullOrEmpty(settingsPath))
                    throw new Exception("设置文件路径为空");

                LogHelper.Info($"✓ 设置文件路径: {settingsPath}");

                // 确保设置已加载
                Settings.Settings.Load();
                
                // 保存设置以创建文件
                Settings.Settings.Save();

                // 验证文件是否存在
                if (!File.Exists(settingsPath))
                    throw new Exception("设置文件创建失败");

                // 验证文件大小
                var fileInfo = new FileInfo(settingsPath);
                if (fileInfo.Length == 0)
                    throw new Exception("设置文件为空");

                LogHelper.Info($"✓ 设置文件大小: {fileInfo.Length} 字节");
                LogHelper.Info($"✓ 设置文件创建时间: {fileInfo.CreationTime}");
                LogHelper.Info($"✓ 设置文件修改时间: {fileInfo.LastWriteTime}");
            });

            LogHelper.Info("✓ 设置文件创建测试通过");
        }

        /// <summary>
        /// 测试参数保存功能
        /// </summary>
        private static async Task TestParameterSaving()
        {
            LogHelper.Info("测试2：参数保存功能...");

            await Task.Run(() =>
            {
                var settings = Settings.Settings.Current;
                
                // 记录修改前的文件修改时间
                string settingsPath = Settings.Settings.GetSettingsFilePath();
                DateTime beforeModifyTime = File.GetLastWriteTime(settingsPath);

                // 等待1秒确保时间戳不同
                System.Threading.Thread.Sleep(1000);

                // 修改一些参数
                string originalSystemName = settings.System.Name;
                double originalMotorPulse = settings.Motor.LeftFlipPulseEquivalent;
                string originalRobotIP = settings.Communication.EpsonRobot1IP;

                string testSystemName = "测试系统_" + DateTime.Now.ToString("HHmmss");
                double testMotorPulse = 0.987654;
                string testRobotIP = "*************";

                settings.System.Name = testSystemName;
                settings.Motor.LeftFlipPulseEquivalent = testMotorPulse;
                settings.Communication.EpsonRobot1IP = testRobotIP;

                // 保存设置
                Settings.Settings.Save();

                // 验证文件修改时间已更新
                DateTime afterModifyTime = File.GetLastWriteTime(settingsPath);
                if (afterModifyTime <= beforeModifyTime)
                    throw new Exception("设置文件修改时间未更新");

                LogHelper.Info($"✓ 文件修改时间已更新: {beforeModifyTime} -> {afterModifyTime}");

                // 验证内存中的参数已修改
                if (settings.System.Name != testSystemName)
                    throw new Exception("系统名称保存失败");

                if (Math.Abs(settings.Motor.LeftFlipPulseEquivalent - testMotorPulse) > 0.000001)
                    throw new Exception("电机脉冲当量保存失败");

                if (settings.Communication.EpsonRobot1IP != testRobotIP)
                    throw new Exception("机器人IP保存失败");

                LogHelper.Info("✓ 内存中参数修改验证通过");

                // 恢复原始值
                settings.System.Name = originalSystemName;
                settings.Motor.LeftFlipPulseEquivalent = originalMotorPulse;
                settings.Communication.EpsonRobot1IP = originalRobotIP;
                Settings.Settings.Save();
            });

            LogHelper.Info("✓ 参数保存功能测试通过");
        }

        /// <summary>
        /// 测试参数加载功能
        /// </summary>
        private static async Task TestParameterLoading()
        {
            LogHelper.Info("测试3：参数加载功能...");

            await Task.Run(() =>
            {
                // 修改参数并保存
                var settings = Settings.Settings.Current;
                string testValue = "加载测试_" + DateTime.Now.ToString("HHmmss");
                settings.System.Name = testValue;
                Settings.Settings.Save();

                // 重新加载设置
                Settings.Settings.Load();
                var reloadedSettings = Settings.Settings.Current;

                // 验证参数是否正确加载
                if (reloadedSettings.System.Name != testValue)
                    throw new Exception($"参数加载失败，期望: {testValue}, 实际: {reloadedSettings.System.Name}");

                // 验证所有模块都已加载
                if (reloadedSettings.Motor == null)
                    throw new Exception("Motor模块加载失败");

                if (reloadedSettings.Communication == null)
                    throw new Exception("Communication模块加载失败");

                if (reloadedSettings.Vision == null)
                    throw new Exception("Vision模块加载失败");

                if (reloadedSettings.IO == null)
                    throw new Exception("IO模块加载失败");

                if (reloadedSettings.Statistics == null)
                    throw new Exception("Statistics模块加载失败");

                if (reloadedSettings.Workflow == null)
                    throw new Exception("Workflow模块加载失败");

                if (reloadedSettings.UI == null)
                    throw new Exception("UI模块加载失败");

                LogHelper.Info("✓ 所有模块加载验证通过");

                // 恢复默认值
                settings.System.Name = "HR2系统";
                Settings.Settings.Save();
            });

            LogHelper.Info("✓ 参数加载功能测试通过");
        }

        /// <summary>
        /// 测试参数完整性
        /// </summary>
        private static async Task TestParameterIntegrity()
        {
            LogHelper.Info("测试4：参数完整性...");

            await Task.Run(() =>
            {
                var settings = Settings.Settings.Current;

                // 验证关键参数的完整性
                int totalParameters = 0;
                int validParameters = 0;

                // Motor模块参数
                if (settings.Motor.LeftFlipPulseEquivalent > 0) validParameters++;
                if (settings.Motor.RightFlipPulseEquivalent > 0) validParameters++;
                if (settings.Motor.LeftFlipMaxSpeed > 0) validParameters++;
                if (settings.Motor.RightFlipMaxSpeed > 0) validParameters++;
                totalParameters += 4;

                // Communication模块参数
                if (!string.IsNullOrEmpty(settings.Communication.EpsonRobot1IP)) validParameters++;
                if (settings.Communication.EpsonRobot1ControlPort > 0) validParameters++;
                if (!string.IsNullOrEmpty(settings.Communication.ScannerComPort)) validParameters++;
                if (settings.Communication.ScannerBaudRate > 0) validParameters++;
                totalParameters += 4;

                // System模块参数
                if (!string.IsNullOrEmpty(settings.System.Name)) validParameters++;
                if (!string.IsNullOrEmpty(settings.System.Version)) validParameters++;
                if (settings.System.AutoSaveInterval > 0) validParameters++;
                if (settings.System.MotorMonitorInterval > 0) validParameters++;
                totalParameters += 4;

                // Vision模块参数
                if (settings.Vision.CameraIndex >= 0) validParameters++;
                if (settings.Vision.ImageWidth > 0) validParameters++;
                if (settings.Vision.ConfidenceThreshold > 0) validParameters++;
                totalParameters += 3;

                // IO模块参数
                if (settings.IO.InputChannels > 0) validParameters++;
                if (settings.IO.OutputChannels > 0) validParameters++;
                totalParameters += 2;

                double integrityRatio = (double)validParameters / totalParameters;
                LogHelper.Info($"✓ 参数完整性: {validParameters}/{totalParameters} ({integrityRatio:P1})");

                if (integrityRatio < 0.9)
                    throw new Exception($"参数完整性不足: {integrityRatio:P1}");
            });

            LogHelper.Info("✓ 参数完整性测试通过");
        }

        /// <summary>
        /// 测试序列化稳定性
        /// </summary>
        private static async Task TestSerializationStability()
        {
            LogHelper.Info("测试5：序列化稳定性...");

            await Task.Run(() =>
            {
                // 多次保存和加载，验证序列化稳定性
                for (int i = 0; i < 5; i++)
                {
                    var settings = Settings.Settings.Current;
                    
                    // 修改一个参数
                    string testValue = $"稳定性测试_{i}_{DateTime.Now.Ticks}";
                    settings.System.Name = testValue;
                    
                    // 保存
                    Settings.Settings.Save();
                    
                    // 重新加载
                    Settings.Settings.Load();
                    var reloadedSettings = Settings.Settings.Current;
                    
                    // 验证
                    if (reloadedSettings.System.Name != testValue)
                        throw new Exception($"第{i+1}次序列化稳定性测试失败");
                    
                    LogHelper.Info($"✓ 第{i+1}次序列化稳定性测试通过");
                }

                // 恢复默认值
                var finalSettings = Settings.Settings.Current;
                finalSettings.System.Name = "HR2系统";
                Settings.Settings.Save();
            });

            LogHelper.Info("✓ 序列化稳定性测试通过");
        }

        /// <summary>
        /// 测试错误恢复机制
        /// </summary>
        private static async Task TestErrorRecovery()
        {
            LogHelper.Info("测试6：错误恢复机制...");

            await Task.Run(() =>
            {
                string settingsPath = Settings.Settings.GetSettingsFilePath();
                string backupPath = settingsPath + ".backup";

                try
                {
                    // 备份当前设置文件
                    if (File.Exists(settingsPath))
                    {
                        File.Copy(settingsPath, backupPath, true);
                    }

                    // 创建一个损坏的设置文件
                    File.WriteAllText(settingsPath, "这是一个损坏的文件内容");

                    // 尝试加载设置（应该使用默认设置）
                    Settings.Settings.Load();
                    var settings = Settings.Settings.Current;

                    // 验证是否使用了默认设置
                    if (settings == null)
                        throw new Exception("错误恢复失败，设置为null");

                    if (settings.System == null)
                        throw new Exception("错误恢复失败，System设置为null");

                    LogHelper.Info("✓ 损坏文件错误恢复验证通过");

                    // 保存新的设置文件
                    Settings.Settings.Save();

                    // 验证新文件是否正常
                    if (!File.Exists(settingsPath))
                        throw new Exception("错误恢复后文件创建失败");

                    LogHelper.Info("✓ 错误恢复后文件重建验证通过");
                }
                finally
                {
                    // 恢复备份文件
                    if (File.Exists(backupPath))
                    {
                        File.Copy(backupPath, settingsPath, true);
                        File.Delete(backupPath);
                        
                        // 重新加载正确的设置
                        Settings.Settings.Load();
                    }
                }
            });

            LogHelper.Info("✓ 错误恢复机制测试通过");
        }
    }
}
