using System;
using System.Drawing;
using System.Windows.Forms;
using MyHMI.Helpers;

namespace MyHMI.UI.Controls
{
    /// <summary>
    /// 定位相机控制面板 - 按HTML原型设计
    /// </summary>
    public partial class VisionPositionPanel : UserControl
    {
        #region 私有字段
        
        private Panel _mainPanel;
        private Label _titleLabel;
        private Panel _controlGroup;
        private Label _placeholderLabel;
        
        #endregion

        #region 构造函数
        
        public VisionPositionPanel()
        {
            InitializeComponent();
            InitializeInterface();
        }
        
        #endregion

        #region 界面初始化
        
        /// <summary>
        /// 初始化界面 - 按HTML原型精确设计
        /// </summary>
        private void InitializeInterface()
        {
            try
            {
                // 设置面板属性 - 按HTML原型 660x840 (700-40padding)
                this.Size = new Size(660, 840);
                this.BackColor = ColorTranslator.FromHtml("#34495e");
                this.Dock = DockStyle.Fill;
                this.Padding = new Padding(0);

                // 创建主面板 - 按HTML原型padding: 20px
                CreateMainPanel();
                
                // 创建标题 - 按HTML原型样式
                CreateTitle();
                
                // 创建控制组 - 按HTML原型样式
                CreateControlGroup();

                LogHelper.Info("定位相机控制面板初始化完成 - 按HTML原型设计");
            }
            catch (Exception ex)
            {
                LogHelper.Error("定位相机控制面板初始化失败", ex);
            }
        }
        
        /// <summary>
        /// 创建主面板 - 按HTML原型
        /// </summary>
        private void CreateMainPanel()
        {
            _mainPanel = new Panel
            {
                Size = new Size(660, 840),
                Location = new Point(0, 0),
                BackColor = ColorTranslator.FromHtml("#34495e"),
                Padding = new Padding(20), // 按HTML原型 padding: 20px
                Dock = DockStyle.Fill
            };
            
            this.Controls.Add(_mainPanel);
        }
        
        /// <summary>
        /// 创建标题 - 按HTML原型样式
        /// </summary>
        private void CreateTitle()
        {
            _titleLabel = new Label
            {
                Text = "定位相机控制",
                Font = new Font("微软雅黑", 20F, FontStyle.Bold), // 按HTML原型 font-size: 20px
                ForeColor = ColorTranslator.FromHtml("#3498db"),   // 按HTML原型 color: #3498db
                Size = new Size(300, 30),
                Location = new Point(0, 0),
                AutoSize = true
            };
            
            _mainPanel.Controls.Add(_titleLabel);
        }
        
        /// <summary>
        /// 创建控制组 - 按HTML原型样式，调整尺寸确保不超出功能区域
        /// </summary>
        private void CreateControlGroup()
        {
            _controlGroup = new Panel
            {
                Size = new Size(600, 80), // 调整尺寸：600x80，确保不超出功能区域
                Location = new Point(0, 50), // 标题下方20px间距
                BackColor = ColorTranslator.FromHtml("#2c3e50"), // 按HTML原型
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(10) // 调整padding为10px
            };

            // 设置边框颜色 - 按HTML原型 border: 1px solid #4a5661
            _controlGroup.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _controlGroup.Width - 1, _controlGroup.Height - 1);
                }
            };

            // 创建占位符文本 - 按HTML原型
            _placeholderLabel = new Label
            {
                Text = "面板留空白，后期开发使用",
                Font = new Font("微软雅黑", 14F), // 按HTML原型正常文本大小
                ForeColor = ColorTranslator.FromHtml("#95a5a6"), // 按HTML原型占位符颜色
                Size = new Size(580, 60), // 调整尺寸：580x60
                Location = new Point(0, 0),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            _controlGroup.Controls.Add(_placeholderLabel);
            _mainPanel.Controls.Add(_controlGroup);
        }
        
        #endregion
    }
}
