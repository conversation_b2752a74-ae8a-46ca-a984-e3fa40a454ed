using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using MyHMI.Managers;
using MyHMI.Helpers;
using MyHMI.Models;

namespace MyHMI.UI.Controls
{
    /// <summary>
    /// 生产统计面板
    /// </summary>
    public partial class StatisticsPanel : UserControl
    {
        #region 私有字段
        private GroupBox _todayStatsGroupBox;
        private GroupBox _exportGroupBox;
        private GroupBox _historyGroupBox;
        
        private Label _totalCountLabel;
        private Label _successCountLabel;
        private Label _failedCountLabel;
        private Label _successRateLabel;
        private Label _totalTimeLabel;
        
        private DateTimePicker _startDatePicker;
        private DateTimePicker _endDatePicker;
        private Button _exportCsvButton;
        private Button _exportReportButton;
        
        private DataGridView _historyDataGridView;
        private Button _refreshButton;
        
        private Timer _refreshTimer;
        #endregion

        #region 构造函数
        public StatisticsPanel()
        {
            InitializeComponent();
            InitializeUI();
            InitializeTimer();
        }
        #endregion

        #region 初始化方法
        /// <summary>
        /// 初始化UI组件
        /// </summary>
        private void InitializeUI()
        {
            this.Dock = DockStyle.Fill;
            this.BackColor = Color.White;

            CreateTodayStatsGroup();
            CreateExportGroup();
            CreateHistoryGroup();
            
            LayoutControls();
        }

        /// <summary>
        /// 创建今日统计组
        /// </summary>
        private void CreateTodayStatsGroup()
        {
            _todayStatsGroupBox = new GroupBox
            {
                Text = "今日统计",
                Font = new Font("微软雅黑", 10F, FontStyle.Bold),
                Size = new Size(400, 120),
                Location = new Point(10, 10)
            };

            _totalCountLabel = new Label
            {
                Text = "总数量: 0",
                Size = new Size(150, 20),
                Location = new Point(20, 30),
                Font = new Font("微软雅黑", 9F)
            };

            _successCountLabel = new Label
            {
                Text = "成功数量: 0",
                Size = new Size(150, 20),
                Location = new Point(200, 30),
                Font = new Font("微软雅黑", 9F),
                ForeColor = Color.Green
            };

            _failedCountLabel = new Label
            {
                Text = "失败数量: 0",
                Size = new Size(150, 20),
                Location = new Point(20, 60),
                Font = new Font("微软雅黑", 9F),
                ForeColor = Color.Red
            };

            _successRateLabel = new Label
            {
                Text = "成功率: 0.00%",
                Size = new Size(150, 20),
                Location = new Point(200, 60),
                Font = new Font("微软雅黑", 9F, FontStyle.Bold)
            };

            _totalTimeLabel = new Label
            {
                Text = "总耗时: 00:00:00",
                Size = new Size(200, 20),
                Location = new Point(20, 90),
                Font = new Font("微软雅黑", 9F)
            };

            _todayStatsGroupBox.Controls.AddRange(new Control[] 
            { 
                _totalCountLabel, _successCountLabel, _failedCountLabel, 
                _successRateLabel, _totalTimeLabel
            });

            this.Controls.Add(_todayStatsGroupBox);
        }

        /// <summary>
        /// 创建导出组
        /// </summary>
        private void CreateExportGroup()
        {
            _exportGroupBox = new GroupBox
            {
                Text = "数据导出",
                Font = new Font("微软雅黑", 10F, FontStyle.Bold),
                Size = new Size(500, 120),
                Location = new Point(420, 10)
            };

            var startLabel = new Label
            {
                Text = "开始日期:",
                Size = new Size(70, 20),
                Location = new Point(20, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            _startDatePicker = new DateTimePicker
            {
                Size = new Size(120, 25),
                Location = new Point(100, 28),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today
            };

            var endLabel = new Label
            {
                Text = "结束日期:",
                Size = new Size(70, 20),
                Location = new Point(240, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            _endDatePicker = new DateTimePicker
            {
                Size = new Size(120, 25),
                Location = new Point(320, 28),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today
            };

            _exportCsvButton = new Button
            {
                Text = "导出CSV",
                Size = new Size(100, 30),
                Location = new Point(20, 70),
                UseVisualStyleBackColor = true
            };
            _exportCsvButton.Click += ExportCsvButton_Click;

            _exportReportButton = new Button
            {
                Text = "导出报告",
                Size = new Size(100, 30),
                Location = new Point(140, 70),
                UseVisualStyleBackColor = true
            };
            _exportReportButton.Click += ExportReportButton_Click;

            _exportGroupBox.Controls.AddRange(new Control[] 
            { 
                startLabel, _startDatePicker, endLabel, _endDatePicker,
                _exportCsvButton, _exportReportButton
            });

            this.Controls.Add(_exportGroupBox);
        }

        /// <summary>
        /// 创建历史记录组
        /// </summary>
        private void CreateHistoryGroup()
        {
            _historyGroupBox = new GroupBox
            {
                Text = "历史记录",
                Font = new Font("微软雅黑", 10F, FontStyle.Bold),
                Size = new Size(910, 350),
                Location = new Point(10, 140)
            };

            _historyDataGridView = new DataGridView
            {
                Size = new Size(880, 280),
                Location = new Point(15, 50),
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            // 添加列
            _historyDataGridView.Columns.Add("Time", "时间");
            _historyDataGridView.Columns.Add("ProductId", "产品ID");
            _historyDataGridView.Columns.Add("Result", "结果");
            _historyDataGridView.Columns.Add("ProcessingTime", "处理时间(ms)");
            _historyDataGridView.Columns.Add("X", "X坐标");
            _historyDataGridView.Columns.Add("Y", "Y坐标");
            _historyDataGridView.Columns.Add("Angle", "角度");
            _historyDataGridView.Columns.Add("Confidence", "置信度");

            _refreshButton = new Button
            {
                Text = "刷新",
                Size = new Size(80, 30),
                Location = new Point(15, 20),
                UseVisualStyleBackColor = true
            };
            _refreshButton.Click += RefreshButton_Click;

            _historyGroupBox.Controls.AddRange(new Control[] 
            { 
                _historyDataGridView, _refreshButton
            });

            this.Controls.Add(_historyGroupBox);
        }

        /// <summary>
        /// 布局控件
        /// </summary>
        private void LayoutControls()
        {
            this.MinimumSize = new Size(930, 500);
        }

        /// <summary>
        /// 初始化定时器
        /// </summary>
        private void InitializeTimer()
        {
            _refreshTimer = new Timer
            {
                Interval = 5000 // 5秒刷新一次
            };
            _refreshTimer.Tick += RefreshTimer_Tick;
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 异步初始化面板
        /// </summary>
        public async Task InitializeAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                // 启动刷新定时器
                _refreshTimer.Start();

                // 初始化数据
                await RefreshDataAsync();

                LogHelper.Info("统计面板初始化完成");

                return true;
            }, false, "统计面板初始化");
        }

        /// <summary>
        /// 异步释放资源
        /// </summary>
        public async Task DisposeAsync()
        {
            await Task.Run(() =>
            {
                // 停止定时器
                _refreshTimer?.Stop();
                _refreshTimer?.Dispose();

                LogHelper.Info("统计面板资源释放完成");
            });
        }

        /// <summary>
        /// 刷新数据
        /// </summary>
        public async Task RefreshData()
        {
            await RefreshDataAsync();
        }
        #endregion

        #region 事件处理器
        /// <summary>
        /// 导出CSV按钮点击事件
        /// </summary>
        private async void ExportCsvButton_Click(object sender, EventArgs e)
        {
            await ExportCsvAsync();
        }

        /// <summary>
        /// 导出报告按钮点击事件
        /// </summary>
        private async void ExportReportButton_Click(object sender, EventArgs e)
        {
            await ExportReportAsync();
        }

        /// <summary>
        /// 刷新按钮点击事件
        /// </summary>
        private async void RefreshButton_Click(object sender, EventArgs e)
        {
            await RefreshDataAsync();
        }

        /// <summary>
        /// 刷新定时器事件
        /// </summary>
        private async void RefreshTimer_Tick(object sender, EventArgs e)
        {
            await RefreshTodayStatsAsync();
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 刷新所有数据
        /// </summary>
        private async Task RefreshDataAsync()
        {
            await RefreshTodayStatsAsync();
            await RefreshHistoryDataAsync();
        }

        /// <summary>
        /// 刷新今日统计
        /// </summary>
        private async Task RefreshTodayStatsAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                var summary = await Task.Run(() => StatisticsManager.Instance.GetTodaysSummary());

                UIHelper.SafeInvoke(() =>
                {
                    _totalCountLabel.Text = $"总数量: {summary.Total}";
                    _successCountLabel.Text = $"成功数量: {summary.Success}";
                    _failedCountLabel.Text = $"失败数量: {summary.Failed}";
                    _successRateLabel.Text = $"成功率: {summary.SuccessRate:F2}%";
                    _totalTimeLabel.Text = $"总耗时: {summary.TotalTime:hh\\:mm\\:ss}";
                    
                    // 根据成功率设置颜色
                    if (summary.SuccessRate >= 95)
                        _successRateLabel.ForeColor = Color.Green;
                    else if (summary.SuccessRate >= 80)
                        _successRateLabel.ForeColor = Color.Orange;
                    else
                        _successRateLabel.ForeColor = Color.Red;
                });

                return true;
            }, false, "刷新今日统计");
        }

        /// <summary>
        /// 刷新历史数据
        /// </summary>
        private async Task RefreshHistoryDataAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                var records = await Task.Run(() => 
                    StatisticsManager.Instance.GetRecentProductionRecords(100));

                UIHelper.SafeInvoke(() =>
                {
                    _historyDataGridView.Rows.Clear();
                    
                    foreach (var record in records)
                    {
                        var row = new object[]
                        {
                            record.Timestamp.ToString("yyyy-MM-dd HH:mm:ss"),
                            record.ProductId,
                            record.IsSuccess ? "成功" : "失败",
                            record.ProcessingTimeMs.ToString("F1"),
                            record.VisionResult?.X.ToString("F3") ?? "--",
                            record.VisionResult?.Y.ToString("F3") ?? "--",
                            record.VisionResult?.Angle.ToString("F2") ?? "--",
                            record.VisionResult?.Confidence.ToString("F3") ?? "--"
                        };
                        
                        int rowIndex = _historyDataGridView.Rows.Add(row);
                        
                        // 设置行颜色
                        if (!record.IsSuccess)
                        {
                            _historyDataGridView.Rows[rowIndex].DefaultCellStyle.BackColor = Color.LightPink;
                        }
                    }
                });

                return true;
            }, false, "刷新历史数据");
        }

        /// <summary>
        /// 导出CSV
        /// </summary>
        private async Task ExportCsvAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "CSV文件|*.csv",
                    FileName = $"生产数据_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    bool result = await StatisticsManager.Instance.ExportToCsvAsync(
                        saveDialog.FileName, _startDatePicker.Value.Date, _endDatePicker.Value.Date.AddDays(1));

                    if (result)
                    {
                        MessageBox.Show("CSV导出成功", "导出完成", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show("CSV导出失败", "导出错误", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }

                return true;
            }, false, "导出CSV");
        }

        /// <summary>
        /// 导出报告
        /// </summary>
        private async Task ExportReportAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "JSON文件|*.json",
                    FileName = $"统计报告_{DateTime.Now:yyyyMMdd_HHmmss}.json"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    var startDate = _startDatePicker.Value.Date;
                    bool result = await StatisticsManager.Instance.ExportStatisticsReportAsync(
                        saveDialog.FileName, startDate.Year, startDate.Month);

                    if (result)
                    {
                        MessageBox.Show("报告导出成功", "导出完成", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show("报告导出失败", "导出错误", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }

                return true;
            }, false, "导出报告");
        }
        #endregion
    }
}
