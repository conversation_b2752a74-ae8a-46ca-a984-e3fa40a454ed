using System;
using System.Threading.Tasks;
using MyHMI.Managers;
using MyHMI.Models;
using MyHMI.Helpers;

namespace MyHMI.Testing
{
    /// <summary>
    /// WorkflowManager重构验证测试类
    /// 验证重构后的架构是否正常工作
    /// </summary>
    public class WorkflowManagerRefactoringTest
    {
        #region 测试方法

        /// <summary>
        /// 测试1：验证WorkflowManager单例模式
        /// </summary>
        public static async Task<bool> TestWorkflowManagerSingleton()
        {
            try
            {
                LogHelper.Info("=== 测试1：验证WorkflowManager单例模式 ===");

                var instance1 = WorkflowManager.Instance;
                var instance2 = WorkflowManager.Instance;

                bool singletonTest = ReferenceEquals(instance1, instance2);
                LogHelper.Info($"单例模式测试结果: {(singletonTest ? "通过" : "失败")}");

                // 验证初始状态
                bool initialStateTest = instance1.CurrentState == WorkflowState.Idle;
                LogHelper.Info($"初始状态测试结果: {(initialStateTest ? "通过" : "失败")} - 当前状态: {instance1.CurrentState}");

                bool overallResult = singletonTest && initialStateTest;
                LogHelper.Info($"测试1总体结果: {(overallResult ? "通过" : "失败")}");
                return overallResult;
            }
            catch (Exception ex)
            {
                LogHelper.Error("测试1执行异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 测试2：验证WorkflowManager初始化
        /// </summary>
        public static async Task<bool> TestWorkflowManagerInitialization()
        {
            try
            {
                LogHelper.Info("=== 测试2：验证WorkflowManager初始化 ===");

                var workflowManager = WorkflowManager.Instance;

                // 测试初始化
                bool initResult = await workflowManager.InitializeAsync();
                LogHelper.Info($"初始化结果: {(initResult ? "成功" : "失败")}");

                // 验证初始化状态
                bool isInitialized = workflowManager.IsInitialized;
                LogHelper.Info($"初始化状态验证: {(isInitialized ? "已初始化" : "未初始化")}");

                // 测试重复初始化
                bool repeatInitResult = await workflowManager.InitializeAsync();
                LogHelper.Info($"重复初始化结果: {(repeatInitResult ? "成功" : "失败")}");

                bool overallResult = initResult && isInitialized && repeatInitResult;
                LogHelper.Info($"测试2总体结果: {(overallResult ? "通过" : "失败")}");
                return overallResult;
            }
            catch (Exception ex)
            {
                LogHelper.Error("测试2执行异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 测试3：验证BeltMotorAutoModeController独立性
        /// </summary>
        public static async Task<bool> TestBeltMotorAutoModeControllerIndependence()
        {
            try
            {
                LogHelper.Info("=== 测试3：验证BeltMotorAutoModeController独立性 ===");

                var beltMotorController = BeltMotorAutoModeController.Instance;

                // 验证单例模式
                var instance2 = BeltMotorAutoModeController.Instance;
                bool singletonTest = ReferenceEquals(beltMotorController, instance2);
                LogHelper.Info($"BeltMotorAutoModeController单例测试: {(singletonTest ? "通过" : "失败")}");

                // 验证初始状态
                bool initialStateTest = beltMotorController.CurrentState == BeltMotorState.Idle;
                LogHelper.Info($"BeltMotorAutoModeController初始状态: {(initialStateTest ? "正确" : "错误")} - 当前状态: {beltMotorController.CurrentState}");

                // 测试初始化
                bool initResult = await beltMotorController.InitializeAsync();
                LogHelper.Info($"BeltMotorAutoModeController初始化: {(initResult ? "成功" : "失败")}");

                bool overallResult = singletonTest && initialStateTest && initResult;
                LogHelper.Info($"测试3总体结果: {(overallResult ? "通过" : "失败")}");
                return overallResult;
            }
            catch (Exception ex)
            {
                LogHelper.Error("测试3执行异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 测试4：验证工作流启动序列
        /// </summary>
        public static async Task<bool> TestWorkflowStartSequence()
        {
            try
            {
                LogHelper.Info("=== 测试4：验证工作流启动序列 ===");

                var workflowManager = WorkflowManager.Instance;

                // 确保WorkflowManager已初始化
                if (!workflowManager.IsInitialized)
                {
                    await workflowManager.InitializeAsync();
                }

                // 记录初始状态
                var initialState = workflowManager.CurrentState;
                LogHelper.Info($"工作流初始状态: {initialState}");

                // 测试工作流启动
                string testProductId = "TEST_PRODUCT_001";
                bool startResult = await workflowManager.StartWorkflowAsync(testProductId);
                LogHelper.Info($"工作流启动结果: {(startResult ? "成功" : "失败")}");

                // 验证状态变化
                var currentState = workflowManager.CurrentState;
                LogHelper.Info($"工作流当前状态: {currentState}");

                // 停止工作流
                bool stopResult = await workflowManager.StopWorkflowAsync();
                LogHelper.Info($"工作流停止结果: {(stopResult ? "成功" : "失败")}");

                // 验证最终状态
                var finalState = workflowManager.CurrentState;
                LogHelper.Info($"工作流最终状态: {finalState}");

                bool overallResult = startResult && stopResult;
                LogHelper.Info($"测试4总体结果: {(overallResult ? "通过" : "失败")}");
                return overallResult;
            }
            catch (Exception ex)
            {
                LogHelper.Error("测试4执行异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 测试5：验证工作流重置功能
        /// </summary>
        public static async Task<bool> TestWorkflowReset()
        {
            try
            {
                LogHelper.Info("=== 测试5：验证工作流重置功能 ===");

                var workflowManager = WorkflowManager.Instance;

                // 确保WorkflowManager已初始化
                if (!workflowManager.IsInitialized)
                {
                    await workflowManager.InitializeAsync();
                }

                // 测试重置功能
                bool resetResult = await workflowManager.ResetWorkflowAsync();
                LogHelper.Info($"工作流重置结果: {(resetResult ? "成功" : "失败")}");

                // 验证重置后状态
                var stateAfterReset = workflowManager.CurrentState;
                bool stateTest = stateAfterReset == WorkflowState.Idle;
                LogHelper.Info($"重置后状态验证: {(stateTest ? "正确" : "错误")} - 当前状态: {stateAfterReset}");

                bool overallResult = resetResult && stateTest;
                LogHelper.Info($"测试5总体结果: {(overallResult ? "通过" : "失败")}");
                return overallResult;
            }
            catch (Exception ex)
            {
                LogHelper.Error("测试5执行异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 测试6：验证StartupSelfCheckManager调用关系
        /// </summary>
        public static async Task<bool> TestStartupSelfCheckManagerIntegration()
        {
            try
            {
                LogHelper.Info("=== 测试6：验证StartupSelfCheckManager调用关系 ===");

                var startupSelfCheckManager = StartupSelfCheckManager.Instance;
                var beltMotorController = BeltMotorAutoModeController.Instance;

                // 确保BeltMotorAutoModeController已初始化
                if (!beltMotorController.IsInitialized)
                {
                    await beltMotorController.InitializeAsync();
                }

                // 记录初始状态
                var initialState = beltMotorController.CurrentState;
                LogHelper.Info($"皮带电机控制器初始状态: {initialState}");

                // 执行开机自检（这会调用BeltMotorAutoModeController）
                LogHelper.Info("执行开机自检流程...");
                var selfCheckResult = await startupSelfCheckManager.ExecuteStartupSelfCheckAsync();

                // 验证自检结果
                bool selfCheckSuccess = selfCheckResult.OverallSuccess;
                LogHelper.Info($"开机自检总体结果: {(selfCheckSuccess ? "成功" : "失败")}");

                // 验证皮带电机自动控制启动结果
                bool beltMotorSuccess = selfCheckResult.BeltMotorAutoControlSuccess;
                LogHelper.Info($"皮带电机自动控制启动: {(beltMotorSuccess ? "成功" : "失败")}");

                // 验证调用关系正确性（通过状态变化判断）
                var currentState = beltMotorController.CurrentState;
                LogHelper.Info($"皮带电机控制器当前状态: {currentState}");

                bool overallResult = selfCheckSuccess || beltMotorSuccess; // 允许脱机模式
                LogHelper.Info($"测试6总体结果: {(overallResult ? "通过" : "失败")}");
                return overallResult;
            }
            catch (Exception ex)
            {
                LogHelper.Error("测试6执行异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 执行所有测试
        /// </summary>
        public static async Task<bool> RunAllTests()
        {
            try
            {
                LogHelper.Info("========== 开始执行WorkflowManager重构验证测试 ==========");

                bool test1 = await TestWorkflowManagerSingleton();
                bool test2 = await TestWorkflowManagerInitialization();
                bool test3 = await TestBeltMotorAutoModeControllerIndependence();
                bool test4 = await TestWorkflowStartSequence();
                bool test5 = await TestWorkflowReset();
                bool test6 = await TestStartupSelfCheckManagerIntegration();

                // 统计测试结果
                int passedTests = 0;
                int totalTests = 6;

                if (test1) passedTests++;
                if (test2) passedTests++;
                if (test3) passedTests++;
                if (test4) passedTests++;
                if (test5) passedTests++;
                if (test6) passedTests++;

                bool allTestsPassed = passedTests == totalTests;

                LogHelper.Info("========== 测试结果汇总 ==========");
                LogHelper.Info($"测试1 - WorkflowManager单例模式: {(test1 ? "通过" : "失败")}");
                LogHelper.Info($"测试2 - WorkflowManager初始化: {(test2 ? "通过" : "失败")}");
                LogHelper.Info($"测试3 - BeltMotorAutoModeController独立性: {(test3 ? "通过" : "失败")}");
                LogHelper.Info($"测试4 - 工作流启动序列: {(test4 ? "通过" : "失败")}");
                LogHelper.Info($"测试5 - 工作流重置功能: {(test5 ? "通过" : "失败")}");
                LogHelper.Info($"测试6 - StartupSelfCheckManager调用关系: {(test6 ? "通过" : "失败")}");
                LogHelper.Info($"总体结果: {passedTests}/{totalTests} 测试通过");
                LogHelper.Info($"重构验证: {(allTestsPassed ? "成功" : "部分成功")}");

                LogHelper.Info("========== WorkflowManager重构验证测试完成 ==========");
                return allTestsPassed;
            }
            catch (Exception ex)
            {
                LogHelper.Error("执行测试时发生异常", ex);
                return false;
            }
        }

        #endregion
    }
}
