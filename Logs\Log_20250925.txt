[2025-09-25 18:16:56.522] [INFO] 程序启动开始
[2025-09-25 18:16:56.524] [INFO] 配置系统初始化成功
[2025-09-25 18:16:56.559] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-25 18:16:56.559] [INFO] 配置系统初始化完成
[2025-09-25 18:16:56.559] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-25 18:16:56.567] [INFO] 开始初始化各个Manager...
[2025-09-25 18:16:56.568] [INFO] 初始化DMC1000B控制卡...
[2025-09-25 18:16:56.571] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-25 18:16:56.573] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-25 18:16:56.573] [WARN] DMC1000B控制卡初始化失败，相关功能可能无法正常工作
[2025-09-25 18:16:56.573] [INFO] 初始化基础Manager...
[2025-09-25 18:16:56.579] [INFO] IO状态缓存初始化完成
[2025-09-25 18:16:56.580] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-25 18:16:56.580] [WARN] DMC1000BIO管理器初始化失败
[2025-09-25 18:16:56.584] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-25 18:16:56.585] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 136
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-25 18:16:56.653] [WARN] DMC1000B电机管理器初始化失败
[2025-09-25 18:16:56.653] [INFO] 初始化系统模式管理器...
[2025-09-25 18:16:56.658] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-25 18:16:56.658] [INFO] 开始初始化MotorManager...
[2025-09-25 18:16:56.658] [INFO] 当前模式已经是 Manual，无需切换
[2025-09-25 18:16:56.660] [INFO] 模拟初始化运动控制卡
[2025-09-25 18:16:56.875] [INFO] 加载了8个电机的默认配置
[2025-09-25 18:16:56.878] [INFO] 电机监控任务已启动
[2025-09-25 18:16:56.879] [INFO] MotorManager初始化完成
[2025-09-25 18:16:56.880] [INFO] 初始化通信Manager...
[2025-09-25 18:16:56.885] [INFO] 电机监控循环开始
[2025-09-25 18:16:56.889] [INFO] 开始初始化ScannerManager...
[2025-09-25 18:16:56.892] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-25 18:16:56.895] [INFO] 串口初始化完成
[2025-09-25 18:16:56.899] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-25 18:16:56.902] [INFO] 扫描枪连接成功: COM1
[2025-09-25 18:16:56.903] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-25 18:16:56.903] [INFO] ScannerManager初始化完成
[2025-09-25 18:16:56.907] [INFO] 开始初始化ModbusTcpManager...
[2025-09-25 18:16:56.910] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-25 18:16:56.915] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-25 18:17:01.923] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-25 18:17:01.926] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-25 18:17:01.927] [INFO] ModbusTcpManager初始化完成
[2025-09-25 18:17:01.940] [INFO] 开始初始化EpsonRobotManager...
[2025-09-25 18:17:01.942] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-25 18:17:01.947] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-25 18:17:01.948] [INFO] EpsonRobotManager初始化完成
[2025-09-25 18:17:01.949] [INFO] 初始化视觉Manager...
[2025-09-25 18:17:01.956] [INFO] 开始初始化VisionManager...
[2025-09-25 18:17:01.957] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-25 18:17:01.959] [INFO] 模拟初始化相机，索引: 0
[2025-09-25 18:17:02.469] [INFO] 相机初始化成功
[2025-09-25 18:17:02.470] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-25 18:17:02.470] [INFO] 视觉配置加载完成
[2025-09-25 18:17:02.470] [INFO] VisionManager初始化完成
[2025-09-25 18:17:02.470] [INFO] 初始化数据Manager...
[2025-09-25 18:17:02.473] [INFO] 开始初始化StatisticsManager...
[2025-09-25 18:17:02.474] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-25 18:17:02.477] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-25 18:17:02.477] [INFO] 历史数据加载完成
[2025-09-25 18:17:02.477] [INFO] 自动保存任务已启动
[2025-09-25 18:17:02.477] [INFO] StatisticsManager初始化完成
[2025-09-25 18:17:02.477] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-25 18:17:02.477] [INFO] 所有Manager初始化完成
[2025-09-25 18:17:02.478] [INFO] 自动保存循环开始
[2025-09-25 18:17:02.519] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-25 18:17:02.519] [INFO] 主界面布局创建完成
[2025-09-25 18:17:02.520] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-25 18:20:59.137] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-25 18:20:59.141] [INFO] 开始按顺序释放各个Manager资源...
[2025-09-25 18:20:59.141] [INFO] 释放工作流管理器资源...
[2025-09-25 18:20:59.143] [INFO] 开始释放WorkflowManager资源...
[2025-09-25 18:20:59.145] [WARN] 皮带电机自动控制未在运行
[2025-09-25 18:20:59.145] [INFO] 工作流事件取消订阅完成
[2025-09-25 18:20:59.146] [INFO] WorkflowManager资源释放完成
[2025-09-25 18:20:59.146] [INFO] 工作流管理器资源释放完成
[2025-09-25 18:20:59.146] [INFO] 释放启动自检管理器资源...
[2025-09-25 18:20:59.147] [INFO] 开始释放启动自检管理器资源...
[2025-09-25 18:20:59.147] [INFO] 启动自检管理器资源释放完成
[2025-09-25 18:20:59.147] [INFO] 启动自检管理器资源释放完成
[2025-09-25 18:20:59.147] [INFO] 释放DMC1000B电机管理器资源...
[2025-09-25 18:20:59.149] [INFO] 开始释放DMC1000B资源...
[2025-09-25 18:20:59.156] [INFO] 停止所有电机部分失败
[2025-09-25 18:20:59.157] [INFO] DMC1000B资源释放完成
[2025-09-25 18:20:59.157] [INFO] DMC1000B电机管理器资源释放完成
[2025-09-25 18:20:59.157] [INFO] 释放DMC1000BIO管理器资源...
[2025-09-25 18:20:59.161] [INFO] DMC1000BIO管理器资源释放完成
[2025-09-25 18:20:59.161] [INFO] 释放其他Manager资源...
[2025-09-25 18:20:59.163] [INFO] 开始释放MotorManager资源...
[2025-09-25 18:20:59.167] [INFO] 电机0停止运动
[2025-09-25 18:20:59.168] [INFO] 电机5停止运动
[2025-09-25 18:20:59.168] [INFO] 电机6停止运动
[2025-09-25 18:20:59.168] [INFO] 电机3停止运动
[2025-09-25 18:20:59.168] [INFO] 电机4停止运动
[2025-09-25 18:20:59.168] [INFO] 电机7停止运动
[2025-09-25 18:20:59.168] [INFO] 电机1停止运动
[2025-09-25 18:20:59.169] [INFO] 电机2停止运动
[2025-09-25 18:20:59.169] [INFO] 所有电机已停止
[2025-09-25 18:20:59.170] [INFO] 电机监控循环被取消
[2025-09-25 18:20:59.170] [INFO] 电机监控循环结束
[2025-09-25 18:20:59.170] [INFO] 电机监控任务已停止
[2025-09-25 18:20:59.170] [INFO] 模拟释放运动控制卡资源
[2025-09-25 18:20:59.171] [INFO] MotorManager资源释放完成
[2025-09-25 18:20:59.172] [INFO] 开始释放ScannerManager资源...
[2025-09-25 18:20:59.190] [INFO] 扫描枪连接已断开
[2025-09-25 18:20:59.190] [INFO] 扫描枪状态变更: Disconnected - 扫描枪连接已断开
[2025-09-25 18:20:59.190] [INFO] ScannerManager资源释放完成
[2025-09-25 18:20:59.192] [INFO] 开始释放ModbusTcpManager资源...
[2025-09-25 18:20:59.193] [INFO] Modbus TCP状态变更: Disconnected - Modbus TCP连接已断开
[2025-09-25 18:20:59.193] [INFO] Modbus TCP连接已断开
[2025-09-25 18:20:59.193] [INFO] ModbusTcpManager资源释放完成
[2025-09-25 18:20:59.195] [INFO] 开始释放EpsonRobotManager资源...
[2025-09-25 18:20:59.199] [INFO] EpsonRobotManager资源释放完成
[2025-09-25 18:20:59.201] [INFO] 开始释放EpsonRobotManager2资源...
[2025-09-25 18:20:59.206] [INFO] EpsonRobotManager2资源释放完成
[2025-09-25 18:20:59.207] [INFO] 开始释放VisionManager资源...
[2025-09-25 18:20:59.209] [INFO] 模拟释放相机资源
[2025-09-25 18:20:59.209] [INFO] VisionManager资源释放完成
[2025-09-25 18:20:59.210] [INFO] 开始释放StatisticsManager资源...
[2025-09-25 18:20:59.211] [INFO] 自动保存循环被取消
[2025-09-25 18:20:59.211] [INFO] 自动保存循环结束
[2025-09-25 18:20:59.211] [INFO] 自动保存任务已停止
[2025-09-25 18:20:59.214] [INFO] StatisticsManager资源释放完成
[2025-09-25 18:20:59.214] [INFO] 其他Manager资源释放完成
[2025-09-25 18:20:59.214] [INFO] 释放DMC1000B控制卡资源...
[2025-09-25 18:20:59.216] [INFO] DMC1000B控制卡未初始化，无需释放
[2025-09-25 18:20:59.216] [INFO] DMC1000B控制卡资源释放完成
[2025-09-25 18:20:59.216] [INFO] 所有Manager资源释放流程完成
[2025-09-25 18:20:59.217] [INFO] 所有资源释放完成，程序即将退出
[2025-09-25 18:20:59.218] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-25 18:21:36.801] [INFO] 启动测试模式: quick
[2025-09-25 18:21:36.802] [INFO] 配置系统初始化成功
[2025-09-25 18:21:36.837] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-25 18:21:36.838] [INFO] 初始化测试所需的基本Manager...
[2025-09-25 18:21:36.839] [INFO] SCARA通信管理器已初始化
[2025-09-25 18:21:36.839] [INFO] SCARA通信管理器已初始化
[2025-09-25 18:21:36.843] [INFO] 开始初始化SCARA自动模式控制器...
[2025-09-25 18:21:36.850] [INFO] IO状态缓存初始化完成
[2025-09-25 18:21:36.850] [INFO] SCARA自动模式控制器初始化完成
[2025-09-25 18:21:36.850] [INFO] SCARA自动模式控制器初始化成功
[2025-09-25 18:21:36.851] [INFO] 基本Manager初始化完成
[2025-09-25 18:21:36.852] [INFO] 开始执行SCARA系统快速测试
[2025-09-25 18:21:36.855] [INFO] 快速测试全部通过
[2025-09-25 18:21:36.855] [INFO] 测试通过
[2025-09-25 18:21:43.201] [INFO] 启动测试模式: quick
[2025-09-25 18:21:43.202] [INFO] 配置系统初始化成功
[2025-09-25 18:21:43.240] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-25 18:21:43.241] [INFO] 初始化测试所需的基本Manager...
[2025-09-25 18:21:43.242] [INFO] SCARA通信管理器已初始化
[2025-09-25 18:21:43.242] [INFO] SCARA通信管理器已初始化
[2025-09-25 18:21:43.247] [INFO] 开始初始化SCARA自动模式控制器...
[2025-09-25 18:21:43.253] [INFO] IO状态缓存初始化完成
[2025-09-25 18:21:43.254] [INFO] SCARA自动模式控制器初始化完成
[2025-09-25 18:21:43.254] [INFO] SCARA自动模式控制器初始化成功
[2025-09-25 18:21:43.254] [INFO] 基本Manager初始化完成
[2025-09-25 18:21:43.255] [INFO] 开始执行SCARA系统快速测试
[2025-09-25 18:21:43.258] [INFO] 快速测试全部通过
[2025-09-25 18:21:43.258] [INFO] 测试通过
[2025-09-25 18:22:03.486] [INFO] 启动测试模式: full
[2025-09-25 18:22:03.487] [INFO] 配置系统初始化成功
[2025-09-25 18:22:03.522] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-25 18:22:03.523] [INFO] 初始化测试所需的基本Manager...
[2025-09-25 18:22:03.524] [INFO] SCARA通信管理器已初始化
[2025-09-25 18:22:03.524] [INFO] SCARA通信管理器已初始化
[2025-09-25 18:22:03.529] [INFO] 开始初始化SCARA自动模式控制器...
[2025-09-25 18:22:03.535] [INFO] IO状态缓存初始化完成
[2025-09-25 18:22:03.536] [INFO] SCARA自动模式控制器初始化完成
[2025-09-25 18:22:03.536] [INFO] SCARA自动模式控制器初始化成功
[2025-09-25 18:22:03.536] [INFO] 基本Manager初始化完成
[2025-09-25 18:22:03.537] [INFO] 开始执行SCARA系统完整测试
[2025-09-25 18:22:03.538] [INFO] 开始执行SCARA系统快速测试
[2025-09-25 18:22:03.541] [INFO] 快速测试全部通过
[2025-09-25 18:22:03.544] [INFO] 启动SCARA自动模式...
[2025-09-25 18:22:03.544] [INFO] 所有SCARA通信字段已复位
[2025-09-25 18:22:03.547] [INFO] SCARA状态变化: Idle -> Initializing 开始SCARA自动模式
[2025-09-25 18:22:03.548] [INFO] SCARA自动模式主循环开始
[2025-09-25 18:22:03.549] [INFO] 开始执行步骤1：初始化准备
[2025-09-25 18:22:03.549] [INFO] 左翻转电机开始回原点...
[2025-09-25 18:22:03.553] [ERROR] 翻转电机轴0回零 执行失败
异常详情: 翻转电机轴0参数未设置
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass50_0.<<FlipMotorHomeAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 249
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-25 18:22:03.575] [ERROR] SCARA自动模式错误: 左翻转电机回原点失败
[2025-09-25 18:22:03.575] [INFO] SCARA状态变化: Initializing -> Error 左翻转电机回原点失败
[2025-09-25 18:22:03.576] [INFO] === SCARA系统状态快照 ===
[2025-09-25 18:22:03.576] [INFO] 当前状态: Error
[2025-09-25 18:22:03.576] [INFO] 线程运行状态: True
[2025-09-25 18:22:03.576] [INFO] 取消令牌状态: False
[2025-09-25 18:22:03.577] [INFO] 通信字段状态:
[2025-09-25 18:22:03.577] [INFO]   L_moto_ready: False
[2025-09-25 18:22:03.577] [INFO]   R_moto_ready: False
[2025-09-25 18:22:03.577] [INFO]   L_position_Arrived: False
[2025-09-25 18:22:03.577] [INFO]   R_position_Arrived: False
[2025-09-25 18:22:03.577] [INFO]   L_gripper_ok: False
[2025-09-25 18:22:03.577] [INFO]   R_gripper_ok: False
[2025-09-25 18:22:03.578] [INFO]   L_Angle_ok: False
[2025-09-25 18:22:03.578] [INFO]   R_Angle_ok: False
[2025-09-25 18:22:03.578] [INFO]   L_safe_ok: False
[2025-09-25 18:22:03.578] [INFO]   R_safe_ok: False
[2025-09-25 18:22:03.578] [INFO]   L_dataget_ok: False
[2025-09-25 18:22:03.578] [INFO]   R_dataget_ok: False
[2025-09-25 18:22:03.578] [INFO]   Scanner3_dataget_ok: False
[2025-09-25 18:22:03.579] [INFO]   L_moto_finish: False
[2025-09-25 18:22:03.579] [INFO]   R_moto_finish: False
[2025-09-25 18:22:03.584] [INFO] 内存使用: 33 MB
[2025-09-25 18:22:03.585] [INFO] CPU时间: 125 ms
[2025-09-25 18:22:03.585] [INFO] 线程数: 13
[2025-09-25 18:22:03.586] [INFO] GC Gen0: 0, Gen1: 0, Gen2: 0
[2025-09-25 18:22:03.586] [INFO] 总内存: 3 MB
[2025-09-25 18:22:03.586] [INFO] === 系统状态快照结束 ===
[2025-09-25 18:22:03.586] [ERROR] SCARA自动模式错误: 步骤1：初始化准备失败
[2025-09-25 18:22:03.586] [INFO] === SCARA系统状态快照 ===
[2025-09-25 18:22:03.586] [INFO] 当前状态: Error
[2025-09-25 18:22:03.587] [INFO] 线程运行状态: True
[2025-09-25 18:22:03.587] [INFO] 取消令牌状态: False
[2025-09-25 18:22:03.587] [INFO] 通信字段状态:
[2025-09-25 18:22:03.588] [INFO]   L_moto_ready: False
[2025-09-25 18:22:03.588] [INFO]   R_moto_ready: False
[2025-09-25 18:22:03.588] [INFO]   L_position_Arrived: False
[2025-09-25 18:22:03.588] [INFO]   R_position_Arrived: False
[2025-09-25 18:22:03.588] [INFO]   L_gripper_ok: False
[2025-09-25 18:22:03.589] [INFO]   R_gripper_ok: False
[2025-09-25 18:22:03.589] [INFO]   L_Angle_ok: False
[2025-09-25 18:22:03.589] [INFO]   R_Angle_ok: False
[2025-09-25 18:22:03.589] [INFO]   L_safe_ok: False
[2025-09-25 18:22:03.589] [INFO] 开始尝试故障恢复...
[2025-09-25 18:22:03.589] [INFO]   R_safe_ok: False
[2025-09-25 18:22:03.589] [INFO]   L_dataget_ok: False
[2025-09-25 18:22:03.590] [INFO]   R_dataget_ok: False
[2025-09-25 18:22:03.590] [INFO]   Scanner3_dataget_ok: False
[2025-09-25 18:22:03.590] [INFO]   L_moto_finish: False
[2025-09-25 18:22:03.590] [INFO]   R_moto_finish: False
[2025-09-25 18:22:03.593] [INFO] 内存使用: 33 MB
[2025-09-25 18:22:03.593] [INFO] CPU时间: 156.25 ms
[2025-09-25 18:22:03.594] [INFO] 线程数: 14
[2025-09-25 18:22:03.594] [INFO] GC Gen0: 0, Gen1: 0, Gen2: 0
[2025-09-25 18:22:03.594] [INFO] 总内存: 3 MB
[2025-09-25 18:22:03.594] [INFO] === 系统状态快照结束 ===
[2025-09-25 18:22:03.594] [INFO] 开始尝试故障恢复...
[2025-09-25 18:22:04.553] [INFO] 停止SCARA自动模式...
[2025-09-25 18:22:04.554] [INFO] SCARA状态变化: Error -> Idle SCARA自动模式已停止
[2025-09-25 18:22:04.554] [INFO] 所有SCARA通信字段已复位
[2025-09-25 18:22:04.554] [INFO] SCARA自动模式已停止
[2025-09-25 18:22:04.557] [INFO] 测试通过
[2025-09-25 18:22:24.969] [INFO] 启动测试模式: performance
[2025-09-25 18:22:24.970] [INFO] 配置系统初始化成功
[2025-09-25 18:22:25.006] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-25 18:22:25.007] [INFO] 初始化测试所需的基本Manager...
[2025-09-25 18:22:25.008] [INFO] SCARA通信管理器已初始化
[2025-09-25 18:22:25.008] [INFO] SCARA通信管理器已初始化
[2025-09-25 18:22:25.012] [INFO] 开始初始化SCARA自动模式控制器...
[2025-09-25 18:22:25.021] [INFO] IO状态缓存初始化完成
[2025-09-25 18:22:25.022] [INFO] SCARA自动模式控制器初始化完成
[2025-09-25 18:22:25.022] [INFO] SCARA自动模式控制器初始化成功
[2025-09-25 18:22:25.022] [INFO] 基本Manager初始化完成
[2025-09-25 18:22:25.023] [INFO] 开始执行SCARA系统性能测试
[2025-09-25 18:22:25.025] [INFO] 开始执行SCARA系统快速测试
[2025-09-25 18:22:25.028] [INFO] 快速测试全部通过
[2025-09-25 18:22:25.033] [INFO] 测试通过
