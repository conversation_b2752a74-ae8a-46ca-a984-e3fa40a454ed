using System;
using System.Drawing;
using System.Windows.Forms;
using System.Threading.Tasks;
using MyHMI.Helpers;
using MyHMI.Managers;
using MyHMI.Models;
using MyHMI.Events;

namespace MyHMI.UI.Controls
{
    public partial class MotorBeltPanel : UserControl
    {
        #region 私有字段
        private Panel _mainPanel;
        private Label _titleLabel;
        private Panel _inputBeltPanel;
        private Panel _outputBeltPanel;

        // 业务逻辑相关
        private DMC1000BMotorManager _motorManager;
        private const short INPUT_BELT_AXIS = 3;
        private const short OUTPUT_BELT_AXIS = 2;
        private bool _isUpdatingUI = false;

        // 输入皮带电机控件
        private TextBox _inputPulseEquivalentTextBox;
        private TextBox _inputMaxAccelerationTextBox;
        private TextBox _inputRunSpeedTextBox;
        private TextBox _inputJogDistanceTextBox;

        // 输出皮带电机控件
        private TextBox _outputPulseEquivalentTextBox;
        private TextBox _outputMaxAccelerationTextBox;
        private TextBox _outputRunSpeedTextBox;
        private TextBox _outputJogDistanceTextBox;

        // 控制按钮
        private Button _inputPositiveJogButton, _inputNegativeJogButton;
        private Button _inputContinuousPositiveButton, _inputContinuousNegativeButton, _inputStopButton;
        private Button _outputPositiveJogButton, _outputNegativeJogButton;
        private Button _outputContinuousPositiveButton, _outputContinuousNegativeButton, _outputStopButton;
        #endregion

        #region 构造函数
        public MotorBeltPanel()
        {
            InitializeComponent();
            InitializeInterface();
            InitializeBusinessLogic();
        }
        #endregion

        #region 界面初始化
        private void InitializeInterface()
        {
            try
            {
                this.Size = new Size(660, 840);
                this.BackColor = ColorTranslator.FromHtml("#34495e");
                this.Dock = DockStyle.Fill;
                this.Padding = new Padding(0);

                CreateMainPanel();
                CreateTitle();
                CreateInputBeltPanel();
                CreateOutputBeltPanel();

                LogHelper.Info("皮带电机控制面板初始化完成 - 双电机设计");
            }
            catch (Exception ex)
            {
                LogHelper.Error("皮带电机控制面板初始化失败", ex);
            }
        }

        private void CreateMainPanel()
        {
            _mainPanel = new Panel
            {
                Size = new Size(660, 840),
                Location = new Point(0, 0),
                BackColor = ColorTranslator.FromHtml("#34495e"),
                Padding = new Padding(20),
                Dock = DockStyle.Fill
            };
            this.Controls.Add(_mainPanel);
        }

        private void CreateTitle()
        {
            _titleLabel = new Label
            {
                Text = "皮带电机控制",
                Font = new Font("微软雅黑", 20F, FontStyle.Bold),
                ForeColor = ColorTranslator.FromHtml("#3498db"),
                Size = new Size(300, 30),
                Location = new Point(0, 0),
                AutoSize = true
            };
            _mainPanel.Controls.Add(_titleLabel);
        }

        private void CreateInputBeltPanel()
        {
            _inputBeltPanel = new Panel
            {
                Size = new Size(600, 380),
                Location = new Point(0, 50),
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15)
            };

            _inputBeltPanel.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _inputBeltPanel.Width - 1, _inputBeltPanel.Height - 1);
                }
            };

            var titleLabel = new Label
            {
                Text = "输入皮带电机",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(200, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };

            CreateBeltParams(_inputBeltPanel, 35);
            CreateBeltControls(_inputBeltPanel, 220);

            _inputBeltPanel.Controls.Add(titleLabel);
            _mainPanel.Controls.Add(_inputBeltPanel);
        }

        private void CreateOutputBeltPanel()
        {
            _outputBeltPanel = new Panel
            {
                Size = new Size(600, 380),
                Location = new Point(0, 450),
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15)
            };

            _outputBeltPanel.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _outputBeltPanel.Width - 1, _outputBeltPanel.Height - 1);
                }
            };

            var titleLabel = new Label
            {
                Text = "输出皮带电机",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(200, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };

            CreateBeltParams(_outputBeltPanel, 35);
            CreateBeltControls(_outputBeltPanel, 220);

            _outputBeltPanel.Controls.Add(titleLabel);
            _mainPanel.Controls.Add(_outputBeltPanel);
        }

        private void CreateBeltParams(Panel parentPanel, int yOffset)
        {
            var paramsPanel = new Panel
            {
                Size = new Size(570, 160),
                Location = new Point(0, yOffset),
                BackColor = Color.Transparent
            };

            var groupTitle = new Label
            {
                Text = "参数设置",
                Font = new Font("微软雅黑", 14F, FontStyle.Bold),
                ForeColor = ColorTranslator.FromHtml("#3498db"),
                Size = new Size(100, 20),
                Location = new Point(0, 0),
                AutoSize = true
            };

            // 根据父面板确定是输入皮带还是输出皮带
            bool isInputBelt = parentPanel == _inputBeltPanel;

            var pulseEquivalentTextBox = CreateParamGroup(paramsPanel, "脉冲当量:", "0.01", "mm/pulse", 25, 0);
            var maxAccelerationTextBox = CreateParamGroup(paramsPanel, "最大加速度:", "500", "mm/s²", 25, 285);
            var runSpeedTextBox = CreateParamGroup(paramsPanel, "运行速度:", "100", "mm/s", 70, 0);
            var jogDistanceTextBox = CreateParamGroup(paramsPanel, "点动移动距离:", "10", "mm", 70, 285);

            // 保存TextBox引用并添加事件处理
            if (isInputBelt)
            {
                _inputPulseEquivalentTextBox = pulseEquivalentTextBox;
                _inputMaxAccelerationTextBox = maxAccelerationTextBox;
                _inputRunSpeedTextBox = runSpeedTextBox;
                _inputJogDistanceTextBox = jogDistanceTextBox;

                // 添加参数验证事件
                _inputPulseEquivalentTextBox.Leave += (s, e) => ValidateInputParameter(_inputPulseEquivalentTextBox, "脉冲当量", 0.001, 1.0);
                _inputMaxAccelerationTextBox.Leave += (s, e) => ValidateInputParameter(_inputMaxAccelerationTextBox, "最大加速度", 100, 2000);
                _inputRunSpeedTextBox.Leave += (s, e) => ValidateInputParameter(_inputRunSpeedTextBox, "运行速度", 10, 500);
                _inputJogDistanceTextBox.Leave += (s, e) => ValidateInputParameter(_inputJogDistanceTextBox, "点动距离", 1, 100);

                // 添加参数变化事件处理
                _inputPulseEquivalentTextBox.TextChanged += (s, e) => OnInputParameterChanged();
                _inputMaxAccelerationTextBox.TextChanged += (s, e) => OnInputParameterChanged();
                _inputRunSpeedTextBox.TextChanged += (s, e) => OnInputParameterChanged();
                _inputJogDistanceTextBox.TextChanged += (s, e) => OnInputParameterChanged();
            }
            else
            {
                _outputPulseEquivalentTextBox = pulseEquivalentTextBox;
                _outputMaxAccelerationTextBox = maxAccelerationTextBox;
                _outputRunSpeedTextBox = runSpeedTextBox;
                _outputJogDistanceTextBox = jogDistanceTextBox;

                // 添加参数验证事件
                _outputPulseEquivalentTextBox.Leave += (s, e) => ValidateInputParameter(_outputPulseEquivalentTextBox, "脉冲当量", 0.001, 1.0);
                _outputMaxAccelerationTextBox.Leave += (s, e) => ValidateInputParameter(_outputMaxAccelerationTextBox, "最大加速度", 100, 2000);
                _outputRunSpeedTextBox.Leave += (s, e) => ValidateInputParameter(_outputRunSpeedTextBox, "运行速度", 10, 500);
                _outputJogDistanceTextBox.Leave += (s, e) => ValidateInputParameter(_outputJogDistanceTextBox, "点动距离", 1, 100);

                // 添加参数变化事件处理
                _outputPulseEquivalentTextBox.TextChanged += (s, e) => OnOutputParameterChanged();
                _outputMaxAccelerationTextBox.TextChanged += (s, e) => OnOutputParameterChanged();
                _outputRunSpeedTextBox.TextChanged += (s, e) => OnOutputParameterChanged();
                _outputJogDistanceTextBox.TextChanged += (s, e) => OnOutputParameterChanged();
            }

            paramsPanel.Controls.Add(groupTitle);
            parentPanel.Controls.Add(paramsPanel);
        }

        private void CreateBeltControls(Panel parentPanel, int yOffset)
        {
            var controlsPanel = new Panel
            {
                Size = new Size(570, 120),
                Location = new Point(0, yOffset),
                BackColor = Color.Transparent
            };

            var groupTitle = new Label
            {
                Text = "控制操作",
                Font = new Font("微软雅黑", 14F, FontStyle.Bold),
                ForeColor = ColorTranslator.FromHtml("#3498db"),
                Size = new Size(100, 20),
                Location = new Point(0, 0),
                AutoSize = true
            };

            // 根据父面板确定是输入皮带还是输出皮带
            bool isInputBelt = parentPanel == _inputBeltPanel;

            var positiveJogButton = CreateBeltButton("正转点动", new Point(0, 30), ColorTranslator.FromHtml("#3498db"));
            var negativeJogButton = CreateBeltButton("反转点动", new Point(120, 30), ColorTranslator.FromHtml("#3498db"));
            var continuousPositiveButton = CreateBeltButton("连续正转", new Point(240, 30), ColorTranslator.FromHtml("#27ae60"));
            var continuousNegativeButton = CreateBeltButton("连续反转", new Point(360, 30), ColorTranslator.FromHtml("#27ae60"));
            var stopButton = CreateBeltButton("停止", new Point(480, 30), ColorTranslator.FromHtml("#e74c3c"));

            controlsPanel.Controls.Add(groupTitle);
            controlsPanel.Controls.Add(positiveJogButton);
            controlsPanel.Controls.Add(negativeJogButton);
            controlsPanel.Controls.Add(continuousPositiveButton);
            controlsPanel.Controls.Add(continuousNegativeButton);
            controlsPanel.Controls.Add(stopButton);

            // 保存按钮引用并添加事件处理
            if (isInputBelt)
            {
                _inputPositiveJogButton = positiveJogButton;
                _inputNegativeJogButton = negativeJogButton;
                _inputContinuousPositiveButton = continuousPositiveButton;
                _inputContinuousNegativeButton = continuousNegativeButton;
                _inputStopButton = stopButton;

                // 添加事件处理
                // 注意: 方向已反转 - "正转"按钮传false,"反转"按钮传true
                positiveJogButton.Click += async (s, e) => await OnInputBeltJogAsync(false);  // 反转方向
                negativeJogButton.Click += async (s, e) => await OnInputBeltJogAsync(true);   // 反转方向
                continuousPositiveButton.Click += async (s, e) => await OnInputBeltContinuousRunAsync(false);  // 反转方向
                continuousNegativeButton.Click += async (s, e) => await OnInputBeltContinuousRunAsync(true);   // 反转方向
                stopButton.Click += async (s, e) => await OnInputBeltStopAsync();
            }
            else
            {
                _outputPositiveJogButton = positiveJogButton;
                _outputNegativeJogButton = negativeJogButton;
                _outputContinuousPositiveButton = continuousPositiveButton;
                _outputContinuousNegativeButton = continuousNegativeButton;
                _outputStopButton = stopButton;

                // 添加事件处理
                // 注意: 方向已反转 - "正转"按钮传false,"反转"按钮传true
                positiveJogButton.Click += async (s, e) => await OnOutputBeltJogAsync(false);  // 反转方向
                negativeJogButton.Click += async (s, e) => await OnOutputBeltJogAsync(true);   // 反转方向
                continuousPositiveButton.Click += async (s, e) => await OnOutputBeltContinuousRunAsync(false);  // 反转方向
                continuousNegativeButton.Click += async (s, e) => await OnOutputBeltContinuousRunAsync(true);   // 反转方向
                stopButton.Click += async (s, e) => await OnOutputBeltStopAsync();
            }

            parentPanel.Controls.Add(controlsPanel);
        }
        #endregion

        #region 辅助方法
        private TextBox CreateParamGroup(Panel parent, string labelText, string defaultValue, string unit, int yOffset, int xOffset)
        {
            var label = new Label
            {
                Text = labelText,
                Font = new Font("微软雅黑", 12F),
                ForeColor = Color.White,
                Size = new Size(120, 25),
                Location = new Point(xOffset, yOffset + 5),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var textBox = new TextBox
            {
                Text = defaultValue,
                Font = new Font("微软雅黑", 12F),
                Size = new Size(80, 25),
                Location = new Point(xOffset + 125, yOffset),
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var unitLabel = new Label
            {
                Text = unit,
                Font = new Font("微软雅黑", 12F),
                ForeColor = ColorTranslator.FromHtml("#95a5a6"),
                Size = new Size(60, 25),
                Location = new Point(xOffset + 210, yOffset + 5),
                TextAlign = ContentAlignment.MiddleLeft
            };

            parent.Controls.Add(label);
            parent.Controls.Add(textBox);
            parent.Controls.Add(unitLabel);

            return textBox; // 返回TextBox引用
        }

        private Button CreateBeltButton(string text, Point location, Color backColor)
        {
            var button = new Button
            {
                Text = text,
                Location = location,
                Size = new Size(100, 35),
                BackColor = backColor,
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 12F, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false,
                Cursor = Cursors.Hand
            };

            button.FlatAppearance.BorderSize = 1;
            button.FlatAppearance.BorderColor = ColorTranslator.FromHtml("#4a5661");

            return button;
        }
        #endregion

        #region 业务逻辑集成

        /// <summary>
        /// 初始化业务逻辑
        /// </summary>
        private void InitializeBusinessLogic()
        {
            try
            {
                // 获取电机管理器实例
                _motorManager = DMC1000BMotorManager.Instance;

                // 检查电机管理器是否已初始化
                if (!_motorManager.IsInitialized)
                {
                    LogHelper.Warning("DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作");
                }

                // 订阅电机状态变化事件
                _motorManager.MotorStatusChanged += OnMotorStatusChanged;

                // 初始化电机参数
                _ = InitializeMotorParametersAsync(); // 异步初始化，不等待

                LogHelper.Info("皮带电机控制面板业务逻辑初始化完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("皮带电机控制面板业务逻辑初始化失败", ex);
            }
        }

        /// <summary>
        /// 初始化电机参数 - 从UI控件读取参数，避免硬编码
        /// </summary>
        private async Task InitializeMotorParametersAsync()
        {
            try
            {
                // 从UI控件读取输入皮带电机参数
                var inputParams = GetInputBeltParamsFromUI();
                await _motorManager.SetBeltMotorParamsAsync(INPUT_BELT_AXIS, inputParams);

                // 从UI控件读取输出皮带电机参数
                var outputParams = GetOutputBeltParamsFromUI();
                await _motorManager.SetBeltMotorParamsAsync(OUTPUT_BELT_AXIS, outputParams);

                // 刷新UI显示，确保显示的是实际设置的参数
                RefreshMotorParametersDisplay();

                LogHelper.Info("皮带电机参数初始化完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("皮带电机参数初始化失败", ex);
            }
        }

        /// <summary>
        /// 从UI控件获取输入皮带电机参数
        /// </summary>
        /// <returns>输入皮带电机参数</returns>
        private BeltMotorParams GetInputBeltParamsFromUI()
        {
            return new BeltMotorParams
            {
                MotorName = "输入皮带电机",
                PulseEquivalent = ParseDoubleFromTextBox(_inputPulseEquivalentTextBox, 0.01), // 基于10000 pulse/r的默认值
                MaxSpeed = ParseDoubleFromTextBox(_inputRunSpeedTextBox, 100), // 从运行速度文本框获取
                StartSpeed = ParseDoubleFromTextBox(_inputRunSpeedTextBox, 100) * 0.1, // 起始速度设为运行速度的10%
                Acceleration = ParseDoubleFromTextBox(_inputMaxAccelerationTextBox, 500), // 从加速度文本框获取
                JogDistance = ParseDoubleFromTextBox(_inputJogDistanceTextBox, 10)
            };
        }

        /// <summary>
        /// 从UI控件获取输出皮带电机参数
        /// </summary>
        /// <returns>输出皮带电机参数</returns>
        private BeltMotorParams GetOutputBeltParamsFromUI()
        {
            return new BeltMotorParams
            {
                MotorName = "输出皮带电机",
                PulseEquivalent = ParseDoubleFromTextBox(_outputPulseEquivalentTextBox, 0.01), // 基于10000 pulse/r的默认值
                MaxSpeed = ParseDoubleFromTextBox(_outputRunSpeedTextBox, 100),
                StartSpeed = ParseDoubleFromTextBox(_outputRunSpeedTextBox, 100) * 0.1, // 起始速度设为运行速度的10%
                Acceleration = ParseDoubleFromTextBox(_outputMaxAccelerationTextBox, 500),
                JogDistance = ParseDoubleFromTextBox(_outputJogDistanceTextBox, 10)
            };
        }

        /// <summary>
        /// 安全解析TextBox中的double值
        /// </summary>
        /// <param name="textBox">文本框</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>解析后的值</returns>
        private double ParseDoubleFromTextBox(TextBox textBox, double defaultValue)
        {
            if (textBox == null || string.IsNullOrWhiteSpace(textBox.Text))
                return defaultValue;

            if (double.TryParse(textBox.Text, out double result))
                return result;

            return defaultValue;
        }

        /// <summary>
        /// 更新输入皮带电机参数 - 从UI读取并应用到电机管理器
        /// </summary>
        private async Task UpdateInputBeltParametersAsync()
        {
            try
            {
                var inputParams = GetInputBeltParamsFromUI();
                await _motorManager.SetBeltMotorParamsAsync(INPUT_BELT_AXIS, inputParams);
                LogHelper.Info("输入皮带电机参数更新成功");
            }
            catch (Exception ex)
            {
                LogHelper.Error("输入皮带电机参数更新失败", ex);
                MessageBox.Show($"输入皮带电机参数更新失败: {ex.Message}", "参数更新失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 更新输出皮带电机参数 - 从UI读取并应用到电机管理器
        /// </summary>
        private async Task UpdateOutputBeltParametersAsync()
        {
            try
            {
                var outputParams = GetOutputBeltParamsFromUI();
                await _motorManager.SetBeltMotorParamsAsync(OUTPUT_BELT_AXIS, outputParams);
                LogHelper.Info("输出皮带电机参数更新成功");
            }
            catch (Exception ex)
            {
                LogHelper.Error("输出皮带电机参数更新失败", ex);
                MessageBox.Show($"输出皮带电机参数更新失败: {ex.Message}", "参数更新失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 验证输入参数的有效性
        /// </summary>
        /// <param name="textBox">要验证的文本框</param>
        /// <param name="paramName">参数名称</param>
        /// <param name="minValue">最小值</param>
        /// <param name="maxValue">最大值</param>
        private void ValidateInputParameter(TextBox textBox, string paramName, double minValue, double maxValue)
        {
            if (textBox == null || string.IsNullOrWhiteSpace(textBox.Text))
                return;

            if (!double.TryParse(textBox.Text, out double value))
            {
                MessageBox.Show($"{paramName}必须是有效的数字", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                textBox.Focus();
                return;
            }

            if (value < minValue || value > maxValue)
            {
                MessageBox.Show($"{paramName}必须在{minValue}到{maxValue}之间", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                textBox.Focus();
                return;
            }

            // 参数有效，改变文本框颜色表示验证通过
            textBox.BackColor = ColorTranslator.FromHtml("#d5f4e6"); // 淡绿色表示验证通过

            // 1秒后恢复原色
            var timer = new System.Windows.Forms.Timer();
            timer.Interval = 1000;
            timer.Tick += (s, e) =>
            {
                textBox.BackColor = ColorTranslator.FromHtml("#34495e");
                timer.Stop();
                timer.Dispose();
            };
            timer.Start();
        }

        /// <summary>
        /// 刷新UI显示的电机参数 - 从电机管理器读取当前参数并更新到UI
        /// </summary>
        private void RefreshMotorParametersDisplay()
        {
            try
            {
                // 设置UI更新标志，防止触发参数变化事件
                _isUpdatingUI = true;

                // 获取输入皮带电机参数
                var inputParams = _motorManager.GetBeltMotorParams(INPUT_BELT_AXIS);
                if (inputParams != null)
                {
                    _inputPulseEquivalentTextBox.Text = inputParams.PulseEquivalent.ToString("F3");
                    _inputMaxAccelerationTextBox.Text = inputParams.Acceleration.ToString("F0");
                    _inputRunSpeedTextBox.Text = inputParams.MaxSpeed.ToString("F0");
                    _inputJogDistanceTextBox.Text = inputParams.JogDistance.ToString("F1");
                }

                // 获取输出皮带电机参数
                var outputParams = _motorManager.GetBeltMotorParams(OUTPUT_BELT_AXIS);
                if (outputParams != null)
                {
                    _outputPulseEquivalentTextBox.Text = outputParams.PulseEquivalent.ToString("F3");
                    _outputMaxAccelerationTextBox.Text = outputParams.Acceleration.ToString("F0");
                    _outputRunSpeedTextBox.Text = outputParams.MaxSpeed.ToString("F0");
                    _outputJogDistanceTextBox.Text = outputParams.JogDistance.ToString("F1");
                }

                LogHelper.Info("电机参数显示刷新完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("刷新电机参数显示失败", ex);
            }
            finally
            {
                // 恢复UI更新标志
                _isUpdatingUI = false;
            }
        }

        /// <summary>
        /// 电机状态变化事件处理
        /// </summary>
        private void OnMotorStatusChanged(object sender, MotorStatusEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnMotorStatusChanged(sender, e)));
                return;
            }

            try
            {
                // 根据电机状态更新按钮状态
                if (e.Status.MotorId == INPUT_BELT_AXIS)
                {
                    UpdateBeltButtonStates(_inputPositiveJogButton, _inputNegativeJogButton,
                        _inputContinuousPositiveButton, _inputContinuousNegativeButton,
                        _inputStopButton, e.Status.IsMoving);
                }
                else if (e.Status.MotorId == OUTPUT_BELT_AXIS)
                {
                    UpdateBeltButtonStates(_outputPositiveJogButton, _outputNegativeJogButton,
                        _outputContinuousPositiveButton, _outputContinuousNegativeButton,
                        _outputStopButton, e.Status.IsMoving);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("更新皮带电机状态显示失败", ex);
            }
        }

        /// <summary>
        /// 更新皮带电机按钮状态
        /// </summary>
        private void UpdateBeltButtonStates(Button jogPosBtn, Button jogNegBtn, Button contPosBtn, Button contNegBtn, Button stopBtn, bool isMoving)
        {
            // 运动中时禁用点动和连续运转按钮，启用停止按钮
            jogPosBtn.Enabled = !isMoving;
            jogNegBtn.Enabled = !isMoving;
            contPosBtn.Enabled = !isMoving;
            contNegBtn.Enabled = !isMoving;
            stopBtn.Enabled = isMoving;

            // 更新按钮颜色
            if (isMoving)
            {
                jogPosBtn.BackColor = ColorTranslator.FromHtml("#7f8c8d");
                jogNegBtn.BackColor = ColorTranslator.FromHtml("#7f8c8d");
                contPosBtn.BackColor = ColorTranslator.FromHtml("#7f8c8d");
                contNegBtn.BackColor = ColorTranslator.FromHtml("#7f8c8d");
                stopBtn.BackColor = ColorTranslator.FromHtml("#e74c3c");
            }
            else
            {
                jogPosBtn.BackColor = ColorTranslator.FromHtml("#3498db");
                jogNegBtn.BackColor = ColorTranslator.FromHtml("#3498db");
                contPosBtn.BackColor = ColorTranslator.FromHtml("#27ae60");
                contNegBtn.BackColor = ColorTranslator.FromHtml("#27ae60");
                stopBtn.BackColor = ColorTranslator.FromHtml("#7f8c8d");
            }
        }

        /// <summary>
        /// 释放资源，取消事件订阅
        /// </summary>
        private void CleanupResources()
        {
            try
            {
                // 取消事件订阅
                if (_motorManager != null)
                {
                    _motorManager.MotorStatusChanged -= OnMotorStatusChanged;
                }
                LogHelper.Info("皮带电机控制面板资源释放完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("皮带电机控制面板资源释放失败", ex);
            }
        }

        #endregion

        #region 输入皮带电机事件处理

        /// <summary>
        /// 输入皮带电机点动 - 先更新参数再执行操作
        /// </summary>
        private async Task OnInputBeltJogAsync(bool direction)
        {
            try
            {
                // 先更新电机参数
                await UpdateInputBeltParametersAsync();

                if (!double.TryParse(_inputJogDistanceTextBox.Text, out double jogDistance))
                {
                    MessageBox.Show("请输入有效的点动距离", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (jogDistance <= 0)
                {
                    MessageBox.Show("点动距离必须大于0", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                bool success = await _motorManager.BeltMotorJogAsync(INPUT_BELT_AXIS, direction, jogDistance);
                if (!success)
                {
                    MessageBox.Show("输入皮带电机点动失败", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("输入皮带电机点动失败", ex);
                MessageBox.Show($"输入皮带电机点动失败: {ex.Message}", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 输入皮带电机连续运转 - 先更新参数再执行操作
        /// </summary>
        private async Task OnInputBeltContinuousRunAsync(bool direction)
        {
            try
            {
                // 先更新电机参数
                await UpdateInputBeltParametersAsync();

                bool success = await _motorManager.BeltMotorContinuousRunAsync(INPUT_BELT_AXIS, direction);
                if (!success)
                {
                    MessageBox.Show("输入皮带电机连续运转失败", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("输入皮带电机连续运转失败", ex);
                MessageBox.Show($"输入皮带电机连续运转失败: {ex.Message}", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 输入皮带电机停止
        /// </summary>
        private async Task OnInputBeltStopAsync()
        {
            try
            {
                bool success = await _motorManager.StopMotorAsync(INPUT_BELT_AXIS);
                if (!success)
                {
                    MessageBox.Show("输入皮带电机停止失败", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("输入皮带电机停止失败", ex);
                MessageBox.Show($"输入皮带电机停止失败: {ex.Message}", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 输出皮带电机事件处理

        /// <summary>
        /// 输出皮带电机点动 - 先更新参数再执行操作
        /// </summary>
        private async Task OnOutputBeltJogAsync(bool direction)
        {
            try
            {
                // 先更新电机参数
                await UpdateOutputBeltParametersAsync();

                if (!double.TryParse(_outputJogDistanceTextBox.Text, out double jogDistance))
                {
                    MessageBox.Show("请输入有效的点动距离", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (jogDistance <= 0)
                {
                    MessageBox.Show("点动距离必须大于0", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                bool success = await _motorManager.BeltMotorJogAsync(OUTPUT_BELT_AXIS, direction, jogDistance);
                if (!success)
                {
                    MessageBox.Show("输出皮带电机点动失败", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("输出皮带电机点动失败", ex);
                MessageBox.Show($"输出皮带电机点动失败: {ex.Message}", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 输出皮带电机连续运转 - 先更新参数再执行操作
        /// </summary>
        private async Task OnOutputBeltContinuousRunAsync(bool direction)
        {
            try
            {
                // 先更新电机参数
                await UpdateOutputBeltParametersAsync();

                bool success = await _motorManager.BeltMotorContinuousRunAsync(OUTPUT_BELT_AXIS, direction);
                if (!success)
                {
                    MessageBox.Show("输出皮带电机连续运转失败", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("输出皮带电机连续运转失败", ex);
                MessageBox.Show($"输出皮带电机连续运转失败: {ex.Message}", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 输出皮带电机停止
        /// </summary>
        private async Task OnOutputBeltStopAsync()
        {
            try
            {
                bool success = await _motorManager.StopMotorAsync(OUTPUT_BELT_AXIS);
                if (!success)
                {
                    MessageBox.Show("输出皮带电机停止失败", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("输出皮带电机停止失败", ex);
                MessageBox.Show($"输出皮带电机停止失败: {ex.Message}", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 参数动态更新

        /// <summary>
        /// 输入皮带电机参数变化处理
        /// </summary>
        private async void OnInputParameterChanged()
        {
            try
            {
                // 防止在UI更新过程中触发事件
                if (_isUpdatingUI) return;

                // 从UI获取参数值
                double pulseEquivalent = ParseDoubleFromTextBox(_inputPulseEquivalentTextBox, 0.01);
                double maxAcceleration = ParseDoubleFromTextBox(_inputMaxAccelerationTextBox, 500);
                double runSpeed = ParseDoubleFromTextBox(_inputRunSpeedTextBox, 100);
                double jogDistance = ParseDoubleFromTextBox(_inputJogDistanceTextBox, 10);

                // 创建新的电机参数
                var inputParams = new BeltMotorParams
                {
                    MotorName = "输入皮带电机",
                    PulseEquivalent = pulseEquivalent,
                    MaxSpeed = runSpeed,
                    StartSpeed = runSpeed * 0.1,       // 起始速度设为运行速度的10%
                    Acceleration = maxAcceleration,
                    JogDistance = jogDistance
                };

                // 更新电机参数
                await _motorManager.SetBeltMotorParamsAsync(INPUT_BELT_AXIS, inputParams);
                LogHelper.Info($"输入皮带电机参数已更新: 脉冲当量={pulseEquivalent}, 最大速度={runSpeed}, 加速度={maxAcceleration}, 点动距离={jogDistance}");
            }
            catch (Exception ex)
            {
                LogHelper.Error($"更新输入皮带电机参数失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 输出皮带电机参数变化处理
        /// </summary>
        private async void OnOutputParameterChanged()
        {
            try
            {
                // 防止在UI更新过程中触发事件
                if (_isUpdatingUI) return;

                // 从UI获取参数值
                double pulseEquivalent = ParseDoubleFromTextBox(_outputPulseEquivalentTextBox, 0.01);
                double maxAcceleration = ParseDoubleFromTextBox(_outputMaxAccelerationTextBox, 500);
                double runSpeed = ParseDoubleFromTextBox(_outputRunSpeedTextBox, 100);
                double jogDistance = ParseDoubleFromTextBox(_outputJogDistanceTextBox, 10);

                // 创建新的电机参数
                var outputParams = new BeltMotorParams
                {
                    MotorName = "输出皮带电机",
                    PulseEquivalent = pulseEquivalent,
                    MaxSpeed = runSpeed,
                    StartSpeed = runSpeed * 0.1,       // 起始速度设为运行速度的10%
                    Acceleration = maxAcceleration,
                    JogDistance = jogDistance
                };

                // 更新电机参数
                await _motorManager.SetBeltMotorParamsAsync(OUTPUT_BELT_AXIS, outputParams);
                LogHelper.Info($"输出皮带电机参数已更新: 脉冲当量={pulseEquivalent}, 最大速度={runSpeed}, 加速度={maxAcceleration}, 点动距离={jogDistance}");
            }
            catch (Exception ex)
            {
                LogHelper.Error($"更新输出皮带电机参数失败: {ex.Message}");
            }
        }

        #endregion
    }
}