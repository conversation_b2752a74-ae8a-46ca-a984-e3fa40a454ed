# MyHMI 上位机控制系统 - 开发环境配置

## 系统要求

### 操作系统要求
- **Windows 10** 或更高版本（推荐 Windows 11）
- **Windows Server 2016** 或更高版本（服务器环境）
- 支持 64 位和 32 位系统

### 硬件要求
- **CPU**: Intel Core i5 或 AMD 同等性能处理器
- **内存**: 最少 8GB RAM（推荐 16GB 或更多）
- **硬盘**: 至少 10GB 可用空间
- **显示器**: 1920x1080 分辨率或更高

## 开发工具安装

### 1. Visual Studio 安装

#### 推荐版本
- **Visual Studio 2019 Professional** 或更高版本
- **Visual Studio 2022 Community/Professional** （推荐）

#### 必需工作负载
安装 Visual Studio 时，请确保选择以下工作负载：

```
☑ .NET 桌面开发
☑ 使用 C++ 的桌面开发（如需要调用 C++ 库）
☑ Visual Studio 扩展开发（可选）
```

#### 必需组件
```
☑ .NET Framework 4.8 开发工具
☑ .NET Framework 4.8 目标包
☑ Windows 10/11 SDK
☑ MSBuild
☑ NuGet 包管理器
☑ Git for Windows（版本控制）
```

### 2. .NET Framework 4.8 安装

如果系统未安装 .NET Framework 4.8：

1. 下载 .NET Framework 4.8 Runtime
   ```
   https://dotnet.microsoft.com/download/dotnet-framework/net48
   ```

2. 下载 .NET Framework 4.8 Developer Pack
   ```
   https://dotnet.microsoft.com/download/dotnet-framework/thank-you/net48-developer-pack-offline-installer
   ```

3. 按顺序安装 Runtime 和 Developer Pack

### 3. Git 版本控制工具

#### 安装 Git for Windows
```
下载地址: https://git-scm.com/download/win
```

#### 配置 Git
```bash
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
git config --global core.autocrlf true
```

## 项目环境配置

### 1. 克隆项目
```bash
git clone <repository-url>
cd MyHMI
```

### 2. NuGet 包还原

#### 方法一：Visual Studio 自动还原
1. 打开 `MyHMI.sln`
2. Visual Studio 会自动提示还原 NuGet 包
3. 点击"还原"按钮

#### 方法二：命令行还原
```bash
# 在项目根目录执行
nuget restore MyHMI.sln
```

#### 方法三：Package Manager Console
```powershell
# 在 Visual Studio 中打开 Package Manager Console
Update-Package -reinstall
```

### 3. 依赖包说明

项目使用的主要 NuGet 包：

```xml
<!-- packages.config -->
<packages>
  <package id="NLog" version="5.2.8" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="NModbus4" version="2.1.0" targetFramework="net48" />
  <package id="EPPlus" version="4.5.3.3" targetFramework="net48" />
  <package id="CsvHelper" version="30.0.1" targetFramework="net48" />
</packages>
```

**包功能说明**：
- **NLog**: 日志记录框架
- **Newtonsoft.Json**: JSON 序列化/反序列化
- **NModbus4**: Modbus 通信协议支持
- **EPPlus**: Excel 文件操作
- **CsvHelper**: CSV 文件操作

## 编译配置

### 1. 编译配置选项

#### Debug 配置（开发调试）
```xml
<PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
  <DebugSymbols>true</DebugSymbols>
  <DebugType>full</DebugType>
  <Optimize>false</Optimize>
  <DefineConstants>DEBUG;TRACE</DefineConstants>
</PropertyGroup>
```

#### Release 配置（发布部署）
```xml
<PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
  <DebugType>pdbonly</DebugType>
  <Optimize>true</Optimize>
  <DefineConstants>TRACE</DefineConstants>
</PropertyGroup>
```

### 2. 编译命令

#### Visual Studio 编译
```
菜单: 生成 → 生成解决方案 (Ctrl+Shift+B)
菜单: 生成 → 重新生成解决方案
```

#### MSBuild 命令行编译
```bash
# Debug 版本
msbuild MyHMI.sln /p:Configuration=Debug /p:Platform="Any CPU"

# Release 版本
msbuild MyHMI.sln /p:Configuration=Release /p:Platform="Any CPU"

# 清理编译
msbuild MyHMI.sln /t:Clean

# 重新编译
msbuild MyHMI.sln /t:Rebuild
```

#### 使用 Visual Studio Developer Command Prompt
```bash
# 打开 Developer Command Prompt
# 导航到项目目录
cd "C:\path\to\MyHMI"

# 编译项目
msbuild MyHMI.sln
```

## 调试配置

### 1. 调试设置

#### 启动项目设置
1. 右键点击 `MyHMI` 项目
2. 选择"设为启动项目"
3. 确保输出类型为"Windows 应用程序"

#### 调试配置
```xml
<PropertyGroup>
  <OutputType>WinExe</OutputType>
  <StartupObject>MyHMI.Program</StartupObject>
</PropertyGroup>
```

### 2. 断点调试
- 在代码行左侧点击设置断点
- 按 F5 开始调试
- 使用 F10（逐过程）和 F11（逐语句）调试

### 3. 日志调试
```csharp
// 在代码中添加日志
LogHelper.Debug("调试信息");
LogHelper.Info("运行信息");
LogHelper.Warning("警告信息");
LogHelper.Error("错误信息");
```

## 开发工具推荐

### 1. Visual Studio 扩展
```
☑ ReSharper（代码分析和重构）
☑ Visual Studio IntelliCode（AI 辅助编程）
☑ GitLens（Git 增强）
☑ Productivity Power Tools（生产力工具）
☑ CodeMaid（代码清理）
```

### 2. 外部工具
```
☑ Beyond Compare（文件比较）
☑ Notepad++（文本编辑）
☑ Postman（API 测试）
☑ WinSCP（文件传输）
☑ Process Monitor（进程监控）
```

### 3. 数据库工具（如需要）
```
☑ SQL Server Management Studio
☑ SQLite Browser
☑ Navicat（多数据库支持）
```

## 环境变量配置

### 1. 系统环境变量
```
MSBUILD_PATH = C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin
PATH = %PATH%;%MSBUILD_PATH%
```

### 2. 项目环境变量
```
MYHMI_CONFIG_PATH = .\Config\SystemConfig.json
MYHMI_LOG_PATH = .\Logs
MYHMI_DATA_PATH = .\Data
```

## 常见问题解决

### 1. NuGet 包还原失败
```bash
# 清理 NuGet 缓存
nuget locals all -clear

# 重新还原包
nuget restore MyHMI.sln
```

### 2. 编译错误：找不到引用
```
解决方案：
1. 检查 packages.config 文件
2. 重新安装 NuGet 包
3. 检查项目引用路径
```

### 3. 运行时错误：缺少 DLL
```
解决方案：
1. 确保所有依赖 DLL 在输出目录
2. 检查 .NET Framework 版本
3. 重新编译项目
```

### 4. 调试时无法命中断点
```
解决方案：
1. 确保使用 Debug 配置
2. 检查 PDB 文件是否生成
3. 清理并重新编译项目
```

## 性能优化建议

### 1. 编译优化
- Release 模式下启用代码优化
- 移除不必要的调试信息
- 使用 AnyCPU 平台配置

### 2. 开发环境优化
- 使用 SSD 硬盘存储项目
- 增加系统内存
- 关闭不必要的 Visual Studio 扩展

### 3. 项目结构优化
- 合理组织项目文件
- 避免深层嵌套目录
- 定期清理临时文件

## 团队开发配置

### 1. 代码规范
```csharp
// 使用统一的代码格式化规则
// 配置 .editorconfig 文件
```

### 2. 版本控制
```bash
# 配置 .gitignore 文件
bin/
obj/
packages/
*.user
*.suo
```

### 3. 持续集成
- 配置自动化构建
- 设置代码质量检查
- 实现自动化测试

按照以上配置完成开发环境搭建后，即可开始 MyHMI 项目的开发工作。
