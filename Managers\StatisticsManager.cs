using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using Newtonsoft.Json;
using MyHMI.Models;
using MyHMI.Helpers;

namespace MyHMI.Managers
{
    /// <summary>
    /// 生产统计管理器
    /// 负责记录和导出生产数据，提供内存缓存和文件持久化功能
    /// </summary>
    public class StatisticsManager
    {
        #region 单例模式
        private static readonly Lazy<StatisticsManager> _instance = new Lazy<StatisticsManager>(() => new StatisticsManager());
        public static StatisticsManager Instance => _instance.Value;
        private StatisticsManager() { }
        #endregion

        #region 私有字段
        private bool _isInitialized = false;
        private readonly object _lockObject = new object();
        
        // 数据缓存
        private List<ProductionRecord> _productionRecords = new List<ProductionRecord>();
        private Dictionary<string, ProductionStatistics> _dailyStatistics = new Dictionary<string, ProductionStatistics>();
        private Dictionary<string, ProductionStatistics> _monthlyStatistics = new Dictionary<string, ProductionStatistics>();
        
        // 配置参数
        private string _dataDirectory = "";
        private int _maxCacheSize = 10000;
        private int _autoSaveIntervalMinutes = 5;
        private bool _enableAutoSave = true;
        
        // 自动保存任务
        private Task _autoSaveTask;
        private CancellationTokenSource _cancellationTokenSource;
        #endregion

        #region 公共属性
        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// 当前缓存的记录数量
        /// </summary>
        public int CachedRecordCount
        {
            get
            {
                lock (_lockObject)
                {
                    return _productionRecords.Count;
                }
            }
        }

        /// <summary>
        /// 数据存储目录
        /// </summary>
        public string DataDirectory => _dataDirectory;
        #endregion

        #region 初始化和释放
        /// <summary>
        /// 异步初始化统计管理器
        /// </summary>
        /// <returns></returns>
        public async Task<bool> InitializeAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_isInitialized)
                {
                    LogHelper.Warning("StatisticsManager已经初始化，跳过重复初始化");
                    return true;
                }

                LogHelper.Info("开始初始化StatisticsManager...");

                // 加载配置
                LoadConfiguration();

                // 创建数据目录
                await CreateDataDirectoryAsync();

                // 加载历史数据
                await LoadHistoricalDataAsync();

                // 启动自动保存任务
                if (_enableAutoSave)
                {
                    StartAutoSaveTask();
                }

                _isInitialized = true;
                LogHelper.Info("StatisticsManager初始化完成");
                return true;

            }, false, "StatisticsManager初始化");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public async Task DisposeAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info("开始释放StatisticsManager资源...");

                // 停止自动保存任务
                await StopAutoSaveTaskAsync();

                // 保存所有数据
                await SaveAllDataAsync();

                _isInitialized = false;
                LogHelper.Info("StatisticsManager资源释放完成");

                return true;
            }, false, "StatisticsManager资源释放");
        }
        #endregion

        #region 数据记录
        /// <summary>
        /// 记录生产数据
        /// </summary>
        /// <param name="record">生产记录</param>
        /// <returns></returns>
        public async Task<bool> RecordProductionDataAsync(ProductionRecord record)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                await Task.CompletedTask; // 消除CS1998警告
                if (!_isInitialized)
                    throw new InvalidOperationException("StatisticsManager未初始化");

                if (record == null)
                    throw new ArgumentNullException(nameof(record));

                // 设置记录时间
                if (record.Timestamp == default)
                {
                    record.Timestamp = DateTime.Now;
                }

                lock (_lockObject)
                {
                    // 添加到缓存
                    _productionRecords.Add(record);

                    // 更新统计数据
                    UpdateStatistics(record);

                    // 检查缓存大小
                    if (_productionRecords.Count > _maxCacheSize)
                    {
                        // 移除最旧的记录
                        int removeCount = _productionRecords.Count - _maxCacheSize;
                        _productionRecords.RemoveRange(0, removeCount);
                        LogHelper.Debug($"缓存已满，移除了{removeCount}条旧记录");
                    }
                }

                LogHelper.Debug($"记录生产数据: 产品ID={record.ProductId}, 结果={record.IsSuccess}");
                return true;

            }, false, $"记录生产数据({record?.ProductId})");
        }

        /// <summary>
        /// 批量记录生产数据
        /// </summary>
        /// <param name="records">生产记录列表</param>
        /// <returns></returns>
        public async Task<bool> RecordProductionDataBatchAsync(IEnumerable<ProductionRecord> records)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (!_isInitialized)
                    throw new InvalidOperationException("StatisticsManager未初始化");

                if (records == null)
                    throw new ArgumentNullException(nameof(records));

                var recordList = records.ToList();
                if (recordList.Count == 0)
                    return true;

                await Task.Run(() =>
                {
                    lock (_lockObject)
                    {
                        foreach (var record in recordList)
                        {
                            if (record.Timestamp == default)
                            {
                                record.Timestamp = DateTime.Now;
                            }

                            _productionRecords.Add(record);
                            UpdateStatistics(record);
                        }

                        // 检查缓存大小
                        if (_productionRecords.Count > _maxCacheSize)
                        {
                            int removeCount = _productionRecords.Count - _maxCacheSize;
                            _productionRecords.RemoveRange(0, removeCount);
                            LogHelper.Debug($"缓存已满，移除了{removeCount}条旧记录");
                        }
                    }
                });

                LogHelper.Info($"批量记录生产数据: {recordList.Count}条记录");
                return true;

            }, false, $"批量记录生产数据({records?.Count()}条)");
        }
        #endregion

        #region 数据查询
        /// <summary>
        /// 获取指定日期的生产统计
        /// </summary>
        /// <param name="date">日期</param>
        /// <returns>生产统计</returns>
        public ProductionStatistics GetDailyStatistics(DateTime date)
        {
            string dateKey = date.ToString("yyyy-MM-dd");
            
            lock (_lockObject)
            {
                if (_dailyStatistics.TryGetValue(dateKey, out var statistics))
                {
                    return statistics.Clone();
                }
            }

            return new ProductionStatistics { Date = date };
        }

        /// <summary>
        /// 获取指定月份的生产统计
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="month">月份</param>
        /// <returns>生产统计</returns>
        public ProductionStatistics GetMonthlyStatistics(int year, int month)
        {
            string monthKey = $"{year:D4}-{month:D2}";
            
            lock (_lockObject)
            {
                if (_monthlyStatistics.TryGetValue(monthKey, out var statistics))
                {
                    return statistics.Clone();
                }
            }

            return new ProductionStatistics { Date = new DateTime(year, month, 1) };
        }

        /// <summary>
        /// 获取指定时间范围内的生产记录
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>生产记录列表</returns>
        public List<ProductionRecord> GetProductionRecords(DateTime startTime, DateTime endTime)
        {
            lock (_lockObject)
            {
                return _productionRecords
                    .Where(r => r.Timestamp >= startTime && r.Timestamp <= endTime)
                    .OrderBy(r => r.Timestamp)
                    .ToList();
            }
        }

        /// <summary>
        /// 获取最近的生产记录
        /// </summary>
        /// <param name="count">记录数量</param>
        /// <returns>生产记录列表</returns>
        public List<ProductionRecord> GetRecentProductionRecords(int count = 100)
        {
            lock (_lockObject)
            {
                return _productionRecords
                    .OrderByDescending(r => r.Timestamp)
                    .Take(count)
                    .ToList();
            }
        }

        /// <summary>
        /// 获取今日统计摘要
        /// </summary>
        /// <returns>统计摘要</returns>
        public (int Total, int Success, int Failed, double SuccessRate, TimeSpan TotalTime) GetTodaysSummary()
        {
            var today = DateTime.Today;
            var todayRecords = GetProductionRecords(today, today.AddDays(1));

            int total = todayRecords.Count;
            int success = todayRecords.Count(r => r.IsSuccess);
            int failed = total - success;
            double successRate = total > 0 ? (double)success / total * 100 : 0;
            
            var totalTime = TimeSpan.FromMilliseconds(todayRecords.Sum(r => r.ProcessingTimeMs));

            return (total, success, failed, successRate, totalTime);
        }
        #endregion

        #region 数据导出
        /// <summary>
        /// 导出生产数据到CSV文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>是否成功</returns>
        public async Task<bool> ExportToCsvAsync(string filePath, DateTime startTime, DateTime endTime)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                var records = GetProductionRecords(startTime, endTime);

                await Task.Run(() =>
                {
                    using (var writer = new StreamWriter(filePath, false, System.Text.Encoding.UTF8))
                    {
                        // 写入标题行
                        writer.WriteLine("时间,产品ID,结果,处理时间(ms),X坐标,Y坐标,角度,置信度,错误信息");

                        // 写入数据行
                        foreach (var record in records)
                        {
                            writer.WriteLine($"{record.Timestamp:yyyy-MM-dd HH:mm:ss.fff}," +
                                           $"{record.ProductId}," +
                                           $"{(record.IsSuccess ? "成功" : "失败")}," +
                                           $"{record.ProcessingTimeMs:F1}," +
                                           $"{record.VisionResult?.X:F3}," +
                                           $"{record.VisionResult?.Y:F3}," +
                                           $"{record.VisionResult?.Angle:F3}," +
                                           $"{record.VisionResult?.Confidence:F3}," +
                                           $"\"{record.ErrorMessage}\"");
                        }
                    }
                });

                LogHelper.Info($"导出生产数据到CSV: {filePath}, 记录数: {records.Count}");
                return true;

            }, false, $"导出CSV({filePath})");
        }

        /// <summary>
        /// 导出统计报告到JSON文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="year">年份</param>
        /// <param name="month">月份（可选，0表示整年）</param>
        /// <returns>是否成功</returns>
        public async Task<bool> ExportStatisticsReportAsync(string filePath, int year, int month = 0)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                var report = new
                {
                    GeneratedTime = DateTime.Now,
                    Year = year,
                    Month = month,
                    DailyStatistics = month == 0 ?
                        GetYearlyDailyStatistics(year) :
                        GetMonthlyDailyStatistics(year, month),
                    MonthlyStatistics = month == 0 ?
                        GetYearlyMonthlyStatistics(year).ToArray() :
                        new[] { GetMonthlyStatistics(year, month) }
                };

                await Task.Run(() =>
                {
                    string json = JsonConvert.SerializeObject(report, Formatting.Indented);
                    File.WriteAllText(filePath, json, System.Text.Encoding.UTF8);
                });

                LogHelper.Info($"导出统计报告: {filePath}");
                return true;

            }, false, $"导出统计报告({filePath})");
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfiguration()
        {
            var statisticsSettings = Settings.Settings.Current.Statistics;
            _dataDirectory = statisticsSettings.ExportPath;
            _maxCacheSize = statisticsSettings.CacheSize;
            _autoSaveIntervalMinutes = statisticsSettings.ExportInterval;
            _enableAutoSave = statisticsSettings.AutoExport;

            LogHelper.Info($"统计管理器配置: 数据目录={_dataDirectory}, 缓存大小={_maxCacheSize}, 自动保存间隔={_autoSaveIntervalMinutes}分钟");
        }

        /// <summary>
        /// 创建数据目录
        /// </summary>
        /// <returns></returns>
        private async Task CreateDataDirectoryAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    if (!Directory.Exists(_dataDirectory))
                    {
                        Directory.CreateDirectory(_dataDirectory);
                        LogHelper.Info($"创建数据目录: {_dataDirectory}");
                    }

                    // 创建子目录
                    string dailyDir = Path.Combine(_dataDirectory, "Daily");
                    string monthlyDir = Path.Combine(_dataDirectory, "Monthly");
                    string recordsDir = Path.Combine(_dataDirectory, "Records");

                    foreach (string dir in new[] { dailyDir, monthlyDir, recordsDir })
                    {
                        if (!Directory.Exists(dir))
                        {
                            Directory.CreateDirectory(dir);
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Error("创建数据目录失败", ex);
                    throw;
                }
            });
        }

        /// <summary>
        /// 加载历史数据
        /// </summary>
        /// <returns></returns>
        private async Task LoadHistoricalDataAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    // 加载今日的生产记录
                    LoadTodaysRecords();

                    // 加载统计数据
                    LoadStatisticsData();

                    LogHelper.Info("历史数据加载完成");
                }
                catch (Exception ex)
                {
                    LogHelper.Error("加载历史数据失败", ex);
                }
            });
        }

        /// <summary>
        /// 加载今日记录
        /// </summary>
        private void LoadTodaysRecords()
        {
            try
            {
                string todayFile = Path.Combine(_dataDirectory, "Records", $"{DateTime.Today:yyyy-MM-dd}.json");

                if (File.Exists(todayFile))
                {
                    string json = File.ReadAllText(todayFile, System.Text.Encoding.UTF8);
                    var records = JsonConvert.DeserializeObject<List<ProductionRecord>>(json);

                    if (records != null)
                    {
                        lock (_lockObject)
                        {
                            _productionRecords.AddRange(records);
                        }

                        LogHelper.Info($"加载今日记录: {records.Count}条");
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("加载今日记录失败", ex);
            }
        }

        /// <summary>
        /// 加载统计数据
        /// </summary>
        private void LoadStatisticsData()
        {
            try
            {
                // 加载日统计数据
                string dailyDir = Path.Combine(_dataDirectory, "Daily");
                if (Directory.Exists(dailyDir))
                {
                    foreach (string file in Directory.GetFiles(dailyDir, "*.json"))
                    {
                        try
                        {
                            string json = File.ReadAllText(file, System.Text.Encoding.UTF8);
                            var statistics = JsonConvert.DeserializeObject<ProductionStatistics>(json);

                            if (statistics != null)
                            {
                                string dateKey = statistics.Date.ToString("yyyy-MM-dd");
                                lock (_lockObject)
                                {
                                    _dailyStatistics[dateKey] = statistics;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            LogHelper.Error($"加载日统计文件失败: {file}", ex);
                        }
                    }
                }

                // 加载月统计数据
                string monthlyDir = Path.Combine(_dataDirectory, "Monthly");
                if (Directory.Exists(monthlyDir))
                {
                    foreach (string file in Directory.GetFiles(monthlyDir, "*.json"))
                    {
                        try
                        {
                            string json = File.ReadAllText(file, System.Text.Encoding.UTF8);
                            var statistics = JsonConvert.DeserializeObject<ProductionStatistics>(json);

                            if (statistics != null)
                            {
                                string monthKey = statistics.Date.ToString("yyyy-MM");
                                lock (_lockObject)
                                {
                                    _monthlyStatistics[monthKey] = statistics;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            LogHelper.Error($"加载月统计文件失败: {file}", ex);
                        }
                    }
                }

                LogHelper.Info($"加载统计数据: 日统计{_dailyStatistics.Count}条, 月统计{_monthlyStatistics.Count}条");
            }
            catch (Exception ex)
            {
                LogHelper.Error("加载统计数据失败", ex);
            }
        }

        /// <summary>
        /// 更新统计数据
        /// </summary>
        /// <param name="record">生产记录</param>
        private void UpdateStatistics(ProductionRecord record)
        {
            // 更新日统计
            string dateKey = record.Timestamp.ToString("yyyy-MM-dd");
            if (!_dailyStatistics.TryGetValue(dateKey, out var dailyStats))
            {
                dailyStats = new ProductionStatistics { Date = record.Timestamp.Date };
                _dailyStatistics[dateKey] = dailyStats;
            }
            dailyStats.UpdateWithRecord(record);

            // 更新月统计
            string monthKey = record.Timestamp.ToString("yyyy-MM");
            if (!_monthlyStatistics.TryGetValue(monthKey, out var monthlyStats))
            {
                monthlyStats = new ProductionStatistics { Date = new DateTime(record.Timestamp.Year, record.Timestamp.Month, 1) };
                _monthlyStatistics[monthKey] = monthlyStats;
            }
            monthlyStats.UpdateWithRecord(record);
        }

        /// <summary>
        /// 启动自动保存任务
        /// </summary>
        private void StartAutoSaveTask()
        {
            _cancellationTokenSource = new CancellationTokenSource();
            _autoSaveTask = Task.Run(async () => await AutoSaveLoopAsync(_cancellationTokenSource.Token));
            LogHelper.Info("自动保存任务已启动");
        }

        /// <summary>
        /// 停止自动保存任务
        /// </summary>
        /// <returns></returns>
        private async Task StopAutoSaveTaskAsync()
        {
            try
            {
                _cancellationTokenSource?.Cancel();

                if (_autoSaveTask != null)
                {
                    await _autoSaveTask;
                }

                LogHelper.Info("自动保存任务已停止");
            }
            catch (Exception ex)
            {
                LogHelper.Error("停止自动保存任务失败", ex);
            }
        }

        /// <summary>
        /// 自动保存循环
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        private async Task AutoSaveLoopAsync(CancellationToken cancellationToken)
        {
            LogHelper.Info("自动保存循环开始");

            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    await Task.Delay(TimeSpan.FromMinutes(_autoSaveIntervalMinutes), cancellationToken);

                    if (!cancellationToken.IsCancellationRequested)
                    {
                        await SaveAllDataAsync();
                    }
                }
            }
            catch (OperationCanceledException)
            {
                LogHelper.Info("自动保存循环被取消");
            }
            catch (Exception ex)
            {
                LogHelper.Error("自动保存循环异常", ex);
            }

            LogHelper.Info("自动保存循环结束");
        }

        /// <summary>
        /// 保存所有数据
        /// </summary>
        /// <returns></returns>
        private async Task SaveAllDataAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                await Task.Run(() =>
                {
                    // 保存今日记录
                    SaveTodaysRecords();

                    // 保存统计数据
                    SaveStatisticsData();
                });

                LogHelper.Debug("所有数据保存完成");

                return true;
            }, false, "保存所有数据");
        }

        /// <summary>
        /// 保存今日记录
        /// </summary>
        private void SaveTodaysRecords()
        {
            try
            {
                var todayRecords = GetProductionRecords(DateTime.Today, DateTime.Today.AddDays(1));

                if (todayRecords.Count > 0)
                {
                    string todayFile = Path.Combine(_dataDirectory, "Records", $"{DateTime.Today:yyyy-MM-dd}.json");
                    string json = JsonConvert.SerializeObject(todayRecords, Formatting.Indented);
                    File.WriteAllText(todayFile, json, System.Text.Encoding.UTF8);

                    LogHelper.Debug($"保存今日记录: {todayRecords.Count}条");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("保存今日记录失败", ex);
            }
        }

        /// <summary>
        /// 保存统计数据
        /// </summary>
        private void SaveStatisticsData()
        {
            try
            {
                lock (_lockObject)
                {
                    // 保存日统计
                    foreach (var kvp in _dailyStatistics)
                    {
                        try
                        {
                            string dailyFile = Path.Combine(_dataDirectory, "Daily", $"{kvp.Key}.json");
                            string json = JsonConvert.SerializeObject(kvp.Value, Formatting.Indented);
                            File.WriteAllText(dailyFile, json, System.Text.Encoding.UTF8);
                        }
                        catch (Exception ex)
                        {
                            LogHelper.Error($"保存日统计失败: {kvp.Key}", ex);
                        }
                    }

                    // 保存月统计
                    foreach (var kvp in _monthlyStatistics)
                    {
                        try
                        {
                            string monthlyFile = Path.Combine(_dataDirectory, "Monthly", $"{kvp.Key}.json");
                            string json = JsonConvert.SerializeObject(kvp.Value, Formatting.Indented);
                            File.WriteAllText(monthlyFile, json, System.Text.Encoding.UTF8);
                        }
                        catch (Exception ex)
                        {
                            LogHelper.Error($"保存月统计失败: {kvp.Key}", ex);
                        }
                    }
                }

                LogHelper.Debug("统计数据保存完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("保存统计数据失败", ex);
            }
        }

        /// <summary>
        /// 获取年度日统计数据
        /// </summary>
        /// <param name="year">年份</param>
        /// <returns>日统计数据列表</returns>
        private List<ProductionStatistics> GetYearlyDailyStatistics(int year)
        {
            lock (_lockObject)
            {
                return _dailyStatistics
                    .Where(kvp => kvp.Value.Date.Year == year)
                    .Select(kvp => kvp.Value.Clone())
                    .OrderBy(s => s.Date)
                    .ToList();
            }
        }

        /// <summary>
        /// 获取月度日统计数据
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="month">月份</param>
        /// <returns>日统计数据列表</returns>
        private List<ProductionStatistics> GetMonthlyDailyStatistics(int year, int month)
        {
            lock (_lockObject)
            {
                return _dailyStatistics
                    .Where(kvp => kvp.Value.Date.Year == year && kvp.Value.Date.Month == month)
                    .Select(kvp => kvp.Value.Clone())
                    .OrderBy(s => s.Date)
                    .ToList();
            }
        }

        /// <summary>
        /// 获取年度月统计数据
        /// </summary>
        /// <param name="year">年份</param>
        /// <returns>月统计数据列表</returns>
        private List<ProductionStatistics> GetYearlyMonthlyStatistics(int year)
        {
            lock (_lockObject)
            {
                return _monthlyStatistics
                    .Where(kvp => kvp.Value.Date.Year == year)
                    .Select(kvp => kvp.Value.Clone())
                    .OrderBy(s => s.Date)
                    .ToList();
            }
        }
        #endregion
    }
}
