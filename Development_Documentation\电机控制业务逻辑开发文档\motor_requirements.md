# 电机控制业务逻辑开发 - Requirements Document

基于DMC1000B运动控制卡，为左右翻转电机和两个皮带电机开发完整的业务逻辑控制功能，包括参数设置、运动控制、状态监控和UI集成。涉及4个步进电机轴：轴0(左翻转电机)、轴1(右翻转电机)、轴2(输出皮带电机)、轴3(输入皮带电机)。

## Core Features

### 1. DMC1000B运动控制卡集成
- 封装DMC1000B运动控制卡的底层API函数
- 实现控制卡的初始化、配置和资源释放
- 提供统一的电机控制接口，支持4轴步进电机控制

### 2. 翻转电机控制（轴0和轴1）
- **角度控制模式**：支持角度单位的参数设置和运动控制
- **参数配置**：脉冲当量(pulse/°)、最大加速度(°/s²)、运行速度(°/s)
- **位置控制**：当前角度显示、目标角度设置、绝对位置运动
- **基本控制**：正转、反转、回原点操作
- **示教功能**：
  - 回原点操作：电机能够自动回到原点位置（0°位置）
  - 点动示教：通过点动控制分别移动到3个不同的目标位置
  - 位置保存：通过控件保存当前位置相对于原点的角度值（位置1、位置2、位置3）
  - 位置移动：通过控件快速移动到已保存的相对位置
  - 参数一致性：示教移动时使用与运行参数一致的速度和加速度设置
  - 相对位置计算：所有保存的位置都是相对于原点的角度值，移动时自动计算到目标相对位置

### 3. 皮带电机控制（轴2和轴3）
- **线性控制模式**：支持毫米单位的参数设置和运动控制
- **参数配置**：脉冲当量(pulse/mm)、最大加速度(mm/s²)、运行速度(mm/s)
- **点动控制**：正转点动、反转点动，可设置点动距离
- **连续运转**：连续正转、连续反转、停止控制
- **双电机独立控制**：输入皮带电机和输出皮带电机独立参数和控制

### 4. 状态监控和反馈
- **实时状态监控**：电机位置、速度、运行状态的实时更新
- **原点检测**：翻转电机的原点开关状态监控
- **运动完成检测**：运动指令执行完成的状态反馈
- **异常处理**：电机报警、限位触发等异常状态处理

### 5. UI界面集成
- **现有控件增强**：为MotorFlipPanel和MotorBeltPanel添加业务逻辑
- **参数设置界面**：用户友好的参数配置界面
- **实时状态显示**：电机状态的可视化显示
- **操作按钮响应**：所有控制按钮的业务逻辑实现

## User Stories

### 操作员用户故事
- 作为设备操作员，我希望能够设置翻转电机的角度参数，以便精确控制产品翻转角度
- 作为设备操作员，我希望能够通过示教功能保存常用的翻转位置，以便快速切换到预设位置
- 作为设备操作员，我希望能够通过点动控制精确调整翻转电机到目标位置，然后保存该位置
- 作为设备操作员，我希望保存的位置是相对于原点的角度值，移动时能够准确到达相对位置
- 作为设备操作员，我希望能够控制皮带电机的点动和连续运行，以便调试传送带位置
- 作为设备操作员，我希望能够实时查看电机的当前位置和状态，以便监控设备运行情况
- 作为设备操作员，我希望电机能够自动回到原点位置，以便重新校准设备

### 维护工程师用户故事
- 作为维护工程师，我希望能够调整电机的速度和加速度参数，以便优化设备性能
- 作为维护工程师，我希望示教移动时使用与运行参数一致的速度设置，确保运动一致性
- 作为维护工程师，我希望系统能够检测和报告电机异常状态，以便及时处理故障
- 作为维护工程师，我希望能够手动控制每个电机轴，以便进行设备调试和维护

### 系统集成用户故事
- 作为系统集成商，我希望电机控制模块能够与现有的UI界面无缝集成，以便保持界面一致性
- 作为系统集成商，我希望电机控制能够与其他系统模块协调工作，以便实现完整的生产流程

## Acceptance Criteria

### 硬件集成验收标准
- [ ] DMC1000B控制卡能够成功初始化和关闭
- [ ] 4个电机轴（0-3轴）能够正确识别和控制
- [ ] 翻转电机的原点开关能够正确检测
- [ ] 所有电机的脉冲输出和方向控制正常工作

### 翻转电机控制验收标准
- [ ] 能够设置和读取角度单位的运动参数
- [ ] 正转、反转、回原点功能正常工作
- [ ] 能够通过点动控制精确移动到目标位置
- [ ] 能够保存和调用3个预设位置（相对于原点的角度值）
- [ ] 移动到保存位置时能够准确到达相对位置
- [ ] 示教移动时使用与运行参数一致的速度和加速度
- [ ] 绝对角度定位精度满足±0.1°要求
- [ ] 当前角度和目标角度能够实时显示和更新

### 皮带电机控制验收标准
- [ ] 能够设置和读取线性单位的运动参数
- [ ] 点动控制功能正常，点动距离可调
- [ ] 连续运转和停止功能正常工作
- [ ] 输入和输出皮带电机能够独立控制
- [ ] 运动速度和加速度参数能够实时生效

### 状态监控验收标准
- [ ] 电机位置能够实时更新，更新频率不低于10Hz
- [ ] 电机运行状态（运行中/停止/报警）能够正确显示
- [ ] 运动完成状态能够及时反馈
- [ ] 异常状态能够及时检测和报告

### UI集成验收标准
- [ ] 所有控制按钮能够正确响应用户操作
- [ ] 参数设置界面能够正确保存和加载参数
- [ ] 状态显示界面能够实时更新电机状态
- [ ] 界面风格与现有设计保持一致
- [ ] 用户操作响应时间不超过100ms

### 示教功能验收标准
- [ ] 回原点功能能够将电机准确移动到0°位置
- [ ] 点动控制能够精确调整电机位置
- [ ] 位置保存功能能够记录当前相对于原点的角度值
- [ ] 位置移动功能能够准确移动到保存的相对位置
- [ ] 保存的位置数据能够持久化存储
- [ ] 示教移动的速度参数与运行参数保持一致

### 安全性验收标准
- [ ] 急停功能能够立即停止所有电机运动
- [ ] 限位保护功能正常工作
- [ ] 参数范围检查能够防止异常输入
- [ ] 异常情况下能够安全停机

## Non-functional Requirements

### 性能要求
- **响应时间**：用户操作响应时间不超过100ms
- **实时性**：电机状态更新频率不低于10Hz
- **精度要求**：翻转电机角度控制精度±0.1°，皮带电机位置控制精度±0.1mm
- **运动平滑性**：电机运动应平滑无抖动，支持S型速度曲线

### 可靠性要求
- **故障恢复**：系统异常后能够自动恢复到安全状态
- **数据完整性**：电机参数设置和示教位置能够持久化保存
- **异常处理**：所有可能的异常情况都有相应的处理机制
- **运行稳定性**：连续运行24小时无故障

### 兼容性要求
- **硬件兼容**：与DMC1000B运动控制卡完全兼容
- **软件兼容**：与现有C# WinForms架构兼容
- **接口兼容**：与现有MotorManager接口保持兼容
- **UI兼容**：与现有界面设计风格保持一致

### 可维护性要求
- **代码结构**：遵循现有项目的分层架构
- **文档完整**：提供完整的中文代码注释和使用文档
- **配置灵活**：电机参数可通过配置文件调整
- **日志记录**：提供详细的操作和异常日志

### 安全性要求
- **输入验证**：所有用户输入都进行有效性检查
- **权限控制**：关键操作需要相应权限
- **异常保护**：异常情况下自动进入安全模式
- **数据保护**：防止参数被意外修改或丢失
