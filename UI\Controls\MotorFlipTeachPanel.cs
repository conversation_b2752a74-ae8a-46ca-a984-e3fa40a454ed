using System;
using System.Drawing;
using System.Windows.Forms;
using System.Threading.Tasks;
using MyHMI.Helpers;
using MyHMI.Managers;
using MyHMI.Models;
using MyHMI.Events;

namespace MyHMI.UI.Controls
{
    /// <summary>
    /// 翻转电机示教面板 - 专门用于电机操作和位置示教
    /// </summary>
    public partial class MotorFlipTeachPanel : UserControl
    {
        #region 私有字段

        private Panel _mainPanel;
        private Label _titleLabel;
        private Panel _motorGroup;
        private Panel _leftMotorPanel;
        private Panel _rightMotorPanel;

        // 业务逻辑相关
        private DMC1000BMotorManager _motorManager;
        private const short LEFT_FLIP_AXIS = 0;
        private const short RIGHT_FLIP_AXIS = 1;

        // 左翻转电机操作按钮
        private Button _leftPositiveButton;
        private Button _leftNegativeButton;
        private Button _leftHomeButton;
        private Button[] _leftSavePositionButtons = new Button[5];
        private Button[] _leftMoveToPositionButtons = new Button[5];

        // 左翻转电机参数控件
        private TextBox _leftJogAngleTextBox;
        private TextBox _leftCurrentAngleTextBox;

        // 右翻转电机操作按钮
        private Button _rightPositiveButton;
        private Button _rightNegativeButton;
        private Button _rightHomeButton;
        private Button[] _rightSavePositionButtons = new Button[5];
        private Button[] _rightMoveToPositionButtons = new Button[5];

        // 右翻转电机参数控件
        private TextBox _rightJogAngleTextBox;
        private TextBox _rightCurrentAngleTextBox;

        #endregion

        #region 构造函数

        public MotorFlipTeachPanel()
        {
            InitializeComponent();
            InitializeBusinessLogic();
            InitializeInterface();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化界面 - 按HTML原型精确设计
        /// </summary>
        private void InitializeInterface()
        {
            try
            {
                // 设置面板属性 - 按HTML原型 660x840 (700-40padding)
                this.Size = new Size(660, 840);
                this.BackColor = ColorTranslator.FromHtml("#34495e");
                this.Dock = DockStyle.Fill;
                this.Padding = new Padding(0);

                // 创建主面板 - 按HTML原型padding: 20px
                CreateMainPanel();

                // 创建标题 - 按HTML原型样式
                CreateTitle();

                // 创建电机组 - 按HTML原型样式
                CreateMotorGroup();

                LogHelper.Info("翻转电机示教面板初始化完成 - 按HTML原型设计");
            }
            catch (Exception ex)
            {
                LogHelper.Error("翻转电机示教面板初始化失败", ex);
            }
        }

        /// <summary>
        /// 创建主面板 - 按HTML原型
        /// </summary>
        private void CreateMainPanel()
        {
            _mainPanel = new Panel
            {
                Size = new Size(660, 840),
                Location = new Point(0, 0),
                BackColor = ColorTranslator.FromHtml("#34495e"),
                Padding = new Padding(20), // 按HTML原型 padding: 20px
                Dock = DockStyle.Fill
            };

            this.Controls.Add(_mainPanel);
        }

        /// <summary>
        /// 创建标题 - 按HTML原型样式
        /// </summary>
        private void CreateTitle()
        {
            _titleLabel = new Label
            {
                Text = "翻转电机示教",
                Font = new Font("微软雅黑", 20F, FontStyle.Bold), // 按HTML原型 font-size: 20px
                ForeColor = ColorTranslator.FromHtml("#3498db"),   // 按HTML原型 color: #3498db
                Size = new Size(300, 30),
                Location = new Point(0, 0),
                AutoSize = true
            };

            _mainPanel.Controls.Add(_titleLabel);
        }

        /// <summary>
        /// 创建电机组 - 按HTML原型样式
        /// </summary>
        private void CreateMotorGroup()
        {
            _motorGroup = new Panel
            {
                Size = new Size(620, 780), // 660-40padding
                Location = new Point(0, 40), // 标题下方
                BackColor = Color.Transparent
            };

            // 创建左翻转电机面板
            CreateLeftMotorPanel();

            // 创建右翻转电机面板
            CreateRightMotorPanel();

            _mainPanel.Controls.Add(_motorGroup);
        }

        /// <summary>
        /// 创建左翻转电机面板
        /// </summary>
        private void CreateLeftMotorPanel()
        {
            _leftMotorPanel = new Panel
            {
                Size = new Size(620, 470), // 每个电机面板高度470px（增加90px容纳两行新按钮）
                Location = new Point(0, 0),
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(12)
            };

            // 创建标题
            var titleLabel = new Label
            {
                Text = "左翻转电机",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold), // 按HTML原型 h4
                ForeColor = Color.White,
                Size = new Size(200, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };

            _leftMotorPanel.Controls.Add(titleLabel);

            // 创建操作按钮区域
            CreateLeftMotorControls();

            _motorGroup.Controls.Add(_leftMotorPanel);
        }

        /// <summary>
        /// 创建右翻转电机面板
        /// </summary>
        private void CreateRightMotorPanel()
        {
            _rightMotorPanel = new Panel
            {
                Size = new Size(620, 470), // 每个电机面板高度470px（增加90px容纳两行新按钮）
                Location = new Point(0, 480), // 左电机面板下方，留10px间距
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(12)
            };

            // 创建标题
            var titleLabel = new Label
            {
                Text = "右翻转电机",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold), // 按HTML原型 h4
                ForeColor = Color.White,
                Size = new Size(200, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };

            _rightMotorPanel.Controls.Add(titleLabel);

            // 创建操作按钮区域
            CreateRightMotorControls();

            _motorGroup.Controls.Add(_rightMotorPanel);
        }

        /// <summary>
        /// 创建左翻转电机控制按钮
        /// </summary>
        private void CreateLeftMotorControls()
        {
            int yOffset = 35; // 标题下方

            // 第一行：基本操作按钮
            var controlsPanel1 = new Panel
            {
                Size = new Size(596, 35), // 620-24padding
                Location = new Point(0, yOffset),
                BackColor = Color.Transparent
            };

            // 创建基本操作按钮：正转、反转、回原点
            _leftPositiveButton = CreateMotorButton("正转", new Point(0, 0), ColorTranslator.FromHtml("#3498db"));
            _leftNegativeButton = CreateMotorButton("反转", new Point(90, 0), ColorTranslator.FromHtml("#3498db"));
            _leftHomeButton = CreateMotorButton("回原点", new Point(180, 0), ColorTranslator.FromHtml("#f39c12"));

            // 创建参数控件：点动角度和当前角度
            _leftJogAngleTextBox = CreateParamControl("点动角度:", "5.0", "°", new Point(290, 0));
            _leftCurrentAngleTextBox = CreateParamControl("当前角度:", "0.0", "°", new Point(450, 0));
            _leftCurrentAngleTextBox.ReadOnly = true; // 当前角度只读

            // 绑定事件
            _leftPositiveButton.Click += async (s, e) => await OnLeftMotorJogAsync(true);
            _leftNegativeButton.Click += async (s, e) => await OnLeftMotorJogAsync(false);
            _leftHomeButton.Click += async (s, e) => await OnLeftMotorHomeAsync();
            _leftJogAngleTextBox.TextChanged += async (s, e) => await OnLeftJogAngleChangedAsync();

            controlsPanel1.Controls.Add(_leftPositiveButton);
            controlsPanel1.Controls.Add(_leftNegativeButton);
            controlsPanel1.Controls.Add(_leftHomeButton);

            // 添加点动角度控件和标签
            controlsPanel1.Controls.Add(_leftJogAngleTextBox);
            var jogAngleTag = (dynamic)_leftJogAngleTextBox.Tag;
            controlsPanel1.Controls.Add(jogAngleTag.Label);
            controlsPanel1.Controls.Add(jogAngleTag.UnitLabel);

            // 添加当前角度控件和标签
            controlsPanel1.Controls.Add(_leftCurrentAngleTextBox);
            var currentAngleTag = (dynamic)_leftCurrentAngleTextBox.Tag;
            controlsPanel1.Controls.Add(currentAngleTag.Label);
            controlsPanel1.Controls.Add(currentAngleTag.UnitLabel);

            // 第二行：保存位置按钮（1-3）
            var controlsPanel2 = new Panel
            {
                Size = new Size(596, 35),
                Location = new Point(0, yOffset + 45),
                BackColor = Color.Transparent
            };

            // 创建保存位置按钮（1-3）
            for (int i = 0; i < 3; i++)
            {
                _leftSavePositionButtons[i] = CreateMotorButton($"保存位置{i + 1}", new Point(i * 120, 0), ColorTranslator.FromHtml("#27ae60"));
                int positionIndex = i + 1; // 闭包变量
                _leftSavePositionButtons[i].Click += async (s, e) => await OnLeftMotorSavePositionAsync(positionIndex);
                controlsPanel2.Controls.Add(_leftSavePositionButtons[i]);
            }

            // 第三行：保存位置按钮（4-5）
            var controlsPanel3 = new Panel
            {
                Size = new Size(596, 35),
                Location = new Point(0, yOffset + 90),
                BackColor = Color.Transparent
            };

            // 创建保存位置按钮（4-5）
            for (int i = 3; i < 5; i++)
            {
                _leftSavePositionButtons[i] = CreateMotorButton($"保存位置{i + 1}", new Point((i - 3) * 120, 0), ColorTranslator.FromHtml("#27ae60"));
                int positionIndex = i + 1; // 闭包变量
                _leftSavePositionButtons[i].Click += async (s, e) => await OnLeftMotorSavePositionAsync(positionIndex);
                controlsPanel3.Controls.Add(_leftSavePositionButtons[i]);
            }

            // 第四行：移动到位置按钮（1-3）
            var controlsPanel4 = new Panel
            {
                Size = new Size(596, 35),
                Location = new Point(0, yOffset + 135),
                BackColor = Color.Transparent
            };

            // 创建移动到位置按钮（1-3）
            for (int i = 0; i < 3; i++)
            {
                _leftMoveToPositionButtons[i] = CreateMotorButton($"移动到位{i + 1}", new Point(i * 120, 0), ColorTranslator.FromHtml("#3498db"));
                int positionIndex = i + 1; // 闭包变量
                _leftMoveToPositionButtons[i].Click += async (s, e) => await OnLeftMotorMoveToPositionAsync(positionIndex);
                controlsPanel4.Controls.Add(_leftMoveToPositionButtons[i]);
            }

            // 第五行：移动到位置按钮（4-5）
            var controlsPanel5 = new Panel
            {
                Size = new Size(596, 35),
                Location = new Point(0, yOffset + 180),
                BackColor = Color.Transparent
            };

            // 创建移动到位置按钮（4-5）
            for (int i = 3; i < 5; i++)
            {
                _leftMoveToPositionButtons[i] = CreateMotorButton($"移动到位{i + 1}", new Point((i - 3) * 120, 0), ColorTranslator.FromHtml("#3498db"));
                int positionIndex = i + 1; // 闭包变量
                _leftMoveToPositionButtons[i].Click += async (s, e) => await OnLeftMotorMoveToPositionAsync(positionIndex);
                controlsPanel5.Controls.Add(_leftMoveToPositionButtons[i]);
            }

            _leftMotorPanel.Controls.Add(controlsPanel1);
            _leftMotorPanel.Controls.Add(controlsPanel2);
            _leftMotorPanel.Controls.Add(controlsPanel3);
            _leftMotorPanel.Controls.Add(controlsPanel4);
            _leftMotorPanel.Controls.Add(controlsPanel5);
        }

        /// <summary>
        /// 创建右翻转电机控制按钮
        /// </summary>
        private void CreateRightMotorControls()
        {
            int yOffset = 35; // 标题下方

            // 第一行：基本操作按钮
            var controlsPanel1 = new Panel
            {
                Size = new Size(596, 35), // 620-24padding
                Location = new Point(0, yOffset),
                BackColor = Color.Transparent
            };

            // 创建基本操作按钮：正转、反转、回原点
            _rightPositiveButton = CreateMotorButton("正转", new Point(0, 0), ColorTranslator.FromHtml("#3498db"));
            _rightNegativeButton = CreateMotorButton("反转", new Point(90, 0), ColorTranslator.FromHtml("#3498db"));
            _rightHomeButton = CreateMotorButton("回原点", new Point(180, 0), ColorTranslator.FromHtml("#f39c12"));

            // 创建参数控件：点动角度和当前角度
            _rightJogAngleTextBox = CreateParamControl("点动角度:", "5.0", "°", new Point(290, 0));
            _rightCurrentAngleTextBox = CreateParamControl("当前角度:", "0.0", "°", new Point(450, 0));
            _rightCurrentAngleTextBox.ReadOnly = true; // 当前角度只读

            // 绑定事件
            _rightPositiveButton.Click += async (s, e) => await OnRightMotorJogAsync(true);
            _rightNegativeButton.Click += async (s, e) => await OnRightMotorJogAsync(false);
            _rightHomeButton.Click += async (s, e) => await OnRightMotorHomeAsync();
            _rightJogAngleTextBox.TextChanged += async (s, e) => await OnRightJogAngleChangedAsync();

            controlsPanel1.Controls.Add(_rightPositiveButton);
            controlsPanel1.Controls.Add(_rightNegativeButton);
            controlsPanel1.Controls.Add(_rightHomeButton);

            // 添加点动角度控件和标签
            controlsPanel1.Controls.Add(_rightJogAngleTextBox);
            var rightJogAngleTag = (dynamic)_rightJogAngleTextBox.Tag;
            controlsPanel1.Controls.Add(rightJogAngleTag.Label);
            controlsPanel1.Controls.Add(rightJogAngleTag.UnitLabel);

            // 添加当前角度控件和标签
            controlsPanel1.Controls.Add(_rightCurrentAngleTextBox);
            var rightCurrentAngleTag = (dynamic)_rightCurrentAngleTextBox.Tag;
            controlsPanel1.Controls.Add(rightCurrentAngleTag.Label);
            controlsPanel1.Controls.Add(rightCurrentAngleTag.UnitLabel);

            // 第二行：保存位置按钮（1-3）
            var controlsPanel2 = new Panel
            {
                Size = new Size(596, 35),
                Location = new Point(0, yOffset + 45),
                BackColor = Color.Transparent
            };

            // 创建保存位置按钮（1-3）
            for (int i = 0; i < 3; i++)
            {
                _rightSavePositionButtons[i] = CreateMotorButton($"保存位置{i + 1}", new Point(i * 120, 0), ColorTranslator.FromHtml("#27ae60"));
                int positionIndex = i + 1; // 闭包变量
                _rightSavePositionButtons[i].Click += async (s, e) => await OnRightMotorSavePositionAsync(positionIndex);
                controlsPanel2.Controls.Add(_rightSavePositionButtons[i]);
            }

            // 第三行：保存位置按钮（4-5）
            var controlsPanel3 = new Panel
            {
                Size = new Size(596, 35),
                Location = new Point(0, yOffset + 90),
                BackColor = Color.Transparent
            };

            // 创建保存位置按钮（4-5）
            for (int i = 3; i < 5; i++)
            {
                _rightSavePositionButtons[i] = CreateMotorButton($"保存位置{i + 1}", new Point((i - 3) * 120, 0), ColorTranslator.FromHtml("#27ae60"));
                int positionIndex = i + 1; // 闭包变量
                _rightSavePositionButtons[i].Click += async (s, e) => await OnRightMotorSavePositionAsync(positionIndex);
                controlsPanel3.Controls.Add(_rightSavePositionButtons[i]);
            }

            // 第四行：移动到位置按钮（1-3）
            var controlsPanel4 = new Panel
            {
                Size = new Size(596, 35),
                Location = new Point(0, yOffset + 135),
                BackColor = Color.Transparent
            };

            // 创建移动到位置按钮（1-3）
            for (int i = 0; i < 3; i++)
            {
                _rightMoveToPositionButtons[i] = CreateMotorButton($"移动到位{i + 1}", new Point(i * 120, 0), ColorTranslator.FromHtml("#3498db"));
                int positionIndex = i + 1; // 闭包变量
                _rightMoveToPositionButtons[i].Click += async (s, e) => await OnRightMotorMoveToPositionAsync(positionIndex);
                controlsPanel4.Controls.Add(_rightMoveToPositionButtons[i]);
            }

            // 第五行：移动到位置按钮（4-5）
            var controlsPanel5 = new Panel
            {
                Size = new Size(596, 35),
                Location = new Point(0, yOffset + 180),
                BackColor = Color.Transparent
            };

            // 创建移动到位置按钮（4-5）
            for (int i = 3; i < 5; i++)
            {
                _rightMoveToPositionButtons[i] = CreateMotorButton($"移动到位{i + 1}", new Point((i - 3) * 120, 0), ColorTranslator.FromHtml("#3498db"));
                int positionIndex = i + 1; // 闭包变量
                _rightMoveToPositionButtons[i].Click += async (s, e) => await OnRightMotorMoveToPositionAsync(positionIndex);
                controlsPanel5.Controls.Add(_rightMoveToPositionButtons[i]);
            }

            _rightMotorPanel.Controls.Add(controlsPanel1);
            _rightMotorPanel.Controls.Add(controlsPanel2);
            _rightMotorPanel.Controls.Add(controlsPanel3);
            _rightMotorPanel.Controls.Add(controlsPanel4);
            _rightMotorPanel.Controls.Add(controlsPanel5);
        }

        /// <summary>
        /// 创建电机按钮 - 按HTML原型样式
        /// </summary>
        private Button CreateMotorButton(string text, Point location, Color backColor)
        {
            return new Button
            {
                Text = text,
                Font = new Font("微软雅黑", 10F),
                Size = new Size(110, 30), // 调整按钮大小以适应4个按钮
                Location = location,
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false,
                Cursor = Cursors.Hand
            };
        }

        /// <summary>
        /// 创建参数控件 - 标签+输入框+单位
        /// </summary>
        private TextBox CreateParamControl(string labelText, string defaultValue, string unit, Point location)
        {
            // 标签
            var label = new Label
            {
                Text = labelText,
                Font = new Font("微软雅黑", 10F),
                ForeColor = Color.White,
                Size = new Size(60, 20),
                Location = new Point(location.X, location.Y + 8),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // 输入框
            var textBox = new TextBox
            {
                Text = defaultValue,
                Font = new Font("微软雅黑", 10F),
                Size = new Size(50, 20),
                Location = new Point(location.X + 65, location.Y + 5),
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                TextAlign = HorizontalAlignment.Center
            };

            // 单位标签
            var unitLabel = new Label
            {
                Text = unit,
                Font = new Font("微软雅黑", 10F),
                ForeColor = Color.White,
                Size = new Size(20, 20),
                Location = new Point(location.X + 120, location.Y + 8),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // 将控件添加到父容器（需要在调用处添加到正确的面板）
            // 这里返回textBox，标签需要单独添加
            textBox.Tag = new { Label = label, UnitLabel = unitLabel };

            return textBox;
        }

        #endregion

        #region 业务逻辑初始化

        /// <summary>
        /// 初始化业务逻辑
        /// </summary>
        private void InitializeBusinessLogic()
        {
            try
            {
                // 获取电机管理器实例
                _motorManager = DMC1000BMotorManager.Instance;

                // 订阅电机事件
                _motorManager.MotorPositionChanged += OnMotorPositionChanged;
                _motorManager.MotorStatusChanged += OnMotorStatusChanged;

                LogHelper.Info("翻转电机示教面板业务逻辑初始化完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("翻转电机示教面板业务逻辑初始化失败", ex);
            }
        }

        /// <summary>
        /// 界面加载完成后的初始化
        /// </summary>
        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);

            // 初始化参数显示（需要在控件创建完成后调用）
            if (_leftJogAngleTextBox != null && _rightJogAngleTextBox != null)
            {
                InitializeParameterDisplay();
            }
        }

        #endregion

        #region 资源释放

        /// <summary>
        /// 释放资源
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            try
            {
                if (disposing)
                {
                    // 取消事件订阅
                    if (_motorManager != null)
                    {
                        _motorManager.MotorPositionChanged -= OnMotorPositionChanged;
                        _motorManager.MotorStatusChanged -= OnMotorStatusChanged;
                    }
                }
                LogHelper.Info("翻转电机示教面板资源释放完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("翻转电机示教面板资源释放失败", ex);
            }
            finally
            {
                base.Dispose(disposing);
            }
        }

        #endregion

        #region 左翻转电机事件处理

        /// <summary>
        /// 左翻转电机点动
        /// </summary>
        private async Task OnLeftMotorJogAsync(bool direction)
        {
            try
            {
                // 从UI获取点动角度
                double jogAngle = GetLeftJogAngle();

                // 检查电机管理器是否可用
                if (_motorManager == null)
                {
                    MessageBox.Show("电机管理器未初始化", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                bool success = await _motorManager.FlipMotorJogAsync(LEFT_FLIP_AXIS, direction, jogAngle);
                if (!success)
                {
                    MessageBox.Show("左翻转电机点动失败", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("左翻转电机点动失败", ex);
                MessageBox.Show($"左翻转电机点动失败: {ex.Message}", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 左翻转电机回原点
        /// </summary>
        private async Task OnLeftMotorHomeAsync()
        {
            try
            {
                bool success = await _motorManager.FlipMotorHomeAsync(LEFT_FLIP_AXIS);
                if (!success)
                {
                    MessageBox.Show("左翻转电机回原点失败", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("左翻转电机回原点失败", ex);
                MessageBox.Show($"左翻转电机回原点失败: {ex.Message}", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 左翻转电机保存位置
        /// </summary>
        private async Task OnLeftMotorSavePositionAsync(int positionIndex)
        {
            try
            {
                bool success = await _motorManager.SaveFlipMotorPositionAsync(LEFT_FLIP_AXIS, positionIndex);
                if (success)
                {
                    MessageBox.Show($"左翻转电机位置{positionIndex}保存成功", "操作成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show($"左翻转电机位置{positionIndex}保存失败", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("未归零"))
            {
                // 专门处理归零相关异常
                var result = MessageBox.Show($"左翻转电机未归零，无法保存可靠的位置。是否先执行归零操作？", "需要归零",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        bool homeSuccess = await _motorManager.FlipMotorHomeAsync(LEFT_FLIP_AXIS);
                        if (homeSuccess)
                        {
                            MessageBox.Show("左翻转电机归零成功，请重新点击保存位置", "归零完成",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("左翻转电机归零失败", "归零失败",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception homeEx)
                    {
                        LogHelper.Error("左翻转电机归零失败", homeEx);
                        MessageBox.Show($"左翻转电机归零失败: {homeEx.Message}", "归零失败",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"左翻转电机保存位置{positionIndex}失败", ex);
                MessageBox.Show($"左翻转电机保存位置{positionIndex}失败: {ex.Message}", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 左翻转电机移动到位置
        /// </summary>
        private async Task OnLeftMotorMoveToPositionAsync(int positionIndex)
        {
            try
            {
                bool success = await _motorManager.MoveToFlipMotorPositionAsync(LEFT_FLIP_AXIS, positionIndex);
                if (success)
                {
                    MessageBox.Show($"左翻转电机移动到位置{positionIndex}成功", "操作成功",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show($"左翻转电机移动到位置{positionIndex}失败", "操作失败",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("未归零"))
            {
                // 专门处理归零相关异常
                var result = MessageBox.Show($"左翻转电机未归零，是否先执行归零操作？", "需要归零",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        bool homeSuccess = await _motorManager.FlipMotorHomeAsync(LEFT_FLIP_AXIS);
                        if (homeSuccess)
                        {
                            MessageBox.Show("左翻转电机归零成功，请重新点击移动到位置", "归零完成",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("左翻转电机归零失败", "归零失败",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception homeEx)
                    {
                        LogHelper.Error("左翻转电机归零失败", homeEx);
                        MessageBox.Show($"左翻转电机归零失败: {homeEx.Message}", "归零失败",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"左翻转电机移动到位置{positionIndex}失败", ex);
                MessageBox.Show($"左翻转电机移动到位置{positionIndex}失败: {ex.Message}", "操作失败",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 右翻转电机事件处理

        /// <summary>
        /// 右翻转电机点动
        /// </summary>
        private async Task OnRightMotorJogAsync(bool direction)
        {
            try
            {
                // 从UI获取点动角度
                double jogAngle = GetRightJogAngle();

                // 检查电机管理器是否可用
                if (_motorManager == null)
                {
                    MessageBox.Show("电机管理器未初始化", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                bool success = await _motorManager.FlipMotorJogAsync(RIGHT_FLIP_AXIS, direction, jogAngle);
                if (!success)
                {
                    MessageBox.Show("右翻转电机点动失败", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("右翻转电机点动失败", ex);
                MessageBox.Show($"右翻转电机点动失败: {ex.Message}", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 右翻转电机回原点
        /// </summary>
        private async Task OnRightMotorHomeAsync()
        {
            try
            {
                bool success = await _motorManager.FlipMotorHomeAsync(RIGHT_FLIP_AXIS);
                if (!success)
                {
                    MessageBox.Show("右翻转电机回原点失败", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("右翻转电机回原点失败", ex);
                MessageBox.Show($"右翻转电机回原点失败: {ex.Message}", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 右翻转电机保存位置
        /// </summary>
        private async Task OnRightMotorSavePositionAsync(int positionIndex)
        {
            try
            {
                bool success = await _motorManager.SaveFlipMotorPositionAsync(RIGHT_FLIP_AXIS, positionIndex);
                if (success)
                {
                    MessageBox.Show($"右翻转电机位置{positionIndex}保存成功", "操作成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show($"右翻转电机位置{positionIndex}保存失败", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("未归零"))
            {
                // 专门处理归零相关异常
                var result = MessageBox.Show($"右翻转电机未归零，无法保存可靠的位置。是否先执行归零操作？", "需要归零",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        bool homeSuccess = await _motorManager.FlipMotorHomeAsync(RIGHT_FLIP_AXIS);
                        if (homeSuccess)
                        {
                            MessageBox.Show("右翻转电机归零成功，请重新点击保存位置", "归零完成",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("右翻转电机归零失败", "归零失败",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception homeEx)
                    {
                        LogHelper.Error("右翻转电机归零失败", homeEx);
                        MessageBox.Show($"右翻转电机归零失败: {homeEx.Message}", "归零失败",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"右翻转电机保存位置{positionIndex}失败", ex);
                MessageBox.Show($"右翻转电机保存位置{positionIndex}失败: {ex.Message}", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 右翻转电机移动到位置
        /// </summary>
        private async Task OnRightMotorMoveToPositionAsync(int positionIndex)
        {
            try
            {
                bool success = await _motorManager.MoveToFlipMotorPositionAsync(RIGHT_FLIP_AXIS, positionIndex);
                if (success)
                {
                    MessageBox.Show($"右翻转电机移动到位置{positionIndex}成功", "操作成功",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show($"右翻转电机移动到位置{positionIndex}失败", "操作失败",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("未归零"))
            {
                // 专门处理归零相关异常
                var result = MessageBox.Show($"右翻转电机未归零，是否先执行归零操作？", "需要归零",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        bool homeSuccess = await _motorManager.FlipMotorHomeAsync(RIGHT_FLIP_AXIS);
                        if (homeSuccess)
                        {
                            MessageBox.Show("右翻转电机归零成功，请重新点击移动到位置", "归零完成",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("右翻转电机归零失败", "归零失败",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception homeEx)
                    {
                        LogHelper.Error("右翻转电机归零失败", homeEx);
                        MessageBox.Show($"右翻转电机归零失败: {homeEx.Message}", "归零失败",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"右翻转电机移动到位置{positionIndex}失败", ex);
                MessageBox.Show($"右翻转电机移动到位置{positionIndex}失败: {ex.Message}", "操作失败",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 参数管理

        /// <summary>
        /// 获取左翻转电机点动角度
        /// </summary>
        private double GetLeftJogAngle()
        {
            try
            {
                if (double.TryParse(_leftJogAngleTextBox?.Text, out double angle))
                {
                    // 验证角度范围
                    if (angle >= 0.1 && angle <= 180.0)
                    {
                        return angle;
                    }
                }

                // 如果解析失败或超出范围，使用全局参数管理器的值
                return GlobalMotorParameterManager.GetLeftFlipJogAngle();
            }
            catch (Exception ex)
            {
                LogHelper.Error("获取左翻转电机点动角度失败", ex);
                return 5.0; // 返回默认值
            }
        }

        /// <summary>
        /// 获取右翻转电机点动角度
        /// </summary>
        private double GetRightJogAngle()
        {
            try
            {
                if (double.TryParse(_rightJogAngleTextBox?.Text, out double angle))
                {
                    // 验证角度范围
                    if (angle >= 0.1 && angle <= 180.0)
                    {
                        return angle;
                    }
                }

                // 如果解析失败或超出范围，使用全局参数管理器的值
                return GlobalMotorParameterManager.GetRightFlipJogAngle();
            }
            catch (Exception ex)
            {
                LogHelper.Error("获取右翻转电机点动角度失败", ex);
                return 5.0; // 返回默认值
            }
        }

        /// <summary>
        /// 左翻转电机点动角度变化处理
        /// </summary>
        private async Task OnLeftJogAngleChangedAsync()
        {
            try
            {
                if (double.TryParse(_leftJogAngleTextBox?.Text, out double jogAngle))
                {
                    // 验证角度范围
                    if (jogAngle >= 0.1 && jogAngle <= 180.0)
                    {
                        await GlobalMotorParameterManager.UpdateLeftFlipJogAngleAsync(jogAngle, "示教界面");
                        LogHelper.Info($"左翻转电机点动角度已更新: {jogAngle}°");
                    }
                    else
                    {
                        LogHelper.Warning($"左翻转电机点动角度超出范围: {jogAngle}°，有效范围: 0.1-180°");
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("更新左翻转电机点动角度失败", ex);
            }
        }

        /// <summary>
        /// 右翻转电机点动角度变化处理
        /// </summary>
        private async Task OnRightJogAngleChangedAsync()
        {
            try
            {
                if (double.TryParse(_rightJogAngleTextBox?.Text, out double jogAngle))
                {
                    // 验证角度范围
                    if (jogAngle >= 0.1 && jogAngle <= 180.0)
                    {
                        await GlobalMotorParameterManager.UpdateRightFlipJogAngleAsync(jogAngle, "示教界面");
                        LogHelper.Info($"右翻转电机点动角度已更新: {jogAngle}°");
                    }
                    else
                    {
                        LogHelper.Warning($"右翻转电机点动角度超出范围: {jogAngle}°，有效范围: 0.1-180°");
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("更新右翻转电机点动角度失败", ex);
            }
        }

        /// <summary>
        /// 初始化参数显示
        /// </summary>
        private void InitializeParameterDisplay()
        {
            try
            {
                // 从全局参数管理器获取初始值
                _leftJogAngleTextBox.Text = GlobalMotorParameterManager.GetLeftFlipJogAngle().ToString("F1");
                _rightJogAngleTextBox.Text = GlobalMotorParameterManager.GetRightFlipJogAngle().ToString("F1");

                LogHelper.Info("翻转电机示教界面参数显示初始化完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("初始化参数显示失败", ex);
            }
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 电机位置变化事件处理
        /// </summary>
        private void OnMotorPositionChanged(object sender, MotorPositionEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnMotorPositionChanged(sender, e)));
                return;
            }

            try
            {
                // 更新当前角度显示
                if (e.MotorId == LEFT_FLIP_AXIS)
                {
                    _leftCurrentAngleTextBox.Text = e.Position.ToString("F2");
                }
                else if (e.MotorId == RIGHT_FLIP_AXIS)
                {
                    _rightCurrentAngleTextBox.Text = e.Position.ToString("F2");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("更新电机位置显示失败", ex);
            }
        }

        /// <summary>
        /// 电机状态变化事件处理
        /// </summary>
        private void OnMotorStatusChanged(object sender, EventArgs e)
        {
            // 处理电机状态变化
        }

        #endregion
    }
}
