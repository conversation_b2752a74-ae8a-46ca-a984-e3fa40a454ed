﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Net.Sockets;
using System.IO;
using System.Threading;

namespace epson
{
    public partial class Form1 : Form
    {
        private TcpClient controlTcpClient;    // 控制连接客户端
        private TcpClient dataTcpClient;       // 数据连接客户端
        private NetworkStream controlNetworkStream; // 控制连接流
        private NetworkStream dataNetworkStream;    // 数据连接流
        private bool isControlConnected = false;    // 控制连接状态
        private bool isDataConnected = false;       // 数据连接状态
        private bool isLoggedIn = false;            // 登录状态
        private System.Windows.Forms.Timer statusTimer;
        private CancellationTokenSource dataCancellationTokenSource; // 数据监听取消令牌
        private Thread dataReceiveThread;          // 数据接收线程

        public Form1()
        {
            InitializeComponent();
            // 绑定事件
            btnConnect.Click += btnConnect_Click;
            btnDisconnect.Click += btnDisconnect_Click;
            btnDataConnect.Click += btnDataConnect_Click;
            btnDataDisconnect.Click += btnDataDisconnect_Click;
            btnClearMonitor.Click += btnClearMonitor_Click;
            btnStart.Click += btnStart_Click;
            btnStop.Click += btnStop_Click;
            btnSend.Click += btnSend_Click;
            this.FormClosing += Form1_FormClosing;
            
            // 初始化状态监视定时器
            statusTimer = new System.Windows.Forms.Timer();
            statusTimer.Interval = 5000; // 5秒获取一次状态
            statusTimer.Tick += StatusTimer_Tick;
        }

        // 状态监视定时器事件
        private void StatusTimer_Tick(object sender, EventArgs e)
        {
            GetRobotStatus();
        }

        // 控制连接按钮点击事件
        private void btnConnect_Click(object sender, EventArgs e)
        {
            string ip = txtIP.Text.Trim();
            
            // 连接控制端口
            ConnectControlPort(ip);
        }

        // 数据连接按钮点击事件
        private void btnDataConnect_Click(object sender, EventArgs e)
        {
            string ip = txtIP.Text.Trim();
            
            // 连接数据端口
            ConnectDataPort(ip);
        }

        // 控制断开按钮点击事件
        private void btnDisconnect_Click(object sender, EventArgs e)
        {
            // 停止状态监视
            statusTimer.Stop();
            
            // 断开控制连接
            DisconnectControlConnection();
        }

        // 数据断开按钮点击事件
        private void btnDataDisconnect_Click(object sender, EventArgs e)
        {
            // 断开数据连接
            DisconnectDataConnection();
        }

        // 连接控制端口
        private void ConnectControlPort(string ip)
        {
            if (isControlConnected)
            {
                AppendToMonitor("控制连接已经建立");
                return;
            }

            try
            {
                int controlPort = int.Parse(txtControlPort.Text.Trim());

                controlTcpClient = new TcpClient();
                controlTcpClient.Connect(ip, controlPort);
                controlNetworkStream = controlTcpClient.GetStream();

                isControlConnected = true;
                AppendToMonitor($"成功连接到控制端口 {ip}:{controlPort}");

                // 自动登录
                LoginToRobot();
                
                // 启动状态监视
                statusTimer.Start();
                
                // 更新UI状态
                UpdateUIState();
            }
            catch (Exception ex)
            {
                AppendToMonitor($"控制端口连接失败: {ex.Message}");
            }
        }

        // 连接数据端口
        private void ConnectDataPort(string ip)
        {
            if (isDataConnected)
            {
                AppendToMonitor("数据连接已经建立");
                return;
            }

            try
            {
                int dataPort = int.Parse(txtDataPort.Text.Trim());

                dataTcpClient = new TcpClient();
                dataTcpClient.Connect(ip, dataPort);
                dataNetworkStream = dataTcpClient.GetStream();

                isDataConnected = true;
                
                // 启动数据接收线程
                StartDataReceiveThread();
                
                AppendToMonitor($"成功连接到数据端口 {ip}:{dataPort}");
                
                // 更新UI状态
                UpdateUIState();
            }
            catch (Exception ex)
            {
                AppendToMonitor($"数据端口连接失败: {ex.Message}");
            }
        }

        // 启动数据接收线程
        private void StartDataReceiveThread()
        {
            if (dataReceiveThread != null && dataReceiveThread.IsAlive)
            {
                dataReceiveThread.Abort();
            }

            dataCancellationTokenSource = new CancellationTokenSource();
            dataReceiveThread = new Thread(ReceiveDataLoop);
            dataReceiveThread.IsBackground = true;
            dataReceiveThread.Start();
        }

        // 数据接收循环
        private void ReceiveDataLoop()
        {
            try
            {
                byte[] buffer = new byte[1024];
                while (isDataConnected && dataNetworkStream != null)
                {
                    // 检查是否有数据可读
                    if (dataNetworkStream.DataAvailable)
                    {
                        int bytesRead = dataNetworkStream.Read(buffer, 0, buffer.Length);
                        if (bytesRead > 0)
                        {
                            string receivedData = Encoding.ASCII.GetString(buffer, 0, bytesRead);
                            AppendToMonitor($"[自动接收] 数据端口收到数据: {receivedData.Trim()}");
                        }
                    }
                    else
                    {
                        // 短暂休眠以避免CPU占用过高
                        Thread.Sleep(100);
                    }
                }
            }
            catch (Exception ex)
            {
                if (isDataConnected)
                {
                    AppendToMonitor($"数据接收线程出错: {ex.Message}");
                }
            }
        }

        // 登录到机器人
        private void LoginToRobot()
        {
            try
            {
                string password = txtPassword.Text.Trim();
                string loginCommand = $"$Login,{password}\r\n";
                
                byte[] sendBytes = Encoding.ASCII.GetBytes(loginCommand);
                controlNetworkStream.Write(sendBytes, 0, sendBytes.Length);
                AppendToMonitor($"发送控制命令: {loginCommand.Trim()}");

                // 读取响应
                byte[] buffer = new byte[1024];
                int bytesRead = controlNetworkStream.Read(buffer, 0, buffer.Length);
                string response = Encoding.ASCII.GetString(buffer, 0, bytesRead);
                AppendToMonitor($"接收控制响应: {response.Trim()}");

                if (response.StartsWith("#Login,0"))
                {
                    isLoggedIn = true;
                    AppendToMonitor("登录成功");
                }
                else
                {
                    AppendToMonitor("登录失败");
                }
            }
            catch (Exception ex)
            {
                AppendToMonitor($"登录过程出错: {ex.Message}");
            }
        }

        // 启动机器人
        private void btnStart_Click(object sender, EventArgs e)
        {
            if (!isControlConnected || !isLoggedIn)
            {
                AppendToMonitor("请先连接并登录到机器人控制器");
                return;
            }

            try
            {
                // 发送Start命令，启动Main函数(函数编号0)
                string startCommand = "$Start,0\r\n";
                byte[] sendBytes = Encoding.ASCII.GetBytes(startCommand);
                controlNetworkStream.Write(sendBytes, 0, sendBytes.Length);
                AppendToMonitor($"发送控制命令: {startCommand.Trim()}");

                // 读取响应
                byte[] buffer = new byte[1024];
                int bytesRead = controlNetworkStream.Read(buffer, 0, buffer.Length);
                string response = Encoding.ASCII.GetString(buffer, 0, bytesRead);
                AppendToMonitor($"接收控制响应: {response.Trim()}");

                if (response.StartsWith("#Start,0"))
                {
                    AppendToMonitor("机器人启动命令发送成功");
                }
                else
                {
                    AppendToMonitor("机器人启动失败");
                }
            }
            catch (Exception ex)
            {
                AppendToMonitor($"启动机器人时出错: {ex.Message}");
            }
        }

        // 停止机器人
        private void btnStop_Click(object sender, EventArgs e)
        {
            if (!isControlConnected || !isLoggedIn)
            {
                AppendToMonitor("请先连接并登录到机器人控制器");
                return;
            }

            try
            {
                // 发送Stop命令
                string stopCommand = "$Stop\r\n";
                byte[] sendBytes = Encoding.ASCII.GetBytes(stopCommand);
                controlNetworkStream.Write(sendBytes, 0, sendBytes.Length);
                AppendToMonitor($"发送控制命令: {stopCommand.Trim()}");

                // 读取响应
                byte[] buffer = new byte[1024];
                int bytesRead = controlNetworkStream.Read(buffer, 0, buffer.Length);
                string response = Encoding.ASCII.GetString(buffer, 0, bytesRead);
                AppendToMonitor($"接收控制响应: {response.Trim()}");

                if (response.StartsWith("#Stop,0"))
                {
                    AppendToMonitor("机器人停止命令发送成功");
                }
                else
                {
                    AppendToMonitor("机器人停止失败");
                }
            }
            catch (Exception ex)
            {
                AppendToMonitor($"停止机器人时出错: {ex.Message}");
            }
        }

        // 发送数据按钮点击事件
        private void btnSend_Click(object sender, EventArgs e)
        {
            if (!isDataConnected)
            {
                AppendToMonitor("数据连接未建立，请先连接数据端口");
                return;
            }

            string dataToSend = txtSendData.Text.Trim();
            if (string.IsNullOrEmpty(dataToSend))
            {
                AppendToMonitor("请输入要发送的数据");
                return;
            }

            try
            {
                // 发送用户输入的数据作为命令
                string command = dataToSend + "\r\n";
                byte[] sendBytes = Encoding.ASCII.GetBytes(command);
                dataNetworkStream.Write(sendBytes, 0, sendBytes.Length);
                AppendToMonitor($"[主动发送] 数据端口发送数据: {dataToSend}");

                // 读取响应（可选，有些数据可能不需要响应）
                if (dataNetworkStream.DataAvailable)
                {
                    byte[] buffer = new byte[1024];
                    int bytesRead = dataNetworkStream.Read(buffer, 0, buffer.Length);
                    string response = Encoding.ASCII.GetString(buffer, 0, bytesRead);
                    AppendToMonitor($"[响应] 数据端口收到响应: {response.Trim()}");
                }
            }
            catch (Exception ex)
            {
                AppendToMonitor($"发送数据时出错: {ex.Message}");
            }
        }

        // 断开控制连接
        private void DisconnectControlConnection()
        {
            try
            {
                if (isControlConnected && isLoggedIn)
                {
                    // 发送Logout命令
                    string logoutCommand = "$Logout\r\n";
                    byte[] sendBytes = Encoding.ASCII.GetBytes(logoutCommand);
                    controlNetworkStream.Write(sendBytes, 0, sendBytes.Length);
                    AppendToMonitor($"发送控制命令: {logoutCommand.Trim()}");

                    // 读取响应
                    byte[] buffer = new byte[1024];
                    int bytesRead = controlNetworkStream.Read(buffer, 0, buffer.Length);
                    string response = Encoding.ASCII.GetString(buffer, 0, bytesRead);
                    AppendToMonitor($"接收控制响应: {response.Trim()}");
                }

                if (controlNetworkStream != null)
                {
                    controlNetworkStream.Close();
                    controlNetworkStream = null;
                }

                if (controlTcpClient != null)
                {
                    controlTcpClient.Close();
                    controlTcpClient = null;
                }

                isControlConnected = false;
                isLoggedIn = false;
                AppendToMonitor("已断开控制连接");
                
                // 更新UI状态
                UpdateUIState();
            }
            catch (Exception ex)
            {
                AppendToMonitor($"断开控制连接时出错: {ex.Message}");
            }
        }

        // 断开数据连接
        private void DisconnectDataConnection()
        {
            try
            {
                // 停止数据接收线程
                if (dataCancellationTokenSource != null)
                {
                    dataCancellationTokenSource.Cancel();
                }
                
                if (dataNetworkStream != null)
                {
                    dataNetworkStream.Close();
                    dataNetworkStream = null;
                }

                if (dataTcpClient != null)
                {
                    dataTcpClient.Close();
                    dataTcpClient = null;
                }

                isDataConnected = false;
                AppendToMonitor("已断开数据连接");
                
                // 更新UI状态
                UpdateUIState();
            }
            catch (Exception ex)
            {
                AppendToMonitor($"断开数据连接时出错: {ex.Message}");
            }
        }

        // 更新UI状态
        private void UpdateUIState()
        {
            // 控制连接按钮状态
            btnConnect.Enabled = !isControlConnected;
            btnDisconnect.Enabled = isControlConnected;
            
            // 数据连接按钮状态
            btnDataConnect.Enabled = !isDataConnected;
            btnDataDisconnect.Enabled = isDataConnected;
            
            // 控制按钮状态
            btnStart.Enabled = isControlConnected && isLoggedIn;
            btnStop.Enabled = isControlConnected && isLoggedIn;
            
            // 数据发送按钮状态
            btnSend.Enabled = isDataConnected;
        }

        // 添加信息到监视窗口
        private void AppendToMonitor(string message)
        {
            if (txtMonitor.InvokeRequired)
            {
                txtMonitor.Invoke(new Action<string>(AppendToMonitor), message);
            }
            else
            {
                txtMonitor.AppendText($"[{DateTime.Now:HH:mm:ss}] {message}\r\n");
                // 自动滚动到最后一行
                txtMonitor.SelectionStart = txtMonitor.Text.Length;
                txtMonitor.ScrollToCaret();
            }
        }

        // 窗体关闭事件
        private void Form1_FormClosing(object sender, FormClosingEventArgs e)
        {
            // 停止状态监视
            statusTimer.Stop();
            
            // 断开所有连接
            DisconnectControlConnection();
            DisconnectDataConnection();
        }

        // 清空监视窗口
        private void btnClearMonitor_Click(object sender, EventArgs e)
        {
            txtMonitor.Clear();
        }

        // 获取机器人状态
        private void GetRobotStatus()
        {
            if (!isControlConnected || !isLoggedIn)
            {
                return; // 未连接时不获取状态
            }

            try
            {
                // 发送GetStatus命令
                string statusCommand = "$GetStatus\r\n";
                byte[] sendBytes = Encoding.ASCII.GetBytes(statusCommand);
                controlNetworkStream.Write(sendBytes, 0, sendBytes.Length);
                AppendToMonitor($"发送状态查询: {statusCommand.Trim()}");

                // 读取响应
                byte[] buffer = new byte[1024];
                int bytesRead = controlNetworkStream.Read(buffer, 0, buffer.Length);
                string response = Encoding.ASCII.GetString(buffer, 0, bytesRead);
                AppendToMonitor($"接收状态响应: {response.Trim()}");
            }
            catch (Exception ex)
            {
                AppendToMonitor($"获取状态时出错: {ex.Message}");
            }
        }
    }
}