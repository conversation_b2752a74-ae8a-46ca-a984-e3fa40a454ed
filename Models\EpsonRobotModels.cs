using System;
using System.Collections.Generic;

namespace MyHMI.Models
{
    /// <summary>
    /// Epson机器人配置类（单IP双端口模式）
    /// </summary>
    public class EpsonRobotConfiguration
    {
        /// <summary>
        /// 机器人IP地址
        /// </summary>
        public string IPAddress { get; set; } = "*************";

        /// <summary>
        /// 控制端口号（用于启动/停止指令）
        /// </summary>
        public int ControlPort { get; set; } = 5000;

        /// <summary>
        /// 数据端口号（用于数据收发）
        /// </summary>
        public int DataPort { get; set; } = 5001;

        /// <summary>
        /// 登录密码
        /// </summary>
        public string Password { get; set; } = "EPSON";

        /// <summary>
        /// 连接超时时间（毫秒）
        /// </summary>
        public int ConnectTimeout { get; set; } = 5000;

        /// <summary>
        /// 接收超时时间（毫秒）
        /// </summary>
        public int ReceiveTimeout { get; set; } = 3000;

        /// <summary>
        /// 发送超时时间（毫秒）
        /// </summary>
        public int SendTimeout { get; set; } = 3000;

        /// <summary>
        /// 扫描间隔时间（毫秒）
        /// </summary>
        public int ScanInterval { get; set; } = 100;

        /// <summary>
        /// 自动重连
        /// </summary>
        public bool AutoReconnect { get; set; } = true;

        /// <summary>
        /// 重连间隔（毫秒）
        /// </summary>
        public int ReconnectInterval { get; set; } = 5000;

        /// <summary>
        /// 最大重连次数
        /// </summary>
        public int MaxReconnectAttempts { get; set; } = 10;
    }

    /// <summary>
    /// Epson机器人命令类型枚举
    /// </summary>
    public enum EpsonRobotCommandType
    {
        /// <summary>
        /// 登录命令
        /// </summary>
        Login,

        /// <summary>
        /// 登出命令
        /// </summary>
        Logout,

        /// <summary>
        /// 启动命令
        /// </summary>
        Start,

        /// <summary>
        /// 停止命令
        /// </summary>
        Stop,

        /// <summary>
        /// 暂停命令
        /// </summary>
        Pause,

        /// <summary>
        /// 继续命令
        /// </summary>
        Continue,

        /// <summary>
        /// 复位命令
        /// </summary>
        Reset,

        /// <summary>
        /// 获取状态命令
        /// </summary>
        GetStatus,

        /// <summary>
        /// 设置IO命令
        /// </summary>
        SetIO,

        /// <summary>
        /// 获取IO命令
        /// </summary>
        GetIO,

        /// <summary>
        /// 执行命令
        /// </summary>
        Execute,

        /// <summary>
        /// 自定义命令
        /// </summary>
        Custom
    }

    /// <summary>
    /// Epson机器人命令类
    /// </summary>
    public class EpsonRobotCommand
    {
        /// <summary>
        /// 命令ID
        /// </summary>
        public string CommandId { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 命令类型
        /// </summary>
        public EpsonRobotCommandType CommandType { get; set; }

        /// <summary>
        /// 命令参数
        /// </summary>
        public string Parameters { get; set; } = string.Empty;

        /// <summary>
        /// 原始命令字符串
        /// </summary>
        public string RawCommand { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否需要响应
        /// </summary>
        public bool RequiresResponse { get; set; } = true;

        /// <summary>
        /// 超时时间（毫秒）
        /// </summary>
        public int TimeoutMs { get; set; } = 5000;

        /// <summary>
        /// 构造函数
        /// </summary>
        public EpsonRobotCommand() { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="commandType">命令类型</param>
        /// <param name="parameters">参数</param>
        public EpsonRobotCommand(EpsonRobotCommandType commandType, string parameters = "")
        {
            CommandType = commandType;
            Parameters = parameters ?? string.Empty;
            RawCommand = BuildRawCommand();
        }

        /// <summary>
        /// 构建原始命令字符串
        /// </summary>
        /// <returns>原始命令字符串</returns>
        public string BuildRawCommand()
        {
            string command = CommandType.ToString();
            
            if (!string.IsNullOrEmpty(Parameters))
            {
                return $"${command},{Parameters}";
            }
            else
            {
                return $"${command}";
            }
        }

        /// <summary>
        /// 获取完整的命令字符串（包含结束符）
        /// </summary>
        /// <returns>完整命令字符串</returns>
        public string GetFullCommand()
        {
            if (string.IsNullOrEmpty(RawCommand))
            {
                RawCommand = BuildRawCommand();
            }
            return RawCommand + "\r\n";
        }
    }

    /// <summary>
    /// Epson机器人响应类
    /// </summary>
    public class EpsonRobotResponse
    {
        /// <summary>
        /// 命令ID
        /// </summary>
        public string CommandId { get; set; } = string.Empty;

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 响应数据
        /// </summary>
        public string Data { get; set; } = string.Empty;

        /// <summary>
        /// 错误代码
        /// </summary>
        public int ErrorCode { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 原始响应字符串
        /// </summary>
        public string RawResponse { get; set; } = string.Empty;

        /// <summary>
        /// 响应时间
        /// </summary>
        public DateTime ResponseTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 响应类型
        /// </summary>
        public EpsonResponseType ResponseType { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public EpsonRobotResponse() { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="rawResponse">原始响应</param>
        public EpsonRobotResponse(string rawResponse)
        {
            RawResponse = rawResponse ?? string.Empty;
            ParseResponse();
        }

        /// <summary>
        /// 解析响应
        /// </summary>
        private void ParseResponse()
        {
            if (string.IsNullOrEmpty(RawResponse))
                return;

            if (RawResponse.StartsWith("#"))
            {
                // 成功响应
                IsSuccess = true;
                ResponseType = EpsonResponseType.Success;
                ParseSuccessResponse();
            }
            else if (RawResponse.StartsWith("!"))
            {
                // 错误响应
                IsSuccess = false;
                ResponseType = EpsonResponseType.Error;
                ParseErrorResponse();
            }
            else
            {
                // 未知响应
                ResponseType = EpsonResponseType.Unknown;
                Data = RawResponse;
            }
        }

        /// <summary>
        /// 解析成功响应
        /// </summary>
        private void ParseSuccessResponse()
        {
            // 格式: #[远程命令],[结果]<CR><LF>
            var parts = RawResponse.Substring(1).Split(',');
            if (parts.Length >= 2)
            {
                Data = string.Join(",", parts, 1, parts.Length - 1);
            }
        }

        /// <summary>
        /// 解析错误响应
        /// </summary>
        private void ParseErrorResponse()
        {
            // 格式: ![远程命令],[错误代码]<CR><LF>
            var parts = RawResponse.Substring(1).Split(',');
            if (parts.Length >= 2 && int.TryParse(parts[1], out int errorCode))
            {
                ErrorCode = errorCode;
                ErrorMessage = GetErrorMessage(errorCode);
            }
        }

        /// <summary>
        /// 获取错误消息
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <returns>错误消息</returns>
        private string GetErrorMessage(int errorCode)
        {
            var errorMessages = new Dictionary<int, string>
            {
                { 10, "远程命令未以$开头" },
                { 11, "远程命令错误或未执行Login" },
                { 12, "远程命令格式错误" },
                { 13, "Login命令密码错误" },
                { 14, "要获取的指定数量超出范围或忽略了要获取的数量或指定了一个字符串参数" },
                { 15, "参数不存在或参数尺寸错误或调用了超出范围的元素" },
                { 19, "请求超时" },
                { 20, "控制器未准备好" },
                { 21, "因为正在运行Execute，所以无法执行其他命令" },
                { 98, "使用全局IP地址时，需要输入密码才能登录" },
                { 99, "系统错误或通信错误" }
            };

            return errorMessages.ContainsKey(errorCode) ? errorMessages[errorCode] : $"未知错误代码: {errorCode}";
        }
    }

    /// <summary>
    /// Epson响应类型枚举
    /// </summary>
    public enum EpsonResponseType
    {
        /// <summary>
        /// 成功响应
        /// </summary>
        Success,

        /// <summary>
        /// 错误响应
        /// </summary>
        Error,

        /// <summary>
        /// 未知响应
        /// </summary>
        Unknown
    }

    /// <summary>
    /// Epson机器人状态类
    /// </summary>
    public class EpsonRobotStatus
    {
        /// <summary>
        /// 是否在TEST模式
        /// </summary>
        public bool IsTestMode { get; set; }

        /// <summary>
        /// 是否在TEACH模式
        /// </summary>
        public bool IsTeachMode { get; set; }

        /// <summary>
        /// 是否在Auto模式（远程输入接受条件）
        /// </summary>
        public bool IsAutoMode { get; set; }

        /// <summary>
        /// 是否有警告
        /// </summary>
        public bool HasWarning { get; set; }

        /// <summary>
        /// 是否有严重错误
        /// </summary>
        public bool HasSevereError { get; set; }

        /// <summary>
        /// 安全门是否打开
        /// </summary>
        public bool IsSafeguardOpen { get; set; }

        /// <summary>
        /// 是否紧急停止
        /// </summary>
        public bool IsEmergencyStop { get; set; }

        /// <summary>
        /// 是否有错误
        /// </summary>
        public bool HasError { get; set; }

        /// <summary>
        /// 是否暂停
        /// </summary>
        public bool IsPaused { get; set; }

        /// <summary>
        /// 是否运行中
        /// </summary>
        public bool IsRunning { get; set; }

        /// <summary>
        /// 是否就绪
        /// </summary>
        public bool IsReady { get; set; }

        /// <summary>
        /// 错误/警告代码
        /// </summary>
        public string ErrorWarningCode { get; set; } = "0000";

        /// <summary>
        /// 状态更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 从状态字符串解析状态
        /// </summary>
        /// <param name="statusString">状态字符串（11位二进制）</param>
        /// <param name="errorCode">错误代码</param>
        public void ParseFromStatusString(string statusString, string errorCode = "0000")
        {
            if (string.IsNullOrEmpty(statusString) || statusString.Length != 11)
                return;

            IsTestMode = statusString[0] == '1';
            IsTeachMode = statusString[1] == '1';
            IsAutoMode = statusString[2] == '1';
            HasWarning = statusString[3] == '1';
            HasSevereError = statusString[4] == '1';
            IsSafeguardOpen = statusString[5] == '1';
            IsEmergencyStop = statusString[6] == '1';
            HasError = statusString[7] == '1';
            IsPaused = statusString[8] == '1';
            IsRunning = statusString[9] == '1';
            IsReady = statusString[10] == '1';

            ErrorWarningCode = errorCode ?? "0000";
            UpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 获取状态描述
        /// </summary>
        /// <returns>状态描述</returns>
        public string GetStatusDescription()
        {
            var descriptions = new List<string>();

            if (IsTestMode) descriptions.Add("TEST模式");
            if (IsTeachMode) descriptions.Add("TEACH模式");
            if (IsAutoMode) descriptions.Add("AUTO模式");
            if (HasWarning) descriptions.Add("警告");
            if (HasSevereError) descriptions.Add("严重错误");
            if (IsSafeguardOpen) descriptions.Add("安全门打开");
            if (IsEmergencyStop) descriptions.Add("紧急停止");
            if (HasError) descriptions.Add("错误");
            if (IsPaused) descriptions.Add("暂停");
            if (IsRunning) descriptions.Add("运行中");
            if (IsReady) descriptions.Add("就绪");

            return descriptions.Count > 0 ? string.Join(", ", descriptions) : "未知状态";
        }
    }

    /// <summary>
    /// 特定命令类型枚举（用于自动化流程）
    /// </summary>
    public enum SpecialCommandType
    {
        /// <summary>
        /// 获取拾取命令
        /// </summary>
        GETPICK,

        /// <summary>
        /// 输入拾取命令
        /// </summary>
        INPICK,

        /// <summary>
        /// 获取NG放置命令
        /// </summary>
        GETNGPUT,

        /// <summary>
        /// NG放置满命令
        /// </summary>
        NGPUTFULL,

        /// <summary>
        /// 获取OK放置命令
        /// </summary>
        GETOKPUT
    }
}
