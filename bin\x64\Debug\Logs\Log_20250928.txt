[2025-09-28 10:01:57.765] [INFO] 程序启动开始
[2025-09-28 10:01:57.767] [INFO] 加载Settings系统配置...
[2025-09-28 10:01:57.776] [INFO] 设置加载成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-28 10:01:57.776] [INFO] Settings系统配置加载完成
[2025-09-28 10:01:57.777] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-28 10:01:57.806] [INFO] 开始初始化各个Manager...
[2025-09-28 10:01:57.806] [INFO] 初始化DMC1000B控制卡...
[2025-09-28 10:01:57.810] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-28 10:01:57.812] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-28 10:01:57.812] [WARN] DMC1000B控制卡初始化失败，相关功能可能无法正常工作
[2025-09-28 10:01:57.812] [INFO] 初始化基础Manager...
[2025-09-28 10:01:57.818] [INFO] IO状态缓存初始化完成
[2025-09-28 10:01:57.821] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-28 10:01:57.821] [WARN] DMC1000BIO管理器初始化失败
[2025-09-28 10:01:57.826] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-28 10:01:57.885] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-28 10:01:57.908] [WARN] DMC1000B电机管理器初始化失败
[2025-09-28 10:01:57.909] [INFO] 初始化系统模式管理器...
[2025-09-28 10:01:57.919] [INFO] 从Settings系统加载安全管理器配置成功
[2025-09-28 10:01:57.923] [INFO] 安全管理器实例已创建
[2025-09-28 10:01:57.928] [INFO] 开始初始化MotorManager...
[2025-09-28 10:01:57.929] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-28 10:01:57.931] [INFO] 当前模式已经是 Manual，无需切换
[2025-09-28 10:01:57.933] [INFO] 模拟初始化运动控制卡
[2025-09-28 10:01:58.148] [INFO] 加载了8个电机的默认配置
[2025-09-28 10:01:58.150] [INFO] 电机监控任务已启动
[2025-09-28 10:01:58.150] [INFO] MotorManager初始化完成
[2025-09-28 10:01:58.151] [INFO] 初始化通信Manager...
[2025-09-28 10:01:58.152] [INFO] 电机监控循环开始
[2025-09-28 10:01:58.159] [INFO] 开始初始化ScannerManager...
[2025-09-28 10:01:58.163] [INFO] 扫描枪配置从Settings系统加载: COM1, 9600, 8, One, None
[2025-09-28 10:01:58.171] [INFO] 串口初始化完成
[2025-09-28 10:01:58.195] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-28 10:02:03.787] [ERROR] 连接扫描枪 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerManager.<ConnectAsync>b__23_1() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 145
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerManager.<<ConnectAsync>b__23_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 139
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-28 10:02:03.791] [WARN] 扫描枪连接失败，但初始化完成，可稍后重试连接
[2025-09-28 10:02:03.791] [INFO] ScannerManager初始化完成
[2025-09-28 10:02:03.799] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-28 10:02:03.800] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-28 10:02:03.801] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-28 10:02:03.804] [INFO] SCARA通信管理器已初始化
[2025-09-28 10:02:03.806] [INFO] ScannerAutoModeManager管理器引用初始化完成
[2025-09-28 10:02:03.808] [INFO] 开始初始化ScannerAutoModeManager...
[2025-09-28 10:02:03.810] [INFO] 开始初始化MultiScannerManager...
[2025-09-28 10:02:03.812] [INFO] 开始初始化扫描枪1...
[2025-09-28 10:02:03.813] [INFO] 从Settings系统加载扫描枪1配置: COM1, 115200
[2025-09-28 10:02:03.815] [INFO] 扫描枪1串口初始化完成
[2025-09-28 10:02:03.815] [INFO] 扫描枪1初始化完成
[2025-09-28 10:02:03.816] [INFO] 开始初始化扫描枪2...
[2025-09-28 10:02:03.816] [INFO] 从Settings系统加载扫描枪2配置: COM2, 115200
[2025-09-28 10:02:03.816] [INFO] 扫描枪2串口初始化完成
[2025-09-28 10:02:03.817] [INFO] 扫描枪2初始化完成
[2025-09-28 10:02:03.817] [INFO] 开始初始化扫描枪3...
[2025-09-28 10:02:03.817] [INFO] 从Settings系统加载扫描枪3配置: COM3, 115200
[2025-09-28 10:02:03.817] [INFO] 扫描枪3串口初始化完成
[2025-09-28 10:02:03.817] [INFO] 扫描枪3初始化完成
[2025-09-28 10:02:03.818] [INFO] MultiScannerManager初始化完成
[2025-09-28 10:02:03.818] [INFO] ScannerAutoModeManager初始化完成
[2025-09-28 10:02:03.822] [INFO] 开始初始化ModbusTcpManager...
[2025-09-28 10:02:03.825] [INFO] Modbus TCP配置: *************:502, SlaveId: 1
[2025-09-28 10:02:03.828] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-28 10:02:08.909] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: *************:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 153
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-28 10:02:08.911] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-28 10:02:08.911] [INFO] ModbusTcpManager初始化完成
[2025-09-28 10:02:08.915] [INFO] 开始初始化EpsonRobotManager...
[2025-09-28 10:02:08.917] [INFO] 从Settings系统加载Epson机器人1配置成功
[2025-09-28 10:02:08.917] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-28 10:02:08.917] [INFO] EpsonRobotManager初始化完成
[2025-09-28 10:02:08.918] [INFO] 初始化视觉Manager...
[2025-09-28 10:02:08.920] [INFO] 开始初始化VisionManager...
[2025-09-28 10:02:08.921] [INFO] 视觉系统配置从Settings系统加载: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms, 置信度阈值=0.8
[2025-09-28 10:02:08.922] [INFO] 模拟初始化相机，索引: 0
[2025-09-28 10:02:09.438] [INFO] 相机初始化成功
[2025-09-28 10:02:09.439] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-28 10:02:09.440] [INFO] 视觉配置加载完成
[2025-09-28 10:02:09.440] [INFO] VisionManager初始化完成
[2025-09-28 10:02:09.440] [INFO] 初始化数据Manager...
[2025-09-28 10:02:09.443] [INFO] 开始初始化StatisticsManager...
[2025-09-28 10:02:09.443] [INFO] 统计管理器配置: 数据目录=Export, 缓存大小=1000, 自动保存间隔=24分钟
[2025-09-28 10:02:09.458] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-28 10:02:09.458] [INFO] 历史数据加载完成
[2025-09-28 10:02:09.458] [INFO] StatisticsManager初始化完成
[2025-09-28 10:02:09.458] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-28 10:02:09.458] [INFO] 所有Manager初始化完成
[2025-09-28 10:02:09.522] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-28 10:02:09.522] [INFO] 创建并缓存面板: vision-position
[2025-09-28 10:02:09.523] [INFO] 主界面布局创建完成
[2025-09-28 10:02:09.524] [INFO] 时间更新定时器初始化完成
[2025-09-28 10:02:09.524] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-28 10:02:09.598] [INFO] 开始初始化系统
[2025-09-28 10:02:09.599] [INFO] 初始化业务逻辑
[2025-09-28 10:02:09.600] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-28 10:02:09.600] [INFO] IO管理器初始化完成
[2025-09-28 10:02:09.600] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-28 10:02:09.669] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-28 10:02:09.670] [INFO] 电机管理器初始化完成
[2025-09-28 10:02:09.671] [INFO] IO事件订阅完成
[2025-09-28 10:02:09.671] [INFO] 电机事件订阅完成
[2025-09-28 10:02:09.671] [INFO] 业务层交互机制建立完成
[2025-09-28 10:02:09.671] [INFO] 业务逻辑初始化完成
[2025-09-28 10:02:09.672] [INFO] 执行UI界面刷新
[2025-09-28 10:02:09.674] [INFO] UI界面刷新完成
[2025-09-28 10:02:09.674] [INFO] 系统初始化完成
[2025-09-28 10:02:12.962] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-28 10:02:12.963] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-28 10:02:12.970] [INFO] UI参数显示已从Settings系统更新
[2025-09-28 10:02:12.970] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-28 10:02:12.970] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-28 10:02:12.970] [INFO] 创建并缓存面板: motor-flip-params
[2025-09-28 10:02:13.537] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-28 10:02:13.537] [INFO] Epson机器人管理器初始化完成
[2025-09-28 10:02:13.573] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-28 10:02:13.574] [INFO] 创建并缓存面板: robot6-control
[2025-09-28 10:02:18.764] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-28 10:02:18.764] [INFO] 创建并缓存面板: scara-comm
[2025-09-28 10:02:23.460] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
[2025-09-28 10:02:23.461] [INFO] 创建并缓存面板: io-read
[2025-09-28 10:02:28.906] [ERROR] IO管理器初始化超时，无法启动IO监控
[2025-09-28 10:02:46.427] [INFO] 生产日志管理面板初始化完成 - 按HTML原型设计
[2025-09-28 10:02:46.427] [INFO] 创建并缓存面板: log-time
[2025-09-28 10:03:05.980] [INFO] 对位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-28 10:03:05.981] [INFO] 创建并缓存面板: vision-align
[2025-09-28 10:03:19.847] [INFO] IO输出控制面板初始化完成 - 支持基础IO和扩展IO
[2025-09-28 10:03:19.848] [INFO] 创建并缓存面板: io-write
[2025-09-28 10:03:25.293] [ERROR] IO管理器初始化超时，无法启动IO监控
[2025-09-28 10:07:46.285] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-28 10:07:46.287] [INFO] 保存Settings系统配置...
[2025-09-28 10:07:46.290] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-28 10:07:46.290] [INFO] Settings系统配置保存完成
[2025-09-28 10:07:46.292] [INFO] 开始按顺序释放各个Manager资源...
[2025-09-28 10:07:46.293] [INFO] 清理UI资源和面板缓存...
[2025-09-28 10:07:46.293] [INFO] 时间更新定时器资源已释放
[2025-09-28 10:07:46.294] [INFO] 开始清理面板缓存，共8个面板
[2025-09-28 10:07:46.302] [INFO] 翻转电机控制面板资源释放完成
[2025-09-28 10:07:46.320] [INFO] 面板缓存字典已清空
[2025-09-28 10:07:46.321] [INFO] 面板缓存清理完成
[2025-09-28 10:07:46.321] [INFO] UI资源清理完成
[2025-09-28 10:07:46.321] [INFO] 释放工作流管理器资源...
[2025-09-28 10:07:46.323] [INFO] 开始释放WorkflowManager资源...
[2025-09-28 10:07:46.325] [INFO] 工作流已经处于空闲状态
[2025-09-28 10:07:46.326] [INFO] 开始取消订阅各Manager事件...
[2025-09-28 10:07:46.326] [INFO] Manager事件取消订阅完成
[2025-09-28 10:07:46.326] [INFO] WorkflowManager资源释放完成
[2025-09-28 10:07:46.326] [INFO] 工作流管理器资源释放完成
[2025-09-28 10:07:46.326] [INFO] 释放启动自检管理器资源...
[2025-09-28 10:07:46.328] [INFO] 开始释放启动自检管理器资源...
[2025-09-28 10:07:46.328] [INFO] 启动自检管理器资源释放完成
[2025-09-28 10:07:46.328] [INFO] 启动自检管理器资源释放完成
[2025-09-28 10:07:46.328] [INFO] 释放DMC1000B电机管理器资源...
[2025-09-28 10:07:46.330] [INFO] 开始释放DMC1000B资源...
[2025-09-28 10:07:46.336] [INFO] 停止所有电机部分失败
[2025-09-28 10:07:46.337] [INFO] DMC1000B资源释放完成
[2025-09-28 10:07:46.338] [INFO] DMC1000B电机管理器资源释放完成
[2025-09-28 10:07:46.338] [INFO] 释放DMC1000BIO管理器资源...
[2025-09-28 10:07:46.340] [INFO] DMC1000BIO管理器资源释放完成
[2025-09-28 10:07:46.340] [INFO] 释放其他Manager资源...
[2025-09-28 10:07:46.342] [INFO] 开始释放MotorManager资源...
[2025-09-28 10:07:46.346] [INFO] 电机0停止运动
[2025-09-28 10:07:46.347] [INFO] 电机5停止运动
[2025-09-28 10:07:46.347] [INFO] 电机2停止运动
[2025-09-28 10:07:46.347] [INFO] 电机6停止运动
[2025-09-28 10:07:46.348] [INFO] 电机1停止运动
[2025-09-28 10:07:46.349] [INFO] 电机4停止运动
[2025-09-28 10:07:46.349] [INFO] 电机3停止运动
[2025-09-28 10:07:46.349] [INFO] 电机7停止运动
[2025-09-28 10:07:46.349] [INFO] 所有电机已停止
[2025-09-28 10:07:46.385] [INFO] 电机监控循环被取消
[2025-09-28 10:07:46.385] [INFO] 电机监控循环结束
[2025-09-28 10:07:46.386] [INFO] 电机监控任务已停止
[2025-09-28 10:07:46.386] [INFO] 模拟释放运动控制卡资源
[2025-09-28 10:07:46.386] [INFO] MotorManager资源释放完成
[2025-09-28 10:07:46.387] [INFO] 开始释放ScannerManager资源...
[2025-09-28 10:07:46.389] [INFO] 扫描枪状态变更: Disconnected - 扫描枪连接已断开
[2025-09-28 10:07:46.389] [INFO] ScannerManager资源释放完成
[2025-09-28 10:07:46.391] [INFO] 开始释放ModbusTcpManager资源...
[2025-09-28 10:07:46.393] [INFO] Modbus TCP状态变更: Disconnected - Modbus TCP连接已断开
[2025-09-28 10:07:46.393] [INFO] Modbus TCP连接已断开
[2025-09-28 10:07:46.393] [INFO] ModbusTcpManager资源释放完成
[2025-09-28 10:07:46.395] [INFO] 开始释放EpsonRobotManager资源...
[2025-09-28 10:07:46.399] [INFO] EpsonRobotManager资源释放完成
[2025-09-28 10:07:46.401] [INFO] 开始释放EpsonRobotManager2资源...
[2025-09-28 10:07:46.405] [INFO] EpsonRobotManager2资源释放完成
[2025-09-28 10:07:46.406] [INFO] 开始释放VisionManager资源...
[2025-09-28 10:07:46.408] [INFO] 模拟释放相机资源
[2025-09-28 10:07:46.408] [INFO] VisionManager资源释放完成
[2025-09-28 10:07:46.410] [INFO] 开始释放StatisticsManager资源...
[2025-09-28 10:07:46.410] [INFO] 自动保存任务已停止
[2025-09-28 10:07:46.415] [INFO] StatisticsManager资源释放完成
[2025-09-28 10:07:46.415] [INFO] 其他Manager资源释放完成
[2025-09-28 10:07:46.415] [INFO] 释放DMC1000B控制卡资源...
[2025-09-28 10:07:46.416] [INFO] DMC1000B控制卡未初始化，无需释放
[2025-09-28 10:07:46.417] [INFO] DMC1000B控制卡资源释放完成
[2025-09-28 10:07:46.417] [INFO] 所有Manager资源释放流程完成
[2025-09-28 10:07:46.418] [INFO] 所有资源释放完成，程序即将退出
[2025-09-28 10:07:46.419] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-28 10:53:45.491] [INFO] 程序启动开始
[2025-09-28 10:53:45.492] [INFO] 加载Settings系统配置...
[2025-09-28 10:53:45.499] [INFO] 设置加载成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-28 10:53:45.500] [INFO] Settings系统配置加载完成
[2025-09-28 10:53:45.500] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-28 10:53:45.525] [INFO] 开始初始化各个Manager...
[2025-09-28 10:53:45.526] [INFO] 初始化DMC1000B控制卡...
[2025-09-28 10:53:45.531] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-28 10:53:45.532] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-28 10:53:45.533] [WARN] DMC1000B控制卡初始化失败，相关功能可能无法正常工作
[2025-09-28 10:53:45.533] [INFO] 初始化基础Manager...
[2025-09-28 10:53:45.538] [INFO] IO状态缓存初始化完成
[2025-09-28 10:53:45.540] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-28 10:53:45.541] [WARN] DMC1000BIO管理器初始化失败
[2025-09-28 10:53:45.545] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-28 10:53:45.587] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-28 10:53:45.602] [WARN] DMC1000B电机管理器初始化失败
[2025-09-28 10:53:45.602] [INFO] 初始化系统模式管理器...
[2025-09-28 10:53:45.612] [INFO] 从Settings系统加载安全管理器配置成功
[2025-09-28 10:53:45.615] [INFO] 安全管理器实例已创建
[2025-09-28 10:53:45.618] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-28 10:53:45.619] [INFO] 当前模式已经是 Manual，无需切换
[2025-09-28 10:53:45.620] [INFO] 开始初始化MotorManager...
[2025-09-28 10:53:45.622] [INFO] 模拟初始化运动控制卡
[2025-09-28 10:53:45.837] [INFO] 加载了8个电机的默认配置
[2025-09-28 10:53:45.840] [INFO] 电机监控任务已启动
[2025-09-28 10:53:45.841] [INFO] MotorManager初始化完成
[2025-09-28 10:53:45.842] [INFO] 初始化通信Manager...
[2025-09-28 10:53:45.847] [INFO] 电机监控循环开始
[2025-09-28 10:53:45.852] [INFO] 开始初始化ScannerManager...
[2025-09-28 10:53:45.860] [INFO] 扫描枪配置从Settings系统加载: COM1, 9600, 8, One, None
[2025-09-28 10:53:45.865] [INFO] 串口初始化完成
[2025-09-28 10:53:45.868] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-28 10:53:49.069] [ERROR] 连接扫描枪 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerManager.<ConnectAsync>b__23_1() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 145
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerManager.<<ConnectAsync>b__23_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 139
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-28 10:53:49.072] [WARN] 扫描枪连接失败，但初始化完成，可稍后重试连接
[2025-09-28 10:53:49.072] [INFO] ScannerManager初始化完成
[2025-09-28 10:53:49.078] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-28 10:53:49.079] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-28 10:53:49.080] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-28 10:53:49.081] [INFO] SCARA通信管理器已初始化
[2025-09-28 10:53:49.083] [INFO] ScannerAutoModeManager管理器引用初始化完成
[2025-09-28 10:53:49.085] [INFO] 开始初始化ScannerAutoModeManager...
[2025-09-28 10:53:49.087] [INFO] 开始初始化MultiScannerManager...
[2025-09-28 10:53:49.089] [INFO] 开始初始化扫描枪1...
[2025-09-28 10:53:49.090] [INFO] 从Settings系统加载扫描枪1配置: COM1, 115200
[2025-09-28 10:53:49.092] [INFO] 扫描枪1串口初始化完成
[2025-09-28 10:53:49.093] [INFO] 扫描枪1初始化完成
[2025-09-28 10:53:49.093] [INFO] 开始初始化扫描枪2...
[2025-09-28 10:53:49.094] [INFO] 从Settings系统加载扫描枪2配置: COM2, 115200
[2025-09-28 10:53:49.094] [INFO] 扫描枪2串口初始化完成
[2025-09-28 10:53:49.095] [INFO] 扫描枪2初始化完成
[2025-09-28 10:53:49.096] [INFO] 开始初始化扫描枪3...
[2025-09-28 10:53:49.096] [INFO] 从Settings系统加载扫描枪3配置: COM3, 115200
[2025-09-28 10:53:49.096] [INFO] 扫描枪3串口初始化完成
[2025-09-28 10:53:49.097] [INFO] 扫描枪3初始化完成
[2025-09-28 10:53:49.097] [INFO] MultiScannerManager初始化完成
[2025-09-28 10:53:49.098] [INFO] ScannerAutoModeManager初始化完成
[2025-09-28 10:53:49.100] [INFO] 开始初始化ModbusTcpManager...
[2025-09-28 10:53:49.102] [INFO] Modbus TCP配置: *************:502, SlaveId: 1
[2025-09-28 10:53:49.105] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-28 10:53:54.152] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: *************:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 153
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-28 10:53:54.154] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-28 10:53:54.154] [INFO] ModbusTcpManager初始化完成
[2025-09-28 10:53:54.158] [INFO] 开始初始化EpsonRobotManager...
[2025-09-28 10:53:54.159] [INFO] 从Settings系统加载Epson机器人1配置成功
[2025-09-28 10:53:54.159] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-28 10:53:54.160] [INFO] EpsonRobotManager初始化完成
[2025-09-28 10:53:54.160] [INFO] 初始化视觉Manager...
[2025-09-28 10:53:54.163] [INFO] 开始初始化VisionManager...
[2025-09-28 10:53:54.164] [INFO] 视觉系统配置从Settings系统加载: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms, 置信度阈值=0.8
[2025-09-28 10:53:54.165] [INFO] 模拟初始化相机，索引: 0
[2025-09-28 10:53:54.678] [INFO] 相机初始化成功
[2025-09-28 10:53:54.684] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-28 10:53:54.686] [INFO] 视觉配置加载完成
[2025-09-28 10:53:54.687] [INFO] VisionManager初始化完成
[2025-09-28 10:53:54.688] [INFO] 初始化数据Manager...
[2025-09-28 10:53:54.701] [INFO] 开始初始化StatisticsManager...
[2025-09-28 10:53:54.703] [INFO] 统计管理器配置: 数据目录=Export, 缓存大小=1000, 自动保存间隔=24分钟
[2025-09-28 10:53:54.728] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-28 10:53:54.728] [INFO] 历史数据加载完成
[2025-09-28 10:53:54.728] [INFO] StatisticsManager初始化完成
[2025-09-28 10:53:54.728] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-28 10:53:54.728] [INFO] 所有Manager初始化完成
[2025-09-28 10:53:54.762] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-28 10:53:54.763] [INFO] 创建并缓存面板: vision-position
[2025-09-28 10:53:54.763] [INFO] 主界面布局创建完成
[2025-09-28 10:53:54.765] [INFO] 时间更新定时器初始化完成
[2025-09-28 10:53:54.765] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-28 10:53:54.844] [INFO] 开始初始化系统
[2025-09-28 10:53:54.845] [INFO] 初始化业务逻辑
[2025-09-28 10:53:54.846] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-28 10:53:54.846] [INFO] IO管理器初始化完成
[2025-09-28 10:53:54.846] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-28 10:53:54.912] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-28 10:53:54.913] [INFO] 电机管理器初始化完成
[2025-09-28 10:53:54.914] [INFO] IO事件订阅完成
[2025-09-28 10:53:54.914] [INFO] 电机事件订阅完成
[2025-09-28 10:53:54.914] [INFO] 业务层交互机制建立完成
[2025-09-28 10:53:54.914] [INFO] 业务逻辑初始化完成
[2025-09-28 10:53:54.915] [INFO] 执行UI界面刷新
[2025-09-28 10:53:54.919] [INFO] UI界面刷新完成
[2025-09-28 10:53:54.919] [INFO] 系统初始化完成
[2025-09-28 10:54:01.502] [INFO] 外部设备状态菜单已显示
[2025-09-28 10:54:02.381] [INFO] SCARA模拟器: 控件初始化完成
[2025-09-28 10:54:02.382] [INFO] SCARA模拟器: 模拟器窗体初始化完成
[2025-09-28 10:54:02.383] [INFO] SCARA模拟器窗体已创建
[2025-09-28 10:54:02.449] [INFO] SCARA模拟器: 模拟器窗体已显示并刷新
[2025-09-28 10:54:02.449] [INFO] SCARA机器人模拟器已显示
[2025-09-28 10:54:02.463] [INFO] SCARA模拟器: 模拟器窗体已显示，定时器已启动
[2025-09-28 10:54:04.130] [INFO] SCARA模拟器: 字段 L_moto_ready 设置为 True
[2025-09-28 10:54:04.716] [INFO] SCARA模拟器: 字段 L_moto_ready 设置为 False
[2025-09-28 10:54:06.604] [INFO] SCARA模拟器: 字段 L_moto_ready 设置为 True
[2025-09-28 10:54:07.260] [INFO] SCARA模拟器: 字段 L_moto_ready 设置为 False
[2025-09-28 10:54:16.733] [INFO] SCARA模拟器: 字段 all_save 设置为 False
[2025-09-28 10:54:16.744] [INFO] SCARA模拟器: 确认阅读操作完成，复位了 1 个字段
[2025-09-28 10:54:23.516] [INFO] SCARA模拟器: 字段 L_moto_ready 设置为 True
[2025-09-28 10:54:24.668] [INFO] SCARA模拟器: 字段 L_moto_ready 设置为 False
[2025-09-28 10:54:24.671] [INFO] SCARA模拟器: 确认阅读操作完成，复位了 1 个字段
[2025-09-28 10:54:31.889] [INFO] 所有SCARA通信字段已复位，0个字段发生变化
[2025-09-28 10:54:31.890] [INFO] SCARA模拟器: 全部复位操作完成
[2025-09-28 10:54:36.954] [INFO] SCARA模拟器: 开始执行模拟工作流程序列
[2025-09-28 10:54:36.954] [INFO] 所有SCARA通信字段已复位，0个字段发生变化
[2025-09-28 10:54:37.946] [INFO] SCARA模拟器: 字段 L_moto_ready 设置为 True
[2025-09-28 10:54:37.951] [INFO] SCARA模拟器: 字段 R_moto_ready 设置为 True
[2025-09-28 10:54:38.954] [INFO] SCARA模拟器: 字段 L_position_Arrived 设置为 True
[2025-09-28 10:54:38.958] [INFO] SCARA模拟器: 字段 R_position_Arrived 设置为 True
[2025-09-28 10:54:39.952] [INFO] SCARA模拟器: 字段 L_gripper_ok 设置为 True
[2025-09-28 10:54:39.956] [INFO] SCARA模拟器: 字段 R_gripper_ok 设置为 True
[2025-09-28 10:54:40.949] [INFO] SCARA模拟器: 字段 L_Angle_ok 设置为 True
[2025-09-28 10:54:40.952] [INFO] SCARA模拟器: 字段 R_Angle_ok 设置为 True
[2025-09-28 10:54:41.946] [INFO] SCARA模拟器: 字段 L_safe_ok 设置为 True
[2025-09-28 10:54:41.952] [INFO] SCARA模拟器: 字段 R_safe_ok 设置为 True
[2025-09-28 10:54:42.953] [INFO] SCARA模拟器: 字段 ML_dataget_ok 设置为 True
[2025-09-28 10:54:42.955] [INFO] SCARA模拟器: 字段 MR_dataget_ok 设置为 True
[2025-09-28 10:54:43.952] [INFO] SCARA模拟器: 字段 L_dataget_ok 设置为 True
[2025-09-28 10:54:43.961] [INFO] SCARA模拟器: 字段 R_dataget_ok 设置为 True
[2025-09-28 10:54:44.556] [INFO] SCARA模拟器: 模拟工作流程序列执行完成
[2025-09-28 10:54:44.947] [INFO] SCARA模拟器: 字段 L_moto_finish 设置为 True
[2025-09-28 10:54:44.952] [INFO] SCARA模拟器: 字段 R_moto_finish 设置为 True
[2025-09-28 10:54:49.176] [INFO] SCARA模拟器: 模拟器窗体已隐藏，定时器已停止
[2025-09-28 10:54:50.476] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-28 10:54:50.477] [INFO] 保存Settings系统配置...
[2025-09-28 10:54:50.481] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-28 10:54:50.481] [INFO] Settings系统配置保存完成
[2025-09-28 10:54:50.484] [INFO] 开始按顺序释放各个Manager资源...
[2025-09-28 10:54:50.484] [INFO] 清理UI资源和面板缓存...
[2025-09-28 10:54:50.485] [INFO] 时间更新定时器资源已释放
[2025-09-28 10:54:50.486] [INFO] 开始清理面板缓存，共1个面板
[2025-09-28 10:54:50.494] [INFO] 面板缓存字典已清空
[2025-09-28 10:54:50.494] [INFO] 面板缓存清理完成
[2025-09-28 10:54:50.495] [INFO] UI资源清理完成
[2025-09-28 10:54:50.495] [INFO] 释放工作流管理器资源...
[2025-09-28 10:54:50.498] [INFO] 开始释放WorkflowManager资源...
[2025-09-28 10:54:50.500] [INFO] 工作流已经处于空闲状态
[2025-09-28 10:54:50.501] [INFO] 开始取消订阅各Manager事件...
[2025-09-28 10:54:50.501] [INFO] Manager事件取消订阅完成
[2025-09-28 10:54:50.501] [INFO] WorkflowManager资源释放完成
[2025-09-28 10:54:50.501] [INFO] 工作流管理器资源释放完成
[2025-09-28 10:54:50.501] [INFO] 释放启动自检管理器资源...
[2025-09-28 10:54:50.502] [INFO] 开始释放启动自检管理器资源...
[2025-09-28 10:54:50.503] [INFO] 启动自检管理器资源释放完成
[2025-09-28 10:54:50.503] [INFO] 启动自检管理器资源释放完成
[2025-09-28 10:54:50.503] [INFO] 释放DMC1000B电机管理器资源...
[2025-09-28 10:54:50.505] [INFO] 开始释放DMC1000B资源...
[2025-09-28 10:54:50.511] [INFO] 停止所有电机部分失败
[2025-09-28 10:54:50.512] [INFO] DMC1000B资源释放完成
[2025-09-28 10:54:50.512] [INFO] DMC1000B电机管理器资源释放完成
[2025-09-28 10:54:50.512] [INFO] 释放DMC1000BIO管理器资源...
[2025-09-28 10:54:50.515] [INFO] DMC1000BIO管理器资源释放完成
[2025-09-28 10:54:50.515] [INFO] 释放其他Manager资源...
[2025-09-28 10:54:50.517] [INFO] 开始释放MotorManager资源...
[2025-09-28 10:54:50.522] [INFO] 电机0停止运动
[2025-09-28 10:54:50.522] [INFO] 电机1停止运动
[2025-09-28 10:54:50.523] [INFO] 电机5停止运动
[2025-09-28 10:54:50.523] [INFO] 电机2停止运动
[2025-09-28 10:54:50.523] [INFO] 电机3停止运动
[2025-09-28 10:54:50.523] [INFO] 电机4停止运动
[2025-09-28 10:54:50.524] [INFO] 电机7停止运动
[2025-09-28 10:54:50.524] [INFO] 电机6停止运动
[2025-09-28 10:54:50.524] [INFO] 所有电机已停止
[2025-09-28 10:54:50.551] [INFO] 电机监控循环被取消
[2025-09-28 10:54:50.552] [INFO] 电机监控循环结束
[2025-09-28 10:54:50.552] [INFO] 电机监控任务已停止
[2025-09-28 10:54:50.552] [INFO] 模拟释放运动控制卡资源
[2025-09-28 10:54:50.553] [INFO] MotorManager资源释放完成
[2025-09-28 10:54:50.554] [INFO] 开始释放ScannerManager资源...
[2025-09-28 10:54:50.556] [INFO] 扫描枪状态变更: Disconnected - 扫描枪连接已断开
[2025-09-28 10:54:50.556] [INFO] ScannerManager资源释放完成
[2025-09-28 10:54:50.557] [INFO] 开始释放ModbusTcpManager资源...
[2025-09-28 10:54:50.559] [INFO] Modbus TCP状态变更: Disconnected - Modbus TCP连接已断开
[2025-09-28 10:54:50.560] [INFO] Modbus TCP连接已断开
[2025-09-28 10:54:50.560] [INFO] ModbusTcpManager资源释放完成
[2025-09-28 10:54:50.562] [INFO] 开始释放EpsonRobotManager资源...
[2025-09-28 10:54:50.567] [INFO] EpsonRobotManager资源释放完成
[2025-09-28 10:54:50.569] [INFO] 开始释放EpsonRobotManager2资源...
[2025-09-28 10:54:50.573] [INFO] EpsonRobotManager2资源释放完成
[2025-09-28 10:54:50.574] [INFO] 开始释放VisionManager资源...
[2025-09-28 10:54:50.576] [INFO] 模拟释放相机资源
[2025-09-28 10:54:50.577] [INFO] VisionManager资源释放完成
[2025-09-28 10:54:50.577] [INFO] 开始释放StatisticsManager资源...
[2025-09-28 10:54:50.578] [INFO] 自动保存任务已停止
[2025-09-28 10:54:50.582] [INFO] StatisticsManager资源释放完成
[2025-09-28 10:54:50.582] [INFO] 其他Manager资源释放完成
[2025-09-28 10:54:50.582] [INFO] 释放DMC1000B控制卡资源...
[2025-09-28 10:54:50.584] [INFO] DMC1000B控制卡未初始化，无需释放
[2025-09-28 10:54:50.584] [INFO] DMC1000B控制卡资源释放完成
[2025-09-28 10:54:50.584] [INFO] 释放SCARA模拟器资源...
[2025-09-28 10:54:50.584] [INFO] SCARA模拟器: 模拟器资源已释放
[2025-09-28 10:54:50.632] [ERROR] 释放SCARA模拟器资源失败
异常详情: 线程间操作无效: 从不是创建控件“ScaraRobotSimulatorForm”的线程访问它。
堆栈跟踪:    在 System.Windows.Forms.Control.get_Handle()
   在 System.Windows.Forms.Control.SendMessage(Int32 msg, Int32 wparam, Int32 lparam)
   在 System.Windows.Forms.Form.Close()
   在 MyHMI.UI.MainForm.<DisposeAllManagersAsync>d__90.MoveNext() 位置 E:\projects\C#_projects\HR2\UI\MainForm.cs:行号 1990
[2025-09-28 10:54:50.635] [INFO] 所有Manager资源释放流程完成
[2025-09-28 10:54:50.635] [INFO] 所有资源释放完成，程序即将退出
[2025-09-28 10:54:50.636] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-28 10:54:50.636] [INFO] SCARA模拟器: 模拟器窗体已隐藏，定时器已停止
[2025-09-28 10:54:52.731] [INFO] 程序正在关闭，开始释放所有Manager资源...
