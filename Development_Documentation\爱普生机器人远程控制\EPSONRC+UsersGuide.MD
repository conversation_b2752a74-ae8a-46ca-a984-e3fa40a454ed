以下是根据您提供的 PDF 文档内容（第 435-443 页，对应手册 417-425 页）转换的 Markdown 格式：

```markdown
# 12. I/O设置

## 12.2 远程以太网

远程以太网能够通过以太网(TCP/IP)发送远程命令，从外部设备上控制机器人和控制器。

**安全增强**：从以下固件版本开始，为控制器和 PC 之间的连接增加了密码验证功能。
*   F/W: Ver.7.4.8.x (T系列/VT系列之外)
*   F/W: Ver.7.4.58.x (T系列/VT系列)

有关详细信息，请参考：
*   1.9 控制器连接以太网的安全性
*   1.10 紧凑型视觉系统 CV2-A的以太网连接安全

---

### 12.2.1 远程以太网配置

若要启用远程以太网功能，请按以下步骤配置：

1.  从 **[设置]** 菜单中选择 **[系统配置]**，然后选择 **[远程控制] - [以太网]** 页面。
2.  配置远程以太网控制所需的项目。
3.  点击 **<应用>** 保存设置，然后点击 **<关闭>**。

> **注意**：有关此对话框设置的详细信息，请参阅“5.13.2 [系统配置](设置菜单) – [设置]-[系统配置]-[远程控制]”。
> 选择“只用于监控”时，不必遵循控制设备的设置，仅需使用远程以太网控制获取数值。

---

### 12.2.2 控制设备配置

将控制设备设置为“远程以太网”的步骤：

1.  从 **[设置]** 菜单中选择 **[系统配置]**，然后选择 **[控制器] - [配置]**。
2.  在 **[控制设置]** 框中选择 **“远程以太网”**。
3.  点击 **<应用>** 按钮保存设置，然后点击 **<关闭>**。

> **注意**：有关此对话框设置的详细信息，请参阅“5.13.2 [系统配置](设置菜单) - [设置]-[系统配置]-[设置]”。

---

### 12.2.3 执行远程以太网控制

通过以下步骤设置远程控制：

1.  从客户端设备连接到控制器远程以太网中指定的端口。
2.  将远程以太网中设置的密码作为参数发送 `Login` 命令。
3.  执行远程命令前，客户端设备须等到 `Auto` (通过 `GetStatus` 命令响应获取) 为 `ON` 为止。
4.  此时，控制器将接受远程命令。每个命令会执行其自身的输入接受条件检查。

---

### 12.2.4 调试远程以太网控制

在 EPSON RC+ 7.0 开发环境中调试程序的方法：

1.  照常创建一个程序。
2.  打开运行窗口，点击 **<激活远程 ETH>** 按钮。
    *   > **注意**：如果只是通过远程以太网控制获取数值（即仅用于监控），则不会显示 `<激活远程 ETH>` 按钮。此时应点击指定为控制设备的设备的 `<开始>` 按钮。
3.  此时，将接受远程命令。运行窗口中的断点设置和输出功能可用。

**连接超时**：
*   如果在 5 分钟内未通过外部设备 `Login`，连接将自动切断。
*   `Login` 后，如果在远程以太网的超时时间内未发送命令，连接也将被切断。此时需要重新建立连接。

**错误处理**：
*   如果发生错误，在执行操作命令之前必须先执行 `Reset` 命令以清除错误条件。
*   若要通过监控从外部设备上清除错误条件，则执行 `GetStatus` 和 `Reset` 命令。

> **重要警告**：
> ■ 如果在 **[超时]** 框中设置“0”，则超时时间为无限。在这种情况下，该任务会继续执行，即使没有来自客户端的通信。这意味着机器人可能会继续移动并给设备造成意外损坏。**务必确保使用除通信以外的方式来停止该任务。**

---

### 12.2.5 远程以太网命令

**命令格式**：`$远程命令{,参数....}<CR><LF>`
> **注意**：对于带参数的远程命令，使用逗号 `,` 分隔命令和参数。在逗号前后以及参数字符前后请勿输入空格。

| 远程命令 | 参数 | 内容 | 输入接受条件(\*1) |
| :--- | :--- | :--- | :--- |
| `Login` | 密码 | 启动控制器远程以太网功能，通过密码验证。正确登录后，可执行命令直至退出。 | 随时可用(\*2) |
| `Logout` | (无) | 退出控制器远程以太网功能。退出登录后，需再次执行 `Login` 命令来启动远程以太网功能。在任务执行期间退出会导致错误。 | 随时可用(\*2) |
| `Start` | 函数编号 | 执行指定编号的函数 (\*3)(\*11) | Auto开, Ready开, Error关, EStop关, Safeguard开 |
| `Stop` | (无) | 停止所有任务和命令。 | Auto开 |
| `Pause` | (无) | 暂停所有任务 (\*4) | Auto开, Running开 |
| `Continue` | (无) | 继续已暂停的任务 | Auto开, Paused开 |
| `Reset` | (无) | 清除紧急停止和错误 (\*5) | Auto开, Ready开 |
| `SetMotorsOn` | 机器人编号 | 打开机器人电机 (\*6)(\*7) | Auto开, Ready开, EStop关, Safeguard关 |
| `SetMotorsOff` | 机器人编号 | 关闭机器人电机 (\*7) | Auto开, Ready开 |
| `SetCurRobot` | 机器人编号 | 选择机器人 | Auto开, Ready开 |
| `GetCurRobot` | (无) | 获取当前的机器人编号 | 随时可用(\*2) |
| `Home` | 机器人编号 | 将机器人手臂移动到由用户定义的起始点位置 | Auto开, Ready开, Error关, EStop关, Safeguard关 |
| `GetIO` | I/O位号 | 获得指定的 I/O 位 | 随时可用(\*2) |
| `SetIO` | I/O位号和值 | 设置 I/O 指定位 (1：打开, 0：关闭) | Ready开 |
| `GetIOByte` | I/O位号 | 获得指定的 I/O 端口 (8位) | 随时可用(\*1) |
| `SetIOByte` | I/O端口号和值 | 设置 I/O 指定端口 (8位) | Ready开 |
| `GetIOWord` | I/O字端口号 | 获得指定的 I/O 字端口 (16位) | 随时可用(\*2) |
| `SetIOWord` | I/O字端口号和值 | 设置 I/O 指定字端口 (16位) | Auto开, Ready开 |
| `GetMemIO` | 内存 I/O位号 | 获取指定的内存 I/O 位 | 随时可用(\*2) |
| `SetMemIO` | 内存 I/O位号和值 | 设置指定的内存 I/O 位 (1：打开, 0：关闭) | Auto开, Ready开 |
| `GetMemIOByte` | 内存 I/O端口号 | 获取指定内存 I/O 端口 (8位) | 随时可用(\*2) |
| `SetMemIOByte` | 内存 I/O端口号和值 | 设置指定的内存 I/O 端口 (8位) | Auto开, Ready开 |
| `GetMemIOWord` | 内存 I/O字端口号 | 获得指定的内存 I/O 字端口 (16位) | 随时可用(\*2) |
| `SetMemIOWord` | 内存 I/O字端口号和值 | 设置指定的内存 I/O 字端口 (16位) | Auto开, Ready开 |
| `GetVariable` | 参数名称{,类型} | 获取备份(全局保留)参数的值 (\*8) | 随时可用(\*2) |
| `GetVariable` | [参数名称] (数组元素), [参数名称类型], [获取的编号] | 获取备份(全局保留)数组参数的值(\*9) | (见注释) |
| `SetVariable` | 参数名称和值{,类型} | 设置备份(全局保留)参数的值 (\*8) | Auto开, Ready开 |
| `GetStatus` | (无) | 获取控制器的状态 | 随时可用(\*1) |
| `Execute` | 命令字符串 | 执行命令 (\*10)(\*11) | Auto开, Ready开, Error关, EStop关, Safeguard关 |
| `Abort` | (无) | 中止命令的执行 | Auto开 |
| `GetAlm` | (无) | 获得报警状态 (\*12) | 随时可用(\*2) |
| `ResetAlm` | 报警编号 | 重置指定报警编号的报警 (\*12) | Auto开, Ready开 |

**注释**：
*   (\*1) `GetStatus` 的控制器状态位。
*   (\*2) 如果满足以下任一条件，则“随时可用”：
    *   “远程以太网”被设为控制设备。
    *   “远程以太网”未设为控制设备，而是设为“用于监控”。
*   (\*3) 执行 `Main[函数编号]` 中指定的函数。函数编号对应关系如下：
    | 函数名称 | 函数编号 |
    | :--- | :--- |
    | Main | 0 |
    | Main1 | 1 |
    | Main2 | 2 |
    | ... | ... |
    | Main63 | 63 |
*   (\*4) `Pause` 命令不适用于 `NoPause` 任务和 `NoEmgAbort` 任务。
*   (\*5) `Reset` 命令将关闭 I/O 输出并初始化机器人参数。
*   (\*6) `SetMotorsOn` 命令将初始化机器人参数。
*   (\*7) 如果机器人编号指定为“0”，将操作所有机器人。如需操作具体机器人，应指定目标机器人的编号 (1 到 16)。
*   (\*8) 参数类型指 `{Boolean | Byte | Double | Integer | Long | Real | String | Short | UByte | UShort | Int32 | UInt32 | Int64 | UInt64}`。指定类型用于参数名称和类型相同时的备份参数；未指定类型用于参数名称相同时的备份参数。
*   (\*9) 对于数组元素，应按以下方式指定：
    *   **一维数组**: `参数名称(0)` (从头部获取) 或 `参数名称(元素编号)`。
    *   **二维数组**: `参数名称(0, 0)` (从头部获取) 或 `参数名称(元素编号1, 元素编号2)`。
    *   **三维数组**: `参数名称(0, 0, 0)` (从头部获取) 或 `参数名称(元素编号1, 元素编号2, 元素编号3)`。
    *   不能忽略参数类型和要获取的编号。
    *   不能为参数类型指定字符串。
    *   获取的可用编号最大为 100。如果指定超出数组元素数量的编号，将出现错误。
    *   示例: `$GetVariable,gby2(3,0),Byte,3` 将获取字节类型二维数组 `gby2` 的 `gby2(3,0)`, `gby2(3,1)`, `gby2(3,2)`。
*   (\*10) 使用双引号 `"` 指定命令和参数。执行的命令字符串不能超过 256 字节，执行结果字符串不能超过 4060 字节。机器人动作命令将被执行到所选的机器人上，执行前请用 `GetCurRobot` 检查。
    *   **在 `Execute` 中可用的命令**：`Abort`, `GetStatus`, `SetIO`, `SetIOByte`, `SetIOWord`, `SetMemIO`, `SetMemIOByte`, `SetMemIOWord`。
    *   如果 `Execute` 的执行命令与输出命令 (`SetIO`, `SetIOByte`, `SetIOWord`, `SetMemIO`, `SetMemIOByte`, `SetMemIOWord`) 相同且同时执行，后执行的命令将导致错误。请确保在执行后使用 `GetStatus` 检查结果。
*   (\*11) 当执行包含与 PC 功能 (PC 文件、PC RS-232C、数据库访问、DLL 调用) 相关的命令时，务必在连接 EPSON RC+ 7.0 的状态下执行，否则会报错。
*   (\*12) 有关报警的详细信息，请参阅相关维护手册。
*   (\*13) `Execute` 命令的“命令字符串”参数需用双引号 `"` 括起。如参数中包含 `"`，在 SPEL+ 语言中需使用 `Chr$(34)` 表示。

---

### 12.2.6 监控命令

如果远程以太网控制未设置为控制设备，但设为用于监控目的时，则只能执行以下命令：

*   `Login`
*   `Logout`
*   `GetIO`
*   `GetIOByte`
*   `GetIOWord`
*   `GetMemIO`
*   `GetMemIOByte`
*   `GetMemIOWord`
*   `GetVariable`
*   `GetStatus`
*   `GetCurRobot`
*   `GetAlm`

---

### 12.2.7 响应

#### 成功响应

如果控制器接收到正确的命令，将显示下列格式的响应：

| 命令类型 | 响应格式 | 说明 |
| :--- | :--- | :--- |
| **获取值的命令** (除 `GetIO`, `GetVariable`, `GetStatus`) | `#[远程命令],[0]<CR><LF>` | 通用成功响应 |
| `GetIO` / `GetMemIO` | `#GetIO,[0\|1]<CR><LF>` | `[0\|1]` 表示 I/O 位状态：开=1, 关=0 |
| `GetIOByte` / `GetMemIOByte` | `#GetIOByte,[XX]<CR><LF>` | `[XX]` 为 8 位值的十六进制字符串 (00 到 FF) |
| `GetIOWord` / `GetMemIOWord` | `#GetIOWord,[XXXX]<CR><LF>` | `[XXXX]` 为 16 位值的十六进制字符串 (0000 到 FFFF) |
| `GetVariable` (标量) | `#GetVariable,[参数值]<CR><LF>` | 返回参数值 |
| `GetVariable` (数组) | `#GetVariable,[值1],[值2],...<CR><LF>` | 返回指定数量的数组元素值 |
| `GetStatus` | `#GetStatus,[状态],[错误/警告代码]<CR><LF>`<br>示例: `#GetStatus,01000000001,0000` | `[状态]` 是 11 位二进制字符串，代表 11 个标志位 (见下表)。<br>`[错误/警告代码]` 为 4 位数字，无错误时为 0000。 |
| `Execute` | `#Execute,"[执行结果]"<CR><LF>` | 如果命令执行有返回值，则在此返回。 |
| `GetAlm` | `#GetAlm,[报警数量],[报警编号1],[报警编号2]...<CR><LF>`<br>示例1 (无报警): `#GetAlm,0<CR><LF>`<br>示例2 (报警1和9): `#GetAlm,2,1,9<CR><LF>` | 返回当前报警信息。 |

**`GetStatus` 状态标志位说明 (11位)**：

| 位序 (从左到右) | 标志 | 内容 |
| :--- | :--- | :--- |
| 1 | Test | 在 TEST 模式下为 1 |
| 2 | Teach | 在 TEACH 模式下为 1 |
| 3 | Auto | 在远程输入接受条件下为 1 |
| 4 | Warning | 在警告条件下为 1 (仍可执行任务，但需尽快处理) |
| 5 | SError | 在严重错误状态下为 1 (需重启控制器恢复) |
| 6 | Safeguard | 安全门打开时为 1 |
| 7 | EStop | 在紧急停止状态下为 1 |
| 8 | Error | 在错误状态下为 1 (可用 `Reset` 恢复) |
| 9 | Paused | 任务暂停时为 1 |
| 10 | Running | 任务执行时为 1 (Paused 为 1 时此位为 0) |
| 11 | Ready | 控制器启动完成且无任务执行时为 1 |

#### 错误响应

如果控制器不能正确接收远程命令，将以以下格式显示错误：

**格式**：`![远程命令],[错误代码]<CR><LF>`

| 错误代码 | 内容 |
| :--- | :--- |
| 10 | 远程命令未以 `$` 开头 |
| 11 | 远程命令错误 或 未执行 `Login` |
| 12 | 远程命令格式错误 |
| 13 | `Login` 命令密码错误 |
| 14 | 要获取的指定数量超出范围 (1-100) 或 忽略了要获取的数量 或 指定了一个字符串参数 |
| 15 | 参数不存在 或 参数尺寸错误 或 调用了超出范围的元素 |
| 19 | 请求超时 |
| 20 | 控制器未准备好 |
| 21 | 因为正在运行 `Execute`，所以无法执行其他命令 |
| 98 | 使用全局 IP 地址时，需要输入密码才能登录 |
| 99 | 系统错误 或 通信错误 |

---

### 12.2.8 远程以太网控制的响应时间

描述了一个典型的通信序列：
1.  客户端发起 `Login` 请求。
2.  控制器返回响应。
3.  客户端发送命令请求。
4.  控制器返回命令执行结果。
5.  客户端发送 `Logout` 请求。
6.  控制器返回响应。
> 只有来自指定 IP 地址 `zzz.zzz.zzz.zzz:zz` 的请求可用。

---

## 12.3 远程 RS232

远程 RS232 能够通过 RS-232C 发送远程命令，从而在外部设备上控制机器人和控制器。

### 12.3.1 远程 RS232设置

若要启用远程 RS232 功能，请按以下步骤配置：

1.  选择 **[设置]** 菜单中的 **[系统配置]**，然后选择 **[控制器]**，显示 **[系统配置]** 对话框。
2.  在树形图中选择 **[控制器] - [远程控制] - [RS232]**。
3.  配置远程 RS232 控制所需的项目。
4.  点击 **<应用>** 保存设置，然后点击 **<关闭>**。

> **注意**：有关此对话框设置的详细信息，请参阅“5.13.2 [系统配置](设置菜单) - [设置]-[系统配置]-[远程控制]”。
> 选择“只用于监控”时不必遵循控制设备的设置，使用远程 RS232 控制仅获取数值。
```