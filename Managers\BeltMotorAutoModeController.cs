using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MyHMI.Events;
using MyHMI.Helpers;
using MyHMI.Models;

namespace MyHMI.Managers
{
    /// <summary>
    /// 皮带电机自动模式控制器 - 从WorkflowManager中独立出来的专门控制器
    ///
    /// 职责说明:
    /// - 专门负责输入和输出皮带电机的自动控制功能
    /// - 实现统一的AutoMode控制器接口
    /// - 管理皮带电机的启动、停止、重置等操作
    /// - 监控传感器状态并执行相应的控制逻辑
    ///
    /// 重构背景:
    /// 原本这些功能在WorkflowManager中实现，但违背了单一职责原则。
    /// 重构后将皮带电机相关功能独立出来，使WorkflowManager专注于工作流协调。
    ///
    /// 主要功能:
    /// 1. 输入皮带电机控制 - 控制产品输入流程
    /// 2. 输出皮带电机控制 - 控制产品输出流程
    /// 3. 传感器监控 - 监控各种传感器状态
    /// 4. 状态管理 - 管理控制器的运行状态
    /// 5. 事件通知 - 通过事件通知状态变化
    ///
    /// 使用方法:
    /// var controller = BeltMotorAutoModeController.Instance;
    /// await controller.InitializeAsync();
    /// await controller.StartAsync();
    ///
    /// 作者: HR2项目组
    /// 版本: 1.0 (新建)
    /// 日期: 2025-09-27
    /// </summary>
    public class BeltMotorAutoModeController
    {
        #region 单例模式
        private static readonly Lazy<BeltMotorAutoModeController> _instance = new Lazy<BeltMotorAutoModeController>(() => new BeltMotorAutoModeController());
        public static BeltMotorAutoModeController Instance => _instance.Value;
        private BeltMotorAutoModeController() { }
        #endregion

        #region 私有字段
        private bool _isInitialized = false;
        private bool _isRunning = false;
        private BeltMotorState _currentState = BeltMotorState.Idle;
        private readonly object _stateLock = new object();

        // 皮带电机自动控制相关字段
        private bool _beltMotorAutoControlEnabled = false;
        private CancellationTokenSource _beltMotorCancellationTokenSource;
        private Task _inputBeltControlTask;
        private Task _outputBeltControlTask;
        private readonly SemaphoreSlim _beltMotorSemaphore = new SemaphoreSlim(1, 1);

        // 管理器依赖
        private DMC1000BMotorManager _motorManager;
        private DMC1000BIOManager _ioManager;

        // 皮带电机控制常量
        // 注意: 轴号定义必须与DMC1000BMotorManager保持一致
        // DMC1000BMotorManager定义: AXIS_INPUT_BELT = 3, AXIS_OUTPUT_BELT = 2
        private const short INPUT_BELT_AXIS = 3;   // 输入皮带电机轴号 (与DMC1000BMotorManager.AXIS_INPUT_BELT一致)
        private const short OUTPUT_BELT_AXIS = 2;  // 输出皮带电机轴号 (与DMC1000BMotorManager.AXIS_OUTPUT_BELT一致)
        private const string INPUT_SENSOR_IO = "I0004";   // 输入皮带传感器
        private const string OUTPUT_SENSOR_IO = "I0106";  // 输出皮带传感器
        private const int SENSOR_CHECK_INTERVAL_MS = 50;  // 传感器检查间隔（毫秒）
        private const int MOTOR_STOP_DELAY_MS = 100;      // 电机停止延迟（毫秒）
        #endregion

        #region 公共属性
        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// 是否正在运行
        /// </summary>
        public bool IsRunning => _isRunning;

        /// <summary>
        /// 当前状态
        /// </summary>
        public BeltMotorState CurrentState
        {
            get
            {
                lock (_stateLock)
                {
                    return _currentState;
                }
            }
        }
        #endregion

        #region 事件定义
        /// <summary>
        /// 状态变化事件
        /// </summary>
        public event EventHandler<BeltMotorStateChangedEventArgs> StateChanged;

        /// <summary>
        /// 错误事件
        /// </summary>
        public event EventHandler<BeltMotorErrorEventArgs> ErrorOccurred;
        #endregion

        #region 公共方法
        /// <summary>
        /// 异步初始化皮带电机自动模式控制器
        /// </summary>
        /// <returns>初始化结果</returns>
        public async Task<bool> InitializeAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_isInitialized)
                {
                    LogHelper.Warning("BeltMotorAutoModeController已经初始化，跳过重复初始化");
                    return true;
                }

                LogHelper.Info("开始初始化BeltMotorAutoModeController...");

                // 初始化皮带电机自动控制依赖
                bool dependencyResult = await InitializeBeltMotorControlDependenciesAsync();
                if (!dependencyResult)
                {
                    LogHelper.Error("皮带电机自动控制依赖初始化失败");
                    return false;
                }

                _isInitialized = true;
                ChangeState(BeltMotorState.Idle);
                LogHelper.Info("BeltMotorAutoModeController初始化完成");
                return true;

            }, false, "BeltMotorAutoModeController初始化");
        }

        /// <summary>
        /// 启动皮带电机自动控制
        /// </summary>
        /// <returns>启动结果</returns>
        public async Task<bool> StartAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (!_isInitialized)
                {
                    LogHelper.Error("BeltMotorAutoModeController未初始化");
                    return false;
                }

                if (_isRunning)
                {
                    LogHelper.Warning("皮带电机自动控制已经在运行");
                    return true;
                }

                LogHelper.Info("启动皮带电机自动控制...");

                // 启动皮带电机自动控制
                bool startResult = await StartBeltMotorAutoControlAsync();
                if (!startResult)
                {
                    LogHelper.Error("皮带电机自动控制启动失败");
                    return false;
                }

                _isRunning = true;
                ChangeState(BeltMotorState.Running);
                LogHelper.Info("皮带电机自动控制启动成功");
                return true;

            }, false, "启动皮带电机自动控制");
        }

        /// <summary>
        /// 停止皮带电机自动控制
        /// </summary>
        /// <returns>停止结果</returns>
        public async Task<bool> StopAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (!_isRunning)
                {
                    LogHelper.Warning("皮带电机自动控制未在运行");
                    return true;
                }

                LogHelper.Info("停止皮带电机自动控制...");

                // 停止皮带电机自动控制
                bool stopResult = await StopBeltMotorAutoControlAsync();
                if (!stopResult)
                {
                    LogHelper.Warning("皮带电机自动控制停止时出现问题");
                }

                _isRunning = false;
                ChangeState(BeltMotorState.Idle);
                LogHelper.Info("皮带电机自动控制停止成功");
                return true;

            }, false, "停止皮带电机自动控制");
        }

        /// <summary>
        /// 重置皮带电机自动控制
        /// </summary>
        /// <returns>重置结果</returns>
        public async Task<bool> ResetAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info("重置皮带电机自动控制...");

                // 停止自动控制
                await StopAsync();

                // 注意: 不要在这里释放资源(如信号量),因为重置后还需要继续使用控制器
                // 只有在DisposeAsync中才应该释放资源

                ChangeState(BeltMotorState.Idle);
                LogHelper.Info("皮带电机自动控制重置完成");
                return true;

            }, false, "重置皮带电机自动控制");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <returns>释放结果</returns>
        public async Task<bool> DisposeAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info("开始释放BeltMotorAutoModeController资源...");

                // 停止皮带电机自动控制
                await StopAsync();

                // 释放资源
                await DisposeResourcesAsync();

                _isInitialized = false;
                LogHelper.Info("BeltMotorAutoModeController资源释放完成");
                return true;

            }, false, "释放BeltMotorAutoModeController资源");
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 初始化皮带电机自动控制依赖
        /// </summary>
        /// <returns>初始化结果</returns>
        private async Task<bool> InitializeBeltMotorControlDependenciesAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                await Task.CompletedTask; // 消除CS1998警告
                LogHelper.Info("初始化皮带电机自动控制依赖...");

                // 获取电机管理器实例
                _motorManager = DMC1000BMotorManager.Instance;
                if (!_motorManager.IsInitialized)
                {
                    LogHelper.Warning("DMC1000BMotorManager未初始化，皮带电机自动控制可能无法正常工作");
                }

                // 获取IO管理器实例
                _ioManager = DMC1000BIOManager.Instance;
                if (!_ioManager.IsInitialized)
                {
                    LogHelper.Warning("DMC1000BIOManager未初始化，皮带电机自动控制可能无法正常工作");
                }

                LogHelper.Info("皮带电机自动控制依赖初始化完成");
                return true;

            }, false, "初始化皮带电机自动控制依赖");
        }

        /// <summary>
        /// 输入皮带控制循环
        /// 逻辑：当传感器I0004为0时，皮带停止；为1时，皮带启动
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        private async Task InputBeltControlLoopAsync(CancellationToken cancellationToken)
        {
            LogHelper.Info("输入皮带控制线程启动");
            bool lastSensorState = false;

            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    try
                    {
                        // 读取传感器状态
                        bool currentSensorState = await _ioManager.ReadInputAsync(INPUT_SENSOR_IO);

                        // 检查电机运行状态（通过DMC1000B获取实际状态）
                        bool isMotorRunning = false;
                        try
                        {
                            short motionStatus = _motorManager.GetMotorMotionStatus(INPUT_BELT_AXIS);
                            isMotorRunning = (motionStatus == DMC1000BMotorManager.MOTION_RUNNING);
                        }
                        catch (Exception statusEx)
                        {
                            LogHelper.Debug($"获取输入皮带电机状态失败: {statusEx.Message}");
                            // 在脱机模式下，假设电机未运行
                            isMotorRunning = false;
                        }

                        // 检查传感器状态变化（仅用于日志记录）
                        if (currentSensorState != lastSensorState)
                        {
                            LogHelper.Debug($"输入皮带传感器{INPUT_SENSOR_IO}状态变化: {lastSensorState} -> {currentSensorState}");
                            lastSensorState = currentSensorState;
                        }

                        // 根据传感器状态控制电机（完整的启动和停止逻辑）
                        // 文档要求：I0004=0（检测到产品）时停止皮带，I0004=1（无产品）时启动皮带
                        if (currentSensorState == false && isMotorRunning)
                        {
                            // 传感器为0（检测到产品），停止皮带让产品停留
                            LogHelper.Info("输入皮带传感器为0（检测到产品），停止皮带电机");
                            bool stopResult = await _motorManager.StopMotorAsync(INPUT_BELT_AXIS);
                            if (stopResult)
                            {
                                LogHelper.Info("输入皮带电机停止成功");
                            }
                            else
                            {
                                LogHelper.Error("输入皮带电机停止失败");
                            }

                            // 添加停止延迟
                            await Task.Delay(MOTOR_STOP_DELAY_MS, cancellationToken);
                        }
                        else if (currentSensorState == true && !isMotorRunning)
                        {
                            // 传感器为1（无产品），启动皮带继续传送
                            LogHelper.Info("输入皮带传感器为1（无产品），启动皮带电机");
                            bool startResult = await _motorManager.BeltMotorContinuousRunAsync(INPUT_BELT_AXIS, false); // 反向运转
                            if (startResult)
                            {
                                LogHelper.Info("输入皮带电机启动成功");
                            }
                            else
                            {
                                LogHelper.Error("输入皮带电机启动失败");
                            }
                        }

                        // 等待下次检查
                        await Task.Delay(SENSOR_CHECK_INTERVAL_MS, cancellationToken);
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error("输入皮带控制循环异常", ex);
                        await Task.Delay(1000, cancellationToken); // 异常后等待1秒再继续
                    }
                }
            }
            catch (OperationCanceledException)
            {
                LogHelper.Info("输入皮带控制线程被取消");
            }
            finally
            {
                // 确保电机停止（通过检查实际状态）
                try
                {
                    short motionStatus = _motorManager.GetMotorMotionStatus(INPUT_BELT_AXIS);
                    if (motionStatus == DMC1000BMotorManager.MOTION_RUNNING)
                    {
                        await _motorManager.StopMotorAsync(INPUT_BELT_AXIS);
                        LogHelper.Info("输入皮带控制线程结束，电机已停止");
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Debug($"输入皮带控制线程结束时检查电机状态失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 启动皮带电机自动控制
        /// </summary>
        /// <returns>启动结果</returns>
        private async Task<bool> StartBeltMotorAutoControlAsync()
        {
            if (!await _beltMotorSemaphore.WaitAsync(5000))
            {
                LogHelper.Error("皮带电机自动控制启动超时");
                return false;
            }

            try
            {
                if (_beltMotorAutoControlEnabled)
                {
                    LogHelper.Warning("皮带电机自动控制已经在运行");
                    return true;
                }

                // 检查依赖是否已初始化
                if (_motorManager == null || _ioManager == null)
                {
                    LogHelper.Error("皮带电机自动控制依赖未初始化");
                    return false;
                }

                // 创建取消令牌
                _beltMotorCancellationTokenSource?.Dispose(); // 清理之前的资源
                _beltMotorCancellationTokenSource = new CancellationTokenSource();

                try
                {
                    // ===== 新增: 初始启动逻辑 =====
                    LogHelper.Info("执行皮带电机初始启动检查...");

                    // 1. 检查输入皮带传感器状态并启动电机
                    try
                    {
                        bool inputSensorState = await _ioManager.ReadInputAsync(INPUT_SENSOR_IO);
                        LogHelper.Info($"输入皮带传感器{INPUT_SENSOR_IO}初始状态: {(inputSensorState ? "1(无产品)" : "0(检测到产品)")}");

                        // 如果传感器为1(无产品),立即启动输入皮带电机
                        if (inputSensorState == true)
                        {
                            LogHelper.Info("输入皮带传感器为1(无产品),启动输入皮带电机");
                            bool startResult = await _motorManager.BeltMotorContinuousRunAsync(INPUT_BELT_AXIS, false); // 反向运转
                            if (startResult)
                            {
                                LogHelper.Info("输入皮带电机初始启动成功");
                            }
                            else
                            {
                                LogHelper.Warning("输入皮带电机初始启动失败,将由控制循环继续尝试");
                            }
                        }
                        else
                        {
                            LogHelper.Info("输入皮带传感器为0(检测到产品),输入皮带电机保持停止状态");
                        }
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Warning($"输入皮带初始启动检查失败: {ex.Message},将由控制循环处理");
                    }

                    // 2. 检查输出皮带传感器状态并启动电机
                    try
                    {
                        bool outputSensorState = await _ioManager.ReadInputAsync(OUTPUT_SENSOR_IO);
                        LogHelper.Info($"输出皮带传感器{OUTPUT_SENSOR_IO}初始状态: {(outputSensorState ? "1(无产品)" : "0(检测到产品)")}");

                        // 如果传感器为0(检测到产品),立即启动输出皮带电机
                        if (outputSensorState == false)
                        {
                            LogHelper.Info("输出皮带传感器为0(检测到产品),启动输出皮带电机");
                            bool startResult = await _motorManager.BeltMotorContinuousRunAsync(OUTPUT_BELT_AXIS, false); // 反向运转
                            if (startResult)
                            {
                                LogHelper.Info("输出皮带电机初始启动成功");
                            }
                            else
                            {
                                LogHelper.Warning("输出皮带电机初始启动失败,将由控制循环继续尝试");
                            }
                        }
                        else
                        {
                            LogHelper.Info("输出皮带传感器为1(无产品),输出皮带电机保持停止状态");
                        }
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Warning($"输出皮带初始启动检查失败: {ex.Message},将由控制循环处理");
                    }
                    // ===== 初始启动逻辑结束 =====

                    // 启动输入皮带控制线程
                    _inputBeltControlTask = Task.Run(async () =>
                        await InputBeltControlLoopAsync(_beltMotorCancellationTokenSource.Token));

                    // 启动输出皮带控制线程
                    _outputBeltControlTask = Task.Run(async () =>
                        await OutputBeltControlLoopAsync(_beltMotorCancellationTokenSource.Token));

                    _beltMotorAutoControlEnabled = true;
                    LogHelper.Info("皮带电机自动控制启动成功");
                    return true;
                }
                catch (Exception taskEx)
                {
                    // 如果任务创建失败，清理资源
                    _beltMotorCancellationTokenSource?.Cancel();
                    _beltMotorCancellationTokenSource?.Dispose();
                    _beltMotorCancellationTokenSource = null;
                    throw taskEx;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("皮带电机自动控制启动失败", ex);
                return false;
            }
            finally
            {
                _beltMotorSemaphore.Release();
            }
        }

        /// <summary>
        /// 输出皮带控制循环
        /// 逻辑：当传感器I0106为0时，皮带停止
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        private async Task OutputBeltControlLoopAsync(CancellationToken cancellationToken)
        {
            LogHelper.Info("输出皮带控制线程启动");
            bool lastSensorState = false;

            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    try
                    {
                        // 读取传感器状态
                        bool currentSensorState = await _ioManager.ReadInputAsync(OUTPUT_SENSOR_IO);

                        // 检查电机运行状态（通过DMC1000B获取实际状态）
                        bool isMotorRunning = false;
                        try
                        {
                            short motionStatus = _motorManager.GetMotorMotionStatus(OUTPUT_BELT_AXIS);
                            isMotorRunning = (motionStatus == DMC1000BMotorManager.MOTION_RUNNING);
                        }
                        catch (Exception statusEx)
                        {
                            LogHelper.Debug($"获取输出皮带电机状态失败: {statusEx.Message}");
                            // 在脱机模式下，假设电机未运行
                            isMotorRunning = false;
                        }

                        // 检查传感器状态变化
                        if (currentSensorState != lastSensorState)
                        {
                            LogHelper.Debug($"输出皮带传感器{OUTPUT_SENSOR_IO}状态变化: {lastSensorState} -> {currentSensorState}");
                            lastSensorState = currentSensorState;
                        }

                        // 根据传感器状态控制电机（完整的启动和停止逻辑）
                        // 文档要求：I0106=0（检测到产品）时启动皮带，I0106=1（无产品）时停止皮带
                        if (currentSensorState == false && !isMotorRunning)
                        {
                            // 传感器为0（检测到产品），启动皮带让产品继续传送
                            LogHelper.Info("输出皮带传感器为0（检测到产品），启动皮带电机");
                            bool startResult = await _motorManager.BeltMotorContinuousRunAsync(OUTPUT_BELT_AXIS, false); // 反向运转
                            if (startResult)
                            {
                                LogHelper.Info("输出皮带电机启动成功");
                            }
                            else
                            {
                                LogHelper.Error("输出皮带电机启动失败");
                            }
                        }
                        else if (currentSensorState == true && isMotorRunning)
                        {
                            // 传感器为1（无产品），停止皮带节能等待
                            LogHelper.Info("输出皮带传感器为1（无产品），停止皮带电机");
                            bool stopResult = await _motorManager.StopMotorAsync(OUTPUT_BELT_AXIS);
                            if (stopResult)
                            {
                                LogHelper.Info("输出皮带电机停止成功");
                            }
                            else
                            {
                                LogHelper.Error("输出皮带电机停止失败");
                            }

                            // 添加停止延迟
                            await Task.Delay(MOTOR_STOP_DELAY_MS, cancellationToken);
                        }

                        // 等待下次检查
                        await Task.Delay(SENSOR_CHECK_INTERVAL_MS, cancellationToken);
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error("输出皮带控制循环异常", ex);
                        await Task.Delay(1000, cancellationToken); // 异常后等待1秒再继续
                    }
                }
            }
            catch (OperationCanceledException)
            {
                LogHelper.Info("输出皮带控制线程被取消");
            }
            finally
            {
                // 确保电机停止（通过检查实际状态）
                try
                {
                    short motionStatus = _motorManager.GetMotorMotionStatus(OUTPUT_BELT_AXIS);
                    if (motionStatus == DMC1000BMotorManager.MOTION_RUNNING)
                    {
                        await _motorManager.StopMotorAsync(OUTPUT_BELT_AXIS);
                        LogHelper.Info("输出皮带控制线程结束，电机已停止");
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Debug($"输出皮带控制线程结束时检查电机状态失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 停止皮带电机自动控制
        /// </summary>
        /// <returns>停止结果</returns>
        private async Task<bool> StopBeltMotorAutoControlAsync()
        {
            if (!await _beltMotorSemaphore.WaitAsync(5000))
            {
                LogHelper.Error("皮带电机自动控制停止超时");
                return false;
            }

            try
            {
                if (!_beltMotorAutoControlEnabled)
                {
                    LogHelper.Warning("皮带电机自动控制未在运行");
                    return true;
                }

                // 取消所有控制任务
                _beltMotorCancellationTokenSource?.Cancel();

                // 等待任务完成（设置超时避免无限等待）
                var tasks = new Task[] { _inputBeltControlTask, _outputBeltControlTask };
                var validTasks = tasks.Where(t => t != null).ToArray();

                if (validTasks.Length > 0)
                {
                    try
                    {
                        await Task.WhenAll(validTasks).ConfigureAwait(false);
                    }
                    catch (OperationCanceledException)
                    {
                        // 任务被取消是正常的
                        LogHelper.Info("皮带电机控制任务已被取消");
                    }
                }

                // 停止所有皮带电机
                await StopAllBeltMotorsAsync();

                // 清理资源
                _beltMotorCancellationTokenSource?.Dispose();
                _beltMotorCancellationTokenSource = null;
                _inputBeltControlTask = null;
                _outputBeltControlTask = null;

                _beltMotorAutoControlEnabled = false;
                LogHelper.Info("皮带电机自动控制停止成功");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("皮带电机自动控制停止失败", ex);
                return false;
            }
            finally
            {
                _beltMotorSemaphore.Release();
            }
        }

        /// <summary>
        /// 停止所有皮带电机
        /// </summary>
        private async Task StopAllBeltMotorsAsync()
        {
            try
            {
                var tasks = new[]
                {
                    _motorManager.StopMotorAsync(INPUT_BELT_AXIS),
                    _motorManager.StopMotorAsync(OUTPUT_BELT_AXIS)
                };

                await Task.WhenAll(tasks);
                LogHelper.Info("所有皮带电机已停止");
            }
            catch (Exception ex)
            {
                LogHelper.Error("停止皮带电机失败", ex);
            }
        }

        /// <summary>
        /// 手动启动输出皮带电机
        /// 用于其他系统需要启动输出皮带时调用
        /// </summary>
        /// <returns>启动结果</returns>
        public async Task<bool> StartOutputBeltAsync()
        {
            try
            {
                LogHelper.Info("手动启动输出皮带电机");
                bool result = await _motorManager.BeltMotorContinuousRunAsync(OUTPUT_BELT_AXIS, false); // 反向运转
                if (result)
                {
                    LogHelper.Info("输出皮带电机手动启动成功");
                }
                else
                {
                    LogHelper.Error("输出皮带电机手动启动失败");
                }
                return result;
            }
            catch (Exception ex)
            {
                LogHelper.Error("输出皮带电机手动启动异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 手动停止输出皮带电机
        /// </summary>
        /// <returns>停止结果</returns>
        public async Task<bool> StopOutputBeltAsync()
        {
            try
            {
                LogHelper.Info("手动停止输出皮带电机");
                bool result = await _motorManager.StopMotorAsync(OUTPUT_BELT_AXIS);
                if (result)
                {
                    LogHelper.Info("输出皮带电机手动停止成功");
                }
                else
                {
                    LogHelper.Error("输出皮带电机手动停止失败");
                }
                return result;
            }
            catch (Exception ex)
            {
                LogHelper.Error("输出皮带电机手动停止异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        private async Task DisposeResourcesAsync()
        {
            await Task.CompletedTask; // 消除CS1998警告
            try
            {
                // 释放信号量
                _beltMotorSemaphore?.Dispose();

                LogHelper.Info("皮带电机控制器资源释放完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("释放皮带电机控制器资源时发生异常", ex);
            }
        }

        /// <summary>
        /// 改变状态
        /// </summary>
        /// <param name="newState">新状态</param>
        private void ChangeState(BeltMotorState newState)
        {
            BeltMotorState oldState;
            lock (_stateLock)
            {
                oldState = _currentState;
                _currentState = newState;
            }

            if (oldState != newState)
            {
                LogHelper.Info($"皮带电机控制器状态变更: {oldState} -> {newState}");
                StateChanged?.Invoke(this, new BeltMotorStateChangedEventArgs(oldState, newState));
            }
        }
        #endregion
    }

    /// <summary>
    /// 皮带电机状态枚举
    /// </summary>
    public enum BeltMotorState
    {
        /// <summary>
        /// 空闲状态
        /// </summary>
        Idle = 0,

        /// <summary>
        /// 运行状态
        /// </summary>
        Running = 1,

        /// <summary>
        /// 错误状态
        /// </summary>
        Error = 2
    }

    /// <summary>
    /// 皮带电机状态变化事件参数
    /// </summary>
    public class BeltMotorStateChangedEventArgs : EventArgs
    {
        public BeltMotorState OldState { get; }
        public BeltMotorState NewState { get; }

        public BeltMotorStateChangedEventArgs(BeltMotorState oldState, BeltMotorState newState)
        {
            OldState = oldState;
            NewState = newState;
        }
    }

    /// <summary>
    /// 皮带电机错误事件参数
    /// </summary>
    public class BeltMotorErrorEventArgs : EventArgs
    {
        public string ErrorMessage { get; }
        public Exception Exception { get; }

        public BeltMotorErrorEventArgs(string errorMessage, Exception exception = null)
        {
            ErrorMessage = errorMessage;
            Exception = exception;
        }
    }
}
