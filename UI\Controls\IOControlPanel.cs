using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using MyHMI.Managers;
using MyHMI.Events;
using MyHMI.Helpers;
using MyHMI.Models;

namespace MyHMI.UI.Controls
{
    /// <summary>
    /// IO控制面板
    /// </summary>
    public partial class IOControlPanel : UserControl
    {
        #region 私有字段
        private GroupBox _inputGroupBox;
        private GroupBox _outputGroupBox;
        private GroupBox _controlGroupBox;
        private GroupBox _flipMotorGroupBox;

        private Label[] _inputLabels;
        private Label[] _inputStatusLabels;
        private Label[] _outputLabels;
        private CheckBox[] _outputCheckBoxes;

        private Button _refreshButton;
        private Button _resetButton;
        private Button _testIOButton;
        private Label _statusLabel;

        // 翻转电机控制
        private Button _leftFlipHomeButton;
        private Button _rightFlipHomeButton;
        private Button _leftFlipPositiveButton;
        private Button _leftFlipNegativeButton;
        private Button _rightFlipPositiveButton;
        private Button _rightFlipNegativeButton;
        private CheckBox _leftFlipDirectionCheckBox;
        private CheckBox _rightFlipDirectionCheckBox;
        private Label _leftFlipStatusLabel;
        private Label _rightFlipStatusLabel;

        private const int MAX_IO_COUNT = 16;
        #endregion

        #region 构造函数
        public IOControlPanel()
        {
            InitializeComponent();
            InitializeUI();
        }
        #endregion

        #region 初始化方法
        /// <summary>
        /// 初始化UI组件
        /// </summary>
        private void InitializeUI()
        {
            this.Dock = DockStyle.Fill;
            this.BackColor = Color.White;

            // 创建输入组
            CreateInputGroup();

            // 创建输出组
            CreateOutputGroup();

            // 创建控制组
            CreateControlGroup();

            // 创建翻转电机控制组
            CreateFlipMotorGroup();

            // 布局控件
            LayoutControls();
        }

        /// <summary>
        /// 创建输入组
        /// </summary>
        private void CreateInputGroup()
        {
            _inputGroupBox = new GroupBox
            {
                Text = "数字输入 (DI)",
                Font = new Font("微软雅黑", 10F, FontStyle.Bold),
                Size = new Size(400, 300),
                Location = new Point(10, 10)
            };

            _inputLabels = new Label[MAX_IO_COUNT];
            _inputStatusLabels = new Label[MAX_IO_COUNT];

            for (int i = 0; i < MAX_IO_COUNT; i++)
            {
                // 输入标签
                _inputLabels[i] = new Label
                {
                    Text = $"DI{i:D2}:",
                    Size = new Size(40, 20),
                    Location = new Point(10 + (i % 4) * 90, 25 + (i / 4) * 30),
                    TextAlign = ContentAlignment.MiddleRight
                };

                // 状态标签
                _inputStatusLabels[i] = new Label
                {
                    Text = "OFF",
                    Size = new Size(40, 20),
                    Location = new Point(55 + (i % 4) * 90, 25 + (i / 4) * 30),
                    TextAlign = ContentAlignment.MiddleCenter,
                    BackColor = Color.LightGray,
                    BorderStyle = BorderStyle.FixedSingle
                };

                _inputGroupBox.Controls.Add(_inputLabels[i]);
                _inputGroupBox.Controls.Add(_inputStatusLabels[i]);
            }

            this.Controls.Add(_inputGroupBox);
        }

        /// <summary>
        /// 创建输出组
        /// </summary>
        private void CreateOutputGroup()
        {
            _outputGroupBox = new GroupBox
            {
                Text = "数字输出 (DO)",
                Font = new Font("微软雅黑", 10F, FontStyle.Bold),
                Size = new Size(400, 300),
                Location = new Point(420, 10)
            };

            _outputLabels = new Label[MAX_IO_COUNT];
            _outputCheckBoxes = new CheckBox[MAX_IO_COUNT];

            for (int i = 0; i < MAX_IO_COUNT; i++)
            {
                // 输出标签
                _outputLabels[i] = new Label
                {
                    Text = $"DO{i:D2}:",
                    Size = new Size(40, 20),
                    Location = new Point(10 + (i % 4) * 90, 25 + (i / 4) * 30),
                    TextAlign = ContentAlignment.MiddleRight
                };

                // 输出复选框
                _outputCheckBoxes[i] = new CheckBox
                {
                    Size = new Size(40, 20),
                    Location = new Point(55 + (i % 4) * 90, 25 + (i / 4) * 30),
                    TextAlign = ContentAlignment.MiddleCenter,
                    Tag = i
                };
                // 注意：不在构造函数中订阅事件，而是在状态同步完成后订阅

                _outputGroupBox.Controls.Add(_outputLabels[i]);
                _outputGroupBox.Controls.Add(_outputCheckBoxes[i]);
            }

            this.Controls.Add(_outputGroupBox);
        }

        /// <summary>
        /// 创建控制组
        /// </summary>
        private void CreateControlGroup()
        {
            _controlGroupBox = new GroupBox
            {
                Text = "控制操作",
                Font = new Font("微软雅黑", 10F, FontStyle.Bold),
                Size = new Size(810, 80),
                Location = new Point(10, 320)
            };

            _refreshButton = new Button
            {
                Text = "刷新状态",
                Size = new Size(100, 30),
                Location = new Point(20, 30),
                UseVisualStyleBackColor = true
            };
            _refreshButton.Click += RefreshButton_Click;

            _resetButton = new Button
            {
                Text = "复位输出",
                Size = new Size(100, 30),
                Location = new Point(140, 30),
                UseVisualStyleBackColor = true
            };
            _resetButton.Click += ResetButton_Click;

            _testIOButton = new Button
            {
                Text = "测试IO",
                Size = new Size(100, 30),
                Location = new Point(260, 30),
                UseVisualStyleBackColor = true,
                BackColor = Color.LightBlue
            };
            _testIOButton.Click += TestIOButton_Click;

            _statusLabel = new Label
            {
                Text = "状态: 未连接",
                Size = new Size(400, 30),
                Location = new Point(380, 30),
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("微软雅黑", 9F)
            };

            _controlGroupBox.Controls.AddRange(new Control[]
            {
                _refreshButton, _resetButton, _testIOButton, _statusLabel
            });

            this.Controls.Add(_controlGroupBox);
        }

        /// <summary>
        /// 创建翻转电机控制组
        /// </summary>
        private void CreateFlipMotorGroup()
        {
            _flipMotorGroupBox = new GroupBox
            {
                Text = "翻转电机控制",
                Font = new Font("微软雅黑", 10F, FontStyle.Bold),
                Size = new Size(810, 120),
                Location = new Point(10, 410)
            };

            // 左翻转电机控制
            var leftLabel = new Label
            {
                Text = "左翻转电机:",
                Size = new Size(80, 20),
                Location = new Point(20, 25),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("微软雅黑", 9F, FontStyle.Bold)
            };

            _leftFlipHomeButton = new Button
            {
                Text = "回零",
                Size = new Size(60, 30),
                Location = new Point(110, 20),
                UseVisualStyleBackColor = true,
                BackColor = Color.LightGreen
            };
            _leftFlipHomeButton.Click += LeftFlipHomeButton_Click;

            _leftFlipPositiveButton = new Button
            {
                Text = "正向测试",
                Size = new Size(70, 25),
                Location = new Point(180, 22),
                UseVisualStyleBackColor = true
            };
            _leftFlipPositiveButton.Click += LeftFlipPositiveButton_Click;

            _leftFlipNegativeButton = new Button
            {
                Text = "负向测试",
                Size = new Size(70, 25),
                Location = new Point(260, 22),
                UseVisualStyleBackColor = true
            };
            _leftFlipNegativeButton.Click += LeftFlipNegativeButton_Click;

            _leftFlipDirectionCheckBox = new CheckBox
            {
                Text = "正方向回零",
                Size = new Size(90, 20),
                Location = new Point(340, 25),
                Font = new Font("微软雅黑", 8F)
            };
            _leftFlipDirectionCheckBox.CheckedChanged += LeftFlipDirectionCheckBox_CheckedChanged;

            _leftFlipStatusLabel = new Label
            {
                Text = "状态: 未知",
                Size = new Size(150, 20),
                Location = new Point(440, 25),
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("微软雅黑", 8F)
            };

            // 右翻转电机控制
            var rightLabel = new Label
            {
                Text = "右翻转电机:",
                Size = new Size(80, 20),
                Location = new Point(20, 55),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("微软雅黑", 9F, FontStyle.Bold)
            };

            _rightFlipHomeButton = new Button
            {
                Text = "回零",
                Size = new Size(60, 30),
                Location = new Point(110, 50),
                UseVisualStyleBackColor = true,
                BackColor = Color.LightGreen
            };
            _rightFlipHomeButton.Click += RightFlipHomeButton_Click;

            _rightFlipPositiveButton = new Button
            {
                Text = "正向测试",
                Size = new Size(70, 25),
                Location = new Point(180, 52),
                UseVisualStyleBackColor = true
            };
            _rightFlipPositiveButton.Click += RightFlipPositiveButton_Click;

            _rightFlipNegativeButton = new Button
            {
                Text = "负向测试",
                Size = new Size(70, 25),
                Location = new Point(260, 52),
                UseVisualStyleBackColor = true
            };
            _rightFlipNegativeButton.Click += RightFlipNegativeButton_Click;

            _rightFlipDirectionCheckBox = new CheckBox
            {
                Text = "正方向回零",
                Size = new Size(90, 20),
                Location = new Point(340, 55),
                Font = new Font("微软雅黑", 8F)
            };
            _rightFlipDirectionCheckBox.CheckedChanged += RightFlipDirectionCheckBox_CheckedChanged;

            _rightFlipStatusLabel = new Label
            {
                Text = "状态: 未知",
                Size = new Size(150, 20),
                Location = new Point(440, 55),
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("微软雅黑", 8F)
            };

            // 安全提示
            var safetyLabel = new Label
            {
                Text = "⚠️ 安全提示: 请先使用正/负向测试确定电机正方向，再设置回零方向",
                Size = new Size(400, 20),
                Location = new Point(20, 85),
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("微软雅黑", 8F),
                ForeColor = Color.Red
            };

            _flipMotorGroupBox.Controls.AddRange(new Control[]
            {
                leftLabel, _leftFlipHomeButton, _leftFlipPositiveButton, _leftFlipNegativeButton,
                _leftFlipDirectionCheckBox, _leftFlipStatusLabel,
                rightLabel, _rightFlipHomeButton, _rightFlipPositiveButton, _rightFlipNegativeButton,
                _rightFlipDirectionCheckBox, _rightFlipStatusLabel,
                safetyLabel
            });

            this.Controls.Add(_flipMotorGroupBox);
        }

        /// <summary>
        /// 布局控件
        /// </summary>
        private void LayoutControls()
        {
            // 设置面板最小大小
            this.MinimumSize = new Size(830, 540);
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 异步初始化面板
        /// </summary>
        /// <returns></returns>
        public async Task InitializeAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                // 1. 先刷新初始状态，确保UI与硬件同步
                await RefreshIOStatusAsync();

                // 2. 状态同步完成后再订阅复选框事件
                SubscribeOutputCheckBoxEvents();

                // 3. 订阅IO状态变化事件
                DMC1000BIOManager.Instance.IOInputStateChanged += IOManager_IOInputStateChanged;
                DMC1000BIOManager.Instance.IOOutputStateChanged += IOManager_IOOutputStateChanged;

                // 4. 初始化翻转电机控制状态
                await InitializeFlipMotorControlsAsync();

                LogHelper.Info("IO控制面板初始化完成");

                return true;
            }, false, "IO控制面板初始化");
        }

        /// <summary>
        /// 订阅输出复选框事件
        /// </summary>
        private void SubscribeOutputCheckBoxEvents()
        {
            try
            {
                for (int i = 0; i < MAX_IO_COUNT; i++)
                {
                    if (_outputCheckBoxes[i] != null)
                    {
                        _outputCheckBoxes[i].CheckedChanged += OutputCheckBox_CheckedChanged;
                    }
                }
                LogHelper.Debug("输出复选框事件订阅完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("订阅输出复选框事件失败", ex);
            }
        }

        /// <summary>
        /// 初始化翻转电机控制状态
        /// </summary>
        private async Task InitializeFlipMotorControlsAsync()
        {
            try
            {
                // 获取并设置左翻转电机回零方向
                bool leftDirection = DMC1000BMotorManager.Instance.GetFlipMotorHomeDirection(0);
                _leftFlipDirectionCheckBox.CheckedChanged -= LeftFlipDirectionCheckBox_CheckedChanged;
                _leftFlipDirectionCheckBox.Checked = leftDirection;
                _leftFlipDirectionCheckBox.CheckedChanged += LeftFlipDirectionCheckBox_CheckedChanged;

                // 获取并设置右翻转电机回零方向
                bool rightDirection = DMC1000BMotorManager.Instance.GetFlipMotorHomeDirection(1);
                _rightFlipDirectionCheckBox.CheckedChanged -= RightFlipDirectionCheckBox_CheckedChanged;
                _rightFlipDirectionCheckBox.Checked = rightDirection;
                _rightFlipDirectionCheckBox.CheckedChanged += RightFlipDirectionCheckBox_CheckedChanged;

                // 更新状态显示
                string leftDir = leftDirection ? "正方向" : "负方向";
                string rightDir = rightDirection ? "正方向" : "负方向";
                UpdateFlipMotorStatus(0, $"回零方向: {leftDir}");
                UpdateFlipMotorStatus(1, $"回零方向: {rightDir}");

                LogHelper.Info("翻转电机控制状态初始化完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("初始化翻转电机控制状态失败", ex);
            }
        }

        /// <summary>
        /// 异步释放资源
        /// </summary>
        /// <returns></returns>
        public async Task DisposeAsync()
        {
            await Task.Run(() =>
            {
                // 取消订阅事件
                DMC1000BIOManager.Instance.IOInputStateChanged -= IOManager_IOInputStateChanged;
                DMC1000BIOManager.Instance.IOOutputStateChanged -= IOManager_IOOutputStateChanged;

                LogHelper.Info("IO控制面板资源释放完成");
            });
        }
        #endregion

        #region IO编号映射方法
        /// <summary>
        /// 根据索引获取输入IO编号
        /// </summary>
        /// <param name="index">索引 (0-31)</param>
        /// <returns>IO编号，如I0001、I0101等</returns>
        private string GetInputIONumberByIndex(int index)
        {
            try
            {
                var allInputPorts = IOConfiguration.GetAllInputPorts();
                if (index >= 0 && index < allInputPorts.Count)
                {
                    return allInputPorts[index].IONumber;
                }

                LogHelper.Warning($"输入IO索引超出范围: {index}, 总数: {allInputPorts.Count}");
                return null;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"获取输入IO编号失败，索引: {index}", ex);
                return null;
            }
        }

        /// <summary>
        /// 根据索引获取输出IO编号
        /// </summary>
        /// <param name="index">索引 (0-31)</param>
        /// <returns>IO编号，如O0001、O0113等</returns>
        private string GetOutputIONumberByIndex(int index)
        {
            try
            {
                var allOutputPorts = IOConfiguration.GetAllOutputPorts();
                if (index >= 0 && index < allOutputPorts.Count)
                {
                    return allOutputPorts[index].IONumber;
                }

                LogHelper.Warning($"输出IO索引超出范围: {index}, 总数: {allOutputPorts.Count}");
                return null;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"获取输出IO编号失败，索引: {index}", ex);
                return null;
            }
        }

        /// <summary>
        /// 根据IO编号获取索引
        /// </summary>
        /// <param name="ioNumber">IO编号，如I0001、O0001等</param>
        /// <returns>索引，未找到返回-1</returns>
        private int GetIndexByIONumber(string ioNumber)
        {
            try
            {
                if (string.IsNullOrEmpty(ioNumber))
                    return -1;

                // 检查输入IO
                var allInputPorts = IOConfiguration.GetAllInputPorts();
                for (int i = 0; i < allInputPorts.Count; i++)
                {
                    if (allInputPorts[i].IONumber == ioNumber)
                        return i;
                }

                // 检查输出IO
                var allOutputPorts = IOConfiguration.GetAllOutputPorts();
                for (int i = 0; i < allOutputPorts.Count; i++)
                {
                    if (allOutputPorts[i].IONumber == ioNumber)
                        return i;
                }

                LogHelper.Warning($"未找到IO编号对应的索引: {ioNumber}");
                return -1;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"根据IO编号获取索引失败: {ioNumber}", ex);
                return -1;
            }
        }
        #endregion

        #region 事件处理器
        /// <summary>
        /// IO输入状态变化事件处理
        /// </summary>
        private void IOManager_IOInputStateChanged(object sender, Models.IOInputStateChangedEventArgs e)
        {
            UIHelper.SafeInvoke(() =>
            {
                // 使用新的IO编号映射方法获取索引
                int ioIndex = GetIndexByIONumber(e.IONumber);
                if (ioIndex >= 0 && ioIndex < MAX_IO_COUNT)
                {
                    UpdateInputStatus(ioIndex, e.NewState);
                    LogHelper.Debug($"更新输入IO状态: {e.IONumber} -> 索引{ioIndex} -> {(e.NewState ? "ON" : "OFF")}");
                }
                else
                {
                    LogHelper.Warning($"输入IO状态变化事件：无法找到IO编号 {e.IONumber} 对应的索引");
                }
            });
        }

        /// <summary>
        /// IO输出状态变化事件处理
        /// </summary>
        private void IOManager_IOOutputStateChanged(object sender, Models.IOOutputStateChangedEventArgs e)
        {
            UIHelper.SafeInvoke(() =>
            {
                // 使用新的IO编号映射方法获取索引
                int ioIndex = GetIndexByIONumber(e.IONumber);
                if (ioIndex >= 0 && ioIndex < MAX_IO_COUNT)
                {
                    UpdateOutputStatus(ioIndex, e.NewState);
                    LogHelper.Debug($"更新输出IO状态: {e.IONumber} -> 索引{ioIndex} -> {(e.NewState ? "ON" : "OFF")}");
                }
                else
                {
                    LogHelper.Warning($"输出IO状态变化事件：无法找到IO编号 {e.IONumber} 对应的索引");
                }
            });
        }

        /// <summary>
        /// 输出复选框状态变化事件
        /// </summary>
        private async void OutputCheckBox_CheckedChanged(object sender, EventArgs e)
        {
            if (sender is CheckBox checkBox && checkBox.Tag is int index)
            {
                await SetOutputAsync(index, checkBox.Checked);
            }
        }

        /// <summary>
        /// 刷新按钮点击事件
        /// </summary>
        private async void RefreshButton_Click(object sender, EventArgs e)
        {
            await RefreshIOStatusAsync();
        }

        /// <summary>
        /// 复位按钮点击事件
        /// </summary>
        private async void ResetButton_Click(object sender, EventArgs e)
        {
            await ResetAllOutputsAsync();
        }

        /// <summary>
        /// 测试IO按钮点击事件
        /// </summary>
        private async void TestIOButton_Click(object sender, EventArgs e)
        {
            try
            {
                _testIOButton.Enabled = false;
                _testIOButton.Text = "测试中...";
                _statusLabel.Text = "状态: 正在测试IO功能...";

                // 调用DMC1000BIOManager的测试方法
                var ioManager = DMC1000BIOManager.Instance;
                if (ioManager.IsInitialized)
                {
                    await Task.Run(() => ioManager.TestIOFunctionality());
                    _statusLabel.Text = "状态: IO测试完成，请查看日志";
                    MessageBox.Show("IO测试完成！请查看日志窗口了解详细结果。", "测试完成",
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    _statusLabel.Text = "状态: IO管理器未初始化";
                    MessageBox.Show("IO管理器未初始化，无法进行测试。", "错误",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                _statusLabel.Text = "状态: IO测试失败";
                LogHelper.Error("IO测试失败", ex);
                MessageBox.Show($"IO测试失败：{ex.Message}", "错误",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _testIOButton.Enabled = true;
                _testIOButton.Text = "测试IO";
            }
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 刷新IO状态
        /// </summary>
        private async Task RefreshIOStatusAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                UpdateStatus("正在刷新IO状态...");

                var ioManager = DMC1000BIOManager.Instance;
                if (!ioManager.IsInitialized)
                {
                    UpdateStatus("IO管理器未初始化");
                    return false;
                }

                // 读取所有输入状态 - 使用IOConfiguration中的IO编号
                var allInputPorts = IOConfiguration.GetAllInputPorts();
                for (int i = 0; i < Math.Min(MAX_IO_COUNT, allInputPorts.Count); i++)
                {
                    string inputIONumber = allInputPorts[i].IONumber;
                    bool inputState = await ioManager.ReadInputAsync(inputIONumber);
                    UpdateInputStatus(i, inputState);
                    LogHelper.Debug($"刷新输入IO状态: 索引{i} -> {inputIONumber} -> {(inputState ? "ON" : "OFF")}");
                }

                // 读取所有输出状态 - 使用IOConfiguration中的IO编号
                var allOutputPorts = IOConfiguration.GetAllOutputPorts();
                for (int i = 0; i < Math.Min(MAX_IO_COUNT, allOutputPorts.Count); i++)
                {
                    string outputIONumber = allOutputPorts[i].IONumber;
                    bool outputState = await ioManager.ReadOutputAsync(outputIONumber);
                    // 使用强制更新确保UI状态与硬件状态完全同步
                    UpdateOutputStatus(i, outputState, forceUpdate: true);
                    LogHelper.Debug($"刷新输出IO状态: 索引{i} -> {outputIONumber} -> {(outputState ? "ON" : "OFF")}");
                }

                UpdateStatus($"IO状态刷新完成 - {DateTime.Now:HH:mm:ss}");

                return true;
            }, false, "刷新IO状态");
        }

        /// <summary>
        /// 设置输出
        /// </summary>
        private async Task SetOutputAsync(int index, bool state)
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                var ioManager = DMC1000BIOManager.Instance;
                if (!ioManager.IsInitialized)
                {
                    UpdateStatus("IO管理器未初始化");
                    return false;
                }

                // 使用IOConfiguration中的IO编号
                string outputIONumber = GetOutputIONumberByIndex(index);
                if (string.IsNullOrEmpty(outputIONumber))
                {
                    UpdateStatus($"无效的输出IO索引: {index}");
                    return false;
                }

                // 先读取当前硬件状态进行验证
                bool currentHardwareState = await ioManager.ReadOutputAsync(outputIONumber);
                LogHelper.Debug($"当前硬件状态: {outputIONumber} -> {currentHardwareState}, 期望状态: {state}");

                // 如果硬件状态与期望状态已经一致，记录并继续执行（可能是UI状态不同步）
                if (currentHardwareState == state)
                {
                    LogHelper.Info($"硬件状态已经是期望状态: {outputIONumber} -> {state}，但仍执行设置以确保同步");
                }

                bool result = await ioManager.SetOutputAsync(outputIONumber, state);

                if (result)
                {
                    LogHelper.Debug($"设置输出IO成功: 索引{index} -> {outputIONumber} -> {(state ? "ON" : "OFF")}");
                    UpdateStatus($"设置输出IO成功: DO{index:D2} -> {(state ? "ON" : "OFF")}");
                }
                else
                {
                    // 如果设置失败，恢复复选框状态
                    UIHelper.SafeInvoke(() =>
                    {
                        _outputCheckBoxes[index].CheckedChanged -= OutputCheckBox_CheckedChanged;
                        _outputCheckBoxes[index].Checked = !state;
                        _outputCheckBoxes[index].CheckedChanged += OutputCheckBox_CheckedChanged;
                    });

                    UpdateStatus($"设置输出IO失败: {outputIONumber}");
                    LogHelper.Error($"设置输出IO失败: 索引{index} -> {outputIONumber} -> {state}");
                }

                return true;
            }, false, $"设置输出DO{index:D2}");
        }

        /// <summary>
        /// 复位所有输出
        /// </summary>
        private async Task ResetAllOutputsAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                UpdateStatus("正在复位所有输出...");

                var ioManager = DMC1000BIOManager.Instance;
                if (!ioManager.IsInitialized)
                {
                    UpdateStatus("IO管理器未初始化");
                    return false;
                }

                // 复位所有输出IO - 使用IOConfiguration中的IO编号
                var allOutputPorts = IOConfiguration.GetAllOutputPorts();
                for (int i = 0; i < Math.Min(MAX_IO_COUNT, allOutputPorts.Count); i++)
                {
                    string outputIONumber = allOutputPorts[i].IONumber;
                    await ioManager.SetOutputAsync(outputIONumber, false);
                    UpdateOutputStatus(i, false);
                    LogHelper.Debug($"复位输出IO: 索引{i} -> {outputIONumber}");
                }

                UpdateStatus("所有输出已复位");

                return true;
            }, false, "复位所有输出");
        }

        /// <summary>
        /// 更新输入状态显示
        /// </summary>
        private void UpdateInputStatus(int index, bool state)
        {
            if (index >= 0 && index < MAX_IO_COUNT)
            {
                UIHelper.SafeInvoke(() =>
                {
                    _inputStatusLabels[index].Text = state ? "ON" : "OFF";
                    _inputStatusLabels[index].BackColor = state ? Color.LightGreen : Color.LightGray;
                });
            }
        }

        /// <summary>
        /// 更新输出状态显示
        /// </summary>
        /// <param name="index">IO索引</param>
        /// <param name="state">IO状态</param>
        /// <param name="forceUpdate">是否强制更新，即使状态相同</param>
        private void UpdateOutputStatus(int index, bool state, bool forceUpdate = false)
        {
            if (index >= 0 && index < MAX_IO_COUNT)
            {
                UIHelper.SafeInvoke(() =>
                {
                    // 检查是否需要更新
                    if (!forceUpdate && _outputCheckBoxes[index].Checked == state)
                    {
                        LogHelper.Debug($"输出IO{index}状态无变化，跳过更新: {state}");
                        return;
                    }

                    // 临时取消事件订阅，避免触发CheckedChanged
                    _outputCheckBoxes[index].CheckedChanged -= OutputCheckBox_CheckedChanged;
                    _outputCheckBoxes[index].Checked = state;
                    _outputCheckBoxes[index].CheckedChanged += OutputCheckBox_CheckedChanged;

                    LogHelper.Debug($"更新输出IO{index}状态显示: {state}");
                });
            }
        }

        /// <summary>
        /// 更新状态显示
        /// </summary>
        private void UpdateStatus(string message)
        {
            UIHelper.SafeInvoke(() =>
            {
                _statusLabel.Text = $"状态: {message}";
            });
        }

        /// <summary>
        /// 更新翻转电机状态显示
        /// </summary>
        private void UpdateFlipMotorStatus(short axis, string message)
        {
            UIHelper.SafeInvoke(() =>
            {
                if (axis == 0)
                    _leftFlipStatusLabel.Text = $"状态: {message}";
                else if (axis == 1)
                    _rightFlipStatusLabel.Text = $"状态: {message}";
            });
        }
        #endregion

        #region 翻转电机事件处理
        /// <summary>
        /// 左翻转电机回零按钮点击
        /// </summary>
        private async void LeftFlipHomeButton_Click(object sender, EventArgs e)
        {
            await PerformFlipMotorHome(0, "左翻转电机");
        }

        /// <summary>
        /// 右翻转电机回零按钮点击
        /// </summary>
        private async void RightFlipHomeButton_Click(object sender, EventArgs e)
        {
            await PerformFlipMotorHome(1, "右翻转电机");
        }

        /// <summary>
        /// 执行翻转电机回零
        /// </summary>
        private async Task PerformFlipMotorHome(short axis, string motorName)
        {
            var homeButton = axis == 0 ? _leftFlipHomeButton : _rightFlipHomeButton;

            try
            {
                homeButton.Enabled = false;
                homeButton.Text = "回零中...";
                homeButton.BackColor = Color.Orange;

                UpdateFlipMotorStatus(axis, "回零中...");

                bool result = await DMC1000BMotorManager.Instance.FlipMotorHomeAsync(axis);

                if (result)
                {
                    homeButton.BackColor = Color.LightGreen;
                    UpdateFlipMotorStatus(axis, "回零成功");
                    MessageBox.Show($"{motorName}回零成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    homeButton.BackColor = Color.LightCoral;
                    UpdateFlipMotorStatus(axis, "回零失败");
                    MessageBox.Show($"{motorName}回零失败！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                homeButton.BackColor = Color.LightCoral;
                UpdateFlipMotorStatus(axis, "回零异常");
                MessageBox.Show($"{motorName}回零异常: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                LogHelper.Error($"{motorName}回零异常", ex);
            }
            finally
            {
                homeButton.Enabled = true;
                homeButton.Text = "回零";
            }
        }

        /// <summary>
        /// 左翻转电机正向测试按钮点击
        /// </summary>
        private async void LeftFlipPositiveButton_Click(object sender, EventArgs e)
        {
            await PerformFlipMotorTest(0, true, "左翻转电机");
        }

        /// <summary>
        /// 左翻转电机负向测试按钮点击
        /// </summary>
        private async void LeftFlipNegativeButton_Click(object sender, EventArgs e)
        {
            await PerformFlipMotorTest(0, false, "左翻转电机");
        }

        /// <summary>
        /// 右翻转电机正向测试按钮点击
        /// </summary>
        private async void RightFlipPositiveButton_Click(object sender, EventArgs e)
        {
            await PerformFlipMotorTest(1, true, "右翻转电机");
        }

        /// <summary>
        /// 右翻转电机负向测试按钮点击
        /// </summary>
        private async void RightFlipNegativeButton_Click(object sender, EventArgs e)
        {
            await PerformFlipMotorTest(1, false, "右翻转电机");
        }

        /// <summary>
        /// 执行翻转电机方向测试
        /// </summary>
        private async Task PerformFlipMotorTest(short axis, bool positive, string motorName)
        {
            try
            {
                UpdateFlipMotorStatus(axis, positive ? "正向测试中..." : "负向测试中...");

                bool result;
                if (positive)
                {
                    result = await DMC1000BMotorManager.Instance.FlipMotorMovePositiveAsync(axis, 10); // 测试移动10度
                }
                else
                {
                    result = await DMC1000BMotorManager.Instance.FlipMotorMoveNegativeAsync(axis, 10); // 测试移动10度
                }

                if (result)
                {
                    UpdateFlipMotorStatus(axis, positive ? "正向测试完成" : "负向测试完成");
                    string direction = positive ? "正方向" : "负方向";
                    MessageBox.Show($"{motorName}{direction}测试完成！\n请观察电机转动方向，确定哪个是正方向。",
                        "测试完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    UpdateFlipMotorStatus(axis, "测试失败");
                    MessageBox.Show($"{motorName}方向测试失败！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                UpdateFlipMotorStatus(axis, "测试异常");
                MessageBox.Show($"{motorName}方向测试异常: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                LogHelper.Error($"{motorName}方向测试异常", ex);
            }
        }

        /// <summary>
        /// 左翻转电机回零方向复选框变化
        /// </summary>
        private async void LeftFlipDirectionCheckBox_CheckedChanged(object sender, EventArgs e)
        {
            await SetFlipMotorHomeDirection(0, _leftFlipDirectionCheckBox.Checked, "左翻转电机");
        }

        /// <summary>
        /// 右翻转电机回零方向复选框变化
        /// </summary>
        private async void RightFlipDirectionCheckBox_CheckedChanged(object sender, EventArgs e)
        {
            await SetFlipMotorHomeDirection(1, _rightFlipDirectionCheckBox.Checked, "右翻转电机");
        }

        /// <summary>
        /// 设置翻转电机回零方向
        /// </summary>
        private async Task SetFlipMotorHomeDirection(short axis, bool positiveDirection, string motorName)
        {
            try
            {
                bool result = await DMC1000BMotorManager.Instance.SetFlipMotorHomeDirectionAsync(axis, positiveDirection);

                if (result)
                {
                    string direction = positiveDirection ? "正方向" : "负方向";
                    UpdateFlipMotorStatus(axis, $"回零方向: {direction}");
                    LogHelper.Info($"{motorName}回零方向已设置为: {direction}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置{motorName}回零方向失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                LogHelper.Error($"设置{motorName}回零方向失败", ex);
            }
        }
        #endregion
    }
}
