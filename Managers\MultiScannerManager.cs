using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Threading.Tasks;

using MyHMI.Events;
using MyHMI.Models;
using MyHMI.Helpers;
using MyHMI.Settings;

namespace MyHMI.Managers
{
    /// <summary>
    /// 多扫描枪管理器
    /// 支持管理3个独立的扫描枪，每个扫描枪有独立的串口配置和通信状态
    /// </summary>
    public class MultiScannerManager
    {
        #region 单例模式
        private static readonly Lazy<MultiScannerManager> _instance = new Lazy<MultiScannerManager>(() => new MultiScannerManager());
        public static MultiScannerManager Instance => _instance.Value;
        private MultiScannerManager() 
        {
            InitializeScanners();
        }
        #endregion

        #region 事件定义
        /// <summary>
        /// 条码扫描事件
        /// </summary>
        public event EventHandler<MultiBarcodeScannedEventArgs> BarcodeScanned;

        /// <summary>
        /// 通信状态变化事件
        /// </summary>
        public event EventHandler<MultiScannerStatusChangedEventArgs> StatusChanged;
        #endregion

        #region 私有字段
        private readonly Dictionary<int, ScannerInstance> _scanners = new Dictionary<int, ScannerInstance>();
        private bool _isInitialized = false;
        private readonly object _lockObject = new object();
        #endregion

        #region 公共属性
        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// 扫描枪数量
        /// </summary>
        public int ScannerCount => 3;
        #endregion

        #region 初始化和释放
        /// <summary>
        /// 初始化扫描枪实例
        /// </summary>
        private void InitializeScanners()
        {
            for (int i = 1; i <= ScannerCount; i++)
            {
                _scanners[i] = new ScannerInstance(i);
            }
        }

        /// <summary>
        /// 异步初始化多扫描枪管理器
        /// </summary>
        /// <returns></returns>
        public async Task<bool> InitializeAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_isInitialized)
                {
                    LogHelper.Warning("MultiScannerManager已经初始化，跳过重复初始化");
                    return true;
                }

                LogHelper.Info("开始初始化MultiScannerManager...");

                // 初始化所有扫描枪实例
                foreach (var scanner in _scanners.Values)
                {
                    await scanner.InitializeAsync();
                    scanner.BarcodeScanned += OnScannerBarcodeScanned;
                    scanner.StatusChanged += OnScannerStatusChanged;
                }

                _isInitialized = true;
                LogHelper.Info("MultiScannerManager初始化完成");
                return true;

            }, false, "MultiScannerManager初始化");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public async Task DisposeAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info("开始释放MultiScannerManager资源...");

                foreach (var scanner in _scanners.Values)
                {
                    scanner.BarcodeScanned -= OnScannerBarcodeScanned;
                    scanner.StatusChanged -= OnScannerStatusChanged;
                    await scanner.DisposeAsync();
                }

                _scanners.Clear();
                _isInitialized = false;
                LogHelper.Info("MultiScannerManager资源释放完成");

                return true;
            }, false, "MultiScannerManager资源释放");
        }
        #endregion

        #region 扫描枪控制方法
        /// <summary>
        /// 获取指定扫描枪的配置
        /// </summary>
        /// <param name="scannerId">扫描枪ID (1-3)</param>
        /// <returns>串口配置</returns>
        public SerialPortConfiguration GetScannerConfiguration(int scannerId)
        {
            ValidateScannerId(scannerId);
            return _scanners[scannerId].Configuration;
        }

        /// <summary>
        /// 设置指定扫描枪的配置
        /// </summary>
        /// <param name="scannerId">扫描枪ID (1-3)</param>
        /// <param name="config">串口配置</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SetScannerConfigurationAsync(int scannerId, SerialPortConfiguration config)
        {
            ValidateScannerId(scannerId);
            return await _scanners[scannerId].SetConfigurationAsync(config);
        }

        /// <summary>
        /// 连接指定扫描枪
        /// </summary>
        /// <param name="scannerId">扫描枪ID (1-3)</param>
        /// <returns>是否成功</returns>
        public async Task<bool> ConnectScannerAsync(int scannerId)
        {
            ValidateScannerId(scannerId);
            return await _scanners[scannerId].ConnectAsync();
        }

        /// <summary>
        /// 断开指定扫描枪连接
        /// </summary>
        /// <param name="scannerId">扫描枪ID (1-3)</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DisconnectScannerAsync(int scannerId)
        {
            ValidateScannerId(scannerId);
            return await _scanners[scannerId].DisconnectAsync();
        }

        /// <summary>
        /// 向指定扫描枪发送数据
        /// </summary>
        /// <param name="scannerId">扫描枪ID (1-3)</param>
        /// <param name="data">要发送的数据</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SendDataAsync(int scannerId, string data)
        {
            ValidateScannerId(scannerId);
            return await _scanners[scannerId].SendDataAsync(data);
        }

        /// <summary>
        /// 获取指定扫描枪的状态
        /// </summary>
        /// <param name="scannerId">扫描枪ID (1-3)</param>
        /// <returns>通信状态</returns>
        public CommunicationStatus GetScannerStatus(int scannerId)
        {
            ValidateScannerId(scannerId);
            return _scanners[scannerId].Status;
        }

        /// <summary>
        /// 获取系统可用的串口列表
        /// </summary>
        /// <returns>串口名称列表</returns>
        public string[] GetAvailableSerialPorts()
        {
            try
            {
                var allPorts = SerialPort.GetPortNames().OrderBy(p => p).ToArray();
                // 去重处理，避免重复串口
                return allPorts.Distinct().ToArray();
            }
            catch (Exception ex)
            {
                LogHelper.Error("获取可用串口列表失败", ex);
                return new string[0];
            }
        }

        /// <summary>
        /// 获取带占用状态的串口列表
        /// </summary>
        /// <param name="currentScannerId">当前扫描枪ID</param>
        /// <returns>串口名称列表（包含占用状态）</returns>
        public string[] GetAvailableSerialPortsWithStatus(int currentScannerId)
        {
            try
            {
                var allPorts = SerialPort.GetPortNames().Distinct().OrderBy(p => p).ToArray();
                var portsWithStatus = new List<string>();

                foreach (var port in allPorts)
                {
                    // 检查是否被其他扫描枪占用
                    var occupiedBy = GetPortOccupiedBy(port, currentScannerId);
                    if (occupiedBy > 0)
                    {
                        portsWithStatus.Add($"{port}（{occupiedBy}号扫码器）");
                    }
                    else
                    {
                        portsWithStatus.Add(port);
                    }
                }

                return portsWithStatus.ToArray();
            }
            catch (Exception ex)
            {
                LogHelper.Error("获取带状态的串口列表失败", ex);
                return new string[0];
            }
        }

        /// <summary>
        /// 检查串口是否被其他扫描枪占用
        /// </summary>
        /// <param name="portName">串口名称</param>
        /// <param name="currentScannerId">当前扫描枪ID</param>
        /// <returns>占用的扫描枪ID，0表示未占用</returns>
        private int GetPortOccupiedBy(string portName, int currentScannerId)
        {
            lock (_lockObject)
            {
                foreach (var kvp in _scanners)
                {
                    if (kvp.Key != currentScannerId &&
                        kvp.Value.Status == CommunicationStatus.Connected &&
                        kvp.Value.Configuration?.PortName == portName)
                    {
                        return kvp.Key;
                    }
                }
                return 0;
            }
        }
        #endregion

        #region 事件处理
        /// <summary>
        /// 扫描枪条码扫描事件处理 - 增加调试日志
        /// </summary>
        private void OnScannerBarcodeScanned(object sender, BarcodeScannedEventArgs e)
        {
            var scanner = sender as ScannerInstance;
            if (scanner != null)
            {
                LogHelper.Debug($"MultiScannerManager收到扫描枪{scanner.Id}的条码事件: {e.ScannerData.BarcodeData}");
                var multiEventArgs = new MultiBarcodeScannedEventArgs(scanner.Id, e.ScannerData);
                LogHelper.Debug($"MultiScannerManager准备转发扫描枪{scanner.Id}的事件到UI");
                BarcodeScanned?.Invoke(this, multiEventArgs);
                LogHelper.Debug($"MultiScannerManager已转发扫描枪{scanner.Id}的事件");
            }
            else
            {
                LogHelper.Warning("OnScannerBarcodeScanned: sender不是ScannerInstance类型");
            }
        }

        /// <summary>
        /// 扫描枪状态变化事件处理
        /// </summary>
        private void OnScannerStatusChanged(object sender, CommunicationStatusChangedEventArgs e)
        {
            var scanner = sender as ScannerInstance;
            if (scanner != null)
            {
                var multiEventArgs = new MultiScannerStatusChangedEventArgs(scanner.Id, e.Status, e.Description);
                StatusChanged?.Invoke(this, multiEventArgs);
            }
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 验证扫描枪ID的有效性
        /// </summary>
        /// <param name="scannerId">扫描枪ID</param>
        private void ValidateScannerId(int scannerId)
        {
            if (scannerId < 1 || scannerId > ScannerCount)
            {
                throw new ArgumentOutOfRangeException(nameof(scannerId), $"扫描枪ID必须在1到{ScannerCount}之间");
            }

            if (!_scanners.ContainsKey(scannerId))
            {
                throw new InvalidOperationException($"扫描枪{scannerId}未初始化");
            }
        }
        #endregion
    }

    /// <summary>
    /// 单个扫描枪实例
    /// </summary>
    internal class ScannerInstance
    {
        #region 事件定义
        /// <summary>
        /// 条码扫描事件
        /// </summary>
        public event EventHandler<BarcodeScannedEventArgs> BarcodeScanned;

        /// <summary>
        /// 通信状态变化事件
        /// </summary>
        public event EventHandler<CommunicationStatusChangedEventArgs> StatusChanged;
        #endregion

        #region 私有字段
        private SerialPort _serialPort;
        private CommunicationStatus _status = CommunicationStatus.Disconnected;
        private SerialPortConfiguration _config;
        private readonly object _lockObject = new object();
        #endregion

        #region 公共属性
        /// <summary>
        /// 扫描枪ID
        /// </summary>
        public int Id { get; }

        /// <summary>
        /// 当前连接状态
        /// </summary>
        public CommunicationStatus Status => _status;

        /// <summary>
        /// 串口配置
        /// </summary>
        public SerialPortConfiguration Configuration => _config;
        #endregion

        #region 构造函数
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="id">扫描枪ID</param>
        public ScannerInstance(int id)
        {
            Id = id;
            LoadDefaultConfiguration();
        }
        #endregion

        #region 初始化和释放
        /// <summary>
        /// 异步初始化扫描枪实例
        /// </summary>
        /// <returns></returns>
        public async Task<bool> InitializeAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info($"开始初始化扫描枪{Id}...");

                // 从系统配置加载配置
                LoadConfigurationFromSystem();

                // 初始化串口
                await InitializeSerialPortAsync();

                LogHelper.Info($"扫描枪{Id}初始化完成");
                return true;

            }, false, $"扫描枪{Id}初始化");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public async Task DisposeAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info($"开始释放扫描枪{Id}资源...");

                await DisconnectAsync();

                lock (_lockObject)
                {
                    _serialPort?.Dispose();
                    _serialPort = null;
                }

                LogHelper.Info($"扫描枪{Id}资源释放完成");

                return true;
            }, false, $"扫描枪{Id}资源释放");
        }
        #endregion

        #region 配置管理
        /// <summary>
        /// 从系统配置加载扫描器配置
        /// </summary>
        private void LoadConfigurationFromSystem()
        {
            try
            {
                // 从新的Settings系统加载配置
                var communicationSettings = Settings.Settings.Current.Communication;

                // 根据扫描器ID获取对应的配置
                switch (Id)
                {
                    case 1:
                        _config = new SerialPortConfiguration
                        {
                            PortName = communicationSettings.MultiScanner1ComPort,
                            BaudRate = communicationSettings.MultiScanner1BaudRate,
                            DataBits = communicationSettings.MultiScanner1DataBits,
                            StopBits = ParseStopBits(communicationSettings.MultiScanner1StopBits),
                            Parity = ParseParity(communicationSettings.MultiScanner1Parity),
                            ReadTimeout = communicationSettings.MultiScanner1Timeout,
                            WriteTimeout = communicationSettings.MultiScanner1Timeout
                        };
                        break;
                    case 2:
                        _config = new SerialPortConfiguration
                        {
                            PortName = communicationSettings.MultiScanner2ComPort,
                            BaudRate = communicationSettings.MultiScanner2BaudRate,
                            DataBits = communicationSettings.MultiScanner2DataBits,
                            StopBits = ParseStopBits(communicationSettings.MultiScanner2StopBits),
                            Parity = ParseParity(communicationSettings.MultiScanner2Parity),
                            ReadTimeout = communicationSettings.MultiScanner2Timeout,
                            WriteTimeout = communicationSettings.MultiScanner2Timeout
                        };
                        break;
                    case 3:
                        _config = new SerialPortConfiguration
                        {
                            PortName = communicationSettings.MultiScanner3ComPort,
                            BaudRate = communicationSettings.MultiScanner3BaudRate,
                            DataBits = communicationSettings.MultiScanner3DataBits,
                            StopBits = ParseStopBits(communicationSettings.MultiScanner3StopBits),
                            Parity = ParseParity(communicationSettings.MultiScanner3Parity),
                            ReadTimeout = communicationSettings.MultiScanner3Timeout,
                            WriteTimeout = communicationSettings.MultiScanner3Timeout
                        };
                        break;
                    default:
                        LogHelper.Warning($"不支持的扫描器ID: {Id}，使用默认配置");
                        LoadDefaultConfiguration();
                        return;
                }

                LogHelper.Info($"从Settings系统加载扫描枪{Id}配置: {_config.PortName}, {_config.BaudRate}");
            }
            catch (Exception ex)
            {
                LogHelper.Error($"从配置文件加载扫描枪{Id}配置失败", ex);
                LoadDefaultConfiguration();
            }
        }

        /// <summary>
        /// 加载默认配置 - 修复配置覆盖问题
        /// </summary>
        private void LoadDefaultConfiguration()
        {
            try
            {
                // 从新的Settings系统获取配置值
                var communicationSettings = Settings.Settings.Current.Communication;

                // 根据扫描器ID获取对应的配置
                switch (Id)
                {
                    case 1:
                        _config = new SerialPortConfiguration
                        {
                            PortName = communicationSettings.MultiScanner1PortName ?? $"COM{Id}",
                            BaudRate = communicationSettings.MultiScanner1BaudRate,
                            DataBits = communicationSettings.MultiScanner1DataBits,
                            StopBits = ParseStopBits(communicationSettings.MultiScanner1StopBits),
                            Parity = ParseParity(communicationSettings.MultiScanner1Parity),
                            ReadTimeout = communicationSettings.MultiScanner1Timeout,
                            WriteTimeout = communicationSettings.MultiScanner1Timeout
                        };
                        LogHelper.Info($"扫描枪{Id}从Settings加载配置: {_config.PortName}, {_config.BaudRate}, {_config.DataBits}, {_config.StopBits}, {_config.Parity}");
                        break;
                    case 2:
                        _config = new SerialPortConfiguration
                        {
                            PortName = communicationSettings.MultiScanner2PortName ?? $"COM{Id}",
                            BaudRate = communicationSettings.MultiScanner2BaudRate,
                            DataBits = communicationSettings.MultiScanner2DataBits,
                            StopBits = ParseStopBits(communicationSettings.MultiScanner2StopBits),
                            Parity = ParseParity(communicationSettings.MultiScanner2Parity),
                            ReadTimeout = communicationSettings.MultiScanner2Timeout,
                            WriteTimeout = communicationSettings.MultiScanner2Timeout
                        };
                        LogHelper.Info($"扫描枪{Id}从Settings加载配置: {_config.PortName}, {_config.BaudRate}, {_config.DataBits}, {_config.StopBits}, {_config.Parity}");
                        break;
                    case 3:
                        _config = new SerialPortConfiguration
                        {
                            PortName = communicationSettings.MultiScanner3PortName ?? $"COM{Id}",
                            BaudRate = communicationSettings.MultiScanner3BaudRate,
                            DataBits = communicationSettings.MultiScanner3DataBits,
                            StopBits = ParseStopBits(communicationSettings.MultiScanner3StopBits),
                            Parity = ParseParity(communicationSettings.MultiScanner3Parity),
                            ReadTimeout = communicationSettings.MultiScanner3Timeout,
                            WriteTimeout = communicationSettings.MultiScanner3Timeout
                        };
                        LogHelper.Info($"扫描枪{Id}从Settings加载配置: {_config.PortName}, {_config.BaudRate}, {_config.DataBits}, {_config.StopBits}, {_config.Parity}");
                        break;
                    default:
                        // 不支持的ID，使用硬编码默认值
                        _config = new SerialPortConfiguration
                        {
                            PortName = $"COM{Id}",
                            BaudRate = 115200,
                            DataBits = 8,
                            StopBits = StopBits.One,
                            Parity = Parity.None,
                            ReadTimeout = 1000,
                            WriteTimeout = 1000
                        };
                        LogHelper.Warning($"不支持的扫描器ID: {Id}，使用硬编码默认配置: {_config.PortName}, {_config.BaudRate}");
                        break;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"扫描枪{Id}加载默认配置失败，使用硬编码配置", ex);

                // 异常情况下使用硬编码默认值
                _config = new SerialPortConfiguration
                {
                    PortName = $"COM{Id}",
                    BaudRate = 115200,
                    DataBits = 8,
                    StopBits = StopBits.One,
                    Parity = Parity.None,
                    ReadTimeout = 1000,
                    WriteTimeout = 1000
                };
            }
        }

        /// <summary>
        /// 解析停止位字符串
        /// </summary>
        /// <param name="stopBitsStr">停止位字符串</param>
        /// <returns>停止位枚举</returns>
        private StopBits ParseStopBits(string stopBitsStr)
        {
            switch (stopBitsStr)
            {
                case "1": return StopBits.One;
                case "1.5": return StopBits.OnePointFive;
                case "2": return StopBits.Two;
                default: return StopBits.One;
            }
        }

        /// <summary>
        /// 解析奇偶校验字符串
        /// </summary>
        /// <param name="parityStr">奇偶校验字符串</param>
        /// <returns>奇偶校验枚举</returns>
        private Parity ParseParity(string parityStr)
        {
            switch (parityStr)
            {
                case "无校验": return Parity.None;
                case "奇校验": return Parity.Odd;
                case "偶校验": return Parity.Even;
                default: return Parity.None;
            }
        }

        /// <summary>
        /// 判断是否为扫描器触发命令
        /// </summary>
        /// <param name="command">命令字符串（小写）</param>
        /// <returns>是否为触发命令</returns>
        private bool IsTriggerCommand(string command)
        {
            // 常见的扫描器触发命令
            string[] triggerCommands = {
                "start",        // 通用启动命令
                "trigger",      // 触发命令
                "scan",         // 扫描命令
                "read",         // 读取命令
                "t",            // 简短触发命令
                "s",            // 简短扫描命令
                "on",           // 开启命令
                "fire",         // 激发命令
                "activate",     // 激活命令
                "begin"         // 开始命令
            };

            return triggerCommands.Contains(command);
        }

        /// <summary>
        /// 处理接收到的原始数据，支持多种扫描器数据格式
        /// </summary>
        /// <param name="rawData">原始数据</param>
        /// <returns>处理后的数据数组</returns>
        private string[] ProcessReceivedData(string rawData)
        {
            var results = new List<string>();

            try
            {
                // 方法1: 按行分割（适用于多行数据）
                string[] lines = rawData.Split(new char[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                foreach (string line in lines)
                {
                    string cleaned = CleanDataString(line);
                    if (!string.IsNullOrEmpty(cleaned))
                    {
                        results.Add(cleaned);
                    }
                }

                // 方法2: 如果没有找到有效行数据，尝试整体清理
                if (results.Count == 0)
                {
                    string cleaned = CleanDataString(rawData);
                    if (!string.IsNullOrEmpty(cleaned))
                    {
                        results.Add(cleaned);
                    }
                }

                // 方法3: 检查是否包含特殊分隔符（某些扫描器使用特殊分隔符）
                if (results.Count == 0)
                {
                    // 尝试其他常见分隔符
                    char[] separators = { '\t', ';', ',', '|' };
                    foreach (char sep in separators)
                    {
                        if (rawData.Contains(sep))
                        {
                            string[] parts = rawData.Split(sep);
                            foreach (string part in parts)
                            {
                                string cleaned = CleanDataString(part);
                                if (!string.IsNullOrEmpty(cleaned))
                                {
                                    results.Add(cleaned);
                                }
                            }
                            break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"扫描枪{Id}处理接收数据失败", ex);
            }

            return results.ToArray();
        }

        /// <summary>
        /// 清理数据字符串，移除控制字符和无效字符
        /// </summary>
        /// <param name="data">原始数据</param>
        /// <returns>清理后的数据</returns>
        private string CleanDataString(string data)
        {
            if (string.IsNullOrEmpty(data))
            {
                LogHelper.Debug("CleanDataString: 输入数据为空");
                return string.Empty;
            }

            LogHelper.Debug($"CleanDataString: 输入='{data}' (长度:{data.Length})");

            // 移除常见的控制字符
            string cleaned = data.Trim('\r', '\n', '\0', ' ', '\t', '\x1A', '\x03', '\x02');
            LogHelper.Debug($"CleanDataString: 移除控制字符后='{cleaned}' (长度:{cleaned.Length})");

            // 移除不可见字符（保留可打印字符）
            cleaned = new string(cleaned.Where(c => !char.IsControl(c) || char.IsWhiteSpace(c)).ToArray());
            LogHelper.Debug($"CleanDataString: 移除不可见字符后='{cleaned}' (长度:{cleaned.Length})");

            // 再次清理空白字符
            cleaned = cleaned.Trim();
            LogHelper.Debug($"CleanDataString: 最终清理后='{cleaned}' (长度:{cleaned.Length})");

            // 过滤掉太短的数据（可能是噪声）
            if (cleaned.Length < 3)
            {
                LogHelper.Debug($"CleanDataString: 数据太短({cleaned.Length}<3)，返回空字符串");
                return string.Empty;
            }

            LogHelper.Debug($"CleanDataString: 返回清理后的数据='{cleaned}'");
            return cleaned;
        }

        /// <summary>
        /// 判断是否为扫描器状态响应 - 增加调试日志
        /// </summary>
        /// <param name="data">接收到的数据</param>
        /// <returns>是否为状态响应</returns>
        private bool IsStatusResponse(string data)
        {
            if (string.IsNullOrEmpty(data))
            {
                LogHelper.Debug("IsStatusResponse: 数据为空，返回false");
                return false;
            }

            string lowerData = data.ToLower();
            LogHelper.Debug($"IsStatusResponse检查: 原始='{data}', 小写='{lowerData}'");

            // 常见的扫描器状态响应
            string[] statusResponses = {
                "noread",       // 没有读取到条码
                "no read",      // 没有读取到条码（带空格）
                "error",        // 错误
                "ok",           // 成功确认
                "ack",          // 确认
                "nak",          // 否定确认
                "ready",        // 就绪
                "busy",         // 忙碌
                "timeout",      // 超时
                "fail",         // 失败
                "success",      // 成功
                "trigger",      // 触发确认
                "scanning",     // 正在扫描
                "idle"          // 空闲
            };

            bool isStatus = statusResponses.Any(response => lowerData.Contains(response));
            LogHelper.Debug($"IsStatusResponse结果: {isStatus} (匹配的响应: {string.Join(", ", statusResponses.Where(r => lowerData.Contains(r)))})");

            return isStatus;
        }

        /// <summary>
        /// 停止位枚举转字符串
        /// </summary>
        /// <param name="stopBits">停止位枚举</param>
        /// <returns>停止位字符串</returns>
        private string StopBitsToString(StopBits stopBits)
        {
            switch (stopBits)
            {
                case StopBits.One: return "1";
                case StopBits.OnePointFive: return "1.5";
                case StopBits.Two: return "2";
                default: return "1";
            }
        }

        /// <summary>
        /// 奇偶校验枚举转字符串
        /// </summary>
        /// <param name="parity">奇偶校验枚举</param>
        /// <returns>奇偶校验字符串</returns>
        private string ParityToString(Parity parity)
        {
            switch (parity)
            {
                case Parity.None: return "无校验";
                case Parity.Odd: return "奇校验";
                case Parity.Even: return "偶校验";
                default: return "无校验";
            }
        }

        /// <summary>
        /// 保存配置到系统配置
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> SaveConfigurationAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    var communicationSettings = Settings.Settings.Current.Communication;
                    if (communicationSettings == null)
                    {
                        LogHelper.Warning($"系统配置中通信配置节点不存在，无法保存扫描枪{Id}配置");
                        return false;
                    }

                    // 根据扫描器ID保存到对应的配置属性
                    switch (Id)
                    {
                        case 1:
                            communicationSettings.MultiScanner1PortName = _config.PortName;
                            communicationSettings.MultiScanner1BaudRate = _config.BaudRate;
                            communicationSettings.MultiScanner1DataBits = _config.DataBits;
                            communicationSettings.MultiScanner1StopBits = StopBitsToString(_config.StopBits);
                            communicationSettings.MultiScanner1Parity = ParityToString(_config.Parity);
                            communicationSettings.MultiScanner1Timeout = _config.ReadTimeout;
                            break;
                        case 2:
                            communicationSettings.MultiScanner2PortName = _config.PortName;
                            communicationSettings.MultiScanner2BaudRate = _config.BaudRate;
                            communicationSettings.MultiScanner2DataBits = _config.DataBits;
                            communicationSettings.MultiScanner2StopBits = StopBitsToString(_config.StopBits);
                            communicationSettings.MultiScanner2Parity = ParityToString(_config.Parity);
                            communicationSettings.MultiScanner2Timeout = _config.ReadTimeout;
                            break;
                        case 3:
                            communicationSettings.MultiScanner3PortName = _config.PortName;
                            communicationSettings.MultiScanner3BaudRate = _config.BaudRate;
                            communicationSettings.MultiScanner3DataBits = _config.DataBits;
                            communicationSettings.MultiScanner3StopBits = StopBitsToString(_config.StopBits);
                            communicationSettings.MultiScanner3Parity = ParityToString(_config.Parity);
                            communicationSettings.MultiScanner3Timeout = _config.ReadTimeout;
                            break;
                        default:
                            LogHelper.Warning($"无效的扫描器ID: {Id}");
                            return false;
                    }

                    // 保存配置文件
                    bool saveSuccess = Settings.Settings.Save();
                    if (saveSuccess)
                    {
                        LogHelper.Info($"扫描枪{Id}配置保存成功: {_config.PortName}, {_config.BaudRate}, {_config.DataBits}, {StopBitsToString(_config.StopBits)}, {ParityToString(_config.Parity)}");
                    }
                    else
                    {
                        LogHelper.Warning($"扫描枪{Id}配置保存失败: {_config.PortName}, {_config.BaudRate}, {_config.DataBits}, {StopBitsToString(_config.StopBits)}, {ParityToString(_config.Parity)}");
                    }
                    return saveSuccess;
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"保存扫描枪{Id}配置失败", ex);
                    return false;
                }
            });
        }

        /// <summary>
        /// 设置配置
        /// </summary>
        /// <param name="config">串口配置</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SetConfigurationAsync(SerialPortConfiguration config)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (config == null)
                    throw new ArgumentNullException(nameof(config));

                // 如果正在连接，先断开
                if (_status == CommunicationStatus.Connected)
                {
                    await DisconnectAsync();
                }

                _config = config;

                // 重新初始化串口
                await InitializeSerialPortAsync();

                // 异步保存到配置文件（不等待结果，避免阻塞）
                _ = Task.Run(async () =>
                {
                    bool saveSuccess = await SaveConfigurationAsync();
                    if (!saveSuccess)
                    {
                        LogHelper.Warning($"扫描枪{Id}配置保存到配置文件失败");
                    }
                });

                LogHelper.Info($"扫描枪{Id}配置更新: {_config.PortName}, {_config.BaudRate}");
                return true;

            }, false, $"扫描枪{Id}配置设置");
        }
        #endregion

        #region 连接管理
        /// <summary>
        /// 异步连接扫描枪
        /// </summary>
        /// <returns></returns>
        public async Task<bool> ConnectAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_status == CommunicationStatus.Connected)
                {
                    LogHelper.Info($"扫描枪{Id}已经连接");
                    return true;
                }

                UpdateStatus(CommunicationStatus.Connecting, $"正在连接扫描枪{Id}...");

                await Task.Run(() =>
                {
                    lock (_lockObject)
                    {
                        if (_serialPort != null && !_serialPort.IsOpen)
                        {
                            _serialPort.Open();
                            LogHelper.Info($"扫描枪{Id}连接成功: {_config.PortName}");
                        }
                    }
                });

                UpdateStatus(CommunicationStatus.Connected, $"扫描枪{Id}连接成功");
                return true;

            }, false, $"连接扫描枪{Id}");
        }

        /// <summary>
        /// 异步断开连接
        /// </summary>
        /// <returns></returns>
        public async Task<bool> DisconnectAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_status == CommunicationStatus.Disconnected)
                {
                    return true;
                }

                await Task.Run(() =>
                {
                    lock (_lockObject)
                    {
                        if (_serialPort != null && _serialPort.IsOpen)
                        {
                            _serialPort.Close();
                            LogHelper.Info($"扫描枪{Id}连接已断开");
                        }
                    }
                });

                UpdateStatus(CommunicationStatus.Disconnected, $"扫描枪{Id}连接已断开");
                return true;

            }, false, $"断开扫描枪{Id}连接");
        }

        /// <summary>
        /// 发送数据
        /// 增强发送功能，支持不同的数据格式和调试信息
        /// </summary>
        /// <param name="data">要发送的数据</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SendDataAsync(string data)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_status != CommunicationStatus.Connected)
                {
                    throw new InvalidOperationException($"扫描枪{Id}未连接");
                }

                if (string.IsNullOrEmpty(data))
                {
                    throw new ArgumentException("发送数据不能为空");
                }

                // 先获取发送参数，避免在lock中使用await
                string lowerData = data.ToLower().Trim();
                bool isTrigger = IsTriggerCommand(lowerData);

                await Task.Run(() =>
                {
                    lock (_lockObject)
                    {
                        if (_serialPort != null && _serialPort.IsOpen)
                        {
                            // 增强发送逻辑：支持多种扫描器触发命令格式
                            LogHelper.Info($"扫描枪{Id}准备发送数据: [{data}] (长度:{data.Length})");

                            if (isTrigger)
                            {
                                // 扫描器触发命令 - 根据串口助手的成功格式发送
                                LogHelper.Info($"扫描枪{Id}识别为触发命令，使用串口助手验证的格式发送");

                                // 根据串口助手的成功经验：发送纯文本命令，不带任何结束符
                                // 串口助手发送: 73 74 61 72 74 = "start" (纯文本，无结束符)
                                _serialPort.Write(data);  // 纯文本，不添加CR或CRLF

                                // 记录发送的十六进制数据，便于对比
                                byte[] sentBytes = System.Text.Encoding.UTF8.GetBytes(data);
                                string hexSent = BitConverter.ToString(sentBytes).Replace("-", " ");
                                LogHelper.Info($"扫描枪{Id}发送触发命令: [{data}] HEX:[{hexSent}]");

                                // 短暂延迟等待响应
                                System.Threading.Thread.Sleep(100);
                            }
                            else
                            {
                                // 普通数据命令
                                _serialPort.WriteLine(data);
                                LogHelper.Info($"扫描枪{Id}发送普通数据: {data} + CRLF");
                            }

                            // 强制刷新缓冲区
                            _serialPort.BaseStream.Flush();
                        }
                        else
                        {
                            LogHelper.Warning($"扫描枪{Id}串口未打开，无法发送数据");
                        }
                    }
                });

                return true;

            }, false, $"扫描枪{Id}发送数据");
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 初始化串口
        /// </summary>
        /// <returns></returns>
        private async Task InitializeSerialPortAsync()
        {
            await Task.Run(() =>
            {
                lock (_lockObject)
                {
                    // 释放旧的串口
                    if (_serialPort != null)
                    {
                        if (_serialPort.IsOpen)
                            _serialPort.Close();
                        _serialPort.Dispose();
                    }

                    _serialPort = new SerialPort
                    {
                        PortName = _config.PortName,
                        BaudRate = _config.BaudRate,
                        DataBits = _config.DataBits,
                        StopBits = _config.StopBits,
                        Parity = _config.Parity,
                        ReadTimeout = _config.ReadTimeout,
                        WriteTimeout = _config.WriteTimeout,
                        Encoding = System.Text.Encoding.UTF8
                    };

                    // 订阅数据接收事件
                    _serialPort.DataReceived += SerialPort_DataReceived;
                    _serialPort.ErrorReceived += SerialPort_ErrorReceived;

                    LogHelper.Info($"扫描枪{Id}串口初始化完成");
                }
            });
        }

        /// <summary>
        /// 串口数据接收事件处理
        /// 优化数据接收逻辑，支持不同格式的扫码器数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void SerialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            ExceptionHelper.SafeExecute(() =>
            {
                if (_serialPort == null || !_serialPort.IsOpen)
                    return;

                try
                {
                    // 优化数据读取：使用ReadExisting()读取所有可用数据
                    string rawData = _serialPort.ReadExisting();

                    if (!string.IsNullOrEmpty(rawData))
                    {
                        // 显示原始数据的十六进制表示，便于调试
                        string hexData = BitConverter.ToString(System.Text.Encoding.UTF8.GetBytes(rawData)).Replace("-", " ");
                        LogHelper.Debug($"扫描枪{Id}接收到原始数据: [{rawData}] (长度:{rawData.Length}) HEX:[{hexData}]");

                        // 增强数据处理：支持多种数据格式
                        string[] processedData = ProcessReceivedData(rawData);

                        foreach (string cleanData in processedData)
                        {
                            if (!string.IsNullOrEmpty(cleanData))
                            {
                                LogHelper.Info($"扫描枪{Id}处理后的有效数据: [{cleanData}]");

                                // 检查是否为扫描器状态响应
                                if (IsStatusResponse(cleanData))
                                {
                                    LogHelper.Info($"扫描枪{Id}收到状态响应: {cleanData}");
                                    // 状态响应也显示在UI中，让用户知道扫描器已响应
                                    var statusData = new ScannerData($"[状态] {cleanData}");
                                    LogHelper.Debug($"扫描枪{Id}准备触发状态响应事件: {statusData.BarcodeData}");
                                    BarcodeScanned?.Invoke(this, new BarcodeScannedEventArgs(statusData));
                                    LogHelper.Debug($"扫描枪{Id}状态响应事件已触发");
                                }
                                else
                                {
                                    // 正常的条码数据
                                    var scannerData = new ScannerData(cleanData);
                                    LogHelper.Debug($"扫描枪{Id}准备触发条码数据事件: {scannerData.BarcodeData}");
                                    BarcodeScanned?.Invoke(this, new BarcodeScannedEventArgs(scannerData));
                                    LogHelper.Debug($"扫描枪{Id}条码数据事件已触发");
                                }
                            }
                        }

                        if (processedData.Length == 0 || processedData.All(string.IsNullOrEmpty))
                        {
                            LogHelper.Debug($"扫描枪{Id}接收到数据但处理后为空，可能是控制字符或响应确认");
                        }
                    }
                }
                catch (TimeoutException)
                {
                    LogHelper.Warning($"扫描枪{Id}串口读取超时");
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"扫描枪{Id}处理串口数据失败", ex);
                }

            }, $"扫描枪{Id}处理串口数据接收");
        }

        /// <summary>
        /// 串口错误事件处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void SerialPort_ErrorReceived(object sender, SerialErrorReceivedEventArgs e)
        {
            LogHelper.Error($"扫描枪{Id}串口错误: {e.EventType}");
            UpdateStatus(CommunicationStatus.Error, $"扫描枪{Id}串口错误: {e.EventType}");
        }



        /// <summary>
        /// 更新连接状态
        /// </summary>
        /// <param name="status">新状态</param>
        /// <param name="description">状态描述</param>
        private void UpdateStatus(CommunicationStatus status, string description)
        {
            if (_status != status)
            {
                _status = status;
                LogHelper.Info($"扫描枪{Id}状态变更: {status} - {description}");
                StatusChanged?.Invoke(this, new CommunicationStatusChangedEventArgs($"Scanner{Id}", status, description));
            }
        }
        #endregion
    }
}
