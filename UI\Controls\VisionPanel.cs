using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using MyHMI.Managers;
using MyHMI.Events;
using MyHMI.Helpers;
using MyHMI.Models;

namespace MyHMI.UI.Controls
{
    /// <summary>
    /// 视觉系统面板
    /// </summary>
    public partial class VisionPanel : UserControl
    {
        #region 私有字段
        private GroupBox _cameraControlGroupBox;
        private GroupBox _detectionResultGroupBox;
        private GroupBox _parametersGroupBox;
        
        private Button _startCaptureButton;
        private Button _stopCaptureButton;
        private Button _singleDetectButton;
        private Label _cameraStatusLabel;
        
        private Label _resultXLabel;
        private Label _resultYLabel;
        private Label _resultAngleLabel;
        private Label _confidenceLabel;
        private Label _processingTimeLabel;
        
        private NumericUpDown _confidenceThresholdNumericUpDown;
        private NumericUpDown _captureIntervalNumericUpDown;
        private Button _applyParametersButton;
        
        private PictureBox _imageDisplayPictureBox;
        #endregion

        #region 构造函数
        public VisionPanel()
        {
            InitializeComponent();
            InitializeUI();
        }
        #endregion

        #region 初始化方法
        /// <summary>
        /// 初始化UI组件
        /// </summary>
        private void InitializeUI()
        {
            this.Dock = DockStyle.Fill;
            this.BackColor = Color.White;

            CreateCameraControlGroup();
            CreateDetectionResultGroup();
            CreateParametersGroup();
            CreateImageDisplay();
            
            LayoutControls();
        }

        /// <summary>
        /// 创建相机控制组
        /// </summary>
        private void CreateCameraControlGroup()
        {
            _cameraControlGroupBox = new GroupBox
            {
                Text = "相机控制",
                Font = new Font("微软雅黑", 10F, FontStyle.Bold),
                Size = new Size(300, 120),
                Location = new Point(10, 10)
            };

            _startCaptureButton = new Button
            {
                Text = "开始采集",
                Size = new Size(80, 30),
                Location = new Point(20, 30),
                UseVisualStyleBackColor = true
            };
            _startCaptureButton.Click += StartCaptureButton_Click;

            _stopCaptureButton = new Button
            {
                Text = "停止采集",
                Size = new Size(80, 30),
                Location = new Point(110, 30),
                UseVisualStyleBackColor = true,
                Enabled = false
            };
            _stopCaptureButton.Click += StopCaptureButton_Click;

            _singleDetectButton = new Button
            {
                Text = "单次检测",
                Size = new Size(80, 30),
                Location = new Point(200, 30),
                UseVisualStyleBackColor = true
            };
            _singleDetectButton.Click += SingleDetectButton_Click;

            _cameraStatusLabel = new Label
            {
                Text = "相机状态: 未连接",
                Size = new Size(250, 20),
                Location = new Point(20, 75),
                Font = new Font("微软雅黑", 9F)
            };

            _cameraControlGroupBox.Controls.AddRange(new Control[] 
            { 
                _startCaptureButton, _stopCaptureButton, _singleDetectButton, _cameraStatusLabel 
            });

            this.Controls.Add(_cameraControlGroupBox);
        }

        /// <summary>
        /// 创建检测结果组
        /// </summary>
        private void CreateDetectionResultGroup()
        {
            _detectionResultGroupBox = new GroupBox
            {
                Text = "检测结果",
                Font = new Font("微软雅黑", 10F, FontStyle.Bold),
                Size = new Size(300, 150),
                Location = new Point(320, 10)
            };

            _resultXLabel = new Label
            {
                Text = "X坐标: --",
                Size = new Size(120, 20),
                Location = new Point(20, 30),
                Font = new Font("微软雅黑", 9F)
            };

            _resultYLabel = new Label
            {
                Text = "Y坐标: --",
                Size = new Size(120, 20),
                Location = new Point(150, 30),
                Font = new Font("微软雅黑", 9F)
            };

            _resultAngleLabel = new Label
            {
                Text = "角度: --",
                Size = new Size(120, 20),
                Location = new Point(20, 60),
                Font = new Font("微软雅黑", 9F)
            };

            _confidenceLabel = new Label
            {
                Text = "置信度: --",
                Size = new Size(120, 20),
                Location = new Point(150, 60),
                Font = new Font("微软雅黑", 9F)
            };

            _processingTimeLabel = new Label
            {
                Text = "处理时间: --",
                Size = new Size(200, 20),
                Location = new Point(20, 90),
                Font = new Font("微软雅黑", 9F)
            };

            _detectionResultGroupBox.Controls.AddRange(new Control[] 
            { 
                _resultXLabel, _resultYLabel, _resultAngleLabel, _confidenceLabel, _processingTimeLabel
            });

            this.Controls.Add(_detectionResultGroupBox);
        }

        /// <summary>
        /// 创建参数设置组
        /// </summary>
        private void CreateParametersGroup()
        {
            _parametersGroupBox = new GroupBox
            {
                Text = "参数设置",
                Font = new Font("微软雅黑", 10F, FontStyle.Bold),
                Size = new Size(300, 100),
                Location = new Point(630, 10)
            };

            var confidenceLabel = new Label
            {
                Text = "置信度阈值:",
                Size = new Size(80, 20),
                Location = new Point(10, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            _confidenceThresholdNumericUpDown = new NumericUpDown
            {
                Size = new Size(80, 25),
                Location = new Point(100, 28),
                Minimum = 0,
                Maximum = 100,
                Value = 80,
                DecimalPlaces = 0
            };

            var intervalLabel = new Label
            {
                Text = "采集间隔(ms):",
                Size = new Size(80, 20),
                Location = new Point(10, 60),
                TextAlign = ContentAlignment.MiddleRight
            };

            _captureIntervalNumericUpDown = new NumericUpDown
            {
                Size = new Size(80, 25),
                Location = new Point(100, 58),
                Minimum = 100,
                Maximum = 10000,
                Value = 1000,
                Increment = 100
            };

            _applyParametersButton = new Button
            {
                Text = "应用",
                Size = new Size(60, 25),
                Location = new Point(200, 40),
                UseVisualStyleBackColor = true
            };
            _applyParametersButton.Click += ApplyParametersButton_Click;

            _parametersGroupBox.Controls.AddRange(new Control[] 
            { 
                confidenceLabel, _confidenceThresholdNumericUpDown,
                intervalLabel, _captureIntervalNumericUpDown, _applyParametersButton
            });

            this.Controls.Add(_parametersGroupBox);
        }

        /// <summary>
        /// 创建图像显示区域
        /// </summary>
        private void CreateImageDisplay()
        {
            _imageDisplayPictureBox = new PictureBox
            {
                Size = new Size(920, 400),
                Location = new Point(10, 170),
                BorderStyle = BorderStyle.FixedSingle,
                SizeMode = PictureBoxSizeMode.Zoom,
                BackColor = Color.Black
            };

            // 添加占位文本
            var placeholderLabel = new Label
            {
                Text = "图像显示区域",
                ForeColor = Color.White,
                BackColor = Color.Transparent,
                Size = new Size(200, 30),
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("微软雅黑", 12F)
            };
            placeholderLabel.Location = new Point(
                (_imageDisplayPictureBox.Width - placeholderLabel.Width) / 2,
                (_imageDisplayPictureBox.Height - placeholderLabel.Height) / 2);
            
            _imageDisplayPictureBox.Controls.Add(placeholderLabel);
            this.Controls.Add(_imageDisplayPictureBox);
        }

        /// <summary>
        /// 布局控件
        /// </summary>
        private void LayoutControls()
        {
            this.MinimumSize = new Size(940, 580);
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 异步初始化面板
        /// </summary>
        public async Task InitializeAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                // 订阅视觉系统事件
                VisionManager.Instance.VisionResultReady += VisionManager_VisionResultReady;
                VisionManager.Instance.VisionError += VisionManager_VisionError;

                // 更新初始状态
                await UpdateVisionStatusAsync();

                LogHelper.Info("视觉面板初始化完成");

                return true;
            }, false, "视觉面板初始化");
        }

        /// <summary>
        /// 异步释放资源
        /// </summary>
        public async Task DisposeAsync()
        {
            await Task.Run(() =>
            {
                // 取消订阅事件
                VisionManager.Instance.VisionResultReady -= VisionManager_VisionResultReady;
                VisionManager.Instance.VisionError -= VisionManager_VisionError;

                LogHelper.Info("视觉面板资源释放完成");
            });
        }

        /// <summary>
        /// 选项卡激活时调用
        /// </summary>
        public void OnTabActivated()
        {
            // 刷新状态显示
            Task.Run(async () => await UpdateVisionStatusAsync());
        }
        #endregion

        #region 事件处理器
        /// <summary>
        /// 开始采集按钮点击事件
        /// </summary>
        private async void StartCaptureButton_Click(object sender, EventArgs e)
        {
            await StartCaptureAsync();
        }

        /// <summary>
        /// 停止采集按钮点击事件
        /// </summary>
        private async void StopCaptureButton_Click(object sender, EventArgs e)
        {
            await StopCaptureAsync();
        }

        /// <summary>
        /// 单次检测按钮点击事件
        /// </summary>
        private async void SingleDetectButton_Click(object sender, EventArgs e)
        {
            await SingleDetectAsync();
        }

        /// <summary>
        /// 应用参数按钮点击事件
        /// </summary>
        private void ApplyParametersButton_Click(object sender, EventArgs e)
        {
            ApplyParameters();
        }

        /// <summary>
        /// 视觉结果就绪事件处理
        /// </summary>
        private void VisionManager_VisionResultReady(object sender, VisionResultReadyEventArgs e)
        {
            UIHelper.SafeInvoke(() =>
            {
                UpdateDetectionResult(e.Result);
            });
        }

        /// <summary>
        /// 视觉错误事件处理
        /// </summary>
        private void VisionManager_VisionError(object sender, VisionErrorEventArgs e)
        {
            UIHelper.SafeInvoke(() =>
            {
                _cameraStatusLabel.Text = $"相机状态: 错误 - {e.ErrorMessage}";
                _cameraStatusLabel.ForeColor = Color.Red;
            });
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 开始采集
        /// </summary>
        private async Task StartCaptureAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                bool result = await VisionManager.Instance.StartCaptureAsync();
                
                if (result)
                {
                    _startCaptureButton.Enabled = false;
                    _stopCaptureButton.Enabled = true;
                    _cameraStatusLabel.Text = "相机状态: 采集中";
                    _cameraStatusLabel.ForeColor = Color.Green;
                }

                return true;
            }, false, "开始视觉采集");
        }

        /// <summary>
        /// 停止采集
        /// </summary>
        private async Task StopCaptureAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                bool result = await VisionManager.Instance.StopCaptureAsync();
                
                if (result)
                {
                    _startCaptureButton.Enabled = true;
                    _stopCaptureButton.Enabled = false;
                    _cameraStatusLabel.Text = "相机状态: 已停止";
                    _cameraStatusLabel.ForeColor = Color.Black;
                }

                return true;
            }, false, "停止视觉采集");
        }

        /// <summary>
        /// 单次检测
        /// </summary>
        private async Task SingleDetectAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                var result = await VisionManager.Instance.DetectOnceAsync();
                UpdateDetectionResult(result);

                return true;
            }, false, "单次视觉检测");
        }

        /// <summary>
        /// 应用参数
        /// </summary>
        private void ApplyParameters()
        {
            double confidenceThreshold = (double)_confidenceThresholdNumericUpDown.Value / 100.0;
            int captureInterval = (int)_captureIntervalNumericUpDown.Value;
            
            VisionManager.Instance.SetDetectionParameters(confidenceThreshold, captureInterval);
            
            LogHelper.Info($"应用视觉参数: 置信度阈值={confidenceThreshold:F2}, 采集间隔={captureInterval}ms");
        }

        /// <summary>
        /// 更新检测结果显示
        /// </summary>
        private void UpdateDetectionResult(Models.VisionResult result)
        {
            if (result.IsSuccess)
            {
                _resultXLabel.Text = $"X坐标: {result.X:F3}";
                _resultYLabel.Text = $"Y坐标: {result.Y:F3}";
                _resultAngleLabel.Text = $"角度: {result.Angle:F2}°";
                _confidenceLabel.Text = $"置信度: {result.Confidence:F3}";
                _processingTimeLabel.Text = $"处理时间: {result.ProcessingTimeMs:F1}ms";
                
                // 设置成功颜色
                _resultXLabel.ForeColor = Color.Green;
                _resultYLabel.ForeColor = Color.Green;
                _resultAngleLabel.ForeColor = Color.Green;
                _confidenceLabel.ForeColor = Color.Green;
            }
            else
            {
                _resultXLabel.Text = "X坐标: 失败";
                _resultYLabel.Text = "Y坐标: 失败";
                _resultAngleLabel.Text = "角度: 失败";
                _confidenceLabel.Text = "置信度: --";
                _processingTimeLabel.Text = $"处理时间: {result.ProcessingTimeMs:F1}ms";
                
                // 设置失败颜色
                _resultXLabel.ForeColor = Color.Red;
                _resultYLabel.ForeColor = Color.Red;
                _resultAngleLabel.ForeColor = Color.Red;
                _confidenceLabel.ForeColor = Color.Red;
            }
        }

        /// <summary>
        /// 更新视觉状态
        /// </summary>
        private async Task UpdateVisionStatusAsync()
        {
            await Task.Run(() =>
            {
                UIHelper.SafeInvoke(() =>
                {
                    bool isConnected = VisionManager.Instance.IsCameraConnected;
                    bool isCapturing = VisionManager.Instance.IsCapturing;
                    
                    if (isCapturing)
                    {
                        _cameraStatusLabel.Text = "相机状态: 采集中";
                        _cameraStatusLabel.ForeColor = Color.Green;
                        _startCaptureButton.Enabled = false;
                        _stopCaptureButton.Enabled = true;
                    }
                    else if (isConnected)
                    {
                        _cameraStatusLabel.Text = "相机状态: 已连接";
                        _cameraStatusLabel.ForeColor = Color.Black;
                        _startCaptureButton.Enabled = true;
                        _stopCaptureButton.Enabled = false;
                    }
                    else
                    {
                        _cameraStatusLabel.Text = "相机状态: 未连接";
                        _cameraStatusLabel.ForeColor = Color.Red;
                        _startCaptureButton.Enabled = false;
                        _stopCaptureButton.Enabled = false;
                    }
                });
            });
        }
        #endregion
    }
}
