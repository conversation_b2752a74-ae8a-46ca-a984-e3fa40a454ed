using System;
using System.Collections.Generic;
using MyHMI.Models;

namespace MyHMI.Events
{
    /// <summary>
    /// 条码扫描事件参数
    /// </summary>
    public class BarcodeScannedEventArgs : EventArgs
    {
        /// <summary>
        /// 扫描数据
        /// </summary>
        public ScannerData ScannerData { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="scannerData">扫描数据</param>
        public BarcodeScannedEventArgs(ScannerData scannerData)
        {
            ScannerData = scannerData;
            EventTime = DateTime.Now;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="barcodeData">条码数据</param>
        public BarcodeScannedEventArgs(string barcodeData)
        {
            ScannerData = new ScannerData(barcodeData);
            EventTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 多扫描枪条码扫描事件参数
    /// </summary>
    public class MultiBarcodeScannedEventArgs : EventArgs
    {
        /// <summary>
        /// 扫描枪ID
        /// </summary>
        public int ScannerId { get; set; }

        /// <summary>
        /// 扫描数据
        /// </summary>
        public ScannerData ScannerData { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="scannerId">扫描枪ID</param>
        /// <param name="scannerData">扫描数据</param>
        public MultiBarcodeScannedEventArgs(int scannerId, ScannerData scannerData)
        {
            ScannerId = scannerId;
            ScannerData = scannerData;
            EventTime = DateTime.Now;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="scannerId">扫描枪ID</param>
        /// <param name="barcodeData">条码数据</param>
        public MultiBarcodeScannedEventArgs(int scannerId, string barcodeData)
        {
            ScannerId = scannerId;
            ScannerData = new ScannerData(barcodeData);
            EventTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 多扫描枪状态变化事件参数
    /// </summary>
    public class MultiScannerStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 扫描枪ID
        /// </summary>
        public int ScannerId { get; set; }

        /// <summary>
        /// 通信状态
        /// </summary>
        public CommunicationStatus Status { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="scannerId">扫描枪ID</param>
        /// <param name="status">通信状态</param>
        /// <param name="description">状态描述</param>
        public MultiScannerStatusChangedEventArgs(int scannerId, CommunicationStatus status, string description)
        {
            ScannerId = scannerId;
            Status = status;
            Description = description;
            EventTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 通信连接状态变化事件参数
    /// </summary>
    public class CommunicationStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 连接名称/标识
        /// </summary>
        public string ConnectionName { get; set; }

        /// <summary>
        /// 连接状态
        /// </summary>
        public CommunicationStatus Status { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string StatusDescription { get; set; }

        /// <summary>
        /// 错误信息（如果有）
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 描述信息（兼容性属性）
        /// </summary>
        public string Description => StatusDescription;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="connectionName">连接名称</param>
        /// <param name="status">连接状态</param>
        /// <param name="statusDescription">状态描述</param>
        /// <param name="errorMessage">错误信息</param>
        public CommunicationStatusChangedEventArgs(string connectionName, CommunicationStatus status, 
            string statusDescription = "", string errorMessage = "")
        {
            ConnectionName = connectionName ?? string.Empty;
            Status = status;
            StatusDescription = statusDescription ?? string.Empty;
            ErrorMessage = errorMessage ?? string.Empty;
            EventTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 机器人响应接收事件参数
    /// </summary>
    public class RobotResponseReceivedEventArgs : EventArgs
    {
        /// <summary>
        /// 机器人响应
        /// </summary>
        public RobotResponse Response { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="response">机器人响应</param>
        public RobotResponseReceivedEventArgs(RobotResponse response)
        {
            Response = response;
            EventTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 机器人错误事件参数
    /// </summary>
    public class RobotErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 错误类型
        /// </summary>
        public RobotErrorType ErrorType { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 相关指令ID（如果有）
        /// </summary>
        public string CommandId { get; set; }

        /// <summary>
        /// 是否为严重错误
        /// </summary>
        public bool IsCritical { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="errorType">错误类型</param>
        /// <param name="errorMessage">错误信息</param>
        /// <param name="commandId">指令ID</param>
        /// <param name="isCritical">是否严重</param>
        public RobotErrorEventArgs(RobotErrorType errorType, string errorMessage, 
            string commandId = "", bool isCritical = false)
        {
            ErrorType = errorType;
            ErrorMessage = errorMessage ?? string.Empty;
            CommandId = commandId ?? string.Empty;
            IsCritical = isCritical;
            EventTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 机器人错误类型枚举
    /// </summary>
    public enum RobotErrorType
    {
        /// <summary>
        /// 通信超时
        /// </summary>
        CommunicationTimeout,

        /// <summary>
        /// 连接断开
        /// </summary>
        ConnectionLost,

        /// <summary>
        /// 指令执行失败
        /// </summary>
        CommandExecutionFailed,

        /// <summary>
        /// 机器人报警
        /// </summary>
        RobotAlarm,

        /// <summary>
        /// 位置超限
        /// </summary>
        PositionOutOfRange,

        /// <summary>
        /// 其他错误
        /// </summary>
        Other
    }

    /// <summary>
    /// 视觉检测结果事件参数
    /// </summary>
    public class VisionResultReadyEventArgs : EventArgs
    {
        /// <summary>
        /// 视觉检测结果
        /// </summary>
        public VisionResult Result { get; set; }

        /// <summary>
        /// 产品ID（如果有）
        /// </summary>
        public string ProductId { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="result">视觉检测结果</param>
        /// <param name="productId">产品ID</param>
        public VisionResultReadyEventArgs(VisionResult result, string productId = "")
        {
            Result = result;
            ProductId = productId ?? string.Empty;
            EventTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 视觉系统错误事件参数
    /// </summary>
    public class VisionErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 错误类型
        /// </summary>
        public VisionErrorType ErrorType { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 产品ID（如果有）
        /// </summary>
        public string ProductId { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="errorType">错误类型</param>
        /// <param name="errorMessage">错误信息</param>
        /// <param name="productId">产品ID</param>
        public VisionErrorEventArgs(VisionErrorType errorType, string errorMessage, string productId = "")
        {
            ErrorType = errorType;
            ErrorMessage = errorMessage ?? string.Empty;
            ProductId = productId ?? string.Empty;
            EventTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 视觉错误类型枚举
    /// </summary>
    public enum VisionErrorType
    {
        /// <summary>
        /// 相机连接失败
        /// </summary>
        CameraConnectionFailed,

        /// <summary>
        /// 图像采集失败
        /// </summary>
        ImageCaptureFailed,

        /// <summary>
        /// 图像处理失败
        /// </summary>
        ImageProcessingFailed,

        /// <summary>
        /// 未找到目标
        /// </summary>
        TargetNotFound,

        /// <summary>
        /// 置信度过低
        /// </summary>
        LowConfidence,

        /// <summary>
        /// 其他错误
        /// </summary>
        Other
    }

    /// <summary>
    /// 工作流状态变化事件参数
    /// </summary>
    public class WorkflowStateChangedEventArgs : EventArgs
    {
        public WorkflowState OldState { get; }
        public WorkflowState NewState { get; }
        public string ProductId { get; }

        public WorkflowStateChangedEventArgs(WorkflowState oldState, WorkflowState newState, string productId)
        {
            OldState = oldState;
            NewState = newState;
            ProductId = productId;
        }
    }

    /// <summary>
    /// 工作流完成事件参数
    /// </summary>
    public class WorkflowCompletedEventArgs : EventArgs
    {
        public string ProductId { get; }
        public bool IsSuccess { get; }
        public TimeSpan TotalTime { get; }
        public string Message { get; }

        public WorkflowCompletedEventArgs(string productId, bool isSuccess, TimeSpan totalTime, string message)
        {
            ProductId = productId;
            IsSuccess = isSuccess;
            TotalTime = totalTime;
            Message = message;
        }
    }

    /// <summary>
    /// 工作流错误事件参数
    /// </summary>
    public class WorkflowErrorEventArgs : EventArgs
    {
        public string ProductId { get; }
        public string ErrorType { get; }
        public string ErrorMessage { get; }

        public WorkflowErrorEventArgs(string productId, string errorType, string errorMessage)
        {
            ProductId = productId;
            ErrorType = errorType;
            ErrorMessage = errorMessage;
        }
    }

    /// <summary>
    /// 通信字段变化事件参数
    /// </summary>
    public class CommunicationEventArgs : EventArgs
    {
        /// <summary>
        /// 字段名称
        /// </summary>
        public string FieldName { get; set; }

        /// <summary>
        /// 旧值
        /// </summary>
        public bool OldValue { get; set; }

        /// <summary>
        /// 新值
        /// </summary>
        public bool NewValue { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// SCARA字段变化事件参数
    /// </summary>
    public class ScaraFieldChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 字段名称
        /// </summary>
        public string FieldName { get; set; }

        /// <summary>
        /// 旧值
        /// </summary>
        public bool OldValue { get; set; }

        /// <summary>
        /// 新值
        /// </summary>
        public bool NewValue { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 扫码器完成事件参数
    /// </summary>
    public class ScannerCompletedEventArgs : EventArgs
    {
        /// <summary>
        /// 扫码器ID
        /// </summary>
        public int ScannerId { get; set; }

        /// <summary>
        /// 扫描到的条码
        /// </summary>
        public string Barcode { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 所有扫码器完成事件参数
    /// </summary>
    public class AllScannersCompletedEventArgs : EventArgs
    {
        /// <summary>
        /// 所有扫码器数据
        /// </summary>
        public Dictionary<int, string> ScannerData { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 自动模式状态变化事件参数
    /// </summary>
    public class AutoModeStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 状态消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 变化时间
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
}
