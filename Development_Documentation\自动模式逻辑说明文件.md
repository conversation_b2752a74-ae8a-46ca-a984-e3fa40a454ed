# 自动模式逻辑说明文件

## 🔧 硬件IO逻辑特性说明

**⚠️ 重要：本系统采用低电平有效的硬件接线方式**

### 输出IO控制逻辑
- **系统处理方式**：业务代码使用逻辑状态（逻辑ON/OFF），系统自动通过`InvertAllOutputs=true`配置处理物理状态转换
- **逻辑ON** → 物理低电平 → 硬件设备激活
- **逻辑OFF** → 物理高电平 → 硬件设备关闭

### 输入IO读取逻辑（传感器状态）
- **物理低电平（读取值=0）** → 传感器感应到产品/信号有效
- **物理高电平（读取值=1）** → 传感器未感应到产品/信号无效

### 业务代码编写规范
- **输出IO**：统一使用逻辑状态（true/false），由系统自动处理物理转换
- **输入IO**：直接使用物理状态，按照低电平有效逻辑进行业务判断

---

## 开机自检模式
程序打开后自动切换到上一次退出程序时的模式（调试模式或自动模式）
进入自动化工作流时，自动开启以下功能：
1. 运动控制卡（dmc1000b）开启初始化，程序运行时，控制卡初始化应该一直保持，程序退出时才关闭初始化
2. 左/右翻转电机自动回零，并移动到位置1
3. 自动对2台6轴机器人连接主端口，启动机器人，激活数据端口（数据传输端口）

## 皮带电机（需要独立线程）
1. 输入/输出皮带电机控制参数由皮带电机面板的ui界面设置，**注意**：需要排除硬编码和参数映射逻辑以及参数永久保存逻辑，确保参数正确传导

2. **输入皮带电机控制逻辑**：
   * **传感器**：I0004（输入皮带传感器）
   * **控制逻辑**：
     - 持续扫描输入传感器状态
     - **传感器=0（低电平）**：检测到产品 → 停止皮带电机
     - **传感器=1（高电平）**：无产品 → 启动皮带电机
   * **业务含义**：有产品时皮带停止，无产品时皮带运行

3. **输出皮带电机控制逻辑**：
   * **传感器**：I0106（输出皮带传感器）
   * **控制逻辑**：
     - 持续扫描输入传感器状态
     - **传感器=0（低电平）**：检测到产品 → 启动皮带电机
     - **传感器=1（高电平）**：无产品 → 停止皮带电机
   * **业务含义**：有产品时皮带运行，无产品时皮带停止

## 左/右翻转电机（scara通信部分）自动模式（独立线程）
**说明**：scara动作由第三方开发，利用通信字段与本系统配合自动模式运行，所以开始开发前，先定义一个全局静态类，用于存储各模块的通信字段

### 通信字段定义：
* 左电机是否准备完毕: `bool L_moto_ready` ; 右电机是否准备完毕: `bool R_moto_ready`
* 左位置是否放置到位: `bool L_position_Arrived` ; 右位置是否放置到位: `bool R_position_Arrived`
* 左夹爪是否夹紧：`bool L_gripper_ok` ; 右夹爪是否夹紧: `bool R_gripper_ok`
* 左角度矫正是否完成：`bool L_Angle_ok` ; 右角度矫正是否完成: `bool R_Angle_ok`
* 左边机器人是否到达安全距离：`bool L_safe_ok` ; 右边机器人是否到达安全距离：`bool R_safe_ok`
* 扫描器数据获取状态：
  - 1号扫描器：`bool L_dataget_ok`
  - 2号扫描器：`bool R_dataget_ok`
  - 3号扫描器：`bool ML_dataget_ok` 和 `bool MR_dataget_ok`
* 翻转电机工作完成状态：`bool L_moto_finish`；`bool R_moto_finish`
* 系统运行状态：`bool all_save`（停止时置0，启动和运行时置1）

### 运行逻辑：
1. **初始化阶段**：
   - 左/右翻转电机回原点，然后到达1号位置（开启自动模式后自动执行，已完成代码开发）
   - 完成后：`L_moto_ready = true` 和 `R_moto_ready = true`

2. **夹爪控制阶段**：
   - **左翻转电机**：等待 `L_position_Arrived = true` → O0001（左夹爪气缸）输出**逻辑ON** → 等待100ms → 检查I0005状态
     - **I0005 = 0（低电平）**：夹爪到位 → `L_gripper_ok = true`
     - **I0005 = 1（高电平）**：夹爪未到位 → 输出"左夹爪错误"
   - **右翻转电机**：等待 `R_position_Arrived = true` → O0003（右夹爪气缸）输出**逻辑ON** → 等待100ms → 检查I0009状态
     - **I0009 = 0（低电平）**：夹爪到位 → `R_gripper_ok = true`
     - **I0009 = 1（高电平）**：夹爪未到位 → 输出"右夹爪错误"

3. **安全确认阶段**：
   - **左翻转电机**：等待 `L_Angle_ok = true` → 等待 `L_safe_ok = true`
   - **右翻转电机**：等待 `R_Angle_ok = true` → 等待 `R_safe_ok = true`

4. **顶料控制阶段**：
   - **左翻转电机**：`L_safe_ok = true` → 移动到位置2 → O0002（左顶料气缸）输出**逻辑ON** → 等待100ms → 检查I0007状态
     - **I0007 = 0（低电平）**：顶料到位 → 移动到位置3
     - **I0007 = 1（高电平）**：顶料未到位 → 输出"左顶料错误"
   - **右翻转电机**：`R_safe_ok = true` → 移动到位置2 → O0004（右顶料气缸）输出**逻辑ON** → 等待100ms → 检查I0011状态
     - **I0011 = 0（低电平）**：顶料到位 → 移动到位置3
     - **I0011 = 1（高电平）**：顶料未到位 → 输出"右顶料错误"

5. **退料控制阶段**：
   - **左翻转电机**：到达位置3 → O0002（左顶料气缸）输出**逻辑OFF** → 等待100ms → 检查I0008状态
     - **I0008 = 0（低电平）**：退料到位 → 移动到位置4
     - **I0008 = 1（高电平）**：退料未到位 → 输出"左退料错误"
   - **右翻转电机**：到达位置3 → O0004（右顶料气缸）输出**逻辑OFF** → 等待100ms → 检查I0012状态
     - **I0012 = 0（低电平）**：退料到位 → 移动到位置4
     - **I0012 = 1（高电平）**：退料未到位 → 输出"右退料错误"

6. **完成阶段**：
   - 左翻转电机到达位置4 → `L_dataget_ok = true`
   - 右翻转电机到达位置4 → `R_dataget_ok = true`

**⚠️ 重要说明**：
- 所有输出IO使用**逻辑状态**（系统自动处理物理状态转换）
- 所有输入IO检查使用**物理状态**（低电平有效逻辑）
- 所有扫描到的静态字段为true时，执行完该条件动作后，将该静态字段复位为false

## 扫码器自动模式功能
**说明**：本项目有3个扫码器，1号对应左扫码器，2号对应右扫码器，3号对应中间扫码器。
**使用方法**：3个扫码器均使用串口自定义协议通讯，已设定使用串口发送"start"触发扫码，在UI界面中可以分别选择对应的端口，设置相关的参数

### 扫码器自动模式开发任务
- 目前扫码器的逻辑存在问题，需要检测其UI控件是否存在硬编码，控件输入参数与业务参数引用没有问题，业务代码是否有问题，然后修正，确保正常能正常使用
- 需要确保程序启动时，需要自动选择上一次选择的端口号，永久保存上一次程序运行时输入的参数

### 自动模式逻辑
1. **初始化通信**：
   - 程序启动时，自动连接3个串口端口
   - 分别发送"hello"给3个串口，若串口回复"world"则表示通讯正常

2. **3号扫码器触发逻辑**：
   - 等待 `ML_dataget_ok = true` → 发送触发3号扫码器扫码信号 → 将扫码结果保存在中间向左扫码器字符串中
   - 等待 `MR_dataget_ok = true` → 发送触发3号扫码器扫码信号 → 将扫码结果保存在中间向右扫码器字符串中

3. **1号扫码器触发逻辑**：
   - 等待 `L_dataget_ok = true` → 发送触发1号扫码器扫码信号 → 将扫码结果保存在左扫码器字符串中

4. **2号扫码器触发逻辑**：
   - 等待 `R_dataget_ok = true` → 发送触发2号扫码器扫码信号 → 将扫码结果保存在右扫码器字符串中

## 左/右翻转电机（六轴机器人通信部分）自动模式（独立线程）
**说明**：这部分通信直接用于本程序的架构进行，旨在通过TCP协议，与2个6轴机器人（Epson机器人）进行通信，控制机器人执行步骤。

### 运行逻辑
**说明**：两个机器人端为服务器端，本项目程序为客户端，程序启动时，必须分别连接这两个主端口（6轴机器人指令收发的端口），然后自动登录并发送启动指令，最后连接数据端口（6轴机器人数据收发的端口）

1. **数据端口监听**：
   - 持续扫描机器人1和机器人2的数据端口

2. **取料权限请求处理**：
   - **机器人1**：收到"GETPICK" → 判断1号扫码器是否完成扫码
     - 完成：发送"ALLOWPICK"
     - 未完成：发送"DENYPICK"
   - **机器人2**：收到"GETPICK" → 判断2号扫码器是否完成扫码
     - 完成：发送"ALLOWPICK"
     - 未完成：发送"DENYPICK"

3. **取料确认等待**：
   - 发送"ALLOWPICK"后，等待机器人发送"INPICK"字符串数据

4. **🚨 取料执行和数据传输（关键IO逻辑）**：
   - **机器人1**：收到"INPICK" → O0001输出**逻辑OFF** → 等待100ms → 检查I0006状态
     - **⚠️ 修正逻辑**：**I0006 = 0（低电平）**：取料完成 → 发送"中间向左扫码器字符串,左扫码器字符串"
     - **I0006 = 1（高电平）**：取料未完成 → 不发送数据
   - **机器人2**：收到"INPICK" → O0003输出**逻辑OFF** → 等待100ms → 检查I0010状态
     - **⚠️ 修正逻辑**：**I0010 = 0（低电平）**：取料完成 → 发送"中间向右扫码器字符串,右扫码器字符串"
     - **I0010 = 1（高电平）**：取料未完成 → 不发送数据

5. **复位操作**：
   - 发送完数据后，等待1s → 自动复位到位置1 → `L_moto_ready = true` / `R_moto_ready = true`

6. **🚨 NG品放置请求处理（关键IO逻辑修正）**：
   - **机器人1**：收到"GETNGPUT" → 检查I0104状态
     - **⚠️ 修正逻辑**：**I0104 = 0（低电平）**：可以放置 → 发送"ALLOWNGPUT"
     - **I0104 = 1（高电平）**：不可放置 → 发送"DENYNGPUT"
   - **机器人2**：收到"GETNGPUT" → 检查I0105状态
     - **⚠️ 修正逻辑**：**I0105 = 0（低电平）**：可以放置 → 发送"ALLOWNGPUT"
     - **I0105 = 1（高电平）**：不可放置 → 发送"DENYNGPUT"

7. **NG品放置完成处理**：
   - **机器人1**：收到"NGPUTFULL" → 监测I0104状态变化（1→0→1）→ 发送"RESETNGPUT"
   - **机器人2**：收到"NGPUTFULL" → 监测I0105状态变化（1→0→1）→ 发送"RESETNGPUT"

8. **🚨 OK品放置请求处理（关键IO逻辑修正）**：
   - **机器人1/2**：收到"GETOKPUT" → 检查I0106状态
     - **⚠️ 修正逻辑**：**I0106 = 0（低电平）**：可以放置 → 发送"ALLOWOKPUT"
     - **I0106 = 1（高电平）**：不可放置 → 发送"DENYOKPUT"

**⚠️ 重要说明**：
- 本项目已经开发完成的2台六轴机器人（Epson机器人）通信，但没有自动模式收发信息，请查看已开发的功能，创建新的自动模式控制器类来管理六轴机器人通信自动化模式运行
- 两台机器人的主端口负责连接，登录，启动，停止等指令（这部分已开发），以上字符串数据收发功能，均由他们的数据端口收发
- **所有IO逻辑已按照低电平有效特性进行修正**

## 安全管理（独立线程）

### 紧急停止条件
1. **🚨 安全门和光幕检测（关键IO逻辑修正）**：
   - **I0101（安全门）= 0（低电平）**：安全门打开 → **立即紧急停止**
   - **I0102（光幕1）= 1（高电平）**：光幕被遮挡 → **立即紧急停止**
   - **I0103（光幕2）= 1（高电平）**：光幕被遮挡 → **立即紧急停止**
   - **业务逻辑**：`if (!I0101 || I0102 || I0103) → 紧急停止`

2. **启动按钮控制**：
   - **I0001状态变化**：检测电平变化（0→1 或 1→0）→ 启动机器人
   - **边沿触发**：任何电平变化都视为启动信号

3. **停止按钮控制**：
   - **I0002状态变化**：检测电平变化（0→1 或 1→0）→ 停止程序和机器人所有动作
   - **恢复机制**：再次点击启动后，程序复位并继续执行

4. **安全锁控制**：
   - **I0003 = 1（高电平）**：安全锁激活 → **紧急停止程序所有操作**
   - **I0003 = 0（低电平）**：安全锁解除 → 允许重新启动程序

### 指示灯控制
**说明**：所有指示灯使用逻辑状态控制，系统自动处理物理状态转换

1. **黄灯（调试/停止状态）**：
   - **条件**：程序在调试模式或停止状态
   - **控制**：O0010输出**逻辑ON**
   - **物理效果**：黄灯亮起

2. **绿灯（正常运行状态）**：
   - **条件**：程序在自动模式正常运行
   - **控制**：O0009输出**逻辑ON**
   - **物理效果**：绿灯亮起

3. **红灯（报错/紧急停止状态）**：
   - **条件**：程序出现报错或紧急停止
   - **控制**：O0008输出**逻辑ON**
   - **物理效果**：红灯亮起

4. **状态切换逻辑**：
   - **切换原则**：先将原状态置**逻辑OFF**，再将新状态置**逻辑ON**
   - **互斥控制**：确保同时只有一个指示灯亮起
   - **示例**：调试→运行：O0010=OFF → O0009=ON

**⚠️ 安全管理重要说明**：
- 所有安全相关的IO检测都已按照低电平有效特性进行修正
- 紧急停止条件具有最高优先级，任何时候都不能被忽略
- 指示灯控制使用逻辑状态，确保与硬件接线逻辑一致

---

## 📋 IO逻辑修正总结

### 已修正的关键IO逻辑问题：
1. **I0006/I0010（取料完成检测）**：修正为低电平=取料完成
2. **I0104/I0105（NG品放置检测）**：修正为低电平=可以放置
3. **I0106（OK品放置检测）**：修正为低电平=可以放置
4. **I0101（安全门）**：修正为低电平=门打开（紧急停止）
5. **I0102/I0103（光幕）**：修正为高电平=被遮挡（紧急停止）

### 输出IO统一规范：
- 所有输出IO（O0001-O0010）使用**逻辑状态**控制
- 系统自动通过`InvertAllOutputs=true`处理物理状态转换
- 业务代码无需关心物理电平，只需关心逻辑功能

### 输入IO统一规范：
- 所有输入IO（I0001-I0106）直接使用**物理状态**
- 按照低电平有效逻辑进行业务判断
- 文档中明确标注每个IO的物理状态对应的业务含义
