# WorkflowManager架构设计文档

## 文档版本
- 版本：2.0
- 更新时间：2025-09-27
- 更新内容：WorkflowManager重构后的新架构

## 架构概述

### 设计目标
WorkflowManager作为工作流协调器，负责管理和协调各种AutoMode控制器的工作流程，实现真正的工作流调度功能。

### 核心原则
1. **单一职责原则**：WorkflowManager专注于流程协调，不直接控制硬件
2. **开放封闭原则**：易于扩展新的AutoMode控制器
3. **依赖倒置原则**：依赖抽象接口，不依赖具体实现
4. **事件驱动架构**：通过事件实现松耦合的组件通信

## 架构组件

### 1. WorkflowManager（工作流协调器）

#### 1.1 核心职责
- 协调各AutoMode控制器的启动、停止、重置
- 管理工作流状态转换
- 处理工作流级别的事件
- 提供统一的工作流控制接口

#### 1.2 主要接口
```csharp
public class WorkflowManager
{
    // 工作流控制
    public async Task<bool> StartWorkflowAsync(string productId = "")
    public async Task<bool> StopWorkflowAsync()
    public async Task<bool> ResetWorkflowAsync()
    
    // 状态属性
    public bool IsInitialized { get; }
    public WorkflowState CurrentState { get; }
    public bool IsWorkflowEnabled { get; }
    
    // 事件
    public event EventHandler<WorkflowStateChangedEventArgs> WorkflowStateChanged;
    public event EventHandler<WorkflowCompletedEventArgs> WorkflowCompleted;
    public event EventHandler<WorkflowErrorEventArgs> WorkflowError;
}
```

#### 1.3 状态管理
```csharp
public enum WorkflowState
{
    Idle,           // 空闲状态
    WaitingForScan, // 等待扫码
    MotorMoving,    // 电机运行中
    VisionDetecting,// 视觉检测中
    RobotOperating, // 机器人操作中
    Error           // 错误状态
}
```

### 2. AutoMode控制器架构

#### 2.1 统一接口设计
所有AutoMode控制器实现统一的接口：
```csharp
public interface IAutoModeController
{
    Task<bool> InitializeAsync();
    Task<bool> StartAsync();
    Task<bool> StopAsync();
    Task<bool> ResetAsync();
    
    bool IsInitialized { get; }
    bool IsRunning { get; }
}
```

#### 2.2 具体控制器

##### BeltMotorAutoModeController（皮带电机自动模式控制器）
- **职责**：管理皮带电机的自动控制逻辑
- **功能**：输入皮带控制、输出皮带控制、传感器监控
- **状态**：BeltMotorState（Idle, Running, Error）

##### ScannerAutoModeManager（扫码器自动模式管理器）
- **职责**：管理扫码器的自动模式
- **功能**：扫码器启动、停止、状态监控
- **事件**：AllScannersCompleted、AutoModeStatusChanged

##### EpsonRobotAutoModeController（机器人自动模式控制器）
- **职责**：管理Epson机器人的自动模式
- **功能**：机器人通信、工作流执行、状态管理
- **状态**：待集成（partial class编译问题）

## 工作流执行序列

### 1. 工作流启动序列
```
StartWorkflowAsync(productId)
    ↓
ExecuteWorkflowStartSequenceAsync(productId)
    ↓
1. 初始化所有AutoMode控制器
    ↓
2. 启动扫码器自动模式
    ↓
3. 启动皮带电机控制器
    ↓
4. 启动机器人控制器（待实现）
    ↓
5. 等待系统稳定
    ↓
工作流启动完成
```

### 2. 工作流完成序列
```
扫码器完成事件触发
    ↓
OnAllScannersCompleted()
    ↓
ExecuteWorkflowCompletionSequenceAsync(productId)
    ↓
1. 检查所有控制器状态
    ↓
2. 触发工作流完成事件
    ↓
3. 重置工作流状态为Idle
    ↓
工作流完成
```

### 3. 状态转换流程
```
Idle → WaitingForScan → MotorMoving → RobotOperating → VisionDetecting → Idle
                                ↓
                              Error（任何阶段都可能转入）
```

## 事件驱动架构

### 1. 事件流向
```
AutoMode控制器事件 → WorkflowManager → UI界面/其他组件
```

### 2. 主要事件类型

#### WorkflowManager事件
- **WorkflowStateChanged**：工作流状态变化
- **WorkflowCompleted**：工作流完成
- **WorkflowError**：工作流错误

#### AutoMode控制器事件
- **BeltMotorStateChanged**：皮带电机状态变化
- **BeltMotorErrorOccurred**：皮带电机错误
- **AllScannersCompleted**：所有扫码器完成
- **AutoModeStatusChanged**：自动模式状态变化

### 3. 事件订阅机制
```csharp
private async Task SubscribeToManagerEventsAsync()
{
    // 订阅皮带电机控制器事件
    if (_beltMotorController != null)
    {
        _beltMotorController.StateChanged += OnBeltMotorStateChanged;
        _beltMotorController.ErrorOccurred += OnBeltMotorErrorOccurred;
    }
    
    // 订阅扫码器自动模式管理器事件
    if (_scannerAutoModeManager != null)
    {
        _scannerAutoModeManager.AllScannersCompleted += OnAllScannersCompleted;
        _scannerAutoModeManager.AutoModeStatusChanged += OnScannerAutoModeStatusChanged;
    }
}
```

## 调用关系图

### 1. 系统启动调用链
```
MainForm.StartBtn_Click
    ↓
SystemModeManager.StartAutomationAsync
    ↓
SystemModeManager.StartAutomationInternalAsync
    ↓
StartupSelfCheckManager.ExecuteStartupSelfCheckAsync
    ↓
StartupSelfCheckManager.StartBeltMotorAutoControlAsync
    ↓
BeltMotorAutoModeController.StartAsync
```

### 2. 工作流控制调用链
```
WorkflowManager.StartWorkflowAsync
    ↓
WorkflowManager.ExecuteWorkflowStartSequenceAsync
    ↓
WorkflowManager.InitializeAllAutoModeControllersAsync
    ↓
各AutoMode控制器.InitializeAsync/StartAsync
```

## 扩展性设计

### 1. 添加新的AutoMode控制器
1. 实现统一的IAutoModeController接口
2. 在WorkflowManager中添加控制器引用
3. 在初始化方法中添加控制器实例获取
4. 在事件订阅方法中添加事件订阅
5. 在启动序列中添加控制器启动逻辑

### 2. 扩展工作流状态
1. 在WorkflowState枚举中添加新状态
2. 在状态转换逻辑中添加新的转换规则
3. 在事件处理中添加新状态的处理逻辑

## 错误处理机制

### 1. 异常处理层次
```
WorkflowManager（工作流级别）
    ↓
AutoMode控制器（控制器级别）
    ↓
硬件管理器（硬件级别）
```

### 2. 错误恢复策略
- **自动重试**：临时性错误自动重试
- **状态重置**：严重错误时重置到安全状态
- **事件通知**：错误信息通过事件传播
- **日志记录**：完整的错误日志记录

## 性能考虑

### 1. 异步操作
- 所有IO操作使用异步方法
- 避免阻塞UI线程
- 合理使用CancellationToken

### 2. 资源管理
- 及时释放不需要的资源
- 使用using语句管理IDisposable对象
- 避免内存泄漏

### 3. 线程安全
- 使用SemaphoreSlim进行异步同步
- 状态变更使用适当的锁机制
- 避免死锁和竞态条件

## 测试策略

### 1. 单元测试
- 每个控制器的独立功能测试
- 工作流状态转换测试
- 异常处理测试

### 2. 集成测试
- 控制器间协调测试
- 完整工作流执行测试
- 事件传播测试

### 3. 性能测试
- 工作流执行时间测试
- 内存使用测试
- 并发处理测试

## 部署和维护

### 1. 配置管理
- 工作流参数可配置
- 控制器启用/禁用配置
- 超时和重试参数配置

### 2. 监控和诊断
- 工作流执行状态监控
- 性能指标收集
- 错误统计和分析

### 3. 版本兼容性
- 向后兼容性保证
- 平滑升级策略
- 配置迁移支持

## 总结

新的WorkflowManager架构实现了：
- **清晰的职责分离**：工作流协调与硬件控制分离
- **统一的接口设计**：所有AutoMode控制器使用统一接口
- **完整的事件驱动**：松耦合的组件通信
- **良好的扩展性**：易于添加新的控制器和功能
- **健壮的错误处理**：多层次的错误处理和恢复机制

这个架构为HR2项目的后续开发和维护提供了坚实的基础。
