{"Version": 1, "WorkspaceRootPath": "E:\\projects\\C#_projects\\HR2\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{12345678-1234-5678-9ABC-123456789ABC}|MyHMI.csproj|e:\\projects\\c#_projects\\hr2\\managers\\beltmotorautomodecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{12345678-1234-5678-9ABC-123456789ABC}|MyHMI.csproj|solutionrelative:managers\\beltmotorautomodecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{12345678-1234-5678-9ABC-123456789ABC}|MyHMI.csproj|E:\\projects\\C#_projects\\HR2\\ui\\mainform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{12345678-1234-5678-9ABC-123456789ABC}|MyHMI.csproj|solutionrelative:ui\\mainform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{12345678-1234-5678-9ABC-123456789ABC}|MyHMI.csproj|E:\\projects\\C#_projects\\HR2\\ui\\controls\\iocontrolpanel.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{12345678-1234-5678-9ABC-123456789ABC}|MyHMI.csproj|solutionrelative:ui\\controls\\iocontrolpanel.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{12345678-1234-5678-9ABC-123456789ABC}|MyHMI.csproj|E:\\projects\\C#_projects\\HR2\\managers\\workflowmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{12345678-1234-5678-9ABC-123456789ABC}|MyHMI.csproj|solutionrelative:managers\\workflowmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{12345678-1234-5678-9ABC-123456789ABC}|MyHMI.csproj|E:\\projects\\C#_projects\\HR2\\ui\\controls\\ioreadpanel.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{12345678-1234-5678-9ABC-123456789ABC}|MyHMI.csproj|solutionrelative:ui\\controls\\ioreadpanel.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{12345678-1234-5678-9ABC-123456789ABC}|MyHMI.csproj|E:\\projects\\C#_projects\\HR2\\ui\\controls\\iocontrolpanel.resx||{81828910-B8B3-4D2B-99A3-067027C180C1}", "RelativeMoniker": "D:0:0:{12345678-1234-5678-9ABC-123456789ABC}|MyHMI.csproj|solutionrelative:ui\\controls\\iocontrolpanel.resx||{81828910-B8B3-4D2B-99A3-067027C180C1}"}, {"AbsoluteMoniker": "D:0:0:{12345678-1234-5678-9ABC-123456789ABC}|MyHMI.csproj|E:\\projects\\C#_projects\\HR2\\ui\\controls\\communicationpanel.resx||{81828910-B8B3-4D2B-99A3-067027C180C1}", "RelativeMoniker": "D:0:0:{12345678-1234-5678-9ABC-123456789ABC}|MyHMI.csproj|solutionrelative:ui\\controls\\communicationpanel.resx||{81828910-B8B3-4D2B-99A3-067027C180C1}"}, {"AbsoluteMoniker": "D:0:0:{12345678-1234-5678-9ABC-123456789ABC}|MyHMI.csproj|E:\\projects\\C#_projects\\HR2\\ui\\controls\\iocontrolpanel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{12345678-1234-5678-9ABC-123456789ABC}|MyHMI.csproj|solutionrelative:ui\\controls\\iocontrolpanel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{12345678-1234-5678-9ABC-123456789ABC}|MyHMI.csproj|E:\\projects\\C#_projects\\HR2\\ui\\controls\\communicationpanel.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{12345678-1234-5678-9ABC-123456789ABC}|MyHMI.csproj|solutionrelative:ui\\controls\\communicationpanel.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{12345678-1234-5678-9ABC-123456789ABC}|MyHMI.csproj|E:\\projects\\C#_projects\\HR2\\ui\\controls\\communicationpanel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{12345678-1234-5678-9ABC-123456789ABC}|MyHMI.csproj|solutionrelative:ui\\controls\\communicationpanel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "BeltMotorAutoModeController.cs", "DocumentMoniker": "E:\\projects\\C#_projects\\HR2\\Managers\\BeltMotorAutoModeController.cs", "RelativeDocumentMoniker": "Managers\\BeltMotorAutoModeController.cs", "ToolTip": "E:\\projects\\C#_projects\\HR2\\Managers\\BeltMotorAutoModeController.cs", "RelativeToolTip": "Managers\\BeltMotorAutoModeController.cs", "ViewState": "AgIAAOICAAAAAAAAAAAAwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-28T23:22:45.557Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "WorkflowManager.cs", "DocumentMoniker": "E:\\projects\\C#_projects\\HR2\\Managers\\WorkflowManager.cs", "RelativeDocumentMoniker": "Managers\\WorkflowManager.cs", "ToolTip": "E:\\projects\\C#_projects\\HR2\\Managers\\WorkflowManager.cs", "RelativeToolTip": "Managers\\WorkflowManager.cs", "ViewState": "AgIAABsAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-24T09:01:02.999Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "MainForm.cs [设计]", "DocumentMoniker": "E:\\projects\\C#_projects\\HR2\\UI\\MainForm.cs", "RelativeDocumentMoniker": "UI\\MainForm.cs", "ToolTip": "E:\\projects\\C#_projects\\HR2\\UI\\MainForm.cs [设计]", "RelativeToolTip": "UI\\MainForm.cs [设计]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-20T06:36:42.534Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "IOReadPanel.Designer.cs", "DocumentMoniker": "E:\\projects\\C#_projects\\HR2\\UI\\Controls\\IOReadPanel.Designer.cs", "RelativeDocumentMoniker": "UI\\Controls\\IOReadPanel.Designer.cs", "ToolTip": "E:\\projects\\C#_projects\\HR2\\UI\\Controls\\IOReadPanel.Designer.cs", "RelativeToolTip": "UI\\Controls\\IOReadPanel.Designer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-20T06:38:09.715Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "IOControlPanel.Designer.cs", "DocumentMoniker": "E:\\projects\\C#_projects\\HR2\\UI\\Controls\\IOControlPanel.Designer.cs", "RelativeDocumentMoniker": "UI\\Controls\\IOControlPanel.Designer.cs", "ToolTip": "E:\\projects\\C#_projects\\HR2\\UI\\Controls\\IOControlPanel.Designer.cs", "RelativeToolTip": "UI\\Controls\\IOControlPanel.Designer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-20T06:38:04.917Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "CommunicationPanel.resx", "DocumentMoniker": "E:\\projects\\C#_projects\\HR2\\UI\\Controls\\CommunicationPanel.resx", "RelativeDocumentMoniker": "UI\\Controls\\CommunicationPanel.resx", "ToolTip": "E:\\projects\\C#_projects\\HR2\\UI\\Controls\\CommunicationPanel.resx", "RelativeToolTip": "UI\\Controls\\CommunicationPanel.resx", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001005|", "WhenOpened": "2025-09-20T06:37:56.457Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "IOControlPanel.cs [设计]", "DocumentMoniker": "E:\\projects\\C#_projects\\HR2\\UI\\Controls\\IOControlPanel.cs", "RelativeDocumentMoniker": "UI\\Controls\\IOControlPanel.cs", "ToolTip": "E:\\projects\\C#_projects\\HR2\\UI\\Controls\\IOControlPanel.cs [设计]", "RelativeToolTip": "UI\\Controls\\IOControlPanel.cs [设计]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-20T06:37:55.577Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "IOControlPanel.resx", "DocumentMoniker": "E:\\projects\\C#_projects\\HR2\\UI\\Controls\\IOControlPanel.resx", "RelativeDocumentMoniker": "UI\\Controls\\IOControlPanel.resx", "ToolTip": "E:\\projects\\C#_projects\\HR2\\UI\\Controls\\IOControlPanel.resx", "RelativeToolTip": "UI\\Controls\\IOControlPanel.resx", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001005|", "WhenOpened": "2025-09-20T06:38:06.513Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "CommunicationPanel.Designer.cs", "DocumentMoniker": "E:\\projects\\C#_projects\\HR2\\UI\\Controls\\CommunicationPanel.Designer.cs", "RelativeDocumentMoniker": "UI\\Controls\\CommunicationPanel.Designer.cs", "ToolTip": "E:\\projects\\C#_projects\\HR2\\UI\\Controls\\CommunicationPanel.Designer.cs", "RelativeToolTip": "UI\\Controls\\CommunicationPanel.Designer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-20T06:37:54.053Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "CommunicationPanel.cs [设计]", "DocumentMoniker": "E:\\projects\\C#_projects\\HR2\\UI\\Controls\\CommunicationPanel.cs", "RelativeDocumentMoniker": "UI\\Controls\\CommunicationPanel.cs", "ToolTip": "E:\\projects\\C#_projects\\HR2\\UI\\Controls\\CommunicationPanel.cs [设计]", "RelativeToolTip": "UI\\Controls\\CommunicationPanel.cs [设计]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-20T06:37:50.381Z"}]}]}]}