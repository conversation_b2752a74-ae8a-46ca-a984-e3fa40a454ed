# MyHMI 上位机控制系统 - 配置文件说明

## 配置文件概述

MyHMI 系统采用多层配置架构，支持 App.config 和 JSON 配置文件，提供灵活的系统配置管理。

## 配置文件结构

```
Config/
├── SystemConfig.json              # 主配置文件（JSON格式）
└── SystemConfiguration.cs         # 配置管理类

根目录/
└── App.config                     # 应用程序配置文件
```

## App.config 配置文件

### 文件位置
```
MyHMI/App.config
```

### 配置内容
```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
    </startup>
    
    <!-- 应用程序设置 -->
    <appSettings>
        <add key="SystemName" value="上位机控制系统" />
        <add key="Version" value="1.0.0" />
        <add key="LogLevel" value="Info" />
        <add key="ConfigFilePath" value=".\Config\SystemConfig.json" />
    </appSettings>
    
    <!-- 连接字符串 -->
    <connectionStrings>
        <add name="DefaultConnection" 
             connectionString="Data Source=.\Data\MyHMI.db;Version=3;" 
             providerName="System.Data.SQLite" />
    </connectionStrings>
    
    <!-- NLog 配置 -->
    <configSections>
        <section name="nlog" type="NLog.Config.ConfigSectionHandler, NLog" />
    </configSections>
    
    <nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
        <targets>
            <target xsi:type="File" name="fileTarget"
                    fileName=".\Logs\MyHMI-${shortdate}.log"
                    layout="${longdate} ${level:uppercase=true} ${logger} ${message} ${exception:format=tostring}" />
        </targets>
        <rules>
            <logger name="*" minlevel="Info" writeTo="fileTarget" />
        </rules>
    </nlog>
</configuration>
```

## SystemConfig.json 主配置文件

### 文件位置
```
MyHMI/Config/SystemConfig.json
```

### 完整配置结构
```json
{
  "System": {
    "Name": "上位机控制系统",
    "Version": "1.0.0",
    "Description": "基于WinForms的工业控制系统",
    "StartupMode": "Normal",
    "DebugMode": false,
    "Language": "zh-CN"
  },
  "IO": {
    "CardType": "DMC1000",
    "CardIndex": 0,
    "InputChannels": 16,
    "OutputChannels": 16,
    "ScanInterval": 100,
    "EnableMonitoring": true,
    "InputFilters": [
      {
        "Channel": 0,
        "Name": "启动按钮",
        "FilterTime": 50
      },
      {
        "Channel": 1,
        "Name": "停止按钮",
        "FilterTime": 50
      }
    ]
  },
  "Motor": {
    "AxisCount": 4,
    "DefaultSpeed": 1000,
    "DefaultAcceleration": 5000,
    "HomeSpeed": 500,
    "PositionTolerance": 0.01,
    "Axes": [
      {
        "AxisId": 0,
        "Name": "X轴",
        "MaxSpeed": 5000,
        "MaxAcceleration": 10000,
        "MaxPosition": 200.0,
        "MinPosition": -200.0,
        "HomeOffset": 0.0,
        "PulsePerMM": 1000
      },
      {
        "AxisId": 1,
        "Name": "Y轴",
        "MaxSpeed": 5000,
        "MaxAcceleration": 10000,
        "MaxPosition": 150.0,
        "MinPosition": -150.0,
        "HomeOffset": 0.0,
        "PulsePerMM": 1000
      }
    ]
  },
  "Vision": {
    "CameraId": 0,
    "ImageWidth": 1920,
    "ImageHeight": 1080,
    "ExposureTime": 10000,
    "Gain": 1.0,
    "TemplateMatchThreshold": 0.8,
    "MaxSearchTime": 5000,
    "SaveImages": true,
    "ImageSavePath": ".\\Data\\Images",
    "CalibrationFile": ".\\Config\\CameraCalibration.xml"
  },
  "Communication": {
    "ModbusTcp": {
      "IPAddress": "*************",
      "Port": 502,
      "SlaveId": 1,
      "ConnectTimeout": 5000,
      "ReadTimeout": 3000,
      "WriteTimeout": 3000,
      "RetryCount": 3,
      "RetryInterval": 1000
    },
    "Scanner": {
      "PortName": "COM3",
      "BaudRate": 9600,
      "DataBits": 8,
      "Parity": "None",
      "StopBits": "One",
      "ReadTimeout": 1000,
      "WriteTimeout": 1000,
      "TriggerMode": "Auto"
    },
    "RobotTcp": {
      "IPAddress": "*************",
      "Port": 8080,
      "ConnectTimeout": 5000,
      "HeartbeatInterval": 10000,
      "CommandTimeout": 30000,
      "MaxRetryCount": 3
    }
  },
  "Workflow": {
    "AutoStart": false,
    "StepTimeout": 30000,
    "ErrorRetryCount": 3,
    "ErrorRetryInterval": 2000,
    "EnableStatistics": true,
    "Steps": [
      {
        "StepId": 1,
        "Name": "扫描条码",
        "Timeout": 10000,
        "Required": true
      },
      {
        "StepId": 2,
        "Name": "视觉定位",
        "Timeout": 5000,
        "Required": true
      },
      {
        "StepId": 3,
        "Name": "电机运动",
        "Timeout": 15000,
        "Required": true
      },
      {
        "StepId": 4,
        "Name": "机器人操作",
        "Timeout": 20000,
        "Required": true
      }
    ]
  },
  "Statistics": {
    "EnableRecording": true,
    "DataRetentionDays": 30,
    "ExportPath": ".\\Data\\Export",
    "AutoExport": true,
    "ExportInterval": "Daily",
    "ExportFormat": "Excel"
  },
  "Logging": {
    "LogLevel": "Info",
    "LogPath": ".\\Logs",
    "MaxLogFiles": 10,
    "MaxLogFileSize": "10MB",
    "EnableConsoleLog": true,
    "EnableFileLog": true,
    "LogFormat": "${longdate} ${level:uppercase=true} ${logger} ${message} ${exception:format=tostring}"
  }
}
```

## 配置参数详细说明

### System 系统配置
| 参数 | 类型 | 说明 | 默认值 |
|------|------|------|--------|
| Name | string | 系统名称 | "上位机控制系统" |
| Version | string | 系统版本 | "1.0.0" |
| Description | string | 系统描述 | - |
| StartupMode | string | 启动模式 (Normal/Debug/Test) | "Normal" |
| DebugMode | bool | 调试模式 | false |
| Language | string | 界面语言 | "zh-CN" |

### IO 配置
| 参数 | 类型 | 说明 | 默认值 |
|------|------|------|--------|
| CardType | string | 控制卡类型 | "DMC1000" |
| CardIndex | int | 控制卡索引 | 0 |
| InputChannels | int | 输入通道数 | 16 |
| OutputChannels | int | 输出通道数 | 16 |
| ScanInterval | int | 扫描间隔(ms) | 100 |
| EnableMonitoring | bool | 启用监控 | true |

### Motor 电机配置
| 参数 | 类型 | 说明 | 默认值 |
|------|------|------|--------|
| AxisCount | int | 轴数量 | 4 |
| DefaultSpeed | double | 默认速度 | 1000 |
| DefaultAcceleration | double | 默认加速度 | 5000 |
| HomeSpeed | double | 回零速度 | 500 |
| PositionTolerance | double | 位置容差 | 0.01 |

### Vision 视觉配置
| 参数 | 类型 | 说明 | 默认值 |
|------|------|------|--------|
| CameraId | int | 相机ID | 0 |
| ImageWidth | int | 图像宽度 | 1920 |
| ImageHeight | int | 图像高度 | 1080 |
| ExposureTime | int | 曝光时间(μs) | 10000 |
| Gain | double | 增益 | 1.0 |
| TemplateMatchThreshold | double | 模板匹配阈值 | 0.8 |

### Communication 通信配置

#### ModbusTcp 配置
| 参数 | 类型 | 说明 | 默认值 |
|------|------|------|--------|
| IPAddress | string | IP地址 | "*************" |
| Port | int | 端口号 | 502 |
| SlaveId | int | 从站ID | 1 |
| ConnectTimeout | int | 连接超时(ms) | 5000 |
| ReadTimeout | int | 读取超时(ms) | 3000 |
| WriteTimeout | int | 写入超时(ms) | 3000 |

#### Scanner 扫描枪配置
| 参数 | 类型 | 说明 | 默认值 |
|------|------|------|--------|
| PortName | string | 串口名称 | "COM3" |
| BaudRate | int | 波特率 | 9600 |
| DataBits | int | 数据位 | 8 |
| Parity | string | 校验位 | "None" |
| StopBits | string | 停止位 | "One" |

## 配置文件管理

### 加载配置
```csharp
// 加载配置
var config = SystemConfiguration.Instance;
bool loaded = config.LoadConfiguration();

// 访问配置
string systemName = config.Config.System.Name;
int ioChannels = config.Config.IO.InputChannels;
```

### 保存配置
```csharp
// 修改配置
config.Config.System.Name = "新系统名称";

// 保存配置
bool saved = config.SaveConfiguration();
```

### 重新加载配置
```csharp
// 重新加载配置（热重载）
bool reloaded = config.ReloadConfiguration();
```

## 配置验证

### 必需参数检查
```csharp
public bool ValidateConfiguration()
{
    var config = SystemConfiguration.Instance.Config;
    
    // 检查必需参数
    if (string.IsNullOrEmpty(config.System.Name))
        return false;
        
    if (config.IO.InputChannels <= 0)
        return false;
        
    // 更多验证...
    return true;
}
```

### 参数范围检查
```csharp
// 电机参数验证
foreach (var axis in config.Motor.Axes)
{
    if (axis.MaxSpeed <= 0 || axis.MaxSpeed > 10000)
        throw new ArgumentOutOfRangeException("电机最大速度超出范围");
        
    if (axis.MaxPosition <= axis.MinPosition)
        throw new ArgumentException("电机位置范围设置错误");
}
```

## 配置文件备份和恢复

### 自动备份
```csharp
public void BackupConfiguration()
{
    string backupPath = $"./Config/Backup/SystemConfig_{DateTime.Now:yyyyMMdd_HHmmss}.json";
    File.Copy("./Config/SystemConfig.json", backupPath);
}
```

### 恢复配置
```csharp
public bool RestoreConfiguration(string backupFilePath)
{
    try
    {
        File.Copy(backupFilePath, "./Config/SystemConfig.json", true);
        return SystemConfiguration.Instance.ReloadConfiguration();
    }
    catch (Exception ex)
    {
        LogHelper.Error("配置恢复失败", ex);
        return false;
    }
}
```

## 环境相关配置

### 开发环境配置
```json
{
  "System": {
    "StartupMode": "Debug",
    "DebugMode": true
  },
  "Logging": {
    "LogLevel": "Debug",
    "EnableConsoleLog": true
  }
}
```

### 生产环境配置
```json
{
  "System": {
    "StartupMode": "Normal",
    "DebugMode": false
  },
  "Logging": {
    "LogLevel": "Info",
    "EnableConsoleLog": false
  }
}
```

### 测试环境配置
```json
{
  "System": {
    "StartupMode": "Test",
    "DebugMode": true
  },
  "Communication": {
    "ModbusTcp": {
      "IPAddress": "127.0.0.1"
    }
  }
}
```

## 配置最佳实践

### 1. 配置分层
- 基础配置放在 App.config
- 业务配置放在 SystemConfig.json
- 敏感配置单独加密存储

### 2. 配置验证
- 启动时验证所有配置参数
- 提供配置错误的详细提示
- 支持配置修复建议

### 3. 配置热重载
- 支持运行时重新加载配置
- 配置变更时通知相关模块
- 保证配置一致性

### 4. 配置备份
- 定期自动备份配置文件
- 配置修改前创建备份
- 提供配置恢复功能

### 5. 配置文档
- 为每个配置参数提供详细说明
- 提供配置示例和最佳实践
- 维护配置变更历史

通过合理的配置管理，MyHMI 系统可以灵活适应不同的部署环境和业务需求。
