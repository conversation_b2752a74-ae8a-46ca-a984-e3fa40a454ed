# IO控制逻辑需求文档

**项目名称：** DMC1000B运动控制卡IO控制系统  
**文档版本：** 1.0  
**创建日期：** 2025-09-19  
**最后更新：** 2025-09-19  

## 1. 项目概述

### 1.1 项目背景
基于DMC1000B运动控制卡的IO控制系统，需要实现对所有输入输出端口的完整控制和监控功能，包括基础IO和扩展IO。

### 1.2 项目目标
- 实现DMC1000B控制卡的完整IO控制功能
- 提供直观的用户界面进行IO操作
- 实现实时IO状态监控和反馈
- 支持基础IO和扩展IO的统一管理

### 1.3 技术架构
- 硬件平台：DMC1000B运动控制卡
- 软件平台：C# WinForms应用程序
- 通信方式：P/Invoke调用Dmc1000.dll
- 架构模式：分层架构 + 事件驱动

## 2. 功能需求

### 2.1 基础IO控制需求

#### 2.1.1 输入IO监控需求
**基础输入IO（I0001-I0016）：**
- I0001 (30): 启动 - 系统启动信号
- I0002 (31): 暂停/停止 - 系统暂停和停止信号
- I0003 (32): 急停 - 紧急停止信号
- I0004 (33): 来料感应 - 物料到位检测
- I0005 (55): 左夹料夹感应 - 左侧夹料气缸夹紧状态
- I0006 (56): 左夹料松感应 - 左侧夹料气缸松开状态
- I0007 (57): 左顶料进感应 - 左侧顶料气缸伸出状态
- I0008 (58): 左顶料退感应 - 左侧顶料气缸缩回状态
- I0009 (59): 右夹料夹感应 - 右侧夹料气缸夹紧状态
- I0010 (60): 右夹料松感应 - 右侧夹料气缸松开状态
- I0011 (61): 右顶料进感应 - 右侧顶料气缸伸出状态
- I0012 (62): 右顶料退感应 - 右侧顶料气缸缩回状态
- I0013 (63): 左NG盘感应 - 左侧NG料盘检测
- I0014 (64): 右NG盘感应 - 右侧NG料盘检测
- I0015 (65): 备用 - 预留输入端口
- I0016 (68): 板卡上电检测 - 控制卡电源状态

#### 2.1.2 输出IO控制需求
**基础输出IO（O0001-O0012）：**
- O0001 (43): 左夹料气缸 - 控制左侧夹料气缸动作
- O0002 (44): 左顶料气缸 - 控制左侧顶料气缸动作
- O0003 (45): 右夹料气缸 - 控制右侧夹料气缸动作
- O0004 (46): 右顶料气缸 - 控制右侧顶料气缸动作
- O0005 (47): 主光源 - 控制主要照明光源
- O0006 (48): 左NG灯 - 控制左侧NG指示灯
- O0007 (49): 右NG灯 - 控制右侧NG指示灯
- O0008 (50): 三色灯红 - 控制三色灯红色
- O0009 (51): 三色灯绿 - 控制三色灯绿色
- O0010 (52): 三色灯黄 - 控制三色灯黄色
- O0011 (53): 上料光源 - 控制上料区域光源
- O0012 (54): 备用 - 预留输出端口

### 2.2 扩展IO控制需求

#### 2.2.1 扩展输入IO监控需求
**扩展输入IO（I0101-I0116）：**
- I0101: 安全门 - 安全门开关状态检测
- I0102: 光栅1 - 第一道光栅安全检测
- I0103: 光栅2 - 第二道光栅安全检测
- I0104: 左NG确认 - 左侧NG确认按钮
- I0105: 右NG确认 - 右侧NG确认按钮
- I0106: 出料感应 - 出料区域物料检测
- I0107: 出料完成 - 出料完成信号
- I0108-I0112: 备用 - 预留输入端口
- I0113: 后站对接输入2 - 后工位对接信号2
- I0114: 前站对接输入 - 前工位对接信号
- I0115: 后站对接输入1 - 后工位对接信号1
- I0116: 备用 - 预留输入端口

#### 2.2.2 扩展输出IO控制需求
**扩展输出IO（O0101-O0115）：**
- O0101-O0112: 备用 - 预留输出端口
- O0113: 后站对接输出2 - 后工位对接输出信号2
- O0114: 前站对接输出 - 前工位对接输出信号
- O0115: 后站对接输出 - 后工位对接输出信号

### 2.3 系统集成需求
- IO控制模块与现有UI界面无缝集成
- IO控制与电机控制系统协调工作
- 实现完整的生产流程自动化控制

## 3. 验收标准

### 3.1 硬件集成验收标准
- [ ] DMC1000B控制卡IO功能能够正常初始化
- [ ] 基础IO端口（输入16个，输出12个）能够正确识别和控制
- [ ] 扩展IO端口（输入16个，输出15个）能够正确识别和控制
- [ ] 所有IO端口的电气连接和信号传输正常

### 3.2 输入IO监控验收标准
- [ ] 能够实时读取所有32个输入IO的状态
- [ ] 输入IO状态变化能够及时检测和响应
- [ ] 输入IO状态显示准确，更新频率不低于10Hz
- [ ] 关键安全输入（急停、安全门、光栅）能够立即响应

### 3.3 输出IO控制验收标准
- [ ] 能够独立控制所有27个输出IO的状态
- [ ] 输出IO控制指令能够准确执行
- [ ] 输出IO状态能够正确读取和显示
- [ ] 输出IO控制响应时间不超过50ms

### 3.4 UI界面集成验收标准
- [ ] IO控制界面布局清晰，操作直观
- [ ] 输入IO状态能够实时显示，状态指示清晰
- [ ] 输出IO控制按钮响应正常，状态反馈及时
- [ ] 界面风格与现有设计保持一致

### 3.5 实时监控验收标准
- [ ] IO状态监控频率不低于10Hz
- [ ] IO状态变化事件能够及时触发
- [ ] 批量IO状态更新功能正常
- [ ] IO状态历史记录功能完整

### 3.6 安全性验收标准
- [ ] 急停功能能够立即切断所有输出
- [ ] 安全门和光栅信号能够触发安全保护
- [ ] IO控制权限管理功能正常
- [ ] 异常情况下能够安全停机

### 3.7 扩展性验收标准
- [ ] 扩展IO功能完全正常
- [ ] IO配置能够灵活修改
- [ ] 支持未来IO功能扩展
- [ ] IO映射关系清晰可维护

## 4. 技术规范

### 4.1 硬件规范
- 控制卡型号：DMC1000B
- 基础IO：输入16路，输出12路（24V隔离）
- 扩展IO：输入16路，输出15路（3.3V非隔离）
- 通信接口：PCI总线

### 4.2 软件规范
- 开发语言：C# .NET Framework 4.8
- 通信方式：P/Invoke调用Dmc1000.dll
- 架构模式：单例模式 + 事件驱动
- 更新频率：IO状态监控10Hz以上

### 4.3 性能规范
- IO读取响应时间：< 10ms
- IO输出响应时间：< 50ms
- 状态更新频率：≥ 10Hz
- 内存占用：< 50MB

## 5. 风险评估

### 5.1 技术风险
- DMC1000B硬件兼容性问题
- P/Invoke调用稳定性风险
- 实时性能要求的挑战

### 5.2 集成风险
- 与现有系统的兼容性问题
- UI界面集成的复杂性
- 多线程并发访问的安全性

### 5.3 安全风险
- 安全IO信号的可靠性
- 紧急停止功能的及时性
- IO控制权限的安全性

## 6. 项目约束

### 6.1 时间约束
- 项目需要在合理时间内完成
- 分阶段交付，优先实现核心功能

### 6.2 资源约束
- 基于现有硬件平台开发
- 复用现有代码架构和组件
- 最小化对现有系统的影响

### 6.3 质量约束
- 代码质量必须符合现有项目标准
- 必须提供完整的中文注释
- 必须通过所有验收测试

## 7. 开发计划

### 7.1 第一阶段：基础功能开发
- 修改现有IO控件适配端口定义
- 封装DMC1000B的IO控制函数
- 实现基础IO读写功能

### 7.2 第二阶段：扩展功能开发
- 实现扩展IO控制功能
- 添加实时监控和事件系统
- 完善UI界面集成

### 7.3 第三阶段：测试和优化
- 功能测试和性能优化
- 安全性测试和验证
- 文档完善和交付

## 8. 交付物

### 8.1 代码文件
- DMC1000BIOManager.cs - IO业务逻辑管理器
- IOModels.cs - IO数据模型定义
- 修改后的IOReadPanel.cs和IOWritePanel.cs
- IO配置和映射文件

### 8.2 文档文件
- IO控制逻辑需求文档
- IO功能测试报告
- IO操作用户手册
- 技术实现文档

### 8.3 测试文件
- IO功能测试用例
- 性能测试报告
- 安全性测试报告
