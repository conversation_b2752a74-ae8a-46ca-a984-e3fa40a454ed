[2025-09-28 11:08:57.030] [INFO] 程序启动开始
[2025-09-28 11:08:57.031] [INFO] 加载Settings系统配置...
[2025-09-28 11:08:57.036] [INFO] 设置加载成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-28 11:08:57.036] [INFO] Settings系统配置加载完成
[2025-09-28 11:08:57.037] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-28 11:08:57.047] [INFO] 开始初始化各个Manager...
[2025-09-28 11:08:57.048] [INFO] 初始化DMC1000B控制卡...
[2025-09-28 11:08:57.051] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-28 11:08:57.052] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-28 11:08:57.053] [WARN] DMC1000B控制卡初始化失败，相关功能可能无法正常工作
[2025-09-28 11:08:57.053] [INFO] 初始化基础Manager...
[2025-09-28 11:08:57.057] [INFO] IO状态缓存初始化完成
[2025-09-28 11:08:57.059] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-28 11:08:57.059] [WARN] DMC1000BIO管理器初始化失败
[2025-09-28 11:08:57.063] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-28 11:08:57.063] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-28 11:08:57.086] [WARN] DMC1000B电机管理器初始化失败
[2025-09-28 11:08:57.086] [INFO] 初始化系统模式管理器...
[2025-09-28 11:08:57.094] [INFO] 从Settings系统加载安全管理器配置成功
[2025-09-28 11:08:57.096] [INFO] 安全管理器实例已创建
[2025-09-28 11:08:57.100] [INFO] 开始初始化MotorManager...
[2025-09-28 11:08:57.100] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-28 11:08:57.101] [INFO] 当前模式已经是 Manual，无需切换
[2025-09-28 11:08:57.101] [INFO] 模拟初始化运动控制卡
[2025-09-28 11:08:57.313] [INFO] 加载了8个电机的默认配置
[2025-09-28 11:08:57.315] [INFO] 电机监控任务已启动
[2025-09-28 11:08:57.315] [INFO] MotorManager初始化完成
[2025-09-28 11:08:57.315] [INFO] 初始化通信Manager...
[2025-09-28 11:08:57.318] [INFO] 电机监控循环开始
[2025-09-28 11:08:57.320] [INFO] 开始初始化ScannerManager...
[2025-09-28 11:08:57.323] [INFO] 扫描枪配置从Settings系统加载: COM1, 9600, 8, One, None
[2025-09-28 11:08:57.326] [INFO] 串口初始化完成
[2025-09-28 11:08:57.329] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-28 11:08:57.336] [ERROR] 连接扫描枪 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerManager.<ConnectAsync>b__23_1() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 145
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerManager.<<ConnectAsync>b__23_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 139
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-28 11:08:57.338] [WARN] 扫描枪连接失败，但初始化完成，可稍后重试连接
[2025-09-28 11:08:57.339] [INFO] ScannerManager初始化完成
[2025-09-28 11:08:57.343] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-28 11:08:57.344] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-28 11:08:57.344] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-28 11:08:57.345] [INFO] SCARA通信管理器已初始化
[2025-09-28 11:08:57.347] [INFO] ScannerAutoModeManager管理器引用初始化完成
[2025-09-28 11:08:57.349] [INFO] 开始初始化ScannerAutoModeManager...
[2025-09-28 11:08:57.351] [INFO] 开始初始化MultiScannerManager...
[2025-09-28 11:08:57.352] [INFO] 开始初始化扫描枪1...
[2025-09-28 11:08:57.353] [INFO] 从Settings系统加载扫描枪1配置: COM1, 115200
[2025-09-28 11:08:57.354] [INFO] 扫描枪1串口初始化完成
[2025-09-28 11:08:57.354] [INFO] 扫描枪1初始化完成
[2025-09-28 11:08:57.355] [INFO] 开始初始化扫描枪2...
[2025-09-28 11:08:57.355] [INFO] 从Settings系统加载扫描枪2配置: COM2, 115200
[2025-09-28 11:08:57.355] [INFO] 扫描枪2串口初始化完成
[2025-09-28 11:08:57.355] [INFO] 扫描枪2初始化完成
[2025-09-28 11:08:57.356] [INFO] 开始初始化扫描枪3...
[2025-09-28 11:08:57.356] [INFO] 从Settings系统加载扫描枪3配置: COM3, 115200
[2025-09-28 11:08:57.356] [INFO] 扫描枪3串口初始化完成
[2025-09-28 11:08:57.356] [INFO] 扫描枪3初始化完成
[2025-09-28 11:08:57.356] [INFO] MultiScannerManager初始化完成
[2025-09-28 11:08:57.356] [INFO] ScannerAutoModeManager初始化完成
[2025-09-28 11:08:57.359] [INFO] 开始初始化ModbusTcpManager...
[2025-09-28 11:08:57.361] [INFO] Modbus TCP配置: *************:502, SlaveId: 1
[2025-09-28 11:08:57.365] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-28 11:09:02.387] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: *************:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 153
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-28 11:09:02.388] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-28 11:09:02.388] [INFO] ModbusTcpManager初始化完成
[2025-09-28 11:09:02.391] [INFO] 开始初始化EpsonRobotManager...
[2025-09-28 11:09:02.392] [INFO] 从Settings系统加载Epson机器人1配置成功
[2025-09-28 11:09:02.392] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-28 11:09:02.392] [INFO] EpsonRobotManager初始化完成
[2025-09-28 11:09:02.393] [INFO] 初始化视觉Manager...
[2025-09-28 11:09:02.394] [INFO] 开始初始化VisionManager...
[2025-09-28 11:09:02.395] [INFO] 视觉系统配置从Settings系统加载: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms, 置信度阈值=0.8
[2025-09-28 11:09:02.396] [INFO] 模拟初始化相机，索引: 0
[2025-09-28 11:09:02.902] [INFO] 相机初始化成功
[2025-09-28 11:09:02.903] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-28 11:09:02.903] [INFO] 视觉配置加载完成
[2025-09-28 11:09:02.903] [INFO] VisionManager初始化完成
[2025-09-28 11:09:02.903] [INFO] 初始化数据Manager...
[2025-09-28 11:09:02.905] [INFO] 开始初始化StatisticsManager...
[2025-09-28 11:09:02.906] [INFO] 统计管理器配置: 数据目录=Export, 缓存大小=1000, 自动保存间隔=24分钟
[2025-09-28 11:09:02.907] [INFO] 创建数据目录: Export
[2025-09-28 11:09:02.912] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-28 11:09:02.912] [INFO] 历史数据加载完成
[2025-09-28 11:09:02.912] [INFO] StatisticsManager初始化完成
[2025-09-28 11:09:02.913] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-28 11:09:02.913] [INFO] 所有Manager初始化完成
[2025-09-28 11:09:02.950] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-28 11:09:02.950] [INFO] 创建并缓存面板: vision-position
[2025-09-28 11:09:02.951] [INFO] 主界面布局创建完成
[2025-09-28 11:09:02.952] [INFO] 时间更新定时器初始化完成
[2025-09-28 11:09:02.952] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-28 11:09:03.051] [INFO] 开始初始化系统
[2025-09-28 11:09:03.052] [INFO] 初始化业务逻辑
[2025-09-28 11:09:03.054] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-28 11:09:03.054] [INFO] IO管理器初始化完成
[2025-09-28 11:09:03.055] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-28 11:09:03.055] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-28 11:09:03.055] [INFO] 电机管理器初始化完成
[2025-09-28 11:09:03.056] [INFO] IO事件订阅完成
[2025-09-28 11:09:03.057] [INFO] 电机事件订阅完成
[2025-09-28 11:09:03.057] [INFO] 业务层交互机制建立完成
[2025-09-28 11:09:03.057] [INFO] 业务逻辑初始化完成
[2025-09-28 11:09:03.058] [INFO] 执行UI界面刷新
[2025-09-28 11:09:03.062] [INFO] UI界面刷新完成
[2025-09-28 11:09:03.062] [INFO] 系统初始化完成
[2025-09-28 11:10:19.994] [INFO] 开始切换到自动模式...
[2025-09-28 11:10:19.995] [INFO] 开始检查系统是否准备好进入自动模式...
[2025-09-28 11:10:19.997] [INFO] 系统基础检查完成，准备进入自动模式（具体硬件初始化将在开机自检中完成）
[2025-09-28 11:10:19.998] [INFO] 系统模式已从 Manual 切换到 Automatic
[2025-09-28 11:10:20.000] [INFO] 启动安全管理器...
[2025-09-28 11:10:20.003] [ERROR] 安全管理器未初始化，无法启动
[2025-09-28 11:10:20.003] [ERROR] 启动安全管理器失败，无法切换到自动模式
[2025-09-28 11:10:20.004] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-28 11:10:20.004] [INFO] 系统模式已保存到配置文件: Automatic
[2025-09-28 11:10:32.108] [INFO] 外部设备状态菜单已显示
[2025-09-28 11:10:33.951] [INFO] 打开自动模式工作流程测试窗口
[2025-09-28 11:10:34.000] [INFO] 自动模式工作流程测试窗口已打开
[2025-09-28 11:10:35.656] [INFO] ========== 开始执行自动模式工作流程综合测试 ==========
[2025-09-28 11:10:35.657] [INFO] 初始化测试环境中的所有管理器...
[2025-09-28 11:10:35.658] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-28 11:10:35.658] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-28 11:10:35.659] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-28 11:10:35.659] [INFO] 测试环境管理器初始化完成
[2025-09-28 11:10:35.661] [INFO] 开始运行所有测试...
[2025-09-28 11:10:35.662] [INFO] [RUN] 开机自检-DMC1000B控制卡初始化测试
[2025-09-28 11:10:35.663] [INFO] 测试: DMC1000B控制卡初始化
[2025-09-28 11:10:35.664] [WARN] DMC1000B控制卡不可用，跳过硬件相关测试
[2025-09-28 11:10:35.664] [INFO] [PASS] 开机自检-DMC1000B控制卡初始化测试 (1ms)
[2025-09-28 11:10:35.664] [INFO] [RUN] 开机自检-翻转电机回零测试
[2025-09-28 11:10:35.665] [INFO] 测试: 左/右翻转电机自动回零并移动到位置1
[2025-09-28 11:10:35.665] [WARN] 电机管理器未初始化，跳过电机测试
[2025-09-28 11:10:35.665] [INFO] [PASS] 开机自检-翻转电机回零测试 (0ms)
[2025-09-28 11:10:35.665] [INFO] [RUN] 开机自检-6轴机器人连接测试
[2025-09-28 11:10:35.666] [INFO] 测试: 2台6轴机器人连接主端口、启动机器人、激活数据端口
[2025-09-28 11:10:35.666] [INFO] 机器人1管理器状态: True
[2025-09-28 11:10:35.666] [INFO] 机器人2管理器状态: True
[2025-09-28 11:10:35.666] [INFO] ✅ 6轴机器人连接测试通过
[2025-09-28 11:10:35.666] [INFO] [PASS] 开机自检-6轴机器人连接测试 (1ms)
[2025-09-28 11:10:35.666] [INFO] [RUN] 皮带电机-输入皮带控制逻辑测试
[2025-09-28 11:10:35.668] [INFO] 测试: 输入皮带电机控制逻辑 (传感器I0004)
[2025-09-28 11:10:35.793] [INFO] 传感器I0004=0，皮带应该转动
[2025-09-28 11:10:35.917] [INFO] 传感器I0004=1，皮带应该停止
[2025-09-28 11:10:35.917] [INFO] ✅ 输入皮带控制逻辑测试通过
[2025-09-28 11:10:35.918] [INFO] [PASS] 皮带电机-输入皮带控制逻辑测试 (251ms)
[2025-09-28 11:10:35.918] [INFO] [RUN] 皮带电机-输出皮带控制逻辑测试
[2025-09-28 11:10:35.920] [INFO] 测试: 输出皮带电机控制逻辑 (传感器I0106)
[2025-09-28 11:10:36.042] [INFO] 传感器I0106=0，皮带应该停止
[2025-09-28 11:10:36.167] [INFO] 传感器I0106=1，皮带应该转动
[2025-09-28 11:10:36.168] [INFO] ✅ 输出皮带控制逻辑测试通过
[2025-09-28 11:10:36.169] [INFO] [PASS] 皮带电机-输出皮带控制逻辑测试 (250ms)
[2025-09-28 11:10:36.169] [INFO] [RUN] SCARA-左翻转电机工作流程测试
[2025-09-28 11:10:36.177] [INFO] 测试: 左翻转电机完整工作流程
[2025-09-28 11:10:36.791] [INFO] ✅ 左翻转电机工作流程测试通过
[2025-09-28 11:10:36.792] [INFO] [PASS] SCARA-左翻转电机工作流程测试 (622ms)
[2025-09-28 11:10:36.793] [INFO] [RUN] SCARA-右翻转电机工作流程测试
[2025-09-28 11:10:36.799] [INFO] 测试: 右翻转电机完整工作流程
[2025-09-28 11:10:37.226] [INFO] ✅ 右翻转电机工作流程测试通过
[2025-09-28 11:10:37.227] [INFO] [PASS] SCARA-右翻转电机工作流程测试 (434ms)
[2025-09-28 11:10:37.228] [INFO] [RUN] 扫码器-串口连接和通信测试
[2025-09-28 11:10:37.230] [INFO] 测试: 3个扫码器串口连接和hello/world通信
[2025-09-28 11:10:37.231] [INFO] 模拟1号扫码器发送hello，接收world
[2025-09-28 11:10:37.232] [INFO] 模拟2号扫码器发送hello，接收world
[2025-09-28 11:10:37.232] [INFO] 模拟3号扫码器发送hello，接收world
[2025-09-28 11:10:37.233] [INFO] 扫码器管理器可用
[2025-09-28 11:10:37.233] [INFO] ✅ 扫码器串口连接测试通过
[2025-09-28 11:10:37.234] [INFO] [PASS] 扫码器-串口连接和通信测试 (6ms)
[2025-09-28 11:10:37.235] [INFO] [RUN] 扫码器-3号扫码器自动模式测试
[2025-09-28 11:10:37.239] [INFO] 测试: 3号扫码器自动模式逻辑
[2025-09-28 11:10:37.305] [INFO] ML_dataget_ok置1，触发3号扫码器扫码
[2025-09-28 11:10:37.367] [INFO] MR_dataget_ok置1，触发3号扫码器扫码
[2025-09-28 11:10:37.368] [INFO] ✅ 3号扫码器自动模式测试通过
[2025-09-28 11:10:37.368] [INFO] [PASS] 扫码器-3号扫码器自动模式测试 (133ms)
[2025-09-28 11:10:37.369] [INFO] [RUN] 扫码器-1号和2号扫码器自动模式测试
[2025-09-28 11:10:37.373] [INFO] 测试: 1号和2号扫码器自动模式逻辑
[2025-09-28 11:10:37.445] [INFO] L_dataget_ok置1，触发1号扫码器扫码
[2025-09-28 11:10:37.508] [INFO] R_dataget_ok置1，触发2号扫码器扫码
[2025-09-28 11:10:37.509] [INFO] ✅ 1号和2号扫码器自动模式测试通过
[2025-09-28 11:10:37.510] [INFO] [PASS] 扫码器-1号和2号扫码器自动模式测试 (139ms)
[2025-09-28 11:10:37.510] [INFO] [RUN] 6轴机器人-连接和登录测试
[2025-09-28 11:10:37.513] [INFO] 测试: 2台6轴机器人连接主端口和数据端口
[2025-09-28 11:10:37.513] [INFO] 机器人1主端口连接: True
[2025-09-28 11:10:37.514] [INFO] 机器人2主端口连接: True
[2025-09-28 11:10:37.515] [INFO] ✅ 6轴机器人连接测试通过
[2025-09-28 11:10:37.515] [INFO] [PASS] 6轴机器人-连接和登录测试 (5ms)
[2025-09-28 11:10:37.516] [INFO] [RUN] 6轴机器人-GETPICK/ALLOWPICK流程测试
[2025-09-28 11:10:37.521] [INFO] 测试: 机器人GETPICK询问和ALLOWPICK/DENYPICK响应
[2025-09-28 11:10:37.521] [INFO] 模拟机器人1发送GETPICK
[2025-09-28 11:10:37.522] [INFO] 程序响应机器人1: ALLOWPICK
[2025-09-28 11:10:37.523] [INFO] 模拟机器人2发送GETPICK
[2025-09-28 11:10:37.524] [INFO] 程序响应机器人2: ALLOWPICK
[2025-09-28 11:10:37.525] [INFO] ✅ GETPICK/ALLOWPICK流程测试通过
[2025-09-28 11:10:37.525] [INFO] [PASS] 6轴机器人-GETPICK/ALLOWPICK流程测试 (8ms)
[2025-09-28 11:10:37.526] [INFO] [RUN] 6轴机器人-INPICK数据传输测试
[2025-09-28 11:10:37.536] [INFO] 测试: 机器人INPICK和数据传输流程
[2025-09-28 11:10:37.537] [INFO] 模拟机器人1发送INPICK
[2025-09-28 11:10:37.679] [INFO] 发送数据给机器人1: ML_PRODUCT_001,L_PRODUCT_003
[2025-09-28 11:10:37.679] [INFO] 模拟机器人2发送INPICK
[2025-09-28 11:10:37.819] [INFO] 发送数据给机器人2: MR_PRODUCT_002,R_PRODUCT_004
[2025-09-28 11:10:38.833] [INFO] 电机复位到位置1，L_moto_ready和R_moto_ready置1
[2025-09-28 11:10:38.834] [INFO] ✅ INPICK数据传输测试通过
[2025-09-28 11:10:38.834] [INFO] [PASS] 6轴机器人-INPICK数据传输测试 (1308ms)
[2025-09-28 11:10:38.835] [INFO] [RUN] 6轴机器人-NG品处理流程测试
[2025-09-28 11:10:38.840] [INFO] 测试: 机器人NG品处理流程
[2025-09-28 11:10:38.840] [INFO] 模拟机器人1发送GETNGPUT
[2025-09-28 11:10:38.862] [INFO] I0104为1，发送ALLOWNGPUT给机器人1
[2025-09-28 11:10:38.863] [INFO] 模拟机器人1发送NGPUTFULL
[2025-09-28 11:10:38.955] [INFO] 发送RESETNGPUT给机器人1
[2025-09-28 11:10:38.955] [INFO] 模拟机器人2发送GETNGPUT
[2025-09-28 11:10:38.971] [INFO] I0105为1，发送ALLOWNGPUT给机器人2
[2025-09-28 11:10:38.971] [INFO] ✅ NG品处理流程测试通过
[2025-09-28 11:10:38.971] [INFO] [PASS] 6轴机器人-NG品处理流程测试 (136ms)
[2025-09-28 11:10:38.971] [INFO] [RUN] 6轴机器人-OK品处理流程测试
[2025-09-28 11:10:38.972] [INFO] 测试: 机器人OK品处理流程
[2025-09-28 11:10:38.972] [INFO] 模拟机器人发送GETOKPUT
[2025-09-28 11:10:38.987] [INFO] I0106为0，发送ALLOWOKPUT给机器人
[2025-09-28 11:10:39.002] [INFO] I0106为1，发送DENYOKPUT给机器人
[2025-09-28 11:10:39.002] [INFO] ✅ OK品处理流程测试通过
[2025-09-28 11:10:39.003] [INFO] [PASS] 6轴机器人-OK品处理流程测试 (31ms)
[2025-09-28 11:10:39.004] [INFO] [RUN] 安全管理-紧急停止测试
[2025-09-28 11:10:39.007] [INFO] 测试: 紧急停止逻辑
[2025-09-28 11:10:39.033] [INFO] I0102为1，触发机器人紧急停止
[2025-09-28 11:10:39.049] [INFO] I0103为1，触发机器人紧急停止
[2025-09-28 11:10:39.065] [INFO] I0101为0，触发机器人紧急停止
[2025-09-28 11:10:39.065] [INFO] ✅ 紧急停止测试通过
[2025-09-28 11:10:39.065] [INFO] [PASS] 安全管理-紧急停止测试 (61ms)
[2025-09-28 11:10:39.065] [INFO] [RUN] 安全管理-启动停止按钮测试
[2025-09-28 11:10:39.066] [INFO] 测试: 启动停止按钮逻辑
[2025-09-28 11:10:39.081] [INFO] I0001电平变化0->1，启动机器人
[2025-09-28 11:10:39.096] [INFO] I0001电平变化1->0，启动机器人
[2025-09-28 11:10:39.112] [INFO] I0002电平变化0->1，停止程序和机器人所有动作
[2025-09-28 11:10:39.127] [INFO] I0002电平变化1->0，停止程序和机器人所有动作
[2025-09-28 11:10:39.143] [INFO] I0003为1，紧急停止程序所有操作
[2025-09-28 11:10:39.159] [INFO] I0003为0，可以再次启动程序
[2025-09-28 11:10:39.159] [INFO] ✅ 启动停止按钮测试通过
[2025-09-28 11:10:39.159] [INFO] [PASS] 安全管理-启动停止按钮测试 (94ms)
[2025-09-28 11:10:39.159] [INFO] [RUN] 安全管理-指示灯控制测试
[2025-09-28 11:10:39.160] [INFO] 测试: 指示灯控制逻辑
[2025-09-28 11:10:39.174] [INFO] 调试模式停止时，黄灯亮 (O0010=1)
[2025-09-28 11:10:39.206] [INFO] 自动模式正常运行时，绿灯亮 (O0009=1)
[2025-09-28 11:10:39.237] [INFO] 程序报错紧急停止时，红灯亮 (O0008=1)
[2025-09-28 11:10:39.237] [INFO] ✅ 指示灯控制测试通过
[2025-09-28 11:10:39.237] [INFO] [PASS] 安全管理-指示灯控制测试 (78ms)
[2025-09-28 11:10:39.238] [INFO] [RUN] 集成测试-完整自动模式工作流程
[2025-09-28 11:10:39.241] [INFO] 测试: 完整自动模式工作流程集成
[2025-09-28 11:10:39.241] [INFO] 步骤1: 执行开机自检
[2025-09-28 11:10:39.242] [INFO] 步骤2: 启动皮带电机自动控制
[2025-09-28 11:10:39.268] [INFO] 步骤3: 执行SCARA工作流程
[2025-09-28 11:10:39.704] [INFO] 步骤4: 执行扫码器工作流程
[2025-09-28 11:10:39.923] [INFO] 步骤5: 执行6轴机器人工作流程
[2025-09-28 11:10:39.926] [INFO] 模拟机器人1和机器人2完成取料和数据传输
[2025-09-28 11:10:40.437] [INFO] 步骤6: 验证工作流程完成
[2025-09-28 11:10:40.439] [INFO] ✅ 完整自动模式工作流程集成测试通过
[2025-09-28 11:10:40.440] [INFO] [PASS] 集成测试-完整自动模式工作流程 (1202ms)
[2025-09-28 11:10:40.443] [INFO] === 测试摘要 ===
[2025-09-28 11:10:40.444] [INFO] 总测试数: 19
[2025-09-28 11:10:40.445] [INFO] 通过: 19
[2025-09-28 11:10:40.446] [INFO] 失败: 0
[2025-09-28 11:10:40.446] [INFO] 跳过: 0
[2025-09-28 11:10:40.447] [INFO] 总耗时: 4779.3ms
[2025-09-28 11:10:40.448] [INFO] 成功率: 100.0%
[2025-09-28 11:10:40.449] [INFO] ===============
[2025-09-28 11:10:40.453] [INFO] ========== 自动模式工作流程综合测试报告 ==========
[2025-09-28 11:10:40.454] [INFO] 总测试数: 19
[2025-09-28 11:10:40.454] [INFO] 通过测试数: 19
[2025-09-28 11:10:40.455] [INFO] 失败测试数: 0
[2025-09-28 11:10:40.456] [INFO] 总耗时: 4.79秒
[2025-09-28 11:10:40.456] [INFO] 整体结果: ✅ 通过
[2025-09-28 11:10:40.456] [INFO] ================================================
[2025-09-28 11:10:40.457] [INFO] ========== 自动模式工作流程综合测试完成，总耗时: 4.79秒 ==========
[2025-09-28 11:10:59.721] [INFO] ========== 开始执行自动模式工作流程综合测试 ==========
[2025-09-28 11:10:59.721] [INFO] 初始化测试环境中的所有管理器...
[2025-09-28 11:10:59.722] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-28 11:10:59.722] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-28 11:10:59.722] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-28 11:10:59.722] [INFO] 测试环境管理器初始化完成
[2025-09-28 11:10:59.722] [INFO] 开始运行所有测试...
[2025-09-28 11:10:59.723] [INFO] [RUN] 开机自检-DMC1000B控制卡初始化测试
[2025-09-28 11:10:59.723] [INFO] 测试: DMC1000B控制卡初始化
[2025-09-28 11:10:59.723] [WARN] DMC1000B控制卡不可用，跳过硬件相关测试
[2025-09-28 11:10:59.723] [INFO] [PASS] 开机自检-DMC1000B控制卡初始化测试 (0ms)
[2025-09-28 11:10:59.723] [INFO] [RUN] 开机自检-翻转电机回零测试
[2025-09-28 11:10:59.723] [INFO] 测试: 左/右翻转电机自动回零并移动到位置1
[2025-09-28 11:10:59.723] [WARN] 电机管理器未初始化，跳过电机测试
[2025-09-28 11:10:59.724] [INFO] [PASS] 开机自检-翻转电机回零测试 (0ms)
[2025-09-28 11:10:59.724] [INFO] [RUN] 开机自检-6轴机器人连接测试
[2025-09-28 11:10:59.724] [INFO] 测试: 2台6轴机器人连接主端口、启动机器人、激活数据端口
[2025-09-28 11:10:59.724] [INFO] 机器人1管理器状态: True
[2025-09-28 11:10:59.724] [INFO] 机器人2管理器状态: True
[2025-09-28 11:10:59.724] [INFO] ✅ 6轴机器人连接测试通过
[2025-09-28 11:10:59.724] [INFO] [PASS] 开机自检-6轴机器人连接测试 (0ms)
[2025-09-28 11:10:59.725] [INFO] [RUN] 皮带电机-输入皮带控制逻辑测试
[2025-09-28 11:10:59.725] [INFO] 测试: 输入皮带电机控制逻辑 (传感器I0004)
[2025-09-28 11:10:59.857] [INFO] 传感器I0004=0，皮带应该转动
[2025-09-28 11:10:59.981] [INFO] 传感器I0004=1，皮带应该停止
[2025-09-28 11:10:59.982] [INFO] ✅ 输入皮带控制逻辑测试通过
[2025-09-28 11:10:59.983] [INFO] [PASS] 皮带电机-输入皮带控制逻辑测试 (257ms)
[2025-09-28 11:10:59.983] [INFO] [RUN] 皮带电机-输出皮带控制逻辑测试
[2025-09-28 11:10:59.984] [INFO] 测试: 输出皮带电机控制逻辑 (传感器I0106)
[2025-09-28 11:11:00.106] [INFO] 传感器I0106=0，皮带应该停止
[2025-09-28 11:11:00.228] [INFO] 传感器I0106=1，皮带应该转动
[2025-09-28 11:11:00.228] [INFO] ✅ 输出皮带控制逻辑测试通过
[2025-09-28 11:11:00.228] [INFO] [PASS] 皮带电机-输出皮带控制逻辑测试 (245ms)
[2025-09-28 11:11:00.228] [INFO] [RUN] SCARA-左翻转电机工作流程测试
[2025-09-28 11:11:00.229] [INFO] 测试: 左翻转电机完整工作流程
[2025-09-28 11:11:00.836] [INFO] ✅ 左翻转电机工作流程测试通过
[2025-09-28 11:11:00.836] [INFO] [PASS] SCARA-左翻转电机工作流程测试 (608ms)
[2025-09-28 11:11:00.837] [INFO] [RUN] SCARA-右翻转电机工作流程测试
[2025-09-28 11:11:00.838] [INFO] 测试: 右翻转电机完整工作流程
[2025-09-28 11:11:01.256] [INFO] ✅ 右翻转电机工作流程测试通过
[2025-09-28 11:11:01.257] [INFO] [PASS] SCARA-右翻转电机工作流程测试 (419ms)
[2025-09-28 11:11:01.257] [INFO] [RUN] 扫码器-串口连接和通信测试
[2025-09-28 11:11:01.258] [INFO] 测试: 3个扫码器串口连接和hello/world通信
[2025-09-28 11:11:01.259] [INFO] 模拟1号扫码器发送hello，接收world
[2025-09-28 11:11:01.259] [INFO] 模拟2号扫码器发送hello，接收world
[2025-09-28 11:11:01.260] [INFO] 模拟3号扫码器发送hello，接收world
[2025-09-28 11:11:01.261] [INFO] 扫码器管理器可用
[2025-09-28 11:11:01.261] [INFO] ✅ 扫码器串口连接测试通过
[2025-09-28 11:11:01.262] [INFO] [PASS] 扫码器-串口连接和通信测试 (4ms)
[2025-09-28 11:11:01.263] [INFO] [RUN] 扫码器-3号扫码器自动模式测试
[2025-09-28 11:11:01.263] [INFO] 测试: 3号扫码器自动模式逻辑
[2025-09-28 11:11:01.318] [INFO] ML_dataget_ok置1，触发3号扫码器扫码
[2025-09-28 11:11:01.380] [INFO] MR_dataget_ok置1，触发3号扫码器扫码
[2025-09-28 11:11:01.380] [INFO] ✅ 3号扫码器自动模式测试通过
[2025-09-28 11:11:01.380] [INFO] [PASS] 扫码器-3号扫码器自动模式测试 (117ms)
[2025-09-28 11:11:01.380] [INFO] [RUN] 扫码器-1号和2号扫码器自动模式测试
[2025-09-28 11:11:01.380] [INFO] 测试: 1号和2号扫码器自动模式逻辑
[2025-09-28 11:11:01.442] [INFO] L_dataget_ok置1，触发1号扫码器扫码
[2025-09-28 11:11:01.505] [INFO] R_dataget_ok置1，触发2号扫码器扫码
[2025-09-28 11:11:01.505] [INFO] ✅ 1号和2号扫码器自动模式测试通过
[2025-09-28 11:11:01.505] [INFO] [PASS] 扫码器-1号和2号扫码器自动模式测试 (125ms)
[2025-09-28 11:11:01.506] [INFO] [RUN] 6轴机器人-连接和登录测试
[2025-09-28 11:11:01.506] [INFO] 测试: 2台6轴机器人连接主端口和数据端口
[2025-09-28 11:11:01.507] [INFO] 机器人1主端口连接: True
[2025-09-28 11:11:01.507] [INFO] 机器人2主端口连接: True
[2025-09-28 11:11:01.508] [INFO] ✅ 6轴机器人连接测试通过
[2025-09-28 11:11:01.508] [INFO] [PASS] 6轴机器人-连接和登录测试 (2ms)
[2025-09-28 11:11:01.509] [INFO] [RUN] 6轴机器人-GETPICK/ALLOWPICK流程测试
[2025-09-28 11:11:01.509] [INFO] 测试: 机器人GETPICK询问和ALLOWPICK/DENYPICK响应
[2025-09-28 11:11:01.510] [INFO] 模拟机器人1发送GETPICK
[2025-09-28 11:11:01.511] [INFO] 程序响应机器人1: ALLOWPICK
[2025-09-28 11:11:01.512] [INFO] 模拟机器人2发送GETPICK
[2025-09-28 11:11:01.512] [INFO] 程序响应机器人2: ALLOWPICK
[2025-09-28 11:11:01.513] [INFO] ✅ GETPICK/ALLOWPICK流程测试通过
[2025-09-28 11:11:01.513] [INFO] [PASS] 6轴机器人-GETPICK/ALLOWPICK流程测试 (4ms)
[2025-09-28 11:11:01.514] [INFO] [RUN] 6轴机器人-INPICK数据传输测试
[2025-09-28 11:11:01.515] [INFO] 测试: 机器人INPICK和数据传输流程
[2025-09-28 11:11:01.516] [INFO] 模拟机器人1发送INPICK
[2025-09-28 11:11:01.676] [INFO] 发送数据给机器人1: ML_PRODUCT_001,L_PRODUCT_003
[2025-09-28 11:11:01.676] [INFO] 模拟机器人2发送INPICK
[2025-09-28 11:11:01.817] [INFO] 发送数据给机器人2: MR_PRODUCT_002,R_PRODUCT_004
[2025-09-28 11:11:02.831] [INFO] 电机复位到位置1，L_moto_ready和R_moto_ready置1
[2025-09-28 11:11:02.832] [INFO] ✅ INPICK数据传输测试通过
[2025-09-28 11:11:02.833] [INFO] [PASS] 6轴机器人-INPICK数据传输测试 (1318ms)
[2025-09-28 11:11:02.833] [INFO] [RUN] 6轴机器人-NG品处理流程测试
[2025-09-28 11:11:02.834] [INFO] 测试: 机器人NG品处理流程
[2025-09-28 11:11:02.835] [INFO] 模拟机器人1发送GETNGPUT
[2025-09-28 11:11:02.862] [INFO] I0104为1，发送ALLOWNGPUT给机器人1
[2025-09-28 11:11:02.862] [INFO] 模拟机器人1发送NGPUTFULL
[2025-09-28 11:11:02.955] [INFO] 发送RESETNGPUT给机器人1
[2025-09-28 11:11:02.955] [INFO] 模拟机器人2发送GETNGPUT
[2025-09-28 11:11:02.970] [INFO] I0105为1，发送ALLOWNGPUT给机器人2
[2025-09-28 11:11:02.970] [INFO] ✅ NG品处理流程测试通过
[2025-09-28 11:11:02.970] [INFO] [PASS] 6轴机器人-NG品处理流程测试 (137ms)
[2025-09-28 11:11:02.971] [INFO] [RUN] 6轴机器人-OK品处理流程测试
[2025-09-28 11:11:02.971] [INFO] 测试: 机器人OK品处理流程
[2025-09-28 11:11:02.972] [INFO] 模拟机器人发送GETOKPUT
[2025-09-28 11:11:02.985] [INFO] I0106为0，发送ALLOWOKPUT给机器人
[2025-09-28 11:11:03.001] [INFO] I0106为1，发送DENYOKPUT给机器人
[2025-09-28 11:11:03.001] [INFO] ✅ OK品处理流程测试通过
[2025-09-28 11:11:03.001] [INFO] [PASS] 6轴机器人-OK品处理流程测试 (30ms)
[2025-09-28 11:11:03.001] [INFO] [RUN] 安全管理-紧急停止测试
[2025-09-28 11:11:03.001] [INFO] 测试: 紧急停止逻辑
[2025-09-28 11:11:03.016] [INFO] I0102为1，触发机器人紧急停止
[2025-09-28 11:11:03.032] [INFO] I0103为1，触发机器人紧急停止
[2025-09-28 11:11:03.048] [INFO] I0101为0，触发机器人紧急停止
[2025-09-28 11:11:03.048] [INFO] ✅ 紧急停止测试通过
[2025-09-28 11:11:03.048] [INFO] [PASS] 安全管理-紧急停止测试 (46ms)
[2025-09-28 11:11:03.048] [INFO] [RUN] 安全管理-启动停止按钮测试
[2025-09-28 11:11:03.048] [INFO] 测试: 启动停止按钮逻辑
[2025-09-28 11:11:03.064] [INFO] I0001电平变化0->1，启动机器人
[2025-09-28 11:11:03.080] [INFO] I0001电平变化1->0，启动机器人
[2025-09-28 11:11:03.095] [INFO] I0002电平变化0->1，停止程序和机器人所有动作
[2025-09-28 11:11:03.110] [INFO] I0002电平变化1->0，停止程序和机器人所有动作
[2025-09-28 11:11:03.125] [INFO] I0003为1，紧急停止程序所有操作
[2025-09-28 11:11:03.140] [INFO] I0003为0，可以再次启动程序
[2025-09-28 11:11:03.140] [INFO] ✅ 启动停止按钮测试通过
[2025-09-28 11:11:03.140] [INFO] [PASS] 安全管理-启动停止按钮测试 (91ms)
[2025-09-28 11:11:03.140] [INFO] [RUN] 安全管理-指示灯控制测试
[2025-09-28 11:11:03.140] [INFO] 测试: 指示灯控制逻辑
[2025-09-28 11:11:03.156] [INFO] 调试模式停止时，黄灯亮 (O0010=1)
[2025-09-28 11:11:03.187] [INFO] 自动模式正常运行时，绿灯亮 (O0009=1)
[2025-09-28 11:11:03.219] [INFO] 程序报错紧急停止时，红灯亮 (O0008=1)
[2025-09-28 11:11:03.219] [INFO] ✅ 指示灯控制测试通过
[2025-09-28 11:11:03.219] [INFO] [PASS] 安全管理-指示灯控制测试 (78ms)
[2025-09-28 11:11:03.219] [INFO] [RUN] 集成测试-完整自动模式工作流程
[2025-09-28 11:11:03.219] [INFO] 测试: 完整自动模式工作流程集成
[2025-09-28 11:11:03.219] [INFO] 步骤1: 执行开机自检
[2025-09-28 11:11:03.220] [INFO] 步骤2: 启动皮带电机自动控制
[2025-09-28 11:11:03.250] [INFO] 步骤3: 执行SCARA工作流程
[2025-09-28 11:11:03.688] [INFO] 步骤4: 执行扫码器工作流程
[2025-09-28 11:11:03.891] [INFO] 步骤5: 执行6轴机器人工作流程
[2025-09-28 11:11:03.892] [INFO] 模拟机器人1和机器人2完成取料和数据传输
[2025-09-28 11:11:04.405] [INFO] 步骤6: 验证工作流程完成
[2025-09-28 11:11:04.405] [INFO] ✅ 完整自动模式工作流程集成测试通过
[2025-09-28 11:11:04.405] [INFO] [PASS] 集成测试-完整自动模式工作流程 (1186ms)
[2025-09-28 11:11:04.407] [INFO] === 测试摘要 ===
[2025-09-28 11:11:04.407] [INFO] 总测试数: 19
[2025-09-28 11:11:04.408] [INFO] 通过: 19
[2025-09-28 11:11:04.408] [INFO] 失败: 0
[2025-09-28 11:11:04.408] [INFO] 跳过: 0
[2025-09-28 11:11:04.408] [INFO] 总耗时: 4684.6ms
[2025-09-28 11:11:04.409] [INFO] 成功率: 100.0%
[2025-09-28 11:11:04.409] [INFO] ===============
[2025-09-28 11:11:04.409] [INFO] ========== 自动模式工作流程综合测试报告 ==========
[2025-09-28 11:11:04.410] [INFO] 总测试数: 19
[2025-09-28 11:11:04.410] [INFO] 通过测试数: 19
[2025-09-28 11:11:04.410] [INFO] 失败测试数: 0
[2025-09-28 11:11:04.410] [INFO] 总耗时: 9.48秒
[2025-09-28 11:11:04.411] [INFO] 整体结果: ✅ 通过
[2025-09-28 11:11:04.411] [INFO] ================================================
[2025-09-28 11:11:04.411] [INFO] ========== 自动模式工作流程综合测试完成，总耗时: 9.48秒 ==========
[2025-09-28 11:12:55.247] [INFO] 开始切换到自动模式...
[2025-09-28 11:12:55.247] [INFO] 开始检查系统是否准备好进入自动模式...
[2025-09-28 11:12:55.247] [INFO] 系统基础检查完成，准备进入自动模式（具体硬件初始化将在开机自检中完成）
[2025-09-28 11:12:55.247] [INFO] 系统模式已从 Manual 切换到 Automatic
[2025-09-28 11:12:55.248] [INFO] 启动安全管理器...
[2025-09-28 11:12:55.248] [ERROR] 安全管理器未初始化，无法启动
[2025-09-28 11:12:55.248] [ERROR] 启动安全管理器失败，无法切换到自动模式
[2025-09-28 11:12:55.248] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-28 11:12:55.249] [INFO] 系统模式已保存到配置文件: Automatic
[2025-09-28 11:23:08.510] [INFO] 程序启动开始
[2025-09-28 11:23:08.511] [INFO] 加载Settings系统配置...
[2025-09-28 11:23:08.516] [INFO] 设置加载成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-28 11:23:08.516] [INFO] Settings系统配置加载完成
[2025-09-28 11:23:08.516] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-28 11:23:08.527] [INFO] 开始初始化各个Manager...
[2025-09-28 11:23:08.527] [INFO] 初始化DMC1000B控制卡...
[2025-09-28 11:23:08.531] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-28 11:23:08.532] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-28 11:23:08.532] [WARN] DMC1000B控制卡初始化失败，相关功能可能无法正常工作
[2025-09-28 11:23:08.532] [INFO] 初始化基础Manager...
[2025-09-28 11:23:08.539] [INFO] IO状态缓存初始化完成
[2025-09-28 11:23:08.541] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-28 11:23:08.541] [WARN] DMC1000BIO管理器初始化失败
[2025-09-28 11:23:08.546] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-28 11:23:08.547] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-28 11:23:08.570] [WARN] DMC1000B电机管理器初始化失败
[2025-09-28 11:23:08.570] [INFO] 初始化系统模式管理器...
[2025-09-28 11:23:08.576] [INFO] 从Settings系统加载安全管理器配置成功
[2025-09-28 11:23:08.579] [INFO] 安全管理器实例已创建
[2025-09-28 11:23:08.582] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-28 11:23:08.582] [INFO] 开始初始化MotorManager...
[2025-09-28 11:23:08.582] [INFO] 恢复上次系统模式: Automatic
[2025-09-28 11:23:08.583] [INFO] 模拟初始化运动控制卡
[2025-09-28 11:23:08.584] [INFO] 开始切换到自动模式...
[2025-09-28 11:23:08.586] [INFO] 开始检查系统是否准备好进入自动模式...
[2025-09-28 11:23:08.588] [INFO] 系统基础检查完成，准备进入自动模式（具体硬件初始化将在开机自检中完成）
[2025-09-28 11:23:08.589] [INFO] 系统模式已从 Manual 切换到 Automatic
[2025-09-28 11:23:08.589] [INFO] 启动安全管理器...
[2025-09-28 11:23:08.591] [ERROR] 安全管理器未初始化，无法启动
[2025-09-28 11:23:08.591] [ERROR] 启动安全管理器失败，无法切换到自动模式
[2025-09-28 11:23:08.591] [ERROR] 恢复上次系统模式失败: Automatic，保持当前模式
[2025-09-28 11:23:08.591] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-28 11:23:08.591] [INFO] 系统模式已保存到配置文件: Automatic
[2025-09-28 11:23:08.793] [INFO] 加载了8个电机的默认配置
[2025-09-28 11:23:08.795] [INFO] 电机监控任务已启动
[2025-09-28 11:23:08.796] [INFO] MotorManager初始化完成
[2025-09-28 11:23:08.796] [INFO] 初始化通信Manager...
[2025-09-28 11:23:08.799] [INFO] 电机监控循环开始
[2025-09-28 11:23:08.802] [INFO] 开始初始化ScannerManager...
[2025-09-28 11:23:08.807] [INFO] 扫描枪配置从Settings系统加载: COM1, 9600, 8, One, None
[2025-09-28 11:23:08.809] [INFO] 串口初始化完成
[2025-09-28 11:23:08.813] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-28 11:23:08.825] [ERROR] 连接扫描枪 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerManager.<ConnectAsync>b__23_1() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 145
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerManager.<<ConnectAsync>b__23_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 139
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-28 11:23:08.828] [WARN] 扫描枪连接失败，但初始化完成，可稍后重试连接
[2025-09-28 11:23:08.828] [INFO] ScannerManager初始化完成
[2025-09-28 11:23:08.833] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-28 11:23:08.834] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-28 11:23:08.834] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-28 11:23:08.835] [INFO] SCARA通信管理器已初始化
[2025-09-28 11:23:08.836] [INFO] ScannerAutoModeManager管理器引用初始化完成
[2025-09-28 11:23:08.838] [INFO] 开始初始化ScannerAutoModeManager...
[2025-09-28 11:23:08.839] [INFO] 开始初始化MultiScannerManager...
[2025-09-28 11:23:08.841] [INFO] 开始初始化扫描枪1...
[2025-09-28 11:23:08.842] [INFO] 从Settings系统加载扫描枪1配置: COM1, 115200
[2025-09-28 11:23:08.843] [INFO] 扫描枪1串口初始化完成
[2025-09-28 11:23:08.843] [INFO] 扫描枪1初始化完成
[2025-09-28 11:23:08.843] [INFO] 开始初始化扫描枪2...
[2025-09-28 11:23:08.843] [INFO] 从Settings系统加载扫描枪2配置: COM2, 115200
[2025-09-28 11:23:08.844] [INFO] 扫描枪2串口初始化完成
[2025-09-28 11:23:08.844] [INFO] 扫描枪2初始化完成
[2025-09-28 11:23:08.844] [INFO] 开始初始化扫描枪3...
[2025-09-28 11:23:08.844] [INFO] 从Settings系统加载扫描枪3配置: COM3, 115200
[2025-09-28 11:23:08.844] [INFO] 扫描枪3串口初始化完成
[2025-09-28 11:23:08.844] [INFO] 扫描枪3初始化完成
[2025-09-28 11:23:08.844] [INFO] MultiScannerManager初始化完成
[2025-09-28 11:23:08.845] [INFO] ScannerAutoModeManager初始化完成
[2025-09-28 11:23:08.847] [INFO] 开始初始化ModbusTcpManager...
[2025-09-28 11:23:08.848] [INFO] Modbus TCP配置: *************:502, SlaveId: 1
[2025-09-28 11:23:08.851] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-28 11:23:13.867] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: *************:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 153
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-28 11:23:13.867] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-28 11:23:13.868] [INFO] ModbusTcpManager初始化完成
[2025-09-28 11:23:13.870] [INFO] 开始初始化EpsonRobotManager...
[2025-09-28 11:23:13.872] [INFO] 从Settings系统加载Epson机器人1配置成功
[2025-09-28 11:23:13.872] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-28 11:23:13.872] [INFO] EpsonRobotManager初始化完成
[2025-09-28 11:23:13.872] [INFO] 初始化视觉Manager...
[2025-09-28 11:23:13.875] [INFO] 开始初始化VisionManager...
[2025-09-28 11:23:13.875] [INFO] 视觉系统配置从Settings系统加载: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms, 置信度阈值=0.8
[2025-09-28 11:23:13.876] [INFO] 模拟初始化相机，索引: 0
[2025-09-28 11:23:14.381] [INFO] 相机初始化成功
[2025-09-28 11:23:14.383] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-28 11:23:14.384] [INFO] 视觉配置加载完成
[2025-09-28 11:23:14.384] [INFO] VisionManager初始化完成
[2025-09-28 11:23:14.384] [INFO] 初始化数据Manager...
[2025-09-28 11:23:14.390] [INFO] 开始初始化StatisticsManager...
[2025-09-28 11:23:14.391] [INFO] 统计管理器配置: 数据目录=Export, 缓存大小=1000, 自动保存间隔=24分钟
[2025-09-28 11:23:14.404] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-28 11:23:14.404] [INFO] 历史数据加载完成
[2025-09-28 11:23:14.405] [INFO] StatisticsManager初始化完成
[2025-09-28 11:23:14.405] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-28 11:23:14.405] [INFO] 所有Manager初始化完成
[2025-09-28 11:23:14.466] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-28 11:23:14.467] [INFO] 创建并缓存面板: vision-position
[2025-09-28 11:23:14.467] [INFO] 主界面布局创建完成
[2025-09-28 11:23:14.468] [INFO] 时间更新定时器初始化完成
[2025-09-28 11:23:14.469] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-28 11:23:14.548] [INFO] 开始初始化系统
[2025-09-28 11:23:14.550] [INFO] 初始化业务逻辑
[2025-09-28 11:23:14.551] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-28 11:23:14.551] [INFO] IO管理器初始化完成
[2025-09-28 11:23:14.551] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-28 11:23:14.552] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-28 11:23:14.552] [INFO] 电机管理器初始化完成
[2025-09-28 11:23:14.553] [INFO] IO事件订阅完成
[2025-09-28 11:23:14.553] [INFO] 电机事件订阅完成
[2025-09-28 11:23:14.553] [INFO] 业务层交互机制建立完成
[2025-09-28 11:23:14.553] [INFO] 业务逻辑初始化完成
[2025-09-28 11:23:14.554] [INFO] 执行UI界面刷新
[2025-09-28 11:23:14.556] [INFO] UI界面刷新完成
[2025-09-28 11:23:14.556] [INFO] 系统初始化完成
[2025-09-28 11:32:12.933] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-28 11:32:12.934] [INFO] 保存Settings系统配置...
[2025-09-28 11:32:12.935] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-28 11:32:12.936] [INFO] Settings系统配置保存完成
[2025-09-28 11:32:12.940] [INFO] 开始按顺序释放各个Manager资源...
[2025-09-28 11:32:12.941] [INFO] 清理UI资源和面板缓存...
[2025-09-28 11:32:12.941] [INFO] 时间更新定时器资源已释放
[2025-09-28 11:32:12.942] [INFO] 开始清理面板缓存，共1个面板
[2025-09-28 11:32:12.951] [INFO] 面板缓存字典已清空
[2025-09-28 11:32:12.952] [INFO] 面板缓存清理完成
[2025-09-28 11:32:12.952] [INFO] UI资源清理完成
[2025-09-28 11:32:12.952] [INFO] 释放工作流管理器资源...
[2025-09-28 11:32:12.955] [INFO] 开始释放WorkflowManager资源...
[2025-09-28 11:32:12.956] [INFO] 工作流已经处于空闲状态
[2025-09-28 11:32:12.957] [INFO] 开始取消订阅各Manager事件...
[2025-09-28 11:32:12.957] [INFO] Manager事件取消订阅完成
[2025-09-28 11:32:12.957] [INFO] WorkflowManager资源释放完成
[2025-09-28 11:32:12.957] [INFO] 工作流管理器资源释放完成
[2025-09-28 11:32:12.958] [INFO] 释放启动自检管理器资源...
[2025-09-28 11:32:12.958] [INFO] 开始释放启动自检管理器资源...
[2025-09-28 11:32:12.958] [INFO] 启动自检管理器资源释放完成
[2025-09-28 11:32:12.958] [INFO] 启动自检管理器资源释放完成
[2025-09-28 11:32:12.958] [INFO] 释放DMC1000B电机管理器资源...
[2025-09-28 11:32:12.959] [INFO] 开始释放DMC1000B资源...
[2025-09-28 11:32:12.965] [INFO] 停止所有电机部分失败
[2025-09-28 11:32:12.967] [INFO] DMC1000B资源释放完成
[2025-09-28 11:32:12.967] [INFO] DMC1000B电机管理器资源释放完成
[2025-09-28 11:32:12.967] [INFO] 释放DMC1000BIO管理器资源...
[2025-09-28 11:32:12.969] [INFO] DMC1000BIO管理器资源释放完成
[2025-09-28 11:32:12.969] [INFO] 释放其他Manager资源...
[2025-09-28 11:32:12.971] [INFO] 开始释放MotorManager资源...
[2025-09-28 11:32:12.975] [INFO] 电机0停止运动
[2025-09-28 11:32:12.976] [INFO] 电机7停止运动
[2025-09-28 11:32:12.976] [INFO] 电机3停止运动
[2025-09-28 11:32:12.976] [INFO] 电机2停止运动
[2025-09-28 11:32:12.976] [INFO] 电机6停止运动
[2025-09-28 11:32:12.976] [INFO] 电机5停止运动
[2025-09-28 11:32:12.977] [INFO] 电机1停止运动
[2025-09-28 11:32:12.977] [INFO] 电机4停止运动
[2025-09-28 11:32:12.977] [INFO] 所有电机已停止
[2025-09-28 11:32:12.978] [INFO] 电机监控循环被取消
[2025-09-28 11:32:12.978] [INFO] 电机监控循环结束
[2025-09-28 11:32:12.978] [INFO] 电机监控任务已停止
[2025-09-28 11:32:12.978] [INFO] 模拟释放运动控制卡资源
[2025-09-28 11:32:12.978] [INFO] MotorManager资源释放完成
[2025-09-28 11:32:12.979] [INFO] 开始释放ScannerManager资源...
[2025-09-28 11:32:12.980] [INFO] 扫描枪状态变更: Disconnected - 扫描枪连接已断开
[2025-09-28 11:32:12.981] [INFO] ScannerManager资源释放完成
[2025-09-28 11:32:12.982] [INFO] 开始释放ModbusTcpManager资源...
[2025-09-28 11:32:12.984] [INFO] Modbus TCP状态变更: Disconnected - Modbus TCP连接已断开
[2025-09-28 11:32:12.984] [INFO] Modbus TCP连接已断开
[2025-09-28 11:32:12.984] [INFO] ModbusTcpManager资源释放完成
[2025-09-28 11:32:12.985] [INFO] 开始释放EpsonRobotManager资源...
[2025-09-28 11:32:12.989] [INFO] EpsonRobotManager资源释放完成
[2025-09-28 11:32:12.990] [INFO] 开始释放EpsonRobotManager2资源...
[2025-09-28 11:32:12.995] [INFO] EpsonRobotManager2资源释放完成
[2025-09-28 11:32:12.997] [INFO] 开始释放VisionManager资源...
[2025-09-28 11:32:12.999] [INFO] 模拟释放相机资源
[2025-09-28 11:32:12.999] [INFO] VisionManager资源释放完成
[2025-09-28 11:32:13.000] [INFO] 开始释放StatisticsManager资源...
[2025-09-28 11:32:13.002] [INFO] 自动保存任务已停止
[2025-09-28 11:32:13.005] [INFO] StatisticsManager资源释放完成
[2025-09-28 11:32:13.005] [INFO] 其他Manager资源释放完成
[2025-09-28 11:32:13.005] [INFO] 释放DMC1000B控制卡资源...
[2025-09-28 11:32:13.007] [INFO] DMC1000B控制卡未初始化，无需释放
[2025-09-28 11:32:13.007] [INFO] DMC1000B控制卡资源释放完成
[2025-09-28 11:32:13.007] [INFO] 所有Manager资源释放流程完成
[2025-09-28 11:32:13.007] [INFO] 所有资源释放完成，程序即将退出
[2025-09-28 11:32:13.007] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-28 11:33:39.055] [INFO] 程序启动开始
[2025-09-28 11:33:39.055] [INFO] 加载Settings系统配置...
[2025-09-28 11:33:39.060] [INFO] 设置加载成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-28 11:33:39.060] [INFO] Settings系统配置加载完成
[2025-09-28 11:33:39.061] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-28 11:33:39.070] [INFO] 开始初始化各个Manager...
[2025-09-28 11:33:39.071] [INFO] 初始化DMC1000B控制卡...
[2025-09-28 11:33:39.074] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-28 11:33:39.075] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-28 11:33:39.075] [WARN] DMC1000B控制卡初始化失败，相关功能可能无法正常工作
[2025-09-28 11:33:39.075] [INFO] 初始化基础Manager...
[2025-09-28 11:33:39.080] [INFO] IO状态缓存初始化完成
[2025-09-28 11:33:39.082] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-28 11:33:39.082] [WARN] DMC1000BIO管理器初始化失败
[2025-09-28 11:33:39.085] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-28 11:33:39.085] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-28 11:33:39.107] [WARN] DMC1000B电机管理器初始化失败
[2025-09-28 11:33:39.107] [INFO] 初始化系统模式管理器...
[2025-09-28 11:33:39.115] [INFO] 从Settings系统加载安全管理器配置成功
[2025-09-28 11:33:39.117] [INFO] 安全管理器实例已创建
[2025-09-28 11:33:39.120] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-28 11:33:39.120] [INFO] 开始初始化MotorManager...
[2025-09-28 11:33:39.120] [INFO] 恢复上次系统模式: Automatic
[2025-09-28 11:33:39.122] [INFO] 模拟初始化运动控制卡
[2025-09-28 11:33:39.123] [INFO] 开始切换到自动模式...
[2025-09-28 11:33:39.124] [INFO] 开始检查系统是否准备好进入自动模式...
[2025-09-28 11:33:39.126] [INFO] 系统基础检查完成，准备进入自动模式（具体硬件初始化将在开机自检中完成）
[2025-09-28 11:33:39.127] [INFO] 系统模式已从 Manual 切换到 Automatic
[2025-09-28 11:33:39.127] [INFO] 启动安全管理器...
[2025-09-28 11:33:39.129] [ERROR] 安全管理器未初始化，无法启动
[2025-09-28 11:33:39.129] [ERROR] 启动安全管理器失败，无法切换到自动模式
[2025-09-28 11:33:39.129] [ERROR] 恢复上次系统模式失败: Automatic，保持当前模式
[2025-09-28 11:33:39.130] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-28 11:33:39.130] [INFO] 系统模式已保存到配置文件: Automatic
[2025-09-28 11:33:39.333] [INFO] 加载了8个电机的默认配置
[2025-09-28 11:33:39.334] [INFO] 电机监控任务已启动
[2025-09-28 11:33:39.335] [INFO] MotorManager初始化完成
[2025-09-28 11:33:39.335] [INFO] 初始化通信Manager...
[2025-09-28 11:33:39.338] [INFO] 电机监控循环开始
[2025-09-28 11:33:39.340] [INFO] 开始初始化ScannerManager...
[2025-09-28 11:33:39.346] [INFO] 扫描枪配置从Settings系统加载: COM1, 9600, 8, One, None
[2025-09-28 11:33:39.349] [INFO] 串口初始化完成
[2025-09-28 11:33:39.353] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-28 11:33:39.364] [ERROR] 连接扫描枪 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerManager.<ConnectAsync>b__23_1() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 145
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerManager.<<ConnectAsync>b__23_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 139
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-28 11:33:39.367] [WARN] 扫描枪连接失败，但初始化完成，可稍后重试连接
[2025-09-28 11:33:39.368] [INFO] ScannerManager初始化完成
[2025-09-28 11:33:39.377] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-28 11:33:39.378] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-28 11:33:39.378] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-28 11:33:39.380] [INFO] SCARA通信管理器已初始化
[2025-09-28 11:33:39.383] [INFO] ScannerAutoModeManager管理器引用初始化完成
[2025-09-28 11:33:39.386] [INFO] 开始初始化ScannerAutoModeManager...
[2025-09-28 11:33:39.388] [INFO] 开始初始化MultiScannerManager...
[2025-09-28 11:33:39.389] [INFO] 开始初始化扫描枪1...
[2025-09-28 11:33:39.390] [INFO] 从Settings系统加载扫描枪1配置: COM1, 115200
[2025-09-28 11:33:39.391] [INFO] 扫描枪1串口初始化完成
[2025-09-28 11:33:39.391] [INFO] 扫描枪1初始化完成
[2025-09-28 11:33:39.391] [INFO] 开始初始化扫描枪2...
[2025-09-28 11:33:39.392] [INFO] 从Settings系统加载扫描枪2配置: COM2, 115200
[2025-09-28 11:33:39.392] [INFO] 扫描枪2串口初始化完成
[2025-09-28 11:33:39.392] [INFO] 扫描枪2初始化完成
[2025-09-28 11:33:39.392] [INFO] 开始初始化扫描枪3...
[2025-09-28 11:33:39.392] [INFO] 从Settings系统加载扫描枪3配置: COM3, 115200
[2025-09-28 11:33:39.393] [INFO] 扫描枪3串口初始化完成
[2025-09-28 11:33:39.393] [INFO] 扫描枪3初始化完成
[2025-09-28 11:33:39.393] [INFO] MultiScannerManager初始化完成
[2025-09-28 11:33:39.394] [INFO] ScannerAutoModeManager初始化完成
[2025-09-28 11:33:39.394] [INFO] 跳过Modbus TCP管理器初始化 - 由第三方控制
[2025-09-28 11:33:39.397] [INFO] 开始初始化EpsonRobotManager...
[2025-09-28 11:33:39.398] [INFO] 从Settings系统加载Epson机器人1配置成功
[2025-09-28 11:33:39.398] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-28 11:33:39.399] [INFO] EpsonRobotManager初始化完成
[2025-09-28 11:33:39.399] [INFO] 初始化视觉Manager...
[2025-09-28 11:33:39.401] [INFO] 开始初始化VisionManager...
[2025-09-28 11:33:39.402] [INFO] 视觉系统配置从Settings系统加载: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms, 置信度阈值=0.8
[2025-09-28 11:33:39.404] [INFO] 模拟初始化相机，索引: 0
[2025-09-28 11:33:39.919] [INFO] 相机初始化成功
[2025-09-28 11:33:39.924] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-28 11:33:39.924] [INFO] 视觉配置加载完成
[2025-09-28 11:33:39.925] [INFO] VisionManager初始化完成
[2025-09-28 11:33:39.925] [INFO] 初始化数据Manager...
[2025-09-28 11:33:39.941] [INFO] 开始初始化StatisticsManager...
[2025-09-28 11:33:39.944] [INFO] 统计管理器配置: 数据目录=Export, 缓存大小=1000, 自动保存间隔=24分钟
[2025-09-28 11:33:39.968] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-28 11:33:39.969] [INFO] 历史数据加载完成
[2025-09-28 11:33:39.969] [INFO] StatisticsManager初始化完成
[2025-09-28 11:33:39.969] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-28 11:33:39.969] [INFO] 所有Manager初始化完成
[2025-09-28 11:33:40.019] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-28 11:33:40.019] [INFO] 创建并缓存面板: vision-position
[2025-09-28 11:33:40.020] [INFO] 主界面布局创建完成
[2025-09-28 11:33:40.021] [INFO] 时间更新定时器初始化完成
[2025-09-28 11:33:40.021] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-28 11:33:40.101] [INFO] 开始初始化系统
[2025-09-28 11:33:40.102] [INFO] 初始化业务逻辑
[2025-09-28 11:33:40.104] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-28 11:33:40.104] [INFO] IO管理器初始化完成
[2025-09-28 11:33:40.104] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-28 11:33:40.104] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-28 11:33:40.104] [INFO] 电机管理器初始化完成
[2025-09-28 11:33:40.105] [INFO] IO事件订阅完成
[2025-09-28 11:33:40.105] [INFO] 电机事件订阅完成
[2025-09-28 11:33:40.105] [INFO] 业务层交互机制建立完成
[2025-09-28 11:33:40.105] [INFO] 业务逻辑初始化完成
[2025-09-28 11:33:40.106] [INFO] 执行UI界面刷新
[2025-09-28 11:33:40.109] [INFO] UI界面刷新完成
[2025-09-28 11:33:40.109] [INFO] 系统初始化完成
[2025-09-28 11:34:00.026] [INFO] 开始切换到自动模式...
[2025-09-28 11:34:00.026] [INFO] 开始检查系统是否准备好进入自动模式...
[2025-09-28 11:34:00.027] [INFO] 系统基础检查完成，准备进入自动模式（具体硬件初始化将在开机自检中完成）
[2025-09-28 11:34:00.027] [INFO] 系统模式已从 Manual 切换到 Automatic
[2025-09-28 11:34:00.030] [INFO] 启动安全管理器...
[2025-09-28 11:34:00.030] [ERROR] 安全管理器未初始化，无法启动
[2025-09-28 11:34:00.031] [ERROR] 启动安全管理器失败，无法切换到自动模式
[2025-09-28 11:34:00.031] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-28 11:34:00.031] [INFO] 系统模式已保存到配置文件: Automatic
[2025-09-28 11:34:08.766] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-28 11:34:08.767] [INFO] 保存Settings系统配置...
[2025-09-28 11:34:08.768] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-28 11:34:08.768] [INFO] Settings系统配置保存完成
[2025-09-28 11:34:08.772] [INFO] 开始按顺序释放各个Manager资源...
[2025-09-28 11:34:08.773] [INFO] 清理UI资源和面板缓存...
[2025-09-28 11:34:08.773] [INFO] 时间更新定时器资源已释放
[2025-09-28 11:34:08.774] [INFO] 开始清理面板缓存，共1个面板
[2025-09-28 11:34:08.781] [INFO] 面板缓存字典已清空
[2025-09-28 11:34:08.781] [INFO] 面板缓存清理完成
[2025-09-28 11:34:08.781] [INFO] UI资源清理完成
[2025-09-28 11:34:08.781] [INFO] 释放工作流管理器资源...
[2025-09-28 11:34:08.783] [INFO] 开始释放WorkflowManager资源...
[2025-09-28 11:34:08.784] [INFO] 工作流已经处于空闲状态
[2025-09-28 11:34:08.785] [INFO] 开始取消订阅各Manager事件...
[2025-09-28 11:34:08.785] [INFO] Manager事件取消订阅完成
[2025-09-28 11:34:08.785] [INFO] WorkflowManager资源释放完成
[2025-09-28 11:34:08.785] [INFO] 工作流管理器资源释放完成
[2025-09-28 11:34:08.785] [INFO] 释放启动自检管理器资源...
[2025-09-28 11:34:08.786] [INFO] 开始释放启动自检管理器资源...
[2025-09-28 11:34:08.786] [INFO] 启动自检管理器资源释放完成
[2025-09-28 11:34:08.786] [INFO] 启动自检管理器资源释放完成
[2025-09-28 11:34:08.786] [INFO] 释放DMC1000B电机管理器资源...
[2025-09-28 11:34:08.788] [INFO] 开始释放DMC1000B资源...
[2025-09-28 11:34:08.794] [INFO] 停止所有电机部分失败
[2025-09-28 11:34:08.794] [INFO] DMC1000B资源释放完成
[2025-09-28 11:34:08.795] [INFO] DMC1000B电机管理器资源释放完成
[2025-09-28 11:34:08.795] [INFO] 释放DMC1000BIO管理器资源...
[2025-09-28 11:34:08.796] [INFO] DMC1000BIO管理器资源释放完成
[2025-09-28 11:34:08.796] [INFO] 释放其他Manager资源...
[2025-09-28 11:34:08.798] [INFO] 开始释放MotorManager资源...
[2025-09-28 11:34:08.801] [INFO] 电机0停止运动
[2025-09-28 11:34:08.801] [INFO] 电机6停止运动
[2025-09-28 11:34:08.801] [INFO] 电机7停止运动
[2025-09-28 11:34:08.801] [INFO] 电机1停止运动
[2025-09-28 11:34:08.802] [INFO] 电机5停止运动
[2025-09-28 11:34:08.802] [INFO] 电机2停止运动
[2025-09-28 11:34:08.803] [INFO] 电机4停止运动
[2025-09-28 11:34:08.803] [INFO] 电机3停止运动
[2025-09-28 11:34:08.803] [INFO] 所有电机已停止
[2025-09-28 11:34:08.804] [INFO] 电机监控循环被取消
[2025-09-28 11:34:08.804] [INFO] 电机监控循环结束
[2025-09-28 11:34:08.804] [INFO] 电机监控任务已停止
[2025-09-28 11:34:08.804] [INFO] 模拟释放运动控制卡资源
[2025-09-28 11:34:08.804] [INFO] MotorManager资源释放完成
[2025-09-28 11:34:08.806] [INFO] 开始释放ScannerManager资源...
[2025-09-28 11:34:08.807] [INFO] 扫描枪状态变更: Disconnected - 扫描枪连接已断开
[2025-09-28 11:34:08.807] [INFO] ScannerManager资源释放完成
[2025-09-28 11:34:08.808] [INFO] 开始释放EpsonRobotManager资源...
[2025-09-28 11:34:08.812] [INFO] EpsonRobotManager资源释放完成
[2025-09-28 11:34:08.813] [INFO] 开始释放EpsonRobotManager2资源...
[2025-09-28 11:34:08.817] [INFO] EpsonRobotManager2资源释放完成
[2025-09-28 11:34:08.819] [INFO] 开始释放VisionManager资源...
[2025-09-28 11:34:08.820] [INFO] 模拟释放相机资源
[2025-09-28 11:34:08.821] [INFO] VisionManager资源释放完成
[2025-09-28 11:34:08.822] [INFO] 开始释放StatisticsManager资源...
[2025-09-28 11:34:08.822] [INFO] 自动保存任务已停止
[2025-09-28 11:34:08.825] [INFO] StatisticsManager资源释放完成
[2025-09-28 11:34:08.825] [INFO] 其他Manager资源释放完成
[2025-09-28 11:34:08.825] [INFO] 释放DMC1000B控制卡资源...
[2025-09-28 11:34:08.827] [INFO] DMC1000B控制卡未初始化，无需释放
[2025-09-28 11:34:08.827] [INFO] DMC1000B控制卡资源释放完成
[2025-09-28 11:34:08.827] [INFO] 所有Manager资源释放流程完成
[2025-09-28 11:34:08.827] [INFO] 所有资源释放完成，程序即将退出
[2025-09-28 11:34:08.828] [INFO] 程序正在关闭，开始释放所有Manager资源...
