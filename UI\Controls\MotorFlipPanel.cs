using System;
using System.Drawing;
using System.Windows.Forms;
using System.Threading.Tasks;
using MyHMI.Helpers;
using MyHMI.Managers;
using MyHMI.Models;
using MyHMI.Events;
using MyHMI.Settings;

namespace MyHMI.UI.Controls
{
    /// <summary>
    /// 翻转电机参数面板 - 专门用于电机参数设置
    /// </summary>
    public partial class MotorFlipPanel : UserControl
    {
        #region 私有字段

        private Panel _mainPanel;
        private Label _titleLabel;
        private Panel _motorGroup;
        private Panel _leftMotorPanel;
        private Panel _rightMotorPanel;

        // 业务逻辑相关
        private DMC1000BMotorManager _motorManager;
        private const short LEFT_FLIP_AXIS = 0;
        private const short RIGHT_FLIP_AXIS = 1;

        // 左翻转电机控件
        private TextBox _leftPulseEquivalentTextBox;
        private TextBox _leftMaxAccelerationTextBox;
        private TextBox _leftMaxSpeedTextBox;
        private TextBox _leftAccelerationTimeTextBox;
        private TextBox _leftStartSpeedTextBox;
        private CheckBox _leftHomeDirectionCheckBox;

        // 右翻转电机控件
        private TextBox _rightPulseEquivalentTextBox;
        private TextBox _rightMaxAccelerationTextBox;
        private TextBox _rightMaxSpeedTextBox;
        private TextBox _rightAccelerationTimeTextBox;
        private TextBox _rightStartSpeedTextBox;
        private CheckBox _rightHomeDirectionCheckBox;



        #endregion

        #region 构造函数

        public MotorFlipPanel()
        {
            InitializeComponent();
            InitializeInterface();
            InitializeBusinessLogic();
        }

        #endregion

        #region 界面初始化

        /// <summary>
        /// 初始化界面 - 按HTML原型精确设计
        /// </summary>
        private void InitializeInterface()
        {
            try
            {
                // 设置面板属性 - 按HTML原型 660x840 (700-40padding)
                this.Size = new Size(660, 840);
                this.BackColor = ColorTranslator.FromHtml("#34495e");
                this.Dock = DockStyle.Fill;
                this.Padding = new Padding(0);

                // 创建主面板 - 按HTML原型padding: 20px
                CreateMainPanel();

                // 创建标题 - 按HTML原型样式
                CreateTitle();

                // 创建电机组 - 按HTML原型样式
                CreateMotorGroup();

                LogHelper.Info("翻转电机控制面板初始化完成 - 按HTML原型设计");
            }
            catch (Exception ex)
            {
                LogHelper.Error("翻转电机控制面板初始化失败", ex);
            }
        }

        /// <summary>
        /// 创建主面板 - 按HTML原型
        /// </summary>
        private void CreateMainPanel()
        {
            _mainPanel = new Panel
            {
                Size = new Size(660, 840),
                Location = new Point(0, 0),
                BackColor = ColorTranslator.FromHtml("#34495e"),
                Padding = new Padding(20), // 按HTML原型 padding: 20px
                Dock = DockStyle.Fill
            };

            this.Controls.Add(_mainPanel);
        }

        /// <summary>
        /// 创建标题 - 按HTML原型样式
        /// </summary>
        private void CreateTitle()
        {
            _titleLabel = new Label
            {
                Text = "翻转电机参数",
                Font = new Font("微软雅黑", 20F, FontStyle.Bold), // 按HTML原型 font-size: 20px
                ForeColor = ColorTranslator.FromHtml("#3498db"),   // 按HTML原型 color: #3498db
                Size = new Size(300, 30),
                Location = new Point(0, 0),
                AutoSize = true
            };

            _mainPanel.Controls.Add(_titleLabel);
        }

        /// <summary>
        /// 创建电机组 - 按HTML原型样式
        /// </summary>
        private void CreateMotorGroup()
        {
            _motorGroup = new Panel
            {
                Size = new Size(600, 530), // 调整尺寸：250+15+250+15=530px
                Location = new Point(0, 50), // 标题下方20px间距
                BackColor = ColorTranslator.FromHtml("#2c3e50"), // 按HTML原型
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15) // 按HTML原型 gap: 15px
            };

            // 设置边框颜色 - 按HTML原型 border: 1px solid #4a5661
            _motorGroup.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _motorGroup.Width - 1, _motorGroup.Height - 1);
                }
            };

            // 创建左电机面板
            CreateLeftMotorPanel();

            // 创建右电机面板
            CreateRightMotorPanel();

            _mainPanel.Controls.Add(_motorGroup);
        }

        /// <summary>
        /// 创建左电机面板 - 按HTML原型样式
        /// </summary>
        private void CreateLeftMotorPanel()
        {
            _leftMotorPanel = new Panel
            {
                Size = new Size(570, 250), // 增加高度以容纳回零方向控件
                Location = new Point(0, 0),
                BackColor = ColorTranslator.FromHtml("#34495e"), // 按HTML原型
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(12) // 按HTML原型 padding: 12px
            };

            // 设置边框颜色 - 按HTML原型 border: 1px solid #4a5661
            _leftMotorPanel.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _leftMotorPanel.Width - 1, _leftMotorPanel.Height - 1);
                }
            };

            // 创建标题
            var titleLabel = new Label
            {
                Text = "左翻转电机",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold), // 按HTML原型 h4
                ForeColor = Color.White,
                Size = new Size(200, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };

            // 创建参数区域
            CreateMotorParams(_leftMotorPanel, 30);

            _leftMotorPanel.Controls.Add(titleLabel);
            _motorGroup.Controls.Add(_leftMotorPanel);
        }

        /// <summary>
        /// 创建右电机面板 - 按HTML原型样式
        /// </summary>
        private void CreateRightMotorPanel()
        {
            _rightMotorPanel = new Panel
            {
                Size = new Size(570, 250), // 增加高度以容纳回零方向控件
                Location = new Point(0, 265), // 调整位置：左电机下方15px间距
                BackColor = ColorTranslator.FromHtml("#34495e"), // 按HTML原型
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(12) // 按HTML原型 padding: 12px
            };

            // 设置边框颜色 - 按HTML原型 border: 1px solid #4a5661
            _rightMotorPanel.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _rightMotorPanel.Width - 1, _rightMotorPanel.Height - 1);
                }
            };

            // 创建标题
            var titleLabel = new Label
            {
                Text = "右翻转电机",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold), // 按HTML原型 h4
                ForeColor = Color.White,
                Size = new Size(200, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };

            // 创建参数区域
            CreateMotorParams(_rightMotorPanel, 30);

            _rightMotorPanel.Controls.Add(titleLabel);
            _motorGroup.Controls.Add(_rightMotorPanel);
        }

        /// <summary>
        /// 创建电机参数区域 - 按HTML原型样式，增加角度位置显示和回零方向选择
        /// </summary>
        private void CreateMotorParams(Panel parentPanel, int yOffset)
        {
            // 创建参数面板 - 按HTML原型 motor-params
            var paramsPanel = new Panel
            {
                Size = new Size(546, 200), // 增加高度以容纳回零方向选择
                Location = new Point(0, yOffset),
                BackColor = Color.Transparent
            };

            // 创建两列参数布局 - 按HTML原型 display: flex, gap: 15px
            var leftColumn = new Panel
            {
                Size = new Size(265, 200),
                Location = new Point(0, 0),
                BackColor = Color.Transparent
            };

            var rightColumn = new Panel
            {
                Size = new Size(265, 200),
                Location = new Point(281, 0), // 265 + 15px gap
                BackColor = Color.Transparent
            };

            // 根据父面板确定是左电机还是右电机
            bool isLeftMotor = parentPanel == _leftMotorPanel;

            // 左列参数
            var pulseEquivalentTextBox = CreateParamGroup(leftColumn, "脉冲当量:", "0.012", "°/pulse", 0);
            var maxAccelerationTextBox = CreateParamGroup(leftColumn, "最大加速度:", "120", "°/s²", 40);
            var accelerationTimeTextBox = CreateParamGroup(leftColumn, "加速时间:", "0.1", "s", 80);
            var startSpeedTextBox = CreateParamGroup(leftColumn, "起始速度:", "5", "°/s", 120);

            // 右列参数
            var maxSpeedTextBox = CreateParamGroup(rightColumn, "最大速度:", "60", "°/s", 0);

            // 添加回零方向选择控件
            var homeDirectionCheckBox = CreateHomeDirectionControl(rightColumn, 40, isLeftMotor);

            // 保存控件引用
            if (isLeftMotor)
            {
                _leftPulseEquivalentTextBox = pulseEquivalentTextBox;
                _leftMaxAccelerationTextBox = maxAccelerationTextBox;
                _leftAccelerationTimeTextBox = accelerationTimeTextBox;
                _leftStartSpeedTextBox = startSpeedTextBox;
                _leftMaxSpeedTextBox = maxSpeedTextBox;
                _leftHomeDirectionCheckBox = homeDirectionCheckBox;

                // 添加参数变化事件处理
                _leftPulseEquivalentTextBox.TextChanged += (s, e) => OnLeftParameterChanged();
                _leftMaxAccelerationTextBox.TextChanged += (s, e) => OnLeftParameterChanged();
                _leftAccelerationTimeTextBox.TextChanged += (s, e) => OnLeftParameterChanged();
                _leftStartSpeedTextBox.TextChanged += (s, e) => OnLeftParameterChanged();
                _leftMaxSpeedTextBox.TextChanged += (s, e) => OnLeftParameterChanged();
            }
            else
            {
                _rightPulseEquivalentTextBox = pulseEquivalentTextBox;
                _rightMaxAccelerationTextBox = maxAccelerationTextBox;
                _rightAccelerationTimeTextBox = accelerationTimeTextBox;
                _rightStartSpeedTextBox = startSpeedTextBox;
                _rightMaxSpeedTextBox = maxSpeedTextBox;
                _rightHomeDirectionCheckBox = homeDirectionCheckBox;

                // 添加参数变化事件处理
                _rightPulseEquivalentTextBox.TextChanged += (s, e) => OnRightParameterChanged();
                _rightMaxAccelerationTextBox.TextChanged += (s, e) => OnRightParameterChanged();
                _rightAccelerationTimeTextBox.TextChanged += (s, e) => OnRightParameterChanged();
                _rightStartSpeedTextBox.TextChanged += (s, e) => OnRightParameterChanged();
                _rightMaxSpeedTextBox.TextChanged += (s, e) => OnRightParameterChanged();
            }

            paramsPanel.Controls.Add(leftColumn);
            paramsPanel.Controls.Add(rightColumn);
            parentPanel.Controls.Add(paramsPanel);
        }

        /// <summary>
        /// 创建参数组 - 按HTML原型样式
        /// </summary>
        private TextBox CreateParamGroup(Panel parent, string labelText, string defaultValue, string unit, int yOffset)
        {
            // 标签 - 按HTML原型 width: 80px, font-size: 12px
            var label = new Label
            {
                Text = labelText,
                Font = new Font("微软雅黑", 12F), // 按HTML原型
                ForeColor = Color.White,
                Size = new Size(80, 25),
                Location = new Point(0, yOffset + 5),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // 输入框 - 按HTML原型 width: 80px, height: 25px, font-size: 12px
            var textBox = new TextBox
            {
                Text = defaultValue,
                Font = new Font("微软雅黑", 12F), // 按HTML原型
                Size = new Size(80, 25), // 按HTML原型
                Location = new Point(85, yOffset),
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // 单位标签
            var unitLabel = new Label
            {
                Text = unit,
                Font = new Font("微软雅黑", 10F),
                ForeColor = ColorTranslator.FromHtml("#95a5a6"),
                Size = new Size(80, 25),
                Location = new Point(170, yOffset + 5),
                TextAlign = ContentAlignment.MiddleLeft
            };

            parent.Controls.Add(label);
            parent.Controls.Add(textBox);
            parent.Controls.Add(unitLabel);

            return textBox; // 返回TextBox引用
        }

        /// <summary>
        /// 创建回零方向选择控件
        /// </summary>
        private CheckBox CreateHomeDirectionControl(Panel parent, int yOffset, bool isLeftMotor)
        {
            // 标签
            var label = new Label
            {
                Text = "回零方向:",
                Font = new Font("微软雅黑", 12F),
                ForeColor = Color.White,
                Size = new Size(80, 25),
                Location = new Point(0, yOffset + 5),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // 复选框
            var checkBox = new CheckBox
            {
                Text = "正方向",
                Font = new Font("微软雅黑", 10F),
                ForeColor = Color.White,
                Size = new Size(80, 25),
                Location = new Point(85, yOffset),
                BackColor = Color.Transparent,
                UseVisualStyleBackColor = false,
                Checked = false // 默认负方向回零
            };

            // 添加事件处理
            if (isLeftMotor)
            {
                checkBox.CheckedChanged += async (s, e) => await OnLeftHomeDirectionChangedAsync(checkBox.Checked);
            }
            else
            {
                checkBox.CheckedChanged += async (s, e) => await OnRightHomeDirectionChangedAsync(checkBox.Checked);
            }

            // 提示标签
            var hintLabel = new Label
            {
                Text = "⚠️ 请先测试确定正方向",
                Font = new Font("微软雅黑", 8F),
                ForeColor = ColorTranslator.FromHtml("#e74c3c"),
                Size = new Size(150, 20),
                Location = new Point(0, yOffset + 30),
                TextAlign = ContentAlignment.MiddleLeft
            };

            parent.Controls.Add(label);
            parent.Controls.Add(checkBox);
            parent.Controls.Add(hintLabel);

            return checkBox;
        }






        #endregion

        #region 业务逻辑集成

        /// <summary>
        /// 初始化业务逻辑
        /// </summary>
        private void InitializeBusinessLogic()
        {
            try
            {
                // 获取电机管理器实例
                _motorManager = DMC1000BMotorManager.Instance;

                // 检查电机管理器是否已初始化
                if (!_motorManager.IsInitialized)
                {
                    LogHelper.Warning("DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作");
                }

                // 订阅电机状态变化事件
                _motorManager.MotorPositionChanged += OnMotorPositionChanged;
                _motorManager.MotorStatusChanged += OnMotorStatusChanged;

                // 初始化电机参数
                _ = InitializeMotorParametersAsync(); // 异步初始化，不等待

                LogHelper.Info("翻转电机控制面板业务逻辑初始化完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("翻转电机控制面板业务逻辑初始化失败", ex);
            }
        }

        /// <summary>
        /// 初始化电机参数 - 只更新UI显示，不覆盖已保存的参数
        /// </summary>
        private async Task InitializeMotorParametersAsync()
        {
            try
            {
                // 不再设置硬编码参数，让电机管理器从配置文件加载参数
                // 如果配置文件中没有参数，电机管理器会自动使用默认参数

                // 更新UI显示的参数值
                UpdateUIParameters();

                LogHelper.Info("翻转电机参数初始化完成 - 已配置传感器IO和安全限制");
            }
            catch (Exception ex)
            {
                LogHelper.Error("翻转电机参数初始化失败", ex);
            }
        }

        /// <summary>
        /// 更新UI参数显示 - 从Settings系统获取值
        /// </summary>
        private async void UpdateUIParameters()
        {
            try
            {
                _isUpdatingUI = true; // 防止触发参数变化事件

                // 从Settings系统获取电机参数
                var motorSettings = Settings.Settings.Current.Motor;

                // 更新左翻转电机参数显示
                if (_leftPulseEquivalentTextBox != null)
                    _leftPulseEquivalentTextBox.Text = motorSettings.LeftFlipPulseEquivalent.ToString("F3");
                if (_leftMaxAccelerationTextBox != null)
                    _leftMaxAccelerationTextBox.Text = motorSettings.LeftFlipAcceleration.ToString("F0");
                if (_leftMaxSpeedTextBox != null)
                    _leftMaxSpeedTextBox.Text = motorSettings.LeftFlipMaxSpeed.ToString("F0");
                if (_leftAccelerationTimeTextBox != null)
                    _leftAccelerationTimeTextBox.Text = motorSettings.LeftFlipAccelerationTime.ToString("F1");
                if (_leftStartSpeedTextBox != null)
                    _leftStartSpeedTextBox.Text = motorSettings.LeftFlipStartSpeed.ToString("F0");
                if (_leftHomeDirectionCheckBox != null)
                    _leftHomeDirectionCheckBox.Checked = motorSettings.LeftFlipHomeDirection;

                // 更新右翻转电机参数显示
                if (_rightPulseEquivalentTextBox != null)
                    _rightPulseEquivalentTextBox.Text = motorSettings.RightFlipPulseEquivalent.ToString("F3");
                if (_rightMaxAccelerationTextBox != null)
                    _rightMaxAccelerationTextBox.Text = motorSettings.RightFlipAcceleration.ToString("F0");
                if (_rightMaxSpeedTextBox != null)
                    _rightMaxSpeedTextBox.Text = motorSettings.RightFlipMaxSpeed.ToString("F0");
                if (_rightAccelerationTimeTextBox != null)
                    _rightAccelerationTimeTextBox.Text = motorSettings.RightFlipAccelerationTime.ToString("F1");
                if (_rightStartSpeedTextBox != null)
                    _rightStartSpeedTextBox.Text = motorSettings.RightFlipStartSpeed.ToString("F0");
                if (_rightHomeDirectionCheckBox != null)
                    _rightHomeDirectionCheckBox.Checked = motorSettings.RightFlipHomeDirection;

                LogHelper.Info("UI参数显示已从Settings系统更新");
            }
            catch (Exception ex)
            {
                LogHelper.Error("更新UI参数显示失败", ex);
            }
            finally
            {
                _isUpdatingUI = false; // 恢复事件处理
            }
        }

        /// <summary>
        /// 电机位置变化事件处理
        /// </summary>
        private void OnMotorPositionChanged(object sender, MotorPositionEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnMotorPositionChanged(sender, e)));
                return;
            }

            try
            {
                // 当前角度显示已迁移到示教界面，这里不再处理
                // 保留事件处理框架以备将来扩展
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理电机位置变化事件失败", ex);
            }
        }

        /// <summary>
        /// 电机状态变化事件处理
        /// </summary>
        private void OnMotorStatusChanged(object sender, MotorStatusEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnMotorStatusChanged(sender, e)));
                return;
            }

            try
            {
                // 电机状态更新逻辑已迁移到翻转电机示教页面
            }
            catch (Exception ex)
            {
                LogHelper.Error("更新电机状态显示失败", ex);
            }
        }



        /// <summary>
        /// 释放资源，取消事件订阅
        /// </summary>
        private void CleanupResources()
        {
            try
            {
                // 取消事件订阅
                if (_motorManager != null)
                {
                    _motorManager.MotorPositionChanged -= OnMotorPositionChanged;
                    _motorManager.MotorStatusChanged -= OnMotorStatusChanged;
                }
                LogHelper.Info("翻转电机控制面板资源释放完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("翻转电机控制面板资源释放失败", ex);
            }
        }

        #endregion

        #region 方向切换事件处理

        /// <summary>
        /// 左翻转电机回零方向改变事件处理
        /// </summary>
        private async Task OnLeftHomeDirectionChangedAsync(bool isPositiveDirection)
        {
            try
            {
                // 直接更新Settings系统中的回零方向
                var motorSettings = Settings.Settings.Current.Motor;
                motorSettings.LeftFlipHomeDirection = isPositiveDirection;

                // 保存参数到文件
                Settings.Settings.Save();

                // 创建完整的电机参数对象并更新到电机管理器
                var leftParams = new FlipMotorParams
                {
                    MotorName = "左翻转电机",
                    PulseEquivalent = motorSettings.LeftFlipPulseEquivalent,
                    MaxSpeed = motorSettings.LeftFlipMaxSpeed,
                    StartSpeed = motorSettings.LeftFlipStartSpeed,
                    Acceleration = motorSettings.LeftFlipAcceleration,
                    AccelerationTime = motorSettings.LeftFlipAccelerationTime,
                    HomeSpeed = motorSettings.LeftFlipHomeSpeed,
                    HomeDirection = isPositiveDirection,
                    HomeIO = motorSettings.LeftFlipHomeIO,
                    PositiveLimitIO = motorSettings.LeftFlipPositiveLimitIO,
                    NegativeLimitIO = motorSettings.LeftFlipNegativeLimitIO,
                    HomeTimeout = motorSettings.LeftFlipHomeTimeout
                };

                await _motorManager.SetFlipMotorParamsAsync(LEFT_FLIP_AXIS, leftParams);
                LogHelper.Info($"左翻转电机回零方向已设置并保存为: {(isPositiveDirection ? "正方向" : "负方向")}");
            }
            catch (Exception ex)
            {
                LogHelper.Error("左翻转电机回零方向设置失败", ex);
            }
        }

        /// <summary>
        /// 右翻转电机回零方向改变事件处理
        /// </summary>
        private async Task OnRightHomeDirectionChangedAsync(bool isPositiveDirection)
        {
            try
            {
                // 直接更新Settings系统中的回零方向
                var motorSettings = Settings.Settings.Current.Motor;
                motorSettings.RightFlipHomeDirection = isPositiveDirection;

                // 保存参数到文件
                Settings.Settings.Save();

                // 创建完整的电机参数对象并更新到电机管理器
                var rightParams = new FlipMotorParams
                {
                    MotorName = "右翻转电机",
                    PulseEquivalent = motorSettings.RightFlipPulseEquivalent,
                    MaxSpeed = motorSettings.RightFlipMaxSpeed,
                    StartSpeed = motorSettings.RightFlipStartSpeed,
                    Acceleration = motorSettings.RightFlipAcceleration,
                    AccelerationTime = motorSettings.RightFlipAccelerationTime,
                    HomeSpeed = motorSettings.RightFlipHomeSpeed,
                    HomeDirection = isPositiveDirection,
                    HomeIO = motorSettings.RightFlipHomeIO,
                    PositiveLimitIO = motorSettings.RightFlipPositiveLimitIO,
                    NegativeLimitIO = motorSettings.RightFlipNegativeLimitIO,
                    HomeTimeout = motorSettings.RightFlipHomeTimeout
                };

                await _motorManager.SetFlipMotorParamsAsync(RIGHT_FLIP_AXIS, rightParams);
                LogHelper.Info($"右翻转电机回零方向已设置并保存为: {(isPositiveDirection ? "正方向" : "负方向")}");
            }
            catch (Exception ex)
            {
                LogHelper.Error("右翻转电机回零方向设置失败", ex);
            }
        }

        #endregion

        #region 参数动态更新

        /// <summary>
        /// 左翻转电机参数变化处理
        /// </summary>
        private async void OnLeftParameterChanged()
        {
            try
            {
                // 防止在UI更新过程中触发事件
                if (_isUpdatingUI) return;

                // 从UI获取参数值
                if (!double.TryParse(_leftPulseEquivalentTextBox?.Text, out double pulseEquivalent)) pulseEquivalent = 0.012;
                if (!double.TryParse(_leftMaxAccelerationTextBox?.Text, out double maxAcceleration)) maxAcceleration = 120;
                if (!double.TryParse(_leftAccelerationTimeTextBox?.Text, out double accelerationTime)) accelerationTime = 0.1;
                if (!double.TryParse(_leftStartSpeedTextBox?.Text, out double startSpeed)) startSpeed = 5;
                if (!double.TryParse(_leftMaxSpeedTextBox?.Text, out double maxSpeed)) maxSpeed = 60;

                // 获取当前的其他参数（保持不变）
                var motorSettings = Settings.Settings.Current.Motor;

                // 创建完整的电机参数对象
                var leftParams = new FlipMotorParams
                {
                    MotorName = "左翻转电机",
                    PulseEquivalent = pulseEquivalent,
                    MaxSpeed = maxSpeed,
                    StartSpeed = startSpeed,
                    Acceleration = maxAcceleration,
                    AccelerationTime = accelerationTime,
                    HomeSpeed = motorSettings.LeftFlipHomeSpeed,
                    HomeDirection = _leftHomeDirectionCheckBox?.Checked ?? false,
                    HomeIO = motorSettings.LeftFlipHomeIO,
                    PositiveLimitIO = motorSettings.LeftFlipPositiveLimitIO,
                    NegativeLimitIO = motorSettings.LeftFlipNegativeLimitIO,
                    HomeTimeout = motorSettings.LeftFlipHomeTimeout
                };

                // 使用全局参数管理器更新参数（自动保存到Settings并通知所有使用者）
                await GlobalMotorParameterManager.UpdateLeftFlipMotorParamsAsync(leftParams, "UI参数变化");
                LogHelper.Info($"左翻转电机参数已更新并保存: 脉冲当量={pulseEquivalent}, 起始速度={startSpeed}, 最大速度={maxSpeed}, 最大加速度={maxAcceleration}, 加速时间={accelerationTime}");
            }
            catch (Exception ex)
            {
                LogHelper.Error($"更新左翻转电机参数失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 右翻转电机参数变化处理
        /// </summary>
        private async void OnRightParameterChanged()
        {
            try
            {
                // 防止在UI更新过程中触发事件
                if (_isUpdatingUI) return;

                // 从UI获取参数值
                if (!double.TryParse(_rightPulseEquivalentTextBox?.Text, out double pulseEquivalent)) pulseEquivalent = 0.012;
                if (!double.TryParse(_rightMaxAccelerationTextBox?.Text, out double maxAcceleration)) maxAcceleration = 120;
                if (!double.TryParse(_rightAccelerationTimeTextBox?.Text, out double accelerationTime)) accelerationTime = 0.1;
                if (!double.TryParse(_rightStartSpeedTextBox?.Text, out double startSpeed)) startSpeed = 5;
                if (!double.TryParse(_rightMaxSpeedTextBox?.Text, out double maxSpeed)) maxSpeed = 60;

                // 获取当前的其他参数（保持不变）
                var motorSettings = Settings.Settings.Current.Motor;

                // 创建完整的电机参数对象
                var rightParams = new FlipMotorParams
                {
                    MotorName = "右翻转电机",
                    PulseEquivalent = pulseEquivalent,
                    MaxSpeed = maxSpeed,
                    StartSpeed = startSpeed,
                    Acceleration = maxAcceleration,
                    AccelerationTime = accelerationTime,
                    HomeSpeed = motorSettings.RightFlipHomeSpeed,
                    HomeDirection = _rightHomeDirectionCheckBox?.Checked ?? false,
                    HomeIO = motorSettings.RightFlipHomeIO,
                    PositiveLimitIO = motorSettings.RightFlipPositiveLimitIO,
                    NegativeLimitIO = motorSettings.RightFlipNegativeLimitIO,
                    HomeTimeout = motorSettings.RightFlipHomeTimeout
                };

                // 使用全局参数管理器更新参数（自动保存到Settings并通知所有使用者）
                await GlobalMotorParameterManager.UpdateRightFlipMotorParamsAsync(rightParams, "UI参数变化");
                LogHelper.Info($"右翻转电机参数已更新并保存: 脉冲当量={pulseEquivalent}, 起始速度={startSpeed}, 最大速度={maxSpeed}, 最大加速度={maxAcceleration}, 加速时间={accelerationTime}");
            }
            catch (Exception ex)
            {
                LogHelper.Error($"更新右翻转电机参数失败: {ex.Message}");
            }
        }

        // 添加UI更新标志，防止循环触发
        private bool _isUpdatingUI = false;

        #endregion
    }
}
