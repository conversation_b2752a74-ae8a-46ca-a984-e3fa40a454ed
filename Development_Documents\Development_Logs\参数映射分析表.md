# 参数管理系统映射分析表

## 概述
本文档详细分析现有参数管理系统中的所有参数，为迁移到新的序列化参数管理系统提供映射关系。

## 现有参数管理系统分析

### 1. SystemConfiguration.Instance 使用分析

#### 主要使用位置：
1. **EpsonRobotManager.cs** - 机器人1配置管理
2. **EpsonRobotManager2.cs** - 机器人2配置管理  
3. **MultiScannerManager.cs** - 多扫描器配置管理
4. **SystemModeManager.cs** - 系统模式保存
5. **DMC1000BIOManager.cs** - IO输出状态管理
6. **SystemTests.cs** - 配置系统测试

#### 参数访问模式：
```csharp
// 读取参数
var systemConfig = SystemConfiguration.Instance.Config;
var robotConfig = systemConfig.Communication.EpsonRobot1;

// 保存参数
systemConfig.System.LastSystemMode = currentModeString;
SystemConfiguration.Instance.SaveConfiguration();
```

### 2. ConfigHelper 使用分析

#### 主要使用位置：
1. **StatisticsManager.cs** - 统计数据配置
2. **VisionManager.cs** - 视觉系统配置
3. **ScannerManager.cs** - 扫描器配置
4. **EpsonRobotManager.cs** - 机器人默认配置
5. **EpsonRobotManager2.cs** - 机器人2默认配置

#### 参数访问模式：
```csharp
// 从App.config读取参数
_cameraIndex = ConfigHelper.GetAppSettingInt("VisionCameraIndex", 0);
_portName = ConfigHelper.GetAppSetting("ScannerComPort", "COM1");
_autoSave = ConfigHelper.GetAppSettingBool("StatisticsEnableAutoSave", true);
```

### 3. Properties.Settings 使用分析
- **当前状态**: 基本未使用，只有空的Settings.Designer.cs和Settings.settings文件
- **潜在用途**: 可用于ApplicationSettingsBase实现，但项目中未实际使用

## 参数映射表

### SystemConfig.json → Settings.Current 映射

| 原路径 | 新路径 | 参数类型 | 默认值 | 使用位置 |
|--------|--------|----------|--------|----------|
| **System 系统参数** |
| System.Name | System.Name | string | "上位机控制系统" | SystemTests |
| System.Version | System.Version | string | "1.0.0" | - |
| System.LogLevel | System.LogLevel | string | "Info" | - |
| System.MaxLogFiles | System.MaxLogFiles | int | 30 | - |
| System.AutoStartWorkflow | System.AutoStartWorkflow | bool | false | - |
| System.LastSystemMode | System.LastSystemMode | string | "Manual" | SystemModeManager |
| **IO 参数** |
| IO.CardType | IO.CardType | string | "DMC1000" | - |
| IO.InputChannels | IO.InputChannels | int | 16 | - |
| IO.OutputChannels | IO.OutputChannels | int | 16 | - |
| IO.MonitorInterval | IO.MonitorInterval | int | 100 | - |
| IO.DebounceTime | IO.DebounceTime | int | 50 | - |
| IO.OutputStates | *复杂对象* | Dictionary | {} | DMC1000BIOManager |
| **Motor 电机参数** |
| Motor.AxisCount | Motor.AxisCount | int | 4 | - |
| Motor.DefaultSpeed | Motor.DefaultSpeed | double | 1000 | - |
| Motor.DefaultAcceleration | Motor.DefaultAcceleration | double | 5000 | - |
| Motor.HomeSpeed | Motor.HomeSpeed | double | 500 | - |
| Motor.MaxPosition | Motor.MaxPosition | double | 100000 | - |
| Motor.MinPosition | Motor.MinPosition | double | -100000 | - |
| Motor.PositionTolerance | Motor.PositionTolerance | double | 0.01 | - |
| Motor.FlipMotor.LeftMotor.* | Motor.LeftFlip* | 多个属性 | 见详细表 | MotorFlipPanel |
| Motor.FlipMotor.RightMotor.* | Motor.RightFlip* | 多个属性 | 见详细表 | MotorFlipPanel |
| **Communication 通信参数** |
| Communication.EpsonRobot1.* | Communication.EpsonRobot1* | 多个属性 | 见详细表 | EpsonRobotManager |
| Communication.EpsonRobot2.* | Communication.EpsonRobot2* | 多个属性 | 见详细表 | EpsonRobotManager2 |
| Communication.Scanner.* | Communication.Scanner* | 多个属性 | 见详细表 | - |
| Communication.ModbusTcp.* | Communication.ModbusTcp* | 多个属性 | 见详细表 | - |
| Communication.MultiScanner.* | Communication.Scanner1/2/3* | 多个属性 | 见详细表 | MultiScannerManager |
| **Vision 视觉参数** |
| Vision.CameraIndex | Vision.CameraIndex | int | 0 | - |
| Vision.ImageWidth | Vision.ImageWidth | int | 1920 | - |
| Vision.ImageHeight | Vision.ImageHeight | int | 1080 | - |
| Vision.DetectionInterval | Vision.DetectionInterval | int | 1000 | - |
| Vision.ConfidenceThreshold | Vision.ConfidenceThreshold | double | 0.8 | - |
| Vision.MaxDetectionTime | Vision.MaxDetectionTime | int | 5000 | - |
| Vision.SaveImages | Vision.SaveImages | bool | true | - |
| Vision.ImageSavePath | Vision.ImageSavePath | string | "Images" | - |
| **Statistics 统计参数** |
| Statistics.DataRetentionDays | Statistics.DataRetentionDays | int | 90 | - |
| Statistics.AutoExport | Statistics.AutoExport | bool | false | - |
| Statistics.ExportInterval | Statistics.ExportInterval | int | 24 | - |
| Statistics.ExportPath | Statistics.ExportPath | string | "Export" | - |
| Statistics.CacheSize | Statistics.CacheSize | int | 1000 | - |
| **UI 界面参数** |
| UI.Theme | UI.Theme | string | "Default" | - |
| UI.Language | UI.Language | string | "zh-CN" | - |
| UI.AutoRefreshInterval | UI.AutoRefreshInterval | int | 1000 | - |
| UI.ShowDebugInfo | UI.ShowDebugInfo | bool | false | - |
| UI.WindowState | UI.WindowState | string | "Maximized" | - |
| **Workflow 工作流参数** |
| Workflow.Enabled | Workflow.Enabled | bool | true | - |
| Workflow.AutoStart | Workflow.AutoStart | bool | false | - |
| Workflow.TimeoutSeconds | Workflow.TimeoutSeconds | int | 60 | - |
| Workflow.RetryCount | Workflow.RetryCount | int | 3 | - |
| Workflow.StepDelayMs | Workflow.StepDelayMs | int | 100 | - |

### App.config → Settings.Current 映射

| App.config键 | 新路径 | 参数类型 | 默认值 | 使用位置 |
|--------------|--------|----------|--------|----------|
| **串口配置** |
| ScannerComPort | Communication.ScannerComPort | string | "COM1" | ScannerManager |
| ScannerBaudRate | Communication.ScannerBaudRate | int | 9600 | ScannerManager |
| **Epson机器人配置** |
| EpsonRobotIP | Communication.EpsonRobot1IP | string | "*************" | EpsonRobotManager |
| EpsonControlPort | Communication.EpsonRobot1ControlPort | int | 5000 | EpsonRobotManager |
| EpsonDataPort | Communication.EpsonRobot1DataPort | int | 5001 | EpsonRobotManager |
| EpsonPassword | Communication.EpsonRobot1Password | string | "EPSON" | EpsonRobotManager |
| EpsonConnectTimeout | Communication.EpsonRobot1ConnectTimeout | int | 5000 | EpsonRobotManager |
| EpsonReceiveTimeout | Communication.EpsonRobot1ReceiveTimeout | int | 3000 | EpsonRobotManager |
| EpsonSendTimeout | Communication.EpsonRobot1SendTimeout | int | 3000 | EpsonRobotManager |
| EpsonScanInterval | Communication.EpsonRobot1ScanInterval | int | 100 | EpsonRobotManager |
| EpsonAutoReconnect | Communication.EpsonRobot1AutoReconnect | bool | true | EpsonRobotManager |
| EpsonReconnectInterval | Communication.EpsonRobot1ReconnectInterval | int | 5000 | EpsonRobotManager |
| EpsonMaxReconnectAttempts | Communication.EpsonRobot1MaxReconnectAttempts | int | 10 | EpsonRobotManager |
| **Modbus TCP配置** |
| ModbusTcpIP | Communication.ModbusTcpIP | string | "*************" | - |
| ModbusTcpPort | Communication.ModbusTcpPort | int | 502 | - |
| ModbusSlaveId | Communication.ModbusSlaveId | int | 1 | - |
| **硬件配置** |
| DMC1000CardIndex | IO.DMC1000CardIndex | int | 0 | - |
| VisionCameraIndex | Vision.CameraIndex | int | 0 | VisionManager |
| **路径配置** |
| VisionConfigPath | Vision.VisionConfigPath | string | "Config\\VisionConfig.json" | VisionManager |
| LogPath | System.LogPath | string | "Logs\\" | - |
| StatisticsDataPath | Statistics.StatisticsDataPath | string | "Data\\Statistics.csv" | StatisticsManager |
| **其他配置** |
| LogLevel | System.LogLevel | string | "Info" | - |
| AutoSaveInterval | System.AutoSaveInterval | int | 300 | - |

## 冲突参数分析

### 重复配置项：
1. **EpsonRobotIP**: 同时存在于App.config和SystemConfig.json中
2. **LogLevel**: 同时存在于App.config和SystemConfig.json中  
3. **VisionCameraIndex**: 同时存在于App.config和SystemConfig.json中

### 解决方案：
- 优先使用SystemConfig.json中的值
- App.config中的值作为默认值或备用值
- 迁移时统一到Settings系统中

## 复杂对象映射

### 翻转电机参数详细映射：
```
Motor.FlipMotor.LeftMotor.PulseEquivalent → Motor.LeftFlipPulseEquivalent
Motor.FlipMotor.LeftMotor.MaxSpeed → Motor.LeftFlipMaxSpeed
Motor.FlipMotor.LeftMotor.StartSpeed → Motor.LeftFlipStartSpeed
Motor.FlipMotor.LeftMotor.Acceleration → Motor.LeftFlipAcceleration
Motor.FlipMotor.LeftMotor.HomeSpeed → Motor.LeftFlipHomeSpeed
Motor.FlipMotor.LeftMotor.HomeDirection → Motor.LeftFlipHomeDirection
Motor.FlipMotor.LeftMotor.HomeIO → Motor.LeftFlipHomeIO
Motor.FlipMotor.LeftMotor.PositiveLimitIO → Motor.LeftFlipPositiveLimitIO
Motor.FlipMotor.LeftMotor.NegativeLimitIO → Motor.LeftFlipNegativeLimitIO
Motor.FlipMotor.LeftMotor.HomeTimeout → Motor.LeftFlipHomeTimeout

Motor.FlipMotor.LeftPositions.Position1 → Motor.LeftFlipPosition1
Motor.FlipMotor.LeftPositions.Position2 → Motor.LeftFlipPosition2
Motor.FlipMotor.LeftPositions.Position3 → Motor.LeftFlipPosition3
Motor.FlipMotor.LeftPositions.Position4 → Motor.LeftFlipPosition4
```

### IO输出状态映射：
- **原结构**: `IO.OutputStates.OutputStates` (Dictionary<string, bool>)
- **新结构**: 需要特殊处理，可能需要保持Dictionary结构或转换为具体属性

## 迁移优先级

### 高优先级（影响核心功能）：
1. Motor参数 - 影响电机控制
2. Communication参数 - 影响设备通信
3. System.LastSystemMode - 影响系统状态恢复

### 中优先级（影响用户体验）：
1. UI参数 - 影响界面显示
2. Vision参数 - 影响视觉功能
3. IO参数 - 影响IO控制

### 低优先级（不影响核心功能）：
1. Statistics参数 - 统计功能
2. Workflow参数 - 工作流配置

## 迁移策略

### 阶段1：创建迁移工具
- 读取现有配置文件
- 转换为新的Settings结构
- 验证数据完整性

### 阶段2：逐模块迁移
- 按优先级逐个模块迁移
- 保持向后兼容
- 充分测试每个模块

### 阶段3：清理旧代码
- 删除旧的配置文件
- 清理相关代码
- 更新文档

## 风险评估

### 高风险项：
1. IO输出状态的复杂对象迁移
2. 电机参数的精确映射
3. 通信参数的兼容性

### 缓解措施：
1. 创建详细的测试用例
2. 保留原配置文件作为备份
3. 分阶段迁移，确保每步可回滚
