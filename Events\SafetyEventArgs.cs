using System;

namespace MyHMI.Events
{
    /// <summary>
    /// 安全状态枚举
    /// </summary>
    public enum SafetyState
    {
        /// <summary>
        /// 安全状态 - 系统正常运行
        /// </summary>
        Safe = 0,

        /// <summary>
        /// 紧急停止状态 - 触发紧急停止条件
        /// </summary>
        EmergencyStop = 1,

        /// <summary>
        /// 安全锁定状态 - 系统被安全锁定
        /// </summary>
        SafetyLocked = 2,

        /// <summary>
        /// 程序停止状态 - 程序被手动停止
        /// </summary>
        ProgramStopped = 3,

        /// <summary>
        /// 系统启动中 - 系统正在启动过程中
        /// </summary>
        Starting = 4,

        /// <summary>
        /// 系统错误状态 - 发生系统错误
        /// </summary>
        SystemError = 5
    }

    /// <summary>
    /// 指示灯状态枚举
    /// </summary>
    public enum IndicatorLightState
    {
        /// <summary>
        /// 红灯 - 错误/紧急停止状态
        /// </summary>
        Red = 0,

        /// <summary>
        /// 绿灯 - 自动模式正常运行
        /// </summary>
        Green = 1,

        /// <summary>
        /// 黄灯 - 调试模式停止状态
        /// </summary>
        Yellow = 2,

        /// <summary>
        /// 所有灯关闭
        /// </summary>
        Off = 3
    }

    /// <summary>
    /// I/O信号类型枚举
    /// </summary>
    public enum IOSignalType
    {
        /// <summary>
        /// 启动信号 (I0001)
        /// </summary>
        StartSignal,

        /// <summary>
        /// 停止信号 (I0002)
        /// </summary>
        StopSignal,

        /// <summary>
        /// 安全锁定信号 (I0003)
        /// </summary>
        SafetyLockSignal,

        /// <summary>
        /// 安全门信号 (I0101)
        /// </summary>
        SafetyDoorSignal,

        /// <summary>
        /// 光栅1信号 (I0102)
        /// </summary>
        LightCurtain1Signal,

        /// <summary>
        /// 光栅2信号 (I0103)
        /// </summary>
        LightCurtain2Signal
    }

    /// <summary>
    /// 安全状态变化事件参数
    /// </summary>
    public class SafetyStateChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 旧的安全状态
        /// </summary>
        public SafetyState OldState { get; set; }

        /// <summary>
        /// 新的安全状态
        /// </summary>
        public SafetyState NewState { get; set; }

        /// <summary>
        /// 触发状态变化的原因
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// 相关的I/O信号（如果有）
        /// </summary>
        public string TriggerIOSignal { get; set; }

        /// <summary>
        /// 事件时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="oldState">旧状态</param>
        /// <param name="newState">新状态</param>
        /// <param name="reason">变化原因</param>
        /// <param name="triggerIOSignal">触发的I/O信号</param>
        public SafetyStateChangedEventArgs(SafetyState oldState, SafetyState newState, string reason, string triggerIOSignal = "")
        {
            OldState = oldState;
            NewState = newState;
            Reason = reason ?? string.Empty;
            TriggerIOSignal = triggerIOSignal ?? string.Empty;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// I/O信号变化事件参数
    /// </summary>
    public class IOSignalChangedEventArgs : EventArgs
    {
        /// <summary>
        /// I/O信号编号
        /// </summary>
        public string IONumber { get; set; }

        /// <summary>
        /// I/O信号类型
        /// </summary>
        public IOSignalType SignalType { get; set; }

        /// <summary>
        /// 旧的信号值
        /// </summary>
        public bool OldValue { get; set; }

        /// <summary>
        /// 新的信号值
        /// </summary>
        public bool NewValue { get; set; }

        /// <summary>
        /// 是否为边沿触发（上升沿或下降沿）
        /// </summary>
        public bool IsEdgeTriggered { get; set; }

        /// <summary>
        /// 边沿类型（true为上升沿，false为下降沿）
        /// </summary>
        public bool IsRisingEdge { get; set; }

        /// <summary>
        /// 事件时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="ioNumber">I/O编号</param>
        /// <param name="signalType">信号类型</param>
        /// <param name="oldValue">旧值</param>
        /// <param name="newValue">新值</param>
        /// <param name="isEdgeTriggered">是否边沿触发</param>
        /// <param name="isRisingEdge">是否上升沿</param>
        public IOSignalChangedEventArgs(string ioNumber, IOSignalType signalType, bool oldValue, bool newValue, 
            bool isEdgeTriggered = false, bool isRisingEdge = false)
        {
            IONumber = ioNumber ?? string.Empty;
            SignalType = signalType;
            OldValue = oldValue;
            NewValue = newValue;
            IsEdgeTriggered = isEdgeTriggered;
            IsRisingEdge = isRisingEdge;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 紧急停止事件参数
    /// </summary>
    public class EmergencyStopEventArgs : EventArgs
    {
        /// <summary>
        /// 触发紧急停止的I/O信号
        /// </summary>
        public string TriggerIOSignal { get; set; }

        /// <summary>
        /// 紧急停止原因
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// 是否为严重紧急停止
        /// </summary>
        public bool IsCritical { get; set; }

        /// <summary>
        /// 事件时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="triggerIOSignal">触发信号</param>
        /// <param name="reason">停止原因</param>
        /// <param name="isCritical">是否严重</param>
        public EmergencyStopEventArgs(string triggerIOSignal, string reason, bool isCritical = true)
        {
            TriggerIOSignal = triggerIOSignal ?? string.Empty;
            Reason = reason ?? string.Empty;
            IsCritical = isCritical;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 指示灯状态变化事件参数
    /// </summary>
    public class IndicatorLightChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 旧的指示灯状态
        /// </summary>
        public IndicatorLightState OldState { get; set; }

        /// <summary>
        /// 新的指示灯状态
        /// </summary>
        public IndicatorLightState NewState { get; set; }

        /// <summary>
        /// 状态变化原因
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// 事件时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="oldState">旧状态</param>
        /// <param name="newState">新状态</param>
        /// <param name="reason">变化原因</param>
        public IndicatorLightChangedEventArgs(IndicatorLightState oldState, IndicatorLightState newState, string reason)
        {
            OldState = oldState;
            NewState = newState;
            Reason = reason ?? string.Empty;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 安全管理器错误事件参数
    /// </summary>
    public class SafetyManagerErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 错误类型
        /// </summary>
        public string ErrorType { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 相关的I/O信号（如果有）
        /// </summary>
        public string RelatedIOSignal { get; set; }

        /// <summary>
        /// 是否为严重错误
        /// </summary>
        public bool IsCritical { get; set; }

        /// <summary>
        /// 异常对象（如果有）
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// 事件时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="errorType">错误类型</param>
        /// <param name="errorMessage">错误消息</param>
        /// <param name="relatedIOSignal">相关I/O信号</param>
        /// <param name="isCritical">是否严重</param>
        /// <param name="exception">异常对象</param>
        public SafetyManagerErrorEventArgs(string errorType, string errorMessage, string relatedIOSignal = "", 
            bool isCritical = false, Exception exception = null)
        {
            ErrorType = errorType ?? string.Empty;
            ErrorMessage = errorMessage ?? string.Empty;
            RelatedIOSignal = relatedIOSignal ?? string.Empty;
            IsCritical = isCritical;
            Exception = exception;
            Timestamp = DateTime.Now;
        }
    }
}
