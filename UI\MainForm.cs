using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using MyHMI.Helpers;
using MyHMI.UI.Controls;
using MyHMI.Managers;
using MyHMI.Settings;
// UI.Core命名空间已完全移除 - 改为事件驱动架构
// using MyHMI.UI.Core.Extensions; // 已移除
// using MyHMI.UI.Core.Managers; // 已移除

namespace MyHMI.UI
{
    /// <summary>
    /// 主窗体 - 完全按照HTML原型设计的工业HMI界面 (1920×1080)
    /// </summary>
    public partial class MainForm : Form
    {
        #region 私有字段 - 按HTML原型精确尺寸设计
        
        // 主布局组件 - 按HTML原型精确尺寸
        private Panel _titleBar;           // 顶部标题栏 60px
        private Panel _systemMenu;         // 系统菜单栏 40px  
        private Panel _mainArea;           // 主功能区 920px
        private Panel _statusBar;          // 底部状态栏 60px
        
        // 主功能区组件 - 按HTML原型精确尺寸
        private Panel _leftMenu;           // 左侧菜单 200px
        private Panel _functionArea;       // 功能显示区 700px
        private Panel _rightArea;          // 右侧区域 1020px
        
        // 功能显示区组件
        private Panel _tabMenu;            // 二级Tab菜单栏 40px
        private Panel _panelArea;          // 功能面板区 880px
        
        // 右侧区域组件
        private Panel _cameraArea;         // 工业相机显示区域 460px
        private Panel _controlArea;        // 控制操作区 230px
        private Panel _statisticsArea;     // 产量统计区 230px
        
        // 左侧菜单按钮
        private Button[] _menuButtons;
        private string[] _menuNames = { "视觉控制", "电机控制", "6轴机器人控制", "scara控制", "io控制", "生产日志管理" };
        
        // 系统菜单按钮
        private Button _systemLogBtn;
        private Button _securityBtn;
        private Button _deviceStatusBtn;
        private Button _minimizeBtn;
        private Button _exitBtn;
        
        // 当前显示的面板
        private UserControl _currentPanel;
        private string _currentMenuType = "vision";
        private int _currentSelectedMenu = 0;

        // SCARA模拟器窗体 - 临时功能
        private ScaraRobotSimulatorForm _scaraSimulatorForm;

        // 面板缓存机制 - 避免重复创建和销毁面板
        private readonly Dictionary<string, UserControl> _panelCache = new Dictionary<string, UserControl>();

        // 时间显示独立更新机制
        private System.Windows.Forms.Timer _timeUpdateTimer;
        private Label _timeDisplayLabel;
        
        // Tab配置 - 按HTML原型
        private readonly Dictionary<string, (string id, string name)[]> _tabConfig = new Dictionary<string, (string, string)[]>
        {
            ["vision"] = new[] { ("vision-position", "定位相机控制"), ("vision-align", "对位相机控制") },
            ["motor"] = new[] { ("motor-flip-params", "翻转电机参数"), ("motor-flip-teach", "翻转电机示教"), ("motor-belt", "皮带电机") },
            ["robot6"] = new[] { ("robot6-control", "机器人控制"), ("robot6-control2", "机器人2"), ("scanner-control", "扫描器控制") },
            ["scara"] = new[] { ("scara-comm", "通信管理"), ("scara-teach", "示教功能") },
            ["io"] = new[] { ("io-read", "读取IO"), ("io-write", "写入IO") },
            ["log"] = new[] { ("log-time", "日志时间选择") }
        };
        


        // 控制按钮
        private Button _startBtn;
        private Button _pauseBtn;
        private Button _stopBtn;
        private Button _resetBtn;
        private Label _statusLabel;
        private Label _modeLabel;

        #endregion

        #region 构造函数
        
        public MainForm()
        {
            InitializeComponent();
            InitializeInterface();
            InitializeSystemModeEvents();

            // 初始化时间显示独立更新机制
            InitializeTimeUpdateTimer();

            // 订阅窗体关闭事件
            this.FormClosing += MainForm_FormClosing;

            // 订阅窗体加载事件，用于初始化新架构
            this.Load += MainForm_Load;

            LogHelper.Info("主界面初始化完成 - 按HTML原型设计");
        }
        
        #endregion

        #region 事件处理器

        /// <summary>
        /// 窗体加载事件处理器 - 简化启动逻辑，直接初始化业务逻辑
        /// </summary>
        private async void MainForm_Load(object sender, EventArgs e)
        {
            try
            {
                LogHelper.Info("开始初始化系统");

                // 直接初始化业务逻辑和管理器
                await InitializeBusinessLogicAsync();

                // 执行一次完整的UI界面刷新
                await RefreshAllUIAsync();

                LogHelper.Info("系统初始化完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("MainForm_Load事件处理异常", ex);
                MessageBox.Show($"系统初始化异常: {ex.Message}",
                    "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 业务逻辑初始化方法

        /// <summary>
        /// 初始化业务逻辑和管理器 - 简化的事件驱动模式
        /// </summary>
        private async Task InitializeBusinessLogicAsync()
        {
            try
            {
                LogHelper.Info("初始化业务逻辑");

                // 初始化核心管理器
                await InitializeCoreManagersAsync();

                // 建立与业务层的交互机制
                EstablishBusinessLayerInteraction();

                LogHelper.Info("业务逻辑初始化完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("业务逻辑初始化失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 初始化核心管理器
        /// </summary>
        private async Task InitializeCoreManagersAsync()
        {
            try
            {
                // 初始化IO管理器
                if (DMC1000BIOManager.Instance != null)
                {
                    await DMC1000BIOManager.Instance.InitializeAsync();
                    LogHelper.Info("IO管理器初始化完成");
                }

                // 初始化电机管理器
                if (DMC1000BMotorManager.Instance != null)
                {
                    await DMC1000BMotorManager.Instance.InitializeAsync();
                    LogHelper.Info("电机管理器初始化完成");
                }

                // 初始化其他核心管理器
                // 这里可以添加其他必要的管理器初始化
            }
            catch (Exception ex)
            {
                LogHelper.Error("核心管理器初始化失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 建立与业务层的交互机制 - 事件驱动模式
        /// </summary>
        private void EstablishBusinessLayerInteraction()
        {
            try
            {
                // 订阅IO状态变化事件
                if (DMC1000BIOManager.Instance != null)
                {
                    DMC1000BIOManager.Instance.IOInputStateChanged += OnIOInputStateChanged;
                    DMC1000BIOManager.Instance.BatchIOStateChanged += OnBatchIOStateChanged;
                    LogHelper.Info("IO事件订阅完成");
                }

                // 订阅电机状态变化事件
                if (DMC1000BMotorManager.Instance != null)
                {
                    // 这里可以添加电机状态变化事件订阅
                    LogHelper.Info("电机事件订阅完成");
                }

                LogHelper.Info("业务层交互机制建立完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("建立业务层交互机制失败", ex);
                throw;
            }
        }

        #endregion

        #region UI刷新方法

        /// <summary>
        /// 执行一次完整的UI界面刷新
        /// </summary>
        private async Task RefreshAllUIAsync()
        {
            try
            {
                LogHelper.Info("执行UI界面刷新");

                // 使用Control.Invoke确保在UI线程中执行
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(async () => await RefreshAllUIAsync()));
                    return;
                }

                // 刷新所有面板的显示状态
                await RefreshAllPanelsAsync();

                LogHelper.Info("UI界面刷新完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("UI界面刷新失败", ex);
            }
        }

        /// <summary>
        /// 刷新所有面板
        /// </summary>
        private async Task RefreshAllPanelsAsync()
        {
            try
            {
                // 刷新当前显示的面板
                if (_currentPanel != null)
                {
                    // 根据面板类型进行相应的数据更新
                    await UpdatePanelDataAsync(_currentPanel);
                }

                // 更新状态栏
                UpdateStatusBar();
            }
            catch (Exception ex)
            {
                LogHelper.Error("刷新面板失败", ex);
            }
        }

        /// <summary>
        /// 更新面板数据
        /// </summary>
        private async Task UpdatePanelDataAsync(Control panel)
        {
            try
            {
                // 根据面板类型更新数据
                if (panel is UI.Controls.IOControlPanel ioPanel)
                {
                    // 更新IO面板数据
                    await UpdateIOPanelAsync(ioPanel);
                }
                else if (panel is UI.Controls.MotorControlPanel motorPanel)
                {
                    // 更新电机面板数据
                    await UpdateMotorPanelAsync(motorPanel);
                }
                // 可以添加其他面板类型的处理
            }
            catch (Exception ex)
            {
                LogHelper.Error("更新面板数据失败", ex);
            }
        }

        #endregion

        #region 事件驱动UI更新方法

        /// <summary>
        /// IO输入状态变化事件处理器 - 事件驱动UI更新
        /// </summary>
        private void OnIOInputStateChanged(object sender, Models.IOInputStateChangedEventArgs e)
        {
            try
            {
                // 使用Control.Invoke进行跨线程UI更新
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() => OnIOInputStateChanged(sender, e)));
                    return;
                }

                // 记录IO状态变化
                LogHelper.Debug($"UI收到IO输入状态变化: {e.IONumber} = {e.NewState}");

                // 立即更新相关UI控件
                UpdateSpecificIOUI(e.IONumber, e.NewState);

                // 更新整体IO相关UI
                UpdateIORelatedUI();
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理IO输入状态变化事件失败", ex);
            }
        }

        /// <summary>
        /// 批量IO状态变化事件处理器
        /// </summary>
        private void OnBatchIOStateChanged(object sender, MyHMI.Models.BatchIOStateChangedEventArgs e)
        {
            try
            {
                // 使用Control.Invoke进行跨线程UI更新
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() => OnBatchIOStateChanged(sender, e)));
                    return;
                }

                // 记录批量IO状态变化
                LogHelper.Debug($"UI收到批量IO状态变化: {e.ChangedStates.Count}个{e.IOType}发生变化");

                // 批量更新特定IO的UI
                foreach (var kvp in e.ChangedStates)
                {
                    UpdateSpecificIOUI(kvp.Key, kvp.Value);
                }

                // 批量更新IO相关UI
                UpdateIORelatedUI();
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理批量IO状态变化事件失败", ex);
            }
        }

        /// <summary>
        /// 更新特定IO的UI显示 - 精确更新单个IO状态
        /// </summary>
        /// <param name="ioName">IO名称</param>
        /// <param name="state">IO状态</param>
        private void UpdateSpecificIOUI(string ioName, bool state)
        {
            try
            {
                // 如果当前显示的是IO面板，则更新特定IO的显示
                if (_currentPanel is UI.Controls.IOControlPanel ioPanel)
                {
                    // 调用IO面板的特定IO更新方法
                    // 这里可以根据IO面板的具体实现来更新特定控件
                    UpdateIOPanelSpecificIO(ioPanel, ioName, state);
                }

                // 可以在这里添加其他需要响应IO状态变化的UI更新
                // 例如：状态指示灯、报警显示等
            }
            catch (Exception ex)
            {
                LogHelper.Error($"更新特定IO UI失败: {ioName}", ex);
            }
        }

        /// <summary>
        /// 更新IO面板中的特定IO显示
        /// </summary>
        /// <param name="ioPanel">IO控制面板</param>
        /// <param name="ioName">IO名称</param>
        /// <param name="state">IO状态</param>
        private void UpdateIOPanelSpecificIO(UI.Controls.IOControlPanel ioPanel, string ioName, bool state)
        {
            try
            {
                // 这里可以根据IO面板的具体实现来更新特定IO控件
                // 例如：查找对应的按钮、指示灯等控件并更新其状态

                // 示例：如果IO面板有按IO名称查找控件的方法
                // var ioControl = ioPanel.FindIOControl(ioName);
                // if (ioControl != null)
                // {
                //     ioControl.UpdateState(state);
                // }

                LogHelper.Debug($"更新IO面板中的IO: {ioName} = {state}");
            }
            catch (Exception ex)
            {
                LogHelper.Error($"更新IO面板特定IO失败: {ioName}", ex);
            }
        }

        /// <summary>
        /// 更新IO相关UI
        /// </summary>
        private void UpdateIORelatedUI()
        {
            try
            {
                // 如果当前显示的是IO面板，则更新其显示
                if (_currentPanel is UI.Controls.IOControlPanel ioPanel)
                {
                    // 触发IO面板的整体更新
                    // 这里可以调用IO面板的更新方法
                    RefreshIOPanelDisplay(ioPanel);
                }

                // 更新状态栏中的IO状态指示
                UpdateStatusBar();
            }
            catch (Exception ex)
            {
                LogHelper.Error("更新IO相关UI失败", ex);
            }
        }

        /// <summary>
        /// 刷新IO面板显示
        /// </summary>
        /// <param name="ioPanel">IO控制面板</param>
        private void RefreshIOPanelDisplay(UI.Controls.IOControlPanel ioPanel)
        {
            try
            {
                // 这里可以调用IO面板的刷新方法
                // 例如：ioPanel.RefreshDisplay();

                LogHelper.Debug("刷新IO面板显示");
            }
            catch (Exception ex)
            {
                LogHelper.Error("刷新IO面板显示失败", ex);
            }
        }

        /// <summary>
        /// 更新IO面板
        /// </summary>
        private async Task UpdateIOPanelAsync(UI.Controls.IOControlPanel ioPanel)
        {
            try
            {
                // 这里可以添加IO面板的数据更新逻辑
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                LogHelper.Error("更新IO面板失败", ex);
            }
        }

        /// <summary>
        /// 更新电机面板
        /// </summary>
        private async Task UpdateMotorPanelAsync(UI.Controls.MotorControlPanel motorPanel)
        {
            try
            {
                // 这里可以添加电机面板的数据更新逻辑
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                LogHelper.Error("更新电机面板失败", ex);
            }
        }

        /// <summary>
        /// 更新状态栏 - 不再包含时间显示更新
        /// </summary>
        private void UpdateStatusBar()
        {
            try
            {
                // 时间显示已移至独立的定时器更新机制
                // 这里只处理其他状态信息的更新

                // 可以添加其他状态信息的更新，如IO状态指示等
                LogHelper.Debug("状态栏更新（不包含时间显示）");
            }
            catch (Exception ex)
            {
                LogHelper.Error("更新状态栏失败", ex);
            }
        }

        #endregion

        #region 界面初始化方法

        /// <summary>
        /// 初始化界面 - 完全按照HTML原型布局
        /// </summary>
        private void InitializeInterface()
        {
            try
            {
                // 性能优化：暂停布局更新
                this.SuspendLayout();

                // 设置窗体属性 - 按HTML原型
                this.Size = new Size(1920, 1080);
                this.StartPosition = FormStartPosition.CenterScreen;
                this.FormBorderStyle = FormBorderStyle.None;
                this.WindowState = FormWindowState.Maximized;
                this.BackColor = ColorTranslator.FromHtml("#2c3e50");

                // 性能优化：启用双缓冲
                this.SetStyle(ControlStyles.AllPaintingInWmPaint |
                             ControlStyles.UserPaint |
                             ControlStyles.DoubleBuffer |
                             ControlStyles.ResizeRedraw, true);

                // 创建主布局
                CreateMainLayout();



                // 默认显示视觉控制
                ShowPanel("vision");

                // 启动时间更新定时器
                StartTimeUpdateTimer();

                // 性能优化：恢复布局更新
                this.ResumeLayout(true);

                LogHelper.Info("主界面布局创建完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("主界面初始化失败", ex);
                MessageBox.Show($"界面初始化失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                // 确保恢复布局更新
                this.ResumeLayout(true);
            }
        }
        
        /// <summary>
        /// 创建主布局 - 按HTML原型精确尺寸
        /// </summary>
        private void CreateMainLayout()
        {
            // 顶部标题栏 - 60px高
            CreateTitleBar();
            
            // 系统菜单栏 - 40px高
            CreateSystemMenu();
            
            // 主功能区 - 920px高
            CreateMainArea();
            
            // 底部状态栏 - 60px高
            CreateStatusBar();
        }
        
        /// <summary>
        /// 创建顶部标题栏 - 按HTML原型
        /// </summary>
        private void CreateTitleBar()
        {
            _titleBar = new Panel
            {
                Size = new Size(1920, 60),
                Location = new Point(0, 0),
                BackColor = ColorTranslator.FromHtml("#1e2329"),
                Dock = DockStyle.None
            };
            
            var titleLabel = new Label
            {
                Text = "视觉机器人检测系统",
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 24F, FontStyle.Bold),
                Size = new Size(400, 60),
                Location = new Point(760, 0), // 居中显示
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            _titleBar.Controls.Add(titleLabel);
            this.Controls.Add(_titleBar);
        }
        
        /// <summary>
        /// 创建系统菜单栏 - 按HTML原型
        /// </summary>
        private void CreateSystemMenu()
        {
            _systemMenu = new Panel
            {
                Size = new Size(1920, 40),
                Location = new Point(0, 60),
                BackColor = ColorTranslator.FromHtml("#252930"),
                Dock = DockStyle.None
            };

            // 系统菜单按钮 - 调整按钮宽度以适应文本长度
            _systemLogBtn = CreateSystemMenuButton("系统日志", 20, 100);
            _securityBtn = CreateSystemMenuButton("安全设置", 130, 100);
            _deviceStatusBtn = CreateSystemMenuButton("外部设备状态", 240, 140);
            _minimizeBtn = CreateSystemMenuButton("最小化", 1650, 100);
            _exitBtn = CreateSystemMenuButton("退出系统", 1780, 120);

            _systemMenu.Controls.AddRange(new Control[] { _systemLogBtn, _securityBtn, _deviceStatusBtn, _minimizeBtn, _exitBtn });
            this.Controls.Add(_systemMenu);
        }
        
        /// <summary>
        /// 创建系统菜单按钮
        /// </summary>
        private Button CreateSystemMenuButton(string text, int x, int width = 120)
        {
            var button = new Button
            {
                Text = text,
                Size = new Size(width, 32), // 增加按钮高度确保完整显示
                Location = new Point(x, 4), // 调整垂直位置居中
                BackColor = ColorTranslator.FromHtml("#252930"),
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 12F), // 稍微减小字体确保文本完整显示
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false,
                TextAlign = ContentAlignment.MiddleCenter // 确保文本居中显示
            };

            button.FlatAppearance.BorderSize = 0;
            button.MouseEnter += (s, e) => button.BackColor = ColorTranslator.FromHtml("#3498db");
            button.MouseLeave += (s, e) => button.BackColor = ColorTranslator.FromHtml("#252930");

            if (text == "退出系统")
            {
                button.Click += (s, e) => this.Close();
            }
            else if (text == "最小化")
            {
                button.Click += (s, e) => this.WindowState = FormWindowState.Minimized;
            }
            else if (text == "安全设置")
            {
                button.Click += (s, e) => ShowModeSelectionDialog();
            }
            else if (text == "外部设备状态")
            {
                button.Click += (s, e) => ShowDeviceStatusMenu();
            }

            return button;
        }
        
        /// <summary>
        /// 创建主功能区 - 按HTML原型
        /// </summary>
        private void CreateMainArea()
        {
            _mainArea = new Panel
            {
                Size = new Size(1920, 920),
                Location = new Point(0, 100),
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                Dock = DockStyle.None
            };
            
            // 左侧菜单 - 200px宽
            CreateLeftMenu();
            
            // 功能显示区 - 700px宽
            CreateFunctionArea();
            
            // 右侧区域 - 1020px宽
            CreateRightArea();
            
            this.Controls.Add(_mainArea);
        }
        


        /// <summary>
        /// 创建左侧菜单 - 按HTML原型 200px宽
        /// </summary>
        private void CreateLeftMenu()
        {
            _leftMenu = new Panel
            {
                Size = new Size(200, 920),
                Location = new Point(0, 0),
                BackColor = ColorTranslator.FromHtml("#2d3339"),
                Dock = DockStyle.None
            };

            // 创建菜单按钮 - 每个153px高
            _menuButtons = new Button[_menuNames.Length];
            for (int i = 0; i < _menuNames.Length; i++)
            {
                _menuButtons[i] = new Button
                {
                    Text = _menuNames[i],
                    Size = new Size(200, 153),
                    Location = new Point(0, i * 153),
                    BackColor = ColorTranslator.FromHtml("#2d3339"),
                    ForeColor = Color.White,
                    Font = new Font("微软雅黑", 16F),
                    FlatStyle = FlatStyle.Flat,
                    UseVisualStyleBackColor = false,
                    TextAlign = ContentAlignment.MiddleCenter
                };

                _menuButtons[i].FlatAppearance.BorderSize = 1;
                _menuButtons[i].FlatAppearance.BorderColor = ColorTranslator.FromHtml("#4a5661");

                // 设置默认选中第一个
                if (i == 0)
                {
                    _menuButtons[i].BackColor = ColorTranslator.FromHtml("#3498db");
                }

                // 绑定点击事件
                int index = i; // 闭包变量
                _menuButtons[i].Click += (s, e) => MenuButton_Click(index);
                _menuButtons[i].MouseEnter += (s, e) => {
                    if (index != _currentSelectedMenu)
                        ((Button)s).BackColor = ColorTranslator.FromHtml("#3498db");
                };
                _menuButtons[i].MouseLeave += (s, e) => {
                    if (index != _currentSelectedMenu)
                        ((Button)s).BackColor = ColorTranslator.FromHtml("#2d3339");
                };

                _leftMenu.Controls.Add(_menuButtons[i]);
            }

            _mainArea.Controls.Add(_leftMenu);
        }

        /// <summary>
        /// 创建功能显示区 - 按HTML原型 700px宽
        /// </summary>
        private void CreateFunctionArea()
        {
            _functionArea = new Panel
            {
                Size = new Size(700, 920),
                Location = new Point(200, 0),
                BackColor = ColorTranslator.FromHtml("#34495e"),
                BorderStyle = BorderStyle.FixedSingle,
                Dock = DockStyle.None
            };

            // 二级Tab菜单栏 - 40px高
            CreateTabMenu();

            // 功能面板区 - 880px高
            CreatePanelArea();

            _mainArea.Controls.Add(_functionArea);
        }

        /// <summary>
        /// 创建二级Tab菜单栏 - 按HTML原型
        /// </summary>
        private void CreateTabMenu()
        {
            _tabMenu = new Panel
            {
                Size = new Size(700, 40),
                Location = new Point(0, 0),
                BackColor = ColorTranslator.FromHtml("#2d3339"),
                BorderStyle = BorderStyle.None,
                Dock = DockStyle.None
            };

            // Tab按钮将在ShowPanel方法中动态创建
            _functionArea.Controls.Add(_tabMenu);
        }

        /// <summary>
        /// 创建功能面板区 - 按HTML原型
        /// </summary>
        private void CreatePanelArea()
        {
            _panelArea = new Panel
            {
                Size = new Size(700, 880),
                Location = new Point(0, 40),
                BackColor = ColorTranslator.FromHtml("#34495e"),
                Dock = DockStyle.None,
                AutoScroll = true,
                Padding = new Padding(20)
            };

            _functionArea.Controls.Add(_panelArea);
        }

        #endregion

        #region 菜单切换方法

        /// <summary>
        /// 菜单按钮点击事件
        /// </summary>
        private void MenuButton_Click(int index)
        {
            // 更新选中状态
            for (int i = 0; i < _menuButtons.Length; i++)
            {
                _menuButtons[i].BackColor = (i == index) ?
                    ColorTranslator.FromHtml("#3498db") :
                    ColorTranslator.FromHtml("#2d3339");
            }

            _currentSelectedMenu = index;

            // 显示对应面板
            string[] menuTypes = { "vision", "motor", "robot6", "scara", "io", "log" };
            ShowPanel(menuTypes[index]);
        }

        /// <summary>
        /// 显示指定面板 - 按HTML原型Tab配置
        /// </summary>
        private void ShowPanel(string panelType)
        {
            try
            {
                _currentMenuType = panelType;

                // 更新Tab菜单
                UpdateTabMenu(panelType);

                // 显示第一个Tab的面板
                if (_tabConfig.ContainsKey(panelType) && _tabConfig[panelType].Length > 0)
                {
                    ShowTabPanel(_tabConfig[panelType][0].id);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"显示面板失败: {panelType}", ex);
            }
        }

        /// <summary>
        /// 更新Tab菜单 - 按HTML原型
        /// </summary>
        private void UpdateTabMenu(string panelType)
        {
            _tabMenu.Controls.Clear();

            if (!_tabConfig.ContainsKey(panelType)) return;

            var tabs = _tabConfig[panelType];
            int x = 0;

            for (int i = 0; i < tabs.Length; i++)
            {
                var tabButton = new Button
                {
                    Text = tabs[i].name,
                    Size = new Size(tabs[i].name.Length * 12 + 32, 32),
                    Location = new Point(x, 4),
                    BackColor = (i == 0) ? ColorTranslator.FromHtml("#3498db") : ColorTranslator.FromHtml("#3b434a"),
                    ForeColor = Color.White,
                    Font = new Font("微软雅黑", 14F),
                    FlatStyle = FlatStyle.Flat,
                    UseVisualStyleBackColor = false,
                    Tag = tabs[i].id
                };

                tabButton.FlatAppearance.BorderSize = 0;
                tabButton.Click += TabButton_Click;
                tabButton.MouseEnter += (s, e) => tabButton.BackColor = ColorTranslator.FromHtml("#3498db");
                tabButton.MouseLeave += (s, e) => {
                    if (tabButton.Tag.ToString() != GetCurrentTabId())
                        tabButton.BackColor = ColorTranslator.FromHtml("#3b434a");
                };

                _tabMenu.Controls.Add(tabButton);
                x += tabButton.Width + 2;
            }
        }

        /// <summary>
        /// Tab按钮点击事件
        /// </summary>
        private void TabButton_Click(object sender, EventArgs e)
        {
            var button = sender as Button;
            if (button == null) return;

            // 更新Tab按钮状态
            foreach (Button btn in _tabMenu.Controls.OfType<Button>())
            {
                btn.BackColor = (btn == button) ?
                    ColorTranslator.FromHtml("#3498db") :
                    ColorTranslator.FromHtml("#3b434a");
            }

            // 显示对应面板
            ShowTabPanel(button.Tag.ToString());
        }

        /// <summary>
        /// 显示Tab面板 - 使用缓存机制避免重复创建和销毁
        /// </summary>
        private void ShowTabPanel(string tabId)
        {
            try
            {
                // 性能优化：暂停布局更新
                _panelArea.SuspendLayout();

                // 隐藏当前面板而不是销毁
                if (_currentPanel != null)
                {
                    _currentPanel.Visible = false;
                }

                // 从缓存中获取面板，如果不存在则创建
                if (!_panelCache.ContainsKey(tabId))
                {
                    var newPanel = CreateFunctionPanel(tabId);
                    if (newPanel != null)
                    {
                        newPanel.Dock = DockStyle.Fill;
                        newPanel.Visible = false; // 初始隐藏
                        _panelArea.Controls.Add(newPanel);
                        _panelCache[tabId] = newPanel;
                        LogHelper.Info($"创建并缓存面板: {tabId}");
                    }
                    else
                    {
                        LogHelper.Warning($"创建面板失败: {tabId}");
                        _panelArea.ResumeLayout(true);
                        return;
                    }
                }

                // 显示目标面板
                _currentPanel = _panelCache[tabId];
                _currentPanel.Visible = true;

                LogHelper.Debug($"切换到面板: {tabId}");

                // 性能优化：恢复布局更新
                _panelArea.ResumeLayout(true);
            }
            catch (Exception ex)
            {
                LogHelper.Error($"显示Tab面板失败: {tabId}", ex);
                // 确保恢复布局更新
                _panelArea.ResumeLayout(true);
            }
        }

        /// <summary>
        /// 创建功能面板 - 按HTML原型
        /// </summary>
        private UserControl CreateFunctionPanel(string panelId)
        {
            switch (panelId)
            {
                case "vision-position":
                    return new VisionPositionPanel();
                case "vision-align":
                    return new VisionAlignPanel();
                case "motor-flip-params":
                    return new MotorFlipPanel();
                case "motor-flip-teach":
                    return new MotorFlipTeachPanel();
                case "motor-belt":
                    return new MotorBeltPanel();
                case "robot6-control":
                    return new Robot6AxisPanel();
                case "robot6-control2":
                    return new Robot6AxisPanel2();
                case "scanner-control":
                    return new ScannerControlPanel();
                case "scara-comm":
                    return new ScaraCommPanel();
                case "scara-teach":
                    return new ScaraTeachPanel();
                case "io-read":
                    return new IOReadPanel();
                case "io-write":
                    return new IOWritePanel();
                case "log-time":
                    return new ProductionLogPanel();
                default:
                    return CreatePlaceholderPanel(panelId);
            }
        }

        /// <summary>
        /// 创建占位符面板
        /// </summary>
        private UserControl CreatePlaceholderPanel(string panelId)
        {
            var panel = new UserControl
            {
                BackColor = ColorTranslator.FromHtml("#34495e"),
                Size = new Size(660, 840)
            };

            var label = new Label
            {
                Text = $"{panelId} - 面板开发中",
                ForeColor = ColorTranslator.FromHtml("#95a5a6"),
                Font = new Font("微软雅黑", 16F),
                Size = new Size(300, 50),
                Location = new Point(180, 200),
                TextAlign = ContentAlignment.MiddleCenter
            };

            panel.Controls.Add(label);
            return panel;
        }

        /// <summary>
        /// 获取当前Tab ID
        /// </summary>
        private string GetCurrentTabId()
        {
            foreach (Button btn in _tabMenu.Controls.OfType<Button>())
            {
                if (btn.BackColor == ColorTranslator.FromHtml("#3498db"))
                    return btn.Tag.ToString();
            }
            return "";
        }

        #endregion

        #region 右侧区域和状态栏方法

        /// <summary>
        /// 创建右侧区域 - 按HTML原型 1020px宽
        /// </summary>
        private void CreateRightArea()
        {
            _rightArea = new Panel
            {
                Size = new Size(1020, 920),
                Location = new Point(900, 0),
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                Dock = DockStyle.None
            };

            // 工业相机显示区域 - 460px高
            CreateCameraArea();

            // 控制操作区 - 230px高
            CreateControlArea();

            // 产量统计区 - 230px高
            CreateStatisticsArea();

            _mainArea.Controls.Add(_rightArea);
        }

        /// <summary>
        /// 创建底部状态栏 - 按HTML原型
        /// </summary>
        private void CreateStatusBar()
        {
            _statusBar = new Panel
            {
                Size = new Size(1920, 60),
                Location = new Point(0, 1020),
                BackColor = ColorTranslator.FromHtml("#1e2329"),
                Dock = DockStyle.None
            };

            // 左侧状态指示器
            var leftStatus = new Panel
            {
                Size = new Size(960, 60),
                Location = new Point(20, 0),
                BackColor = ColorTranslator.FromHtml("#1e2329")
            };

            // 右侧时间显示 - 使用独立的时间显示标签
            _timeDisplayLabel = new Label
            {
                Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 14F),
                Size = new Size(200, 60),
                Location = new Point(1700, 0),
                TextAlign = ContentAlignment.MiddleCenter
            };

            _statusBar.Controls.AddRange(new Control[] { leftStatus, _timeDisplayLabel });
            this.Controls.Add(_statusBar);
        }

        /// <summary>
        /// 创建工业相机显示区域 - 按HTML原型
        /// </summary>
        private void CreateCameraArea()
        {
            _cameraArea = new Panel
            {
                Size = new Size(1020, 460),
                Location = new Point(0, 0),
                BackColor = ColorTranslator.FromHtml("#2d3339"),
                BorderStyle = BorderStyle.FixedSingle
            };

            // 相机1显示区
            var camera1Panel = new Panel
            {
                Size = new Size(500, 440),
                Location = new Point(10, 10),
                BackColor = Color.Black,
                BorderStyle = BorderStyle.FixedSingle
            };

            var camera1Label = new Label
            {
                Text = "定位相机",
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 14F),
                Size = new Size(100, 30),
                Location = new Point(10, 10),
                BackColor = ColorTranslator.FromHtml("#3498db")
            };

            // 相机2显示区
            var camera2Panel = new Panel
            {
                Size = new Size(500, 440),
                Location = new Point(520, 10),
                BackColor = Color.Black,
                BorderStyle = BorderStyle.FixedSingle
            };

            var camera2Label = new Label
            {
                Text = "对位相机",
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 14F),
                Size = new Size(100, 30),
                Location = new Point(10, 10),
                BackColor = ColorTranslator.FromHtml("#3498db")
            };

            camera1Panel.Controls.Add(camera1Label);
            camera2Panel.Controls.Add(camera2Label);
            _cameraArea.Controls.AddRange(new Control[] { camera1Panel, camera2Panel });
            _rightArea.Controls.Add(_cameraArea);
        }

        /// <summary>
        /// 创建控制操作区 - 按HTML原型
        /// </summary>
        private void CreateControlArea()
        {
            _controlArea = new Panel
            {
                Size = new Size(1020, 230),
                Location = new Point(0, 460),
                BackColor = ColorTranslator.FromHtml("#2d3339"),
                BorderStyle = BorderStyle.FixedSingle
            };

            // 控制按钮组
            _startBtn = CreateControlButton("启动", 50, 50, ColorTranslator.FromHtml("#27ae60"));
            _pauseBtn = CreateControlButton("暂停", 200, 50, ColorTranslator.FromHtml("#f39c12"));
            _stopBtn = CreateControlButton("停止", 350, 50, ColorTranslator.FromHtml("#e74c3c"));
            _resetBtn = CreateControlButton("复位", 500, 50, ColorTranslator.FromHtml("#3498db"));

            // 添加按钮事件处理
            _startBtn.Click += StartBtn_Click;
            _pauseBtn.Click += PauseBtn_Click;
            _stopBtn.Click += StopBtn_Click;
            _resetBtn.Click += ResetBtn_Click;

            // 状态指示器
            _statusLabel = new Label
            {
                Text = "系统状态: 待机",
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 16F),
                Size = new Size(200, 30),
                Location = new Point(50, 150),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // 模式指示器
            _modeLabel = new Label
            {
                Text = "当前模式: 手动模式",
                ForeColor = ColorTranslator.FromHtml("#3498db"),
                Font = new Font("微软雅黑", 14F),
                Size = new Size(200, 25),
                Location = new Point(50, 180),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _controlArea.Controls.AddRange(new Control[] { _startBtn, _pauseBtn, _stopBtn, _resetBtn, _statusLabel, _modeLabel });
            _rightArea.Controls.Add(_controlArea);
        }

        /// <summary>
        /// 创建产量统计区 - 按HTML原型
        /// </summary>
        private void CreateStatisticsArea()
        {
            _statisticsArea = new Panel
            {
                Size = new Size(1020, 230),
                Location = new Point(0, 690),
                BackColor = ColorTranslator.FromHtml("#2d3339"),
                BorderStyle = BorderStyle.FixedSingle
            };

            // 统计标签
            var titleLabel = new Label
            {
                Text = "产量统计",
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 18F, FontStyle.Bold),
                Size = new Size(120, 30),
                Location = new Point(20, 20)
            };

            // 统计数据
            var totalLabel = CreateStatLabel("总数量: 0", 50, 70);
            var okLabel = CreateStatLabel("合格数: 0", 250, 70, ColorTranslator.FromHtml("#27ae60"));
            var ngLabel = CreateStatLabel("不良数: 0", 450, 70, ColorTranslator.FromHtml("#e74c3c"));
            var efficiencyLabel = CreateStatLabel("良品率: 0%", 650, 70, ColorTranslator.FromHtml("#3498db"));

            _statisticsArea.Controls.AddRange(new Control[] { titleLabel, totalLabel, okLabel, ngLabel, efficiencyLabel });
            _rightArea.Controls.Add(_statisticsArea);
        }

        /// <summary>
        /// 创建控制按钮
        /// </summary>
        private Button CreateControlButton(string text, int x, int y, Color color)
        {
            var button = new Button
            {
                Text = text,
                Size = new Size(120, 50),
                Location = new Point(x, y),
                BackColor = color,
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 16F, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };

            button.FlatAppearance.BorderSize = 0;
            return button;
        }

        /// <summary>
        /// 创建统计标签
        /// </summary>
        private Label CreateStatLabel(string text, int x, int y, Color? color = null)
        {
            return new Label
            {
                Text = text,
                ForeColor = color ?? Color.White,
                Font = new Font("微软雅黑", 14F),
                Size = new Size(150, 30),
                Location = new Point(x, y),
                TextAlign = ContentAlignment.MiddleLeft
            };
        }



        #endregion

        #region 系统模式管理

        /// <summary>
        /// 初始化系统模式事件
        /// </summary>
        private void InitializeSystemModeEvents()
        {
            try
            {
                // 订阅系统模式变化事件
                SystemModeManager.Instance.ModeChanged += SystemModeManager_ModeChanged;
                SystemModeManager.Instance.AutomationStarted += SystemModeManager_AutomationStarted;
                SystemModeManager.Instance.AutomationCompleted += SystemModeManager_AutomationCompleted;
                SystemModeManager.Instance.AutomationError += SystemModeManager_AutomationError;

                // 初始化界面状态
                UpdateModeDisplay();
            }
            catch (Exception ex)
            {
                LogHelper.Error("初始化系统模式事件失败", ex);
            }
        }

        /// <summary>
        /// 系统模式变化事件处理
        /// </summary>
        private void SystemModeManager_ModeChanged(object sender, MyHMI.Events.SystemModeChangedEventArgs e)
        {
            try
            {
                // 在UI线程中更新界面
                if (InvokeRequired)
                {
                    Invoke(new Action(() => UpdateModeDisplay()));
                }
                else
                {
                    UpdateModeDisplay();
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理系统模式变化事件失败", ex);
            }
        }

        /// <summary>
        /// 自动化开始事件处理
        /// </summary>
        private void SystemModeManager_AutomationStarted(object sender, MyHMI.Events.AutomationStartedEventArgs e)
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(() => {
                        _statusLabel.Text = "系统状态: 自动化运行中...";
                        _statusLabel.ForeColor = ColorTranslator.FromHtml("#27ae60");
                    }));
                }
                else
                {
                    _statusLabel.Text = "系统状态: 自动化运行中...";
                    _statusLabel.ForeColor = ColorTranslator.FromHtml("#27ae60");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理自动化开始事件失败", ex);
            }
        }

        /// <summary>
        /// 自动化完成事件处理
        /// </summary>
        private void SystemModeManager_AutomationCompleted(object sender, MyHMI.Events.AutomationCompletedEventArgs e)
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(() => {
                        _statusLabel.Text = e.IsSuccess ? "系统状态: 自动化完成" : "系统状态: 自动化失败";
                        _statusLabel.ForeColor = e.IsSuccess ? ColorTranslator.FromHtml("#27ae60") : ColorTranslator.FromHtml("#e74c3c");
                    }));
                }
                else
                {
                    _statusLabel.Text = e.IsSuccess ? "系统状态: 自动化完成" : "系统状态: 自动化失败";
                    _statusLabel.ForeColor = e.IsSuccess ? ColorTranslator.FromHtml("#27ae60") : ColorTranslator.FromHtml("#e74c3c");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理自动化完成事件失败", ex);
            }
        }

        /// <summary>
        /// 自动化错误事件处理
        /// </summary>
        private void SystemModeManager_AutomationError(object sender, MyHMI.Events.AutomationErrorEventArgs e)
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(() => {
                        ShowDetailedErrorMessage(e);
                    }));
                }
                else
                {
                    ShowDetailedErrorMessage(e);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理自动化错误事件失败", ex);
            }
        }

        /// <summary>
        /// 显示详细的错误信息
        /// </summary>
        private void ShowDetailedErrorMessage(MyHMI.Events.AutomationErrorEventArgs e)
        {
            try
            {
                // 更新状态显示
                _statusLabel.Text = "系统状态: 模式切换失败";
                _statusLabel.ForeColor = ColorTranslator.FromHtml("#e74c3c");

                // 显示详细的错误对话框
                string title = "系统模式切换失败";
                string message = e.ErrorMessage;

                // 如果错误信息包含换行符，说明是详细错误信息
                if (message.Contains("\n"))
                {
                    // 使用多行消息框显示详细信息
                    MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
                else
                {
                    // 简单错误信息
                    MessageBox.Show($"模式切换失败：{message}\n\n请检查系统状态和硬件连接。",
                        title, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }

                // 更新模式显示
                UpdateModeDisplay();
            }
            catch (Exception ex)
            {
                LogHelper.Error("显示错误信息时发生异常", ex);
                // 备用简单错误显示
                MessageBox.Show("系统发生错误，请查看日志文件获取详细信息。", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 更新模式显示
        /// </summary>
        private void UpdateModeDisplay()
        {
            try
            {
                var currentMode = SystemModeManager.Instance.CurrentMode;
                var isTransitioning = SystemModeManager.Instance.IsTransitioning;

                if (_modeLabel != null)
                {
                    _modeLabel.Text = $"当前模式: {(currentMode == SystemMode.Manual ? "手动模式" : "自动模式")}";
                    _modeLabel.ForeColor = currentMode == SystemMode.Manual ?
                        ColorTranslator.FromHtml("#3498db") : ColorTranslator.FromHtml("#27ae60");
                }

                if (_startBtn != null)
                {
                    // 只有在自动模式下启动按钮才可用
                    _startBtn.Enabled = !isTransitioning && currentMode == SystemMode.Automatic;
                    _startBtn.Text = currentMode == SystemMode.Automatic ? "启动" : "启动(需自动模式)";
                }

                if (_pauseBtn != null)
                {
                    _pauseBtn.Enabled = !isTransitioning && currentMode == SystemMode.Automatic;
                }

                if (_stopBtn != null)
                {
                    _stopBtn.Enabled = !isTransitioning;
                }

                if (_resetBtn != null)
                {
                    _resetBtn.Enabled = !isTransitioning && currentMode == SystemMode.Manual;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("更新模式显示失败", ex);
            }
        }

        #endregion

        #region 控制按钮事件处理

        /// <summary>
        /// 启动按钮点击事件 - 只在自动模式下启动自动化流程
        /// </summary>
        private async void StartBtn_Click(object sender, EventArgs e)
        {
            try
            {
                var currentMode = SystemModeManager.Instance.CurrentMode;

                // 检查是否在自动模式下
                if (currentMode != SystemMode.Automatic)
                {
                    MessageBox.Show("启动功能只能在自动模式下使用。\n\n请先通过\"安全设置\"切换到自动模式。",
                        "模式错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                _statusLabel.Text = "系统状态: 正在启动自动化流程...";
                _statusLabel.ForeColor = ColorTranslator.FromHtml("#f39c12");

                // 在自动模式下启动自动化流程（不切换模式）
                bool result = await SystemModeManager.Instance.StartAutomationAsync();

                if (result)
                {
                    _statusLabel.Text = "系统状态: 自动化流程运行中";
                    _statusLabel.ForeColor = ColorTranslator.FromHtml("#27ae60");
                }
                else
                {
                    _statusLabel.Text = "系统状态: 启动失败";
                    _statusLabel.ForeColor = ColorTranslator.FromHtml("#e74c3c");
                    MessageBox.Show("自动化流程启动失败，请检查系统状态", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("启动按钮点击处理失败", ex);
                _statusLabel.Text = "系统状态: 启动异常";
                _statusLabel.ForeColor = ColorTranslator.FromHtml("#e74c3c");
                MessageBox.Show($"启动异常：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 暂停按钮点击事件
        /// </summary>
        private async void PauseBtn_Click(object sender, EventArgs e)
        {
            try
            {
                var currentMode = SystemModeManager.Instance.CurrentMode;

                // 检查是否在自动模式下
                if (currentMode != SystemMode.Automatic)
                {
                    MessageBox.Show("暂停功能只能在自动模式下使用。", "模式错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                _statusLabel.Text = "系统状态: 正在暂停...";
                _statusLabel.ForeColor = ColorTranslator.FromHtml("#f39c12");

                // 暂停工作流
                bool result = await WorkflowManager.Instance.PauseWorkflowAsync();

                if (result)
                {
                    _statusLabel.Text = "系统状态: 已暂停";
                    _statusLabel.ForeColor = ColorTranslator.FromHtml("#f39c12");
                    LogHelper.Info("工作流暂停成功");
                }
                else
                {
                    _statusLabel.Text = "系统状态: 暂停失败";
                    _statusLabel.ForeColor = ColorTranslator.FromHtml("#e74c3c");
                    MessageBox.Show("工作流暂停失败，请检查系统状态", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("暂停按钮点击处理失败", ex);
                _statusLabel.Text = "系统状态: 暂停异常";
                _statusLabel.ForeColor = ColorTranslator.FromHtml("#e74c3c");
                MessageBox.Show($"暂停异常：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 停止按钮点击事件
        /// </summary>
        private async void StopBtn_Click(object sender, EventArgs e)
        {
            try
            {
                _statusLabel.Text = "系统状态: 正在停止...";
                _statusLabel.ForeColor = ColorTranslator.FromHtml("#f39c12");

                // 首先停止工作流
                bool workflowStopResult = await WorkflowManager.Instance.StopWorkflowAsync();
                if (!workflowStopResult)
                {
                    LogHelper.Warning("工作流停止失败，但继续执行模式切换");
                }

                // 然后切换回手动模式
                bool modeResult = await SystemModeManager.Instance.SwitchToManualModeAsync();

                if (modeResult)
                {
                    _statusLabel.Text = "系统状态: 已停止";
                    _statusLabel.ForeColor = Color.White;
                    LogHelper.Info("系统停止成功");
                }
                else
                {
                    _statusLabel.Text = "系统状态: 停止失败";
                    _statusLabel.ForeColor = ColorTranslator.FromHtml("#e74c3c");
                    MessageBox.Show("系统停止失败，请检查系统状态", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("停止按钮点击处理失败", ex);
                _statusLabel.Text = "系统状态: 停止异常";
                _statusLabel.ForeColor = ColorTranslator.FromHtml("#e74c3c");
                MessageBox.Show($"停止异常：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 复位按钮点击事件
        /// </summary>
        private async void ResetBtn_Click(object sender, EventArgs e)
        {
            try
            {
                // 确认对话框
                var result = MessageBox.Show(
                    "确定要复位系统吗？\n\n复位操作将：\n" +
                    "1. 停止所有正在运行的流程\n" +
                    "2. 重置工作流状态\n" +
                    "3. 切换到手动模式\n" +
                    "4. 重置所有控制器状态",
                    "确认复位", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    _statusLabel.Text = "系统状态: 正在复位...";
                    _statusLabel.ForeColor = ColorTranslator.FromHtml("#f39c12");

                    // 1. 重置工作流
                    bool workflowResetResult = await WorkflowManager.Instance.ResetWorkflowAsync();
                    if (!workflowResetResult)
                    {
                        LogHelper.Warning("工作流重置失败，但继续执行其他复位操作");
                    }

                    // 2. 切换到手动模式
                    bool modeResult = await SystemModeManager.Instance.SwitchToManualModeAsync();
                    if (!modeResult)
                    {
                        LogHelper.Warning("模式切换失败，但继续执行其他复位操作");
                    }

                    // 3. 重置皮带电机控制器
                    try
                    {
                        await BeltMotorAutoModeController.Instance.ResetAsync();
                        LogHelper.Info("皮带电机控制器重置完成");
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error("皮带电机控制器重置失败", ex);
                    }

                    if (workflowResetResult && modeResult)
                    {
                        _statusLabel.Text = "系统状态: 复位完成";
                        _statusLabel.ForeColor = Color.White;
                        LogHelper.Info("系统复位成功");
                        MessageBox.Show("系统复位完成", "复位成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        _statusLabel.Text = "系统状态: 复位部分失败";
                        _statusLabel.ForeColor = ColorTranslator.FromHtml("#f39c12");
                        MessageBox.Show("系统复位部分失败，请检查日志", "复位警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("复位按钮点击处理失败", ex);
                _statusLabel.Text = "系统状态: 复位异常";
                _statusLabel.ForeColor = ColorTranslator.FromHtml("#e74c3c");
                MessageBox.Show($"复位异常：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 显示模式选择对话框
        /// </summary>
        private void ShowModeSelectionDialog()
        {
            try
            {
                var currentMode = SystemModeManager.Instance.CurrentMode;
                var isTransitioning = SystemModeManager.Instance.IsTransitioning;

                if (isTransitioning)
                {
                    MessageBox.Show("系统正在模式切换中，请稍后再试", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var result = MessageBox.Show(
                    $"当前模式：{(currentMode == SystemMode.Manual ? "调试模式（手动模式）" : "自动模式")}\n\n" +
                    "请选择要切换的模式：\n\n" +
                    "是(Y) - 切换到自动模式\n" +
                    "否(N) - 切换到调试模式（手动模式）\n" +
                    "取消 - 保持当前模式",
                    "模式选择",
                    MessageBoxButtons.YesNoCancel,
                    MessageBoxIcon.Question);

                switch (result)
                {
                    case DialogResult.Yes:
                        // 切换到自动模式
                        if (currentMode != SystemMode.Automatic)
                        {
                            SwitchToAutomaticModeAsync();
                        }
                        break;
                    case DialogResult.No:
                        // 切换到手动模式
                        if (currentMode != SystemMode.Manual)
                        {
                            SwitchToManualModeAsync();
                        }
                        break;
                    case DialogResult.Cancel:
                        // 不做任何操作
                        break;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("显示模式选择对话框失败", ex);
                MessageBox.Show($"模式选择失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 异步切换到自动模式
        /// </summary>
        private async void SwitchToAutomaticModeAsync()
        {
            try
            {
                _statusLabel.Text = "系统状态: 正在切换到自动模式...";
                _statusLabel.ForeColor = ColorTranslator.FromHtml("#f39c12");

                bool result = await SystemModeManager.Instance.SwitchToAutomaticModeAsync();

                if (result)
                {
                    _statusLabel.Text = "系统状态: 自动模式";
                    _statusLabel.ForeColor = ColorTranslator.FromHtml("#27ae60");
                    MessageBox.Show("已成功切换到自动模式", "模式切换", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    // 不在这里显示错误信息，等待AutomationError事件处理详细错误信息
                    // 如果没有AutomationError事件，说明是简单的返回false情况
                    LogHelper.Warning("模式切换返回false，但没有异常信息");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("切换到自动模式失败", ex);
                _statusLabel.Text = "系统状态: 模式切换异常";
                _statusLabel.ForeColor = ColorTranslator.FromHtml("#e74c3c");
                MessageBox.Show($"切换到自动模式异常：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 异步切换到手动模式
        /// </summary>
        private async void SwitchToManualModeAsync()
        {
            try
            {
                _statusLabel.Text = "系统状态: 正在切换到手动模式...";
                _statusLabel.ForeColor = ColorTranslator.FromHtml("#f39c12");

                bool result = await SystemModeManager.Instance.SwitchToManualModeAsync();

                if (result)
                {
                    _statusLabel.Text = "系统状态: 手动模式";
                    _statusLabel.ForeColor = ColorTranslator.FromHtml("#3498db");
                    MessageBox.Show("已成功切换到调试模式（手动模式）", "模式切换", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    _statusLabel.Text = "系统状态: 模式切换失败";
                    _statusLabel.ForeColor = ColorTranslator.FromHtml("#e74c3c");
                    MessageBox.Show("切换到手动模式失败，请检查系统状态", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("切换到手动模式失败", ex);
                _statusLabel.Text = "系统状态: 模式切换异常";
                _statusLabel.ForeColor = ColorTranslator.FromHtml("#e74c3c");
                MessageBox.Show($"切换到手动模式异常：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 程序生命周期管理

        // 资源释放标志
        private bool _isDisposing = false;

        /// <summary>
        /// 程序关闭时的资源释放处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private async void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                LogHelper.Info("程序正在关闭，开始释放所有Manager资源...");

                // 显示关闭进度（可选）
                this.Text = "正在关闭程序，请稍候...";

                // 暂时取消关闭，等待资源释放完成
                if (!_isDisposing)
                {
                    e.Cancel = true;
                    _isDisposing = true;

                    // 在后台线程中执行资源释放
                    await Task.Run(async () =>
                    {
                        // 保存Settings系统配置
                        try
                        {
                            LogHelper.Info("保存Settings系统配置...");
                            Settings.Settings.Save();
                            LogHelper.Info("Settings系统配置保存完成");
                        }
                        catch (Exception settingsEx)
                        {
                            LogHelper.Error("保存Settings系统配置失败", settingsEx);
                        }

                        await DisposeAllManagersAsync();
                    });

                    // 资源释放完成后，真正关闭程序
                    this.Invoke(new Action(() =>
                    {
                        LogHelper.Info("所有资源释放完成，程序即将退出");
                        Application.Exit();
                    }));
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("程序关闭时发生异常", ex);
                // 即使发生异常，也要确保程序能够退出
                Application.Exit();
            }
        }

        /// <summary>
        /// 释放所有Manager资源
        /// </summary>
        /// <returns></returns>
        private async Task DisposeAllManagersAsync()
        {
            try
            {
                LogHelper.Info("开始按顺序释放各个Manager资源...");

                // 0. 清理UI资源和面板缓存
                try
                {
                    LogHelper.Info("清理UI资源和面板缓存...");

                    // 停止并释放时间更新定时器
                    DisposeTimeUpdateTimer();

                    // 清理面板缓存，释放所有缓存的面板
                    await ClearPanelCacheAsync();

                    LogHelper.Info("UI资源清理完成");
                }
                catch (Exception ex)
                {
                    LogHelper.Error("清理UI资源异常", ex);
                }

                // 1. 首先停止工作流管理器（包含皮带电机控制）
                try
                {
                    LogHelper.Info("释放工作流管理器资源...");
                    await WorkflowManager.Instance.DisposeAsync();
                    LogHelper.Info("工作流管理器资源释放完成");
                }
                catch (Exception ex)
                {
                    LogHelper.Error("工作流管理器资源释放失败", ex);
                }

                // 2. 释放启动自检管理器资源
                try
                {
                    LogHelper.Info("释放启动自检管理器资源...");
                    await StartupSelfCheckManager.Instance.DisposeAsync();
                    LogHelper.Info("启动自检管理器资源释放完成");
                }
                catch (Exception ex)
                {
                    LogHelper.Error("启动自检管理器资源释放失败", ex);
                }

                // 3. 释放电机管理器资源（不再管理控制卡）
                try
                {
                    LogHelper.Info("释放DMC1000B电机管理器资源...");
                    await DMC1000BMotorManager.Instance.DisposeAsync();
                    LogHelper.Info("DMC1000B电机管理器资源释放完成");
                }
                catch (Exception ex)
                {
                    LogHelper.Error("DMC1000B电机管理器资源释放失败", ex);
                }

                // 4. 设置IO安全状态并释放IO管理器资源
                try
                {
                    LogHelper.Info("设置IO安全状态并释放DMC1000BIO管理器资源...");

                    // 先设置所有输出IO到安全状态
                    if (DMC1000BIOManager.Instance.IsInitialized)
                    {
                        LogHelper.Info("程序关闭时设置IO安全状态...");
                        bool safeStateResult = await DMC1000BIOManager.Instance.SetAllOutputsToSafeStateAsync();
                        if (safeStateResult)
                        {
                            LogHelper.Info("IO安全状态设置成功");
                        }
                        else
                        {
                            LogHelper.Warning("IO安全状态设置部分失败，但继续关闭程序");
                        }
                    }

                    // 然后释放IO管理器资源
                    await DMC1000BIOManager.Instance.ReleaseAsync();
                    LogHelper.Info("DMC1000BIO管理器资源释放完成");
                }
                catch (Exception ex)
                {
                    LogHelper.Error("设置IO安全状态或释放IO管理器资源失败", ex);
                }

                // 5. 释放其他Manager资源
                try
                {
                    LogHelper.Info("释放其他Manager资源...");

                    await MotorManager.Instance.DisposeAsync();
                    await ScannerManager.Instance.DisposeAsync();
                    // 跳过Modbus TCP资源释放 - 由第三方控制
                    // await ModbusTcpManager.Instance.DisposeAsync();
                    await EpsonRobotManager.Instance.DisposeAsync();
                    await EpsonRobotManager2.Instance.DisposeAsync();
                    await VisionManager.Instance.DisposeAsync();
                    await StatisticsManager.Instance.DisposeAsync();

                    LogHelper.Info("其他Manager资源释放完成");
                }
                catch (Exception ex)
                {
                    LogHelper.Error("其他Manager资源释放失败", ex);
                }

                // 6. 最后释放DMC1000B控制卡资源（统一管理）
                try
                {
                    LogHelper.Info("释放DMC1000B控制卡资源...");
                    await DMC1000BCardManager.Instance.ReleaseCardAsync();
                    LogHelper.Info("DMC1000B控制卡资源释放完成");
                }
                catch (Exception ex)
                {
                    LogHelper.Error("释放DMC1000B控制卡资源失败", ex);
                }

                // 释放SCARA模拟器资源
                try
                {
                    if (_scaraSimulatorForm != null && !_scaraSimulatorForm.IsDisposed)
                    {
                        LogHelper.Info("释放SCARA模拟器资源...");
                        _scaraSimulatorForm.DisposeSimulator();
                        _scaraSimulatorForm.Close();
                        _scaraSimulatorForm = null;
                        LogHelper.Info("SCARA模拟器资源释放完成");
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Error("释放SCARA模拟器资源失败", ex);
                }

                LogHelper.Info("所有Manager资源释放流程完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("释放Manager资源过程中发生异常", ex);
            }
        }

        #endregion

        #region 时间显示独立更新机制

        /// <summary>
        /// 初始化时间更新定时器 - 独立的1秒定时器更新时间显示
        /// </summary>
        private void InitializeTimeUpdateTimer()
        {
            try
            {
                // 创建时间更新定时器
                _timeUpdateTimer = new System.Windows.Forms.Timer
                {
                    Interval = 1000, // 1秒更新一次
                    Enabled = false  // 初始不启动，等界面创建完成后启动
                };

                // 绑定定时器事件
                _timeUpdateTimer.Tick += TimeUpdateTimer_Tick;

                LogHelper.Info("时间更新定时器初始化完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("初始化时间更新定时器失败", ex);
            }
        }

        /// <summary>
        /// 时间更新定时器事件处理器 - 每秒更新一次时间显示
        /// </summary>
        private void TimeUpdateTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                // 确保在UI线程中执行
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() => TimeUpdateTimer_Tick(sender, e)));
                    return;
                }

                // 更新时间显示
                if (_timeDisplayLabel != null)
                {
                    _timeDisplayLabel.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("时间更新定时器事件处理失败", ex);
            }
        }

        /// <summary>
        /// 启动时间更新定时器
        /// </summary>
        private void StartTimeUpdateTimer()
        {
            try
            {
                if (_timeUpdateTimer != null && !_timeUpdateTimer.Enabled)
                {
                    _timeUpdateTimer.Start();
                    LogHelper.Info("时间更新定时器已启动");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("启动时间更新定时器失败", ex);
            }
        }

        /// <summary>
        /// 停止时间更新定时器
        /// </summary>
        private void StopTimeUpdateTimer()
        {
            try
            {
                if (_timeUpdateTimer != null && _timeUpdateTimer.Enabled)
                {
                    _timeUpdateTimer.Stop();
                    LogHelper.Info("时间更新定时器已停止");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("停止时间更新定时器失败", ex);
            }
        }

        /// <summary>
        /// 释放时间更新定时器资源
        /// </summary>
        private void DisposeTimeUpdateTimer()
        {
            try
            {
                if (_timeUpdateTimer != null)
                {
                    _timeUpdateTimer.Stop();
                    _timeUpdateTimer.Tick -= TimeUpdateTimer_Tick;
                    _timeUpdateTimer.Dispose();
                    _timeUpdateTimer = null;
                    LogHelper.Info("时间更新定时器资源已释放");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("释放时间更新定时器资源失败", ex);
            }
        }

        #endregion

        #region 面板缓存管理方法

        /// <summary>
        /// 清理面板缓存 - 释放所有缓存的面板资源
        /// </summary>
        private async Task ClearPanelCacheAsync()
        {
            try
            {
                LogHelper.Info($"开始清理面板缓存，共{_panelCache.Count}个面板");

                // 在UI线程中执行面板清理
                if (this.InvokeRequired)
                {
                    await Task.Run(() =>
                    {
                        this.Invoke(new Action(() => ClearPanelCacheSync()));
                    });
                }
                else
                {
                    ClearPanelCacheSync();
                }

                LogHelper.Info("面板缓存清理完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("清理面板缓存失败", ex);
            }
        }

        /// <summary>
        /// 同步清理面板缓存 - 在UI线程中执行
        /// </summary>
        private void ClearPanelCacheSync()
        {
            try
            {
                // 隐藏当前面板
                if (_currentPanel != null)
                {
                    _currentPanel.Visible = false;
                    _currentPanel = null;
                }

                // 逐个释放缓存的面板
                foreach (var kvp in _panelCache)
                {
                    try
                    {
                        var panel = kvp.Value;
                        if (panel != null)
                        {
                            // 从父容器中移除
                            if (panel.Parent != null)
                            {
                                panel.Parent.Controls.Remove(panel);
                            }

                            // 释放面板资源
                            panel.Dispose();
                            LogHelper.Debug($"释放面板: {kvp.Key}");
                        }
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Warning($"释放面板 {kvp.Key} 时发生异常: {ex.Message}");
                    }
                }

                // 清空缓存字典
                _panelCache.Clear();
                LogHelper.Info("面板缓存字典已清空");
            }
            catch (Exception ex)
            {
                LogHelper.Error("同步清理面板缓存失败", ex);
            }
        }

        /// <summary>
        /// 获取面板缓存状态信息
        /// </summary>
        /// <returns>缓存状态信息</returns>
        public string GetPanelCacheStatus()
        {
            try
            {
                var status = new StringBuilder();
                status.AppendLine("=== 面板缓存状态 ===");
                status.AppendLine($"缓存面板数量: {_panelCache.Count}");
                status.AppendLine($"当前显示面板: {(_currentPanel?.Name ?? "无")}");
                status.AppendLine($"当前菜单类型: {_currentMenuType}");

                if (_panelCache.Count > 0)
                {
                    status.AppendLine("缓存的面板列表:");
                    foreach (var kvp in _panelCache)
                    {
                        var panel = kvp.Value;
                        status.AppendLine($"  - {kvp.Key}: {panel?.GetType().Name ?? "null"} (可见: {panel?.Visible ?? false})");
                    }
                }

                return status.ToString();
            }
            catch (Exception ex)
            {
                LogHelper.Error("获取面板缓存状态失败", ex);
                return $"获取面板缓存状态失败: {ex.Message}";
            }
        }

        #endregion

        #region 外部设备状态菜单方法

        /// <summary>
        /// 显示外部设备状态菜单
        /// </summary>
        private void ShowDeviceStatusMenu()
        {
            try
            {
                // 创建上下文菜单
                var contextMenu = new ContextMenuStrip();

                // 添加SCARA机器人模拟器菜单项
                var scaraSimulatorMenuItem = new ToolStripMenuItem("SCARA机器人模拟器");
                scaraSimulatorMenuItem.Click += (s, e) => ShowScaraRobotSimulator();
                contextMenu.Items.Add(scaraSimulatorMenuItem);

                // 可以在这里添加其他外部设备状态菜单项
                // var otherDeviceMenuItem = new ToolStripMenuItem("其他设备");
                // contextMenu.Items.Add(otherDeviceMenuItem);

                // 显示菜单
                contextMenu.Show(_deviceStatusBtn, new Point(0, _deviceStatusBtn.Height));

                LogHelper.Info("外部设备状态菜单已显示");
            }
            catch (Exception ex)
            {
                LogHelper.Error("显示外部设备状态菜单失败", ex);
                MessageBox.Show($"显示外部设备状态菜单失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 显示SCARA机器人模拟器
        /// </summary>
        private void ShowScaraRobotSimulator()
        {
            try
            {
                // 如果模拟器窗体不存在，创建新的
                if (_scaraSimulatorForm == null || _scaraSimulatorForm.IsDisposed)
                {
                    _scaraSimulatorForm = new ScaraRobotSimulatorForm();
                    LogHelper.Info("SCARA模拟器窗体已创建");
                }

                // 显示并刷新模拟器
                _scaraSimulatorForm.ShowSimulatorAndRefresh();
                LogHelper.Info("SCARA机器人模拟器已显示");
            }
            catch (Exception ex)
            {
                LogHelper.Error("显示SCARA机器人模拟器失败", ex);
                MessageBox.Show($"显示SCARA机器人模拟器失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 架构相关方法 - 事件驱动架构

        /// <summary>
        /// 获取事件驱动架构状态
        /// </summary>
        /// <returns>架构状态信息</returns>
        public string GetArchitectureStatus()
        {
            try
            {
                var status = new StringBuilder();
                status.AppendLine("=== 事件驱动UI架构状态 ===");
                status.AppendLine($"架构类型: 事件驱动架构");
                status.AppendLine($"状态检查时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                status.AppendLine($"IO管理器: {(DMC1000BIOManager.Instance.IsInitialized ? "已初始化" : "未初始化")}");
                status.AppendLine($"电机管理器: {(DMC1000BMotorManager.Instance.IsInitialized ? "已初始化" : "未初始化")}");
                status.AppendLine($"系统模式: {SystemModeManager.Instance.CurrentMode}");

                return status.ToString();
            }
            catch (Exception ex)
            {
                LogHelper.Error("获取架构状态失败", ex);
                return $"架构状态获取失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 检查事件驱动架构健康状态
        /// </summary>
        /// <returns>健康状态检查结果</returns>
        public async Task<bool> CheckArchitectureHealthAsync()
        {
            try
            {
                LogHelper.Info("检查事件驱动架构健康状态");

                // 检查核心管理器状态
                bool ioManagerHealthy = DMC1000BIOManager.Instance.IsInitialized;
                bool motorManagerHealthy = DMC1000BMotorManager.Instance.IsInitialized;
                bool systemModeHealthy = SystemModeManager.Instance != null;

                var healthyComponents = 0;
                var totalComponents = 3;

                if (ioManagerHealthy) healthyComponents++;
                if (motorManagerHealthy) healthyComponents++;
                if (systemModeHealthy) healthyComponents++;

                var healthPercentage = (double)healthyComponents / totalComponents;

                LogHelper.Info($"事件驱动架构健康状态: {healthyComponents}/{totalComponents} ({healthPercentage:P0})");

                return healthPercentage >= 0.67; // 67%以上组件健康才认为系统健康
            }
            catch (Exception ex)
            {
                LogHelper.Error("检查架构健康状态失败", ex);
                return false;
            }
        }

        #endregion
    }
}
