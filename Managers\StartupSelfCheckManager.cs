using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using MyHMI.Helpers;

namespace MyHMI.Managers
{
    /// <summary>
    /// 开机自检管理器
    /// 负责在系统进入自动模式时执行完整的开机自检流程
    /// 包括运动控制卡初始化、翻转电机归零、双机器人连接等
    /// </summary>
    public class StartupSelfCheckManager
    {
        #region 单例模式
        private static readonly Lazy<StartupSelfCheckManager> _instance = new Lazy<StartupSelfCheckManager>(() => new StartupSelfCheckManager());
        public static StartupSelfCheckManager Instance => _instance.Value;
        private StartupSelfCheckManager() { }
        #endregion

        #region 私有字段
        private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);
        private bool _isRunning = false;

        /// <summary>
        /// 开机自检结果
        /// </summary>
        public class SelfCheckResult
        {
            public bool OverallSuccess { get; set; }
            public bool DMC1000BCardSuccess { get; set; }
            public bool MotorManagerSuccess { get; set; }
            public bool IOManagerSuccess { get; set; }
            public bool LeftFlipMotorSuccess { get; set; }
            public bool RightFlipMotorSuccess { get; set; }
            public bool Robot1Success { get; set; }
            public bool Robot2Success { get; set; }
            public bool ScannerConnectionSuccess { get; set; }
            public bool BeltMotorAutoControlSuccess { get; set; }
            public List<string> WarningMessages { get; set; } = new List<string>();
            public List<string> ErrorMessages { get; set; } = new List<string>();
        }

        private SelfCheckResult _lastResult = null;
        #endregion

        #region 公共属性
        /// <summary>
        /// 开机自检是否正在运行
        /// </summary>
        public bool IsRunning => _isRunning;

        /// <summary>
        /// 获取最后一次开机自检结果
        /// </summary>
        public SelfCheckResult LastResult => _lastResult;
        #endregion

        #region 开机自检流程
        /// <summary>
        /// 执行完整的开机自检流程
        /// 支持脱机测试：硬件未连接时记录警告但不阻止流程继续
        /// </summary>
        /// <returns>自检结果</returns>
        public async Task<SelfCheckResult> ExecuteStartupSelfCheckAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync<SelfCheckResult>(async () =>
            {
                // 使用信号量确保线程安全
                if (!await _semaphore.WaitAsync(100))
                {
                    LogHelper.Warning("开机自检已在运行中，跳过重复执行");
                    return _lastResult ?? new SelfCheckResult { OverallSuccess = false };
                }

                _isRunning = true;
                var result = new SelfCheckResult();

                try
                {
                    LogHelper.Info("========== 开始执行开机自检流程 ==========");
                    LogHelper.Info("支持脱机测试模式：硬件未连接时将记录警告但继续执行");

                    // 步骤1：运动控制卡初始化
                    await InitializeDMC1000BCardAsync(result);

                    // 步骤2：翻转电机自动归零
                    await ExecuteFlipMotorHomingAsync(result);

                    // 步骤3：双机器人自动连接
                    await ConnectDualRobotsAsync(result);

                    // 步骤4：扫码器连接验证
                    await VerifyScannersConnectionAsync(result);

                    // 步骤5：启动皮带电机自动控制
                    await StartBeltMotorAutoControlAsync(result);

                    // 评估整体结果
                    result.OverallSuccess = EvaluateOverallResult(result);

                    LogHelper.Info("========== 开机自检流程执行完成 ==========");
                    LogSelfCheckSummary(result);

                    _lastResult = result;
                    return result;
                }
                finally
                {
                    _isRunning = false;
                    _semaphore.Release();
                }
            }, new SelfCheckResult { OverallSuccess = false }, "执行开机自检流程");
        }

        /// <summary>
        /// 初始化DMC1000B运动控制卡
        /// </summary>
        /// <param name="result">自检结果对象</param>
        private async Task InitializeDMC1000BCardAsync(SelfCheckResult result)
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info("步骤1：开始初始化DMC1000B运动控制卡...");

                // 检查控制卡状态
                try
                {
                    var cardManager = DMC1000BCardManager.Instance;

                    // 检查控制卡是否可用
                    if (cardManager.IsCardAvailable())
                    {
                        result.DMC1000BCardSuccess = true;
                        LogHelper.Info("DMC1000B控制卡状态检查成功");
                    }
                    else
                    {
                        result.DMC1000BCardSuccess = false;
                        result.WarningMessages.Add("DMC1000B控制卡不可用（可能硬件未连接）");
                        LogHelper.Warning("DMC1000B控制卡不可用（可能硬件未连接）");
                    }
                }
                catch (Exception ex)
                {
                    result.DMC1000BCardSuccess = false;
                    result.ErrorMessages.Add($"DMC1000B控制卡检查异常：{ex.Message}");
                    LogHelper.Error("DMC1000B控制卡检查异常", ex);
                }

                // 初始化电机管理器
                try
                {
                    var motorManager = DMC1000BMotorManager.Instance;
                    if (!motorManager.IsInitialized)
                    {
                        LogHelper.Info("初始化DMC1000B电机管理器...");
                        bool motorManagerResult = await motorManager.InitializeAsync();
                        if (!motorManagerResult)
                        {
                            result.MotorManagerSuccess = false;
                            result.WarningMessages.Add("DMC1000B电机管理器初始化失败（可能硬件未连接）");
                            LogHelper.Warning("DMC1000B电机管理器初始化失败（可能硬件未连接）");
                        }
                        else
                        {
                            result.MotorManagerSuccess = true;
                            LogHelper.Info("DMC1000B电机管理器初始化成功");
                        }
                    }
                    else
                    {
                        result.MotorManagerSuccess = true;
                        LogHelper.Info("DMC1000B电机管理器已初始化");
                    }
                }
                catch (Exception ex)
                {
                    result.MotorManagerSuccess = false;
                    result.ErrorMessages.Add($"DMC1000B电机管理器初始化失败：{ex.Message}");
                    LogHelper.Error("DMC1000B电机管理器初始化失败", ex);
                }

                // 初始化IO管理器
                try
                {
                    var ioManager = DMC1000BIOManager.Instance;
                    if (!ioManager.IsInitialized)
                    {
                        LogHelper.Info("初始化DMC1000B IO管理器...");
                        bool ioManagerResult = await ioManager.InitializeAsync();
                        if (!ioManagerResult)
                        {
                            result.IOManagerSuccess = false;
                            result.WarningMessages.Add("DMC1000B IO管理器初始化失败（可能硬件未连接）");
                            LogHelper.Warning("DMC1000B IO管理器初始化失败（可能硬件未连接）");
                        }
                        else
                        {
                            result.IOManagerSuccess = true;
                            LogHelper.Info("DMC1000B IO管理器初始化成功");
                        }
                    }
                    else
                    {
                        result.IOManagerSuccess = true;
                        LogHelper.Info("DMC1000B IO管理器已初始化");
                    }
                }
                catch (Exception ex)
                {
                    result.IOManagerSuccess = false;
                    result.ErrorMessages.Add($"DMC1000B IO管理器初始化失败：{ex.Message}");
                    LogHelper.Error("DMC1000B IO管理器初始化失败", ex);
                }

                LogHelper.Info("DMC1000B运动控制卡初始化流程完成");

            }, "初始化DMC1000B运动控制卡");
        }

        /// <summary>
        /// 执行翻转电机自动归零流程
        /// </summary>
        /// <param name="result">自检结果对象</param>
        private async Task ExecuteFlipMotorHomingAsync(SelfCheckResult result)
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info("步骤2：开始执行翻转电机自动归零...");

                var motorManager = DMC1000BMotorManager.Instance;
                if (!motorManager.IsInitialized)
                {
                    result.LeftFlipMotorSuccess = false;
                    result.RightFlipMotorSuccess = false;
                    result.WarningMessages.Add("电机管理器未初始化，跳过翻转电机归零操作（脱机测试模式）");
                    LogHelper.Warning("电机管理器未初始化，跳过翻转电机归零操作（脱机测试模式）");
                    return;
                }

                // 检查控制卡是否可用
                if (!DMC1000BCardManager.Instance.IsCardAvailable())
                {
                    result.LeftFlipMotorSuccess = false;
                    result.RightFlipMotorSuccess = false;
                    result.WarningMessages.Add("DMC1000B控制卡不可用，跳过翻转电机归零操作（脱机测试模式）");
                    LogHelper.Warning("DMC1000B控制卡不可用，跳过翻转电机归零操作（脱机测试模式）");
                    return;
                }

                // 左翻转电机归零并移动到位置1
                try
                {
                    LogHelper.Info("执行左翻转电机归零操作...");
                    bool leftHomeResult = await motorManager.FlipMotorHomeAsync(0); // 轴0为左翻转电机
                    if (leftHomeResult)
                    {
                        LogHelper.Info("左翻转电机归零成功，移动到位置1...");
                        bool leftMoveResult = await motorManager.MoveToFlipMotorPositionAsync(0, 1);
                        if (leftMoveResult)
                        {
                            result.LeftFlipMotorSuccess = true;
                            LogHelper.Info("左翻转电机移动到位置1成功");
                        }
                        else
                        {
                            result.LeftFlipMotorSuccess = false;
                            result.WarningMessages.Add("左翻转电机移动到位置1失败");
                            LogHelper.Warning("左翻转电机移动到位置1失败");
                        }
                    }
                    else
                    {
                        result.LeftFlipMotorSuccess = false;
                        result.WarningMessages.Add("左翻转电机归零失败");
                        LogHelper.Warning("左翻转电机归零失败");
                    }
                }
                catch (Exception ex)
                {
                    result.LeftFlipMotorSuccess = false;
                    result.ErrorMessages.Add($"左翻转电机操作异常：{ex.Message}");
                    LogHelper.Error("左翻转电机操作异常", ex);
                }

                // 右翻转电机归零并移动到位置1
                try
                {
                    LogHelper.Info("执行右翻转电机归零操作...");
                    bool rightHomeResult = await motorManager.FlipMotorHomeAsync(1); // 轴1为右翻转电机
                    if (rightHomeResult)
                    {
                        LogHelper.Info("右翻转电机归零成功，移动到位置1...");
                        bool rightMoveResult = await motorManager.MoveToFlipMotorPositionAsync(1, 1);
                        if (rightMoveResult)
                        {
                            result.RightFlipMotorSuccess = true;
                            LogHelper.Info("右翻转电机移动到位置1成功");
                        }
                        else
                        {
                            result.RightFlipMotorSuccess = false;
                            result.WarningMessages.Add("右翻转电机移动到位置1失败");
                            LogHelper.Warning("右翻转电机移动到位置1失败");
                        }
                    }
                    else
                    {
                        result.RightFlipMotorSuccess = false;
                        result.WarningMessages.Add("右翻转电机归零失败");
                        LogHelper.Warning("右翻转电机归零失败");
                    }
                }
                catch (Exception ex)
                {
                    result.RightFlipMotorSuccess = false;
                    result.ErrorMessages.Add($"右翻转电机操作异常：{ex.Message}");
                    LogHelper.Error("右翻转电机操作异常", ex);
                }

                LogHelper.Info("翻转电机自动归零流程完成");

            }, "执行翻转电机自动归零");
        }

        /// <summary>
        /// 连接双机器人
        /// </summary>
        /// <param name="result">自检结果对象</param>
        private async Task ConnectDualRobotsAsync(SelfCheckResult result)
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info("步骤3：开始连接双机器人...");

                // 并行连接两台机器人以提高效率
                var robot1Task = ConnectSingleRobotAsync(EpsonRobotManager.Instance, "机器人1");
                var robot2Task = ConnectSingleRobotAsync(EpsonRobotManager2.Instance, "机器人2");

                var results = await Task.WhenAll(robot1Task, robot2Task);

                bool robot1Result = results[0];
                bool robot2Result = results[1];

                // 记录连接结果到result对象
                result.Robot1Success = robot1Result;
                result.Robot2Success = robot2Result;

                LogHelper.Info($"机器人连接结果 - 机器人1: {(robot1Result ? "成功" : "失败")}, 机器人2: {(robot2Result ? "成功" : "失败")}");

                if (robot1Result && robot2Result)
                {
                    LogHelper.Info("双机器人连接完成");
                }
                else if (robot1Result || robot2Result)
                {
                    result.WarningMessages.Add("部分机器人连接成功，系统可以继续运行");
                    LogHelper.Warning("部分机器人连接成功，系统可以继续运行");
                }
                else
                {
                    result.WarningMessages.Add("双机器人连接失败，系统运行在脱机测试模式");
                    LogHelper.Warning("双机器人连接失败，系统运行在脱机测试模式");
                }

            }, "连接双机器人");
        }

        /// <summary>
        /// 启动皮带电机自动控制
        /// </summary>
        /// <param name="result">自检结果对象</param>
        private async Task StartBeltMotorAutoControlAsync(SelfCheckResult result)
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info("步骤4：开始启动皮带电机自动控制...");

                try
                {
                    var beltMotorController = BeltMotorAutoModeController.Instance;
                    bool startResult = await beltMotorController.StartAsync();

                    if (startResult)
                    {
                        result.BeltMotorAutoControlSuccess = true;
                        LogHelper.Info("皮带电机自动控制启动成功");
                    }
                    else
                    {
                        result.BeltMotorAutoControlSuccess = false;
                        result.WarningMessages.Add("皮带电机自动控制启动失败（可能硬件未连接）");
                        LogHelper.Warning("皮带电机自动控制启动失败（可能硬件未连接）");
                    }
                }
                catch (Exception ex)
                {
                    result.BeltMotorAutoControlSuccess = false;
                    result.ErrorMessages.Add($"皮带电机自动控制启动异常：{ex.Message}");
                    LogHelper.Error("皮带电机自动控制启动异常", ex);
                }

                LogHelper.Info("皮带电机自动控制启动流程完成");

            }, "启动皮带电机自动控制");
        }

        /// <summary>
        /// 连接单个机器人的完整流程
        /// </summary>
        /// <param name="robotManager">机器人管理器实例</param>
        /// <param name="robotName">机器人名称（用于日志）</param>
        /// <returns>连接结果</returns>
        private async Task<bool> ConnectSingleRobotAsync(dynamic robotManager, string robotName)
        {
            return await ExceptionHelper.SafeExecuteAsync<bool>(async () =>
            {
                LogHelper.Info($"开始连接{robotName}...");

                try
                {
                    // 1. 初始化机器人管理器
                    LogHelper.Info($"初始化{robotName}管理器...");
                    bool initResult = await robotManager.InitializeAsync();
                    if (!initResult)
                    {
                        LogHelper.Warning($"{robotName}管理器初始化失败");
                        return false;
                    }

                    // 2. 连接控制端口
                    LogHelper.Info($"连接{robotName}控制端口...");
                    bool controlConnectResult = await robotManager.ConnectStartStopAsync();
                    if (!controlConnectResult)
                    {
                        LogHelper.Warning($"{robotName}控制端口连接失败");
                        return false;
                    }

                    // 3. 登录机器人
                    LogHelper.Info($"登录{robotName}...");
                    bool loginResult = await robotManager.LoginAsync();
                    if (!loginResult)
                    {
                        LogHelper.Warning($"{robotName}登录失败");
                        return false;
                    }

                    // 4. 启动机器人
                    LogHelper.Info($"启动{robotName}...");
                    bool startResult = await robotManager.StartRobotAsync();
                    if (!startResult)
                    {
                        LogHelper.Warning($"{robotName}启动失败");
                        return false;
                    }

                    // 5. 连接数据端口
                    LogHelper.Info($"连接{robotName}数据端口...");
                    bool dataConnectResult = await robotManager.ConnectDataAsync();
                    if (!dataConnectResult)
                    {
                        LogHelper.Warning($"{robotName}数据端口连接失败");
                        return false;
                    }

                    LogHelper.Info($"{robotName}连接完成");
                    return true;
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"{robotName}连接过程中发生异常", ex);
                    return false;
                }

            }, false, $"连接{robotName}");
        }

        /// <summary>
        /// 评估整体自检结果
        /// </summary>
        /// <param name="result">自检结果</param>
        /// <returns>整体是否成功</returns>
        private bool EvaluateOverallResult(SelfCheckResult result)
        {
            // 在脱机测试模式下，只要没有严重错误就认为成功
            // 硬件未连接的警告不影响整体结果
            return result.ErrorMessages.Count == 0;
        }

        /// <summary>
        /// 记录自检结果摘要
        /// </summary>
        /// <param name="result">自检结果</param>
        private void LogSelfCheckSummary(SelfCheckResult result)
        {
            LogHelper.Info("========== 开机自检结果摘要 ==========");
            LogHelper.Info($"整体结果: {(result.OverallSuccess ? "成功" : "失败")}");
            LogHelper.Info($"DMC1000B控制卡: {(result.DMC1000BCardSuccess ? "成功" : "失败/未连接")}");
            LogHelper.Info($"电机管理器: {(result.MotorManagerSuccess ? "成功" : "失败/未连接")}");
            LogHelper.Info($"IO管理器: {(result.IOManagerSuccess ? "成功" : "失败/未连接")}");
            LogHelper.Info($"左翻转电机: {(result.LeftFlipMotorSuccess ? "成功" : "失败/未连接")}");
            LogHelper.Info($"右翻转电机: {(result.RightFlipMotorSuccess ? "成功" : "失败/未连接")}");
            LogHelper.Info($"机器人1: {(result.Robot1Success ? "成功" : "失败/未连接")}");
            LogHelper.Info($"机器人2: {(result.Robot2Success ? "成功" : "失败/未连接")}");
            LogHelper.Info($"皮带电机自动控制: {(result.BeltMotorAutoControlSuccess ? "成功" : "失败/未连接")}");

            if (result.WarningMessages.Count > 0)
            {
                LogHelper.Info($"警告信息 ({result.WarningMessages.Count}条):");
                foreach (var warning in result.WarningMessages)
                {
                    LogHelper.Warning($"  - {warning}");
                }
            }

            if (result.ErrorMessages.Count > 0)
            {
                LogHelper.Info($"错误信息 ({result.ErrorMessages.Count}条):");
                foreach (var error in result.ErrorMessages)
                {
                    LogHelper.Error($"  - {error}");
                }
            }

            if (result.OverallSuccess)
            {
                LogHelper.Info("系统已准备好进入自动模式");
            }
            else
            {
                LogHelper.Warning("系统存在一些问题，但可以在脱机测试模式下运行");
            }

            LogHelper.Info("==========================================");
        }
        #endregion

        #region 资源释放
        /// <summary>
        /// 释放启动自检管理器资源
        /// </summary>
        /// <returns></returns>
        public Task DisposeAsync()
        {
            try
            {
                LogHelper.Info("开始释放启动自检管理器资源...");

                // 启动自检管理器不管理控制卡生命周期，无需释放控制卡资源

                // 释放信号量
                _semaphore?.Dispose();

                LogHelper.Info("启动自检管理器资源释放完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("释放启动自检管理器资源失败", ex);
            }

            return Task.CompletedTask;
        }

        /// <summary>
        /// 验证扫码器连接 - 发送hello给3个扫码器，接收world响应
        /// </summary>
        /// <param name="result">自检结果对象</param>
        private async Task VerifyScannersConnectionAsync(SelfCheckResult result)
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info("========== 步骤4：扫码器连接验证 ==========");

                var multiScannerManager = MultiScannerManager.Instance;
                bool allScannersVerified = true;

                // 验证3个扫码器的连接
                for (int scannerId = 1; scannerId <= 3; scannerId++)
                {
                    try
                    {
                        LogHelper.Info($"正在验证扫码器{scannerId}连接...");

                        // 尝试连接扫码器
                        bool connectResult = await multiScannerManager.ConnectScannerAsync(scannerId);
                        if (!connectResult)
                        {
                            LogHelper.Warning($"扫码器{scannerId}连接失败");
                            allScannersVerified = false;
                            continue;
                        }

                        // 发送hello命令验证通信
                        bool sendResult = await multiScannerManager.SendDataAsync(scannerId, "hello");
                        if (!sendResult)
                        {
                            LogHelper.Warning($"向扫码器{scannerId}发送hello命令失败");
                            allScannersVerified = false;
                            continue;
                        }

                        // 等待响应（简化处理，实际应该监听接收事件）
                        await Task.Delay(1000);

                        LogHelper.Info($"扫码器{scannerId}连接验证完成");
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error($"验证扫码器{scannerId}连接时发生异常", ex);
                        allScannersVerified = false;
                    }

                    // 验证间隔，避免串口冲突
                    await Task.Delay(500);
                }

                if (allScannersVerified)
                {
                    LogHelper.Info("所有扫码器连接验证成功");
                    result.ScannerConnectionSuccess = true;
                }
                else
                {
                    LogHelper.Warning("部分扫码器连接验证失败，但系统将继续运行（支持脱机测试模式）");
                    result.ScannerConnectionSuccess = false;
                }

                LogHelper.Info("========== 扫码器连接验证完成 ==========");
                return true;
            }, false, "扫码器连接验证");
        }
        #endregion
    }
}
