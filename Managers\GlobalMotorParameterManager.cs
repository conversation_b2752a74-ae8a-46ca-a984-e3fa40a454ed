using System;
using System.Threading.Tasks;
using MyHMI.Models;
using MyHMI.Helpers;
using MyHMI.Settings;

namespace MyHMI.Managers
{
    /// <summary>
    /// 全局电机参数管理器
    /// 提供统一的翻转电机参数访问接口，确保所有模块使用相同的参数源
    /// </summary>
    public static class GlobalMotorParameterManager
    {
        #region 事件定义

        /// <summary>
        /// 电机参数变化事件参数
        /// </summary>
        public class MotorParameterChangedEventArgs : EventArgs
        {
            public short Axis { get; set; }
            public FlipMotorParams Parameters { get; set; }
            public string ChangeReason { get; set; }
        }

        /// <summary>
        /// 电机参数变化事件
        /// </summary>
        public static event EventHandler<MotorParameterChangedEventArgs> ParameterChanged;

        #endregion

        #region 翻转电机参数访问接口

        /// <summary>
        /// 获取左翻转电机参数（轴0）
        /// </summary>
        /// <returns>左翻转电机参数</returns>
        public static FlipMotorParams GetLeftFlipMotorParams()
        {
            try
            {
                var motorSettings = Settings.Settings.Current.Motor;
                
                var leftParams = new FlipMotorParams
                {
                    MotorName = "左翻转电机",
                    PulseEquivalent = motorSettings.LeftFlipPulseEquivalent,
                    MaxSpeed = motorSettings.LeftFlipMaxSpeed,
                    StartSpeed = motorSettings.LeftFlipStartSpeed,
                    Acceleration = motorSettings.LeftFlipAcceleration,
                    AccelerationTime = motorSettings.LeftFlipAccelerationTime,
                    HomeSpeed = motorSettings.LeftFlipHomeSpeed,
                    HomeDirection = motorSettings.LeftFlipHomeDirection,
                    HomeIO = motorSettings.LeftFlipHomeIO,
                    PositiveLimitIO = motorSettings.LeftFlipPositiveLimitIO,
                    NegativeLimitIO = motorSettings.LeftFlipNegativeLimitIO,
                    HomeTimeout = motorSettings.LeftFlipHomeTimeout
                };

                LogHelper.Debug($"获取左翻转电机参数: 脉冲当量={leftParams.PulseEquivalent}°/pulse, 最大速度={leftParams.MaxSpeed}°/s");
                return leftParams;
            }
            catch (Exception ex)
            {
                LogHelper.Error("获取左翻转电机参数失败", ex);
                return GetDefaultLeftFlipMotorParams();
            }
        }

        /// <summary>
        /// 获取右翻转电机参数（轴1）
        /// </summary>
        /// <returns>右翻转电机参数</returns>
        public static FlipMotorParams GetRightFlipMotorParams()
        {
            try
            {
                var motorSettings = Settings.Settings.Current.Motor;
                
                var rightParams = new FlipMotorParams
                {
                    MotorName = "右翻转电机",
                    PulseEquivalent = motorSettings.RightFlipPulseEquivalent,
                    MaxSpeed = motorSettings.RightFlipMaxSpeed,
                    StartSpeed = motorSettings.RightFlipStartSpeed,
                    Acceleration = motorSettings.RightFlipAcceleration,
                    AccelerationTime = motorSettings.RightFlipAccelerationTime,
                    HomeSpeed = motorSettings.RightFlipHomeSpeed,
                    HomeDirection = motorSettings.RightFlipHomeDirection,
                    HomeIO = motorSettings.RightFlipHomeIO,
                    PositiveLimitIO = motorSettings.RightFlipPositiveLimitIO,
                    NegativeLimitIO = motorSettings.RightFlipNegativeLimitIO,
                    HomeTimeout = motorSettings.RightFlipHomeTimeout
                };

                LogHelper.Debug($"获取右翻转电机参数: 脉冲当量={rightParams.PulseEquivalent}°/pulse, 最大速度={rightParams.MaxSpeed}°/s");
                return rightParams;
            }
            catch (Exception ex)
            {
                LogHelper.Error("获取右翻转电机参数失败", ex);
                return GetDefaultRightFlipMotorParams();
            }
        }

        /// <summary>
        /// 根据轴号获取翻转电机参数
        /// </summary>
        /// <param name="axis">电机轴号 (0=左翻转电机, 1=右翻转电机)</param>
        /// <returns>翻转电机参数</returns>
        public static FlipMotorParams GetFlipMotorParams(short axis)
        {
            switch (axis)
            {
                case 0:
                    return GetLeftFlipMotorParams();
                case 1:
                    return GetRightFlipMotorParams();
                default:
                    throw new ArgumentException($"无效的翻转电机轴号: {axis}，有效值为0或1");
            }
        }

        #endregion

        #region 参数更新接口

        /// <summary>
        /// 更新左翻转电机参数
        /// </summary>
        /// <param name="parameters">新的参数</param>
        /// <param name="changeReason">变化原因</param>
        public static async Task UpdateLeftFlipMotorParamsAsync(FlipMotorParams parameters, string changeReason = "参数更新")
        {
            try
            {
                if (parameters == null)
                    throw new ArgumentNullException(nameof(parameters));

                // 验证参数
                var validation = parameters.Validate();
                if (!validation.IsValid)
                    throw new ArgumentException($"左翻转电机参数无效: {validation.ErrorMessage}");

                // 更新Settings系统
                var motorSettings = Settings.Settings.Current.Motor;
                motorSettings.LeftFlipPulseEquivalent = parameters.PulseEquivalent;
                motorSettings.LeftFlipMaxSpeed = parameters.MaxSpeed;
                motorSettings.LeftFlipStartSpeed = parameters.StartSpeed;
                motorSettings.LeftFlipAcceleration = parameters.Acceleration;
                motorSettings.LeftFlipAccelerationTime = parameters.AccelerationTime;
                motorSettings.LeftFlipHomeSpeed = parameters.HomeSpeed;
                motorSettings.LeftFlipHomeDirection = parameters.HomeDirection;
                motorSettings.LeftFlipHomeIO = parameters.HomeIO;
                motorSettings.LeftFlipPositiveLimitIO = parameters.PositiveLimitIO;
                motorSettings.LeftFlipNegativeLimitIO = parameters.NegativeLimitIO;
                motorSettings.LeftFlipHomeTimeout = parameters.HomeTimeout;

                // 保存到文件
                await Task.Run(() => Settings.Settings.Save());

                LogHelper.Info($"左翻转电机参数已更新: {changeReason}");

                // 触发参数变化事件
                NotifyParameterChanged(0, parameters, changeReason);
            }
            catch (Exception ex)
            {
                LogHelper.Error("更新左翻转电机参数失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 更新右翻转电机参数
        /// </summary>
        /// <param name="parameters">新的参数</param>
        /// <param name="changeReason">变化原因</param>
        public static async Task UpdateRightFlipMotorParamsAsync(FlipMotorParams parameters, string changeReason = "参数更新")
        {
            try
            {
                if (parameters == null)
                    throw new ArgumentNullException(nameof(parameters));

                // 验证参数
                var validation = parameters.Validate();
                if (!validation.IsValid)
                    throw new ArgumentException($"右翻转电机参数无效: {validation.ErrorMessage}");

                // 更新Settings系统
                var motorSettings = Settings.Settings.Current.Motor;
                motorSettings.RightFlipPulseEquivalent = parameters.PulseEquivalent;
                motorSettings.RightFlipMaxSpeed = parameters.MaxSpeed;
                motorSettings.RightFlipStartSpeed = parameters.StartSpeed;
                motorSettings.RightFlipAcceleration = parameters.Acceleration;
                motorSettings.RightFlipAccelerationTime = parameters.AccelerationTime;
                motorSettings.RightFlipHomeSpeed = parameters.HomeSpeed;
                motorSettings.RightFlipHomeDirection = parameters.HomeDirection;
                motorSettings.RightFlipHomeIO = parameters.HomeIO;
                motorSettings.RightFlipPositiveLimitIO = parameters.PositiveLimitIO;
                motorSettings.RightFlipNegativeLimitIO = parameters.NegativeLimitIO;
                motorSettings.RightFlipHomeTimeout = parameters.HomeTimeout;

                // 保存到文件
                await Task.Run(() => Settings.Settings.Save());

                LogHelper.Info($"右翻转电机参数已更新: {changeReason}");

                // 触发参数变化事件
                NotifyParameterChanged(1, parameters, changeReason);
            }
            catch (Exception ex)
            {
                LogHelper.Error("更新右翻转电机参数失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 根据轴号更新翻转电机参数
        /// </summary>
        /// <param name="axis">电机轴号 (0=左翻转电机, 1=右翻转电机)</param>
        /// <param name="parameters">新的参数</param>
        /// <param name="changeReason">变化原因</param>
        public static async Task UpdateFlipMotorParamsAsync(short axis, FlipMotorParams parameters, string changeReason = "参数更新")
        {
            switch (axis)
            {
                case 0:
                    await UpdateLeftFlipMotorParamsAsync(parameters, changeReason);
                    break;
                case 1:
                    await UpdateRightFlipMotorParamsAsync(parameters, changeReason);
                    break;
                default:
                    throw new ArgumentException($"无效的翻转电机轴号: {axis}，有效值为0或1");
            }
        }

        #endregion

        #region 事件通知

        /// <summary>
        /// 通知参数变化
        /// </summary>
        /// <param name="axis">电机轴号</param>
        /// <param name="parameters">新参数</param>
        /// <param name="changeReason">变化原因</param>
        public static void NotifyParameterChanged(short axis, FlipMotorParams parameters, string changeReason)
        {
            try
            {
                var eventArgs = new MotorParameterChangedEventArgs
                {
                    Axis = axis,
                    Parameters = parameters,
                    ChangeReason = changeReason
                };

                ParameterChanged?.Invoke(null, eventArgs);
                LogHelper.Debug($"翻转电机轴{axis}参数变化事件已触发: {changeReason}");
            }
            catch (Exception ex)
            {
                LogHelper.Error("触发参数变化事件失败", ex);
            }
        }

        #endregion

        #region 点动角度参数管理

        /// <summary>
        /// 获取左翻转电机点动角度
        /// </summary>
        /// <returns>点动角度（度）</returns>
        public static double GetLeftFlipJogAngle()
        {
            try
            {
                var motorSettings = Settings.Settings.Current.Motor;
                double jogAngle = motorSettings.LeftFlipJogAngle;

                // 验证角度范围（0.1-180度）
                if (jogAngle < 0.1 || jogAngle > 180.0)
                {
                    LogHelper.Warning($"左翻转电机点动角度超出范围: {jogAngle}°，使用默认值5°");
                    return 5.0;
                }

                return jogAngle;
            }
            catch (Exception ex)
            {
                LogHelper.Error("获取左翻转电机点动角度失败", ex);
                return 5.0; // 返回默认值
            }
        }

        /// <summary>
        /// 获取右翻转电机点动角度
        /// </summary>
        /// <returns>点动角度（度）</returns>
        public static double GetRightFlipJogAngle()
        {
            try
            {
                var motorSettings = Settings.Settings.Current.Motor;
                double jogAngle = motorSettings.RightFlipJogAngle;

                // 验证角度范围（0.1-180度）
                if (jogAngle < 0.1 || jogAngle > 180.0)
                {
                    LogHelper.Warning($"右翻转电机点动角度超出范围: {jogAngle}°，使用默认值5°");
                    return 5.0;
                }

                return jogAngle;
            }
            catch (Exception ex)
            {
                LogHelper.Error("获取右翻转电机点动角度失败", ex);
                return 5.0; // 返回默认值
            }
        }

        /// <summary>
        /// 更新左翻转电机点动角度
        /// </summary>
        /// <param name="jogAngle">点动角度（度）</param>
        /// <param name="source">更新来源</param>
        /// <returns>是否成功</returns>
        public static async Task<bool> UpdateLeftFlipJogAngleAsync(double jogAngle, string source = "未知")
        {
            try
            {
                // 验证角度范围
                if (jogAngle < 0.1 || jogAngle > 180.0)
                {
                    LogHelper.Error($"左翻转电机点动角度超出范围: {jogAngle}°，有效范围: 0.1-180°");
                    return false;
                }

                // 更新Settings系统
                var motorSettings = Settings.Settings.Current.Motor;
                motorSettings.LeftFlipJogAngle = jogAngle;

                // 保存到文件
                await Task.Run(() => Settings.Settings.Save());

                // 触发参数变化事件（使用现有的ParameterChanged事件）
                ParameterChanged?.Invoke(null, new MotorParameterChangedEventArgs
                {
                    Axis = 0, // 左翻转电机
                    Parameters = null, // 点动角度不是完整参数对象
                    ChangeReason = $"点动角度更新: {jogAngle}° (来源: {source})"
                });

                LogHelper.Info($"左翻转电机点动角度已更新: {jogAngle}° (来源: {source})");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"更新左翻转电机点动角度失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 更新右翻转电机点动角度
        /// </summary>
        /// <param name="jogAngle">点动角度（度）</param>
        /// <param name="source">更新来源</param>
        /// <returns>是否成功</returns>
        public static async Task<bool> UpdateRightFlipJogAngleAsync(double jogAngle, string source = "未知")
        {
            try
            {
                // 验证角度范围
                if (jogAngle < 0.1 || jogAngle > 180.0)
                {
                    LogHelper.Error($"右翻转电机点动角度超出范围: {jogAngle}°，有效范围: 0.1-180°");
                    return false;
                }

                // 更新Settings系统
                var motorSettings = Settings.Settings.Current.Motor;
                motorSettings.RightFlipJogAngle = jogAngle;

                // 保存到文件
                await Task.Run(() => Settings.Settings.Save());

                // 触发参数变化事件（使用现有的ParameterChanged事件）
                ParameterChanged?.Invoke(null, new MotorParameterChangedEventArgs
                {
                    Axis = 1, // 右翻转电机
                    Parameters = null, // 点动角度不是完整参数对象
                    ChangeReason = $"点动角度更新: {jogAngle}° (来源: {source})"
                });

                LogHelper.Info($"右翻转电机点动角度已更新: {jogAngle}° (来源: {source})");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"更新右翻转电机点动角度失败: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 默认参数

        /// <summary>
        /// 获取默认左翻转电机参数
        /// </summary>
        private static FlipMotorParams GetDefaultLeftFlipMotorParams()
        {
            return new FlipMotorParams
            {
                MotorName = "左翻转电机",
                PulseEquivalent = 0.012,    // 0.012°/pulse
                MaxSpeed = 60,              // 60°/s
                StartSpeed = 5,             // 5°/s
                Acceleration = 120,         // 120°/s²
                AccelerationTime = 0.1,     // 0.1s
                HomeSpeed = 20,             // 20°/s
                HomeDirection = false,      // 负方向回零
                HomeIO = 0,                 // X000 - ORG0
                PositiveLimitIO = 8,        // X008 - PEL0
                NegativeLimitIO = 16,       // X016 - NEL0
                HomeTimeout = 30000         // 30秒超时
            };
        }

        /// <summary>
        /// 获取默认右翻转电机参数
        /// </summary>
        private static FlipMotorParams GetDefaultRightFlipMotorParams()
        {
            return new FlipMotorParams
            {
                MotorName = "右翻转电机",
                PulseEquivalent = 0.012,    // 0.012°/pulse
                MaxSpeed = 60,              // 60°/s
                StartSpeed = 5,             // 5°/s
                Acceleration = 120,         // 120°/s²
                AccelerationTime = 0.1,     // 0.1s
                HomeSpeed = 20,             // 20°/s
                HomeDirection = false,      // 负方向回零
                HomeIO = 1,                 // X001 - ORG1
                PositiveLimitIO = 9,        // X009 - PEL1
                NegativeLimitIO = 17,       // X017 - NEL1
                HomeTimeout = 30000         // 30秒超时
            };
        }

        #endregion
    }
}
