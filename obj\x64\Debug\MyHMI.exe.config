<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
    </startup>
    
    <appSettings>
        <!-- 串口配置 -->
        <add key="ScannerComPort" value="COM1" />
        <add key="ScannerBaudRate" value="9600" />
        
        <!-- Epson机器人TCP/IP配置（单IP双端口模式） -->
        <add key="EpsonRobotIP" value="*************" />
        <add key="EpsonControlPort" value="5000" />
        <add key="EpsonDataPort" value="5001" />
        <add key="EpsonPassword" value="EPSON" />
        <add key="EpsonConnectTimeout" value="5000" />
        <add key="EpsonReceiveTimeout" value="3000" />
        <add key="EpsonSendTimeout" value="3000" />
        <add key="EpsonScanInterval" value="100" />
        <add key="EpsonAutoReconnect" value="true" />
        <add key="EpsonReconnectInterval" value="5000" />
        <add key="EpsonMaxReconnectAttempts" value="10" />
        
        <!-- Modbus TCP配置 -->
        <add key="ModbusTcpIP" value="*************" />
        <add key="ModbusTcpPort" value="502" />
        <add key="ModbusSlaveId" value="1" />
        
        <!-- 雷赛卡配置 -->
        <add key="DMC1000CardIndex" value="0" />
        
        <!-- 视觉系统配置 -->
        <add key="VisionCameraIndex" value="0" />
        <add key="VisionConfigPath" value="Config\VisionConfig.json" />
        
        <!-- 日志配置 -->
        <add key="LogLevel" value="Info" />
        <add key="LogPath" value="Logs\" />
        
        <!-- 统计数据配置 -->
        <add key="StatisticsDataPath" value="Data\Statistics.csv" />
        <add key="AutoSaveInterval" value="300" />
    </appSettings>
</configuration>
