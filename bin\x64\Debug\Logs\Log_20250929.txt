[2025-09-29 07:13:50.352] [INFO] 程序启动开始
[2025-09-29 07:13:50.357] [INFO] 加载Settings系统配置...
[2025-09-29 07:13:50.379] [INFO] 设置加载成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-29 07:13:50.379] [INFO] Settings系统配置加载完成
[2025-09-29 07:13:50.380] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-29 07:13:50.416] [INFO] 开始初始化各个Manager...
[2025-09-29 07:13:50.416] [INFO] 初始化DMC1000B控制卡...
[2025-09-29 07:13:50.422] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-29 07:13:50.425] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-29 07:13:50.425] [WARN] DMC1000B控制卡初始化失败，相关功能可能无法正常工作
[2025-09-29 07:13:50.425] [INFO] 初始化基础Manager...
[2025-09-29 07:13:50.432] [INFO] IO状态缓存初始化完成
[2025-09-29 07:13:50.435] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-29 07:13:50.435] [WARN] DMC1000BIO管理器初始化失败
[2025-09-29 07:13:50.439] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-29 07:13:50.488] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-29 07:13:50.508] [WARN] DMC1000B电机管理器初始化失败
[2025-09-29 07:13:50.513] [INFO] 初始化系统模式管理器...
[2025-09-29 07:13:50.530] [INFO] 从Settings系统加载安全管理器配置成功
[2025-09-29 07:13:50.534] [INFO] 安全管理器实例已创建
[2025-09-29 07:13:50.538] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-29 07:13:50.539] [INFO] 恢复上次系统模式: Automatic
[2025-09-29 07:13:50.545] [INFO] 开始切换到自动模式...
[2025-09-29 07:13:50.549] [INFO] 开始检查系统是否准备好进入自动模式...
[2025-09-29 07:13:50.550] [INFO] 开始初始化MotorManager...
[2025-09-29 07:13:50.553] [INFO] 模拟初始化运动控制卡
[2025-09-29 07:13:50.555] [INFO] 系统基础检查完成，准备进入自动模式（具体硬件初始化将在开机自检中完成）
[2025-09-29 07:13:50.557] [INFO] 系统模式已从 Manual 切换到 Automatic
[2025-09-29 07:13:50.558] [INFO] 启动安全管理器...
[2025-09-29 07:13:50.562] [ERROR] 安全管理器未初始化，无法启动
[2025-09-29 07:13:50.563] [ERROR] 启动安全管理器失败，无法切换到自动模式
[2025-09-29 07:13:50.563] [ERROR] 恢复上次系统模式失败: Automatic，保持当前模式
[2025-09-29 07:13:50.576] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-29 07:13:50.576] [INFO] 系统模式已保存到配置文件: Manual
[2025-09-29 07:13:50.764] [INFO] 加载了8个电机的默认配置
[2025-09-29 07:13:50.765] [INFO] 电机监控任务已启动
[2025-09-29 07:13:50.765] [INFO] MotorManager初始化完成
[2025-09-29 07:13:50.766] [INFO] 初始化通信Manager...
[2025-09-29 07:13:50.767] [INFO] 电机监控循环开始
[2025-09-29 07:13:50.770] [INFO] 开始初始化ScannerManager...
[2025-09-29 07:13:50.773] [INFO] 扫描枪配置从Settings系统加载: COM1, 9600, 8, One, None
[2025-09-29 07:13:50.775] [INFO] 串口初始化完成
[2025-09-29 07:13:50.777] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-29 07:13:50.805] [INFO] 扫描枪连接成功: COM1
[2025-09-29 07:13:50.806] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-29 07:13:50.806] [INFO] ScannerManager初始化完成
[2025-09-29 07:13:50.811] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-29 07:13:50.811] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-29 07:13:50.812] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-29 07:13:50.814] [INFO] SCARA通信管理器已初始化
[2025-09-29 07:13:50.816] [INFO] ScannerAutoModeManager管理器引用初始化完成
[2025-09-29 07:13:50.819] [INFO] 开始初始化ScannerAutoModeManager...
[2025-09-29 07:13:50.822] [INFO] 开始初始化MultiScannerManager...
[2025-09-29 07:13:50.823] [INFO] 开始初始化扫描枪1...
[2025-09-29 07:13:50.825] [INFO] 从Settings系统加载扫描枪1配置: COM1, 115200
[2025-09-29 07:13:50.827] [INFO] 扫描枪1串口初始化完成
[2025-09-29 07:13:50.828] [INFO] 扫描枪1初始化完成
[2025-09-29 07:13:50.828] [INFO] 开始初始化扫描枪2...
[2025-09-29 07:13:50.828] [INFO] 从Settings系统加载扫描枪2配置: COM2, 115200
[2025-09-29 07:13:50.829] [INFO] 扫描枪2串口初始化完成
[2025-09-29 07:13:50.829] [INFO] 扫描枪2初始化完成
[2025-09-29 07:13:50.829] [INFO] 开始初始化扫描枪3...
[2025-09-29 07:13:50.830] [INFO] 从Settings系统加载扫描枪3配置: COM3, 115200
[2025-09-29 07:13:50.830] [INFO] 扫描枪3串口初始化完成
[2025-09-29 07:13:50.831] [INFO] 扫描枪3初始化完成
[2025-09-29 07:13:50.831] [INFO] MultiScannerManager初始化完成
[2025-09-29 07:13:50.832] [INFO] ScannerAutoModeManager初始化完成
[2025-09-29 07:13:50.832] [INFO] 跳过Modbus TCP管理器初始化 - 由第三方控制
[2025-09-29 07:13:50.835] [INFO] 开始初始化EpsonRobotManager...
[2025-09-29 07:13:50.837] [INFO] 从Settings系统加载Epson机器人1配置成功
[2025-09-29 07:13:50.838] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-29 07:13:50.838] [INFO] EpsonRobotManager初始化完成
[2025-09-29 07:13:50.838] [INFO] 初始化视觉Manager...
[2025-09-29 07:13:50.841] [INFO] 开始初始化VisionManager...
[2025-09-29 07:13:50.842] [INFO] 视觉系统配置从Settings系统加载: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms, 置信度阈值=0.8
[2025-09-29 07:13:50.844] [INFO] 模拟初始化相机，索引: 0
[2025-09-29 07:13:51.355] [INFO] 相机初始化成功
[2025-09-29 07:13:51.356] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-29 07:13:51.356] [INFO] 视觉配置加载完成
[2025-09-29 07:13:51.357] [INFO] VisionManager初始化完成
[2025-09-29 07:13:51.357] [INFO] 初始化数据Manager...
[2025-09-29 07:13:51.360] [INFO] 开始初始化StatisticsManager...
[2025-09-29 07:13:51.362] [INFO] 统计管理器配置: 数据目录=Export, 缓存大小=1000, 自动保存间隔=24分钟
[2025-09-29 07:13:51.506] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-29 07:13:51.506] [INFO] 历史数据加载完成
[2025-09-29 07:13:51.506] [INFO] StatisticsManager初始化完成
[2025-09-29 07:13:51.507] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-29 07:13:51.507] [INFO] 所有Manager初始化完成
[2025-09-29 07:13:51.564] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-29 07:13:51.564] [INFO] 创建并缓存面板: vision-position
[2025-09-29 07:13:51.565] [INFO] 主界面布局创建完成
[2025-09-29 07:13:51.566] [INFO] 时间更新定时器初始化完成
[2025-09-29 07:13:51.567] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-29 07:13:51.630] [INFO] 开始初始化系统
[2025-09-29 07:13:51.631] [INFO] 初始化业务逻辑
[2025-09-29 07:13:51.632] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-29 07:13:51.632] [INFO] IO管理器初始化完成
[2025-09-29 07:13:51.633] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-29 07:13:51.707] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-29 07:13:51.707] [INFO] 电机管理器初始化完成
[2025-09-29 07:13:51.709] [INFO] IO事件订阅完成
[2025-09-29 07:13:51.709] [INFO] 电机事件订阅完成
[2025-09-29 07:13:51.709] [INFO] 业务层交互机制建立完成
[2025-09-29 07:13:51.709] [INFO] 业务逻辑初始化完成
[2025-09-29 07:13:51.711] [INFO] 执行UI界面刷新
[2025-09-29 07:13:51.713] [INFO] UI界面刷新完成
[2025-09-29 07:13:51.714] [INFO] 系统初始化完成
[2025-09-29 07:14:06.793] [INFO] 开始切换到自动模式...
[2025-09-29 07:14:06.794] [INFO] 开始检查系统是否准备好进入自动模式...
[2025-09-29 07:14:06.794] [INFO] 系统基础检查完成，准备进入自动模式（具体硬件初始化将在开机自检中完成）
[2025-09-29 07:14:06.794] [INFO] 系统模式已从 Manual 切换到 Automatic
[2025-09-29 07:14:06.799] [INFO] 启动安全管理器...
[2025-09-29 07:14:06.799] [ERROR] 安全管理器未初始化，无法启动
[2025-09-29 07:14:06.799] [ERROR] 启动安全管理器失败，无法切换到自动模式
[2025-09-29 07:14:06.800] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-29 07:14:06.800] [INFO] 系统模式已保存到配置文件: Automatic
[2025-09-29 07:16:17.755] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-29 07:16:17.756] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-29 07:16:17.762] [INFO] UI参数显示已从Settings系统更新
[2025-09-29 07:16:17.762] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-29 07:16:17.762] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-29 07:16:17.763] [INFO] 创建并缓存面板: motor-flip-params
[2025-09-29 07:16:21.983] [INFO] 开始切换到自动模式...
[2025-09-29 07:16:21.983] [INFO] 开始检查系统是否准备好进入自动模式...
[2025-09-29 07:16:21.983] [INFO] 系统基础检查完成，准备进入自动模式（具体硬件初始化将在开机自检中完成）
[2025-09-29 07:16:21.983] [INFO] 系统模式已从 Manual 切换到 Automatic
[2025-09-29 07:16:21.984] [INFO] 启动安全管理器...
[2025-09-29 07:16:21.984] [ERROR] 安全管理器未初始化，无法启动
[2025-09-29 07:16:21.984] [ERROR] 启动安全管理器失败，无法切换到自动模式
[2025-09-29 07:16:21.985] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-29 07:16:21.985] [INFO] 系统模式已保存到配置文件: Automatic
[2025-09-29 07:16:29.580] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-29 07:16:29.581] [INFO] 保存Settings系统配置...
[2025-09-29 07:16:29.582] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-29 07:16:29.582] [INFO] Settings系统配置保存完成
[2025-09-29 07:16:29.585] [INFO] 开始按顺序释放各个Manager资源...
[2025-09-29 07:16:29.585] [INFO] 清理UI资源和面板缓存...
[2025-09-29 07:16:29.588] [INFO] 时间更新定时器资源已释放
[2025-09-29 07:16:29.589] [INFO] 开始清理面板缓存，共2个面板
[2025-09-29 07:16:29.600] [INFO] 翻转电机控制面板资源释放完成
[2025-09-29 07:16:29.603] [INFO] 面板缓存字典已清空
[2025-09-29 07:16:29.603] [INFO] 面板缓存清理完成
[2025-09-29 07:16:29.604] [INFO] UI资源清理完成
[2025-09-29 07:16:29.604] [INFO] 释放工作流管理器资源...
[2025-09-29 07:16:29.606] [INFO] 开始释放WorkflowManager资源...
[2025-09-29 07:16:29.607] [INFO] 工作流已经处于空闲状态
[2025-09-29 07:16:29.608] [INFO] 开始取消订阅各Manager事件...
[2025-09-29 07:16:29.608] [INFO] Manager事件取消订阅完成
[2025-09-29 07:16:29.608] [INFO] WorkflowManager资源释放完成
[2025-09-29 07:16:29.608] [INFO] 工作流管理器资源释放完成
[2025-09-29 07:16:29.609] [INFO] 释放启动自检管理器资源...
[2025-09-29 07:16:29.609] [INFO] 开始释放启动自检管理器资源...
[2025-09-29 07:16:29.609] [INFO] 启动自检管理器资源释放完成
[2025-09-29 07:16:29.609] [INFO] 启动自检管理器资源释放完成
[2025-09-29 07:16:29.609] [INFO] 释放DMC1000B电机管理器资源...
[2025-09-29 07:16:29.611] [INFO] 开始释放DMC1000B资源...
[2025-09-29 07:16:29.618] [INFO] 停止所有电机部分失败
[2025-09-29 07:16:29.620] [INFO] DMC1000B资源释放完成
[2025-09-29 07:16:29.620] [INFO] DMC1000B电机管理器资源释放完成
[2025-09-29 07:16:29.620] [INFO] 释放DMC1000BIO管理器资源...
[2025-09-29 07:16:29.623] [INFO] DMC1000BIO管理器资源释放完成
[2025-09-29 07:16:29.623] [INFO] 释放其他Manager资源...
[2025-09-29 07:16:29.624] [INFO] 开始释放MotorManager资源...
[2025-09-29 07:16:29.629] [INFO] 电机0停止运动
[2025-09-29 07:16:29.629] [INFO] 电机3停止运动
[2025-09-29 07:16:29.629] [INFO] 电机5停止运动
[2025-09-29 07:16:29.630] [INFO] 电机7停止运动
[2025-09-29 07:16:29.654] [INFO] 电机1停止运动
[2025-09-29 07:16:29.654] [INFO] 电机2停止运动
[2025-09-29 07:16:29.655] [INFO] 电机6停止运动
[2025-09-29 07:16:29.655] [INFO] 电机4停止运动
[2025-09-29 07:16:29.655] [INFO] 所有电机已停止
[2025-09-29 07:16:29.691] [INFO] 电机监控循环被取消
[2025-09-29 07:16:29.691] [INFO] 电机监控循环结束
[2025-09-29 07:16:29.692] [INFO] 电机监控任务已停止
[2025-09-29 07:16:29.692] [INFO] 模拟释放运动控制卡资源
[2025-09-29 07:16:29.692] [INFO] MotorManager资源释放完成
[2025-09-29 07:16:29.694] [INFO] 开始释放ScannerManager资源...
[2025-09-29 07:16:29.719] [INFO] 扫描枪连接已断开
[2025-09-29 07:16:29.719] [INFO] 扫描枪状态变更: Disconnected - 扫描枪连接已断开
[2025-09-29 07:16:29.719] [INFO] ScannerManager资源释放完成
[2025-09-29 07:16:29.721] [INFO] 开始释放EpsonRobotManager资源...
[2025-09-29 07:16:29.725] [INFO] EpsonRobotManager资源释放完成
[2025-09-29 07:16:29.727] [INFO] 开始释放EpsonRobotManager2资源...
[2025-09-29 07:16:29.731] [INFO] EpsonRobotManager2资源释放完成
[2025-09-29 07:16:29.732] [INFO] 开始释放VisionManager资源...
[2025-09-29 07:16:29.734] [INFO] 模拟释放相机资源
[2025-09-29 07:16:29.735] [INFO] VisionManager资源释放完成
[2025-09-29 07:16:29.736] [INFO] 开始释放StatisticsManager资源...
[2025-09-29 07:16:29.737] [INFO] 自动保存任务已停止
[2025-09-29 07:16:29.741] [INFO] StatisticsManager资源释放完成
[2025-09-29 07:16:29.741] [INFO] 其他Manager资源释放完成
[2025-09-29 07:16:29.741] [INFO] 释放DMC1000B控制卡资源...
[2025-09-29 07:16:29.743] [INFO] DMC1000B控制卡未初始化，无需释放
[2025-09-29 07:16:29.743] [INFO] DMC1000B控制卡资源释放完成
[2025-09-29 07:16:29.743] [INFO] 所有Manager资源释放流程完成
[2025-09-29 07:16:29.744] [INFO] 所有资源释放完成，程序即将退出
[2025-09-29 07:16:29.744] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-29 07:16:58.188] [INFO] 程序启动开始
[2025-09-29 07:16:58.189] [INFO] 加载Settings系统配置...
[2025-09-29 07:16:58.194] [INFO] 设置加载成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-29 07:16:58.194] [INFO] Settings系统配置加载完成
[2025-09-29 07:16:58.194] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-29 07:16:58.205] [INFO] 开始初始化各个Manager...
[2025-09-29 07:16:58.205] [INFO] 初始化DMC1000B控制卡...
[2025-09-29 07:16:58.208] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-29 07:16:58.210] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-29 07:16:58.210] [WARN] DMC1000B控制卡初始化失败，相关功能可能无法正常工作
[2025-09-29 07:16:58.210] [INFO] 初始化基础Manager...
[2025-09-29 07:16:58.214] [INFO] IO状态缓存初始化完成
[2025-09-29 07:16:58.216] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-29 07:16:58.216] [WARN] DMC1000BIO管理器初始化失败
[2025-09-29 07:16:58.220] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-29 07:16:58.220] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-29 07:16:58.236] [WARN] DMC1000B电机管理器初始化失败
[2025-09-29 07:16:58.236] [INFO] 初始化系统模式管理器...
[2025-09-29 07:16:58.244] [INFO] 从Settings系统加载安全管理器配置成功
[2025-09-29 07:16:58.247] [INFO] 安全管理器实例已创建
[2025-09-29 07:16:58.250] [INFO] 开始初始化MotorManager...
[2025-09-29 07:16:58.250] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-29 07:16:58.251] [INFO] 恢复上次系统模式: Automatic
[2025-09-29 07:16:58.252] [INFO] 模拟初始化运动控制卡
[2025-09-29 07:16:58.253] [INFO] 开始切换到自动模式...
[2025-09-29 07:16:58.254] [INFO] 开始检查系统是否准备好进入自动模式...
[2025-09-29 07:16:58.256] [INFO] 系统基础检查完成，准备进入自动模式（具体硬件初始化将在开机自检中完成）
[2025-09-29 07:16:58.257] [INFO] 系统模式已从 Manual 切换到 Automatic
[2025-09-29 07:16:58.258] [INFO] 启动安全管理器...
[2025-09-29 07:16:58.260] [ERROR] 安全管理器未初始化，无法启动
[2025-09-29 07:16:58.260] [ERROR] 启动安全管理器失败，无法切换到自动模式
[2025-09-29 07:16:58.261] [ERROR] 恢复上次系统模式失败: Automatic，保持当前模式
[2025-09-29 07:16:58.261] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-29 07:16:58.261] [INFO] 系统模式已保存到配置文件: Automatic
[2025-09-29 07:16:58.470] [INFO] 加载了8个电机的默认配置
[2025-09-29 07:16:58.471] [INFO] 电机监控任务已启动
[2025-09-29 07:16:58.472] [INFO] MotorManager初始化完成
[2025-09-29 07:16:58.472] [INFO] 初始化通信Manager...
[2025-09-29 07:16:58.473] [INFO] 电机监控循环开始
[2025-09-29 07:16:58.474] [INFO] 开始初始化ScannerManager...
[2025-09-29 07:16:58.475] [INFO] 扫描枪配置从Settings系统加载: COM1, 9600, 8, One, None
[2025-09-29 07:16:58.477] [INFO] 串口初始化完成
[2025-09-29 07:16:58.478] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-29 07:16:58.480] [INFO] 扫描枪连接成功: COM1
[2025-09-29 07:16:58.480] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-29 07:16:58.480] [INFO] ScannerManager初始化完成
[2025-09-29 07:16:58.484] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-29 07:16:58.485] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-29 07:16:58.485] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-29 07:16:58.486] [INFO] SCARA通信管理器已初始化
[2025-09-29 07:16:58.488] [INFO] ScannerAutoModeManager管理器引用初始化完成
[2025-09-29 07:16:58.490] [INFO] 开始初始化ScannerAutoModeManager...
[2025-09-29 07:16:58.492] [INFO] 开始初始化MultiScannerManager...
[2025-09-29 07:16:58.493] [INFO] 开始初始化扫描枪1...
[2025-09-29 07:16:58.493] [INFO] 从Settings系统加载扫描枪1配置: COM1, 115200
[2025-09-29 07:16:58.495] [INFO] 扫描枪1串口初始化完成
[2025-09-29 07:16:58.495] [INFO] 扫描枪1初始化完成
[2025-09-29 07:16:58.495] [INFO] 开始初始化扫描枪2...
[2025-09-29 07:16:58.495] [INFO] 从Settings系统加载扫描枪2配置: COM2, 115200
[2025-09-29 07:16:58.495] [INFO] 扫描枪2串口初始化完成
[2025-09-29 07:16:58.496] [INFO] 扫描枪2初始化完成
[2025-09-29 07:16:58.496] [INFO] 开始初始化扫描枪3...
[2025-09-29 07:16:58.496] [INFO] 从Settings系统加载扫描枪3配置: COM3, 115200
[2025-09-29 07:16:58.496] [INFO] 扫描枪3串口初始化完成
[2025-09-29 07:16:58.496] [INFO] 扫描枪3初始化完成
[2025-09-29 07:16:58.497] [INFO] MultiScannerManager初始化完成
[2025-09-29 07:16:58.497] [INFO] ScannerAutoModeManager初始化完成
[2025-09-29 07:16:58.497] [INFO] 跳过Modbus TCP管理器初始化 - 由第三方控制
[2025-09-29 07:16:58.499] [INFO] 开始初始化EpsonRobotManager...
[2025-09-29 07:16:58.501] [INFO] 从Settings系统加载Epson机器人1配置成功
[2025-09-29 07:16:58.501] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-29 07:16:58.501] [INFO] EpsonRobotManager初始化完成
[2025-09-29 07:16:58.501] [INFO] 初始化视觉Manager...
[2025-09-29 07:16:58.504] [INFO] 开始初始化VisionManager...
[2025-09-29 07:16:58.505] [INFO] 视觉系统配置从Settings系统加载: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms, 置信度阈值=0.8
[2025-09-29 07:16:58.506] [INFO] 模拟初始化相机，索引: 0
[2025-09-29 07:16:59.008] [INFO] 相机初始化成功
[2025-09-29 07:16:59.013] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-29 07:16:59.014] [INFO] 视觉配置加载完成
[2025-09-29 07:16:59.015] [INFO] VisionManager初始化完成
[2025-09-29 07:16:59.015] [INFO] 初始化数据Manager...
[2025-09-29 07:16:59.027] [INFO] 开始初始化StatisticsManager...
[2025-09-29 07:16:59.029] [INFO] 统计管理器配置: 数据目录=Export, 缓存大小=1000, 自动保存间隔=24分钟
[2025-09-29 07:16:59.053] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-29 07:16:59.054] [INFO] 历史数据加载完成
[2025-09-29 07:16:59.054] [INFO] StatisticsManager初始化完成
[2025-09-29 07:16:59.054] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-29 07:16:59.055] [INFO] 所有Manager初始化完成
[2025-09-29 07:16:59.101] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-29 07:16:59.101] [INFO] 创建并缓存面板: vision-position
[2025-09-29 07:16:59.102] [INFO] 主界面布局创建完成
[2025-09-29 07:16:59.103] [INFO] 时间更新定时器初始化完成
[2025-09-29 07:16:59.103] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-29 07:16:59.162] [INFO] 开始初始化系统
[2025-09-29 07:16:59.163] [INFO] 初始化业务逻辑
[2025-09-29 07:16:59.164] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-29 07:16:59.164] [INFO] IO管理器初始化完成
[2025-09-29 07:16:59.164] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-29 07:16:59.164] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-29 07:16:59.165] [INFO] 电机管理器初始化完成
[2025-09-29 07:16:59.165] [INFO] IO事件订阅完成
[2025-09-29 07:16:59.165] [INFO] 电机事件订阅完成
[2025-09-29 07:16:59.166] [INFO] 业务层交互机制建立完成
[2025-09-29 07:16:59.166] [INFO] 业务逻辑初始化完成
[2025-09-29 07:16:59.166] [INFO] 执行UI界面刷新
[2025-09-29 07:16:59.168] [INFO] UI界面刷新完成
[2025-09-29 07:16:59.168] [INFO] 系统初始化完成
[2025-09-29 07:17:09.599] [INFO] 开始切换到自动模式...
[2025-09-29 07:17:09.600] [INFO] 开始检查系统是否准备好进入自动模式...
[2025-09-29 07:17:09.600] [INFO] 系统基础检查完成，准备进入自动模式（具体硬件初始化将在开机自检中完成）
[2025-09-29 07:17:09.600] [INFO] 系统模式已从 Manual 切换到 Automatic
[2025-09-29 07:17:09.603] [INFO] 启动安全管理器...
[2025-09-29 07:17:09.603] [ERROR] 安全管理器未初始化，无法启动
[2025-09-29 07:17:09.603] [ERROR] 启动安全管理器失败，无法切换到自动模式
[2025-09-29 07:17:09.603] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-29 07:17:09.604] [INFO] 系统模式已保存到配置文件: Automatic
[2025-09-29 07:17:40.039] [INFO] 开始切换到自动模式...
[2025-09-29 07:17:40.039] [INFO] 开始检查系统是否准备好进入自动模式...
[2025-09-29 07:17:40.039] [INFO] 系统基础检查完成，准备进入自动模式（具体硬件初始化将在开机自检中完成）
[2025-09-29 07:17:40.039] [INFO] 系统模式已从 Manual 切换到 Automatic
[2025-09-29 07:17:40.039] [INFO] 启动安全管理器...
[2025-09-29 07:17:40.039] [ERROR] 安全管理器未初始化，无法启动
[2025-09-29 07:17:40.040] [ERROR] 启动安全管理器失败，无法切换到自动模式
[2025-09-29 07:17:40.040] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-29 07:17:40.040] [INFO] 系统模式已保存到配置文件: Automatic
[2025-09-29 07:20:03.359] [INFO] 开始切换到自动模式...
[2025-09-29 07:20:03.359] [INFO] 开始检查系统是否准备好进入自动模式...
[2025-09-29 07:20:03.359] [INFO] 系统基础检查完成，准备进入自动模式（具体硬件初始化将在开机自检中完成）
[2025-09-29 07:20:03.360] [INFO] 系统模式已从 Manual 切换到 Automatic
[2025-09-29 07:20:03.360] [INFO] 启动安全管理器...
[2025-09-29 07:20:03.360] [ERROR] 安全管理器未初始化，无法启动
[2025-09-29 07:20:03.360] [ERROR] 启动安全管理器失败，无法切换到自动模式
[2025-09-29 07:20:03.361] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-29 07:20:03.361] [INFO] 系统模式已保存到配置文件: Automatic
[2025-09-29 07:20:46.966] [INFO] 开始切换到自动模式...
[2025-09-29 07:20:46.966] [INFO] 开始检查系统是否准备好进入自动模式...
[2025-09-29 07:20:46.966] [INFO] 系统基础检查完成，准备进入自动模式（具体硬件初始化将在开机自检中完成）
[2025-09-29 07:20:46.966] [INFO] 系统模式已从 Manual 切换到 Automatic
[2025-09-29 07:20:46.966] [INFO] 启动安全管理器...
[2025-09-29 07:20:46.967] [ERROR] 安全管理器未初始化，无法启动
[2025-09-29 07:20:46.967] [ERROR] 启动安全管理器失败，无法切换到自动模式
[2025-09-29 07:20:46.967] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-29 07:20:46.968] [INFO] 系统模式已保存到配置文件: Automatic
[2025-09-29 07:20:50.148] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-29 07:20:50.149] [INFO] 保存Settings系统配置...
[2025-09-29 07:20:50.150] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-29 07:20:50.150] [INFO] Settings系统配置保存完成
[2025-09-29 07:20:50.153] [INFO] 开始按顺序释放各个Manager资源...
[2025-09-29 07:20:50.153] [INFO] 清理UI资源和面板缓存...
[2025-09-29 07:20:50.154] [INFO] 时间更新定时器资源已释放
[2025-09-29 07:20:50.155] [INFO] 开始清理面板缓存，共1个面板
[2025-09-29 07:20:50.162] [INFO] 面板缓存字典已清空
[2025-09-29 07:20:50.162] [INFO] 面板缓存清理完成
[2025-09-29 07:20:50.162] [INFO] UI资源清理完成
[2025-09-29 07:20:50.162] [INFO] 释放工作流管理器资源...
[2025-09-29 07:20:50.164] [INFO] 开始释放WorkflowManager资源...
[2025-09-29 07:20:50.165] [INFO] 工作流已经处于空闲状态
[2025-09-29 07:20:50.166] [INFO] 开始取消订阅各Manager事件...
[2025-09-29 07:20:50.166] [INFO] Manager事件取消订阅完成
[2025-09-29 07:20:50.166] [INFO] WorkflowManager资源释放完成
[2025-09-29 07:20:50.166] [INFO] 工作流管理器资源释放完成
[2025-09-29 07:20:50.167] [INFO] 释放启动自检管理器资源...
[2025-09-29 07:20:50.167] [INFO] 开始释放启动自检管理器资源...
[2025-09-29 07:20:50.167] [INFO] 启动自检管理器资源释放完成
[2025-09-29 07:20:50.167] [INFO] 启动自检管理器资源释放完成
[2025-09-29 07:20:50.167] [INFO] 释放DMC1000B电机管理器资源...
[2025-09-29 07:20:50.169] [INFO] 开始释放DMC1000B资源...
[2025-09-29 07:20:50.175] [INFO] 停止所有电机部分失败
[2025-09-29 07:20:50.175] [INFO] DMC1000B资源释放完成
[2025-09-29 07:20:50.176] [INFO] DMC1000B电机管理器资源释放完成
[2025-09-29 07:20:50.176] [INFO] 释放DMC1000BIO管理器资源...
[2025-09-29 07:20:50.178] [INFO] DMC1000BIO管理器资源释放完成
[2025-09-29 07:20:50.178] [INFO] 释放其他Manager资源...
[2025-09-29 07:20:50.179] [INFO] 开始释放MotorManager资源...
[2025-09-29 07:20:50.183] [INFO] 电机0停止运动
[2025-09-29 07:20:50.183] [INFO] 电机6停止运动
[2025-09-29 07:20:50.183] [INFO] 电机4停止运动
[2025-09-29 07:20:50.183] [INFO] 电机3停止运动
[2025-09-29 07:20:50.183] [INFO] 电机7停止运动
[2025-09-29 07:20:50.184] [INFO] 电机1停止运动
[2025-09-29 07:20:50.184] [INFO] 电机5停止运动
[2025-09-29 07:20:50.184] [INFO] 电机2停止运动
[2025-09-29 07:20:50.184] [INFO] 所有电机已停止
[2025-09-29 07:20:50.185] [INFO] 电机监控循环被取消
[2025-09-29 07:20:50.185] [INFO] 电机监控循环结束
[2025-09-29 07:20:50.185] [INFO] 电机监控任务已停止
[2025-09-29 07:20:50.186] [INFO] 模拟释放运动控制卡资源
[2025-09-29 07:20:50.186] [INFO] MotorManager资源释放完成
[2025-09-29 07:20:50.187] [INFO] 开始释放ScannerManager资源...
[2025-09-29 07:20:50.212] [INFO] 扫描枪连接已断开
[2025-09-29 07:20:50.212] [INFO] 扫描枪状态变更: Disconnected - 扫描枪连接已断开
[2025-09-29 07:20:50.212] [INFO] ScannerManager资源释放完成
[2025-09-29 07:20:50.214] [INFO] 开始释放EpsonRobotManager资源...
[2025-09-29 07:20:50.218] [INFO] EpsonRobotManager资源释放完成
[2025-09-29 07:20:50.219] [INFO] 开始释放EpsonRobotManager2资源...
[2025-09-29 07:20:50.223] [INFO] EpsonRobotManager2资源释放完成
[2025-09-29 07:20:50.224] [INFO] 开始释放VisionManager资源...
[2025-09-29 07:20:50.226] [INFO] 模拟释放相机资源
[2025-09-29 07:20:50.226] [INFO] VisionManager资源释放完成
[2025-09-29 07:20:50.228] [INFO] 开始释放StatisticsManager资源...
[2025-09-29 07:20:50.228] [INFO] 自动保存任务已停止
[2025-09-29 07:20:50.231] [INFO] StatisticsManager资源释放完成
[2025-09-29 07:20:50.231] [INFO] 其他Manager资源释放完成
[2025-09-29 07:20:50.231] [INFO] 释放DMC1000B控制卡资源...
[2025-09-29 07:20:50.233] [INFO] DMC1000B控制卡未初始化，无需释放
[2025-09-29 07:20:50.233] [INFO] DMC1000B控制卡资源释放完成
[2025-09-29 07:20:50.233] [INFO] 所有Manager资源释放流程完成
[2025-09-29 07:20:50.233] [INFO] 所有资源释放完成，程序即将退出
[2025-09-29 07:20:50.234] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-29 07:20:52.532] [INFO] 程序启动开始
[2025-09-29 07:20:52.534] [INFO] 加载Settings系统配置...
[2025-09-29 07:20:52.541] [INFO] 设置加载成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-29 07:20:52.542] [INFO] Settings系统配置加载完成
[2025-09-29 07:20:52.543] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-29 07:20:52.573] [INFO] 开始初始化各个Manager...
[2025-09-29 07:20:52.573] [INFO] 初始化DMC1000B控制卡...
[2025-09-29 07:20:52.577] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-29 07:20:52.579] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-29 07:20:52.579] [WARN] DMC1000B控制卡初始化失败，相关功能可能无法正常工作
[2025-09-29 07:20:52.579] [INFO] 初始化基础Manager...
[2025-09-29 07:20:52.584] [INFO] IO状态缓存初始化完成
[2025-09-29 07:20:52.587] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-29 07:20:52.588] [WARN] DMC1000BIO管理器初始化失败
[2025-09-29 07:20:52.591] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-29 07:20:52.632] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-29 07:20:52.646] [WARN] DMC1000B电机管理器初始化失败
[2025-09-29 07:20:52.646] [INFO] 初始化系统模式管理器...
[2025-09-29 07:20:52.655] [INFO] 从Settings系统加载安全管理器配置成功
[2025-09-29 07:20:52.658] [INFO] 安全管理器实例已创建
[2025-09-29 07:20:52.661] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-29 07:20:52.662] [INFO] 开始初始化MotorManager...
[2025-09-29 07:20:52.662] [INFO] 恢复上次系统模式: Automatic
[2025-09-29 07:20:52.664] [INFO] 模拟初始化运动控制卡
[2025-09-29 07:20:52.665] [INFO] 开始切换到自动模式...
[2025-09-29 07:20:52.666] [INFO] 开始检查系统是否准备好进入自动模式...
[2025-09-29 07:20:52.669] [INFO] 系统基础检查完成，准备进入自动模式（具体硬件初始化将在开机自检中完成）
[2025-09-29 07:20:52.670] [INFO] 系统模式已从 Manual 切换到 Automatic
[2025-09-29 07:20:52.670] [INFO] 启动安全管理器...
[2025-09-29 07:20:52.673] [ERROR] 安全管理器未初始化，无法启动
[2025-09-29 07:20:52.674] [ERROR] 启动安全管理器失败，无法切换到自动模式
[2025-09-29 07:20:52.674] [ERROR] 恢复上次系统模式失败: Automatic，保持当前模式
[2025-09-29 07:20:52.675] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-29 07:20:52.675] [INFO] 系统模式已保存到配置文件: Automatic
[2025-09-29 07:20:52.872] [INFO] 加载了8个电机的默认配置
[2025-09-29 07:20:52.873] [INFO] 电机监控任务已启动
[2025-09-29 07:20:52.873] [INFO] MotorManager初始化完成
[2025-09-29 07:20:52.874] [INFO] 初始化通信Manager...
[2025-09-29 07:20:52.875] [INFO] 电机监控循环开始
[2025-09-29 07:20:52.877] [INFO] 开始初始化ScannerManager...
[2025-09-29 07:20:52.879] [INFO] 扫描枪配置从Settings系统加载: COM1, 9600, 8, One, None
[2025-09-29 07:20:52.881] [INFO] 串口初始化完成
[2025-09-29 07:20:52.883] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-29 07:20:52.886] [INFO] 扫描枪连接成功: COM1
[2025-09-29 07:20:52.886] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-29 07:20:52.887] [INFO] ScannerManager初始化完成
[2025-09-29 07:20:52.894] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-29 07:20:52.895] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-29 07:20:52.896] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-29 07:20:52.897] [INFO] SCARA通信管理器已初始化
[2025-09-29 07:20:52.899] [INFO] ScannerAutoModeManager管理器引用初始化完成
[2025-09-29 07:20:52.902] [INFO] 开始初始化ScannerAutoModeManager...
[2025-09-29 07:20:52.904] [INFO] 开始初始化MultiScannerManager...
[2025-09-29 07:20:52.906] [INFO] 开始初始化扫描枪1...
[2025-09-29 07:20:52.908] [INFO] 从Settings系统加载扫描枪1配置: COM1, 115200
[2025-09-29 07:20:52.912] [INFO] 扫描枪1串口初始化完成
[2025-09-29 07:20:52.912] [INFO] 扫描枪1初始化完成
[2025-09-29 07:20:52.913] [INFO] 开始初始化扫描枪2...
[2025-09-29 07:20:52.913] [INFO] 从Settings系统加载扫描枪2配置: COM2, 115200
[2025-09-29 07:20:52.914] [INFO] 扫描枪2串口初始化完成
[2025-09-29 07:20:52.914] [INFO] 扫描枪2初始化完成
[2025-09-29 07:20:52.915] [INFO] 开始初始化扫描枪3...
[2025-09-29 07:20:52.915] [INFO] 从Settings系统加载扫描枪3配置: COM3, 115200
[2025-09-29 07:20:52.916] [INFO] 扫描枪3串口初始化完成
[2025-09-29 07:20:52.916] [INFO] 扫描枪3初始化完成
[2025-09-29 07:20:52.917] [INFO] MultiScannerManager初始化完成
[2025-09-29 07:20:52.918] [INFO] ScannerAutoModeManager初始化完成
[2025-09-29 07:20:52.918] [INFO] 跳过Modbus TCP管理器初始化 - 由第三方控制
[2025-09-29 07:20:52.921] [INFO] 开始初始化EpsonRobotManager...
[2025-09-29 07:20:52.923] [INFO] 从Settings系统加载Epson机器人1配置成功
[2025-09-29 07:20:52.924] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-29 07:20:52.924] [INFO] EpsonRobotManager初始化完成
[2025-09-29 07:20:52.924] [INFO] 初始化视觉Manager...
[2025-09-29 07:20:52.927] [INFO] 开始初始化VisionManager...
[2025-09-29 07:20:52.927] [INFO] 视觉系统配置从Settings系统加载: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms, 置信度阈值=0.8
[2025-09-29 07:20:52.929] [INFO] 模拟初始化相机，索引: 0
[2025-09-29 07:20:53.444] [INFO] 相机初始化成功
[2025-09-29 07:20:53.445] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-29 07:20:53.445] [INFO] 视觉配置加载完成
[2025-09-29 07:20:53.445] [INFO] VisionManager初始化完成
[2025-09-29 07:20:53.446] [INFO] 初始化数据Manager...
[2025-09-29 07:20:53.449] [INFO] 开始初始化StatisticsManager...
[2025-09-29 07:20:53.450] [INFO] 统计管理器配置: 数据目录=Export, 缓存大小=1000, 自动保存间隔=24分钟
[2025-09-29 07:20:53.461] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-29 07:20:53.461] [INFO] 历史数据加载完成
[2025-09-29 07:20:53.462] [INFO] StatisticsManager初始化完成
[2025-09-29 07:20:53.462] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-29 07:20:53.462] [INFO] 所有Manager初始化完成
[2025-09-29 07:20:53.501] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-29 07:20:53.501] [INFO] 创建并缓存面板: vision-position
[2025-09-29 07:20:53.502] [INFO] 主界面布局创建完成
[2025-09-29 07:20:53.504] [INFO] 时间更新定时器初始化完成
[2025-09-29 07:20:53.504] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-29 07:20:53.597] [INFO] 开始初始化系统
[2025-09-29 07:20:53.598] [INFO] 初始化业务逻辑
[2025-09-29 07:20:53.599] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-29 07:20:53.599] [INFO] IO管理器初始化完成
[2025-09-29 07:20:53.599] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-29 07:20:53.674] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-29 07:20:53.675] [INFO] 电机管理器初始化完成
[2025-09-29 07:20:53.676] [INFO] IO事件订阅完成
[2025-09-29 07:20:53.676] [INFO] 电机事件订阅完成
[2025-09-29 07:20:53.676] [INFO] 业务层交互机制建立完成
[2025-09-29 07:20:53.677] [INFO] 业务逻辑初始化完成
[2025-09-29 07:20:53.678] [INFO] 执行UI界面刷新
[2025-09-29 07:20:53.681] [INFO] UI界面刷新完成
[2025-09-29 07:20:53.681] [INFO] 系统初始化完成
[2025-09-29 14:42:44.204] [INFO] 程序启动开始
[2025-09-29 14:42:44.205] [INFO] 加载Settings系统配置...
[2025-09-29 14:42:44.223] [INFO] 设置加载成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-29 14:42:44.223] [INFO] Settings系统配置加载完成
[2025-09-29 14:42:44.224] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-29 14:42:44.259] [INFO] 开始初始化各个Manager...
[2025-09-29 14:42:44.259] [INFO] 初始化DMC1000B控制卡...
[2025-09-29 14:42:44.263] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-29 14:42:44.266] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-29 14:42:44.270] [WARN] DMC1000B控制卡初始化失败，相关功能可能无法正常工作
[2025-09-29 14:42:44.271] [INFO] 初始化基础Manager...
[2025-09-29 14:42:44.279] [INFO] IO状态缓存初始化完成
[2025-09-29 14:42:44.281] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-29 14:42:44.282] [WARN] DMC1000BIO管理器初始化失败
[2025-09-29 14:42:44.287] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-29 14:42:44.337] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__45_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 136
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-29 14:42:44.352] [WARN] DMC1000B电机管理器初始化失败
[2025-09-29 14:42:44.352] [INFO] 初始化系统模式管理器...
[2025-09-29 14:42:44.362] [INFO] 从Settings系统加载安全管理器配置成功
[2025-09-29 14:42:44.367] [INFO] 安全管理器实例已创建
[2025-09-29 14:42:44.371] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-29 14:42:44.372] [INFO] 恢复上次系统模式: Automatic
[2025-09-29 14:42:44.375] [INFO] 开始初始化MotorManager...
[2025-09-29 14:42:44.375] [INFO] 开始切换到自动模式...
[2025-09-29 14:42:44.376] [INFO] 开始检查系统是否准备好进入自动模式...
[2025-09-29 14:42:44.378] [INFO] 模拟初始化运动控制卡
[2025-09-29 14:42:44.380] [INFO] 系统基础检查完成，准备进入自动模式（具体硬件初始化将在开机自检中完成）
[2025-09-29 14:42:44.384] [INFO] 系统模式已从 Manual 切换到 Automatic
[2025-09-29 14:42:44.384] [INFO] 启动安全管理器...
[2025-09-29 14:42:44.388] [ERROR] 安全管理器未初始化，无法启动
[2025-09-29 14:42:44.388] [ERROR] 启动安全管理器失败，无法切换到自动模式
[2025-09-29 14:42:44.388] [ERROR] 恢复上次系统模式失败: Automatic，保持当前模式
[2025-09-29 14:42:44.394] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-29 14:42:44.394] [INFO] 系统模式已保存到配置文件: Manual
[2025-09-29 14:42:44.592] [INFO] 加载了8个电机的默认配置
[2025-09-29 14:42:44.593] [INFO] 电机监控任务已启动
[2025-09-29 14:42:44.593] [INFO] MotorManager初始化完成
[2025-09-29 14:42:44.594] [INFO] 初始化通信Manager...
[2025-09-29 14:42:44.595] [INFO] 电机监控循环开始
[2025-09-29 14:42:44.597] [INFO] 开始初始化ScannerManager...
[2025-09-29 14:42:44.600] [INFO] 扫描枪配置从Settings系统加载: COM1, 9600, 8, One, None
[2025-09-29 14:42:44.602] [INFO] 串口初始化完成
[2025-09-29 14:42:44.604] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-29 14:42:44.608] [INFO] 扫描枪连接成功: COM1
[2025-09-29 14:42:44.608] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-29 14:42:44.608] [INFO] ScannerManager初始化完成
[2025-09-29 14:42:44.613] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-29 14:42:44.614] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-29 14:42:44.615] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-29 14:42:44.617] [INFO] SCARA通信管理器已初始化
[2025-09-29 14:42:44.621] [INFO] ScannerAutoModeManager管理器引用初始化完成
[2025-09-29 14:42:44.624] [INFO] 开始初始化ScannerAutoModeManager...
[2025-09-29 14:42:44.626] [INFO] 开始初始化MultiScannerManager...
[2025-09-29 14:42:44.629] [INFO] 开始初始化扫描枪1...
[2025-09-29 14:42:44.630] [INFO] 从Settings系统加载扫描枪1配置: COM1, 115200
[2025-09-29 14:42:44.633] [INFO] 扫描枪1串口初始化完成
[2025-09-29 14:42:44.633] [INFO] 扫描枪1初始化完成
[2025-09-29 14:42:44.634] [INFO] 开始初始化扫描枪2...
[2025-09-29 14:42:44.634] [INFO] 从Settings系统加载扫描枪2配置: COM2, 115200
[2025-09-29 14:42:44.658] [INFO] 扫描枪2串口初始化完成
[2025-09-29 14:42:44.658] [INFO] 扫描枪2初始化完成
[2025-09-29 14:42:44.659] [INFO] 开始初始化扫描枪3...
[2025-09-29 14:42:44.659] [INFO] 从Settings系统加载扫描枪3配置: COM3, 115200
[2025-09-29 14:42:44.659] [INFO] 扫描枪3串口初始化完成
[2025-09-29 14:42:44.660] [INFO] 扫描枪3初始化完成
[2025-09-29 14:42:44.661] [INFO] MultiScannerManager初始化完成
[2025-09-29 14:42:44.661] [INFO] ScannerAutoModeManager初始化完成
[2025-09-29 14:42:44.663] [INFO] 跳过Modbus TCP管理器初始化 - 由第三方控制
[2025-09-29 14:42:44.666] [INFO] 开始初始化EpsonRobotManager...
[2025-09-29 14:42:44.668] [INFO] 从Settings系统加载Epson机器人1配置成功
[2025-09-29 14:42:44.669] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-29 14:42:44.669] [INFO] EpsonRobotManager初始化完成
[2025-09-29 14:42:44.669] [INFO] 初始化视觉Manager...
[2025-09-29 14:42:44.673] [INFO] 开始初始化VisionManager...
[2025-09-29 14:42:44.674] [INFO] 视觉系统配置从Settings系统加载: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms, 置信度阈值=0.8
[2025-09-29 14:42:44.676] [INFO] 模拟初始化相机，索引: 0
[2025-09-29 14:42:45.194] [INFO] 相机初始化成功
[2025-09-29 14:42:45.195] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-29 14:42:45.195] [INFO] 视觉配置加载完成
[2025-09-29 14:42:45.196] [INFO] VisionManager初始化完成
[2025-09-29 14:42:45.196] [INFO] 初始化数据Manager...
[2025-09-29 14:42:45.199] [INFO] 开始初始化StatisticsManager...
[2025-09-29 14:42:45.199] [INFO] 统计管理器配置: 数据目录=Export, 缓存大小=1000, 自动保存间隔=24分钟
[2025-09-29 14:42:45.212] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-29 14:42:45.212] [INFO] 历史数据加载完成
[2025-09-29 14:42:45.212] [INFO] StatisticsManager初始化完成
[2025-09-29 14:42:45.212] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-29 14:42:45.212] [INFO] 所有Manager初始化完成
[2025-09-29 14:42:45.257] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-29 14:42:45.257] [INFO] 创建并缓存面板: vision-position
[2025-09-29 14:42:45.257] [INFO] 主界面布局创建完成
[2025-09-29 14:42:45.259] [INFO] 时间更新定时器初始化完成
[2025-09-29 14:42:45.260] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-29 14:42:45.359] [INFO] 开始初始化系统
[2025-09-29 14:42:45.360] [INFO] 初始化业务逻辑
[2025-09-29 14:42:45.362] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-29 14:42:45.363] [INFO] IO管理器初始化完成
[2025-09-29 14:42:45.363] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-29 14:42:45.450] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__45_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 136
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-29 14:42:45.451] [INFO] 电机管理器初始化完成
[2025-09-29 14:42:45.452] [INFO] IO事件订阅完成
[2025-09-29 14:42:45.453] [INFO] 电机事件订阅完成
[2025-09-29 14:42:45.453] [INFO] 业务层交互机制建立完成
[2025-09-29 14:42:45.453] [INFO] 业务逻辑初始化完成
[2025-09-29 14:42:45.454] [INFO] 执行UI界面刷新
[2025-09-29 14:42:45.457] [INFO] UI界面刷新完成
[2025-09-29 14:42:45.457] [INFO] 系统初始化完成
[2025-09-29 14:42:48.520] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-29 14:42:48.521] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-29 14:42:48.530] [INFO] UI参数显示已从Settings系统更新
[2025-09-29 14:42:48.530] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-29 14:42:48.530] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-29 14:42:48.530] [INFO] 创建并缓存面板: motor-flip-params
[2025-09-29 14:42:50.914] [INFO] 翻转电机示教面板业务逻辑初始化完成
[2025-09-29 14:42:50.928] [INFO] 翻转电机示教面板初始化完成 - 按HTML原型设计
[2025-09-29 14:42:50.928] [INFO] 创建并缓存面板: motor-flip-teach
[2025-09-29 14:43:01.366] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-29 14:43:01.368] [INFO] 保存Settings系统配置...
[2025-09-29 14:43:01.369] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-29 14:43:01.369] [INFO] Settings系统配置保存完成
[2025-09-29 14:43:01.372] [INFO] 开始按顺序释放各个Manager资源...
[2025-09-29 14:43:01.373] [INFO] 清理UI资源和面板缓存...
[2025-09-29 14:43:01.373] [INFO] 时间更新定时器资源已释放
[2025-09-29 14:43:01.374] [INFO] 开始清理面板缓存，共3个面板
[2025-09-29 14:43:01.383] [INFO] 翻转电机控制面板资源释放完成
[2025-09-29 14:43:01.387] [INFO] 翻转电机示教面板资源释放完成
[2025-09-29 14:43:01.390] [INFO] 面板缓存字典已清空
[2025-09-29 14:43:01.390] [INFO] 面板缓存清理完成
[2025-09-29 14:43:01.390] [INFO] UI资源清理完成
[2025-09-29 14:43:01.390] [INFO] 释放工作流管理器资源...
[2025-09-29 14:43:01.393] [INFO] 开始释放WorkflowManager资源...
[2025-09-29 14:43:01.395] [INFO] 工作流已经处于空闲状态
[2025-09-29 14:43:01.396] [INFO] 开始取消订阅各Manager事件...
[2025-09-29 14:43:01.396] [INFO] Manager事件取消订阅完成
[2025-09-29 14:43:01.396] [INFO] WorkflowManager资源释放完成
[2025-09-29 14:43:01.396] [INFO] 工作流管理器资源释放完成
[2025-09-29 14:43:01.396] [INFO] 释放启动自检管理器资源...
[2025-09-29 14:43:01.397] [INFO] 开始释放启动自检管理器资源...
[2025-09-29 14:43:01.397] [INFO] 启动自检管理器资源释放完成
[2025-09-29 14:43:01.397] [INFO] 启动自检管理器资源释放完成
[2025-09-29 14:43:01.397] [INFO] 释放DMC1000B电机管理器资源...
[2025-09-29 14:43:01.399] [INFO] 开始释放DMC1000B资源...
[2025-09-29 14:43:01.416] [INFO] 停止所有电机部分失败
[2025-09-29 14:43:01.417] [INFO] DMC1000B资源释放完成
[2025-09-29 14:43:01.417] [INFO] DMC1000B电机管理器资源释放完成
[2025-09-29 14:43:01.418] [INFO] 释放DMC1000BIO管理器资源...
[2025-09-29 14:43:01.421] [INFO] DMC1000BIO管理器资源释放完成
[2025-09-29 14:43:01.421] [INFO] 释放其他Manager资源...
[2025-09-29 14:43:01.423] [INFO] 开始释放MotorManager资源...
[2025-09-29 14:43:01.440] [INFO] 电机0停止运动
[2025-09-29 14:43:01.442] [INFO] 电机1停止运动
[2025-09-29 14:43:01.444] [INFO] 电机5停止运动
[2025-09-29 14:43:01.444] [INFO] 电机2停止运动
[2025-09-29 14:43:01.444] [INFO] 电机4停止运动
[2025-09-29 14:43:01.445] [INFO] 电机3停止运动
[2025-09-29 14:43:01.445] [INFO] 电机6停止运动
[2025-09-29 14:43:01.452] [INFO] 电机7停止运动
[2025-09-29 14:43:01.455] [INFO] 所有电机已停止
[2025-09-29 14:43:01.489] [INFO] 电机监控循环被取消
[2025-09-29 14:43:01.489] [INFO] 电机监控循环结束
[2025-09-29 14:43:01.490] [INFO] 电机监控任务已停止
[2025-09-29 14:43:01.490] [INFO] 模拟释放运动控制卡资源
[2025-09-29 14:43:01.490] [INFO] MotorManager资源释放完成
[2025-09-29 14:43:01.492] [INFO] 开始释放ScannerManager资源...
[2025-09-29 14:43:01.515] [INFO] 扫描枪连接已断开
[2025-09-29 14:43:01.515] [INFO] 扫描枪状态变更: Disconnected - 扫描枪连接已断开
[2025-09-29 14:43:01.515] [INFO] ScannerManager资源释放完成
[2025-09-29 14:43:01.517] [INFO] 开始释放EpsonRobotManager资源...
[2025-09-29 14:43:01.523] [INFO] EpsonRobotManager资源释放完成
[2025-09-29 14:43:01.525] [INFO] 开始释放EpsonRobotManager2资源...
[2025-09-29 14:43:01.530] [INFO] EpsonRobotManager2资源释放完成
[2025-09-29 14:43:01.532] [INFO] 开始释放VisionManager资源...
[2025-09-29 14:43:01.534] [INFO] 模拟释放相机资源
[2025-09-29 14:43:01.534] [INFO] VisionManager资源释放完成
[2025-09-29 14:43:01.536] [INFO] 开始释放StatisticsManager资源...
[2025-09-29 14:43:01.537] [INFO] 自动保存任务已停止
[2025-09-29 14:43:01.540] [INFO] StatisticsManager资源释放完成
[2025-09-29 14:43:01.540] [INFO] 其他Manager资源释放完成
[2025-09-29 14:43:01.541] [INFO] 释放DMC1000B控制卡资源...
[2025-09-29 14:43:01.546] [INFO] DMC1000B控制卡未初始化，无需释放
[2025-09-29 14:43:01.546] [INFO] DMC1000B控制卡资源释放完成
[2025-09-29 14:43:01.546] [INFO] 所有Manager资源释放流程完成
[2025-09-29 14:43:01.547] [INFO] 所有资源释放完成，程序即将退出
[2025-09-29 14:43:01.548] [INFO] 程序正在关闭，开始释放所有Manager资源...
