// TODO: 临时模拟功能，SCARA机器人开发完成后需要删除
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using MyHMI.Helpers;
using MyHMI.Managers;

namespace MyHMI.UI
{
    /// <summary>
    /// SCARA机器人模拟器窗体 - 仅用于测试
    /// 注意：这是临时模拟功能，包含Simulator标识，后期需要删除
    /// </summary>
    public partial class ScaraRobotSimulatorForm : Form
    {
        #region 私有字段

        private ScaraCommunicationManager _communicationManager;
        private Timer _refreshTimer;
        private int _operationCount = 0;
        private DateTime _lastOperationTime = DateTime.Now;

        // 左机器人控件
        private GroupBox _leftRobotGroup;
        private Dictionary<string, CheckBox> _leftRobotCheckBoxes;
        private Dictionary<string, Label> _leftRobotValueLabels;

        // 右机器人控件
        private GroupBox _rightRobotGroup;
        private Dictionary<string, CheckBox> _rightRobotCheckBoxes;
        private Dictionary<string, Label> _rightRobotValueLabels;

        // 扫码器控件
        private GroupBox _scannerGroup;
        private Dictionary<string, CheckBox> _scannerCheckBoxes;
        private Dictionary<string, Label> _scannerValueLabels;

        // 系统控件
        private GroupBox _systemGroup;
        private CheckBox _allSaveCheckBox;
        private Label _allSaveValueLabel;

        // 功能按钮
        private Button _confirmReadBtn;
        private Button _resetAllBtn;
        private Button _simulateWorkflowBtn;
        private Button _refreshStatusBtn;

        // 状态栏
        private StatusStrip _statusStrip;
        private ToolStripStatusLabel _lastOperationLabel;
        private ToolStripStatusLabel _operationCountLabel;

        // 字段定义
        private readonly Dictionary<string, string> _leftRobotFields = new Dictionary<string, string>
        {
            { "L_moto_ready", "左电机准备完毕" },
            { "L_position_Arrived", "左位置到达" },
            { "L_gripper_ok", "左夹爪夹紧" },
            { "L_Angle_ok", "左角度矫正完成" },
            { "L_safe_ok", "左安全距离确认" },
            { "L_dataget_ok", "左数据获取完成" },
            { "L_moto_finish", "左电机工作完成" }
        };

        private readonly Dictionary<string, string> _rightRobotFields = new Dictionary<string, string>
        {
            { "R_moto_ready", "右电机准备完毕" },
            { "R_position_Arrived", "右位置到达" },
            { "R_gripper_ok", "右夹爪夹紧" },
            { "R_Angle_ok", "右角度矫正完成" },
            { "R_safe_ok", "右安全距离确认" },
            { "R_dataget_ok", "右数据获取完成" },
            { "R_moto_finish", "右电机工作完成" }
        };

        private readonly Dictionary<string, string> _scannerFields = new Dictionary<string, string>
        {
            { "ML_dataget_ok", "中间向左扫码器数据获取" },
            { "MR_dataget_ok", "中间向右扫码器数据获取" }
        };

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public ScaraRobotSimulatorForm()
        {
            InitializeComponent();
            InitializeSimulatorControls();
            InitializeSimulatorTimer();
            InitializeSimulatorEvents();

            LogHelper.Info("SCARA模拟器: 模拟器窗体初始化完成");
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化模拟器控件
        /// </summary>
        private void InitializeSimulatorControls()
        {
            try
            {
                // 获取通信管理器实例
                _communicationManager = ScaraCommunicationManager.Instance;

                // 设置窗体属性
                this.Text = "SCARA机器人模拟器 - 仅用于测试";
                this.Size = new Size(1000, 700);
                this.StartPosition = FormStartPosition.CenterScreen;
                this.FormBorderStyle = FormBorderStyle.Sizable;
                this.MinimumSize = new Size(800, 600);

                // 创建主布局
                CreateSimulatorLayout();

                LogHelper.Info("SCARA模拟器: 控件初始化完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("SCARA模拟器: 初始化控件失败", ex);
                MessageBox.Show($"模拟器初始化失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 创建模拟器布局
        /// </summary>
        private void CreateSimulatorLayout()
        {
            // 创建主面板
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3,
                Padding = new Padding(10)
            };

            // 设置列宽比例
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));

            // 设置行高比例
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 40F)); // 左右机器人
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 30F)); // 扫码器和系统
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 80F)); // 按钮区域

            // 创建左机器人组
            CreateLeftRobotSimulatorGroup();
            mainPanel.Controls.Add(_leftRobotGroup, 0, 0);

            // 创建右机器人组
            CreateRightRobotSimulatorGroup();
            mainPanel.Controls.Add(_rightRobotGroup, 1, 0);

            // 创建扫码器组
            CreateScannerSimulatorGroup();
            mainPanel.Controls.Add(_scannerGroup, 0, 1);

            // 创建系统组
            CreateSystemSimulatorGroup();
            mainPanel.Controls.Add(_systemGroup, 1, 1);

            // 创建按钮面板
            var buttonPanel = CreateSimulatorButtonPanel();
            mainPanel.SetColumnSpan(buttonPanel, 2);
            mainPanel.Controls.Add(buttonPanel, 0, 2);

            // 创建状态栏
            CreateSimulatorStatusBar();

            this.Controls.Add(mainPanel);
            this.Controls.Add(_statusStrip);
        }

        /// <summary>
        /// 创建左机器人模拟器组
        /// </summary>
        private void CreateLeftRobotSimulatorGroup()
        {
            _leftRobotGroup = new GroupBox
            {
                Text = "左机器人模拟控制",
                Dock = DockStyle.Fill,
                BackColor = Color.LightBlue,
                Margin = new Padding(5)
            };

            _leftRobotCheckBoxes = new Dictionary<string, CheckBox>();
            _leftRobotValueLabels = new Dictionary<string, Label>();

            CreateFieldSimulatorControls(_leftRobotGroup, _leftRobotFields, _leftRobotCheckBoxes, _leftRobotValueLabels);
        }

        /// <summary>
        /// 创建右机器人模拟器组
        /// </summary>
        private void CreateRightRobotSimulatorGroup()
        {
            _rightRobotGroup = new GroupBox
            {
                Text = "右机器人模拟控制",
                Dock = DockStyle.Fill,
                BackColor = Color.LightGreen,
                Margin = new Padding(5)
            };

            _rightRobotCheckBoxes = new Dictionary<string, CheckBox>();
            _rightRobotValueLabels = new Dictionary<string, Label>();

            CreateFieldSimulatorControls(_rightRobotGroup, _rightRobotFields, _rightRobotCheckBoxes, _rightRobotValueLabels);
        }

        /// <summary>
        /// 创建扫码器模拟器组
        /// </summary>
        private void CreateScannerSimulatorGroup()
        {
            _scannerGroup = new GroupBox
            {
                Text = "扫码器模拟控制",
                Dock = DockStyle.Fill,
                BackColor = Color.LightYellow,
                Margin = new Padding(5)
            };

            _scannerCheckBoxes = new Dictionary<string, CheckBox>();
            _scannerValueLabels = new Dictionary<string, Label>();

            CreateFieldSimulatorControls(_scannerGroup, _scannerFields, _scannerCheckBoxes, _scannerValueLabels);
        }

        /// <summary>
        /// 创建系统模拟器组
        /// </summary>
        private void CreateSystemSimulatorGroup()
        {
            _systemGroup = new GroupBox
            {
                Text = "系统状态模拟控制",
                Dock = DockStyle.Fill,
                BackColor = Color.LightCoral,
                Margin = new Padding(5)
            };

            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            _allSaveCheckBox = new CheckBox
            {
                Text = "系统运行状态 (all_save)",
                Location = new Point(10, 30),
                Size = new Size(200, 25),
                Checked = true
            };

            _allSaveValueLabel = new Label
            {
                Text = "当前值: True",
                Location = new Point(220, 32),
                Size = new Size(100, 20),
                ForeColor = Color.Green,
                Font = new Font("微软雅黑", 9F, FontStyle.Bold)
            };

            // 添加工具提示
            var toolTip = new ToolTip();
            toolTip.SetToolTip(_allSaveCheckBox, "true: 启动和运行时; false: 触发停止时");

            panel.Controls.AddRange(new Control[] { _allSaveCheckBox, _allSaveValueLabel });
            _systemGroup.Controls.Add(panel);
        }

        /// <summary>
        /// 创建字段模拟器控件
        /// </summary>
        private void CreateFieldSimulatorControls(GroupBox group, Dictionary<string, string> fields,
            Dictionary<string, CheckBox> checkBoxes, Dictionary<string, Label> valueLabels)
        {
            var panel = new Panel { Dock = DockStyle.Fill, AutoScroll = true, Padding = new Padding(10) };
            int y = 10;

            foreach (var field in fields)
            {
                // 创建CheckBox
                var checkBox = new CheckBox
                {
                    Text = field.Value,
                    Location = new Point(10, y),
                    Size = new Size(200, 25),
                    Tag = field.Key
                };

                // 创建当前值显示Label
                var valueLabel = new Label
                {
                    Text = "当前值: False",
                    Location = new Point(220, y + 2),
                    Size = new Size(100, 20),
                    ForeColor = Color.Red,
                    Font = new Font("微软雅黑", 9F, FontStyle.Bold)
                };

                // 添加工具提示
                var toolTip = new ToolTip();
                toolTip.SetToolTip(checkBox, $"字段名: {field.Key}\n描述: {field.Value}");

                checkBoxes[field.Key] = checkBox;
                valueLabels[field.Key] = valueLabel;

                panel.Controls.AddRange(new Control[] { checkBox, valueLabel });
                y += 35;
            }

            group.Controls.Add(panel);
        }

        /// <summary>
        /// 创建模拟器按钮面板
        /// </summary>
        private Panel CreateSimulatorButtonPanel()
        {
            var buttonPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.LightGray,
                Margin = new Padding(5)
            };

            _confirmReadBtn = new Button
            {
                Text = "确认阅读",
                Size = new Size(120, 35),
                Location = new Point(50, 20),
                BackColor = Color.Orange,
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 10F, FontStyle.Bold)
            };

            _resetAllBtn = new Button
            {
                Text = "全部复位",
                Size = new Size(120, 35),
                Location = new Point(200, 20),
                BackColor = Color.Red,
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 10F, FontStyle.Bold)
            };

            _simulateWorkflowBtn = new Button
            {
                Text = "模拟完整流程",
                Size = new Size(120, 35),
                Location = new Point(350, 20),
                BackColor = Color.Green,
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 10F, FontStyle.Bold)
            };

            _refreshStatusBtn = new Button
            {
                Text = "刷新状态",
                Size = new Size(120, 35),
                Location = new Point(500, 20),
                BackColor = Color.Blue,
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 10F, FontStyle.Bold)
            };

            buttonPanel.Controls.AddRange(new Control[] {
                _confirmReadBtn, _resetAllBtn, _simulateWorkflowBtn, _refreshStatusBtn
            });

            return buttonPanel;
        }

        /// <summary>
        /// 创建模拟器状态栏
        /// </summary>
        private void CreateSimulatorStatusBar()
        {
            _statusStrip = new StatusStrip();

            _lastOperationLabel = new ToolStripStatusLabel
            {
                Text = $"最后操作时间: {_lastOperationTime:HH:mm:ss}",
                Spring = true,
                TextAlign = ContentAlignment.MiddleLeft
            };

            _operationCountLabel = new ToolStripStatusLabel
            {
                Text = $"操作计数: {_operationCount}",
                TextAlign = ContentAlignment.MiddleRight
            };

            _statusStrip.Items.AddRange(new ToolStripItem[] { _lastOperationLabel, _operationCountLabel });
        }

        /// <summary>
        /// 初始化模拟器定时器
        /// </summary>
        private void InitializeSimulatorTimer()
        {
            _refreshTimer = new Timer
            {
                Interval = 500, // 500ms刷新间隔
                Enabled = false
            };

            _refreshTimer.Tick += RefreshSimulatorTimer_Tick;
        }

        /// <summary>
        /// 初始化模拟器事件
        /// </summary>
        private void InitializeSimulatorEvents()
        {
            // 窗体事件
            this.FormClosing += ScaraRobotSimulatorForm_FormClosing;
            this.Shown += ScaraRobotSimulatorForm_Shown;

            // 按钮事件
            _confirmReadBtn.Click += ConfirmReadSimulatorBtn_Click;
            _resetAllBtn.Click += ResetAllSimulatorBtn_Click;
            _simulateWorkflowBtn.Click += SimulateWorkflowSimulatorBtn_Click;
            _refreshStatusBtn.Click += RefreshStatusSimulatorBtn_Click;

            // CheckBox事件
            BindSimulatorCheckBoxEvents();
        }

        /// <summary>
        /// 绑定模拟器CheckBox事件
        /// </summary>
        private void BindSimulatorCheckBoxEvents()
        {
            // 左机器人CheckBox事件
            foreach (var checkBox in _leftRobotCheckBoxes.Values)
            {
                checkBox.CheckedChanged += SimulatorCheckBox_CheckedChanged;
            }

            // 右机器人CheckBox事件
            foreach (var checkBox in _rightRobotCheckBoxes.Values)
            {
                checkBox.CheckedChanged += SimulatorCheckBox_CheckedChanged;
            }

            // 扫码器CheckBox事件
            foreach (var checkBox in _scannerCheckBoxes.Values)
            {
                checkBox.CheckedChanged += SimulatorCheckBox_CheckedChanged;
            }

            // 系统CheckBox事件
            _allSaveCheckBox.CheckedChanged += SimulatorCheckBox_CheckedChanged;
            _allSaveCheckBox.Tag = "all_save";
        }

        #endregion

        #region 事件处理方法

        /// <summary>
        /// 窗体显示事件
        /// </summary>
        private void ScaraRobotSimulatorForm_Shown(object sender, EventArgs e)
        {
            try
            {
                // 启动定时器
                _refreshTimer.Start();

                // 刷新初始状态
                RefreshAllSimulatorFieldValues();

                LogHelper.Info("SCARA模拟器: 模拟器窗体已显示，定时器已启动");
            }
            catch (Exception ex)
            {
                LogHelper.Error("SCARA模拟器: 窗体显示事件处理失败", ex);
            }
        }

        /// <summary>
        /// 窗体关闭事件
        /// </summary>
        private void ScaraRobotSimulatorForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                // 隐藏窗体而不是关闭
                e.Cancel = true;
                this.Hide();

                // 停止定时器
                _refreshTimer?.Stop();

                LogHelper.Info("SCARA模拟器: 模拟器窗体已隐藏，定时器已停止");
            }
            catch (Exception ex)
            {
                LogHelper.Error("SCARA模拟器: 窗体关闭事件处理失败", ex);
            }
        }

        /// <summary>
        /// CheckBox状态变化事件
        /// </summary>
        private void SimulatorCheckBox_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                var checkBox = sender as CheckBox;
                if (checkBox?.Tag == null) return;

                string fieldName = checkBox.Tag.ToString();
                bool newValue = checkBox.Checked;

                // 更新通信管理器中的字段值
                UpdateSimulatorCommunicationField(fieldName, newValue);

                // 更新操作统计
                UpdateSimulatorOperationStats($"设置 {fieldName} = {newValue}");

                LogHelper.Info($"SCARA模拟器: 字段 {fieldName} 设置为 {newValue}");
            }
            catch (Exception ex)
            {
                LogHelper.Error("SCARA模拟器: CheckBox状态变化处理失败", ex);
            }
        }

        /// <summary>
        /// 确认阅读按钮点击事件
        /// </summary>
        private void ConfirmReadSimulatorBtn_Click(object sender, EventArgs e)
        {
            try
            {
                int resetCount = 0;

                // 获取所有值为true的字段并复位为false
                var allStates = _communicationManager.GetAllFieldStates();
                foreach (var state in allStates.Where(s => s.Value))
                {
                    UpdateSimulatorCommunicationField(state.Key, false);
                    resetCount++;
                }

                // 更新UI显示
                RefreshAllSimulatorFieldValues();

                UpdateSimulatorOperationStats($"确认阅读操作，复位了 {resetCount} 个字段");
                LogHelper.Info($"SCARA模拟器: 确认阅读操作完成，复位了 {resetCount} 个字段");

                MessageBox.Show($"确认阅读操作完成！\n复位了 {resetCount} 个值为1的字段。",
                    "操作完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                LogHelper.Error("SCARA模拟器: 确认阅读操作失败", ex);
                MessageBox.Show($"确认阅读操作失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 全部复位按钮点击事件
        /// </summary>
        private void ResetAllSimulatorBtn_Click(object sender, EventArgs e)
        {
            try
            {
                var result = MessageBox.Show("确定要复位所有字段为0吗？", "确认操作",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // 复位所有字段
                    _communicationManager.ResetAllFields();

                    // 更新UI显示
                    RefreshAllSimulatorFieldValues();

                    UpdateSimulatorOperationStats("全部复位操作");
                    LogHelper.Info("SCARA模拟器: 全部复位操作完成");

                    MessageBox.Show("所有字段已复位为0！", "操作完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("SCARA模拟器: 全部复位操作失败", ex);
                MessageBox.Show($"全部复位操作失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 模拟完整流程按钮点击事件
        /// </summary>
        private async void SimulateWorkflowSimulatorBtn_Click(object sender, EventArgs e)
        {
            try
            {
                var result = MessageBox.Show("确定要模拟完整工作流程吗？\n这将按照典型流程自动设置字段序列。",
                    "确认操作", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    await ExecuteSimulatorWorkflowSequence();
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("SCARA模拟器: 模拟完整流程失败", ex);
                MessageBox.Show($"模拟完整流程失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 刷新状态按钮点击事件
        /// </summary>
        private void RefreshStatusSimulatorBtn_Click(object sender, EventArgs e)
        {
            try
            {
                RefreshAllSimulatorFieldValues();
                UpdateSimulatorOperationStats("手动刷新状态");
                LogHelper.Info("SCARA模拟器: 手动刷新状态完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("SCARA模拟器: 刷新状态失败", ex);
                MessageBox.Show($"刷新状态失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 定时器刷新事件
        /// </summary>
        private void RefreshSimulatorTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                if (this.Visible)
                {
                    RefreshAllSimulatorFieldValues();
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("SCARA模拟器: 定时器刷新失败", ex);
            }
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 更新通信字段值
        /// </summary>
        private void UpdateSimulatorCommunicationField(string fieldName, bool value)
        {
            try
            {
                // 使用反射设置字段值
                var property = typeof(ScaraCommunicationManager).GetProperty(fieldName);
                if (property != null && property.CanWrite)
                {
                    property.SetValue(_communicationManager, value);
                }
                else
                {
                    LogHelper.Warning($"SCARA模拟器: 未找到字段 {fieldName} 或字段不可写");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"SCARA模拟器: 更新字段 {fieldName} 失败", ex);
            }
        }

        /// <summary>
        /// 刷新所有字段值显示
        /// </summary>
        private void RefreshAllSimulatorFieldValues()
        {
            try
            {
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(RefreshAllSimulatorFieldValues));
                    return;
                }

                var allStates = _communicationManager.GetAllFieldStates();

                // 更新左机器人字段
                UpdateSimulatorFieldGroup(_leftRobotCheckBoxes, _leftRobotValueLabels, allStates);

                // 更新右机器人字段
                UpdateSimulatorFieldGroup(_rightRobotCheckBoxes, _rightRobotValueLabels, allStates);

                // 更新扫码器字段
                UpdateSimulatorFieldGroup(_scannerCheckBoxes, _scannerValueLabels, allStates);

                // 更新系统字段
                if (allStates.ContainsKey("all_save"))
                {
                    bool value = allStates["all_save"];
                    _allSaveCheckBox.Checked = value;
                    _allSaveValueLabel.Text = $"当前值: {value}";
                    _allSaveValueLabel.ForeColor = value ? Color.Green : Color.Red;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("SCARA模拟器: 刷新字段值显示失败", ex);
            }
        }

        /// <summary>
        /// 更新字段组显示
        /// </summary>
        private void UpdateSimulatorFieldGroup(Dictionary<string, CheckBox> checkBoxes,
            Dictionary<string, Label> valueLabels, Dictionary<string, bool> allStates)
        {
            foreach (var kvp in checkBoxes)
            {
                string fieldName = kvp.Key;
                var checkBox = kvp.Value;
                var valueLabel = valueLabels[fieldName];

                if (allStates.ContainsKey(fieldName))
                {
                    bool value = allStates[fieldName];
                    checkBox.Checked = value;
                    valueLabel.Text = $"当前值: {value}";
                    valueLabel.ForeColor = value ? Color.Green : Color.Red;

                    // 添加视觉反馈效果
                    if (value)
                    {
                        checkBox.BackColor = Color.LightGreen;
                    }
                    else
                    {
                        checkBox.BackColor = SystemColors.Control;
                    }
                }
            }
        }

        /// <summary>
        /// 更新操作统计
        /// </summary>
        private void UpdateSimulatorOperationStats(string operation)
        {
            try
            {
                _operationCount++;
                _lastOperationTime = DateTime.Now;

                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() => UpdateSimulatorOperationStats(operation)));
                    return;
                }

                _lastOperationLabel.Text = $"最后操作: {operation} ({_lastOperationTime:HH:mm:ss})";
                _operationCountLabel.Text = $"操作计数: {_operationCount}";
            }
            catch (Exception ex)
            {
                LogHelper.Error("SCARA模拟器: 更新操作统计失败", ex);
            }
        }

        /// <summary>
        /// 执行模拟器工作流程序列
        /// </summary>
        private async Task ExecuteSimulatorWorkflowSequence()
        {
            try
            {
                LogHelper.Info("SCARA模拟器: 开始执行模拟工作流程序列");

                // 先复位所有字段
                _communicationManager.ResetAllFields();
                await Task.Delay(500);

                // 步骤1: 电机准备完毕
                UpdateSimulatorCommunicationField("L_moto_ready", true);
                UpdateSimulatorCommunicationField("R_moto_ready", true);
                await Task.Delay(1000);

                // 步骤2: 位置到达
                UpdateSimulatorCommunicationField("L_position_Arrived", true);
                UpdateSimulatorCommunicationField("R_position_Arrived", true);
                await Task.Delay(1000);

                // 步骤3: 夹爪夹紧
                UpdateSimulatorCommunicationField("L_gripper_ok", true);
                UpdateSimulatorCommunicationField("R_gripper_ok", true);
                await Task.Delay(1000);

                // 步骤4: 角度矫正完成
                UpdateSimulatorCommunicationField("L_Angle_ok", true);
                UpdateSimulatorCommunicationField("R_Angle_ok", true);
                await Task.Delay(1000);

                // 步骤5: 安全距离确认
                UpdateSimulatorCommunicationField("L_safe_ok", true);
                UpdateSimulatorCommunicationField("R_safe_ok", true);
                await Task.Delay(1000);

                // 步骤6: 扫码器数据获取
                UpdateSimulatorCommunicationField("ML_dataget_ok", true);
                UpdateSimulatorCommunicationField("MR_dataget_ok", true);
                await Task.Delay(1000);

                // 步骤7: 数据获取完成
                UpdateSimulatorCommunicationField("L_dataget_ok", true);
                UpdateSimulatorCommunicationField("R_dataget_ok", true);
                await Task.Delay(1000);

                // 步骤8: 电机工作完成
                UpdateSimulatorCommunicationField("L_moto_finish", true);
                UpdateSimulatorCommunicationField("R_moto_finish", true);

                UpdateSimulatorOperationStats("模拟完整流程序列");
                LogHelper.Info("SCARA模拟器: 模拟工作流程序列执行完成");

                MessageBox.Show("模拟完整流程序列执行完成！\n所有步骤已按顺序设置完毕。",
                    "流程完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                LogHelper.Error("SCARA模拟器: 执行模拟工作流程序列失败", ex);
                MessageBox.Show($"执行模拟流程失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 显示模拟器窗体并刷新状态
        /// </summary>
        public void ShowSimulatorAndRefresh()
        {
            try
            {
                this.Show();
                this.BringToFront();
                RefreshAllSimulatorFieldValues();

                if (!_refreshTimer.Enabled)
                {
                    _refreshTimer.Start();
                }

                LogHelper.Info("SCARA模拟器: 模拟器窗体已显示并刷新");
            }
            catch (Exception ex)
            {
                LogHelper.Error("SCARA模拟器: 显示模拟器窗体失败", ex);
            }
        }

        /// <summary>
        /// 隐藏模拟器窗体
        /// </summary>
        public void HideSimulator()
        {
            try
            {
                this.Hide();
                _refreshTimer?.Stop();
                LogHelper.Info("SCARA模拟器: 模拟器窗体已隐藏");
            }
            catch (Exception ex)
            {
                LogHelper.Error("SCARA模拟器: 隐藏模拟器窗体失败", ex);
            }
        }

        /// <summary>
        /// 释放模拟器资源
        /// </summary>
        public void DisposeSimulator()
        {
            try
            {
                _refreshTimer?.Stop();
                _refreshTimer?.Dispose();
                LogHelper.Info("SCARA模拟器: 模拟器资源已释放");
            }
            catch (Exception ex)
            {
                LogHelper.Error("SCARA模拟器: 释放模拟器资源失败", ex);
            }
        }

        #endregion
    }
}
