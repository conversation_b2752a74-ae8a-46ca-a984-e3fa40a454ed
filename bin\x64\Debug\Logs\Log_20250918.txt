[2025-09-18 20:22:01.175] [INFO] 程序启动开始
[2025-09-18 20:22:01.178] [INFO] 配置系统初始化成功
[2025-09-18 20:22:01.237] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-18 20:22:01.237] [INFO] 配置系统初始化完成
[2025-09-18 20:22:01.238] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-18 20:22:01.262] [INFO] 开始初始化各个Manager...
[2025-09-18 20:22:01.262] [INFO] 初始化基础Manager...
[2025-09-18 20:22:01.267] [INFO] 开始初始化IOManager...
[2025-09-18 20:22:01.270] [INFO] 模拟初始化雷赛卡，卡号: 0
[2025-09-18 20:22:01.379] [INFO] IO监控任务已启动
[2025-09-18 20:22:01.379] [INFO] IOManager初始化完成
[2025-09-18 20:22:01.380] [INFO] IO监控循环开始
[2025-09-18 20:22:01.383] [INFO] 开始初始化MotorManager...
[2025-09-18 20:22:01.385] [INFO] 模拟初始化运动控制卡
[2025-09-18 20:22:01.597] [INFO] 加载了8个电机的默认配置
[2025-09-18 20:22:01.598] [INFO] 电机监控任务已启动
[2025-09-18 20:22:01.599] [INFO] MotorManager初始化完成
[2025-09-18 20:22:01.599] [INFO] 初始化通信Manager...
[2025-09-18 20:22:01.600] [INFO] 电机监控循环开始
[2025-09-18 20:22:01.602] [INFO] 开始初始化ScannerManager...
[2025-09-18 20:22:01.604] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-18 20:22:01.633] [INFO] 串口初始化完成
[2025-09-18 20:22:01.635] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-18 20:22:01.643] [INFO] 扫描枪连接成功: COM1
[2025-09-18 20:22:01.644] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-18 20:22:01.644] [INFO] ScannerManager初始化完成
[2025-09-18 20:22:01.646] [INFO] 开始初始化ModbusTcpManager...
[2025-09-18 20:22:01.648] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-18 20:22:01.652] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-18 20:22:06.711] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-18 20:22:06.725] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-18 20:22:06.726] [INFO] ModbusTcpManager初始化完成
[2025-09-18 20:22:06.728] [INFO] 开始初始化RobotTcpManager...
[2025-09-18 20:22:06.729] [INFO] 机器人TCP配置: 192.168.1.100:8080
[2025-09-18 20:22:06.731] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-18 20:22:11.801] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: 192.168.1.100:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-18 20:22:11.804] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-18 20:22:11.804] [INFO] RobotTcpManager初始化完成
[2025-09-18 20:22:11.805] [INFO] 初始化视觉Manager...
[2025-09-18 20:22:11.812] [INFO] 开始初始化VisionManager...
[2025-09-18 20:22:11.813] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-18 20:22:11.816] [INFO] 模拟初始化相机，索引: 0
[2025-09-18 20:22:12.346] [INFO] 相机初始化成功
[2025-09-18 20:22:12.348] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-18 20:22:12.349] [INFO] 视觉配置加载完成
[2025-09-18 20:22:12.349] [INFO] VisionManager初始化完成
[2025-09-18 20:22:12.349] [INFO] 初始化数据Manager...
[2025-09-18 20:22:12.352] [INFO] 开始初始化StatisticsManager...
[2025-09-18 20:22:12.352] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-18 20:22:12.356] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-18 20:22:12.356] [INFO] 历史数据加载完成
[2025-09-18 20:22:12.357] [INFO] 自动保存任务已启动
[2025-09-18 20:22:12.357] [INFO] StatisticsManager初始化完成
[2025-09-18 20:22:12.357] [INFO] Manager初始化完成，成功率: 100%
[2025-09-18 20:22:12.357] [INFO] 所有Manager初始化完成
[2025-09-18 20:22:12.359] [INFO] 自动保存循环开始
[2025-09-18 20:22:12.462] [INFO] 定位相机控制面板初始化完成
[2025-09-18 20:22:12.498] [INFO] 开始初始化WorkflowManager...
[2025-09-18 20:22:12.501] [INFO] 工作流事件订阅完成
[2025-09-18 20:22:12.539] [INFO] WorkflowManager初始化完成
[2025-09-18 20:22:12.540] [INFO] 主窗体加载完成
[2025-09-18 20:22:22.284] [INFO] 翻转电机控制面板初始化完成
[2025-09-18 20:22:23.468] [INFO] 6轴机器人控制面板初始化完成
[2025-09-18 20:22:28.990] [INFO] 定位相机控制面板初始化完成
[2025-09-18 20:22:33.005] [INFO] 对位相机控制面板初始化完成
[2025-09-18 20:22:33.840] [INFO] 定位相机控制面板初始化完成
[2025-09-18 20:22:35.457] [INFO] 对位相机控制面板初始化完成
[2025-09-18 20:22:37.203] [INFO] 定位相机控制面板初始化完成
[2025-09-18 20:22:38.120] [INFO] 翻转电机控制面板初始化完成
[2025-09-18 20:22:39.452] [INFO] 6轴机器人控制面板初始化完成
[2025-09-18 20:22:46.490] [INFO] Scara通信管理面板初始化完成
[2025-09-18 20:22:47.175] [INFO] IO读取状态面板初始化完成
[2025-09-18 20:22:48.749] [INFO] 生产日志管理面板初始化完成
[2025-09-18 20:22:49.254] [INFO] IO读取状态面板初始化完成
[2025-09-18 20:22:51.420] [INFO] Scara通信管理面板初始化完成
[2025-09-18 20:22:52.709] [INFO] IO读取状态面板初始化完成
[2025-09-18 20:22:53.597] [INFO] IO输出控制面板初始化完成
[2025-09-18 20:22:54.997] [INFO] IO输出控制 - O0001: ON
[2025-09-18 20:22:55.612] [INFO] IO输出控制 - O0001: OFF
[2025-09-18 20:22:56.606] [INFO] IO读取状态面板初始化完成
[2025-09-18 20:22:56.647] [INFO] Scara通信管理面板初始化完成
[2025-09-18 20:23:20.726] [INFO] 翻转电机控制面板初始化完成
[2025-09-18 20:23:22.434] [INFO] 定位相机控制面板初始化完成
[2025-09-18 20:23:25.282] [INFO] 开始释放WorkflowManager资源...
[2025-09-18 20:23:25.284] [INFO] 工作流事件取消订阅完成
[2025-09-18 20:23:25.310] [INFO] WorkflowManager资源释放完成
[2025-09-18 20:23:25.310] [INFO] 主窗体正在关闭
[2025-09-18 21:15:18.744] [INFO] 程序启动开始
[2025-09-18 21:15:18.746] [INFO] 配置系统初始化成功
[2025-09-18 21:15:18.974] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-18 21:15:18.974] [INFO] 配置系统初始化完成
[2025-09-18 21:15:18.974] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-18 21:15:19.000] [INFO] 开始初始化各个Manager...
[2025-09-18 21:15:19.000] [INFO] 初始化基础Manager...
[2025-09-18 21:15:19.007] [INFO] 开始初始化IOManager...
[2025-09-18 21:15:19.033] [INFO] 模拟初始化雷赛卡，卡号: 0
[2025-09-18 21:15:19.145] [INFO] IO监控任务已启动
[2025-09-18 21:15:19.145] [INFO] IOManager初始化完成
[2025-09-18 21:15:19.146] [INFO] IO监控循环开始
[2025-09-18 21:15:19.148] [INFO] 开始初始化MotorManager...
[2025-09-18 21:15:19.150] [INFO] 模拟初始化运动控制卡
[2025-09-18 21:15:19.365] [INFO] 加载了8个电机的默认配置
[2025-09-18 21:15:19.365] [INFO] 电机监控任务已启动
[2025-09-18 21:15:19.366] [INFO] MotorManager初始化完成
[2025-09-18 21:15:19.366] [INFO] 初始化通信Manager...
[2025-09-18 21:15:19.367] [INFO] 电机监控循环开始
[2025-09-18 21:15:19.368] [INFO] 开始初始化ScannerManager...
[2025-09-18 21:15:19.371] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-18 21:15:19.372] [INFO] 串口初始化完成
[2025-09-18 21:15:19.374] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-18 21:15:19.377] [INFO] 扫描枪连接成功: COM1
[2025-09-18 21:15:19.377] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-18 21:15:19.378] [INFO] ScannerManager初始化完成
[2025-09-18 21:15:19.380] [INFO] 开始初始化ModbusTcpManager...
[2025-09-18 21:15:19.382] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-18 21:15:19.384] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-18 21:15:24.494] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-18 21:15:24.521] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-18 21:15:24.521] [INFO] ModbusTcpManager初始化完成
[2025-09-18 21:15:24.523] [INFO] 开始初始化RobotTcpManager...
[2025-09-18 21:15:24.524] [INFO] 机器人TCP配置: 192.168.1.100:8080
[2025-09-18 21:15:24.527] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-18 21:15:29.581] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: 192.168.1.100:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-18 21:15:29.583] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-18 21:15:29.583] [INFO] RobotTcpManager初始化完成
[2025-09-18 21:15:29.583] [INFO] 初始化视觉Manager...
[2025-09-18 21:15:29.587] [INFO] 开始初始化VisionManager...
[2025-09-18 21:15:29.587] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-18 21:15:29.588] [INFO] 模拟初始化相机，索引: 0
[2025-09-18 21:15:30.133] [INFO] 相机初始化成功
[2025-09-18 21:15:30.141] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-18 21:15:30.142] [INFO] 视觉配置加载完成
[2025-09-18 21:15:30.143] [INFO] VisionManager初始化完成
[2025-09-18 21:15:30.144] [INFO] 初始化数据Manager...
[2025-09-18 21:15:30.159] [INFO] 开始初始化StatisticsManager...
[2025-09-18 21:15:30.161] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-18 21:15:30.178] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-18 21:15:30.179] [INFO] 历史数据加载完成
[2025-09-18 21:15:30.179] [INFO] 自动保存任务已启动
[2025-09-18 21:15:30.180] [INFO] StatisticsManager初始化完成
[2025-09-18 21:15:30.180] [INFO] Manager初始化完成，成功率: 100%
[2025-09-18 21:15:30.181] [INFO] 所有Manager初始化完成
[2025-09-18 21:15:30.183] [INFO] 自动保存循环开始
[2025-09-18 21:15:30.314] [INFO] 定位相机控制面板初始化完成
[2025-09-18 21:15:30.342] [INFO] 开始初始化WorkflowManager...
[2025-09-18 21:15:30.344] [INFO] 工作流事件订阅完成
[2025-09-18 21:15:30.373] [INFO] WorkflowManager初始化完成
[2025-09-18 21:15:30.376] [INFO] 主窗体加载完成
[2025-09-18 21:45:32.471] [INFO] 程序启动开始
[2025-09-18 21:45:32.472] [INFO] 配置系统初始化成功
[2025-09-18 21:45:32.536] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-18 21:45:32.536] [INFO] 配置系统初始化完成
[2025-09-18 21:45:32.536] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-18 21:45:32.569] [INFO] 开始初始化各个Manager...
[2025-09-18 21:45:32.570] [INFO] 初始化基础Manager...
[2025-09-18 21:45:32.577] [INFO] 开始初始化IOManager...
[2025-09-18 21:45:32.579] [INFO] 模拟初始化雷赛卡，卡号: 0
[2025-09-18 21:45:32.686] [INFO] IO监控任务已启动
[2025-09-18 21:45:32.687] [INFO] IOManager初始化完成
[2025-09-18 21:45:32.688] [INFO] IO监控循环开始
[2025-09-18 21:45:32.690] [INFO] 开始初始化MotorManager...
[2025-09-18 21:45:32.694] [INFO] 模拟初始化运动控制卡
[2025-09-18 21:45:32.905] [INFO] 加载了8个电机的默认配置
[2025-09-18 21:45:32.906] [INFO] 电机监控任务已启动
[2025-09-18 21:45:32.906] [INFO] MotorManager初始化完成
[2025-09-18 21:45:32.906] [INFO] 初始化通信Manager...
[2025-09-18 21:45:32.907] [INFO] 电机监控循环开始
[2025-09-18 21:45:32.909] [INFO] 开始初始化ScannerManager...
[2025-09-18 21:45:32.911] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-18 21:45:32.913] [INFO] 串口初始化完成
[2025-09-18 21:45:32.915] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-18 21:45:32.919] [INFO] 扫描枪连接成功: COM1
[2025-09-18 21:45:32.919] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-18 21:45:32.919] [INFO] ScannerManager初始化完成
[2025-09-18 21:45:32.921] [INFO] 开始初始化ModbusTcpManager...
[2025-09-18 21:45:32.923] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-18 21:45:32.925] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-18 21:45:38.031] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-18 21:45:38.044] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-18 21:45:38.044] [INFO] ModbusTcpManager初始化完成
[2025-09-18 21:45:38.047] [INFO] 开始初始化RobotTcpManager...
[2025-09-18 21:45:38.048] [INFO] 机器人TCP配置: 192.168.1.100:8080
[2025-09-18 21:45:38.051] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-18 21:45:43.101] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: 192.168.1.100:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-18 21:45:43.102] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-18 21:45:43.103] [INFO] RobotTcpManager初始化完成
[2025-09-18 21:45:43.103] [INFO] 初始化视觉Manager...
[2025-09-18 21:45:43.106] [INFO] 开始初始化VisionManager...
[2025-09-18 21:45:43.107] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-18 21:45:43.108] [INFO] 模拟初始化相机，索引: 0
[2025-09-18 21:45:43.661] [INFO] 相机初始化成功
[2025-09-18 21:45:43.666] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-18 21:45:43.667] [INFO] 视觉配置加载完成
[2025-09-18 21:45:43.667] [INFO] VisionManager初始化完成
[2025-09-18 21:45:43.668] [INFO] 初始化数据Manager...
[2025-09-18 21:45:43.673] [INFO] 开始初始化StatisticsManager...
[2025-09-18 21:45:43.674] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-18 21:45:43.680] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-18 21:45:43.680] [INFO] 历史数据加载完成
[2025-09-18 21:45:43.681] [INFO] 自动保存任务已启动
[2025-09-18 21:45:43.681] [INFO] StatisticsManager初始化完成
[2025-09-18 21:45:43.682] [INFO] Manager初始化完成，成功率: 100%
[2025-09-18 21:45:43.682] [INFO] 所有Manager初始化完成
[2025-09-18 21:45:43.684] [INFO] 自动保存循环开始
[2025-09-18 21:45:43.754] [INFO] 定位相机控制面板初始化完成
[2025-09-18 21:45:43.774] [INFO] 开始初始化WorkflowManager...
[2025-09-18 21:45:43.777] [INFO] 工作流事件订阅完成
[2025-09-18 21:45:43.808] [INFO] WorkflowManager初始化完成
[2025-09-18 21:45:43.809] [INFO] 主窗体加载完成
[2025-09-18 21:45:46.908] [INFO] 翻转电机控制面板初始化完成
[2025-09-18 21:45:52.004] [INFO] 6轴机器人控制面板初始化完成
[2025-09-18 21:45:54.869] [INFO] 翻转电机控制面板初始化完成
[2025-09-18 21:45:56.344] [INFO] 皮带电机控制面板初始化完成
[2025-09-18 21:50:51.700] [INFO] 翻转电机控制面板初始化完成
[2025-09-18 21:50:52.057] [INFO] IO读取状态面板初始化完成
[2025-09-18 21:50:56.472] [INFO] 生产日志管理面板初始化完成
[2025-09-18 21:50:59.550] [INFO] 6轴机器人控制面板初始化完成
[2025-09-18 21:51:00.828] [INFO] 生产日志管理面板初始化完成
[2025-09-18 21:51:01.897] [INFO] Scara通信管理面板初始化完成
[2025-09-18 21:51:02.757] [INFO] 6轴机器人控制面板初始化完成
[2025-09-18 21:51:03.439] [INFO] 翻转电机控制面板初始化完成
[2025-09-18 21:51:04.111] [INFO] 定位相机控制面板初始化完成
[2025-09-18 21:51:05.133] [INFO] 翻转电机控制面板初始化完成
[2025-09-18 21:52:32.251] [INFO] 开始释放WorkflowManager资源...
[2025-09-18 21:52:32.253] [INFO] 工作流事件取消订阅完成
[2025-09-18 21:52:32.285] [INFO] WorkflowManager资源释放完成
[2025-09-18 21:52:32.285] [INFO] 主窗体正在关闭
[2025-09-18 21:52:43.151] [INFO] 程序启动开始
[2025-09-18 21:52:43.153] [INFO] 配置系统初始化成功
[2025-09-18 21:52:43.210] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-18 21:52:43.210] [INFO] 配置系统初始化完成
[2025-09-18 21:52:43.211] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-18 21:52:43.245] [INFO] 开始初始化各个Manager...
[2025-09-18 21:52:43.245] [INFO] 初始化基础Manager...
[2025-09-18 21:52:43.250] [INFO] 开始初始化IOManager...
[2025-09-18 21:52:43.253] [INFO] 模拟初始化雷赛卡，卡号: 0
[2025-09-18 21:52:43.363] [INFO] IO监控任务已启动
[2025-09-18 21:52:43.363] [INFO] IOManager初始化完成
[2025-09-18 21:52:43.366] [INFO] IO监控循环开始
[2025-09-18 21:52:43.367] [INFO] 开始初始化MotorManager...
[2025-09-18 21:52:43.369] [INFO] 模拟初始化运动控制卡
[2025-09-18 21:52:43.591] [INFO] 加载了8个电机的默认配置
[2025-09-18 21:52:43.593] [INFO] 电机监控任务已启动
[2025-09-18 21:52:43.593] [INFO] MotorManager初始化完成
[2025-09-18 21:52:43.594] [INFO] 初始化通信Manager...
[2025-09-18 21:52:43.600] [INFO] 开始初始化ScannerManager...
[2025-09-18 21:52:43.601] [INFO] 电机监控循环开始
[2025-09-18 21:52:43.602] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-18 21:52:43.605] [INFO] 串口初始化完成
[2025-09-18 21:52:43.611] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-18 21:52:43.615] [INFO] 扫描枪连接成功: COM1
[2025-09-18 21:52:43.616] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-18 21:52:43.616] [INFO] ScannerManager初始化完成
[2025-09-18 21:52:43.618] [INFO] 开始初始化ModbusTcpManager...
[2025-09-18 21:52:43.620] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-18 21:52:43.622] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-18 21:52:48.687] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-18 21:52:48.699] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-18 21:52:48.699] [INFO] ModbusTcpManager初始化完成
[2025-09-18 21:52:48.705] [INFO] 开始初始化RobotTcpManager...
[2025-09-18 21:52:48.706] [INFO] 机器人TCP配置: 192.168.1.100:8080
[2025-09-18 21:52:48.708] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-18 21:52:53.771] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: 192.168.1.100:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-18 21:52:53.773] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-18 21:52:53.773] [INFO] RobotTcpManager初始化完成
[2025-09-18 21:52:53.773] [INFO] 初始化视觉Manager...
[2025-09-18 21:52:53.778] [INFO] 开始初始化VisionManager...
[2025-09-18 21:52:53.778] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-18 21:52:53.779] [INFO] 模拟初始化相机，索引: 0
[2025-09-18 21:52:54.320] [INFO] 相机初始化成功
[2025-09-18 21:52:54.323] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-18 21:52:54.323] [INFO] 视觉配置加载完成
[2025-09-18 21:52:54.324] [INFO] VisionManager初始化完成
[2025-09-18 21:52:54.324] [INFO] 初始化数据Manager...
[2025-09-18 21:52:54.326] [INFO] 开始初始化StatisticsManager...
[2025-09-18 21:52:54.327] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-18 21:52:54.330] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-18 21:52:54.330] [INFO] 历史数据加载完成
[2025-09-18 21:52:54.330] [INFO] 自动保存任务已启动
[2025-09-18 21:52:54.330] [INFO] StatisticsManager初始化完成
[2025-09-18 21:52:54.331] [INFO] Manager初始化完成，成功率: 100%
[2025-09-18 21:52:54.331] [INFO] 所有Manager初始化完成
[2025-09-18 21:52:54.332] [INFO] 自动保存循环开始
[2025-09-18 21:52:54.380] [INFO] 定位相机控制面板初始化完成
[2025-09-18 21:52:54.403] [INFO] 开始初始化WorkflowManager...
[2025-09-18 21:52:54.406] [INFO] 工作流事件订阅完成
[2025-09-18 21:52:54.430] [INFO] WorkflowManager初始化完成
[2025-09-18 21:52:54.431] [INFO] 主窗体加载完成
[2025-09-18 21:52:58.350] [INFO] 翻转电机控制面板初始化完成
[2025-09-18 21:53:00.361] [INFO] Scara通信管理面板初始化完成
[2025-09-18 21:53:01.035] [INFO] 6轴机器人控制面板初始化完成
[2025-09-18 21:53:02.022] [INFO] Scara通信管理面板初始化完成
[2025-09-18 21:53:02.638] [INFO] 6轴机器人控制面板初始化完成
[2025-09-18 21:53:03.152] [INFO] 翻转电机控制面板初始化完成
[2025-09-18 21:53:07.079] [INFO] 皮带电机控制面板初始化完成
[2025-09-18 21:53:08.157] [INFO] 翻转电机控制面板初始化完成
[2025-09-18 21:53:27.070] [INFO] 定位相机控制面板初始化完成
[2025-09-18 21:53:50.309] [INFO] 开始释放WorkflowManager资源...
[2025-09-18 21:53:50.311] [INFO] 工作流事件取消订阅完成
[2025-09-18 21:53:50.339] [INFO] WorkflowManager资源释放完成
[2025-09-18 21:53:50.339] [INFO] 主窗体正在关闭
[2025-09-18 22:48:59.589] [INFO] 程序启动开始
[2025-09-18 22:48:59.590] [INFO] 配置系统初始化成功
[2025-09-18 22:48:59.754] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-18 22:48:59.755] [INFO] 配置系统初始化完成
[2025-09-18 22:48:59.755] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-18 22:48:59.777] [INFO] 开始初始化各个Manager...
[2025-09-18 22:48:59.778] [INFO] 初始化基础Manager...
[2025-09-18 22:48:59.782] [INFO] 开始初始化IOManager...
[2025-09-18 22:48:59.784] [INFO] 模拟初始化雷赛卡，卡号: 0
[2025-09-18 22:48:59.903] [INFO] IO监控任务已启动
[2025-09-18 22:48:59.903] [INFO] IOManager初始化完成
[2025-09-18 22:48:59.904] [INFO] IO监控循环开始
[2025-09-18 22:48:59.906] [INFO] 开始初始化MotorManager...
[2025-09-18 22:48:59.908] [INFO] 模拟初始化运动控制卡
[2025-09-18 22:49:00.122] [INFO] 加载了8个电机的默认配置
[2025-09-18 22:49:00.123] [INFO] 电机监控任务已启动
[2025-09-18 22:49:00.123] [INFO] MotorManager初始化完成
[2025-09-18 22:49:00.124] [INFO] 初始化通信Manager...
[2025-09-18 22:49:00.125] [INFO] 电机监控循环开始
[2025-09-18 22:49:00.127] [INFO] 开始初始化ScannerManager...
[2025-09-18 22:49:00.129] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-18 22:49:00.132] [INFO] 串口初始化完成
[2025-09-18 22:49:00.134] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-18 22:49:00.137] [INFO] 扫描枪连接成功: COM1
[2025-09-18 22:49:00.138] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-18 22:49:00.138] [INFO] ScannerManager初始化完成
[2025-09-18 22:49:00.141] [INFO] 开始初始化ModbusTcpManager...
[2025-09-18 22:49:00.143] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-18 22:49:00.147] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-18 22:49:05.231] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-18 22:49:05.246] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-18 22:49:05.247] [INFO] ModbusTcpManager初始化完成
[2025-09-18 22:49:05.249] [INFO] 开始初始化RobotTcpManager...
[2025-09-18 22:49:05.249] [INFO] 机器人TCP配置: 192.168.1.100:8080
[2025-09-18 22:49:05.252] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-18 22:49:10.321] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: 192.168.1.100:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-18 22:49:10.323] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-18 22:49:10.323] [INFO] RobotTcpManager初始化完成
[2025-09-18 22:49:10.324] [INFO] 初始化视觉Manager...
[2025-09-18 22:49:10.328] [INFO] 开始初始化VisionManager...
[2025-09-18 22:49:10.328] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-18 22:49:10.329] [INFO] 模拟初始化相机，索引: 0
[2025-09-18 22:49:10.856] [INFO] 相机初始化成功
[2025-09-18 22:49:10.859] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-18 22:49:10.859] [INFO] 视觉配置加载完成
[2025-09-18 22:49:10.860] [INFO] VisionManager初始化完成
[2025-09-18 22:49:10.860] [INFO] 初始化数据Manager...
[2025-09-18 22:49:10.865] [INFO] 开始初始化StatisticsManager...
[2025-09-18 22:49:10.865] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-18 22:49:10.870] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-18 22:49:10.871] [INFO] 历史数据加载完成
[2025-09-18 22:49:10.872] [INFO] 自动保存任务已启动
[2025-09-18 22:49:10.872] [INFO] StatisticsManager初始化完成
[2025-09-18 22:49:10.873] [INFO] Manager初始化完成，成功率: 100%
[2025-09-18 22:49:10.873] [INFO] 所有Manager初始化完成
[2025-09-18 22:49:10.877] [INFO] 自动保存循环开始
[2025-09-18 22:49:10.930] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-18 22:49:10.930] [INFO] 主界面布局创建完成
[2025-09-18 22:49:10.930] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-18 22:49:21.745] [INFO] 翻转电机控制面板初始化完成
[2025-09-18 22:49:23.732] [INFO] 6轴机器人控制面板初始化完成
[2025-09-18 22:49:27.842] [INFO] Scara通信管理面板初始化完成
[2025-09-18 22:49:29.458] [INFO] IO读取状态面板初始化完成
[2025-09-18 22:49:30.635] [INFO] 生产日志管理面板初始化完成
[2025-09-18 22:49:32.495] [INFO] Scara通信管理面板初始化完成
[2025-09-18 22:49:34.270] [INFO] 6轴机器人控制面板初始化完成
[2025-09-18 22:49:43.093] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-18 22:49:45.558] [INFO] 翻转电机控制面板初始化完成
[2025-09-18 22:55:15.155] [INFO] 程序启动开始
[2025-09-18 22:55:15.157] [INFO] 配置系统初始化成功
[2025-09-18 22:55:15.205] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-18 22:55:15.205] [INFO] 配置系统初始化完成
[2025-09-18 22:55:15.206] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-18 22:55:15.241] [INFO] 开始初始化各个Manager...
[2025-09-18 22:55:15.241] [INFO] 初始化基础Manager...
[2025-09-18 22:55:15.245] [INFO] 开始初始化IOManager...
[2025-09-18 22:55:15.248] [INFO] 模拟初始化雷赛卡，卡号: 0
[2025-09-18 22:55:15.362] [INFO] IO监控任务已启动
[2025-09-18 22:55:15.362] [INFO] IOManager初始化完成
[2025-09-18 22:55:15.364] [INFO] IO监控循环开始
[2025-09-18 22:55:15.366] [INFO] 开始初始化MotorManager...
[2025-09-18 22:55:15.369] [INFO] 模拟初始化运动控制卡
[2025-09-18 22:55:15.600] [INFO] 加载了8个电机的默认配置
[2025-09-18 22:55:15.601] [INFO] 电机监控任务已启动
[2025-09-18 22:55:15.601] [INFO] MotorManager初始化完成
[2025-09-18 22:55:15.602] [INFO] 初始化通信Manager...
[2025-09-18 22:55:15.603] [INFO] 电机监控循环开始
[2025-09-18 22:55:15.606] [INFO] 开始初始化ScannerManager...
[2025-09-18 22:55:15.608] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-18 22:55:15.610] [INFO] 串口初始化完成
[2025-09-18 22:55:15.612] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-18 22:55:15.615] [INFO] 扫描枪连接成功: COM1
[2025-09-18 22:55:15.616] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-18 22:55:15.618] [INFO] ScannerManager初始化完成
[2025-09-18 22:55:15.623] [INFO] 开始初始化ModbusTcpManager...
[2025-09-18 22:55:15.626] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-18 22:55:15.629] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-18 22:55:20.709] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-18 22:55:20.722] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-18 22:55:20.723] [INFO] ModbusTcpManager初始化完成
[2025-09-18 22:55:20.724] [INFO] 开始初始化RobotTcpManager...
[2025-09-18 22:55:20.725] [INFO] 机器人TCP配置: 192.168.1.100:8080
[2025-09-18 22:55:20.727] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-18 22:55:25.792] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: 192.168.1.100:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-18 22:55:25.793] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-18 22:55:25.793] [INFO] RobotTcpManager初始化完成
[2025-09-18 22:55:25.793] [INFO] 初始化视觉Manager...
[2025-09-18 22:55:25.796] [INFO] 开始初始化VisionManager...
[2025-09-18 22:55:25.796] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-18 22:55:25.798] [INFO] 模拟初始化相机，索引: 0
[2025-09-18 22:55:26.343] [INFO] 相机初始化成功
[2025-09-18 22:55:26.344] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-18 22:55:26.344] [INFO] 视觉配置加载完成
[2025-09-18 22:55:26.345] [INFO] VisionManager初始化完成
[2025-09-18 22:55:26.345] [INFO] 初始化数据Manager...
[2025-09-18 22:55:26.348] [INFO] 开始初始化StatisticsManager...
[2025-09-18 22:55:26.348] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-18 22:55:26.352] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-18 22:55:26.352] [INFO] 历史数据加载完成
[2025-09-18 22:55:26.352] [INFO] 自动保存任务已启动
[2025-09-18 22:55:26.352] [INFO] StatisticsManager初始化完成
[2025-09-18 22:55:26.353] [INFO] Manager初始化完成，成功率: 100%
[2025-09-18 22:55:26.353] [INFO] 所有Manager初始化完成
[2025-09-18 22:55:26.354] [INFO] 自动保存循环开始
[2025-09-18 22:55:26.391] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-18 22:55:26.391] [INFO] 主界面布局创建完成
[2025-09-18 22:55:26.391] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-18 22:55:28.673] [INFO] 翻转电机控制面板初始化完成
[2025-09-18 22:55:30.667] [INFO] 6轴机器人控制面板初始化完成
[2025-09-18 22:55:32.709] [INFO] Scara通信管理面板初始化完成
[2025-09-18 22:55:34.394] [INFO] IO读取状态面板初始化完成
[2025-09-18 22:55:36.023] [INFO] IO读取状态面板初始化完成
[2025-09-18 22:55:37.238] [INFO] Scara通信管理面板初始化完成
[2025-09-18 22:55:38.349] [INFO] 6轴机器人控制面板初始化完成
[2025-09-18 22:55:39.446] [INFO] 翻转电机控制面板初始化完成
[2025-09-18 22:55:40.429] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-18 22:55:41.205] [INFO] 翻转电机控制面板初始化完成
[2025-09-18 22:55:42.161] [INFO] 6轴机器人控制面板初始化完成
[2025-09-18 22:55:43.477] [INFO] Scara通信管理面板初始化完成
[2025-09-18 22:55:44.960] [INFO] IO读取状态面板初始化完成
[2025-09-18 22:55:46.363] [INFO] 生产日志管理面板初始化完成
[2025-09-18 22:55:46.936] [INFO] IO读取状态面板初始化完成
[2025-09-18 22:55:47.470] [INFO] Scara通信管理面板初始化完成
[2025-09-18 22:55:49.406] [INFO] 6轴机器人控制面板初始化完成
[2025-09-18 22:55:52.330] [INFO] 翻转电机控制面板初始化完成
[2025-09-18 22:56:03.894] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-18 22:56:05.169] [INFO] 翻转电机控制面板初始化完成
[2025-09-18 22:56:15.909] [INFO] 6轴机器人控制面板初始化完成
[2025-09-18 22:56:16.750] [INFO] 翻转电机控制面板初始化完成
[2025-09-18 23:15:41.437] [INFO] 程序启动开始
[2025-09-18 23:15:41.439] [INFO] 配置系统初始化成功
[2025-09-18 23:15:41.623] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-18 23:15:41.623] [INFO] 配置系统初始化完成
[2025-09-18 23:15:41.623] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-18 23:15:41.648] [INFO] 开始初始化各个Manager...
[2025-09-18 23:15:41.648] [INFO] 初始化基础Manager...
[2025-09-18 23:15:41.653] [INFO] 开始初始化IOManager...
[2025-09-18 23:15:41.655] [INFO] 模拟初始化雷赛卡，卡号: 0
[2025-09-18 23:15:41.770] [INFO] IO监控任务已启动
[2025-09-18 23:15:41.770] [INFO] IOManager初始化完成
[2025-09-18 23:15:41.772] [INFO] IO监控循环开始
[2025-09-18 23:15:41.774] [INFO] 开始初始化MotorManager...
[2025-09-18 23:15:41.776] [INFO] 模拟初始化运动控制卡
[2025-09-18 23:15:41.992] [INFO] 加载了8个电机的默认配置
[2025-09-18 23:15:41.993] [INFO] 电机监控任务已启动
[2025-09-18 23:15:41.993] [INFO] MotorManager初始化完成
[2025-09-18 23:15:41.993] [INFO] 初始化通信Manager...
[2025-09-18 23:15:41.995] [INFO] 电机监控循环开始
[2025-09-18 23:15:42.002] [INFO] 开始初始化ScannerManager...
[2025-09-18 23:15:42.004] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-18 23:15:42.006] [INFO] 串口初始化完成
[2025-09-18 23:15:42.008] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-18 23:15:42.124] [INFO] 扫描枪连接成功: COM1
[2025-09-18 23:15:42.126] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-18 23:15:42.126] [INFO] ScannerManager初始化完成
[2025-09-18 23:15:42.129] [INFO] 开始初始化ModbusTcpManager...
[2025-09-18 23:15:42.132] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-18 23:15:42.142] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-18 23:15:47.264] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-18 23:15:47.278] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-18 23:15:47.279] [INFO] ModbusTcpManager初始化完成
[2025-09-18 23:15:47.281] [INFO] 开始初始化RobotTcpManager...
[2025-09-18 23:15:47.281] [INFO] 机器人TCP配置: 192.168.1.100:8080
[2025-09-18 23:15:47.283] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-18 23:15:52.343] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: 192.168.1.100:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-18 23:15:52.345] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-18 23:15:52.346] [INFO] RobotTcpManager初始化完成
[2025-09-18 23:15:52.346] [INFO] 初始化视觉Manager...
[2025-09-18 23:15:52.351] [INFO] 开始初始化VisionManager...
[2025-09-18 23:15:52.351] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-18 23:15:52.353] [INFO] 模拟初始化相机，索引: 0
[2025-09-18 23:15:52.891] [INFO] 相机初始化成功
[2025-09-18 23:15:52.894] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-18 23:15:52.894] [INFO] 视觉配置加载完成
[2025-09-18 23:15:52.894] [INFO] VisionManager初始化完成
[2025-09-18 23:15:52.894] [INFO] 初始化数据Manager...
[2025-09-18 23:15:52.897] [INFO] 开始初始化StatisticsManager...
[2025-09-18 23:15:52.898] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-18 23:15:52.905] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-18 23:15:52.905] [INFO] 历史数据加载完成
[2025-09-18 23:15:52.906] [INFO] 自动保存任务已启动
[2025-09-18 23:15:52.906] [INFO] StatisticsManager初始化完成
[2025-09-18 23:15:52.906] [INFO] Manager初始化完成，成功率: 100%
[2025-09-18 23:15:52.906] [INFO] 所有Manager初始化完成
[2025-09-18 23:15:52.908] [INFO] 自动保存循环开始
[2025-09-18 23:15:52.960] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-18 23:15:52.960] [INFO] 主界面布局创建完成
[2025-09-18 23:15:52.961] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-18 23:15:56.685] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-18 23:16:00.302] [INFO] 6轴机器人控制面板初始化完成
[2025-09-18 23:16:12.972] [INFO] Scara通信管理面板初始化完成
[2025-09-18 23:16:13.743] [INFO] 6轴机器人控制面板初始化完成
[2025-09-18 23:16:16.982] [INFO] Scara通信管理面板初始化完成
[2025-09-18 23:16:20.199] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-18 23:16:21.214] [INFO] 皮带电机控制面板初始化完成 - 按HTML原型设计
[2025-09-18 23:16:23.373] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-18 23:16:24.271] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-18 23:16:25.156] [INFO] 对位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-18 23:16:26.374] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-18 23:16:28.422] [INFO] 6轴机器人控制面板初始化完成
[2025-09-18 23:16:29.261] [INFO] Scara通信管理面板初始化完成
[2025-09-18 23:16:38.742] [INFO] IO读取状态面板初始化完成
[2025-09-18 23:16:42.710] [INFO] IO输出控制面板初始化完成
[2025-09-18 23:16:43.467] [INFO] IO输出控制 - O0001: ON
[2025-09-18 23:16:44.185] [INFO] IO输出控制 - O0001: OFF
[2025-09-18 23:16:46.179] [INFO] 生产日志管理面板初始化完成
[2025-09-18 23:16:51.965] [INFO] IO读取状态面板初始化完成
[2025-09-18 23:16:52.686] [INFO] Scara通信管理面板初始化完成
[2025-09-18 23:16:53.701] [INFO] 6轴机器人控制面板初始化完成
[2025-09-18 23:17:00.574] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-18 23:37:27.592] [INFO] 程序启动开始
[2025-09-18 23:37:27.594] [INFO] 配置系统初始化成功
[2025-09-18 23:37:27.646] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-18 23:37:27.646] [INFO] 配置系统初始化完成
[2025-09-18 23:37:27.647] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-18 23:37:27.667] [INFO] 开始初始化各个Manager...
[2025-09-18 23:37:27.668] [INFO] 初始化基础Manager...
[2025-09-18 23:37:27.673] [INFO] 开始初始化IOManager...
[2025-09-18 23:37:27.675] [INFO] 模拟初始化雷赛卡，卡号: 0
[2025-09-18 23:37:27.786] [INFO] IO监控任务已启动
[2025-09-18 23:37:27.786] [INFO] IOManager初始化完成
[2025-09-18 23:37:27.791] [INFO] IO监控循环开始
[2025-09-18 23:37:27.796] [INFO] 开始初始化MotorManager...
[2025-09-18 23:37:27.801] [INFO] 模拟初始化运动控制卡
[2025-09-18 23:37:28.007] [INFO] 加载了8个电机的默认配置
[2025-09-18 23:37:28.008] [INFO] 电机监控任务已启动
[2025-09-18 23:37:28.008] [INFO] MotorManager初始化完成
[2025-09-18 23:37:28.008] [INFO] 初始化通信Manager...
[2025-09-18 23:37:28.009] [INFO] 电机监控循环开始
[2025-09-18 23:37:28.011] [INFO] 开始初始化ScannerManager...
[2025-09-18 23:37:28.013] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-18 23:37:28.015] [INFO] 串口初始化完成
[2025-09-18 23:37:28.017] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-18 23:37:28.020] [INFO] 扫描枪连接成功: COM1
[2025-09-18 23:37:28.020] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-18 23:37:28.021] [INFO] ScannerManager初始化完成
[2025-09-18 23:37:28.023] [INFO] 开始初始化ModbusTcpManager...
[2025-09-18 23:37:28.025] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-18 23:37:28.027] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-18 23:37:33.126] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-18 23:37:33.155] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-18 23:37:33.156] [INFO] ModbusTcpManager初始化完成
[2025-09-18 23:37:33.158] [INFO] 开始初始化RobotTcpManager...
[2025-09-18 23:37:33.159] [INFO] 机器人TCP配置: 192.168.1.100:8080
[2025-09-18 23:37:33.161] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-18 23:37:38.246] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: 192.168.1.100:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-18 23:37:38.249] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-18 23:37:38.249] [INFO] RobotTcpManager初始化完成
[2025-09-18 23:37:38.249] [INFO] 初始化视觉Manager...
[2025-09-18 23:37:38.254] [INFO] 开始初始化VisionManager...
[2025-09-18 23:37:38.254] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-18 23:37:38.256] [INFO] 模拟初始化相机，索引: 0
[2025-09-18 23:37:38.787] [INFO] 相机初始化成功
[2025-09-18 23:37:38.790] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-18 23:37:38.790] [INFO] 视觉配置加载完成
[2025-09-18 23:37:38.790] [INFO] VisionManager初始化完成
[2025-09-18 23:37:38.791] [INFO] 初始化数据Manager...
[2025-09-18 23:37:38.793] [INFO] 开始初始化StatisticsManager...
[2025-09-18 23:37:38.794] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-18 23:37:38.798] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-18 23:37:38.798] [INFO] 历史数据加载完成
[2025-09-18 23:37:38.798] [INFO] 自动保存任务已启动
[2025-09-18 23:37:38.798] [INFO] StatisticsManager初始化完成
[2025-09-18 23:37:38.798] [INFO] Manager初始化完成，成功率: 100%
[2025-09-18 23:37:38.799] [INFO] 所有Manager初始化完成
[2025-09-18 23:37:38.800] [INFO] 自动保存循环开始
[2025-09-18 23:37:38.847] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-18 23:37:38.847] [INFO] 主界面布局创建完成
[2025-09-18 23:37:38.847] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-18 23:37:47.761] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-18 23:38:05.387] [INFO] Scara通信管理面板初始化完成
[2025-09-18 23:38:06.078] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-18 23:38:07.050] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-18 23:38:07.763] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-18 23:38:08.365] [INFO] Scara通信管理面板初始化完成
[2025-09-18 23:38:10.254] [INFO] IO读取状态面板初始化完成
[2025-09-18 23:38:11.337] [INFO] 生产日志管理面板初始化完成
[2025-09-18 23:38:12.159] [INFO] IO读取状态面板初始化完成
[2025-09-18 23:38:14.357] [INFO] 生产日志管理面板初始化完成
[2025-09-18 23:38:15.795] [INFO] IO读取状态面板初始化完成
[2025-09-18 23:38:16.702] [INFO] IO输出控制面板初始化完成
[2025-09-18 23:38:18.100] [INFO] IO读取状态面板初始化完成
[2025-09-18 23:38:20.046] [INFO] Scara通信管理面板初始化完成
[2025-09-18 23:38:22.054] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
