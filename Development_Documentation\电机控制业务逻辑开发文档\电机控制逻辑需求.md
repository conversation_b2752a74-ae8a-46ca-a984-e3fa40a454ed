# 电机控制逻辑需求文档

基于DMC1000B运动控制卡，为左右翻转电机和两个皮带电机开发完整的业务逻辑控制功能，包括参数设置、运动控制、状态监控和UI集成。涉及4个步进电机轴：轴0(左翻转电机)、轴1(右翻转电机)、轴2(输出皮带电机)、轴3(输入皮带电机)。

## 1. 核心功能需求

### 1.1 DMC1000B运动控制卡集成
- 封装DMC1000B运动控制卡的底层API函数
- 实现控制卡的初始化、配置和资源释放
- 提供统一的电机控制接口，支持4轴步进电机控制

### 1.2 翻转电机控制（轴0和轴1）
- **角度控制模式**：支持角度单位的参数设置和运动控制
- **参数配置**：脉冲当量(pulse/°)、最大加速度(°/s²)、运行速度(°/s)
- **位置控制**：当前角度显示、目标角度设置、绝对位置运动
- **基本控制**：正转、反转、回原点操作
- **示教功能**：
  - 回原点操作：电机能够自动回到原点位置（0°位置）
  - 点动示教：通过点动控制分别移动到3个不同的目标位置
  - 位置保存：通过控件保存当前位置相对于原点的角度值（位置1、位置2、位置3）
  - 位置移动：通过控件快速移动到已保存的相对位置
  - 参数一致性：示教移动时使用与运行参数一致的速度和加速度设置
  - 相对位置计算：所有保存的位置都是相对于原点的角度值，移动时自动计算到目标相对位置

### 1.3 皮带电机控制（轴2和轴3）
- **线性控制模式**：支持毫米单位的参数设置和运动控制
- **参数配置**：脉冲当量(pulse/mm)、最大加速度(mm/s²)、运行速度(mm/s)
- **点动控制**：正转点动、反转点动，可设置点动距离
- **连续运转**：连续正转、连续反转、停止控制
- **双电机独立控制**：输入皮带电机和输出皮带电机独立参数和控制

### 1.4 状态监控和反馈
- **实时状态监控**：电机位置、速度、运行状态的实时更新
- **原点检测**：翻转电机的原点开关状态监控
- **运动完成检测**：运动指令执行完成的状态反馈
- **异常处理**：电机报警、限位触发等异常状态处理

### 1.5 UI界面集成
- **现有控件增强**：为MotorFlipPanel和MotorBeltPanel添加业务逻辑
- **参数设置界面**：用户友好的参数配置界面
- **实时状态显示**：电机状态的可视化显示
- **操作按钮响应**：所有控制按钮的业务逻辑实现

## 2. 用户需求分析

### 2.1 操作员需求
- 设置翻转电机的角度参数，精确控制产品翻转角度
- 通过示教功能保存常用的翻转位置，快速切换到预设位置
- 通过点动控制精确调整翻转电机到目标位置，然后保存该位置
- 保存的位置是相对于原点的角度值，移动时能够准确到达相对位置
- 控制皮带电机的点动和连续运行，调试传送带位置
- 实时查看电机的当前位置和状态，监控设备运行情况
- 电机自动回到原点位置，重新校准设备

### 2.2 维护工程师需求
- 调整电机的速度和加速度参数，优化设备性能
- 示教移动时使用与运行参数一致的速度设置，确保运动一致性
- 检测和报告电机异常状态，及时处理故障
- 手动控制每个电机轴，进行设备调试和维护

### 2.3 系统集成需求
- 电机控制模块与现有UI界面无缝集成，保持界面一致性
- 电机控制与其他系统模块协调工作，实现完整的生产流程

## 3. 验收标准

### 3.1 硬件集成验收标准
- [ ] DMC1000B控制卡能够成功初始化和关闭
- [ ] 4个电机轴（0-3轴）能够正确识别和控制
- [ ] 翻转电机的原点开关能够正确检测
- [ ] 所有电机的脉冲输出和方向控制正常工作

### 3.2 翻转电机控制验收标准
- [ ] 能够设置和读取角度单位的运动参数
- [ ] 正转、反转、回原点功能正常工作
- [ ] 能够通过点动控制精确移动到目标位置
- [ ] 能够保存和调用3个预设位置（相对于原点的角度值）
- [ ] 移动到保存位置时能够准确到达相对位置
- [ ] 示教移动时使用与运行参数一致的速度和加速度
- [ ] 绝对角度定位精度满足±0.1°要求
- [ ] 当前角度和目标角度能够实时显示和更新

### 3.3 皮带电机控制验收标准
- [ ] 能够设置和读取线性单位的运动参数
- [ ] 点动控制功能正常，点动距离可调
- [ ] 连续运转和停止功能正常工作
- [ ] 输入和输出皮带电机能够独立控制
- [ ] 运动速度和加速度参数能够实时生效

### 3.4 状态监控验收标准
- [ ] 电机位置能够实时更新，更新频率不低于10Hz
- [ ] 电机运行状态（运行中/停止/报警）能够正确显示
- [ ] 运动完成状态能够及时反馈
- [ ] 异常状态能够及时检测和报告

### 3.5 UI集成验收标准
- [ ] 所有控制按钮能够正确响应用户操作
- [ ] 参数设置界面能够正确保存和加载参数
- [ ] 状态显示界面能够实时更新电机状态
- [ ] 界面风格与现有设计保持一致
- [ ] 用户操作响应时间不超过100ms

### 3.6 示教功能验收标准
- [ ] 回原点功能能够将电机准确移动到0°位置
- [ ] 点动控制能够精确调整电机位置
- [ ] 位置保存功能能够记录当前相对于原点的角度值
- [ ] 位置移动功能能够准确移动到保存的相对位置
- [ ] 保存的位置数据能够持久化存储
- [ ] 示教移动的速度参数与运行参数保持一致

### 3.7 安全性验收标准
- [ ] 急停功能能够立即停止所有电机运动
- [ ] 限位保护功能正常工作
- [ ] 参数范围检查能够防止异常输入
- [ ] 异常情况下能够安全停机

## 4. 非功能性需求

### 4.1 性能要求
- **响应时间**：用户操作响应时间不超过100ms
- **实时性**：电机状态更新频率不低于10Hz
- **精度要求**：翻转电机角度控制精度±0.1°，皮带电机位置控制精度±0.1mm
- **运动平滑性**：电机运动应平滑无抖动，支持S型速度曲线

### 4.2 可靠性要求
- **故障恢复**：系统异常后能够自动恢复到安全状态
- **数据完整性**：电机参数设置和示教位置能够持久化保存
- **异常处理**：所有可能的异常情况都有相应的处理机制
- **运行稳定性**：连续运行24小时无故障

### 4.3 兼容性要求
- **硬件兼容**：与DMC1000B运动控制卡完全兼容
- **软件兼容**：与现有C# WinForms架构兼容
- **接口兼容**：与现有MotorManager接口保持兼容
- **UI兼容**：与现有界面设计风格保持一致

### 4.4 可维护性要求
- **代码结构**：遵循现有项目的分层架构
- **文档完整**：提供完整的中文代码注释和使用文档
- **配置灵活**：电机参数可通过配置文件调整
- **日志记录**：提供详细的操作和异常日志

### 4.5 安全性要求
- **输入验证**：所有用户输入都进行有效性检查
- **权限控制**：关键操作需要相应权限
- **异常保护**：异常情况下自动进入安全模式
- **数据保护**：防止参数被意外修改或丢失

## 5. 技术约束

### 5.1 硬件约束
- 使用DMC1000B运动控制卡，支持4轴步进电机控制
- 电机轴分配：轴0(左翻转)、轴1(右翻转)、轴2(输出皮带)、轴3(输入皮带)
- 翻转电机配备原点开关（ORG0、ORG1）
- 使用24V外部电源供电

### 5.2 软件约束
- 基于现有C# WinForms架构开发
- 使用P/Invoke技术调用Dmc1000.dll
- 遵循现有项目的分层架构和编码规范
- 与现有MotorManager接口保持兼容

### 5.3 接口约束
- 必须与现有UI控件（MotorFlipPanel、MotorBeltPanel）集成
- 保持与现有事件系统的兼容性
- 使用现有的日志和配置系统

## 6. 风险评估

### 6.1 技术风险
- DMC1000B控制卡驱动兼容性问题
- P/Invoke调用可能的内存泄漏风险
- 多线程环境下的线程安全问题

### 6.2 集成风险
- 与现有系统集成可能的接口冲突
- UI控件集成可能的界面布局问题
- 现有MotorManager的替换风险

### 6.3 性能风险
- 实时性要求可能的性能瓶颈
- 多轴同时控制的资源竞争问题
- 长时间运行的稳定性风险

## 7. 项目约束

### 7.1 时间约束
- 项目需要在合理时间内完成
- 分阶段交付，优先实现核心功能

### 7.2 资源约束
- 基于现有硬件平台开发
- 复用现有代码架构和组件
- 最小化对现有系统的影响

### 7.3 质量约束
- 代码质量必须符合现有项目标准
- 必须提供完整的中文注释
- 必须通过所有验收测试
