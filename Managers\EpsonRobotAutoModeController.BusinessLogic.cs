using System;
using System.Threading;
using System.Threading.Tasks;
using MyHMI.Helpers;
using MyHMI.Models;

namespace MyHMI.Managers
{
    /// <summary>
    /// EpsonRobotAutoModeController 业务逻辑部分
    /// </summary>
    public partial class EpsonRobotAutoModeController
    {
        #region 业务逻辑处理方法

        /// <summary>
        /// 处理GETPICK消息 - 取料权限请求
        /// </summary>
        /// <param name="message">机器人消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务</returns>
        private async Task HandleGetPickMessage(RobotMessage message, CancellationToken cancellationToken)
        {
            try
            {
                LogHelper.Info($"处理机器人{message.RobotId}取料权限请求");

                // 设置机器人状态为等待取料权限
                SetRobotWorkflowState(message.RobotId, RobotWorkflowState.WaitingPickPermission);

                // 检查对应扫码器是否有扫码数据（被动检查，扫码器由上游系统控制）
                bool scannerHasData = await CheckScannerHasData(message.RobotId, cancellationToken);

                // 创建响应
                var responseType = scannerHasData ? ControllerResponseType.ALLOWPICK : ControllerResponseType.DENYPICK;
                var response = new ControllerResponse(message.RobotId, responseType)
                {
                    OriginalMessageId = message.MessageId
                };

                // 将响应加入队列
                _responseQueue.Enqueue(response);

                // 更新机器人状态
                if (scannerHasData)
                {
                    SetRobotWorkflowState(message.RobotId, RobotWorkflowState.Picking);
                    LogHelper.Info($"机器人{message.RobotId}获得取料权限 - 扫码器有数据");
                }
                else
                {
                    SetRobotWorkflowState(message.RobotId, RobotWorkflowState.Ready);
                    LogHelper.Info($"机器人{message.RobotId}取料权限被拒绝 - 扫码器无数据");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"处理机器人{message.RobotId}取料权限请求失败", ex);
                SetRobotWorkflowState(message.RobotId, RobotWorkflowState.Error);
                throw;
            }
        }

        /// <summary>
        /// 处理INPICK消息 - 取料确认和数据传输
        ///
        /// 具体流程：
        /// 机器人1: 收到"INPICK" → O0001输出0 → 等待100ms → 扫描I0006 → 状态为1则发送"中间向左扫码器字符串,左扫码器字符串"
        /// 机器人2: 收到"INPICK" → O0003输出0 → 等待100ms → 扫描I0010 → 状态为1则发送"中间向右扫码器字符串,右扫码器字符串"
        /// </summary>
        /// <param name="message">机器人消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务</returns>
        private async Task HandleInPickMessage(RobotMessage message, CancellationToken cancellationToken)
        {
            try
            {
                LogHelper.Info($"处理机器人{message.RobotId}取料确认和数据传输");

                // 设置机器人状态为数据传输中
                SetRobotWorkflowState(message.RobotId, RobotWorkflowState.DataTransmitting);

                // 根据用户要求：控制对应的IO输出为0（false）
                // 机器人1: O0001输出0，机器人2: O0003输出0
                string outputIO = GetRobotOutputIO(message.RobotId);
                LogHelper.Info($"机器人{message.RobotId}设置输出IO {outputIO} = 0");
                await ControlRobotIO(message.RobotId, false, cancellationToken);

                // 根据用户要求：等待100ms
                LogHelper.Info($"机器人{message.RobotId}等待100ms...");
                await Task.Delay(100, cancellationToken);

                // 检查对应的IO输入状态
                // 机器人1: 检查I0006，机器人2: 检查I0010
                string inputIO = GetRobotInputIO(message.RobotId);
                bool ioInputStatus = await CheckRobotIOInput(message.RobotId, cancellationToken);
                LogHelper.Info($"机器人{message.RobotId}检查输入IO {inputIO} 状态: {ioInputStatus}");

                string scanData = string.Empty;
                // 修正IO逻辑：低电平有效硬件，IO=0表示取料完成，IO=1表示取料未完成
                if (!ioInputStatus) // 如果输入状态为0（低电平）
                {
                    // 取料完成，获取扫码器数据并按照用户要求的格式组合
                    scanData = await GetFormattedScannerData(message.RobotId, cancellationToken);
                    LogHelper.Info($"机器人{message.RobotId}取料完成（IO={inputIO}=0），获取到格式化扫码数据: {scanData}");
                }
                else
                {
                    LogHelper.Warning($"机器人{message.RobotId}输入IO {inputIO} 状态为1（高电平），取料未完成，不发送扫码数据");
                }

                // 创建扫码数据响应
                var response = new ControllerResponse(message.RobotId, ControllerResponseType.SCANDATA, scanData)
                {
                    OriginalMessageId = message.MessageId
                };

                // 将响应加入队列
                _responseQueue.Enqueue(response);

                // 更新机器人状态
                SetRobotWorkflowState(message.RobotId, RobotWorkflowState.Processing);

                // 根据文档要求：发送完数据后，等待1s，然后自动复位到位置1，并置L_moto_ready/R_moto_ready为1
                await Task.Delay(1000, cancellationToken);
                await ResetRobotToPosition1AndSetReady(message.RobotId, cancellationToken);

                LogHelper.Info($"机器人{message.RobotId}数据传输完成，扫码数据: {scanData}");
            }
            catch (Exception ex)
            {
                LogHelper.Error($"处理机器人{message.RobotId}取料确认和数据传输失败", ex);
                SetRobotWorkflowState(message.RobotId, RobotWorkflowState.Error);
                throw;
            }
        }

        /// <summary>
        /// 处理GETNGPUT消息 - NG品放置权限请求
        /// </summary>
        /// <param name="message">机器人消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务</returns>
        private async Task HandleGetNGPutMessage(RobotMessage message, CancellationToken cancellationToken)
        {
            try
            {
                LogHelper.Info($"处理机器人{message.RobotId}NG品放置权限请求");

                // 设置机器人状态为等待NG放置权限
                SetRobotWorkflowState(message.RobotId, RobotWorkflowState.WaitingNGPutPermission);

                // 检查对应的NG状态IO
                bool ngStatusOK = await CheckRobotNGStatus(message.RobotId, cancellationToken);

                // 创建响应
                var responseType = ngStatusOK ? ControllerResponseType.ALLOWNGPUT : ControllerResponseType.DENYNGPUT;
                var response = new ControllerResponse(message.RobotId, responseType)
                {
                    OriginalMessageId = message.MessageId
                };

                // 将响应加入队列
                _responseQueue.Enqueue(response);

                // 更新机器人状态
                if (ngStatusOK)
                {
                    SetRobotWorkflowState(message.RobotId, RobotWorkflowState.NGPutting);
                    LogHelper.Info($"机器人{message.RobotId}获得NG品放置权限");
                }
                else
                {
                    SetRobotWorkflowState(message.RobotId, RobotWorkflowState.Processing);
                    LogHelper.Info($"机器人{message.RobotId}NG品放置权限被拒绝 - NG状态不允许");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"处理机器人{message.RobotId}NG品放置权限请求失败", ex);
                SetRobotWorkflowState(message.RobotId, RobotWorkflowState.Error);
                throw;
            }
        }

        /// <summary>
        /// 处理NGPUTFULL消息 - NG品放置满确认
        /// </summary>
        /// <param name="message">机器人消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务</returns>
        private async Task HandleNGPutFullMessage(RobotMessage message, CancellationToken cancellationToken)
        {
            try
            {
                LogHelper.Info($"处理机器人{message.RobotId}NG品放置满确认");

                // 设置机器人状态为复位中
                SetRobotWorkflowState(message.RobotId, RobotWorkflowState.Resetting);

                // 监控对应的NG状态IO变化
                await MonitorNGStatusChange(message.RobotId, cancellationToken);

                // 等待复位延迟时间
                await Task.Delay(_configuration.ResetDelayMs, cancellationToken);

                // 创建复位响应
                var response = new ControllerResponse(message.RobotId, ControllerResponseType.RESETNGPUT)
                {
                    OriginalMessageId = message.MessageId
                };

                // 将响应加入队列
                _responseQueue.Enqueue(response);

                // 更新机器人状态为准备就绪
                SetRobotWorkflowState(message.RobotId, RobotWorkflowState.Ready);

                LogHelper.Info($"机器人{message.RobotId}NG品放置复位完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error($"处理机器人{message.RobotId}NG品放置满确认失败", ex);
                SetRobotWorkflowState(message.RobotId, RobotWorkflowState.Error);
                throw;
            }
        }

        /// <summary>
        /// 处理GETOKPUT消息 - OK品放置权限请求
        /// </summary>
        /// <param name="message">机器人消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务</returns>
        private async Task HandleGetOKPutMessage(RobotMessage message, CancellationToken cancellationToken)
        {
            try
            {
                LogHelper.Info($"处理机器人{message.RobotId}OK品放置权限请求");

                // 设置机器人状态为等待OK放置权限
                SetRobotWorkflowState(message.RobotId, RobotWorkflowState.WaitingOKPutPermission);

                // 检查I0106状态（OK品放置状态）
                bool okPutStatusOK = await CheckOKPutStatus(cancellationToken);

                // 创建响应
                var responseType = okPutStatusOK ? ControllerResponseType.ALLOWOKPUT : ControllerResponseType.DENYOKPUT;
                var response = new ControllerResponse(message.RobotId, responseType)
                {
                    OriginalMessageId = message.MessageId
                };

                // 将响应加入队列
                _responseQueue.Enqueue(response);

                // 更新机器人状态
                if (okPutStatusOK)
                {
                    SetRobotWorkflowState(message.RobotId, RobotWorkflowState.OKPutting);
                    LogHelper.Info($"机器人{message.RobotId}获得OK品放置权限");
                }
                else
                {
                    SetRobotWorkflowState(message.RobotId, RobotWorkflowState.Processing);
                    LogHelper.Info($"机器人{message.RobotId}OK品放置权限被拒绝 - OK品放置状态不允许");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"处理机器人{message.RobotId}OK品放置权限请求失败", ex);
                SetRobotWorkflowState(message.RobotId, RobotWorkflowState.Error);
                throw;
            }
        }

        #endregion

        #region IO和扫码器操作方法

        /// <summary>
        /// 检查扫码器是否有扫码数据（被动检查，扫码器由上游系统控制）
        /// </summary>
        /// <param name="robotId">机器人ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>扫码器是否有数据</returns>
        private async Task<bool> CheckScannerHasData(int robotId, CancellationToken cancellationToken)
        {
            await Task.CompletedTask; // 消除CS1998警告
            try
            {
                // 根据机器人ID确定对应的扫码器
                int scannerId = GetScannerIdByRobotId(robotId);
                
                // 检查扫码器是否有数据（被动检查，不控制扫码器）
                var scanData = _scannerAutoModeManager.GetScannerData(scannerId);
                bool hasData = !string.IsNullOrEmpty(scanData);

                LogHelper.Info($"机器人{robotId}对应扫码器{scannerId}数据状态: {(hasData ? "有数据" : "无数据")}");
                return hasData;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"检查机器人{robotId}扫码器状态失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 控制机器人对应的IO输出
        /// </summary>
        /// <param name="robotId">机器人ID</param>
        /// <param name="outputValue">输出值</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务</returns>
        private async Task ControlRobotIO(int robotId, bool outputValue, CancellationToken cancellationToken)
        {
            try
            {
                string outputIO = GetRobotOutputIO(robotId);
                bool result = await _ioManager.SetOutputAsync(outputIO, outputValue);
                
                LogHelper.Info($"控制机器人{robotId}输出IO {outputIO} = {outputValue}, 结果: {result}");
            }
            catch (Exception ex)
            {
                LogHelper.Error($"控制机器人{robotId}IO输出失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 检查机器人对应的IO输入状态
        /// </summary>
        /// <param name="robotId">机器人ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>IO输入状态</returns>
        private async Task<bool> CheckRobotIOInput(int robotId, CancellationToken cancellationToken)
        {
            try
            {
                string inputIO = GetRobotInputIO(robotId);
                bool inputStatus = await _ioManager.ReadInputAsync(inputIO);
                
                LogHelper.Info($"机器人{robotId}输入IO {inputIO} 状态: {inputStatus}");
                return inputStatus;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"检查机器人{robotId}IO输入状态失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取扫码器中已有的数据（被动获取，扫码器由上游系统控制）
        /// </summary>
        /// <param name="robotId">机器人ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>扫码数据</returns>
        private async Task<string> GetScannerData(int robotId, CancellationToken cancellationToken)
        {
            await Task.CompletedTask; // 消除CS1998警告
            try
            {
                int scannerId = GetScannerIdByRobotId(robotId);
                string scanData = _scannerAutoModeManager.GetScannerData(scannerId);

                LogHelper.Info($"获取机器人{robotId}对应扫码器{scannerId}中的数据: {scanData}");
                return scanData ?? string.Empty;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"获取机器人{robotId}扫码器数据失败", ex);
                return string.Empty;
            }
        }

        /// <summary>
        /// 获取格式化的扫码器数据
        /// 机器人1: "中间向左扫码器字符串,左扫码器字符串"
        /// 机器人2: "中间向右扫码器字符串,右扫码器字符串"
        /// </summary>
        /// <param name="robotId">机器人ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>格式化的扫码数据</returns>
        private async Task<string> GetFormattedScannerData(int robotId, CancellationToken cancellationToken)
        {
            await Task.CompletedTask; // 消除CS1998警告
            try
            {
                if (robotId == _configuration.Robot1Id)
                {
                    // 机器人1: 获取中间向左扫码器和左扫码器数据
                    // 根据配置，机器人1对应扫码器1，中间扫码器是扫码器3
                    string middleLeftData = _scannerAutoModeManager.GetMiddleLeftScannerData() ?? string.Empty;
                    string leftData = _scannerAutoModeManager.GetScannerData(_configuration.ScannerMapping.Scanner1Id) ?? string.Empty;

                    string formattedData = $"{middleLeftData},{leftData}";
                    LogHelper.Info($"机器人1格式化扫码数据: 中间向左={middleLeftData}, 左={leftData}, 组合={formattedData}");
                    return formattedData;
                }
                else if (robotId == _configuration.Robot2Id)
                {
                    // 机器人2: 获取中间向右扫码器和右扫码器数据
                    // 根据配置，机器人2对应扫码器2，中间扫码器是扫码器3
                    string middleRightData = _scannerAutoModeManager.GetMiddleRightScannerData() ?? string.Empty;
                    string rightData = _scannerAutoModeManager.GetScannerData(_configuration.ScannerMapping.Scanner2Id) ?? string.Empty;

                    string formattedData = $"{middleRightData},{rightData}";
                    LogHelper.Info($"机器人2格式化扫码数据: 中间向右={middleRightData}, 右={rightData}, 组合={formattedData}");
                    return formattedData;
                }
                else
                {
                    LogHelper.Warning($"未知的机器人ID: {robotId}，返回空数据");
                    return string.Empty;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"获取机器人{robotId}格式化扫码数据失败", ex);
                return string.Empty;
            }
        }

        /// <summary>
        /// 检查机器人NG状态
        /// </summary>
        /// <param name="robotId">机器人ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>NG状态</returns>
        private async Task<bool> CheckRobotNGStatus(int robotId, CancellationToken cancellationToken)
        {
            try
            {
                string ngStatusIO = GetRobotNGStatusIO(robotId);
                bool ngStatus = await _ioManager.ReadInputAsync(ngStatusIO);
                
                // NG状态为0表示可以放置
                bool canPut = !ngStatus;
                LogHelper.Info($"机器人{robotId}NG状态IO {ngStatusIO} = {ngStatus}, 可放置: {canPut}");
                return canPut;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"检查机器人{robotId}NG状态失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 监控NG状态变化
        /// 监控IO状态变化，检测完整的变化周期
        /// </summary>
        /// <param name="robotId">机器人ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务</returns>
        private async Task MonitorNGStatusChange(int robotId, CancellationToken cancellationToken)
        {
            try
            {
                string ngStatusIO = GetRobotNGStatusIO(robotId);
                LogHelper.Info($"开始监控机器人{robotId}NG状态IO {ngStatusIO} 变化周期");

                // 获取初始状态
                bool initialStatus = await _ioManager.ReadInputAsync(ngStatusIO);
                LogHelper.Info($"机器人{robotId}NG状态IO {ngStatusIO} 初始状态: {initialStatus}");

                // 监控状态变化的参数
                int maxMonitoringTime = _configuration.NGStatusMonitorTimeoutMs; // 最大监控时间
                int checkInterval = _configuration.NGStatusCheckIntervalMs; // 检查间隔
                int stableTime = _configuration.NGStatusStableTimeMs; // 状态稳定时间

                DateTime startTime = DateTime.Now;
                bool hasChanged = false;
                bool currentStatus = initialStatus;
                DateTime lastChangeTime = DateTime.Now;

                while ((DateTime.Now - startTime).TotalMilliseconds < maxMonitoringTime && !cancellationToken.IsCancellationRequested)
                {
                    // 读取当前状态
                    bool newStatus = await _ioManager.ReadInputAsync(ngStatusIO);

                    // 检查状态是否发生变化
                    if (newStatus != currentStatus)
                    {
                        LogHelper.Info($"机器人{robotId}NG状态IO {ngStatusIO} 状态变化: {currentStatus} → {newStatus}");
                        currentStatus = newStatus;
                        lastChangeTime = DateTime.Now;
                        hasChanged = true;
                    }

                    // 如果状态已经变化并且保持稳定一段时间，认为变化周期完成
                    if (hasChanged && (DateTime.Now - lastChangeTime).TotalMilliseconds >= stableTime)
                    {
                        LogHelper.Info($"机器人{robotId}NG状态IO {ngStatusIO} 变化周期完成，最终状态: {currentStatus}");
                        break;
                    }

                    // 等待下次检查
                    await Task.Delay(checkInterval, cancellationToken);
                }

                // 检查是否超时
                if ((DateTime.Now - startTime).TotalMilliseconds >= maxMonitoringTime)
                {
                    LogHelper.Warning($"机器人{robotId}NG状态IO {ngStatusIO} 监控超时，当前状态: {currentStatus}");
                }
                else
                {
                    LogHelper.Info($"机器人{robotId}NG状态IO {ngStatusIO} 监控完成，总耗时: {(DateTime.Now - startTime).TotalMilliseconds}ms");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"监控机器人{robotId}NG状态变化失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 检查OK品放置状态
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>OK品放置状态</returns>
        private async Task<bool> CheckOKPutStatus(CancellationToken cancellationToken)
        {
            try
            {
                string okPutStatusIO = _configuration.IOMapping.OKPutStatusIO;
                bool okPutStatus = await _ioManager.ReadInputAsync(okPutStatusIO);
                
                // I0106为0表示可以放置
                bool canPut = !okPutStatus;
                LogHelper.Info($"OK品放置状态IO {okPutStatusIO} = {okPutStatus}, 可放置: {canPut}");
                return canPut;
            }
            catch (Exception ex)
            {
                LogHelper.Error("检查OK品放置状态失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 初始化机器人准备状态字段
        /// 根据文档要求：程序启动时初始化L_moto_ready和R_moto_ready字段
        /// </summary>
        /// <returns>任务</returns>
        private async Task InitializeRobotReadyStatus()
        {
            await Task.CompletedTask; // 消除CS1998警告
            try
            {
                LogHelper.Info("初始化机器人准备状态字段...");

                // 设置L_moto_ready为1（机器人1准备就绪）
                _scaraCommunicationManager.L_moto_ready = true;
                LogHelper.Info("设置L_moto_ready = 1");

                // 设置R_moto_ready为1（机器人2准备就绪）
                _scaraCommunicationManager.R_moto_ready = true;
                LogHelper.Info("设置R_moto_ready = 1");

                LogHelper.Info("机器人准备状态字段初始化完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("初始化机器人准备状态字段失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 复位机器人到位置1并设置准备状态
        /// 根据文档要求：发送完数据后，等待1s，然后自动复位到位置1，并置L_moto_ready/R_moto_ready为1
        /// </summary>
        /// <param name="robotId">机器人ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务</returns>
        private async Task ResetRobotToPosition1AndSetReady(int robotId, CancellationToken cancellationToken)
        {
            try
            {
                LogHelper.Info($"机器人{robotId}开始复位到位置1并设置准备状态...");

                // 发送复位到位置1的指令给机器人
                var resetCommand = "RESETPOS1"; // 复位到位置1的指令
                var robotManager = GetRobotManager(robotId);
                if (robotManager != null)
                {
                    var result = await robotManager.SendCustomCommandAsync(resetCommand, "Reset");
                    LogHelper.Info($"机器人{robotId}复位指令发送结果: {result != null}");
                }

                // 设置对应的准备状态字段
                if (robotId == _configuration.Robot1Id)
                {
                    _scaraCommunicationManager.L_moto_ready = true;
                    LogHelper.Info($"机器人{robotId}设置L_moto_ready = 1");
                }
                else if (robotId == _configuration.Robot2Id)
                {
                    _scaraCommunicationManager.R_moto_ready = true;
                    LogHelper.Info($"机器人{robotId}设置R_moto_ready = 1");
                }

                LogHelper.Info($"机器人{robotId}复位到位置1并设置准备状态完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error($"机器人{robotId}复位到位置1并设置准备状态失败", ex);
                throw;
            }
        }

        #endregion
    }
}
