using System;
using System.Threading.Tasks;
using MyHMI.Settings;
using MyHMI.Helpers;

namespace MyHMI.Tests
{
    /// <summary>
    /// Communication模块参数管理测试
    /// </summary>
    public static class CommunicationParameterTest
    {
        /// <summary>
        /// 运行Communication模块参数管理测试
        /// </summary>
        public static async Task RunCommunicationParameterTestAsync()
        {
            try
            {
                LogHelper.Info("开始Communication模块参数管理测试...");

                // 测试1：验证EpsonRobot参数
                await TestEpsonRobotParameters();

                // 测试2：验证Scanner参数
                await TestScannerParameters();

                // 测试3：验证MultiScanner参数
                await TestMultiScannerParameters();

                // 测试4：验证Modbus参数
                await TestModbusParameters();

                // 测试5：验证参数持久化
                await TestCommunicationParameterPersistence();

                LogHelper.Info("Communication模块参数管理测试完成，所有测试通过！");
            }
            catch (Exception ex)
            {
                LogHelper.Error("Communication模块参数管理测试失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 测试EpsonRobot参数
        /// </summary>
        private static async Task TestEpsonRobotParameters()
        {
            LogHelper.Info("测试1：EpsonRobot参数...");

            await Task.Run(() =>
            {
                var commSettings = Settings.Settings.Current.Communication;
                
                // 验证EpsonRobot1参数
                if (string.IsNullOrEmpty(commSettings.EpsonRobot1IP))
                    throw new Exception("EpsonRobot1 IP地址为空");

                if (commSettings.EpsonRobot1ControlPort <= 0)
                    throw new Exception("EpsonRobot1控制端口无效");

                if (commSettings.EpsonRobot1DataPort <= 0)
                    throw new Exception("EpsonRobot1数据端口无效");

                LogHelper.Info($"✓ EpsonRobot1 IP: {commSettings.EpsonRobot1IP}");
                LogHelper.Info($"✓ EpsonRobot1 控制端口: {commSettings.EpsonRobot1ControlPort}");
                LogHelper.Info($"✓ EpsonRobot1 数据端口: {commSettings.EpsonRobot1DataPort}");
                LogHelper.Info($"✓ EpsonRobot1 密码: {commSettings.EpsonRobot1Password}");

                // 验证EpsonRobot2参数
                if (string.IsNullOrEmpty(commSettings.EpsonRobot2IP))
                    throw new Exception("EpsonRobot2 IP地址为空");

                if (commSettings.EpsonRobot2ControlPort <= 0)
                    throw new Exception("EpsonRobot2控制端口无效");

                if (commSettings.EpsonRobot2DataPort <= 0)
                    throw new Exception("EpsonRobot2数据端口无效");

                LogHelper.Info($"✓ EpsonRobot2 IP: {commSettings.EpsonRobot2IP}");
                LogHelper.Info($"✓ EpsonRobot2 控制端口: {commSettings.EpsonRobot2ControlPort}");
                LogHelper.Info($"✓ EpsonRobot2 数据端口: {commSettings.EpsonRobot2DataPort}");

                // 验证超时参数
                if (commSettings.EpsonRobot1ConnectTimeout <= 0)
                    throw new Exception("EpsonRobot1连接超时无效");

                if (commSettings.EpsonRobot1ReceiveTimeout <= 0)
                    throw new Exception("EpsonRobot1接收超时无效");

                LogHelper.Info($"✓ EpsonRobot1 连接超时: {commSettings.EpsonRobot1ConnectTimeout}ms");
                LogHelper.Info($"✓ EpsonRobot1 接收超时: {commSettings.EpsonRobot1ReceiveTimeout}ms");
            });

            LogHelper.Info("✓ EpsonRobot参数测试通过");
        }

        /// <summary>
        /// 测试Scanner参数
        /// </summary>
        private static async Task TestScannerParameters()
        {
            LogHelper.Info("测试2：Scanner参数...");

            await Task.Run(() =>
            {
                var commSettings = Settings.Settings.Current.Communication;
                
                // 验证扫描器参数
                if (string.IsNullOrEmpty(commSettings.ScannerComPort))
                    throw new Exception("扫描器串口为空");

                if (commSettings.ScannerBaudRate <= 0)
                    throw new Exception("扫描器波特率无效");

                if (commSettings.ScannerDataBits <= 0)
                    throw new Exception("扫描器数据位无效");

                if (commSettings.ScannerTimeout <= 0)
                    throw new Exception("扫描器超时无效");

                LogHelper.Info($"✓ 扫描器串口: {commSettings.ScannerComPort}");
                LogHelper.Info($"✓ 扫描器波特率: {commSettings.ScannerBaudRate}");
                LogHelper.Info($"✓ 扫描器数据位: {commSettings.ScannerDataBits}");
                LogHelper.Info($"✓ 扫描器停止位: {commSettings.ScannerStopBits}");
                LogHelper.Info($"✓ 扫描器校验位: {commSettings.ScannerParity}");
                LogHelper.Info($"✓ 扫描器超时: {commSettings.ScannerTimeout}ms");
                LogHelper.Info($"✓ 扫描器自动重连: {commSettings.ScannerAutoReconnect}");
            });

            LogHelper.Info("✓ Scanner参数测试通过");
        }

        /// <summary>
        /// 测试MultiScanner参数
        /// </summary>
        private static async Task TestMultiScannerParameters()
        {
            LogHelper.Info("测试3：MultiScanner参数...");

            await Task.Run(() =>
            {
                var commSettings = Settings.Settings.Current.Communication;
                
                // 验证Scanner1参数
                if (string.IsNullOrEmpty(commSettings.Scanner1ComPort))
                    throw new Exception("Scanner1串口为空");

                if (commSettings.Scanner1BaudRate <= 0)
                    throw new Exception("Scanner1波特率无效");

                LogHelper.Info($"✓ Scanner1 串口: {commSettings.Scanner1ComPort}");
                LogHelper.Info($"✓ Scanner1 波特率: {commSettings.Scanner1BaudRate}");
                LogHelper.Info($"✓ Scanner1 数据位: {commSettings.Scanner1DataBits}");
                LogHelper.Info($"✓ Scanner1 停止位: {commSettings.Scanner1StopBits}");
                LogHelper.Info($"✓ Scanner1 校验位: {commSettings.Scanner1Parity}");

                // 验证Scanner2参数
                if (string.IsNullOrEmpty(commSettings.Scanner2ComPort))
                    throw new Exception("Scanner2串口为空");

                if (commSettings.Scanner2BaudRate <= 0)
                    throw new Exception("Scanner2波特率无效");

                LogHelper.Info($"✓ Scanner2 串口: {commSettings.Scanner2ComPort}");
                LogHelper.Info($"✓ Scanner2 波特率: {commSettings.Scanner2BaudRate}");

                // 验证Scanner3参数
                if (string.IsNullOrEmpty(commSettings.Scanner3ComPort))
                    throw new Exception("Scanner3串口为空");

                if (commSettings.Scanner3BaudRate <= 0)
                    throw new Exception("Scanner3波特率无效");

                LogHelper.Info($"✓ Scanner3 串口: {commSettings.Scanner3ComPort}");
                LogHelper.Info($"✓ Scanner3 波特率: {commSettings.Scanner3BaudRate}");

                // 验证兼容性属性
                LogHelper.Info($"✓ MultiScanner1 波特率: {commSettings.MultiScanner1BaudRate}");
                LogHelper.Info($"✓ MultiScanner2 波特率: {commSettings.MultiScanner2BaudRate}");
                LogHelper.Info($"✓ MultiScanner3 波特率: {commSettings.MultiScanner3BaudRate}");

                LogHelper.Info($"✓ MultiScanner1 端口名: {commSettings.MultiScanner1PortName}");
                LogHelper.Info($"✓ MultiScanner2 端口名: {commSettings.MultiScanner2PortName}");
                LogHelper.Info($"✓ MultiScanner3 端口名: {commSettings.MultiScanner3PortName}");
            });

            LogHelper.Info("✓ MultiScanner参数测试通过");
        }

        /// <summary>
        /// 测试Modbus参数
        /// </summary>
        private static async Task TestModbusParameters()
        {
            LogHelper.Info("测试4：Modbus参数...");

            await Task.Run(() =>
            {
                var commSettings = Settings.Settings.Current.Communication;
                
                // 验证ModbusTcp参数
                if (string.IsNullOrEmpty(commSettings.ModbusTcpIP))
                    throw new Exception("ModbusTcp IP地址为空");

                if (commSettings.ModbusTcpPort <= 0)
                    throw new Exception("ModbusTcp端口无效");

                if (commSettings.ModbusTcpSlaveId < 0)
                    throw new Exception("ModbusTcp从站ID无效");

                if (commSettings.ModbusTcpTimeout <= 0)
                    throw new Exception("ModbusTcp超时无效");

                LogHelper.Info($"✓ ModbusTcp IP: {commSettings.ModbusTcpIP}");
                LogHelper.Info($"✓ ModbusTcp 端口: {commSettings.ModbusTcpPort}");
                LogHelper.Info($"✓ ModbusTcp 从站ID: {commSettings.ModbusTcpSlaveId}");
                LogHelper.Info($"✓ ModbusTcp 超时: {commSettings.ModbusTcpTimeout}ms");
                LogHelper.Info($"✓ ModbusTcp 自动重连: {commSettings.ModbusTcpAutoReconnect}");
                LogHelper.Info($"✓ ModbusTcp 重连间隔: {commSettings.ModbusTcpReconnectInterval}ms");
                LogHelper.Info($"✓ ModbusTcp 最大重试: {commSettings.ModbusTcpMaxRetries}");
            });

            LogHelper.Info("✓ Modbus参数测试通过");
        }

        /// <summary>
        /// 测试Communication参数持久化
        /// </summary>
        private static async Task TestCommunicationParameterPersistence()
        {
            LogHelper.Info("测试5：Communication参数持久化...");

            await Task.Run(() =>
            {
                var commSettings = Settings.Settings.Current.Communication;

                // 保存原始值
                string originalEpsonIP = commSettings.EpsonRobot1IP;
                int originalEpsonPort = commSettings.EpsonRobot1ControlPort;
                string originalScannerPort = commSettings.ScannerComPort;
                int originalScannerBaud = commSettings.ScannerBaudRate;

                try
                {
                    // 修改参数
                    string testEpsonIP = "***************";
                    int testEpsonPort = 9999;
                    string testScannerPort = "COM99";
                    int testScannerBaud = 38400;

                    commSettings.EpsonRobot1IP = testEpsonIP;
                    commSettings.EpsonRobot1ControlPort = testEpsonPort;
                    commSettings.ScannerComPort = testScannerPort;
                    commSettings.ScannerBaudRate = testScannerBaud;

                    // 保存到文件
                    Settings.Settings.Save();

                    // 重新加载
                    Settings.Settings.Load();
                    var reloadedSettings = Settings.Settings.Current.Communication;

                    // 验证持久化
                    if (reloadedSettings.EpsonRobot1IP != testEpsonIP)
                        throw new Exception("EpsonRobot1 IP持久化失败");

                    if (reloadedSettings.EpsonRobot1ControlPort != testEpsonPort)
                        throw new Exception("EpsonRobot1控制端口持久化失败");

                    if (reloadedSettings.ScannerComPort != testScannerPort)
                        throw new Exception("扫描器串口持久化失败");

                    if (reloadedSettings.ScannerBaudRate != testScannerBaud)
                        throw new Exception("扫描器波特率持久化失败");

                    LogHelper.Info("✓ Communication参数持久化验证通过");
                }
                finally
                {
                    // 恢复原始值
                    var currentSettings = Settings.Settings.Current.Communication;
                    currentSettings.EpsonRobot1IP = originalEpsonIP;
                    currentSettings.EpsonRobot1ControlPort = originalEpsonPort;
                    currentSettings.ScannerComPort = originalScannerPort;
                    currentSettings.ScannerBaudRate = originalScannerBaud;
                    Settings.Settings.Save();
                }
            });

            LogHelper.Info("✓ Communication参数持久化测试通过");
        }
    }
}
