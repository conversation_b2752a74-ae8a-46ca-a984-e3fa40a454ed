using System;
using MyHMI.Models;

namespace MyHMI.Events
{
    /// <summary>
    /// 自动模式状态变化事件参数
    /// </summary>
    public class AutoModeStateChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 旧状态
        /// </summary>
        public EpsonAutoModeState OldState { get; set; }

        /// <summary>
        /// 新状态
        /// </summary>
        public EpsonAutoModeState NewState { get; set; }

        /// <summary>
        /// 状态变化消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 变化时间
        /// </summary>
        public DateTime ChangeTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否是错误状态变化
        /// </summary>
        public bool IsError { get; set; } = false;

        /// <summary>
        /// 错误详情（如果是错误状态变化）
        /// </summary>
        public Exception ErrorDetails { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public AutoModeStateChangedEventArgs() { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="oldState">旧状态</param>
        /// <param name="newState">新状态</param>
        /// <param name="message">消息</param>
        public AutoModeStateChangedEventArgs(EpsonAutoModeState oldState, EpsonAutoModeState newState, string message = "")
        {
            OldState = oldState;
            NewState = newState;
            Message = message ?? string.Empty;
            IsError = newState == EpsonAutoModeState.Error;
        }

        /// <summary>
        /// 构造函数（错误状态变化）
        /// </summary>
        /// <param name="oldState">旧状态</param>
        /// <param name="newState">新状态</param>
        /// <param name="message">消息</param>
        /// <param name="errorDetails">错误详情</param>
        public AutoModeStateChangedEventArgs(EpsonAutoModeState oldState, EpsonAutoModeState newState, string message, Exception errorDetails)
            : this(oldState, newState, message)
        {
            ErrorDetails = errorDetails;
            IsError = true;
        }
    }

    /// <summary>
    /// 机器人消息接收事件参数
    /// </summary>
    public class RobotMessageReceivedEventArgs : EventArgs
    {
        /// <summary>
        /// 机器人消息
        /// </summary>
        public RobotMessage Message { get; set; }

        /// <summary>
        /// 接收时间
        /// </summary>
        public DateTime ReceivedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 构造函数
        /// </summary>
        public RobotMessageReceivedEventArgs() { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="message">机器人消息</param>
        public RobotMessageReceivedEventArgs(RobotMessage message)
        {
            Message = message;
        }
    }

    /// <summary>
    /// 机器人工作流状态变化事件参数
    /// </summary>
    public class RobotWorkflowStateChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 机器人ID
        /// </summary>
        public int RobotId { get; set; }

        /// <summary>
        /// 旧状态
        /// </summary>
        public RobotWorkflowState OldState { get; set; }

        /// <summary>
        /// 新状态
        /// </summary>
        public RobotWorkflowState NewState { get; set; }

        /// <summary>
        /// 状态变化消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 变化时间
        /// </summary>
        public DateTime ChangeTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 构造函数
        /// </summary>
        public RobotWorkflowStateChangedEventArgs() { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="robotId">机器人ID</param>
        /// <param name="oldState">旧状态</param>
        /// <param name="newState">新状态</param>
        /// <param name="message">消息</param>
        public RobotWorkflowStateChangedEventArgs(int robotId, RobotWorkflowState oldState, RobotWorkflowState newState, string message = "")
        {
            RobotId = robotId;
            OldState = oldState;
            NewState = newState;
            Message = message ?? string.Empty;
        }
    }

    /// <summary>
    /// 机器人工作流程完成事件参数
    /// </summary>
    public class RobotWorkflowCompletedEventArgs : EventArgs
    {
        /// <summary>
        /// 机器人ID
        /// </summary>
        public int RobotId { get; set; }

        /// <summary>
        /// 工作流程类型
        /// </summary>
        public string WorkflowType { get; set; } = string.Empty;

        /// <summary>
        /// 是否成功完成
        /// </summary>
        public bool IsSuccess { get; set; } = true;

        /// <summary>
        /// 完成消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 完成时间
        /// </summary>
        public DateTime CompletedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 处理耗时
        /// </summary>
        public TimeSpan ProcessingDuration { get; set; }

        /// <summary>
        /// 处理的数据
        /// </summary>
        public object ProcessedData { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public RobotWorkflowCompletedEventArgs() { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="robotId">机器人ID</param>
        /// <param name="workflowType">工作流程类型</param>
        /// <param name="isSuccess">是否成功</param>
        /// <param name="message">消息</param>
        public RobotWorkflowCompletedEventArgs(int robotId, string workflowType, bool isSuccess, string message = "")
        {
            RobotId = robotId;
            WorkflowType = workflowType ?? string.Empty;
            IsSuccess = isSuccess;
            Message = message ?? string.Empty;
        }
    }

    /// <summary>
    /// 自动模式错误事件参数
    /// </summary>
    public class AutoModeErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 异常详情
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// 错误类型
        /// </summary>
        public string ErrorType { get; set; } = string.Empty;

        /// <summary>
        /// 相关机器人ID（如果适用）
        /// </summary>
        public int? RobotId { get; set; }

        /// <summary>
        /// 当前控制器状态
        /// </summary>
        public EpsonAutoModeState CurrentState { get; set; }

        /// <summary>
        /// 错误发生时间
        /// </summary>
        public DateTime ErrorTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否可以自动恢复
        /// </summary>
        public bool CanAutoRecover { get; set; } = false;

        /// <summary>
        /// 恢复建议
        /// </summary>
        public string RecoveryAdvice { get; set; } = string.Empty;

        /// <summary>
        /// 构造函数
        /// </summary>
        public AutoModeErrorEventArgs() { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <param name="exception">异常</param>
        /// <param name="errorType">错误类型</param>
        public AutoModeErrorEventArgs(string errorMessage, Exception exception = null, string errorType = "")
        {
            ErrorMessage = errorMessage ?? string.Empty;
            Exception = exception;
            ErrorType = errorType ?? string.Empty;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <param name="robotId">机器人ID</param>
        /// <param name="currentState">当前状态</param>
        /// <param name="exception">异常</param>
        public AutoModeErrorEventArgs(string errorMessage, int robotId, EpsonAutoModeState currentState, Exception exception = null)
        {
            ErrorMessage = errorMessage ?? string.Empty;
            RobotId = robotId;
            CurrentState = currentState;
            Exception = exception;
        }
    }

    /// <summary>
    /// 响应发送事件参数
    /// </summary>
    public class ResponseSentEventArgs : EventArgs
    {
        /// <summary>
        /// 控制器响应
        /// </summary>
        public ControllerResponse Response { get; set; }

        /// <summary>
        /// 是否发送成功
        /// </summary>
        public bool IsSuccess { get; set; } = true;

        /// <summary>
        /// 发送结果消息
        /// </summary>
        public string ResultMessage { get; set; } = string.Empty;

        /// <summary>
        /// 发送时间
        /// </summary>
        public DateTime SentTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 构造函数
        /// </summary>
        public ResponseSentEventArgs() { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="response">控制器响应</param>
        /// <param name="isSuccess">是否成功</param>
        /// <param name="resultMessage">结果消息</param>
        public ResponseSentEventArgs(ControllerResponse response, bool isSuccess, string resultMessage = "")
        {
            Response = response;
            IsSuccess = isSuccess;
            ResultMessage = resultMessage ?? string.Empty;
        }
    }
}
