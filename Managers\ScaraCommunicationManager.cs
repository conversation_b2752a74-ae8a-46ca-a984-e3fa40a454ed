using System;
using System.Collections.Generic;
using MyHMI.Events;
using MyHMI.Helpers;

namespace MyHMI.Managers
{
    /// <summary>
    /// SCARA通信管理器
    /// 管理SCARA系统与本系统之间的通信字段
    /// 使用简化的线程安全机制，便于第三方开发者理解和使用
    /// </summary>
    public class ScaraCommunicationManager
    {
        #region 单例模式
        private static readonly Lazy<ScaraCommunicationManager> _instance =
            new Lazy<ScaraCommunicationManager>(() => new ScaraCommunicationManager());

        /// <summary>
        /// 获取ScaraCommunicationManager的单例实例
        /// </summary>
        public static ScaraCommunicationManager Instance => _instance.Value;

        /// <summary>
        /// 私有构造函数，防止外部实例化
        /// </summary>
        private ScaraCommunicationManager()
        {
            InitializeFields();
            LogHelper.Info("SCARA通信管理器已初始化");
        }
        #endregion

        #region 字段存储和锁
        private readonly Dictionary<string, bool> _fields = new Dictionary<string, bool>();
        private readonly object _lock = new object();
        #endregion

        #region 事件定义
        /// <summary>
        /// 字段值变化事件
        /// </summary>
        public event EventHandler<CommunicationEventArgs> FieldChanged;
        #endregion

        #region 通信字段属性

        /// <summary>
        /// 左电机是否准备完毕
        /// </summary>
        public bool L_moto_ready
        {
            get => GetField(nameof(L_moto_ready));
            set => SetField(nameof(L_moto_ready), value);
        }

        /// <summary>
        /// 右电机是否准备完毕
        /// </summary>
        public bool R_moto_ready
        {
            get => GetField(nameof(R_moto_ready));
            set => SetField(nameof(R_moto_ready), value);
        }

        /// <summary>
        /// 左位置是否放置到位
        /// </summary>
        public bool L_position_Arrived
        {
            get => GetField(nameof(L_position_Arrived));
            set => SetField(nameof(L_position_Arrived), value);
        }

        /// <summary>
        /// 右位置是否放置到位
        /// </summary>
        public bool R_position_Arrived
        {
            get => GetField(nameof(R_position_Arrived));
            set => SetField(nameof(R_position_Arrived), value);
        }

        /// <summary>
        /// 左夹爪是否夹紧
        /// </summary>
        public bool L_gripper_ok
        {
            get => GetField(nameof(L_gripper_ok));
            set => SetField(nameof(L_gripper_ok), value);
        }

        /// <summary>
        /// 右夹爪是否夹紧
        /// </summary>
        public bool R_gripper_ok
        {
            get => GetField(nameof(R_gripper_ok));
            set => SetField(nameof(R_gripper_ok), value);
        }

        /// <summary>
        /// 左角度矫正是否完成
        /// </summary>
        public bool L_Angle_ok
        {
            get => GetField(nameof(L_Angle_ok));
            set => SetField(nameof(L_Angle_ok), value);
        }

        /// <summary>
        /// 右角度矫正是否完成
        /// </summary>
        public bool R_Angle_ok
        {
            get => GetField(nameof(R_Angle_ok));
            set => SetField(nameof(R_Angle_ok), value);
        }

        /// <summary>
        /// 左安全距离是否确认
        /// </summary>
        public bool L_safe_ok
        {
            get => GetField(nameof(L_safe_ok));
            set => SetField(nameof(L_safe_ok), value);
        }

        /// <summary>
        /// 右安全距离是否确认
        /// </summary>
        public bool R_safe_ok
        {
            get => GetField(nameof(R_safe_ok));
            set => SetField(nameof(R_safe_ok), value);
        }

        /// <summary>
        /// 左数据获取是否完成
        /// </summary>
        public bool L_dataget_ok
        {
            get => GetField(nameof(L_dataget_ok));
            set => SetField(nameof(L_dataget_ok), value);
        }

        /// <summary>
        /// 右数据获取是否完成
        /// </summary>
        public bool R_dataget_ok
        {
            get => GetField(nameof(R_dataget_ok));
            set => SetField(nameof(R_dataget_ok), value);
        }

        /// <summary>
        /// 中间向左数据获取是否完成
        /// </summary>
        public bool ML_dataget_ok
        {
            get => GetField(nameof(ML_dataget_ok));
            set => SetField(nameof(ML_dataget_ok), value);
        }

        /// <summary>
        /// 中间向右数据获取是否完成
        /// </summary>
        public bool MR_dataget_ok
        {
            get => GetField(nameof(MR_dataget_ok));
            set => SetField(nameof(MR_dataget_ok), value);
        }

        /// <summary>
        /// 左电机是否完成
        /// </summary>
        public bool L_moto_finish
        {
            get => GetField(nameof(L_moto_finish));
            set => SetField(nameof(L_moto_finish), value);
        }

        /// <summary>
        /// 右电机是否完成
        /// </summary>
        public bool R_moto_finish
        {
            get => GetField(nameof(R_moto_finish));
            set => SetField(nameof(R_moto_finish), value);
        }

        /// <summary>
        /// 系统运行状态标志（文档中的all_save字段）
        /// true: 启动和运行时
        /// false: 触发停止时
        /// </summary>
        public bool all_save
        {
            get => GetField(nameof(all_save));
            set => SetField(nameof(all_save), value);
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化所有字段为false
        /// </summary>
        private void InitializeFields()
        {
            lock (_lock)
            {
                _fields[nameof(L_moto_ready)] = false;
                _fields[nameof(R_moto_ready)] = false;
                _fields[nameof(L_position_Arrived)] = false;
                _fields[nameof(R_position_Arrived)] = false;
                _fields[nameof(L_gripper_ok)] = false;
                _fields[nameof(R_gripper_ok)] = false;
                _fields[nameof(L_Angle_ok)] = false;
                _fields[nameof(R_Angle_ok)] = false;
                _fields[nameof(L_safe_ok)] = false;
                _fields[nameof(R_safe_ok)] = false;
                _fields[nameof(L_dataget_ok)] = false;
                _fields[nameof(R_dataget_ok)] = false;
                _fields[nameof(ML_dataget_ok)] = false;
                _fields[nameof(MR_dataget_ok)] = false;
                _fields[nameof(L_moto_finish)] = false;
                _fields[nameof(R_moto_finish)] = false;
                _fields[nameof(all_save)] = true; // 默认为运行状态
            }
        }

        /// <summary>
        /// 获取字段值
        /// </summary>
        /// <param name="fieldName">字段名称</param>
        /// <returns>字段值</returns>
        private bool GetField(string fieldName)
        {
            lock (_lock)
            {
                return _fields.ContainsKey(fieldName) ? _fields[fieldName] : false;
            }
        }

        /// <summary>
        /// 设置字段值
        /// </summary>
        /// <param name="fieldName">字段名称</param>
        /// <param name="value">字段值</param>
        private void SetField(string fieldName, bool value)
        {
            bool oldValue;
            lock (_lock)
            {
                oldValue = _fields.ContainsKey(fieldName) ? _fields[fieldName] : false;
                _fields[fieldName] = value;
            }

            // 如果值发生变化，触发事件
            if (oldValue != value)
            {
                OnFieldChanged(fieldName, oldValue, value);
            }
        }

        /// <summary>
        /// 触发字段变化事件
        /// </summary>
        /// <param name="fieldName">字段名称</param>
        /// <param name="oldValue">旧值</param>
        /// <param name="newValue">新值</param>
        private void OnFieldChanged(string fieldName, bool oldValue, bool newValue)
        {
            try
            {
                FieldChanged?.Invoke(this, new CommunicationEventArgs
                {
                    FieldName = fieldName,
                    OldValue = oldValue,
                    NewValue = newValue,
                    Timestamp = DateTime.Now
                });

                LogHelper.Debug($"SCARA通信字段变化: {fieldName} {oldValue} -> {newValue}");
            }
            catch (Exception ex)
            {
                LogHelper.Error($"触发字段变化事件时发生异常: {fieldName}", ex);
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 复位指定字段为false
        /// </summary>
        /// <param name="fieldName">字段名称</param>
        public void ResetField(string fieldName)
        {
            try
            {
                SetField(fieldName, false);
                LogHelper.Debug($"SCARA通信字段已复位: {fieldName}");
            }
            catch (Exception ex)
            {
                LogHelper.Error($"复位字段时发生异常: {fieldName}", ex);
            }
        }

        /// <summary>
        /// 复位所有字段为false
        /// </summary>
        public void ResetAllFields()
        {
            try
            {
                var changedFields = new List<(string fieldName, bool oldValue)>();

                lock (_lock)
                {
                    var fieldNames = new List<string>(_fields.Keys);
                    foreach (var fieldName in fieldNames)
                    {
                        bool oldValue = _fields[fieldName];
                        if (oldValue) // 只记录从true变为false的字段
                        {
                            changedFields.Add((fieldName, oldValue));
                        }
                        _fields[fieldName] = false;
                    }
                }

                // 在锁外触发事件，避免死锁
                foreach (var (fieldName, oldValue) in changedFields)
                {
                    OnFieldChanged(fieldName, oldValue, false);
                }

                LogHelper.Info($"所有SCARA通信字段已复位，{changedFields.Count}个字段发生变化");
            }
            catch (Exception ex)
            {
                LogHelper.Error("复位所有字段时发生异常", ex);
            }
        }

        /// <summary>
        /// 获取所有字段的当前状态
        /// </summary>
        /// <returns>字段状态字典</returns>
        public Dictionary<string, bool> GetAllFieldStates()
        {
            try
            {
                lock (_lock)
                {
                    return new Dictionary<string, bool>(_fields);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("获取所有字段状态时发生异常", ex);
                return new Dictionary<string, bool>();
            }
        }

        /// <summary>
        /// 获取通信状态（别名方法）
        /// </summary>
        /// <returns>字段状态字典</returns>
        public Dictionary<string, bool> GetStatus()
        {
            return GetAllFieldStates();
        }

        #endregion
    }
}