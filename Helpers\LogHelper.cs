using System;
using System.IO;
using System.Configuration;

namespace MyHMI.Helpers
{
    /// <summary>
    /// 日志管理辅助类
    /// 提供统一的日志记录功能，支持不同级别的日志输出
    /// </summary>
    public static class LogHelper
    {
        private static readonly object lockObject = new object();
        private static string logPath;
        private static string logLevel;

        /// <summary>
        /// 静态构造函数，初始化日志配置
        /// </summary>
        static LogHelper()
        {
            try
            {
                logPath = ConfigurationManager.AppSettings["LogPath"] ?? "Logs\\";
                logLevel = ConfigurationManager.AppSettings["LogLevel"] ?? "Info";
                
                // 确保日志目录存在
                if (!Directory.Exists(logPath))
                {
                    Directory.CreateDirectory(logPath);
                }
            }
            catch (Exception ex)
            {
                // 如果配置读取失败，使用默认值
                logPath = "Logs\\";
                logLevel = "Info";
                Console.WriteLine($"日志配置初始化失败，使用默认配置: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录信息级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public static void Info(string message)
        {
            WriteLog("INFO", message, null);
        }

        /// <summary>
        /// 记录警告级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public static void Warning(string message)
        {
            WriteLog("WARN", message, null);
        }

        /// <summary>
        /// 记录错误级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常对象</param>
        public static void Error(string message, Exception exception = null)
        {
            WriteLog("ERROR", message, exception);
        }

        /// <summary>
        /// 记录调试级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public static void Debug(string message)
        {
            if (logLevel == "Debug")
            {
                WriteLog("DEBUG", message, null);
            }
        }

        /// <summary>
        /// 写入日志到文件
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常对象</param>
        private static void WriteLog(string level, string message, Exception exception)
        {
            try
            {
                lock (lockObject)
                {
                    string fileName = $"Log_{DateTime.Now:yyyyMMdd}.txt";
                    string fullPath = Path.Combine(logPath, fileName);
                    
                    string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] [{level}] {message}";
                    
                    if (exception != null)
                    {
                        logEntry += $"\r\n异常详情: {exception.Message}";
                        logEntry += $"\r\n堆栈跟踪: {exception.StackTrace}";
                    }
                    
                    logEntry += "\r\n";
                    
                    File.AppendAllText(fullPath, logEntry);
                    
                    // 同时输出到控制台（调试时有用）
                    Console.WriteLine(logEntry.TrimEnd());
                }
            }
            catch (Exception ex)
            {
                // 日志写入失败时输出到控制台
                Console.WriteLine($"日志写入失败: {ex.Message}");
                Console.WriteLine($"原始日志: [{level}] {message}");
            }
        }

        /// <summary>
        /// 清理过期日志文件（保留最近30天）
        /// </summary>
        public static void CleanupOldLogs()
        {
            try
            {
                if (!Directory.Exists(logPath)) return;

                var files = Directory.GetFiles(logPath, "Log_*.txt");
                var cutoffDate = DateTime.Now.AddDays(-30);

                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(file);
                        Info($"删除过期日志文件: {fileInfo.Name}");
                    }
                }
            }
            catch (Exception ex)
            {
                Error("清理过期日志文件失败", ex);
            }
        }
    }
}
