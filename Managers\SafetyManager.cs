using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MyHMI.Events;
using MyHMI.Models;
using MyHMI.Helpers;

namespace MyHMI.Managers
{
    /// <summary>
    /// 安全管理器运行模式
    /// </summary>
    public enum SafetyManagerMode
    {
        /// <summary>
        /// 完整功能模式（所有依赖可用）
        /// </summary>
        Full,

        /// <summary>
        /// 基础功能模式（部分依赖不可用）
        /// </summary>
        Basic,

        /// <summary>
        /// 最小功能模式（仅基本安全检查）
        /// </summary>
        Minimal
    }

    /// <summary>
    /// 安全管理器
    /// 独立线程运行的自动模式安全控制管理类
    /// 负责I/O信号监控、紧急停止处理、启动控制、三色指示灯控制等功能
    /// 支持降级运行模式，即使部分依赖不可用也能提供基础安全功能
    /// </summary>
    public class SafetyManager
    {
        #region 单例模式
        private static readonly Lazy<SafetyManager> _instance = new Lazy<SafetyManager>(() => new SafetyManager());

        /// <summary>
        /// 获取SafetyManager的单例实例
        /// </summary>
        public static SafetyManager Instance => _instance.Value;

        /// <summary>
        /// 私有构造函数，防止外部实例化
        /// </summary>
        private SafetyManager()
        {
            _status = new SafetyManagerStatus();
            _configuration = LoadConfigurationFromSettings();
            InitializeIOSignalStates();
            LogHelper.Info("安全管理器实例已创建");
        }
        #endregion

        #region 私有字段
        private readonly SafetyManagerStatus _status;
        private readonly SafetyManagerConfiguration _configuration;
        private readonly object _lockObject = new object();
        
        // 线程管理
        private Task _monitoringTask;
        private CancellationTokenSource _cancellationTokenSource;
        
        // 依赖管理器
        private DMC1000BIOManager _ioManager;
        private ScaraCommunicationManager _communicationManager;
        
        // 当前状态缓存
        private SafetyState _currentSafetyState = SafetyState.Safe;
        private IndicatorLightState _currentIndicatorState = IndicatorLightState.Off;

        // 运行模式管理
        private SafetyManagerMode _currentMode = SafetyManagerMode.Full;
        #endregion

        #region 公共属性
        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => _status.IsInitialized;

        /// <summary>
        /// 是否正在运行
        /// </summary>
        public bool IsRunning => _status.IsRunning;

        /// <summary>
        /// 当前安全状态
        /// </summary>
        public SafetyState CurrentSafetyState => _currentSafetyState;

        /// <summary>
        /// 当前指示灯状态
        /// </summary>
        public IndicatorLightState CurrentIndicatorState => _currentIndicatorState;

        /// <summary>
        /// 当前运行模式
        /// </summary>
        public SafetyManagerMode CurrentMode => _currentMode;

        /// <summary>
        /// 获取系统是否处于安全状态
        /// </summary>
        public bool IsSystemSafe
        {
            get
            {
                lock (_lockObject)
                {
                    return _currentSafetyState == SafetyState.Safe;
                }
            }
        }

        /// <summary>
        /// 获取状态副本（线程安全）
        /// </summary>
        public SafetyManagerStatus GetStatus()
        {
            lock (_lockObject)
            {
                var statusCopy = new SafetyManagerStatus
                {
                    IsInitialized = _status.IsInitialized,
                    IsRunning = _status.IsRunning,
                    CurrentSafetyState = _status.CurrentSafetyState,
                    CurrentIndicatorState = _status.CurrentIndicatorState,
                    LastUpdateTime = _status.LastUpdateTime,
                    StartTime = _status.StartTime,
                    ErrorCount = _status.ErrorCount,
                    EmergencyStopCount = _status.EmergencyStopCount,
                    IOSignalStates = new Dictionary<string, IOSignalState>(_status.IOSignalStates)
                };
                return statusCopy;
            }
        }
        #endregion

        #region 事件定义
        /// <summary>
        /// 安全状态变化事件
        /// </summary>
        public event EventHandler<SafetyStateChangedEventArgs> SafetyStateChanged;

        /// <summary>
        /// I/O信号变化事件
        /// </summary>
        public event EventHandler<IOSignalChangedEventArgs> IOSignalChanged;

        /// <summary>
        /// 紧急停止事件
        /// </summary>
        public event EventHandler<EmergencyStopEventArgs> EmergencyStopTriggered;

        /// <summary>
        /// 指示灯状态变化事件
        /// </summary>
        public event EventHandler<IndicatorLightChangedEventArgs> IndicatorLightChanged;

        /// <summary>
        /// 安全管理器错误事件
        /// </summary>
        public event EventHandler<SafetyManagerErrorEventArgs> SafetyManagerError;
        #endregion

        #region 初始化和释放
        /// <summary>
        /// 初始化安全管理器
        /// </summary>
        /// <returns>是否初始化成功</returns>
        public async Task<bool> InitializeAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                lock (_lockObject)
                {
                    if (_status.IsInitialized)
                    {
                        LogHelper.Info("安全管理器已经初始化");
                        return true;
                    }
                }

                LogHelper.Info("开始初始化安全管理器...");

                // 获取依赖管理器实例
                _ioManager = DMC1000BIOManager.Instance;
                _communicationManager = ScaraCommunicationManager.Instance;

                // 检查依赖管理器是否可用（支持降级运行）
                bool hasIOManager = _ioManager != null && _ioManager.IsInitialized;
                bool hasCommunicationManager = _communicationManager != null;

                if (!hasIOManager)
                {
                    LogHelper.Warning("DMC1000BIOManager不可用，安全管理器将在降级模式下运行");
                }

                if (!hasCommunicationManager)
                {
                    LogHelper.Warning("ScaraCommunicationManager不可用，安全管理器将在降级模式下运行");
                }

                // 根据依赖可用性确定运行模式
                SafetyManagerMode mode = DetermineOperationMode(hasIOManager, hasCommunicationManager);

                // 即使部分依赖不可用，也继续初始化基础功能
                // 初始化I/O信号状态
                InitializeIOSignalStates();

                // 设置初始指示灯状态（所有灯关闭）
                await SetIndicatorLightAsync(IndicatorLightState.Off, "初始化");

                lock (_lockObject)
                {
                    _status.IsInitialized = true;
                    _status.CurrentSafetyState = SafetyState.Safe;
                    _status.CurrentIndicatorState = IndicatorLightState.Off;
                    _status.UpdateTimestamp();
                    _currentMode = mode; // 设置运行模式
                }

                LogHelper.Info($"安全管理器初始化完成 - 运行模式: {mode}");
                return true;

            }, false, "初始化安全管理器");
        }

        /// <summary>
        /// 启动安全管理器监控
        /// </summary>
        /// <returns>是否启动成功</returns>
        public async Task<bool> StartAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                lock (_lockObject)
                {
                    if (!_status.IsInitialized)
                    {
                        LogHelper.Warning("安全管理器未初始化，尝试先进行初始化...");
                        // 不立即返回false，而是尝试初始化
                    }

                    if (_status.IsRunning)
                    {
                        LogHelper.Info("安全管理器已经在运行中");
                        return true;
                    }
                }

                // 如果未初始化，尝试先初始化
                if (!_status.IsInitialized)
                {
                    bool initResult = await InitializeAsync();
                    if (!initResult)
                    {
                        LogHelper.Warning("安全管理器初始化失败，但将尝试在最小模式下启动");
                        // 继续尝试启动，但功能受限
                    }
                }

                LogHelper.Info("启动安全管理器监控...");

                // 创建取消令牌
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = new CancellationTokenSource();

                // 设置初始安全状态
                await ChangeSafetyStateAsync(SafetyState.Safe, "安全管理器启动");

                // 设置黄灯（调试模式停止状态）
                await SetIndicatorLightAsync(IndicatorLightState.Yellow, "安全管理器启动");

                // 启动监控任务
                _monitoringTask = Task.Run(async () => await MonitoringLoopAsync(_cancellationTokenSource.Token));

                lock (_lockObject)
                {
                    _status.IsRunning = true;
                    _status.StartTime = DateTime.Now;
                    _status.UpdateTimestamp();
                }

                // 设置通信管理器的运行状态为true（启动和运行时）
                _communicationManager.all_save = true;

                LogHelper.Info("安全管理器监控已启动");
                return true;

            }, false, "启动安全管理器");
        }

        /// <summary>
        /// 停止安全管理器监控
        /// </summary>
        /// <returns>是否停止成功</returns>
        public async Task<bool> StopAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                lock (_lockObject)
                {
                    if (!_status.IsRunning)
                    {
                        LogHelper.Info("安全管理器未在运行中");
                        return true;
                    }
                }

                LogHelper.Info("停止安全管理器监控...");

                // 取消监控任务
                _cancellationTokenSource?.Cancel();

                // 等待监控任务完成
                if (_monitoringTask != null && !_monitoringTask.IsCompleted)
                {
                    try
                    {
                        await _monitoringTask;
                    }
                    catch (OperationCanceledException)
                    {
                        // 正常的取消操作
                    }
                }

                // 设置安全状态为程序停止
                await ChangeSafetyStateAsync(SafetyState.ProgramStopped, "安全管理器停止");

                // 关闭所有指示灯
                await SetIndicatorLightAsync(IndicatorLightState.Off, "安全管理器停止");

                // 设置通信管理器的运行状态为false（触发停止时）
                _communicationManager.all_save = false;

                lock (_lockObject)
                {
                    _status.IsRunning = false;
                    _status.UpdateTimestamp();
                }

                LogHelper.Info("安全管理器监控已停止");
                return true;

            }, false, "停止安全管理器");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                // 停止监控
                StopAsync().Wait(5000);

                // 释放取消令牌
                _cancellationTokenSource?.Dispose();

                LogHelper.Info("安全管理器资源已释放");
            }
            catch (Exception ex)
            {
                LogHelper.Error("释放安全管理器资源时发生异常", ex);
            }
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 根据依赖可用性确定运行模式
        /// </summary>
        private SafetyManagerMode DetermineOperationMode(bool hasIOManager, bool hasCommunicationManager)
        {
            if (hasIOManager && hasCommunicationManager)
            {
                LogHelper.Info("所有依赖可用，安全管理器将在完整功能模式下运行");
                return SafetyManagerMode.Full;
            }
            else if (hasIOManager || hasCommunicationManager)
            {
                LogHelper.Warning("部分依赖不可用，安全管理器将在基础功能模式下运行");
                return SafetyManagerMode.Basic;
            }
            else
            {
                LogHelper.Warning("主要依赖都不可用，安全管理器将在最小功能模式下运行");
                return SafetyManagerMode.Minimal;
            }
        }

        /// <summary>
        /// 从Settings系统加载配置
        /// </summary>
        private SafetyManagerConfiguration LoadConfigurationFromSettings()
        {
            try
            {
                var systemSettings = Settings.Settings.Current.System;
                var config = new SafetyManagerConfiguration
                {
                    MonitoringIntervalMs = systemSettings.SafetyMonitoringInterval,
                    DebounceTimeMs = systemSettings.SafetyDebounceTime,
                    EnableVerboseLogging = systemSettings.SafetyVerboseLogging,
                    EmergencyStopRecoveryDelayMs = systemSettings.SafetyEmergencyRecoveryDelay,
                    IndicatorLightSwitchDelayMs = systemSettings.SafetyIndicatorSwitchDelay,
                    MaxErrorRetryCount = systemSettings.SafetyMaxErrorRetryCount
                };

                LogHelper.Info("从Settings系统加载安全管理器配置成功");
                return config;
            }
            catch (Exception ex)
            {
                LogHelper.Warning($"从Settings系统加载安全管理器配置失败，使用默认配置: {ex.Message}");
                return new SafetyManagerConfiguration();
            }
        }

        /// <summary>
        /// 初始化I/O信号状态
        /// </summary>
        private void InitializeIOSignalStates()
        {
            lock (_lockObject)
            {
                _status.IOSignalStates.Clear();

                foreach (var mapping in _configuration.IOSignalMapping)
                {
                    var signalState = new IOSignalState(mapping.Value, mapping.Key);
                    _status.IOSignalStates[mapping.Value] = signalState;
                }

                LogHelper.Debug($"已初始化{_status.IOSignalStates.Count}个I/O信号状态");
            }
        }

        /// <summary>
        /// 监控循环（在独立线程中运行）
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        private async Task MonitoringLoopAsync(CancellationToken cancellationToken)
        {
            LogHelper.Info("安全管理器监控循环已启动");

            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    try
                    {
                        // 监控所有I/O信号
                        await MonitorIOSignalsAsync();

                        // 更新状态时间戳
                        lock (_lockObject)
                        {
                            _status.UpdateTimestamp();
                        }

                        // 等待下一次监控周期
                        await Task.Delay(_configuration.MonitoringIntervalMs, cancellationToken);
                    }
                    catch (OperationCanceledException)
                    {
                        // 正常的取消操作
                        break;
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error("安全管理器监控循环发生异常", ex);

                        // 触发错误事件
                        OnSafetyManagerError("MonitoringLoop", ex.Message, "", true, ex);

                        // 增加错误计数
                        lock (_lockObject)
                        {
                            _status.ErrorCount++;
                        }

                        // 短暂延迟后继续监控
                        await Task.Delay(1000, cancellationToken);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // 正常的取消操作
            }
            catch (Exception ex)
            {
                LogHelper.Error("安全管理器监控循环发生严重异常", ex);
                OnSafetyManagerError("MonitoringLoop", ex.Message, "", true, ex);
            }

            LogHelper.Info("安全管理器监控循环已停止");
        }

        /// <summary>
        /// 监控所有I/O信号
        /// </summary>
        private async Task MonitorIOSignalsAsync()
        {
            var signalStates = new Dictionary<string, IOSignalState>();

            // 获取当前信号状态的副本
            lock (_lockObject)
            {
                foreach (var kvp in _status.IOSignalStates)
                {
                    signalStates[kvp.Key] = kvp.Value;
                }
            }

            // 读取所有I/O信号的当前值
            foreach (var kvp in signalStates)
            {
                string ioNumber = kvp.Key;
                IOSignalState signalState = kvp.Value;

                try
                {
                    // 读取I/O信号值
                    bool currentValue = await _ioManager.ReadInputAsync(ioNumber);

                    // 更新信号状态（包含防抖动处理）
                    bool valueChanged = signalState.UpdateValue(currentValue, _configuration.DebounceTimeMs);

                    if (valueChanged)
                    {
                        // 信号值发生稳定变化，处理信号变化
                        await ProcessIOSignalChangeAsync(signalState);
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"读取I/O信号 {ioNumber} 时发生异常", ex);
                    OnSafetyManagerError("IOSignalRead", ex.Message, ioNumber, false, ex);
                }
            }
        }

        /// <summary>
        /// 处理I/O信号变化
        /// </summary>
        /// <param name="signalState">信号状态</param>
        private async Task ProcessIOSignalChangeAsync(IOSignalState signalState)
        {
            try
            {
                // 检测边沿触发
                var edgeResult = signalState.DetectEdge();
                bool isEdgeTriggered = edgeResult != null;

                // 触发I/O信号变化事件
                OnIOSignalChanged(signalState.IONumber, signalState.SignalType,
                    signalState.PreviousValue, signalState.CurrentValue,
                    isEdgeTriggered, edgeResult?.IsRisingEdge ?? false);

                // 根据信号类型处理不同的逻辑
                switch (signalState.SignalType)
                {
                    case IOSignalType.StartSignal:
                        if (isEdgeTriggered)
                        {
                            await HandleStartSignalAsync();
                        }
                        break;

                    case IOSignalType.StopSignal:
                        if (isEdgeTriggered)
                        {
                            await HandleStopSignalAsync();
                        }
                        break;

                    case IOSignalType.SafetyLockSignal:
                        await HandleSafetyLockSignalAsync(signalState.CurrentValue);
                        break;

                    case IOSignalType.SafetyDoorSignal:
                    case IOSignalType.LightCurtain1Signal:
                    case IOSignalType.LightCurtain2Signal:
                        await HandleEmergencyStopSignalAsync(signalState);
                        break;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"处理I/O信号变化时发生异常: {signalState.IONumber}", ex);
                OnSafetyManagerError("ProcessIOSignalChange", ex.Message, signalState.IONumber, false, ex);
            }
        }

        /// <summary>
        /// 改变安全状态
        /// </summary>
        /// <param name="newState">新的安全状态</param>
        /// <param name="reason">变化原因</param>
        /// <param name="triggerIOSignal">触发的I/O信号</param>
        private async Task ChangeSafetyStateAsync(SafetyState newState, string reason, string triggerIOSignal = "")
        {
            SafetyState oldState;

            lock (_lockObject)
            {
                oldState = _currentSafetyState;
                if (oldState == newState)
                {
                    return; // 状态没有变化
                }

                _currentSafetyState = newState;
                _status.CurrentSafetyState = newState;
                _status.UpdateTimestamp();
            }

            // 根据新状态更新通信管理器的运行标志
            bool isRunning = (newState == SafetyState.Safe || newState == SafetyState.Starting);
            _communicationManager.all_save = isRunning;

            // 根据安全状态设置相应的指示灯
            IndicatorLightState newIndicatorState = GetIndicatorStateForSafetyState(newState);
            await SetIndicatorLightAsync(newIndicatorState, $"安全状态变化: {reason}");

            // 触发安全状态变化事件
            OnSafetyStateChanged(oldState, newState, reason, triggerIOSignal);

            LogHelper.Info($"安全状态变化: {oldState} -> {newState}, 原因: {reason}");
        }

        /// <summary>
        /// 根据安全状态获取对应的指示灯状态
        /// </summary>
        /// <param name="safetyState">安全状态</param>
        /// <returns>指示灯状态</returns>
        private IndicatorLightState GetIndicatorStateForSafetyState(SafetyState safetyState)
        {
            switch (safetyState)
            {
                case SafetyState.Safe:
                case SafetyState.Starting:
                    return IndicatorLightState.Green; // 绿灯 - 正常运行

                case SafetyState.EmergencyStop:
                case SafetyState.SafetyLocked:
                case SafetyState.SystemError:
                    return IndicatorLightState.Red; // 红灯 - 错误状态

                case SafetyState.ProgramStopped:
                default:
                    return IndicatorLightState.Yellow; // 黄灯 - 调试模式停止状态
            }
        }

        /// <summary>
        /// 设置指示灯状态
        /// </summary>
        /// <param name="newState">新的指示灯状态</param>
        /// <param name="reason">变化原因</param>
        private async Task SetIndicatorLightAsync(IndicatorLightState newState, string reason)
        {
            IndicatorLightState oldState;

            lock (_lockObject)
            {
                oldState = _currentIndicatorState;
                if (oldState == newState)
                {
                    return; // 状态没有变化
                }

                _currentIndicatorState = newState;
                _status.CurrentIndicatorState = newState;
            }

            try
            {
                // 检查IO管理器是否可用
                if (_ioManager == null || !_ioManager.IsInitialized)
                {
                    LogHelper.Warning($"IO管理器不可用，无法设置指示灯状态: {newState}，原因: {reason}");
                    // 仍然触发事件，让其他组件知道状态变化
                    OnIndicatorLightChanged(oldState, newState, reason);
                    return;
                }

                // 先关闭所有指示灯
                await _ioManager.SetOutputAsync("O0008", false); // 红灯
                await _ioManager.SetOutputAsync("O0009", false); // 绿灯
                await _ioManager.SetOutputAsync("O0010", false); // 黄灯

                // 短暂延迟确保状态切换
                await Task.Delay(_configuration.IndicatorLightSwitchDelayMs);

                // 根据新状态点亮相应的指示灯
                switch (newState)
                {
                    case IndicatorLightState.Red:
                        await _ioManager.SetOutputAsync("O0008", true); // 红灯
                        break;
                    case IndicatorLightState.Green:
                        await _ioManager.SetOutputAsync("O0009", true); // 绿灯
                        break;
                    case IndicatorLightState.Yellow:
                        await _ioManager.SetOutputAsync("O0010", true); // 黄灯
                        break;
                    case IndicatorLightState.Off:
                        // 所有灯都已关闭
                        break;
                }

                // 触发指示灯状态变化事件
                OnIndicatorLightChanged(oldState, newState, reason);

                LogHelper.Debug($"指示灯状态变化: {oldState} -> {newState}, 原因: {reason}");
            }
            catch (Exception ex)
            {
                LogHelper.Error($"设置指示灯状态时发生异常: {newState}", ex);
                OnSafetyManagerError("SetIndicatorLight", ex.Message, "", false, ex);
            }
        }

        /// <summary>
        /// 处理启动信号
        /// </summary>
        private async Task HandleStartSignalAsync()
        {
            LogHelper.Info("检测到启动信号边沿触发");

            // 检查当前是否可以启动
            if (_currentSafetyState == SafetyState.SafetyLocked)
            {
                LogHelper.Warning("系统处于安全锁定状态，无法启动");
                return;
            }

            // 检查紧急停止条件
            if (await CheckEmergencyStopConditionsAsync())
            {
                LogHelper.Warning("存在紧急停止条件，无法启动");
                return;
            }

            // 设置启动状态
            await ChangeSafetyStateAsync(SafetyState.Starting, "启动信号触发", "I0001");

            // 短暂延迟后设置为安全状态
            await Task.Delay(500);
            await ChangeSafetyStateAsync(SafetyState.Safe, "启动完成", "I0001");

            LogHelper.Info("系统启动完成");
        }

        /// <summary>
        /// 处理停止信号
        /// </summary>
        private async Task HandleStopSignalAsync()
        {
            LogHelper.Info("检测到停止信号边沿触发");

            // 设置程序停止状态
            await ChangeSafetyStateAsync(SafetyState.ProgramStopped, "停止信号触发", "I0002");

            LogHelper.Info("程序已停止");
        }

        /// <summary>
        /// 处理安全锁定信号
        /// </summary>
        /// <param name="isLocked">是否锁定</param>
        private async Task HandleSafetyLockSignalAsync(bool isLocked)
        {
            if (isLocked)
            {
                LogHelper.Warning("检测到安全锁定信号");
                await ChangeSafetyStateAsync(SafetyState.SafetyLocked, "安全锁定信号激活", "I0003");
            }
            else
            {
                LogHelper.Info("安全锁定信号解除");
                // 锁定解除后，设置为程序停止状态，等待启动信号
                await ChangeSafetyStateAsync(SafetyState.ProgramStopped, "安全锁定信号解除", "I0003");
            }
        }

        /// <summary>
        /// 处理紧急停止信号
        /// </summary>
        /// <param name="signalState">信号状态</param>
        private async Task HandleEmergencyStopSignalAsync(IOSignalState signalState)
        {
            // 根据信号类型确定紧急停止条件
            bool shouldTriggerEmergencyStop = false;
            string reason = "";

            switch (signalState.SignalType)
            {
                case IOSignalType.SafetyDoorSignal:
                    // 安全门信号：信号为0时触发紧急停止
                    shouldTriggerEmergencyStop = !signalState.CurrentValue;
                    reason = "安全门开启";
                    break;

                case IOSignalType.LightCurtain1Signal:
                case IOSignalType.LightCurtain2Signal:
                    // 光栅信号：信号为1时触发紧急停止
                    shouldTriggerEmergencyStop = signalState.CurrentValue;
                    reason = signalState.SignalType == IOSignalType.LightCurtain1Signal ? "光栅1触发" : "光栅2触发";
                    break;
            }

            if (shouldTriggerEmergencyStop && _currentSafetyState != SafetyState.EmergencyStop)
            {
                LogHelper.Warning($"触发紧急停止: {reason}");

                // 增加紧急停止计数
                lock (_lockObject)
                {
                    _status.EmergencyStopCount++;
                }

                // 触发紧急停止事件
                OnEmergencyStopTriggered(signalState.IONumber, reason, true);

                // 设置紧急停止状态
                await ChangeSafetyStateAsync(SafetyState.EmergencyStop, reason, signalState.IONumber);
            }
            else if (!shouldTriggerEmergencyStop && _currentSafetyState == SafetyState.EmergencyStop)
            {
                LogHelper.Info($"紧急停止条件解除: {reason}");

                // 紧急停止条件解除，但需要等待启动信号才能恢复
                await Task.Delay(_configuration.EmergencyStopRecoveryDelayMs);
                await ChangeSafetyStateAsync(SafetyState.ProgramStopped, $"紧急停止条件解除: {reason}", signalState.IONumber);
            }
        }

        /// <summary>
        /// 检查紧急停止条件
        /// </summary>
        /// <returns>是否存在紧急停止条件</returns>
        private async Task<bool> CheckEmergencyStopConditionsAsync()
        {
            try
            {
                // 检查安全门 (I0101) - 信号为0时为紧急停止条件
                bool safetyDoor = await _ioManager.ReadInputAsync("I0101");
                if (!safetyDoor)
                {
                    LogHelper.Debug("紧急停止条件: 安全门开启");
                    return true;
                }

                // 检查光栅1 (I0102) - 信号为1时为紧急停止条件
                bool lightCurtain1 = await _ioManager.ReadInputAsync("I0102");
                if (lightCurtain1)
                {
                    LogHelper.Debug("紧急停止条件: 光栅1触发");
                    return true;
                }

                // 检查光栅2 (I0103) - 信号为1时为紧急停止条件
                bool lightCurtain2 = await _ioManager.ReadInputAsync("I0103");
                if (lightCurtain2)
                {
                    LogHelper.Debug("紧急停止条件: 光栅2触发");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LogHelper.Error("检查紧急停止条件时发生异常", ex);
                OnSafetyManagerError("CheckEmergencyStopConditions", ex.Message, "", false, ex);
                return true; // 发生异常时，为安全起见，认为存在紧急停止条件
            }
        }
        #endregion

        #region 公共方法 - I/O信号监控
        /// <summary>
        /// 获取指定I/O信号的当前状态
        /// </summary>
        /// <param name="ioNumber">I/O编号</param>
        /// <returns>信号状态，如果不存在返回null</returns>
        public IOSignalState GetIOSignalState(string ioNumber)
        {
            lock (_lockObject)
            {
                return _status.GetIOSignalState(ioNumber);
            }
        }

        /// <summary>
        /// 获取所有I/O信号的当前状态
        /// </summary>
        /// <returns>I/O信号状态字典</returns>
        public Dictionary<string, IOSignalState> GetAllIOSignalStates()
        {
            lock (_lockObject)
            {
                return new Dictionary<string, IOSignalState>(_status.IOSignalStates);
            }
        }

        /// <summary>
        /// 手动读取指定I/O信号的实时值
        /// </summary>
        /// <param name="ioNumber">I/O编号</param>
        /// <returns>信号值</returns>
        public async Task<bool> ReadIOSignalAsync(string ioNumber)
        {
            try
            {
                return await _ioManager.ReadInputAsync(ioNumber);
            }
            catch (Exception ex)
            {
                LogHelper.Error($"手动读取I/O信号 {ioNumber} 时发生异常", ex);
                OnSafetyManagerError("ManualIORead", ex.Message, ioNumber, false, ex);
                return false;
            }
        }

        /// <summary>
        /// 获取指定I/O信号的边沿检测历史
        /// </summary>
        /// <param name="ioNumber">I/O编号</param>
        /// <returns>边沿检测记录列表</returns>
        public List<EdgeDetectionRecord> GetIOSignalEdgeHistory(string ioNumber)
        {
            lock (_lockObject)
            {
                var signalState = _status.GetIOSignalState(ioNumber);
                return signalState?.EdgeHistory != null ? new List<EdgeDetectionRecord>(signalState.EdgeHistory) : new List<EdgeDetectionRecord>();
            }
        }

        /// <summary>
        /// 清除指定I/O信号的边沿检测历史
        /// </summary>
        /// <param name="ioNumber">I/O编号</param>
        public void ClearIOSignalEdgeHistory(string ioNumber)
        {
            lock (_lockObject)
            {
                var signalState = _status.GetIOSignalState(ioNumber);
                signalState?.EdgeHistory?.Clear();
            }
            LogHelper.Debug($"已清除I/O信号 {ioNumber} 的边沿检测历史");
        }

        /// <summary>
        /// 清除所有I/O信号的边沿检测历史
        /// </summary>
        public void ClearAllIOSignalEdgeHistory()
        {
            lock (_lockObject)
            {
                foreach (var signalState in _status.IOSignalStates.Values)
                {
                    signalState.EdgeHistory?.Clear();
                }
            }
            LogHelper.Debug("已清除所有I/O信号的边沿检测历史");
        }

        /// <summary>
        /// 强制更新指定I/O信号状态（用于测试）
        /// </summary>
        /// <param name="ioNumber">I/O编号</param>
        /// <param name="value">信号值</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> ForceUpdateIOSignalAsync(string ioNumber, bool value)
        {
            try
            {
                IOSignalState signalState;
                lock (_lockObject)
                {
                    signalState = _status.GetIOSignalState(ioNumber);
                }

                if (signalState == null)
                {
                    LogHelper.Warning($"未找到I/O信号状态: {ioNumber}");
                    return false;
                }

                // 强制更新信号值（跳过防抖动）
                bool valueChanged = signalState.UpdateValue(value, 0);

                if (valueChanged)
                {
                    await ProcessIOSignalChangeAsync(signalState);
                    LogHelper.Debug($"强制更新I/O信号 {ioNumber} 为 {value}");
                }

                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"强制更新I/O信号 {ioNumber} 时发生异常", ex);
                OnSafetyManagerError("ForceUpdateIOSignal", ex.Message, ioNumber, false, ex);
                return false;
            }
        }

        /// <summary>
        /// 获取I/O信号监控统计信息
        /// </summary>
        /// <returns>监控统计信息</returns>
        public IOMonitoringStatistics GetIOMonitoringStatistics()
        {
            lock (_lockObject)
            {
                var stats = new IOMonitoringStatistics
                {
                    TotalSignalsMonitored = _status.IOSignalStates.Count,
                    ActiveSignalsCount = _status.IOSignalStates.Values.Count(s => s.CurrentValue),
                    TotalEdgeDetections = _status.IOSignalStates.Values.Sum(s => s.EdgeHistory?.Count ?? 0),
                    LastUpdateTime = _status.LastUpdateTime,
                    MonitoringInterval = _configuration.MonitoringIntervalMs,
                    DebounceTime = _configuration.DebounceTimeMs,
                    ErrorCount = _status.ErrorCount,
                    EmergencyStopCount = _status.EmergencyStopCount
                };

                // 计算每个信号的统计信息
                stats.SignalStatistics = new Dictionary<string, IOSignalStatistics>();
                foreach (var kvp in _status.IOSignalStates)
                {
                    var signalStat = new IOSignalStatistics
                    {
                        IONumber = kvp.Key,
                        SignalType = kvp.Value.SignalType,
                        CurrentValue = kvp.Value.CurrentValue,
                        IsStable = kvp.Value.IsStable,
                        LastUpdateTime = kvp.Value.LastUpdateTime,
                        EdgeDetectionCount = kvp.Value.EdgeHistory?.Count ?? 0,
                        LastEdgeTime = kvp.Value.EdgeHistory?.LastOrDefault()?.Timestamp ?? DateTime.MinValue
                    };
                    stats.SignalStatistics[kvp.Key] = signalStat;
                }

                return stats;
            }
        }
        #endregion

        #region 公共方法 - 紧急停止处理
        /// <summary>
        /// 手动触发紧急停止
        /// </summary>
        /// <param name="reason">触发原因</param>
        /// <param name="triggerSource">触发源</param>
        /// <returns>是否成功触发</returns>
        public async Task<bool> TriggerEmergencyStopAsync(string reason, string triggerSource = "Manual")
        {
            try
            {
                LogHelper.Warning($"手动触发紧急停止: {reason}");

                // 增加紧急停止计数
                lock (_lockObject)
                {
                    _status.EmergencyStopCount++;
                }

                // 触发紧急停止事件
                OnEmergencyStopTriggered(triggerSource, reason, true);

                // 设置紧急停止状态
                await ChangeSafetyStateAsync(SafetyState.EmergencyStop, reason, triggerSource);

                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("手动触发紧急停止时发生异常", ex);
                OnSafetyManagerError("ManualEmergencyStop", ex.Message, triggerSource, true, ex);
                return false;
            }
        }

        /// <summary>
        /// 检查是否可以从紧急停止状态恢复
        /// </summary>
        /// <returns>是否可以恢复</returns>
        public async Task<bool> CanRecoverFromEmergencyStopAsync()
        {
            try
            {
                if (_currentSafetyState != SafetyState.EmergencyStop)
                {
                    return true; // 不在紧急停止状态
                }

                // 检查所有紧急停止条件是否已解除
                bool emergencyConditionsExist = await CheckEmergencyStopConditionsAsync();

                if (emergencyConditionsExist)
                {
                    LogHelper.Debug("紧急停止条件仍然存在，无法恢复");
                    return false;
                }

                // 检查安全锁定状态
                bool safetyLocked = await _ioManager.ReadInputAsync("I0003");
                if (safetyLocked)
                {
                    LogHelper.Debug("系统处于安全锁定状态，无法恢复");
                    return false;
                }

                LogHelper.Info("紧急停止条件已解除，可以恢复");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("检查紧急停止恢复条件时发生异常", ex);
                OnSafetyManagerError("CheckEmergencyStopRecovery", ex.Message, "", false, ex);
                return false;
            }
        }

        /// <summary>
        /// 尝试从紧急停止状态恢复
        /// </summary>
        /// <returns>是否成功恢复</returns>
        public async Task<bool> RecoverFromEmergencyStopAsync()
        {
            try
            {
                if (_currentSafetyState != SafetyState.EmergencyStop)
                {
                    LogHelper.Info("系统不在紧急停止状态，无需恢复");
                    return true;
                }

                // 检查是否可以恢复
                bool canRecover = await CanRecoverFromEmergencyStopAsync();
                if (!canRecover)
                {
                    LogHelper.Warning("紧急停止恢复条件不满足");
                    return false;
                }

                LogHelper.Info("开始从紧急停止状态恢复...");

                // 等待恢复延迟时间
                await Task.Delay(_configuration.EmergencyStopRecoveryDelayMs);

                // 设置为程序停止状态，等待启动信号
                await ChangeSafetyStateAsync(SafetyState.ProgramStopped, "紧急停止恢复", "System");

                LogHelper.Info("已从紧急停止状态恢复到程序停止状态");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("从紧急停止状态恢复时发生异常", ex);
                OnSafetyManagerError("EmergencyStopRecovery", ex.Message, "", true, ex);
                return false;
            }
        }

        /// <summary>
        /// 获取当前紧急停止条件详情
        /// </summary>
        /// <returns>紧急停止条件详情</returns>
        public async Task<EmergencyStopConditions> GetEmergencyStopConditionsAsync()
        {
            var conditions = new EmergencyStopConditions();

            try
            {
                // 检查安全门 (I0101)
                conditions.SafetyDoorOpen = !await _ioManager.ReadInputAsync("I0101");

                // 检查光栅1 (I0102)
                conditions.LightCurtain1Triggered = await _ioManager.ReadInputAsync("I0102");

                // 检查光栅2 (I0103)
                conditions.LightCurtain2Triggered = await _ioManager.ReadInputAsync("I0103");

                // 检查安全锁定 (I0003)
                conditions.SafetyLocked = await _ioManager.ReadInputAsync("I0003");

                conditions.HasAnyEmergencyCondition = conditions.SafetyDoorOpen ||
                                                    conditions.LightCurtain1Triggered ||
                                                    conditions.LightCurtain2Triggered;

                conditions.CheckTime = DateTime.Now;

                LogHelper.Debug($"紧急停止条件检查: 安全门={conditions.SafetyDoorOpen}, " +
                              $"光栅1={conditions.LightCurtain1Triggered}, " +
                              $"光栅2={conditions.LightCurtain2Triggered}, " +
                              $"安全锁定={conditions.SafetyLocked}");
            }
            catch (Exception ex)
            {
                LogHelper.Error("获取紧急停止条件详情时发生异常", ex);
                OnSafetyManagerError("GetEmergencyStopConditions", ex.Message, "", false, ex);

                // 发生异常时，为安全起见，设置所有条件为true
                conditions.SafetyDoorOpen = true;
                conditions.LightCurtain1Triggered = true;
                conditions.LightCurtain2Triggered = true;
                conditions.SafetyLocked = true;
                conditions.HasAnyEmergencyCondition = true;
            }

            return conditions;
        }

        /// <summary>
        /// 重置紧急停止计数器
        /// </summary>
        public void ResetEmergencyStopCounter()
        {
            lock (_lockObject)
            {
                _status.EmergencyStopCount = 0;
            }
            LogHelper.Info("紧急停止计数器已重置");
        }
        #endregion

        #region 公共方法 - 启动和停止信号处理
        /// <summary>
        /// 手动触发启动信号处理
        /// </summary>
        /// <returns>是否成功处理</returns>
        public async Task<bool> TriggerStartSignalAsync()
        {
            try
            {
                LogHelper.Info("手动触发启动信号处理");
                await HandleStartSignalAsync();
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("手动触发启动信号处理时发生异常", ex);
                OnSafetyManagerError("ManualStartSignal", ex.Message, "I0001", false, ex);
                return false;
            }
        }

        /// <summary>
        /// 手动触发停止信号处理
        /// </summary>
        /// <returns>是否成功处理</returns>
        public async Task<bool> TriggerStopSignalAsync()
        {
            try
            {
                LogHelper.Info("手动触发停止信号处理");
                await HandleStopSignalAsync();
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("手动触发停止信号处理时发生异常", ex);
                OnSafetyManagerError("ManualStopSignal", ex.Message, "I0002", false, ex);
                return false;
            }
        }

        /// <summary>
        /// 执行程序复位
        /// </summary>
        /// <returns>是否成功复位</returns>
        public async Task<bool> ResetProgramAsync()
        {
            try
            {
                LogHelper.Info("开始执行程序复位...");

                // 检查当前状态是否允许复位
                if (_currentSafetyState == SafetyState.SafetyLocked)
                {
                    LogHelper.Warning("系统处于安全锁定状态，无法复位");
                    return false;
                }

                // 检查紧急停止条件
                if (await CheckEmergencyStopConditionsAsync())
                {
                    LogHelper.Warning("存在紧急停止条件，无法复位");
                    return false;
                }

                // 复位通信管理器的所有字段
                _communicationManager.ResetAllFields();

                // 清除所有I/O信号的边沿检测历史
                ClearAllIOSignalEdgeHistory();

                // 重置错误计数
                lock (_lockObject)
                {
                    _status.ErrorCount = 0;
                }

                // 设置为程序停止状态
                await ChangeSafetyStateAsync(SafetyState.ProgramStopped, "程序复位", "System");

                LogHelper.Info("程序复位完成");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("执行程序复位时发生异常", ex);
                OnSafetyManagerError("ProgramReset", ex.Message, "", true, ex);
                return false;
            }
        }

        /// <summary>
        /// 检查是否可以启动
        /// </summary>
        /// <returns>启动检查结果</returns>
        public async Task<StartupCheckResult> CheckCanStartAsync()
        {
            var result = new StartupCheckResult();

            try
            {
                // 检查安全锁定状态
                result.SafetyLocked = await _ioManager.ReadInputAsync("I0003");
                if (result.SafetyLocked)
                {
                    result.CanStart = false;
                    result.BlockingReasons.Add("系统处于安全锁定状态");
                }

                // 检查紧急停止条件
                var emergencyConditions = await GetEmergencyStopConditionsAsync();
                result.EmergencyConditions = emergencyConditions;

                if (emergencyConditions.HasAnyEmergencyCondition)
                {
                    result.CanStart = false;
                    result.BlockingReasons.AddRange(emergencyConditions.GetTriggeredConditions());
                }

                // 检查当前安全状态
                if (_currentSafetyState == SafetyState.SystemError)
                {
                    result.CanStart = false;
                    result.BlockingReasons.Add("系统处于错误状态");
                }

                // 检查依赖管理器状态
                if (_ioManager == null || _communicationManager == null)
                {
                    result.CanStart = false;
                    result.BlockingReasons.Add("依赖管理器未初始化");
                }

                result.CurrentSafetyState = _currentSafetyState;
                result.CheckTime = DateTime.Now;

                // 如果没有阻塞原因，则可以启动
                if (result.BlockingReasons.Count == 0)
                {
                    result.CanStart = true;
                }

                LogHelper.Debug($"启动检查结果: 可启动={result.CanStart}, 阻塞原因数量={result.BlockingReasons.Count}");
            }
            catch (Exception ex)
            {
                LogHelper.Error("检查启动条件时发生异常", ex);
                OnSafetyManagerError("StartupCheck", ex.Message, "", false, ex);

                result.CanStart = false;
                result.BlockingReasons.Add($"检查异常: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 获取启动和停止信号的当前状态
        /// </summary>
        /// <returns>信号状态</returns>
        public async Task<StartStopSignalStatus> GetStartStopSignalStatusAsync()
        {
            var status = new StartStopSignalStatus();

            try
            {
                // 读取启动信号 (I0001)
                status.StartSignalValue = await _ioManager.ReadInputAsync("I0001");
                var startSignalState = GetIOSignalState("I0001");
                if (startSignalState != null)
                {
                    status.StartSignalLastEdge = startSignalState.EdgeHistory?.LastOrDefault();
                }

                // 读取停止信号 (I0002)
                status.StopSignalValue = await _ioManager.ReadInputAsync("I0002");
                var stopSignalState = GetIOSignalState("I0002");
                if (stopSignalState != null)
                {
                    status.StopSignalLastEdge = stopSignalState.EdgeHistory?.LastOrDefault();
                }

                // 读取安全锁定信号 (I0003)
                status.SafetyLockValue = await _ioManager.ReadInputAsync("I0003");
                var safetyLockState = GetIOSignalState("I0003");
                if (safetyLockState != null)
                {
                    status.SafetyLockLastEdge = safetyLockState.EdgeHistory?.LastOrDefault();
                }

                status.ReadTime = DateTime.Now;
                status.CurrentSafetyState = _currentSafetyState;
            }
            catch (Exception ex)
            {
                LogHelper.Error("获取启动停止信号状态时发生异常", ex);
                OnSafetyManagerError("GetStartStopSignalStatus", ex.Message, "", false, ex);
            }

            return status;
        }
        #endregion

        #region 公共方法 - 三色指示灯控制
        /// <summary>
        /// 手动设置指示灯状态
        /// </summary>
        /// <param name="state">指示灯状态</param>
        /// <param name="reason">设置原因</param>
        /// <returns>是否设置成功</returns>
        public async Task<bool> SetIndicatorLightManualAsync(IndicatorLightState state, string reason = "手动设置")
        {
            try
            {
                LogHelper.Info($"手动设置指示灯状态: {state}, 原因: {reason}");
                await SetIndicatorLightAsync(state, reason);
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"手动设置指示灯状态时发生异常: {state}", ex);
                OnSafetyManagerError("ManualSetIndicatorLight", ex.Message, "", false, ex);
                return false;
            }
        }

        /// <summary>
        /// 获取当前指示灯状态详情
        /// </summary>
        /// <returns>指示灯状态详情</returns>
        public async Task<IndicatorLightStatus> GetIndicatorLightStatusAsync()
        {
            var status = new IndicatorLightStatus();

            try
            {
                // 读取三色灯的实际输出状态
                status.RedLightOutput = await _ioManager.ReadOutputAsync("O0008");
                status.GreenLightOutput = await _ioManager.ReadOutputAsync("O0009");
                status.YellowLightOutput = await _ioManager.ReadOutputAsync("O0010");

                // 确定当前实际的指示灯状态
                if (status.RedLightOutput)
                    status.ActualState = IndicatorLightState.Red;
                else if (status.GreenLightOutput)
                    status.ActualState = IndicatorLightState.Green;
                else if (status.YellowLightOutput)
                    status.ActualState = IndicatorLightState.Yellow;
                else
                    status.ActualState = IndicatorLightState.Off;

                status.ExpectedState = _currentIndicatorState;
                status.IsConsistent = (status.ActualState == status.ExpectedState);
                status.ReadTime = DateTime.Now;

                LogHelper.Debug($"指示灯状态: 期望={status.ExpectedState}, 实际={status.ActualState}, 一致={status.IsConsistent}");
            }
            catch (Exception ex)
            {
                LogHelper.Error("获取指示灯状态时发生异常", ex);
                OnSafetyManagerError("GetIndicatorLightStatus", ex.Message, "", false, ex);
            }

            return status;
        }

        /// <summary>
        /// 测试三色指示灯（依次点亮每种颜色）
        /// </summary>
        /// <param name="testDurationMs">每种颜色的测试持续时间（毫秒）</param>
        /// <returns>是否测试成功</returns>
        public async Task<bool> TestIndicatorLightsAsync(int testDurationMs = 1000)
        {
            try
            {
                LogHelper.Info($"开始三色指示灯测试，每种颜色持续 {testDurationMs}ms");

                var originalState = _currentIndicatorState;

                // 测试红灯
                LogHelper.Debug("测试红灯...");
                await SetIndicatorLightAsync(IndicatorLightState.Red, "指示灯测试");
                await Task.Delay(testDurationMs);

                // 测试绿灯
                LogHelper.Debug("测试绿灯...");
                await SetIndicatorLightAsync(IndicatorLightState.Green, "指示灯测试");
                await Task.Delay(testDurationMs);

                // 测试黄灯
                LogHelper.Debug("测试黄灯...");
                await SetIndicatorLightAsync(IndicatorLightState.Yellow, "指示灯测试");
                await Task.Delay(testDurationMs);

                // 关闭所有灯
                LogHelper.Debug("关闭所有指示灯...");
                await SetIndicatorLightAsync(IndicatorLightState.Off, "指示灯测试");
                await Task.Delay(testDurationMs);

                // 恢复原始状态
                await SetIndicatorLightAsync(originalState, "指示灯测试完成");

                LogHelper.Info("三色指示灯测试完成");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("三色指示灯测试时发生异常", ex);
                OnSafetyManagerError("TestIndicatorLights", ex.Message, "", false, ex);
                return false;
            }
        }

        /// <summary>
        /// 同步指示灯状态（确保实际输出与期望状态一致）
        /// </summary>
        /// <returns>是否同步成功</returns>
        public async Task<bool> SynchronizeIndicatorLightAsync()
        {
            try
            {
                var status = await GetIndicatorLightStatusAsync();

                if (status.IsConsistent)
                {
                    LogHelper.Debug("指示灯状态已同步");
                    return true;
                }

                LogHelper.Warning($"指示灯状态不一致，期望: {status.ExpectedState}, 实际: {status.ActualState}，正在同步...");

                // 重新设置指示灯状态
                await SetIndicatorLightAsync(_currentIndicatorState, "状态同步");

                // 验证同步结果
                var newStatus = await GetIndicatorLightStatusAsync();
                if (newStatus.IsConsistent)
                {
                    LogHelper.Info("指示灯状态同步成功");
                    return true;
                }
                else
                {
                    LogHelper.Error("指示灯状态同步失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("同步指示灯状态时发生异常", ex);
                OnSafetyManagerError("SynchronizeIndicatorLight", ex.Message, "", false, ex);
                return false;
            }
        }

        /// <summary>
        /// 获取指示灯控制统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public IndicatorLightStatistics GetIndicatorLightStatistics()
        {
            lock (_lockObject)
            {
                var stats = new IndicatorLightStatistics
                {
                    CurrentState = _currentIndicatorState,
                    LastChangeTime = _status.LastUpdateTime,
                    SwitchDelayMs = _configuration.IndicatorLightSwitchDelayMs
                };

                // 这里可以添加更多统计信息，如状态切换次数等
                return stats;
            }
        }
        #endregion

        #region 事件触发方法
        /// <summary>
        /// 触发安全状态变化事件
        /// </summary>
        /// <param name="oldState">旧状态</param>
        /// <param name="newState">新状态</param>
        /// <param name="reason">变化原因</param>
        /// <param name="triggerIOSignal">触发的I/O信号</param>
        private void OnSafetyStateChanged(SafetyState oldState, SafetyState newState, string reason, string triggerIOSignal)
        {
            try
            {
                SafetyStateChanged?.Invoke(this, new SafetyStateChangedEventArgs(oldState, newState, reason, triggerIOSignal));
            }
            catch (Exception ex)
            {
                LogHelper.Error("触发安全状态变化事件时发生异常", ex);
            }
        }

        /// <summary>
        /// 触发I/O信号变化事件
        /// </summary>
        /// <param name="ioNumber">I/O编号</param>
        /// <param name="signalType">信号类型</param>
        /// <param name="oldValue">旧值</param>
        /// <param name="newValue">新值</param>
        /// <param name="isEdgeTriggered">是否边沿触发</param>
        /// <param name="isRisingEdge">是否上升沿</param>
        private void OnIOSignalChanged(string ioNumber, IOSignalType signalType, bool oldValue, bool newValue,
            bool isEdgeTriggered, bool isRisingEdge)
        {
            try
            {
                IOSignalChanged?.Invoke(this, new IOSignalChangedEventArgs(ioNumber, signalType, oldValue, newValue,
                    isEdgeTriggered, isRisingEdge));
            }
            catch (Exception ex)
            {
                LogHelper.Error("触发I/O信号变化事件时发生异常", ex);
            }
        }

        /// <summary>
        /// 触发紧急停止事件
        /// </summary>
        /// <param name="triggerIOSignal">触发信号</param>
        /// <param name="reason">停止原因</param>
        /// <param name="isCritical">是否严重</param>
        private void OnEmergencyStopTriggered(string triggerIOSignal, string reason, bool isCritical)
        {
            try
            {
                EmergencyStopTriggered?.Invoke(this, new EmergencyStopEventArgs(triggerIOSignal, reason, isCritical));
            }
            catch (Exception ex)
            {
                LogHelper.Error("触发紧急停止事件时发生异常", ex);
            }
        }

        /// <summary>
        /// 触发指示灯状态变化事件
        /// </summary>
        /// <param name="oldState">旧状态</param>
        /// <param name="newState">新状态</param>
        /// <param name="reason">变化原因</param>
        private void OnIndicatorLightChanged(IndicatorLightState oldState, IndicatorLightState newState, string reason)
        {
            try
            {
                IndicatorLightChanged?.Invoke(this, new IndicatorLightChangedEventArgs(oldState, newState, reason));
            }
            catch (Exception ex)
            {
                LogHelper.Error("触发指示灯状态变化事件时发生异常", ex);
            }
        }

        /// <summary>
        /// 触发安全管理器错误事件
        /// </summary>
        /// <param name="errorType">错误类型</param>
        /// <param name="errorMessage">错误消息</param>
        /// <param name="relatedIOSignal">相关I/O信号</param>
        /// <param name="isCritical">是否严重</param>
        /// <param name="exception">异常对象</param>
        private void OnSafetyManagerError(string errorType, string errorMessage, string relatedIOSignal,
            bool isCritical, Exception exception)
        {
            try
            {
                SafetyManagerError?.Invoke(this, new SafetyManagerErrorEventArgs(errorType, errorMessage,
                    relatedIOSignal, isCritical, exception));
            }
            catch (Exception ex)
            {
                LogHelper.Error("触发安全管理器错误事件时发生异常", ex);
            }
        }
        #endregion
    }
}
