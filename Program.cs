using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using MyHMI.Helpers;
using MyHMI.Managers;
using MyHMI.Settings;

namespace MyHMI
{
    /// <summary>
    /// 程序入口点
    /// 负责初始化所有Manager和启动主窗体
    /// </summary>
    static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            try
            {
                // 初始化日志系统
                LogHelper.Info("程序启动开始");

                // 加载新的Settings系统配置
                LogHelper.Info("加载Settings系统配置...");
                Settings.Settings.Load();
                LogHelper.Info("Settings系统配置加载完成");

                // 更新初始化状态管理器
                InitializationStatusManager.Instance.UpdateModuleStatus("SettingsSystem", true, true, null);
                InitializationStatusManager.Instance.UpdateModuleStatus("SystemConfiguration", true, true, null);

                // 初始化UI辅助类
                UIHelper.Initialize();

                // 异步初始化所有Manager
                var initTask = Task.Run(async () =>
                {
                    try
                    {
                        await InitializeManagersAsync();
                        LogHelper.Info("所有Manager初始化完成");
                        return true;
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error("Manager初始化失败", ex);
                        UIHelper.ShowMessageBox($"系统初始化失败：{ex.Message}", "错误",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return false;
                    }
                });

                // 等待初始化完成
                if (initTask.Result)
                {
                    // 启动主窗体（在UI线程中）
                    Application.Run(new UI.MainForm());
                }
                else
                {
                    LogHelper.Error("程序初始化失败，退出程序");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("程序启动失败", ex);
                MessageBox.Show($"程序启动失败：{ex.Message}", "严重错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 异步初始化所有Manager
        /// </summary>
        /// <returns></returns>
        private static async Task InitializeManagersAsync()
        {
            LogHelper.Info("开始初始化各个Manager...");

            var config = Settings.Settings.Current;
            bool allSuccess = true;

            try
            {
                // 首先初始化DMC1000B控制卡（全局唯一初始化）
                LogHelper.Info("初始化DMC1000B控制卡...");
                var cardInitResult = await DMC1000BCardManager.Instance.InitializeCardAsync();
                if (!cardInitResult)
                {
                    LogHelper.Warning("DMC1000B控制卡初始化失败，相关功能可能无法正常工作");
                    allSuccess = false;
                }

                // 更新初始化状态管理器
                InitializationStatusManager.Instance.UpdateModuleStatus("DMC1000BCard", cardInitResult, false,
                    cardInitResult ? null : "控制卡初始化失败");

                // 按依赖关系顺序初始化Manager
                // 1. 基础Manager（无依赖）
                LogHelper.Info("初始化基础Manager...");

                // 初始化DMC1000BIO管理器（统一使用真实硬件管理器）
                var dmc1000bIOResult = await DMC1000BIOManager.Instance.InitializeAsync();
                if (!dmc1000bIOResult)
                {
                    LogHelper.Warning("DMC1000BIO管理器初始化失败");
                    allSuccess = false;
                }

                // 更新初始化状态管理器
                InitializationStatusManager.Instance.UpdateModuleStatus("IOManager", dmc1000bIOResult,
                    dmc1000bIOResult && DMC1000BIOManager.Instance.IsMonitoring,
                    dmc1000bIOResult ? null : "IO管理器初始化失败");

                // 初始化DMC1000B电机管理器（使用统一控制卡管理器，避免重复初始化）
                var dmc1000bMotorResult = await DMC1000BMotorManager.Instance.InitializeAsync();
                if (!dmc1000bMotorResult)
                {
                    LogHelper.Warning("DMC1000B电机管理器初始化失败");
                    allSuccess = false;
                }

                // 更新初始化状态管理器
                InitializationStatusManager.Instance.UpdateModuleStatus("MotorManager", dmc1000bMotorResult, false,
                    dmc1000bMotorResult ? null : "电机管理器初始化失败");

                // 初始化系统模式管理器
                LogHelper.Info("初始化系统模式管理器...");
                // SystemModeManager是单例，不需要显式初始化，但可以在这里订阅全局事件
                SystemModeManager.Instance.ModeChanged += (sender, e) =>
                {
                    LogHelper.Info($"系统模式已从 {e.PreviousMode} 切换到 {e.CurrentMode}");
                };
                SystemModeManager.Instance.AutomationStarted += (sender, e) =>
                {
                    LogHelper.Info("自动化流程已启动");
                };
                SystemModeManager.Instance.AutomationCompleted += (sender, e) =>
                {
                    LogHelper.Info($"自动化流程已完成，结果: {(e.IsSuccess ? "成功" : "失败")} - {e.Message}");
                };
                SystemModeManager.Instance.AutomationError += (sender, e) =>
                {
                    LogHelper.Error($"自动化流程发生错误: {e.ErrorMessage}", e.Exception);
                };

                var motorResult = await MotorManager.Instance.InitializeAsync();
                if (!motorResult)
                {
                    LogHelper.Warning("电机管理器初始化失败");
                    allSuccess = false;
                }

                // 2. 通信Manager
                LogHelper.Info("初始化通信Manager...");

                var scannerResult = await ScannerManager.Instance.InitializeAsync();
                if (!scannerResult)
                {
                    LogHelper.Warning("扫描枪管理器初始化失败");
                    allSuccess = false;
                }

                // 初始化扫码器自动模式管理器
                var scannerAutoModeResult = await ScannerAutoModeManager.Instance.InitializeAsync();
                if (!scannerAutoModeResult)
                {
                    LogHelper.Warning("扫码器自动模式管理器初始化失败");
                    allSuccess = false;
                }

                // 注释掉Modbus TCP初始化 - 由第三方控制，不需要硬件连接验证
                // var modbusResult = await ModbusTcpManager.Instance.InitializeAsync();
                // if (!modbusResult)
                // {
                //     LogHelper.Warning("Modbus TCP管理器初始化失败");
                //     allSuccess = false;
                // }
                LogHelper.Info("跳过Modbus TCP管理器初始化 - 由第三方控制");

                var epsonRobotResult = await EpsonRobotManager.Instance.InitializeAsync();
                if (!epsonRobotResult)
                {
                    LogHelper.Warning("Epson机器人管理器初始化失败");
                    allSuccess = false;
                }

                // 3. 视觉Manager
                LogHelper.Info("初始化视觉Manager...");

                var visionResult = await VisionManager.Instance.InitializeAsync();
                if (!visionResult)
                {
                    LogHelper.Warning("视觉管理器初始化失败");
                    allSuccess = false;
                }

                // 4. 数据Manager
                LogHelper.Info("初始化数据Manager...");

                var statsResult = await StatisticsManager.Instance.InitializeAsync();
                if (!statsResult)
                {
                    LogHelper.Warning("统计管理器初始化失败");
                    allSuccess = false;
                }

                LogHelper.Info($"Manager初始化完成，成功率: {(allSuccess ? "100%" : "部分失败")}");
            }
            catch (Exception ex)
            {
                LogHelper.Error("Manager初始化过程中发生异常", ex);
                throw;
            }
        }
    }
}
