using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;
using MyHMI.Helpers;

namespace MyHMI.Testing
{
    /// <summary>
    /// 简单的单元测试框架
    /// </summary>
    public class SimpleTestFramework
    {
        #region 私有字段
        private List<TestCase> _testCases = new List<TestCase>();
        private int _passedCount = 0;
        private int _failedCount = 0;
        private int _skippedCount = 0;
        #endregion

        #region 公共属性
        /// <summary>
        /// 通过的测试数量
        /// </summary>
        public int PassedCount => _passedCount;

        /// <summary>
        /// 失败的测试数量
        /// </summary>
        public int FailedCount => _failedCount;

        /// <summary>
        /// 跳过的测试数量
        /// </summary>
        public int SkippedCount => _skippedCount;

        /// <summary>
        /// 总测试数量
        /// </summary>
        public int TotalCount => _testCases.Count;
        #endregion

        #region 测试注册方法
        /// <summary>
        /// 添加测试用例
        /// </summary>
        /// <param name="name">测试名称</param>
        /// <param name="testAction">测试动作</param>
        /// <param name="category">测试分类</param>
        public void AddTest(string name, Func<Task> testAction, string category = "General")
        {
            _testCases.Add(new TestCase
            {
                Name = name,
                TestAction = testAction,
                Category = category,
                IsEnabled = true
            });
        }

        /// <summary>
        /// 添加同步测试用例
        /// </summary>
        /// <param name="name">测试名称</param>
        /// <param name="testAction">测试动作</param>
        /// <param name="category">测试分类</param>
        public void AddTest(string name, Action testAction, string category = "General")
        {
            _testCases.Add(new TestCase
            {
                Name = name,
                TestAction = () => { testAction(); return Task.CompletedTask; },
                Category = category,
                IsEnabled = true
            });
        }

        /// <summary>
        /// 禁用测试用例
        /// </summary>
        /// <param name="name">测试名称</param>
        public void DisableTest(string name)
        {
            var test = _testCases.Find(t => t.Name == name);
            if (test != null)
            {
                test.IsEnabled = false;
            }
        }
        #endregion

        #region 测试执行方法
        /// <summary>
        /// 运行所有测试
        /// </summary>
        /// <returns>测试结果</returns>
        public async Task<TestResult> RunAllTestsAsync()
        {
            LogHelper.Info("开始运行所有测试...");
            
            var stopwatch = Stopwatch.StartNew();
            _passedCount = 0;
            _failedCount = 0;
            _skippedCount = 0;

            var results = new List<TestCaseResult>();

            foreach (var testCase in _testCases)
            {
                var result = await RunSingleTestAsync(testCase);
                results.Add(result);

                switch (result.Status)
                {
                    case TestStatus.Passed:
                        _passedCount++;
                        break;
                    case TestStatus.Failed:
                        _failedCount++;
                        break;
                    case TestStatus.Skipped:
                        _skippedCount++;
                        break;
                }
            }

            stopwatch.Stop();

            var testResult = new TestResult
            {
                TotalTests = _testCases.Count,
                PassedTests = _passedCount,
                FailedTests = _failedCount,
                SkippedTests = _skippedCount,
                TotalTime = stopwatch.Elapsed,
                TestCaseResults = results
            };

            LogTestSummary(testResult);
            return testResult;
        }

        /// <summary>
        /// 运行指定分类的测试
        /// </summary>
        /// <param name="category">测试分类</param>
        /// <returns>测试结果</returns>
        public async Task<TestResult> RunTestsByCategoryAsync(string category)
        {
            LogHelper.Info($"开始运行分类'{category}'的测试...");

            var categoryTests = _testCases.FindAll(t => t.Category == category);
            var originalTests = _testCases;
            
            _testCases = categoryTests;
            var result = await RunAllTestsAsync();
            _testCases = originalTests;

            return result;
        }

        /// <summary>
        /// 运行单个测试
        /// </summary>
        /// <param name="testCase">测试用例</param>
        /// <returns>测试结果</returns>
        private async Task<TestCaseResult> RunSingleTestAsync(TestCase testCase)
        {
            var result = new TestCaseResult
            {
                Name = testCase.Name,
                Category = testCase.Category
            };

            if (!testCase.IsEnabled)
            {
                result.Status = TestStatus.Skipped;
                result.Message = "测试被禁用";
                LogHelper.Info($"[SKIP] {testCase.Name} - 测试被禁用");
                return result;
            }

            var stopwatch = Stopwatch.StartNew();

            try
            {
                LogHelper.Info($"[RUN] {testCase.Name}");
                await testCase.TestAction();
                
                stopwatch.Stop();
                result.Status = TestStatus.Passed;
                result.ExecutionTime = stopwatch.Elapsed;
                result.Message = "测试通过";
                
                LogHelper.Info($"[PASS] {testCase.Name} ({stopwatch.ElapsedMilliseconds}ms)");
            }
            catch (AssertionException ex)
            {
                stopwatch.Stop();
                result.Status = TestStatus.Failed;
                result.ExecutionTime = stopwatch.Elapsed;
                result.Message = ex.Message;
                result.Exception = ex;
                
                LogHelper.Error($"[FAIL] {testCase.Name} ({stopwatch.ElapsedMilliseconds}ms) - {ex.Message}");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                result.Status = TestStatus.Failed;
                result.ExecutionTime = stopwatch.Elapsed;
                result.Message = $"未处理的异常: {ex.Message}";
                result.Exception = ex;
                
                LogHelper.Error($"[ERROR] {testCase.Name} ({stopwatch.ElapsedMilliseconds}ms)", ex);
            }

            return result;
        }
        #endregion

        #region 断言方法
        /// <summary>
        /// 断言为真
        /// </summary>
        /// <param name="condition">条件</param>
        /// <param name="message">失败消息</param>
        public static void AssertTrue(bool condition, string message = "断言失败")
        {
            if (!condition)
            {
                throw new AssertionException($"AssertTrue失败: {message}");
            }
        }

        /// <summary>
        /// 断言为假
        /// </summary>
        /// <param name="condition">条件</param>
        /// <param name="message">失败消息</param>
        public static void AssertFalse(bool condition, string message = "断言失败")
        {
            if (condition)
            {
                throw new AssertionException($"AssertFalse失败: {message}");
            }
        }

        /// <summary>
        /// 断言相等
        /// </summary>
        /// <typeparam name="T">类型</typeparam>
        /// <param name="expected">期望值</param>
        /// <param name="actual">实际值</param>
        /// <param name="message">失败消息</param>
        public static void AssertEqual<T>(T expected, T actual, string message = "断言失败")
        {
            if (!Equals(expected, actual))
            {
                throw new AssertionException($"AssertEqual失败: {message}. 期望: {expected}, 实际: {actual}");
            }
        }

        /// <summary>
        /// 断言不相等
        /// </summary>
        /// <typeparam name="T">类型</typeparam>
        /// <param name="expected">期望值</param>
        /// <param name="actual">实际值</param>
        /// <param name="message">失败消息</param>
        public static void AssertNotEqual<T>(T expected, T actual, string message = "断言失败")
        {
            if (Equals(expected, actual))
            {
                throw new AssertionException($"AssertNotEqual失败: {message}. 不应该等于: {expected}");
            }
        }

        /// <summary>
        /// 断言为空
        /// </summary>
        /// <param name="value">值</param>
        /// <param name="message">失败消息</param>
        public static void AssertNull(object value, string message = "断言失败")
        {
            if (value != null)
            {
                throw new AssertionException($"AssertNull失败: {message}. 值不为空: {value}");
            }
        }

        /// <summary>
        /// 断言不为空
        /// </summary>
        /// <param name="value">值</param>
        /// <param name="message">失败消息</param>
        public static void AssertNotNull(object value, string message = "断言失败")
        {
            if (value == null)
            {
                throw new AssertionException($"AssertNotNull失败: {message}. 值为空");
            }
        }

        /// <summary>
        /// 断言抛出异常
        /// </summary>
        /// <typeparam name="T">异常类型</typeparam>
        /// <param name="action">动作</param>
        /// <param name="message">失败消息</param>
        public static void AssertThrows<T>(Action action, string message = "断言失败") where T : Exception
        {
            try
            {
                action();
                throw new AssertionException($"AssertThrows失败: {message}. 期望抛出{typeof(T).Name}异常，但没有抛出");
            }
            catch (T)
            {
                // 期望的异常，测试通过
            }
            catch (Exception ex)
            {
                throw new AssertionException($"AssertThrows失败: {message}. 期望抛出{typeof(T).Name}异常，但抛出了{ex.GetType().Name}异常");
            }
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 记录测试摘要
        /// </summary>
        /// <param name="result">测试结果</param>
        private void LogTestSummary(TestResult result)
        {
            LogHelper.Info("=== 测试摘要 ===");
            LogHelper.Info($"总测试数: {result.TotalTests}");
            LogHelper.Info($"通过: {result.PassedTests}");
            LogHelper.Info($"失败: {result.FailedTests}");
            LogHelper.Info($"跳过: {result.SkippedTests}");
            LogHelper.Info($"总耗时: {result.TotalTime.TotalMilliseconds:F1}ms");
            LogHelper.Info($"成功率: {(result.PassedTests * 100.0 / result.TotalTests):F1}%");
            LogHelper.Info("===============");
        }
        #endregion
    }

    #region 数据模型
    /// <summary>
    /// 测试用例
    /// </summary>
    public class TestCase
    {
        public string Name { get; set; }
        public string Category { get; set; }
        public Func<Task> TestAction { get; set; }
        public bool IsEnabled { get; set; }
    }

    /// <summary>
    /// 测试用例结果
    /// </summary>
    public class TestCaseResult
    {
        public string Name { get; set; }
        public string Category { get; set; }
        public TestStatus Status { get; set; }
        public TimeSpan ExecutionTime { get; set; }
        public string Message { get; set; }
        public Exception Exception { get; set; }
    }

    /// <summary>
    /// 测试结果
    /// </summary>
    public class TestResult
    {
        public int TotalTests { get; set; }
        public int PassedTests { get; set; }
        public int FailedTests { get; set; }
        public int SkippedTests { get; set; }
        public TimeSpan TotalTime { get; set; }
        public List<TestCaseResult> TestCaseResults { get; set; }
    }

    /// <summary>
    /// 测试状态
    /// </summary>
    public enum TestStatus
    {
        Passed,
        Failed,
        Skipped
    }

    /// <summary>
    /// 断言异常
    /// </summary>
    public class AssertionException : Exception
    {
        public AssertionException(string message) : base(message) { }
        public AssertionException(string message, Exception innerException) : base(message, innerException) { }
    }
    #endregion
}
