<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CsvHelper</name>
    </assembly>
    <members>
        <member name="T:CsvHelper.ArrayHelper">
            <summary>
            Methods to help with arrays.
            </summary>
        </member>
        <member name="M:CsvHelper.ArrayHelper.Trim(System.Char[],System.Int32@,System.Int32@,System.Char[])">
            <summary>
            Trims the characters off the start and end of the buffer
            by updating the start and length arguments.
            </summary>
            <param name="buffer">The buffer.</param>
            <param name="start">The start.</param>
            <param name="length">The length.</param>
            <param name="trimChars">The characters to trim.</param>
        </member>
        <member name="M:CsvHelper.ArrayHelper.Contains(System.Char[],System.Char@)">
            <summary>
            Determines whether this given array contains the given character.
            </summary>
            <param name="array">The array to search.</param>
            <param name="c">The character to look for.</param>
            <returns>
              <c>true</c> if the array contains the characters, otherwise <c>false</c>.
            </returns>
        </member>
        <member name="T:CsvHelper.BadDataException">
            <summary>
            Represents errors that occur due to bad data.
            </summary>
        </member>
        <member name="F:CsvHelper.BadDataException.Field">
            <summary>
            The full field unedited.
            </summary>
        </member>
        <member name="F:CsvHelper.BadDataException.RawRecord">
            <summary>
            The full row unedited.
            </summary>
        </member>
        <member name="M:CsvHelper.BadDataException.#ctor(System.String,System.String,CsvHelper.CsvContext)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.BadDataException"/> class.
            </summary>
            <param name="field">The full field unedited.</param>
            <param name="rawRecord">The full row unedited.</param>
            <param name="context">The reading context.</param>
        </member>
        <member name="M:CsvHelper.BadDataException.#ctor(System.String,System.String,CsvHelper.CsvContext,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.BadDataException"/> class
            with a specified error message.
            </summary>
            <param name="field">The full field unedited.</param>
            <param name="rawRecord">The full row unedited.</param>
            <param name="context">The reading context.</param>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:CsvHelper.BadDataException.#ctor(System.String,System.String,CsvHelper.CsvContext,System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.BadDataException"/> class
            with a specified error message and a reference to the inner exception that 
            is the cause of this exception.
            </summary>
            <param name="field">The full field unedited.</param>
            <param name="rawRecord">The full row unedited.</param>
            <param name="context">The reading context.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.AllowCommentsAttribute">
            <summary>
            A value indicating if comments are allowed.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.AllowCommentsAttribute.AllowComments">
            <summary>
            Gets a value indicating if comments are allowed.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.AllowCommentsAttribute.#ctor(System.Boolean)">
            <summary>
            A value indicating if comments are allowed.
            </summary>
            <param name="allowComments">The value indicating id comments are allowed.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.AllowCommentsAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.BooleanFalseValuesAttribute">
            <summary>
            The string values used to represent a boolean false when converting.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.BooleanFalseValuesAttribute.FalseValues">
            <summary>
            Gets the false values.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.BooleanFalseValuesAttribute.#ctor(System.String)">
            <summary>
            The string values used to represent a boolean false when converting.
            </summary>
            <param name="falseValue">The false values.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.BooleanFalseValuesAttribute.#ctor(System.String[])">
            <summary>
            The string values used to represent a boolean false when converting.
            </summary>
            <param name="falseValues">The false values.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.BooleanFalseValuesAttribute.ApplyTo(CsvHelper.Configuration.MemberMap)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.BooleanFalseValuesAttribute.ApplyTo(CsvHelper.Configuration.ParameterMap)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.BooleanTrueValuesAttribute">
            <summary>
            The string values used to represent a boolean true when converting.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.BooleanTrueValuesAttribute.TrueValues">
            <summary>
            Gets the true values.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.BooleanTrueValuesAttribute.#ctor(System.String)">
            <summary>
            The string values used to represent a boolean true when converting.
            </summary>
            <param name="trueValue"></param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.BooleanTrueValuesAttribute.#ctor(System.String[])">
            <summary>
            The string values used to represent a boolean true when converting.
            </summary>
            <param name="trueValues"></param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.BooleanTrueValuesAttribute.ApplyTo(CsvHelper.Configuration.MemberMap)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.BooleanTrueValuesAttribute.ApplyTo(CsvHelper.Configuration.ParameterMap)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.BufferSizeAttribute">
            <summary>
            The size of the buffer used for parsing and writing CSV files.
            Default is 0x1000.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.BufferSizeAttribute.BufferSize">
            <summary>
            The buffer size.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.BufferSizeAttribute.#ctor(System.Int32)">
            <summary>
            The size of the buffer used for parsing and writing CSV files.
            </summary>
            <param name="bufferSize"></param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.BufferSizeAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.CacheFieldsAttribute">
            <summary>
            Cache fields that are created when parsing.
            Default is false.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.CacheFieldsAttribute.CacheFields">
            <summary>
            Cache fields that are created when parsing.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.CacheFieldsAttribute.#ctor(System.Boolean)">
            <summary>
            Cache fields that are created when parsing.
            </summary>
            <param name="cacheFields"></param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.CacheFieldsAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.CommentAttribute">
            <summary>
            The character used to denote a line that is commented out.
            Default is #.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.CommentAttribute.Comment">
            <summary>
            Gets the character used to denote a line that is commented out.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.CommentAttribute.#ctor(System.Char)">
            <summary>
            The character used to denote a line that is commented out.
            </summary>
            <param name="comment">The comment character.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.CommentAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.ConstantAttribute">
            <summary>
            The constant value that will be used for every record when 
            reading and writing. This value will always be used no matter 
            what other mapping configurations are specified.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.ConstantAttribute.Constant">
            <summary>
            Gets the constant.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.ConstantAttribute.#ctor(System.Object)">
            <summary>
            The constant value that will be used for every record when 
            reading and writing. This value will always be used no matter 
            what other mapping configurations are specified.
            </summary>
            <param name="constant">The constant.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.ConstantAttribute.ApplyTo(CsvHelper.Configuration.MemberMap)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.ConstantAttribute.ApplyTo(CsvHelper.Configuration.ParameterMap)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.CountBytesAttribute">
            <summary>
            A value indicating whether the number of bytes should
            be counted while parsing. Default is false. This will slow down parsing
            because it needs to get the byte count of every char for the given encoding.
            The <see cref="T:System.Text.Encoding"/> needs to be set correctly for this to be accurate.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.CountBytesAttribute.CountBytes">
            <summary>
            A value indicating whether the number of bytes should
            be counted while parsing. Default is false. This will slow down parsing
            because it needs to get the byte count of every char for the given encoding.
            The <see cref="T:System.Text.Encoding"/> needs to be set correctly for this to be accurate.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.CountBytesAttribute.#ctor(System.Boolean)">
            <summary>
            A value indicating whether the number of bytes should
            be counted while parsing. Default is false. This will slow down parsing
            because it needs to get the byte count of every char for the given encoding.
            The <see cref="T:System.Text.Encoding"/> needs to be set correctly for this to be accurate.
            </summary>
            <param name="countBytes"></param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.CountBytesAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.CultureInfoAttribute">
            <summary>
            The <see cref="P:CsvHelper.Configuration.Attributes.CultureInfoAttribute.CultureInfo"/> used when type converting.
            This will override the global <see cref="P:CsvHelper.Configuration.CsvConfiguration.CultureInfo"/>
            setting. Or set the same if the attribute is specified on class level.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.CultureInfoAttribute.CultureInfo">
            <summary>
            Gets the culture info.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.CultureInfoAttribute.#ctor(System.String)">
            <summary>
            The <see cref="P:CsvHelper.Configuration.Attributes.CultureInfoAttribute.CultureInfo"/> used when type converting.
            This will override the global <see cref="P:CsvHelper.Configuration.CsvConfiguration.CultureInfo"/>
            setting. Or set the same if the attribute is specified on class level.
            </summary>
            <param name="culture">The culture.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.CultureInfoAttribute.ApplyTo(CsvHelper.Configuration.MemberMap)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.CultureInfoAttribute.ApplyTo(CsvHelper.Configuration.ParameterMap)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.DateTimeStylesAttribute">
            <summary>
            The <see cref="P:CsvHelper.Configuration.Attributes.DateTimeStylesAttribute.DateTimeStyles"/> to use when type converting.
            This is used when doing any <see cref="T:System.DateTime"/> conversions.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.DateTimeStylesAttribute.DateTimeStyles">
            <summary>
            Gets the date time styles.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.DateTimeStylesAttribute.#ctor(System.Globalization.DateTimeStyles)">
            <summary>
            The <see cref="P:CsvHelper.Configuration.Attributes.DateTimeStylesAttribute.DateTimeStyles"/> to use when type converting.
            This is used when doing any <see cref="T:System.DateTime"/> conversions.
            </summary>
            <param name="dateTimeStyles">The date time styles.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.DateTimeStylesAttribute.ApplyTo(CsvHelper.Configuration.MemberMap)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.DateTimeStylesAttribute.ApplyTo(CsvHelper.Configuration.ParameterMap)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.DefaultAttribute">
            <summary>
            The default value that will be used when reading when
            the CSV field is empty.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.DefaultAttribute.Default">
            <summary>
            Gets the default value.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.DefaultAttribute.#ctor(System.Object)">
            <summary>
            The default value that will be used when reading when
            the CSV field is empty.
            </summary>
            <param name="defaultValue">The default value</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.DefaultAttribute.ApplyTo(CsvHelper.Configuration.MemberMap)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.DefaultAttribute.ApplyTo(CsvHelper.Configuration.ParameterMap)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.DelimiterAttribute">
            <summary>
            The delimiter used to separate fields.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.DelimiterAttribute.Delimiter">
            <summary>
            Gets the delimiter.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.DelimiterAttribute.#ctor(System.String)">
            <summary>
            The delimiter used to separate fields.
            </summary>
            <param name="delimiter">The delimiter.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.DelimiterAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.DetectColumnCountChangesAttribute">
            <summary>
            A value indicating whether changes in the column
            count should be detected. If true, a <see cref="T:CsvHelper.BadDataException"/>
            will be thrown if a different column count is detected.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.DetectColumnCountChangesAttribute.DetectColumnCountChanges">
            <summary>
            A value indicating whether changes in the column
            count should be detected. If true, a <see cref="T:CsvHelper.BadDataException"/>
            will be thrown if a different column count is detected.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.DetectColumnCountChangesAttribute.#ctor(System.Boolean)">
            <summary>
            A value indicating whether changes in the column
            count should be detected. If true, a <see cref="T:CsvHelper.BadDataException"/>
            will be thrown if a different column count is detected.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.DetectColumnCountChangesAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.DetectDelimiterAttribute">
            <summary>
            Detect the delimiter instead of using the delimiter from configuration.
            Default is <c>false</c>.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.DetectDelimiterAttribute.DetectDelimiter">
            <summary>
            Detect the delimiter instead of using the delimiter from configuration.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.DetectDelimiterAttribute.#ctor(System.Boolean)">
            <summary>
            Detect the delimiter instead of using the delimiter from configuration.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.DetectDelimiterAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.DetectDelimiterValuesAttribute">
            <summary>
            The possible delimiter values used when detecting the delimiter.
            Default is [",", ";", "|", "\t"].
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.DetectDelimiterValuesAttribute.DetectDelimiterValues">
            <summary>
            The possible delimiter values used when detecting the delimiter.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.DetectDelimiterValuesAttribute.#ctor(System.String)">
            <summary>
            The possible delimiter values used when detecting the delimiter.
            </summary>
            <param name="detectDelimiterValues">Whitespace separated list of values.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.DetectDelimiterValuesAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.EncodingAttribute">
            <summary>
            The encoding used when counting bytes.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.EncodingAttribute.Encoding">
            <summary>
            Gets the encoding used when counting bytes.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.EncodingAttribute.#ctor(System.String)">
            <summary>
            The encoding used when counting bytes.
            </summary>
            <param name="encoding">The encoding.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.EncodingAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.EnumIgnoreCaseAttribute">
            <summary>
            Ignore case when parsing enums.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.EnumIgnoreCaseAttribute.ApplyTo(CsvHelper.Configuration.MemberMap)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.EnumIgnoreCaseAttribute.ApplyTo(CsvHelper.Configuration.MemberReferenceMap)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.EnumIgnoreCaseAttribute.ApplyTo(CsvHelper.Configuration.ParameterMap)">
            <inheritdoc/>
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.EscapeAttribute">
            <summary>
            The escape character used to escape a quote inside a field.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.EscapeAttribute.Escape">
            <summary>
            Gets the escape character used to escape a quote inside a field.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.EscapeAttribute.#ctor(System.Char)">
            <summary>
            The escape character used to escape a quote inside a field.
            </summary>
            <param name="escape">The escape character.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.EscapeAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.ExceptionMessagesContainRawDataAttribute">
            <summary>
            A value indicating if exception messages contain raw CSV data.
            <c>true</c> if exception contain raw CSV data, otherwise <c>false</c>.
            Default is <c>true</c>.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.ExceptionMessagesContainRawDataAttribute.ExceptionMessagesContainRawData">
            <summary>
            A value indicating if exception messages contain raw CSV data.
            <c>true</c> if exception contain raw CSV data, otherwise <c>false</c>.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.ExceptionMessagesContainRawDataAttribute.#ctor(System.Boolean)">
            <summary>
            A value indicating if exception messages contain raw CSV data.
            <c>true</c> if exception contain raw CSV data, otherwise <c>false</c>.
            </summary>
            <param name="exceptionMessagesContainRawData"></param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.ExceptionMessagesContainRawDataAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.FormatAttribute">
            <summary>
            The string format to be used when type converting.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.FormatAttribute.Formats">
            <summary>
            Gets the formats.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.FormatAttribute.#ctor(System.String)">
            <summary>
            The string format to be used when type converting.
            </summary>
            <param name="format">The format.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.FormatAttribute.#ctor(System.String[])">
            <summary>
            The string format to be used when type converting.
            </summary>
            <param name="formats">The formats.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.FormatAttribute.ApplyTo(CsvHelper.Configuration.MemberMap)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.FormatAttribute.ApplyTo(CsvHelper.Configuration.ParameterMap)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.HasHeaderRecordAttribute">
            <summary>
            A value indicating if the CSV file has a header record.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.HasHeaderRecordAttribute.HasHeaderRecord">
            <summary>
            Gets a value indicating if the CSV file has a header record.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.HasHeaderRecordAttribute.#ctor(System.Boolean)">
            <summary>
            A value indicating if the CSV file has a header record.
            </summary>
            <param name="hasHeaderRecord">A value indicating if the CSV file has a header record.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.HasHeaderRecordAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.HeaderPrefixAttribute">
            <summary>
            Appends a prefix to the header of each field of the reference member.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.HeaderPrefixAttribute.Prefix">
            <summary>
            Gets the prefix.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.HeaderPrefixAttribute.Inherit">
            <summary>
            Gets a value indicating if the prefix should inherit parent prefixes.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.HeaderPrefixAttribute.#ctor">
            <summary>
            Appends a prefix to the header of each field of the reference member.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.HeaderPrefixAttribute.#ctor(System.String)">
            <summary>
            Appends a prefix to the header of each field of the reference member.
            </summary>
            <param name="prefix">The prefix.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.HeaderPrefixAttribute.#ctor(System.Boolean)">
            <summary>
            Appends a prefix to the header of each field of the reference member.
            </summary>
            <param name="inherit">Inherits parent object prefixes.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.HeaderPrefixAttribute.#ctor(System.String,System.Boolean)">
            <summary>
            Appends a prefix to the header of each field of the reference member.
            </summary>
            <param name="prefix">The prefix.</param>
            <param name="inherit">Inherits parent object prefixes.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.HeaderPrefixAttribute.ApplyTo(CsvHelper.Configuration.MemberReferenceMap)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.HeaderPrefixAttribute.ApplyTo(CsvHelper.Configuration.ParameterReferenceMap)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.IClassMapper">
            <summary>
            Defines methods to enable pluggable configuration.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.IClassMapper.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <summary>
            Applies configuration.
            </summary>
            <param name="configuration">The configuration to apply to.</param>
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.IgnoreAttribute">
            <summary>
            Ignore the member when reading and writing.
            If this member has already been mapped as a reference
            member, either by a class map, or by automapping, calling
            this method will not ignore all the child members down the
            tree that have already been mapped.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.IgnoreAttribute.ApplyTo(CsvHelper.Configuration.MemberMap)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.IgnoreAttribute.ApplyTo(CsvHelper.Configuration.MemberReferenceMap)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.IgnoreAttribute.ApplyTo(CsvHelper.Configuration.ParameterMap)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.IgnoreBaseAttribute">
            <summary>
            Ignores base classes when auto mapping.
            </summary>
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.IgnoreBlankLinesAttribute">
            <summary>
            A value indicating if blank lines should be ignored when reading.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.IgnoreBlankLinesAttribute.IgnoreBlankLines">
            <summary>
            Gets a value indicating if blank lines should be ignored when reading.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.IgnoreBlankLinesAttribute.#ctor(System.Boolean)">
            <summary>
            A value indicating if blank lines should be ignored when reading.
            </summary>
            <param name="ignoreBlankLines">The Ignore Blank Lines Flag.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.IgnoreBlankLinesAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.IgnoreReferencesAttribute">
            <summary>
            Gets a value indicating whether references
            should be ignored when auto mapping. <c>true</c> to ignore
            references, otherwise <c>false</c>. Default is false.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.IgnoreReferencesAttribute.IgnoreReferences">
            <summary>
            Gets a value indicating whether references
            should be ignored when auto mapping. <c>true</c> to ignore
            references, otherwise <c>false</c>. Default is false.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.IgnoreReferencesAttribute.#ctor(System.Boolean)">
            <summary>
            Gets a value indicating whether references
            should be ignored when auto mapping. <c>true</c> to ignore
            references, otherwise <c>false</c>. Default is false.
            </summary>
            <param name="ignoreReferences">Ignore references value.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.IgnoreReferencesAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.IMemberMapper">
            <summary>
            Defines methods to enable pluggable configuration of member mapping.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.IMemberMapper.ApplyTo(CsvHelper.Configuration.MemberMap)">
            <summary>
            Applies configuration to the given <see cref="T:CsvHelper.Configuration.MemberMap"/>.
            </summary>
            <param name="memberMap">The member map.</param>
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.IMemberReferenceMapper">
            <summary>
            Defines methods to enable pluggable configuration of member reference mapping.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.IMemberReferenceMapper.ApplyTo(CsvHelper.Configuration.MemberReferenceMap)">
            <summary>
            Applies configuration to the given <see cref="T:CsvHelper.Configuration.MemberReferenceMap" />.
            </summary>
            <param name="referenceMap">The reference map.</param>
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.IncludePrivateMembersAttribute">
            <summary>
            A value indicating if private member should be read from and written to.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.IncludePrivateMembersAttribute.IncludePrivateMembers">
            <summary>
            Gets a value indicating if private member should be read from and written to.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.IncludePrivateMembersAttribute.#ctor(System.Boolean)">
            <summary>
            A value indicating if private member should be read from and written to.
            </summary>
            <param name="includePrivateMembers">The Include Private Members Flag.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.IncludePrivateMembersAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.IndexAttribute">
            <summary>
            When reading, is used to get the field at
            the given index. When writing, the fields
            will be written in the order of the field
            indexes.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.IndexAttribute.Index">
            <summary>
            Gets the index.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.IndexAttribute.IndexEnd">
            <summary>
            Gets the index end.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.IndexAttribute.#ctor(System.Int32,System.Int32)">
            <summary>
            When reading, is used to get the field at
            the given index. When writing, the fields
            will be written in the order of the field
            indexes.
            </summary>
            <param name="index">The index.</param>
            <param name="indexEnd">The index end.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.IndexAttribute.ApplyTo(CsvHelper.Configuration.MemberMap)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.IndexAttribute.ApplyTo(CsvHelper.Configuration.ParameterMap)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.InjectionCharactersAttribute">
            <summary>
            Gets the characters that are used for injection attacks.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.InjectionCharactersAttribute.InjectionCharacters">
            <summary>
            Gets the characters that are used for injection attacks.
            Default is '=', '@', '+', '-', '\t', '\r'.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.InjectionCharactersAttribute.#ctor(System.String)">
            <summary>
            Gets the characters that are used for injection attacks.
            </summary>
            <param name="injectionCharacters"></param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.InjectionCharactersAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.InjectionEscapeCharacterAttribute">
            <summary>
            The character used to escape a detected injection.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.InjectionEscapeCharacterAttribute.InjectionEscapeCharacter">
            <summary>
            The character used to escape a detected injection.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.InjectionEscapeCharacterAttribute.#ctor(System.Char)">
            <summary>
            The character used to escape a detected injection.
            </summary>
            <param name="injectionEscapeCharacter"></param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.InjectionEscapeCharacterAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.InjectionOptionsAttribute">
            <summary>
            The injection options.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.InjectionOptionsAttribute.InjectionOptions">
            <summary>
            The injection options.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.InjectionOptionsAttribute.#ctor(CsvHelper.Configuration.InjectionOptions)">
            <summary>
            The injection options.
            </summary>
            <param name="injectionOptions"></param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.InjectionOptionsAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.IParameterMapper">
            <summary>
            Defines methods to enable pluggable configuration of parameter mapping.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.IParameterMapper.ApplyTo(CsvHelper.Configuration.ParameterMap)">
            <summary>
            Applies configuration to the given <see cref="T:CsvHelper.Configuration.ParameterMap"/>.
            </summary>
            <param name="parameterMap">The parameter map.</param>
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.IParameterReferenceMapper">
            <summary>
            Defines methods to enable pluggable configuration of parameter reference mapping.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.IParameterReferenceMapper.ApplyTo(CsvHelper.Configuration.ParameterReferenceMap)">
            <summary>
            Applies configuration to the given <see cref="T:CsvHelper.Configuration.ParameterReferenceMap" />.
            </summary>
            <param name="referenceMap">The reference map.</param>
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.LineBreakInQuotedFieldIsBadDataAttribute">
            <summary>
            A value indicating if a line break found in a quote field should
            be considered bad data. <c>true</c> to consider a line break bad data, otherwise <c>false</c>.
            Defaults to false.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.LineBreakInQuotedFieldIsBadDataAttribute.LineBreakInQuotedFieldIsBadData">
            <summary>
            A value indicating if a line break found in a quote field should
            be considered bad data. <c>true</c> to consider a line break bad data, otherwise <c>false</c>.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.LineBreakInQuotedFieldIsBadDataAttribute.#ctor(System.Boolean)">
            <summary>
            A value indicating if a line break found in a quote field should
            be considered bad data. <c>true</c> to consider a line break bad data, otherwise <c>false</c>.
            </summary>
            <param name="lineBreakInQuotedFieldIsBadData"></param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.LineBreakInQuotedFieldIsBadDataAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.MaxFieldSizeAttribute">
            <summary>
            Gets or sets the maximum size of a field.
            Defaults to 0, indicating maximum field size is not checked.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.MaxFieldSizeAttribute.MaxFieldSize">
            <summary>
            Gets or sets the maximum size of a field.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.MaxFieldSizeAttribute.#ctor(System.Double)">
            <summary>
            Gets or sets the maximum size of a field.
            </summary>
            <param name="maxFieldSize"></param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.MaxFieldSizeAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.MemberTypesAttribute">
            <summary>
            The member types that are used when auto mapping.
            MemberTypes are flags, so you can choose more than one.
            Default is Properties.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.MemberTypesAttribute.MemberTypes">
            <summary>
            The member types that are used when auto mapping.
            MemberTypes are flags, so you can choose more than one.
            Default is Properties.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.MemberTypesAttribute.#ctor(CsvHelper.Configuration.MemberTypes)">
            <summary>
            The member types that are used when auto mapping.
            MemberTypes are flags, so you can choose more than one.
            Default is Properties.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.MemberTypesAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.ModeAttribute">
            <summary>
            The mode.
            See <see cref="T:CsvHelper.CsvMode"/> for more details.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.ModeAttribute.Mode">
            <summary>
            The mode.
            See <see cref="T:CsvHelper.CsvMode"/> for more details.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.ModeAttribute.#ctor(CsvHelper.CsvMode)">
            <summary>
            The mode.
            See <see cref="T:CsvHelper.CsvMode"/> for more details.
            </summary>
            <param name="mode"></param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.ModeAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.NameAttribute">
            <summary>
            When reading, is used to get the field
            at the index of the name if there was a
            header specified. It will look for the
            first name match in the order listed.
            When writing, sets the name of the 
            field in the header record.
            The first name will be used.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.NameAttribute.Names">
            <summary>
            Gets the names.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.NameAttribute.#ctor(System.String)">
            <summary>
            When reading, is used to get the field
            at the index of the name if there was a
            header specified. It will look for the
            first name match in the order listed.
            When writing, sets the name of the 
            field in the header record.
            The first name will be used.
            </summary>
            <param name="name">The name</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.NameAttribute.#ctor(System.String[])">
            <summary>
            When reading, is used to get the field
            at the index of the name if there was a
            header specified. It will look for the
            first name match in the order listed.
            When writing, sets the name of the 
            field in the header record.
            The first name will be used.
            </summary>
            <param name="names">The names.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.NameAttribute.ApplyTo(CsvHelper.Configuration.MemberMap)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.NameAttribute.ApplyTo(CsvHelper.Configuration.ParameterMap)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.NameIndexAttribute">
            <summary>
            When reading, is used to get the 
            index of the name used when there 
            are multiple names that are the same.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.NameIndexAttribute.NameIndex">
            <summary>
            The name index.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.NameIndexAttribute.#ctor(System.Int32)">
            <summary>
            When reading, is used to get the 
            index of the name used when there 
            are multiple names that are the same.
            </summary>
            <param name="nameIndex">The name index.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.NameIndexAttribute.ApplyTo(CsvHelper.Configuration.MemberMap)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.NameIndexAttribute.ApplyTo(CsvHelper.Configuration.ParameterMap)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.NewLineAttribute">
            <summary>
            The newline string to use. Default is \r\n (CRLF).
            When writing, this value is always used.
            When reading, this value is only used if explicitly set.
            If not set, the parser uses one of \r\n, \r, or \n.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.NewLineAttribute.NewLine">
            The newline string to use. Default is \r\n (CRLF).
            When writing, this value is always used.
            When reading, this value is only used if explicitly set.
            If not set, the parser uses one of \r\n, \r, or \n.
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.NewLineAttribute.#ctor(System.String)">
            The newline string to use. Default is \r\n (CRLF).
            When writing, this value is always used.
            When reading, this value is only used if explicitly set.
            If not set, the parser uses one of \r\n, \r, or \n.
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.NewLineAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.NullValuesAttribute">
            <summary>
            The string values used to represent null when converting.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.NullValuesAttribute.NullValues">
            <summary>
            Gets the null values.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.NullValuesAttribute.#ctor(System.String)">
            <summary>
            The string values used to represent null when converting.
            </summary>
            <param name="nullValue">The null values.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.NullValuesAttribute.#ctor(System.String[])">
            <summary>
            The string values used to represent null when converting.
            </summary>
            <param name="nullValues">The null values.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.NullValuesAttribute.ApplyTo(CsvHelper.Configuration.MemberMap)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.NullValuesAttribute.ApplyTo(CsvHelper.Configuration.ParameterMap)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.NumberStylesAttribute">
            <summary>
            The <see cref="P:CsvHelper.Configuration.Attributes.NumberStylesAttribute.NumberStyles"/> to use when type converting.
            This is used when doing any number conversions.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.NumberStylesAttribute.NumberStyles">
            <summary>
            Gets the number styles.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.NumberStylesAttribute.#ctor(System.Globalization.NumberStyles)">
            <summary>
            The <see cref="P:CsvHelper.Configuration.Attributes.NumberStylesAttribute.NumberStyles"/> to use when type converting.
            This is used when doing any number conversions.
            </summary>
            <param name="numberStyles">The number styles.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.NumberStylesAttribute.ApplyTo(CsvHelper.Configuration.MemberMap)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.NumberStylesAttribute.ApplyTo(CsvHelper.Configuration.ParameterMap)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.OptionalAttribute">
            <summary>
            Ignore the member when reading if no matching field name can be found.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.OptionalAttribute.ApplyTo(CsvHelper.Configuration.MemberMap)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.OptionalAttribute.ApplyTo(CsvHelper.Configuration.ParameterMap)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.ProcessFieldBufferSizeAttribute">
            <summary>
            The size of the buffer used when processing fields.
            Default is 1024.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.ProcessFieldBufferSizeAttribute.ProcessFieldBufferSize">
            <summary>
            The size of the buffer used when processing fields.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.ProcessFieldBufferSizeAttribute.#ctor(System.Int32)">
            <summary>
            The size of the buffer used when processing fields.
            </summary>
            <param name="processFieldBufferSize"></param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.ProcessFieldBufferSizeAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.QuoteAttribute">
            <summary>
            The character used to quote fields.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.QuoteAttribute.Quote">
            <summary>
            Gets the character used to quote fields.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.QuoteAttribute.#ctor(System.Char)">
            <summary>
            The character used to quote fields.
            </summary>
            <param name="quote">The quote character.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.QuoteAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.TrimOptionsAttribute">
            <summary>
            The fields trimming options.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.TrimOptionsAttribute.TrimOptions">
            <summary>
            Gets the fields trimming options.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.TrimOptionsAttribute.#ctor(CsvHelper.Configuration.TrimOptions)">
            <summary>
            The fields trimming options.
            </summary>
            <param name="trimOptions">The TrimOptions.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.TrimOptionsAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.TypeConverterAttribute">
            <summary>
            Specifies the <see cref="P:CsvHelper.Configuration.Attributes.TypeConverterAttribute.TypeConverter"/> to use
            when converting the member to and from a CSV field.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.TypeConverterAttribute.TypeConverter">
            <summary>
            Gets the type converter.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.TypeConverterAttribute.#ctor(System.Type)">
            <summary>
            Specifies the <see cref="P:CsvHelper.Configuration.Attributes.TypeConverterAttribute.TypeConverter"/> to use
            when converting the member to and from a CSV field.
            </summary>
            <param name="typeConverterType">The type of the <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/>.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.TypeConverterAttribute.#ctor(System.Type,System.Object[])">
            <summary>
            Specifies the <see cref="P:CsvHelper.Configuration.Attributes.TypeConverterAttribute.TypeConverter"/> to use
            when converting the member to and from a CSV field.
            </summary>
            <param name="typeConverterType">The type of the <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/>.</param>
            <param name="constructorArgs">Type constructor arguments for the type converter.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.TypeConverterAttribute.ApplyTo(CsvHelper.Configuration.MemberMap)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.TypeConverterAttribute.ApplyTo(CsvHelper.Configuration.ParameterMap)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.UseNewObjectForNullReferenceMembersAttribute">
            <summary>
            Gets a value indicating that during writing if a new 
            object should be created when a reference member is null.
            True to create a new object and use it's defaults for the
            fields, or false to leave the fields empty for all the
            reference member's member.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.UseNewObjectForNullReferenceMembersAttribute.UseNewObjectForNullReferenceMembers">
            <summary>
            Gets a value indicating that during writing if a new 
            object should be created when a reference member is null.
            True to create a new object and use it's defaults for the
            fields, or false to leave the fields empty for all the
            reference member's member.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.UseNewObjectForNullReferenceMembersAttribute.#ctor(System.Boolean)">
            <summary>
            Gets a value indicating that during writing if a new 
            object should be created when a reference member is null.
            True to create a new object and use it's defaults for the
            fields, or false to leave the fields empty for all the
            reference member's member.
            </summary>
            <param name="useNewObjectForNullReferenceMembers">The value.</param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.UseNewObjectForNullReferenceMembersAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.Attributes.WhiteSpaceCharsAttribute">
            <summary>
            Characters considered whitespace.
            Used when trimming fields.
            Default is [' '].
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.Attributes.WhiteSpaceCharsAttribute.WhiteSpaceChars">
            <summary>
            Characters considered whitespace.
            Used when trimming fields.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.WhiteSpaceCharsAttribute.#ctor(System.String)">
            <summary>
            Characters considered whitespace.
            Used when trimming fields.
            </summary>
            <param name="whiteSpaceChars"></param>
        </member>
        <member name="M:CsvHelper.Configuration.Attributes.WhiteSpaceCharsAttribute.ApplyTo(CsvHelper.Configuration.CsvConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.Configuration.ClassMap">
            <summary>
             Maps class members to CSV fields.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ClassMap.ClassType">
            <summary>
            The type of the class this map is for.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ClassMap.ParameterMaps">
            <summary>
            The class constructor parameter mappings.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ClassMap.MemberMaps">
            <summary>
            The class member mappings.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ClassMap.ReferenceMaps">
            <summary>
            The class member reference mappings.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMap.#ctor(System.Type)">
            <summary>
            Allow only internal creation of CsvClassMap.
            </summary>
            <param name="classType">The type of the class this map is for.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMap.Map(System.Type,System.Reflection.MemberInfo,System.Boolean)">
            <summary>
            Maps a member to a CSV field.
            </summary>
            <param name="classType">The type of the class this map is for. This may not be the same type
            as the member.DeclaringType or the current ClassType due to nested member mappings.</param>
            <param name="member">The member to map.</param>
            <param name="useExistingMap">If true, an existing map will be used if available.
            If false, a new map is created for the same member.</param>
            <returns>The member mapping.</returns>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMap.Map">
            <summary>
            Maps a non-member to a CSV field. This allows for writing
            data that isn't mapped to a class member.
            </summary>
            <returns>The member mapping.</returns>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMap.References(System.Type,System.Reflection.MemberInfo,System.Object[])">
            <summary>
            Maps a member to another class map.
            </summary>
            <param name="classMapType">The type of the class map.</param>
            <param name="member">The member.</param>
            <param name="constructorArgs">Constructor arguments used to create the reference map.</param>
            <returns>The reference mapping for the member.</returns>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMap.Parameter(System.String)">
            <summary>
            Maps a constructor parameter to a CSV field.
            </summary>
            <param name="name">The name of the constructor parameter.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMap.Parameter(System.Func{System.Reflection.ConstructorInfo},System.String)">
            <summary>
            Maps a constructor parameter to a CSV field.
            </summary>
            <param name="getConstructor">A function that returns the <see cref="T:System.Reflection.ConstructorInfo"/> for the constructor.</param>
            <param name="name">The name of the constructor parameter.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMap.Parameter(System.Reflection.ConstructorInfo,System.Reflection.ParameterInfo)">
            <summary>
            Maps a constructor parameter to a CSV field.
            </summary>
            <param name="constructor">The <see cref="T:System.Reflection.ConstructorInfo"/> for the constructor.</param>
            <param name="parameter">The <see cref="T:System.Reflection.ParameterInfo"/> for the constructor parameter.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMap.AutoMap(System.Globalization.CultureInfo)">
            <summary>
            Auto maps all members for the given type. If a member
            is mapped again it will override the existing map.
            </summary>
            <param name="culture">The culture.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMap.AutoMap(CsvHelper.Configuration.CsvConfiguration)">
            <summary>
            Auto maps all members for the given type. If a member 
            is mapped again it will override the existing map.
            </summary>
            <param name="configuration">The configuration.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMap.AutoMap(CsvHelper.CsvContext)">
            <summary>
            Auto maps all members for the given type. If a member 
            is mapped again it will override the existing map.
            </summary>
            <param name="context">The context.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMap.GetMaxIndex(System.Boolean)">
            <summary>
            Get the largest index for the
            members and references.
            </summary>
            <returns>The max index.</returns>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMap.ReIndex(System.Int32)">
            <summary>
            Resets the indexes based on the given start index.
            </summary>
            <param name="indexStart">The index start.</param>
            <returns>The last index + 1.</returns>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMap.AutoMapMembers(CsvHelper.Configuration.ClassMap,CsvHelper.CsvContext,System.Collections.Generic.LinkedList{System.Type},System.Int32)">
            <summary>
            Auto maps the given map and checks for circular references as it goes.
            </summary>
            <param name="map">The map to auto map.</param>
            <param name="context">The context.</param>
            <param name="mapParents">The list of parents for the map.</param>
            <param name="indexStart">The index starting point.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMap.AutoMapConstructorParameters(CsvHelper.Configuration.ClassMap,CsvHelper.CsvContext,System.Collections.Generic.LinkedList{System.Type},System.Int32)">
            <summary>
            Auto maps the given map using constructor parameters.
            </summary>
            <param name="map">The map.</param>
            <param name="context">The context.</param>
            <param name="mapParents">The list of parents for the map.</param>
            <param name="indexStart">The index starting point.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMap.CheckForCircularReference(System.Type,System.Collections.Generic.LinkedList{System.Type})">
            <summary>
            Checks for circular references.
            </summary>
            <param name="type">The type to check for.</param>
            <param name="mapParents">The list of parents to check against.</param>
            <returns>A value indicating if a circular reference was found.
            True if a circular reference was found, otherwise false.</returns>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMap.GetGenericType">
            <summary>
            Gets the generic type for this class map.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMap.ApplyAttributes(CsvHelper.Configuration.ParameterMap)">
            <summary>
            Applies attribute configurations to the map.
            </summary>
            <param name="parameterMap">The parameter map.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMap.ApplyAttributes(CsvHelper.Configuration.ParameterReferenceMap)">
            <summary>
            Applies attribute configurations to the map.
            </summary>
            <param name="referenceMap">The parameter reference map.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMap.ApplyAttributes(CsvHelper.Configuration.MemberMap)">
            <summary>
            Applies attribute configurations to the map.
            </summary>
            <param name="memberMap">The member map.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMap.ApplyAttributes(CsvHelper.Configuration.MemberReferenceMap)">
            <summary>
            Applies attribute configurations to the map.
            </summary>
            <param name="referenceMap">The member reference map.</param>
        </member>
        <member name="T:CsvHelper.Configuration.IHasMap`1">
            <summary>
            Has mapping capabilities.
            </summary>
            <typeparam name="TClass">The class type.</typeparam>
        </member>
        <member name="M:CsvHelper.Configuration.IHasMap`1.Map``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Boolean)">
            <summary>
            Maps a member to a CSV field.
            </summary>
            <param name="expression">The member to map.</param>
            <param name="useExistingMap">If true, an existing map will be used if available.
            If false, a new map is created for the same member.</param>
            <returns>The member mapping.</returns>
        </member>
        <member name="T:CsvHelper.Configuration.IHasMapOptions`2">
            <summary>
            Options after a mapping call.
            </summary>
            <typeparam name="TClass">The class type.</typeparam>
            <typeparam name="TMember">The member type.</typeparam>
        </member>
        <member name="T:CsvHelper.Configuration.IHasTypeConverter`2">
            <summary>
            Has type converter capabilities.
            </summary>
            <typeparam name="TClass">The class type.</typeparam>
            <typeparam name="TMember">The member type.</typeparam>
        </member>
        <member name="M:CsvHelper.Configuration.IHasTypeConverter`2.TypeConverter(CsvHelper.TypeConversion.ITypeConverter)">
            <summary>
            Specifies the <see cref="M:CsvHelper.Configuration.IHasTypeConverter`2.TypeConverter(CsvHelper.TypeConversion.ITypeConverter)"/> to use
            when converting the member to and from a CSV field.
            </summary>
            <param name="typeConverter">The TypeConverter to use.</param>
        </member>
        <member name="M:CsvHelper.Configuration.IHasTypeConverter`2.TypeConverter``1">
            <summary>
            Specifies the <see cref="M:CsvHelper.Configuration.IHasTypeConverter`2.TypeConverter(CsvHelper.TypeConversion.ITypeConverter)"/> to use
            when converting the member to and from a CSV field.
            </summary>
            <typeparam name="TConverter">The <see cref="T:System.Type"/> of the 
            <see cref="M:CsvHelper.Configuration.IHasTypeConverter`2.TypeConverter(CsvHelper.TypeConversion.ITypeConverter)"/> to use.</typeparam>
        </member>
        <member name="T:CsvHelper.Configuration.IHasTypeConverterOptions`2">
            <summary>
            Options after a type converter call.
            </summary>
            <typeparam name="TClass">The class type.</typeparam>
            <typeparam name="TMember">The member type.</typeparam>
        </member>
        <member name="T:CsvHelper.Configuration.IHasIndex`2">
            <summary>
            Has index capabilities.
            </summary>
            <typeparam name="TClass">The class type.</typeparam>
            <typeparam name="TMember">The member type.</typeparam>
        </member>
        <member name="M:CsvHelper.Configuration.IHasIndex`2.Index(System.Int32,System.Int32)">
            <summary>
            When reading, is used to get the field at
            the given index. When writing, the fields
            will be written in the order of the field
            indexes.
            </summary>
            <param name="index">The index of the CSV field.</param>
            <param name="indexEnd">The end index used when mapping to an <see cref="T:System.Collections.IEnumerable"/> member.</param>
        </member>
        <member name="T:CsvHelper.Configuration.IHasIndexOptions`2">
            <summary>
            Options after an index call.
            </summary>
            <typeparam name="TClass">The class type.</typeparam>
            <typeparam name="TMember">The member type.</typeparam>
        </member>
        <member name="T:CsvHelper.Configuration.IHasOptional`2">
            <summary>
            Has optional capabilities.
            </summary>
            <typeparam name="TClass">The class type.</typeparam>
            <typeparam name="TMember">The member type.</typeparam>
        </member>
        <member name="M:CsvHelper.Configuration.IHasOptional`2.Optional">
            <summary>
            Ignore the member when reading if no matching field name can be found.
            </summary>
        </member>
        <member name="T:CsvHelper.Configuration.IHasOptionalOptions`2">
            <summary>
            Options after an optional call.
            </summary>
            <typeparam name="TClass">The class type.</typeparam>
            <typeparam name="TMember">The member type.</typeparam>
        </member>
        <member name="T:CsvHelper.Configuration.IHasName`2">
            <summary>
            Has name capabilities.
            </summary>
            <typeparam name="TClass">The class type.</typeparam>
            <typeparam name="TMember">The member type.</typeparam>
        </member>
        <member name="M:CsvHelper.Configuration.IHasName`2.Name(System.String[])">
            <summary>
            When reading, is used to get the field
            at the index of the name if there was a
            header specified. It will look for the
            first name match in the order listed.
            When writing, sets the name of the 
            field in the header record.
            The first name will be used.
            </summary>
            <param name="names">The possible names of the CSV field.</param>
        </member>
        <member name="T:CsvHelper.Configuration.IHasNameOptions`2">
            <summary>
            Options after a name call.
            </summary>
            <typeparam name="TClass">The class type.</typeparam>
            <typeparam name="TMember">The member type.</typeparam>
        </member>
        <member name="T:CsvHelper.Configuration.IHasNameIndex`2">
            <summary>
            Has name index capabilities.
            </summary>
            <typeparam name="TClass">The class type.</typeparam>
            <typeparam name="TMember">The member type.</typeparam>
        </member>
        <member name="M:CsvHelper.Configuration.IHasNameIndex`2.NameIndex(System.Int32)">
            <summary>
            When reading, is used to get the 
            index of the name used when there 
            are multiple names that are the same.
            </summary>
            <param name="index">The index of the name.</param>
        </member>
        <member name="T:CsvHelper.Configuration.IHasNameIndexOptions`2">
            <summary>
            Options after a name index call.
            </summary>
            <typeparam name="TClass">The class type.</typeparam>
            <typeparam name="TMember">The member type.</typeparam>
        </member>
        <member name="T:CsvHelper.Configuration.IHasConvertUsing`2">
            <summary>
            Has convert using capabilities.
            </summary>
            <typeparam name="TClass">The class type.</typeparam>
            <typeparam name="TMember">The member type.</typeparam>
        </member>
        <member name="M:CsvHelper.Configuration.IHasConvertUsing`2.ConvertUsing(CsvHelper.ConvertFromString{`1})">
            <summary>
            Specifies an expression to be used to convert data in the
            row to the member.
            </summary>
            <param name="convertExpression">The convert expression.</param>
        </member>
        <member name="M:CsvHelper.Configuration.IHasConvertUsing`2.ConvertUsing(CsvHelper.ConvertToString{`0})">
            <summary>
            Specifies an expression to be used to convert the object
            to a field.
            </summary>
            <param name="convertExpression">The convert expression.</param>
        </member>
        <member name="T:CsvHelper.Configuration.IHasDefault`2">
            <summary>
            Has default capabilities.
            </summary>
            <typeparam name="TClass">The class type.</typeparam>
            <typeparam name="TMember">The member type.</typeparam>
        </member>
        <member name="M:CsvHelper.Configuration.IHasDefault`2.Default(`1)">
            <summary>
            The default value that will be used when reading when
            the CSV field is empty.
            </summary>
            <param name="defaultValue">The default value.</param>
        </member>
        <member name="M:CsvHelper.Configuration.IHasDefault`2.Default(System.String)">
            <summary>
            The default value that will be used when reading when
            the CSV field is empty. This value is not type checked
            and will use a <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> to convert
            the field. This could potentially have runtime errors.
            </summary>
            <param name="defaultValue">The default value.</param>
        </member>
        <member name="T:CsvHelper.Configuration.IHasDefaultOptions`2">
            <summary>
            Options after a default call.
            </summary>
            <typeparam name="TClass">The class type.</typeparam>
            <typeparam name="TMember">The member type.</typeparam>
        </member>
        <member name="T:CsvHelper.Configuration.IHasConstant`2">
            <summary>
            Has constant capabilities.
            </summary>
            <typeparam name="TClass">The class type.</typeparam>
            <typeparam name="TMember">The member type.</typeparam>
        </member>
        <member name="M:CsvHelper.Configuration.IHasConstant`2.Constant(`1)">
            <summary>
            The constant value that will be used for every record when 
            reading and writing. This value will always be used no matter 
            what other mapping configurations are specified.
            </summary>
            <param name="value">The constant value.</param>
        </member>
        <member name="T:CsvHelper.Configuration.IHasValidate`2">
            <summary>
            Has validate capabilities.
            </summary>
            <typeparam name="TClass">The class type.</typeparam>
            <typeparam name="TMember">The member type.</typeparam>
        </member>
        <member name="M:CsvHelper.Configuration.IHasValidate`2.Validate(CsvHelper.Validate)">
            <summary>
            The validate expression that will be called on every field when reading.
            The expression should return true if the field is valid.
            If false is returned, a <see cref="T:CsvHelper.ValidationException"/>
            will be thrown.
            </summary>
            <param name="validateExpression">The validation expression.</param>
        </member>
        <member name="T:CsvHelper.Configuration.IBuildableClass`1">
            <summary>
            Has build capabilities.
            </summary>
            <typeparam name="TClass">The class type.</typeparam>
        </member>
        <member name="M:CsvHelper.Configuration.IBuildableClass`1.Build">
            <summary>
            Builds the <see cref="T:CsvHelper.Configuration.ClassMap`1"/>.
            </summary>
        </member>
        <member name="T:CsvHelper.Configuration.ClassMapCollection">
            <summary>
            Collection that holds CsvClassMaps for record types.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ClassMapCollection.Item(System.Type)">
            <summary>
            Gets the <see cref="T:CsvHelper.Configuration.ClassMap"/> for the specified record type.
            </summary>
            <value>
            The <see cref="T:CsvHelper.Configuration.ClassMap"/>.
            </value>
            <param name="type">The record type.</param>
            <returns>The <see cref="T:CsvHelper.Configuration.ClassMap"/> for the specified record type.</returns>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMapCollection.#ctor(CsvHelper.CsvContext)">
            <summary>
            Creates a new instance using the given configuration.
            </summary>
            <param name="context">The context.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMapCollection.Find``1">
            <summary>
            Finds the <see cref="T:CsvHelper.Configuration.ClassMap"/> for the specified record type.
            </summary>
            <typeparam name="T">The record type.</typeparam>
            <returns>The <see cref="T:CsvHelper.Configuration.ClassMap"/> for the specified record type.</returns>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMapCollection.Add(CsvHelper.Configuration.ClassMap)">
            <summary>
            Adds the specified map for it's record type. If a map
            already exists for the record type, the specified
            map will replace it.
            </summary>
            <param name="map">The map.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMapCollection.Remove(System.Type)">
            <summary>
            Removes the class map.
            </summary>
            <param name="classMapType">The class map type.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMapCollection.Clear">
            <summary>
            Removes all maps.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMapCollection.GetGenericCsvClassMapType(System.Type)">
            <summary>
            Goes up the inheritance tree to find the type instance of CsvClassMap{}.
            </summary>
            <param name="type">The type to traverse.</param>
            <returns>The type that is CsvClassMap{}.</returns>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMapCollection.SetMapDefaults(CsvHelper.Configuration.ClassMap)">
            <summary>
            Sets defaults for the mapping tree. The defaults used
            to be set inside the classes, but this didn't allow for
            the TypeConverter to be created from the Configuration's
            TypeConverterFactory.
            </summary>
            <param name="map">The map to set defaults on.</param>
        </member>
        <member name="T:CsvHelper.Configuration.ClassMap`1">
            <summary>
            Maps class members to CSV fields.
            </summary>
            <typeparam name="TClass">The <see cref="T:System.Type"/> of class to map.</typeparam>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMap`1.#ctor">
            <summary>
            Creates an instance of <see cref="T:CsvHelper.Configuration.ClassMap`1"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMap`1.Map``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Boolean)">
            <summary>
            Maps a member to a CSV field.
            </summary>
            <param name="expression">The member to map.</param>
            <param name="useExistingMap">If true, an existing map will be used if available.
            If false, a new map is created for the same member.</param>
            <returns>The member mapping.</returns>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMap`1.Map``1(System.Linq.Expressions.Expression{System.Func{``0,System.Object}},System.Boolean)">
            <summary>
            Maps a member to a CSV field.
            </summary>
            <param name="expression">The member to map.</param>
            <param name="useExistingMap">If true, an existing map will be used if available.
            If false, a new map is created for the same member.</param>
            <returns>The member mapping.</returns>
        </member>
        <member name="M:CsvHelper.Configuration.ClassMap`1.References``1(System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.Object[])">
            <summary>
            Meant for internal use only. 
            Maps a member to another class map. When this is used, accessing a property through
            sub-property mapping later won't work. You can only use one or the other. When using
            this, ConvertUsing will also not work.
            </summary>
            <typeparam name="TClassMap">The type of the class map.</typeparam>
            <param name="expression">The expression.</param>
            <param name="constructorArgs">Constructor arguments used to create the reference map.</param>
            <returns>The reference mapping for the member.</returns>
        </member>
        <member name="T:CsvHelper.Configuration.ConfigurationException">
            <summary>
            Represents configuration errors that occur.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.ConfigurationException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.Configuration.ConfigurationException"/> class.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.ConfigurationException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.Configuration.ConfigurationException"/> class
            with a specified error message.
            </summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ConfigurationException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.Configuration.ConfigurationException"/> class
            with a specified error message and a reference to the inner exception that 
            is the cause of this exception.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
        </member>
        <member name="T:CsvHelper.Configuration.ConfigurationFunctions">
            <summary>Holds the default callback methods for delegate members of <c>CsvHelper.Configuration.Configuration</c>.</summary>
        </member>
        <member name="M:CsvHelper.Configuration.ConfigurationFunctions.HeaderValidated(CsvHelper.HeaderValidatedArgs)">
            <summary>
            Throws a <see cref="T:CsvHelper.ValidationException"/> if <see name="HeaderValidatedArgs.InvalidHeaders"/> is not empty.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.ConfigurationFunctions.MissingFieldFound(CsvHelper.MissingFieldFoundArgs)">
            <summary>
            Throws a <c>MissingFieldException</c>.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.ConfigurationFunctions.BadDataFound(CsvHelper.BadDataFoundArgs)">
            <summary>
            Throws a <see cref="T:CsvHelper.BadDataException"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.ConfigurationFunctions.ReadingExceptionOccurred(CsvHelper.ReadingExceptionOccurredArgs)">
            <summary>
            Throws the given <see name="ReadingExceptionOccurredArgs.Exception"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.ConfigurationFunctions.ShouldQuote(CsvHelper.ShouldQuoteArgs)">
            <summary>
            Returns true if the field contains a <see cref="P:CsvHelper.Configuration.IWriterConfiguration.Quote"/>,
            starts with a space, ends with a space, contains \r or \n, or contains
            the <see cref="P:CsvHelper.Configuration.IWriterConfiguration.Delimiter"/>.
            </summary>
            <param name="args">The args.</param>
            <returns><c>true</c> if the field should be quoted, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:CsvHelper.Configuration.ConfigurationFunctions.PrepareHeaderForMatch(CsvHelper.PrepareHeaderForMatchArgs)">
            <summary>
            Returns the <see name="PrepareHeaderForMatchArgs.Header"/> as given.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.ConfigurationFunctions.ShouldUseConstructorParameters(CsvHelper.ShouldUseConstructorParametersArgs)">
            <summary>
            Returns <c>true</c> if <paramref name="args.ParameterType.ParameterType"/>:
            1. does not have a parameterless constructor
            2. has a constructor
            3. is not a value type
            4. is not a primitive
            5. is not an enum
            6. is not an interface
            7. TypeCode is an Object.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.ConfigurationFunctions.GetConstructor(CsvHelper.GetConstructorArgs)">
            <summary>
            Returns the type's constructor with the most parameters. 
            If two constructors have the same number of parameters, then
            there is no guarantee which one will be returned. If you have
            that situation, you should probably implement this function yourself.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.ConfigurationFunctions.GetDynamicPropertyName(CsvHelper.GetDynamicPropertyNameArgs)">
            <summary>
            Returns the header name ran through <see cref="M:CsvHelper.Configuration.ConfigurationFunctions.PrepareHeaderForMatch(CsvHelper.PrepareHeaderForMatchArgs)"/>.
            If no header exists, property names will be Field1, Field2, Field3, etc.
            </summary>
            <param name="args">The args.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ConfigurationFunctions.GetDelimiter(CsvHelper.Delegates.GetDelimiterArgs)">
            <summary>
            Detects the delimiter based on the given text.
            Return the detected delimiter or null if one wasn't found.
            </summary>
            <param name="args">The args.</param>
        </member>
        <member name="T:CsvHelper.Configuration.CsvConfiguration">
            <summary>
            Configuration used for reading and writing CSV data.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.AllowComments">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.BadDataFound">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.BufferSize">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.CacheFields">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.Comment">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.CountBytes">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.CultureInfo">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.Delimiter">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.DetectDelimiter">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.GetDelimiter">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.DetectDelimiterValues">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.DetectColumnCountChanges">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.DynamicPropertySort">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.Encoding">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.Escape">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.ExceptionMessagesContainRawData">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.GetConstructor">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.GetDynamicPropertyName">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.HasHeaderRecord">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.HeaderValidated">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.IgnoreBlankLines">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.IgnoreReferences">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.IncludePrivateMembers">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.InjectionCharacters">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.InjectionEscapeCharacter">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.InjectionOptions">
            <inheritdoc />
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.IsNewLineSet">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.LineBreakInQuotedFieldIsBadData">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.MaxFieldSize">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.MemberTypes">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.MissingFieldFound">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.Mode">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.NewLine">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.PrepareHeaderForMatch">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.ProcessFieldBufferSize">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.Quote">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.ReadingExceptionOccurred">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.ReferenceHeaderPrefix">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.ShouldQuote">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.ShouldSkipRecord">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.ShouldUseConstructorParameters">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.TrimOptions">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.UseNewObjectForNullReferenceMembers">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.Configuration.CsvConfiguration.WhiteSpaceChars">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.Configuration.CsvConfiguration.#ctor(System.Globalization.CultureInfo,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.Configuration.CsvConfiguration"/> class
            using the given <see cref="T:System.Globalization.CultureInfo"/>. Since <see cref="P:CsvHelper.Configuration.CsvConfiguration.Delimiter"/>
            uses <see cref="P:CsvHelper.Configuration.CsvConfiguration.CultureInfo"/> for it's default, the given <see cref="T:System.Globalization.CultureInfo"/>
            will be used instead.
            </summary>
            <param name="cultureInfo">The culture information.</param>
            <param name="attributesType">The type that contains the configuration attributes.
            This will call <see cref="M:CsvHelper.Configuration.CsvConfiguration.ApplyAttributes(System.Type)"/> automatically.</param>
        </member>
        <member name="M:CsvHelper.Configuration.CsvConfiguration.Validate">
            <summary>
            Validates the configuration.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.CsvConfiguration.ApplyAttributes``1">
            <summary>
            Applies class level attribute to configuration.
            </summary>
            <typeparam name="T">Type with attributes.</typeparam>
        </member>
        <member name="M:CsvHelper.Configuration.CsvConfiguration.ApplyAttributes(System.Type)">
            <summary>
            Applies class level attribute to configuration.
            </summary>
            <param name="type">Type with attributes.</param>
        </member>
        <member name="T:CsvHelper.Configuration.DefaultClassMap`1">
            <summary>
            A default <see cref="T:CsvHelper.Configuration.ClassMap`1"/> that can be used
            to create a class map dynamically.
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="T:CsvHelper.Configuration.InjectionOptions">
            <summary>
            Options for handling injection attacks.
            </summary>
        </member>
        <member name="F:CsvHelper.Configuration.InjectionOptions.None">
            <summary>
            No injection protection.
            </summary>
        </member>
        <member name="F:CsvHelper.Configuration.InjectionOptions.Escape">
            <summary>
            Escape injection characters.
            </summary>
        </member>
        <member name="F:CsvHelper.Configuration.InjectionOptions.Strip">
            <summary>
            Strip injection characters.
            </summary>
        </member>
        <member name="F:CsvHelper.Configuration.InjectionOptions.Exception">
            <summary>
            Throw an exception if injection characters are detected.
            </summary>
        </member>
        <member name="T:CsvHelper.Configuration.IParserConfiguration">
            <summary>
            Configuration used for the <see cref="T:CsvHelper.IParser"/>.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IParserConfiguration.CultureInfo">
            <summary>
            Gets the culture info used to read an write CSV files.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IParserConfiguration.CacheFields">
            <summary>
            Cache fields that are created when parsing.
            Default is false.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IParserConfiguration.NewLine">
            <summary>
            The newline string to use. Default is \r\n (CRLF).
            When writing, this value is always used.
            When reading, this value is only used if explicitly set.
            If not set, the parser uses one of \r\n, \r, or \n.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IParserConfiguration.IsNewLineSet">
            <summary>
            A value indicating if <see cref="P:CsvHelper.Configuration.IParserConfiguration.NewLine"/> was set.
            </summary>
            <value>
              <c>true</c> if <see cref="P:CsvHelper.Configuration.IParserConfiguration.NewLine"/> was set. <c>false</c> if <see cref="P:CsvHelper.Configuration.IParserConfiguration.NewLine"/> is the default.
            </value>
        </member>
        <member name="P:CsvHelper.Configuration.IParserConfiguration.Mode">
            <summary>
            The mode.
            See <see cref="T:CsvHelper.CsvMode"/> for more details.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IParserConfiguration.BufferSize">
            <summary>
            Gets the size of the buffer
            used for parsing and writing CSV files.
            Default is 0x1000.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IParserConfiguration.ProcessFieldBufferSize">
            <summary>
            The size of the buffer used when processing fields.
            Default is 1024.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IParserConfiguration.CountBytes">
            <summary>
            Gets a value indicating whether the number of bytes should
            be counted while parsing. Default is false. This will slow down parsing
            because it needs to get the byte count of every char for the given encoding.
            The <see cref="P:CsvHelper.Configuration.IParserConfiguration.Encoding"/> needs to be set correctly for this to be accurate.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IParserConfiguration.Encoding">
            <summary>
            Gets the encoding used when counting bytes.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IParserConfiguration.BadDataFound">
            <summary>
            Gets the function that is called when bad field data is found. A field
            has bad data if it contains a quote and the field is not quoted (escaped).
            You can supply your own function to do other things like logging the issue
            instead of throwing an exception.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IParserConfiguration.MaxFieldSize">
            <summary>
            Gets or sets the maximum size of a field.
            Defaults to 0, indicating maximum field size is not checked.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IParserConfiguration.LineBreakInQuotedFieldIsBadData">
            <summary>
            Gets a value indicating if a line break found in a quote field should
            be considered bad data. <c>true</c> to consider a line break bad data, otherwise <c>false</c>.
            Defaults to false.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IParserConfiguration.Comment">
            <summary>
            Gets the character used to denote
            a line that is commented out. Default is '#'.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IParserConfiguration.AllowComments">
            <summary>
            Gets a value indicating if comments are allowed.
            <c>true</c> to allow commented out lines, otherwise <c>false</c>.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IParserConfiguration.IgnoreBlankLines">
            <summary>
            Gets a value indicating if blank lines
            should be ignored when reading.
            <c>true</c> to ignore, otherwise <c>false</c>. Default is true.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IParserConfiguration.Quote">
            <summary>
            Gets the character used to quote fields.
            Default is '"'.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IParserConfiguration.Delimiter">
            <summary>
            The delimiter used to separate fields.
            Default is <see cref="P:System.Globalization.TextInfo.ListSeparator"/>.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IParserConfiguration.DetectDelimiter">
            <summary>
            Detect the delimiter instead of using the delimiter from configuration.
            Default is <c>false</c>.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IParserConfiguration.GetDelimiter">
            <summary>
            Gets the function that is called when <see cref="P:CsvHelper.Configuration.IParserConfiguration.DetectDelimiter"/> is enabled.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IParserConfiguration.DetectDelimiterValues">
            <summary>
            The possible delimiter values used when detecting the delimiter.
            Default is [",", ";", "|", "\t"].
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IParserConfiguration.Escape">
            <summary>
            The character used to escape characters.
            Default is '"'.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IParserConfiguration.TrimOptions">
            <summary>
            Gets the field trimming options.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IParserConfiguration.WhiteSpaceChars">
            <summary>
            Characters considered whitespace.
            Used when trimming fields.
            Default is [' '].
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IParserConfiguration.ExceptionMessagesContainRawData">
            <summary>
            A value indicating if exception messages contain raw CSV data.
            <c>true</c> if exception contain raw CSV data, otherwise <c>false</c>.
            Default is <c>true</c>.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.IParserConfiguration.Validate">
            <summary>
            Validates the configuration.
            </summary>
        </member>
        <member name="T:CsvHelper.Configuration.IReaderConfiguration">
            <summary>
            Configuration used for the <see cref="T:CsvHelper.IReader"/>.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IReaderConfiguration.HasHeaderRecord">
            <summary>
            Gets a value indicating if the
            CSV file has a header record.
            Default is true.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IReaderConfiguration.HeaderValidated">
            <summary>
            Gets the function that is called when a header validation check is ran. The default function
            will throw a <see cref="T:CsvHelper.ValidationException"/> if there is no header for a given member mapping.
            You can supply your own function to do other things like logging the issue instead of throwing an exception.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IReaderConfiguration.MissingFieldFound">
            <summary>
            Gets the function that is called when a missing field is found. The default function will
            throw a <see cref="T:CsvHelper.MissingFieldException"/>. You can supply your own function to do other things
            like logging the issue instead of throwing an exception.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IReaderConfiguration.ReadingExceptionOccurred">
            <summary>
            Gets the function that is called when a reading exception occurs.
            The default function will re-throw the given exception. If you want to ignore
            reading exceptions, you can supply your own function to do other things like
            logging the issue.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IReaderConfiguration.PrepareHeaderForMatch">
            <summary>
            Prepares the header field for matching against a member name.
            The header field and the member name are both ran through this function.
            You should do things like trimming, removing whitespace, removing underscores,
            and making casing changes to ignore case.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IReaderConfiguration.ShouldUseConstructorParameters">
            <summary>
            Determines if constructor parameters should be used to create
            the class instead of the default constructor and members.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IReaderConfiguration.GetConstructor">
            <summary>
            Chooses the constructor to use for constructor mapping.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IReaderConfiguration.GetDynamicPropertyName">
            <summary>
            Gets the name to use for the property of the dynamic object.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IReaderConfiguration.IgnoreReferences">
            <summary>
            Gets a value indicating whether references
            should be ignored when auto mapping. <c>true</c> to ignore
            references, otherwise <c>false</c>. Default is false.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IReaderConfiguration.ShouldSkipRecord">
            <summary>
            Gets the callback that will be called to
            determine whether to skip the given record or not.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IReaderConfiguration.IncludePrivateMembers">
            <summary>
            Gets a value indicating if private
            member should be read from and written to.
            <c>true</c> to include private member, otherwise <c>false</c>. Default is false.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IReaderConfiguration.ReferenceHeaderPrefix">
            <summary>
            Gets a callback that will return the prefix for a reference header.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IReaderConfiguration.DetectColumnCountChanges">
            <summary>
            Gets a value indicating whether changes in the column
            count should be detected. If true, a <see cref="T:CsvHelper.BadDataException"/>
            will be thrown if a different column count is detected.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IReaderConfiguration.MemberTypes">
            <summary>
            Gets the member types that are used when auto mapping.
            MemberTypes are flags, so you can choose more than one.
            Default is Properties.
            </summary>
        </member>
        <member name="T:CsvHelper.Configuration.IWriterConfiguration">
            <summary>
            Configuration used for the <see cref="T:CsvHelper.IWriter"/>.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IWriterConfiguration.BufferSize">
            <summary>
            Gets the size of the buffer
            used for parsing and writing CSV files.
            Default is 0x1000.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IWriterConfiguration.Mode">
            <summary>
            The mode.
            See <see cref="T:CsvHelper.CsvMode"/> for more details.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IWriterConfiguration.Delimiter">
            <summary>
            Gets the delimiter used to separate fields.
            Default is ',';
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IWriterConfiguration.Quote">
            <summary>
            Gets the character used to quote fields.
            Default is '"'.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IWriterConfiguration.Escape">
            <summary>
            The character used to escape characters.
            Default is '"'.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IWriterConfiguration.TrimOptions">
            <summary>
            Gets the field trimming options.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IWriterConfiguration.InjectionOptions">
            <summary>
            Gets the injection options.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IWriterConfiguration.InjectionCharacters">
            <summary>
            Gets the characters that are used for injection attacks.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IWriterConfiguration.InjectionEscapeCharacter">
            <summary>
            Gets the character used to escape a detected injection.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IWriterConfiguration.NewLine">
            <summary>
            The newline string to use. Default is \r\n (CRLF).
            When writing, this value is always used.
            When reading, this value is only used if explicitly set. If not set,
            the parser uses one of \r\n, \r, or \n.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IWriterConfiguration.IsNewLineSet">
            <summary>
            A value indicating if <see cref="P:CsvHelper.Configuration.IWriterConfiguration.NewLine"/> was set.
            </summary>
            <value>
              <c>true</c> if <see cref="P:CsvHelper.Configuration.IWriterConfiguration.NewLine"/> was set. <c>false</c> if <see cref="P:CsvHelper.Configuration.IWriterConfiguration.NewLine"/> is the default.
            </value>
        </member>
        <member name="P:CsvHelper.Configuration.IWriterConfiguration.ShouldQuote">
            <summary>
            Gets a function that is used to determine if a field should get quoted 
            when writing.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IWriterConfiguration.CultureInfo">
            <summary>
            Gets the culture info used to read and write CSV files.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IWriterConfiguration.AllowComments">
            <summary>
            Gets a value indicating if comments are allowed.
            True to allow commented out lines, otherwise false.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IWriterConfiguration.Comment">
            <summary>
            Gets the character used to denote
            a line that is commented out. Default is '#'.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IWriterConfiguration.HasHeaderRecord">
            <summary>
            Gets a value indicating if the
            CSV file has a header record.
            Default is true.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IWriterConfiguration.IgnoreReferences">
            <summary>
            Gets a value indicating whether references
            should be ignored when auto mapping. True to ignore
            references, otherwise false. Default is false.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IWriterConfiguration.IncludePrivateMembers">
            <summary>
            Gets a value indicating if private
            member should be read from and written to.
            True to include private member, otherwise false. Default is false.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IWriterConfiguration.ReferenceHeaderPrefix">
            <summary>
            Gets a callback that will return the prefix for a reference header.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IWriterConfiguration.MemberTypes">
            <summary>
            Gets the member types that are used when auto mapping.
            MemberTypes are flags, so you can choose more than one.
            Default is Properties.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IWriterConfiguration.UseNewObjectForNullReferenceMembers">
            <summary>
            Gets a value indicating that during writing if a new 
            object should be created when a reference member is null.
            True to create a new object and use it's defaults for the
            fields, or false to leave the fields empty for all the
            reference member's member.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IWriterConfiguration.DynamicPropertySort">
            <summary>
            Gets the comparer used to order the properties
            of dynamic objects when writing. The default is null,
            which will preserve the order the object properties
            were created with.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.IWriterConfiguration.ExceptionMessagesContainRawData">
            <summary>
            A value indicating if exception messages contain raw CSV data.
            <c>true</c> if exception contain raw CSV data, otherwise <c>false</c>.
            Default is <c>true</c>.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.IWriterConfiguration.Validate">
            <summary>
            Validates the configuration.
            </summary>
        </member>
        <member name="T:CsvHelper.Configuration.MemberMap">
            <summary>
            Mapping info for a member to a CSV field.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMap.Data">
            <summary>
            Gets the member map data.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMap.TypeConverterOption">
            <summary>
            Type converter options.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap.CreateGeneric(System.Type,System.Reflection.MemberInfo)">
            <summary>
            Creates an instance of <see cref="T:CsvHelper.Configuration.MemberMap"/> using the given Type and <see cref="T:System.Reflection.MemberInfo"/>.
            </summary>
            <param name="classType">Type of the class the member being mapped belongs to.</param>
            <param name="member">The member being mapped.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap.Name(System.String[])">
            <summary>
            When reading, is used to get the field
            at the index of the name if there was a
            header specified. It will look for the
            first name match in the order listed.
            When writing, sets the name of the 
            field in the header record.
            The first name will be used.
            </summary>
            <param name="names">The possible names of the CSV field.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap.NameIndex(System.Int32)">
            <summary>
            When reading, is used to get the 
            index of the name used when there 
            are multiple names that are the same.
            </summary>
            <param name="index">The index of the name.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap.Index(System.Int32,System.Int32)">
            <summary>
            When reading, is used to get the field at
            the given index. When writing, the fields
            will be written in the order of the field
            indexes.
            </summary>
            <param name="index">The index of the CSV field.</param>
            <param name="indexEnd">The end index used when mapping to an <see cref="T:System.Collections.IEnumerable"/> member.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap.Ignore">
            <summary>
            Ignore the member when reading and writing.
            If this member has already been mapped as a reference
            member, either by a class map, or by automapping, calling
            this method will not ignore all the child members down the
            tree that have already been mapped.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap.Ignore(System.Boolean)">
            <summary>
            Ignore the member when reading and writing.
            If this member has already been mapped as a reference
            member, either by a class map, or by automapping, calling
            this method will not ignore all the child members down the
            tree that have already been mapped.
            </summary>
            <param name="ignore">True to ignore, otherwise false.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap.Default(System.Object,System.Boolean)">
            <summary>
            The default value that will be used when reading when
            the CSV field is empty.
            </summary>
            <param name="defaultValue">The default value.</param>
            <param name="useOnConversionFailure">Use default on conversion failure.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap.Constant(System.Object)">
            <summary>
            The constant value that will be used for every record when 
            reading and writing. This value will always be used no matter 
            what other mapping configurations are specified.
            </summary>
            <param name="constantValue">The constant value.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap.TypeConverter(CsvHelper.TypeConversion.ITypeConverter)">
            <summary>
            Specifies the <see cref="M:CsvHelper.Configuration.MemberMap.TypeConverter(CsvHelper.TypeConversion.ITypeConverter)"/> to use
            when converting the member to and from a CSV field.
            </summary>
            <param name="typeConverter">The TypeConverter to use.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap.TypeConverter``1">
            <summary>
            Specifies the <see cref="M:CsvHelper.Configuration.MemberMap.TypeConverter(CsvHelper.TypeConversion.ITypeConverter)"/> to use
            when converting the member to and from a CSV field.
            </summary>
            <typeparam name="TConverter">The <see cref="T:System.Type"/> of the 
            <see cref="M:CsvHelper.Configuration.MemberMap.TypeConverter(CsvHelper.TypeConversion.ITypeConverter)"/> to use.</typeparam>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap.Optional">
            <summary>
            Ignore the member when reading if no matching field name can be found.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap.Validate(CsvHelper.Validate)">
            <summary>
            Specifies an expression to be used to validate a field when reading.
            </summary>
            <param name="validateExpression"></param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap.Validate(CsvHelper.Validate,CsvHelper.ValidateMessage)">
            <summary>
            Specifies an expression to be used to validate a field when reading along with specified exception message.
            </summary>
            <param name="validateExpression"></param>
            <param name="validateMessageExpression"></param>
        </member>
        <member name="T:CsvHelper.Configuration.MemberMapCollection">
            <summary>
            A collection that holds <see cref="T:CsvHelper.Configuration.MemberMap"/>'s.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMapCollection.Count">
            <summary>
            Gets the number of elements contained in the <see cref="T:System.Collections.Generic.ICollection`1"/>.
            </summary>
            <returns>
            The number of elements contained in the <see cref="T:System.Collections.Generic.ICollection`1"/>.
            </returns>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMapCollection.IsReadOnly">
            <summary>
            Gets a value indicating whether the <see cref="T:System.Collections.Generic.ICollection`1"/> is read-only.
            </summary>
            <returns>
            true if the <see cref="T:System.Collections.Generic.ICollection`1"/> is read-only; otherwise, false.
            </returns>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.Configuration.MemberMapCollection"/> class.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapCollection.#ctor(System.Collections.Generic.IComparer{CsvHelper.Configuration.MemberMap})">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.Configuration.MemberMapCollection"/> class.
            </summary>
            <param name="comparer">The comparer to use when sorting the member maps.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapCollection.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>
            A <see cref="T:System.Collections.Generic.IEnumerator`1"/> that can be used to iterate through the collection.
            </returns>
            <filterpriority>1</filterpriority>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapCollection.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator"/> object that can be used to iterate through the collection.
            </returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapCollection.Add(CsvHelper.Configuration.MemberMap)">
            <summary>
            Adds an item to the <see cref="T:System.Collections.Generic.ICollection`1"/>.
            </summary>
            <param name="item">The object to add to the <see cref="T:System.Collections.Generic.ICollection`1"/>.
                            </param><exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.Generic.ICollection`1"/> is read-only.
                            </exception>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapCollection.AddRange(System.Collections.Generic.ICollection{CsvHelper.Configuration.MemberMap})">
            <summary>
            Adds a range of items to the <see cref="T:System.Collections.Generic.ICollection`1"/>.
            </summary>
            <param name="collection">The collection to add.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapCollection.Clear">
            <summary>
            Removes all items from the <see cref="T:System.Collections.Generic.ICollection`1"/>.
            </summary>
            <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.Generic.ICollection`1"/> is read-only. 
                            </exception>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapCollection.Contains(CsvHelper.Configuration.MemberMap)">
            <summary>
            Determines whether the <see cref="T:System.Collections.Generic.ICollection`1"/> contains a specific value.
            </summary>
            <returns>
            true if <paramref name="item"/> is found in the <see cref="T:System.Collections.Generic.ICollection`1"/>; otherwise, false.
            </returns>
            <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.ICollection`1"/>.
                            </param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapCollection.CopyTo(CsvHelper.Configuration.MemberMap[],System.Int32)">
            <summary>
            Copies the elements of the <see cref="T:System.Collections.Generic.ICollection`1"/> to an <see cref="T:System.Array"/>, starting at a particular <see cref="T:System.Array"/> index.
            </summary>
            <param name="array">The one-dimensional <see cref="T:System.Array"/> that is the destination of the elements copied from <see cref="T:System.Collections.Generic.ICollection`1"/>. The <see cref="T:System.Array"/> must have zero-based indexing.</param><param name="arrayIndex">The zero-based index in <paramref name="array"/> at which copying begins.</param><exception cref="T:System.ArgumentNullException"><paramref name="array"/> is null.</exception><exception cref="T:System.ArgumentOutOfRangeException"><paramref name="arrayIndex"/> is less than 0.</exception><exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.ICollection`1"/> is greater than the available space from <paramref name="arrayIndex"/> to the end of the destination <paramref name="array"/>.</exception>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapCollection.Remove(CsvHelper.Configuration.MemberMap)">
            <summary>
            Removes the first occurrence of a specific object from the <see cref="T:System.Collections.Generic.ICollection`1"/>.
            </summary>
            <returns>
            true if <paramref name="item"/> was successfully removed from the <see cref="T:System.Collections.Generic.ICollection`1"/>; otherwise, false. This method also returns false if <paramref name="item"/> is not found in the original <see cref="T:System.Collections.Generic.ICollection`1"/>.
            </returns>
            <param name="item">The object to remove from the <see cref="T:System.Collections.Generic.ICollection`1"/>.
                            </param><exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.Generic.ICollection`1"/> is read-only.
                            </exception>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapCollection.IndexOf(CsvHelper.Configuration.MemberMap)">
            <summary>
            Determines the index of a specific item in the <see cref="T:System.Collections.Generic.IList`1"/>.
            </summary>
            <returns>
            The index of <paramref name="item"/> if found in the list; otherwise, -1.
            </returns>
            <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.IList`1"/>.
                            </param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapCollection.Insert(System.Int32,CsvHelper.Configuration.MemberMap)">
            <summary>
            Inserts an item to the <see cref="T:System.Collections.Generic.IList`1"/> at the specified index.
            </summary>
            <param name="index">The zero-based index at which <paramref name="item"/> should be inserted.
                            </param><param name="item">The object to insert into the <see cref="T:System.Collections.Generic.IList`1"/>.
                            </param><exception cref="T:System.ArgumentOutOfRangeException"><paramref name="index"/> is not a valid index in the <see cref="T:System.Collections.Generic.IList`1"/>.
                            </exception><exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.Generic.IList`1"/> is read-only.
                            </exception>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapCollection.RemoveAt(System.Int32)">
            <summary>
            Removes the <see cref="T:System.Collections.Generic.IList`1"/> item at the specified index.
            </summary>
            <param name="index">The zero-based index of the item to remove.
                            </param><exception cref="T:System.ArgumentOutOfRangeException"><paramref name="index"/> is not a valid index in the <see cref="T:System.Collections.Generic.IList`1"/>.
                            </exception><exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.Generic.IList`1"/> is read-only.
                            </exception>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMapCollection.Item(System.Int32)">
            <summary>
            Gets or sets the element at the specified index.
            </summary>
            <returns>
            The element at the specified index.
            </returns>
            <param name="index">The zero-based index of the element to get or set.
                            </param><exception cref="T:System.ArgumentOutOfRangeException"><paramref name="index"/> is not a valid index in the <see cref="T:System.Collections.Generic.IList`1"/>.
                            </exception><exception cref="T:System.NotSupportedException">The member is set and the <see cref="T:System.Collections.Generic.IList`1"/> is read-only.
                            </exception>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapCollection.Find``1(System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            Finds the <see cref="T:CsvHelper.Configuration.MemberMap"/> using the given member expression.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> the member is on.</typeparam>
            <param name="expression">The member expression.</param>
            <returns>The <see cref="T:CsvHelper.Configuration.MemberMap"/> for the given expression, or null if not found.</returns>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapCollection.Find(System.Reflection.MemberInfo)">
            <summary>
            Finds the <see cref="T:CsvHelper.Configuration.MemberMap"/> using the given member.
            </summary>
            <param name="member">The member.</param>
            <returns>The <see cref="T:CsvHelper.Configuration.MemberMap"/> for the given expression, or null if not found.</returns>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapCollection.AddMembers(CsvHelper.Configuration.ClassMap)">
            <summary>
            Adds the members from the mapping. This will recursively
            traverse the mapping tree and add all members for
            reference maps.
            </summary>
            <param name="mapping">The mapping where the members are added from.</param>
        </member>
        <member name="T:CsvHelper.Configuration.MemberMapComparer">
            <summary>
            Used to compare <see cref="T:CsvHelper.Configuration.MemberMap"/>s.
            The order is by field index ascending. Any
            fields that don't have an index are pushed
            to the bottom.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapComparer.Compare(System.Object,System.Object)">
            <summary>
            Compares two objects and returns a value indicating whether one is less than, equal to, or greater than the other.
            </summary>
            <returns>
            Value 
                                Condition 
                                Less than zero 
                            <paramref name="x"/> is less than <paramref name="y"/>. 
                                Zero 
                            <paramref name="x"/> equals <paramref name="y"/>. 
                                Greater than zero 
                            <paramref name="x"/> is greater than <paramref name="y"/>. 
            </returns>
            <param name="x">The first object to compare. 
                            </param><param name="y">The second object to compare. 
                            </param><exception cref="T:System.ArgumentException">Neither <paramref name="x"/> nor <paramref name="y"/> implements the <see cref="T:System.IComparable"/> interface.
                                -or- 
                            <paramref name="x"/> and <paramref name="y"/> are of different types and neither one can handle comparisons with the other. 
                            </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapComparer.Compare(CsvHelper.Configuration.MemberMap,CsvHelper.Configuration.MemberMap)">
            <summary>
            Compares two objects and returns a value indicating whether one is less than, equal to, or greater than the other.
            </summary>
            <returns>
            Value 
                                Condition 
                                Less than zero
                            <paramref name="x"/> is less than <paramref name="y"/>.
                                Zero
                            <paramref name="x"/> equals <paramref name="y"/>.
                                Greater than zero
                            <paramref name="x"/> is greater than <paramref name="y"/>.
            </returns>
            <param name="x">The first object to compare.
                            </param><param name="y">The second object to compare.
                            </param>
        </member>
        <member name="T:CsvHelper.Configuration.MemberMapData">
            <summary>
            The configured data for the member map.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMapData.Type">
            <summary>
            Gets the member type.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMapData.Member">
            <summary>
            Gets the <see cref="T:System.Reflection.MemberInfo"/> that the data
            is associated with.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMapData.Names">
            <summary>
            Gets the list of column names.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMapData.NameIndex">
            <summary>
            Gets or sets the index of the name.
            This is used if there are multiple
            columns with the same names.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMapData.IsNameSet">
            <summary>
            Gets or sets a value indicating if the name was
            explicitly set. True if it was explicitly set,
            otherwise false.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMapData.Index">
            <summary>
            Gets or sets the column index.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMapData.IndexEnd">
            <summary>
            Gets or sets the index end. The Index end is used to specify a range for use
            with a collection member. Index is used as the start of the range, and IndexEnd
            is the end of the range.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMapData.IsIndexSet">
            <summary>
            Gets or sets a value indicating if the index was
            explicitly set. True if it was explicitly set,
            otherwise false.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMapData.TypeConverter">
            <summary>
            Gets or sets the type converter.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMapData.TypeConverterOptions">
            <summary>
            Gets or sets the type converter options.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMapData.Ignore">
            <summary>
            Gets or sets a value indicating whether the field should be ignored.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMapData.Default">
            <summary>
            Gets or sets the default value used when a CSV field is empty.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMapData.IsDefaultSet">
            <summary>
            Gets or sets a value indicating whether this instance is default value set.
            the default value was explicitly set. True if it was
            explicitly set, otherwise false.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMapData.UseDefaultOnConversionFailure">
            <summary>
            Gets or setse a value indicating if the default value should be used when
            a type conversion failure happens. <c>true</c> to use the default, otherwise
            <c>false</c>.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMapData.Constant">
            <summary>
            Gets or sets the constant value used for every record.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMapData.IsConstantSet">
            <summary>
            Gets or sets a value indicating if a constant was explicitly set.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMapData.ReadingConvertExpression">
            <summary>
            Gets or sets the expression used to convert data in the
            row to the member.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMapData.WritingConvertExpression">
            <summary>
            Gets or sets the expression to be used to convert the object
            to a field.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMapData.ValidateExpression">
            <summary>
            Gets or sets the expression use to validate a field.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMapData.ValidateMessageExpression">
            <summary>
            Gets or sets the expression used to get the validation message when validation fails.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberMapData.IsOptional">
            <summary>
            Gets or sets a value indicating if a field is optional.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapData.#ctor(System.Reflection.MemberInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.Configuration.MemberMapData"/> class.
            </summary>
            <param name="member">The member.</param>
        </member>
        <member name="T:CsvHelper.Configuration.MemberMapTypeConverterOption">
            <summary>
            Sets type converter options on a member map.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapTypeConverterOption.#ctor(CsvHelper.Configuration.MemberMap)">
            <summary>
            Creates a new instance using the given <see cref="T:CsvHelper.Configuration.MemberMap"/>.
            </summary>
            <param name="memberMap">The member map the options are being applied to.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapTypeConverterOption.CultureInfo(System.Globalization.CultureInfo)">
            <summary>
            The <see cref="M:CsvHelper.Configuration.MemberMapTypeConverterOption.CultureInfo(System.Globalization.CultureInfo)"/> used when type converting.
            This will override the global <see cref="P:CsvHelper.Configuration.CsvConfiguration.CultureInfo"/>
            setting.
            </summary>
            <param name="cultureInfo">The culture info.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapTypeConverterOption.DateTimeStyles(System.Globalization.DateTimeStyles)">
            <summary>
            The <see cref="M:CsvHelper.Configuration.MemberMapTypeConverterOption.DateTimeStyles(System.Globalization.DateTimeStyles)"/> to use when type converting.
            This is used when doing any <see cref="T:System.DateTime"/> conversions.
            </summary>
            <param name="dateTimeStyle">The date time style.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapTypeConverterOption.TimespanStyles(System.Globalization.TimeSpanStyles)">
            <summary>
            The <see cref="T:System.Globalization.TimeSpanStyles"/> to use when type converting.
            This is used when doing <see cref="T:System.TimeSpan"/> converting.
            </summary>
            <param name="timeSpanStyles">The time span styles.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapTypeConverterOption.NumberStyles(System.Globalization.NumberStyles)">
            <summary>
            The <see cref="M:CsvHelper.Configuration.MemberMapTypeConverterOption.NumberStyles(System.Globalization.NumberStyles)"/> to use when type converting.
            This is used when doing any number conversions.
            </summary>
            <param name="numberStyle"></param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapTypeConverterOption.Format(System.String[])">
            <summary>
            The string format to be used when type converting.
            </summary>
            <param name="formats">The format.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapTypeConverterOption.UriKind(System.UriKind)">
            <summary>
            The <see cref="M:CsvHelper.Configuration.MemberMapTypeConverterOption.UriKind(System.UriKind)"/> to use when converting.
            This is used when doing <see cref="T:System.Uri"/> conversions.
            </summary>
            <param name="uriKind">Kind of the URI.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapTypeConverterOption.BooleanValues(System.Boolean,System.Boolean,System.String[])">
            <summary>
            The string values used to represent a boolean when converting.
            </summary>
            <param name="isTrue">A value indicating whether true values or false values are being set.</param>
            <param name="clearValues">A value indication if the current values should be cleared before adding the new ones.</param>
            <param name="booleanValues">The string boolean values.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapTypeConverterOption.NullValues(System.String[])">
            <summary>
            The string values used to represent null when converting.
            </summary>
            <param name="nullValues">The values that represent null.</param>
            <returns></returns>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapTypeConverterOption.NullValues(System.Boolean,System.String[])">
            <summary>
            The string values used to represent null when converting.
            </summary>
            <param name="clearValues">A value indication if the current values should be cleared before adding the new ones.</param>
            <param name="nullValues">The values that represent null.</param>
            <returns></returns>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMapTypeConverterOption.EnumIgnoreCase(System.Boolean)">
            <summary>
            Ignore case when parsing enums.
            </summary>
            <param name="ignoreCase"><c>true</c> to ignore case, otherwise <c>false</c>.</param>
        </member>
        <member name="T:CsvHelper.Configuration.MemberMap`2">
            <summary>
            Mapping info for a member to a CSV field.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap`2.#ctor(System.Reflection.MemberInfo)">
            <summary>
            Creates a new <see cref="T:CsvHelper.Configuration.MemberMap"/> instance using the specified member.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap`2.Name(System.String[])">
            <summary>
            When reading, is used to get the field
            at the index of the name if there was a
            header specified. It will look for the
            first name match in the order listed.
            When writing, sets the name of the 
            field in the header record.
            The first name will be used.
            </summary>
            <param name="names">The possible names of the CSV field.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap`2.NameIndex(System.Int32)">
            <summary>
            When reading, is used to get the 
            index of the name used when there 
            are multiple names that are the same.
            </summary>
            <param name="index">The index of the name.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap`2.Index(System.Int32,System.Int32)">
            <summary>
            When reading, is used to get the field at
            the given index. When writing, the fields
            will be written in the order of the field
            indexes.
            </summary>
            <param name="index">The index of the CSV field.</param>
            <param name="indexEnd">The end index used when mapping to an <see cref="T:System.Collections.IEnumerable"/> member.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap`2.Ignore">
            <summary>
            Ignore the member when reading and writing.
            If this member has already been mapped as a reference
            member, either by a class map, or by automapping, calling
            this method will not ignore all the child members down the
            tree that have already been mapped.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap`2.Ignore(System.Boolean)">
            <summary>
            Ignore the member when reading and writing.
            If this member has already been mapped as a reference
            member, either by a class map, or by automapping, calling
            this method will not ignore all the child members down the
            tree that have already been mapped.
            </summary>
            <param name="ignore">True to ignore, otherwise false.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap`2.Default(`1,System.Boolean)">
            <summary>
            The default value that will be used when reading when
            the CSV field is empty.
            </summary>
            <param name="defaultValue">The default value.</param>
            <param name="useOnConversionFailure">Use default on conversion failure.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap`2.Default(System.String,System.Boolean)">
            <summary>
            The default value that will be used when reading when
            the CSV field is empty. This value is not type checked
            and will use a <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> to convert
            the field. This could potentially have runtime errors.
            </summary>
            <param name="defaultValue">The default value.</param>
            <param name="useOnConversionFailure">Use default on conversion failure.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap`2.Constant(`1)">
            <summary>
            The constant value that will be used for every record when 
            reading and writing. This value will always be used no matter 
            what other mapping configurations are specified.
            </summary>
            <param name="constantValue">The constant value.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap`2.TypeConverter(CsvHelper.TypeConversion.ITypeConverter)">
            <summary>
            Specifies the <see cref="M:CsvHelper.Configuration.MemberMap`2.TypeConverter(CsvHelper.TypeConversion.ITypeConverter)"/> to use
            when converting the member to and from a CSV field.
            </summary>
            <param name="typeConverter">The TypeConverter to use.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap`2.TypeConverter``1">
            <summary>
            Specifies the <see cref="M:CsvHelper.Configuration.MemberMap`2.TypeConverter(CsvHelper.TypeConversion.ITypeConverter)"/> to use
            when converting the member to and from a CSV field.
            </summary>
            <typeparam name="TConverter">The <see cref="T:System.Type"/> of the 
            <see cref="M:CsvHelper.Configuration.MemberMap`2.TypeConverter(CsvHelper.TypeConversion.ITypeConverter)"/> to use.</typeparam>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap`2.Convert(CsvHelper.ConvertFromString{`1})">
            <summary>
            Specifies an expression to be used to convert data in the
            row to the member.
            </summary>
            <param name="convertFromStringFunction">The convert expression.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap`2.Convert(CsvHelper.ConvertToString{`0})">
            <summary>
            Specifies an expression to be used to convert the object
            to a field.
            </summary>
            <param name="convertToStringFunction">The convert expression.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap`2.Optional">
            <summary>
            Ignore the member when reading if no matching field name can be found.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap`2.Validate(CsvHelper.Validate)">
            <summary>
            Specifies an expression to be used to validate a field when reading.
            </summary>
            <param name="validateExpression"></param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberMap`2.Validate(CsvHelper.Validate,CsvHelper.ValidateMessage)">
            <summary>
            Specifies an expression to be used to validate a field when reading along with specified exception message.
            </summary>
            <param name="validateExpression"></param>
            <param name="validateMessageExpression"></param>
        </member>
        <member name="T:CsvHelper.Configuration.MemberNameCollection">
            <summary>
            A collection that holds member names.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberNameCollection.Item(System.Int32)">
            <summary>
            Gets the name at the given index. If a prefix is set,
            it will be prepended to the name.
            </summary>
            <param name="index"></param>
            <returns></returns>
        </member>
        <member name="P:CsvHelper.Configuration.MemberNameCollection.Prefix">
            <summary>
            Gets the prefix to use for each name.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberNameCollection.Names">
            <summary>
            Gets the raw list of names without
            the prefix being prepended.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberNameCollection.Count">
            <summary>
            Gets the count.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.MemberNameCollection.Add(System.String)">
            <summary>
            Adds the given name to the collection.
            </summary>
            <param name="name">The name to add.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberNameCollection.Clear">
            <summary>
            Clears all names from the collection.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.MemberNameCollection.AddRange(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Adds a range of names to the collection.
            </summary>
            <param name="names">The range to add.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberNameCollection.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>
            A <see cref="T:System.Collections.Generic.IEnumerator`1"/> that can be used to iterate through the collection.
            </returns>
            <filterpriority>1</filterpriority>
        </member>
        <member name="M:CsvHelper.Configuration.MemberNameCollection.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator"/> object that can be used to iterate through the collection.
            </returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="T:CsvHelper.Configuration.MemberReferenceMap">
            <summary>
            Mapping info for a reference member mapping to a class.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberReferenceMap.Data">
            <summary>
            Gets the member reference map data.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.MemberReferenceMap.#ctor(System.Reflection.MemberInfo,CsvHelper.Configuration.ClassMap)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.Configuration.MemberReferenceMap"/> class.
            </summary>
            <param name="member">The member.</param>
            <param name="mapping">The <see cref="T:CsvHelper.Configuration.ClassMap"/> to use for the reference map.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberReferenceMap.Prefix(System.String,System.Boolean)">
            <summary>
            Appends a prefix to the header of each field of the reference member.
            </summary>
            <param name="prefix">The prefix to be prepended to headers of each reference member.</param>
            <param name="inherit">Inherit parent prefixes.</param>
            <returns>The current <see cref="T:CsvHelper.Configuration.MemberReferenceMap" /></returns>
        </member>
        <member name="M:CsvHelper.Configuration.MemberReferenceMap.GetMaxIndex">
            <summary>
            Get the largest index for the
            members and references.
            </summary>
            <returns>The max index.</returns>
        </member>
        <member name="T:CsvHelper.Configuration.MemberReferenceMapCollection">
            <summary>
            A collection that holds <see cref="T:CsvHelper.Configuration.MemberReferenceMap"/>'s.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberReferenceMapCollection.Count">
            <summary>Gets the number of elements contained in the <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
            <returns>The number of elements contained in the <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
        </member>
        <member name="P:CsvHelper.Configuration.MemberReferenceMapCollection.IsReadOnly">
            <summary>Gets a value indicating whether the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.</summary>
            <returns>true if the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only; otherwise, false.</returns>
        </member>
        <member name="P:CsvHelper.Configuration.MemberReferenceMapCollection.Item(System.Int32)">
            <summary>Gets or sets the element at the specified index.</summary>
            <returns>The element at the specified index.</returns>
            <param name="index">The zero-based index of the element to get or set.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="index" /> is not a valid index in the <see cref="T:System.Collections.Generic.IList`1" />.</exception>
            <exception cref="T:System.NotSupportedException">The member is set and the <see cref="T:System.Collections.Generic.IList`1" /> is read-only.</exception>
        </member>
        <member name="M:CsvHelper.Configuration.MemberReferenceMapCollection.GetEnumerator">
            <summary>Returns an enumerator that iterates through the collection.</summary>
            <returns>A <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can be used to iterate through the collection.</returns>
            <filterpriority>1</filterpriority>
        </member>
        <member name="M:CsvHelper.Configuration.MemberReferenceMapCollection.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary>
            <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="M:CsvHelper.Configuration.MemberReferenceMapCollection.Add(CsvHelper.Configuration.MemberReferenceMap)">
            <summary>Adds an item to the <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
            <param name="item">The object to add to the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
            <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.</exception>
        </member>
        <member name="M:CsvHelper.Configuration.MemberReferenceMapCollection.Clear">
            <summary>Removes all items from the <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
            <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only. </exception>
        </member>
        <member name="M:CsvHelper.Configuration.MemberReferenceMapCollection.Contains(CsvHelper.Configuration.MemberReferenceMap)">
            <summary>Determines whether the <see cref="T:System.Collections.Generic.ICollection`1" /> contains a specific value.</summary>
            <returns>true if <paramref name="item" /> is found in the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.</returns>
            <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberReferenceMapCollection.CopyTo(CsvHelper.Configuration.MemberReferenceMap[],System.Int32)">
            <summary>Copies the elements of the <see cref="T:System.Collections.Generic.ICollection`1" /> to an <see cref="T:System.Array" />, starting at a particular <see cref="T:System.Array" /> index.</summary>
            <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Generic.ICollection`1" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
            <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="array" /> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="arrayIndex" /> is less than 0.</exception>
            <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.ICollection`1" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.</exception>
        </member>
        <member name="M:CsvHelper.Configuration.MemberReferenceMapCollection.Remove(CsvHelper.Configuration.MemberReferenceMap)">
            <summary>Removes the first occurrence of a specific object from the <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
            <returns>true if <paramref name="item" /> was successfully removed from the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false. This method also returns false if <paramref name="item" /> is not found in the original <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
            <param name="item">The object to remove from the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
            <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.</exception>
        </member>
        <member name="M:CsvHelper.Configuration.MemberReferenceMapCollection.IndexOf(CsvHelper.Configuration.MemberReferenceMap)">
            <summary>Determines the index of a specific item in the <see cref="T:System.Collections.Generic.IList`1" />.</summary>
            <returns>The index of <paramref name="item" /> if found in the list; otherwise, -1.</returns>
            <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.IList`1" />.</param>
        </member>
        <member name="M:CsvHelper.Configuration.MemberReferenceMapCollection.Insert(System.Int32,CsvHelper.Configuration.MemberReferenceMap)">
            <summary>Inserts an item to the <see cref="T:System.Collections.Generic.IList`1" /> at the specified index.</summary>
            <param name="index">The zero-based index at which <paramref name="item" /> should be inserted.</param>
            <param name="item">The object to insert into the <see cref="T:System.Collections.Generic.IList`1" />.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="index" /> is not a valid index in the <see cref="T:System.Collections.Generic.IList`1" />.</exception>
            <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.Generic.IList`1" /> is read-only.</exception>
        </member>
        <member name="M:CsvHelper.Configuration.MemberReferenceMapCollection.RemoveAt(System.Int32)">
            <summary>Removes the <see cref="T:System.Collections.Generic.IList`1" /> item at the specified index.</summary>
            <param name="index">The zero-based index of the item to remove.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="index" /> is not a valid index in the <see cref="T:System.Collections.Generic.IList`1" />.</exception>
            <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.Generic.IList`1" /> is read-only.</exception>
        </member>
        <member name="M:CsvHelper.Configuration.MemberReferenceMapCollection.Find``1(System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            Finds the <see cref="T:CsvHelper.Configuration.MemberReferenceMap"/> using the given member expression.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> the member is on.</typeparam>
            <param name="expression">The member expression.</param>
            <returns>The <see cref="T:CsvHelper.Configuration.MemberReferenceMap"/> for the given expression, or null if not found.</returns>
        </member>
        <member name="M:CsvHelper.Configuration.MemberReferenceMapCollection.Find(System.Reflection.MemberInfo)">
            <summary>
            Finds the <see cref="T:CsvHelper.Configuration.MemberReferenceMap"/> using the given member.
            </summary>
            <param name="member">The member.</param>
            <returns>The <see cref="T:CsvHelper.Configuration.MemberReferenceMap"/> for the given expression, or null if not found.</returns>
        </member>
        <member name="T:CsvHelper.Configuration.MemberReferenceMapData">
            <summary>
            The configuration data for the reference map.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberReferenceMapData.Prefix">
            <summary>
            Gets or sets the header prefix to use.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberReferenceMapData.Inherit">
            <summary>
            Gets or sets a value indicating if a prefix should inherit its parent.
            <c>true</c> to inherit, otherwise <c>false</c>.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberReferenceMapData.Member">
            <summary>
            Gets the <see cref="T:System.Reflection.MemberInfo"/> that the data
            is associated with.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.MemberReferenceMapData.Mapping">
            <summary>
            Gets the mapping this is a reference for.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.MemberReferenceMapData.#ctor(System.Reflection.MemberInfo,CsvHelper.Configuration.ClassMap)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.Configuration.MemberReferenceMapData"/> class.
            </summary>
            <param name="member">The member.</param>
            <param name="mapping">The mapping this is a reference for.</param>
        </member>
        <member name="T:CsvHelper.Configuration.MemberTypes">
            <summary>
            Flags for the type of members that 
            can be used for auto mapping.
            </summary>
        </member>
        <member name="F:CsvHelper.Configuration.MemberTypes.None">
            <summary>
            No members. This is not a valid value
            and will cause an exception if used.
            </summary>
        </member>
        <member name="F:CsvHelper.Configuration.MemberTypes.Properties">
            <summary>
            Properties on a class.
            </summary>
        </member>
        <member name="F:CsvHelper.Configuration.MemberTypes.Fields">
            <summary>
            Fields on a class.
            </summary>
        </member>
        <member name="T:CsvHelper.Configuration.ParameterMap">
            <summary>
            Mapping for a constructor parameter.
            This may contain value type data, a constructor type map,
            or a reference map, depending on the type of the parameter.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ParameterMap.Data">
            <summary>
            Gets the parameter map data.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ParameterMap.TypeConverterOption">
            <summary>
            Type converter options.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ParameterMap.ConstructorTypeMap">
            <summary>
            Gets or sets the map for a constructor type.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ParameterMap.ReferenceMap">
            <summary>
            Gets or sets the map for a reference type.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterMap.#ctor(System.Reflection.ParameterInfo)">
            <summary>
            Creates an instance of <see cref="T:CsvHelper.Configuration.ParameterMap"/> using
            the given information.
            </summary>
            <param name="parameter">The parameter being mapped.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterMap.Name(System.String[])">
            <summary>
            When reading, is used to get the field
            at the index of the name if there was a
            header specified. It will look for the
            first name match in the order listed.
            When writing, sets the name of the 
            field in the header record.
            The first name will be used.
            </summary>
            <param name="names">The possible names of the CSV field.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterMap.NameIndex(System.Int32)">
            <summary>
            When reading, is used to get the 
            index of the name used when there 
            are multiple names that are the same.
            </summary>
            <param name="index">The index of the name.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterMap.Index(System.Int32)">
            <summary>
            When reading, is used to get the field at
            the given index. When writing, the fields
            will be written in the order of the field
            indexes.
            </summary>
            <param name="index">The index of the CSV field.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterMap.Ignore">
            <summary>
            Ignore the parameter when reading and writing.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterMap.Ignore(System.Boolean)">
            <summary>
            Ignore the parameter when reading and writing.
            </summary>
            <param name="ignore">True to ignore, otherwise false.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterMap.Default(System.Object)">
            <summary>
            The default value that will be used when reading when
            the CSV field is empty.
            </summary>
            <param name="defaultValue">The default value.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterMap.Constant(System.Object)">
            <summary>
            The constant value that will be used for every record when 
            reading and writing. This value will always be used no matter 
            what other mapping configurations are specified.
            </summary>
            <param name="constantValue">The constant value.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterMap.Optional">
            <summary>
            The field is optional.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterMap.TypeConverter(CsvHelper.TypeConversion.ITypeConverter)">
            <summary>
            Specifies the <see cref="M:CsvHelper.Configuration.ParameterMap.TypeConverter(CsvHelper.TypeConversion.ITypeConverter)"/> to use
            when converting the parameter to and from a CSV field.
            </summary>
            <param name="typeConverter">The TypeConverter to use.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterMap.TypeConverter``1">
            <summary>
            Specifies the <see cref="M:CsvHelper.Configuration.ParameterMap.TypeConverter(CsvHelper.TypeConversion.ITypeConverter)"/> to use
            when converting the parameter to and from a CSV field.
            </summary>
            <typeparam name="TConverter">The <see cref="T:System.Type"/> of the 
            <see cref="M:CsvHelper.Configuration.ParameterMap.TypeConverter(CsvHelper.TypeConversion.ITypeConverter)"/> to use.</typeparam>
        </member>
        <member name="T:CsvHelper.Configuration.ParameterMapData">
            <summary>
            The constructor parameter data for the map.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ParameterMapData.Parameter">
            <summary>
            Gets the <see cref="T:System.Reflection.ParameterInfo"/> that the data
            is associated with.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ParameterMapData.Names">
            <summary>
            Gets the list of column names.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ParameterMapData.NameIndex">
            <summary>
            Gets or sets the index of the name.
            This is used if there are multiple
            columns with the same names.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ParameterMapData.IsNameSet">
            <summary>
            Gets or sets a value indicating if the name was
            explicitly set. True if it was explicitly set,
            otherwise false.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ParameterMapData.Index">
            <summary>
            Gets or sets the column index.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ParameterMapData.IsIndexSet">
            <summary>
            Gets or sets a value indicating if the index was
            explicitly set. True if it was explicitly set,
            otherwise false.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ParameterMapData.TypeConverter">
            <summary>
            Gets or sets the type converter.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ParameterMapData.TypeConverterOptions">
            <summary>
            Gets or sets the type converter options.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ParameterMapData.Ignore">
            <summary>
            Gets or sets a value indicating whether the field should be ignored.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ParameterMapData.Default">
            <summary>
            Gets or sets the default value used when a CSV field is empty.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ParameterMapData.IsDefaultSet">
            <summary>
            Gets or sets a value indicating whether this instance is default value set.
            the default value was explicitly set. True if it was
            explicitly set, otherwise false.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ParameterMapData.Constant">
            <summary>
            Gets or sets the constant value used for every record.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ParameterMapData.IsConstantSet">
            <summary>
            Gets or sets a value indicating if a constant was explicitly set.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ParameterMapData.IsOptional">
            <summary>
            Gets or sets a value indicating if a field is optional.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterMapData.#ctor(System.Reflection.ParameterInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.Configuration.ParameterMapData"/> class.
            </summary>
            <param name="parameter">The constructor parameter.</param>
        </member>
        <member name="T:CsvHelper.Configuration.ParameterMapTypeConverterOption">
            <summary>
            Sets type converter options on a parameter map.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterMapTypeConverterOption.#ctor(CsvHelper.Configuration.ParameterMap)">
            <summary>
            Creates a new instance using the given <see cref="T:CsvHelper.Configuration.ParameterMap"/>.
            </summary>
            <param name="parameterMap">The member map the options are being applied to.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterMapTypeConverterOption.CultureInfo(System.Globalization.CultureInfo)">
            <summary>
            The <see cref="M:CsvHelper.Configuration.ParameterMapTypeConverterOption.CultureInfo(System.Globalization.CultureInfo)"/> used when type converting.
            This will override the global <see cref="P:CsvHelper.Configuration.CsvConfiguration.CultureInfo"/>
            setting.
            </summary>
            <param name="cultureInfo">The culture info.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterMapTypeConverterOption.DateTimeStyles(System.Globalization.DateTimeStyles)">
            <summary>
            The <see cref="M:CsvHelper.Configuration.ParameterMapTypeConverterOption.DateTimeStyles(System.Globalization.DateTimeStyles)"/> to use when type converting.
            This is used when doing any <see cref="T:System.DateTime"/> conversions.
            </summary>
            <param name="dateTimeStyle">The date time style.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterMapTypeConverterOption.TimespanStyles(System.Globalization.TimeSpanStyles)">
            <summary>
            The <see cref="T:System.Globalization.TimeSpanStyles"/> to use when type converting.
            This is used when doing <see cref="T:System.TimeSpan"/> converting.
            </summary>
            <param name="timeSpanStyles">The time span styles.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterMapTypeConverterOption.NumberStyles(System.Globalization.NumberStyles)">
            <summary>
            The <see cref="M:CsvHelper.Configuration.ParameterMapTypeConverterOption.NumberStyles(System.Globalization.NumberStyles)"/> to use when type converting.
            This is used when doing any number conversions.
            </summary>
            <param name="numberStyle"></param>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterMapTypeConverterOption.Format(System.String[])">
            <summary>
            The string format to be used when type converting.
            </summary>
            <param name="formats">The format.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterMapTypeConverterOption.UriKind(System.UriKind)">
            <summary>
            The <see cref="M:CsvHelper.Configuration.ParameterMapTypeConverterOption.UriKind(System.UriKind)"/> to use when converting.
            This is used when doing <see cref="T:System.Uri"/> conversions.
            </summary>
            <param name="uriKind">Kind of the URI.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterMapTypeConverterOption.BooleanValues(System.Boolean,System.Boolean,System.String[])">
            <summary>
            The string values used to represent a boolean when converting.
            </summary>
            <param name="isTrue">A value indicating whether true values or false values are being set.</param>
            <param name="clearValues">A value indication if the current values should be cleared before adding the new ones.</param>
            <param name="booleanValues">The string boolean values.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterMapTypeConverterOption.NullValues(System.String[])">
            <summary>
            The string values used to represent null when converting.
            </summary>
            <param name="nullValues">The values that represent null.</param>
            <returns></returns>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterMapTypeConverterOption.NullValues(System.Boolean,System.String[])">
            <summary>
            The string values used to represent null when converting.
            </summary>
            <param name="clearValues">A value indication if the current values should be cleared before adding the new ones.</param>
            <param name="nullValues">The values that represent null.</param>
            <returns></returns>
        </member>
        <member name="T:CsvHelper.Configuration.ParameterReferenceMap">
            <summary>
            Mapping info for a reference parameter mapping to a class.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ParameterReferenceMap.Data">
            <summary>
            Gets the parameter reference map data.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterReferenceMap.#ctor(System.Reflection.ParameterInfo,CsvHelper.Configuration.ClassMap)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.Configuration.ParameterReferenceMap"/> class.
            </summary>
            <param name="parameter">The parameter.</param>
            <param name="mapping">The <see cref="T:CsvHelper.Configuration.ClassMap"/> to use for the reference map.</param>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterReferenceMap.Prefix(System.String,System.Boolean)">
            <summary>
            Appends a prefix to the header of each field of the reference parameter.
            </summary>
            <param name="prefix">The prefix to be prepended to headers of each reference parameter.</param>
            <param name="inherit">Inherit parent prefixes.</param>
            <returns>The current <see cref="T:CsvHelper.Configuration.ParameterReferenceMap" /></returns>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterReferenceMap.GetMaxIndex">
            <summary>
            Get the largest index for the
            members and references.
            </summary>
            <returns>The max index.</returns>
        </member>
        <member name="T:CsvHelper.Configuration.ParameterReferenceMapData">
            <summary>
            The configuration data for the reference map.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ParameterReferenceMapData.Prefix">
            <summary>
            Gets or sets the header prefix to use.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ParameterReferenceMapData.Inherit">
            <summary>
            Gets or sets a value indicating if a prefix should inherit its parent.
            <c>true</c> to inherit, otherwise <c>false</c>.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ParameterReferenceMapData.Parameter">
            <summary>
            Gets the <see cref="T:System.Reflection.ParameterInfo"/> that the data
            is associated with.
            </summary>
        </member>
        <member name="P:CsvHelper.Configuration.ParameterReferenceMapData.Mapping">
            <summary>
            Gets the mapping this is a reference for.
            </summary>
        </member>
        <member name="M:CsvHelper.Configuration.ParameterReferenceMapData.#ctor(System.Reflection.ParameterInfo,CsvHelper.Configuration.ClassMap)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.Configuration.ParameterReferenceMapData"/> class.
            </summary>
            <param name="parameter">The parameter.</param>
            <param name="mapping">The mapping this is a reference for.</param>
        </member>
        <member name="T:CsvHelper.Configuration.TrimOptions">
            <summary>
            Options for trimming of fields.
            </summary>
        </member>
        <member name="F:CsvHelper.Configuration.TrimOptions.None">
            <summary>
            No trimming.
            </summary>
        </member>
        <member name="F:CsvHelper.Configuration.TrimOptions.Trim">
            <summary>
            Trims the whitespace around a field.
            </summary>
        </member>
        <member name="F:CsvHelper.Configuration.TrimOptions.InsideQuotes">
            <summary>
            Trims the whitespace inside of quotes around a field.
            </summary>
        </member>
        <member name="T:CsvHelper.CsvContext">
            <summary>
            Share state for CsvHelper.
            </summary>
        </member>
        <member name="P:CsvHelper.CsvContext.TypeConverterOptionsCache">
            <summary>
            Gets or sets the <see cref="P:CsvHelper.CsvContext.TypeConverterOptionsCache"/>.
            </summary>
        </member>
        <member name="P:CsvHelper.CsvContext.TypeConverterCache">
            <summary>
            Gets or sets the <see cref="P:CsvHelper.CsvContext.TypeConverterOptionsCache"/>.
            </summary>
        </member>
        <member name="P:CsvHelper.CsvContext.Maps">
            <summary>
            The configured <see cref="T:CsvHelper.Configuration.ClassMap"/>s.
            </summary>
        </member>
        <member name="P:CsvHelper.CsvContext.Parser">
            <summary>
            Gets the parser.
            </summary>
        </member>
        <member name="P:CsvHelper.CsvContext.Reader">
            <summary>
            Gets the reader.
            </summary>
        </member>
        <member name="P:CsvHelper.CsvContext.Writer">
            <summary>
            Gets the writer.
            </summary>
        </member>
        <member name="P:CsvHelper.CsvContext.Configuration">
            <summary>
            Gets the configuration.
            </summary>
        </member>
        <member name="M:CsvHelper.CsvContext.#ctor(CsvHelper.IReader)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.CsvContext"/> class.
            </summary>
            <param name="reader">The reader.</param>
        </member>
        <member name="M:CsvHelper.CsvContext.#ctor(CsvHelper.IParser)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.CsvContext"/> class.
            </summary>
            <param name="parser">The parser.</param>
        </member>
        <member name="M:CsvHelper.CsvContext.#ctor(CsvHelper.IWriter)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.CsvContext"/> class.
            </summary>
            <param name="writer">The writer.</param>
        </member>
        <member name="M:CsvHelper.CsvContext.#ctor(CsvHelper.Configuration.CsvConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.CsvContext"/> class.
            </summary>
            <param name="configuration">The configuration.</param>
        </member>
        <member name="M:CsvHelper.CsvContext.RegisterClassMap``1">
            <summary>
            Use a <see cref="T:CsvHelper.Configuration.ClassMap`1" /> to configure mappings.
            When using a class map, no members are mapped by default.
            Only member specified in the mapping are used.
            </summary>
            <typeparam name="TMap">The type of mapping class to use.</typeparam>
        </member>
        <member name="M:CsvHelper.CsvContext.RegisterClassMap(System.Type)">
            <summary>
            Use a <see cref="T:CsvHelper.Configuration.ClassMap`1" /> to configure mappings.
            When using a class map, no members are mapped by default.
            Only members specified in the mapping are used.
            </summary>
            <param name="classMapType">The type of mapping class to use.</param>
        </member>
        <member name="M:CsvHelper.CsvContext.RegisterClassMap(CsvHelper.Configuration.ClassMap)">
            <summary>
            Registers the class map.
            </summary>
            <param name="map">The class map to register.</param>
        </member>
        <member name="M:CsvHelper.CsvContext.UnregisterClassMap``1">
            <summary>
            Unregisters the class map.
            </summary>
            <typeparam name="TMap">The map type to unregister.</typeparam>
        </member>
        <member name="M:CsvHelper.CsvContext.UnregisterClassMap(System.Type)">
            <summary>
            Unregisters the class map.
            </summary>
            <param name="classMapType">The map type to unregister.</param>
        </member>
        <member name="M:CsvHelper.CsvContext.UnregisterClassMap">
            <summary>
            Unregisters all class maps.
            </summary>
        </member>
        <member name="M:CsvHelper.CsvContext.AutoMap``1">
            <summary>
            Generates a <see cref="T:CsvHelper.Configuration.ClassMap"/> for the type.
            </summary>
            <typeparam name="T">The type to generate the map for.</typeparam>
            <returns>The generate map.</returns>
        </member>
        <member name="M:CsvHelper.CsvContext.AutoMap(System.Type)">
            <summary>
            Generates a <see cref="T:CsvHelper.Configuration.ClassMap"/> for the type.
            </summary>
            <param name="type">The type to generate for the map.</param>
            <returns>The generate map.</returns>
        </member>
        <member name="T:CsvHelper.CsvDataReader">
            <summary>
            Provides a means of reading a CSV file forward-only by using CsvReader.
            </summary>
            <seealso cref="T:System.Data.IDataReader" />
        </member>
        <member name="P:CsvHelper.CsvDataReader.Item(System.Int32)">
            <inheritdoc />
        </member>
        <member name="P:CsvHelper.CsvDataReader.Item(System.String)">
            <inheritdoc />
        </member>
        <member name="P:CsvHelper.CsvDataReader.Depth">
            <inheritdoc />
        </member>
        <member name="P:CsvHelper.CsvDataReader.IsClosed">
            <inheritdoc />
        </member>
        <member name="P:CsvHelper.CsvDataReader.RecordsAffected">
            <inheritdoc />
        </member>
        <member name="P:CsvHelper.CsvDataReader.FieldCount">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.#ctor(CsvHelper.CsvReader,System.Data.DataTable)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.CsvDataReader"/> class.
            </summary>
            <param name="csv">The CSV.</param>
            <param name="schemaTable">The DataTable representing the file schema.</param>
        </member>
        <member name="M:CsvHelper.CsvDataReader.Close">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.Dispose">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.GetBoolean(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.GetByte(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.GetBytes(System.Int32,System.Int64,System.Byte[],System.Int32,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.GetChar(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.GetChars(System.Int32,System.Int64,System.Char[],System.Int32,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.GetData(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.GetDataTypeName(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.GetDateTime(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.GetDecimal(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.GetDouble(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.GetFieldType(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.GetFloat(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.GetGuid(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.GetInt16(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.GetInt32(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.GetInt64(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.GetName(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.GetOrdinal(System.String)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.GetSchemaTable">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.GetString(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.GetValue(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.GetValues(System.Object[])">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.IsDBNull(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.NextResult">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.CsvDataReader.Read">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.CsvHelperException">
            <summary>
            Represents errors that occur in CsvHelper.
            </summary>
        </member>
        <member name="P:CsvHelper.CsvHelperException.Context">
            <summary>
            Gets the context.
            </summary>
        </member>
        <member name="M:CsvHelper.CsvHelperException.#ctor">
            <summary>
            Initializes a new instance of the CsvHelperException class.
            </summary>
        </member>
        <member name="M:CsvHelper.CsvHelperException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CsvHelperException class.
            </summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:CsvHelper.CsvHelperException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the CsvHelperException class.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
        </member>
        <member name="M:CsvHelper.CsvHelperException.#ctor(CsvHelper.CsvContext)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.CsvHelperException"/> class.
            </summary>
        </member>
        <member name="M:CsvHelper.CsvHelperException.#ctor(CsvHelper.CsvContext,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.CsvHelperException"/> class
            with a specified error message.
            </summary>
            <param name="context">The context.</param>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:CsvHelper.CsvHelperException.#ctor(CsvHelper.CsvContext,System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.CsvHelperException"/> class
            with a specified error message and a reference to the inner exception that 
            is the cause of this exception.
            </summary>
            <param name="context">The context.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
        </member>
        <member name="T:CsvHelper.CsvMode">
            <summary>
            Mode to use when parsing and writing.
            </summary>
        </member>
        <member name="F:CsvHelper.CsvMode.RFC4180">
            <summary>
            Uses RFC 4180 format (default).
            If a field contains a <see cref="P:CsvHelper.Configuration.CsvConfiguration.Delimiter"/> or <see cref="P:CsvHelper.Configuration.CsvConfiguration.NewLine"/>,
            it is wrapped in <see cref="P:CsvHelper.Configuration.CsvConfiguration.Quote"/>s.
            If quoted field contains a <see cref="P:CsvHelper.Configuration.CsvConfiguration.Quote"/>, it is preceded by <see cref="P:CsvHelper.Configuration.CsvConfiguration.Escape"/>.
            </summary>
        </member>
        <member name="F:CsvHelper.CsvMode.Escape">
            <summary>
            Uses escapes.
            If a field contains a <see cref="P:CsvHelper.Configuration.CsvConfiguration.Delimiter"/>, <see cref="P:CsvHelper.Configuration.CsvConfiguration.NewLine"/>,
            or <see cref="P:CsvHelper.Configuration.CsvConfiguration.Escape"/>, it is preceded by <see cref="P:CsvHelper.Configuration.CsvConfiguration.Escape"/>.
            Newline defaults to \n.
            </summary>
        </member>
        <member name="F:CsvHelper.CsvMode.NoEscape">
            <summary>
            Doesn't use quotes or escapes.
            This will ignore quoting and escape characters. This means a field cannot contain a
            <see cref="P:CsvHelper.Configuration.CsvConfiguration.Delimiter"/>, <see cref="P:CsvHelper.Configuration.CsvConfiguration.Quote"/>, or
            <see cref="P:CsvHelper.Configuration.CsvConfiguration.NewLine"/>, as they cannot be escaped.
            </summary>
        </member>
        <member name="T:CsvHelper.CsvParser">
            <summary>
            Parses a CSV file.
            </summary>
        </member>
        <member name="P:CsvHelper.CsvParser.CharCount">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.CsvParser.ByteCount">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.CsvParser.Row">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.CsvParser.Record">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.CsvParser.RawRecord">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.CsvParser.Count">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.CsvParser.RawRow">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.CsvParser.Delimiter">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.CsvParser.Context">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.CsvParser.Configuration">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.CsvParser.Item(System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvParser.#ctor(System.IO.TextReader,System.Globalization.CultureInfo,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.CsvParser"/> class.
            </summary>
            <param name="reader">The reader.</param>
            <param name="culture">The culture.</param>
            <param name="leaveOpen">if set to <c>true</c> [leave open].</param>
        </member>
        <member name="M:CsvHelper.CsvParser.#ctor(System.IO.TextReader,CsvHelper.Configuration.IParserConfiguration,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.CsvParser"/> class.
            </summary>
            <param name="reader">The reader.</param>
            <param name="configuration">The configuration.</param>
            <param name="leaveOpen">if set to <c>true</c> [leave open].</param>
        </member>
        <member name="M:CsvHelper.CsvParser.Read">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvParser.ReadAsync">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvParser.ProcessRFC4180Field(System.Int32,System.Int32,System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvParser.ProcessRFC4180BadField(System.Int32,System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvParser.ProcessEscapeField(System.Int32,System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvParser.ProcessNoEscapeField(System.Int32,System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvParser.Dispose">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvParser.Dispose(System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="T:CsvHelper.CsvParser.ProcessedField">
            <summary>
            Processes a raw field based on configuration.
            This will remove quotes, remove escapes, and trim if configured to.
            </summary>
        </member>
        <member name="F:CsvHelper.CsvParser.ProcessedField.Start">
            <summary>
            The start of the field in the buffer.
            </summary>
        </member>
        <member name="F:CsvHelper.CsvParser.ProcessedField.Length">
            <summary>
            The length of the field in the buffer.
            </summary>
        </member>
        <member name="F:CsvHelper.CsvParser.ProcessedField.Buffer">
            <summary>
            The buffer that contains the field.
            </summary>
        </member>
        <member name="M:CsvHelper.CsvParser.ProcessedField.#ctor(System.Int32,System.Int32,System.Char[])">
            <summary>
            Creates a new instance of ProcessedField.
            </summary>
            <param name="start">The start of the field in the buffer.</param>
            <param name="length">The length of the field in the buffer.</param>
            <param name="buffer">The buffer that contains the field.</param>
        </member>
        <member name="F:CsvHelper.CsvParser.Field.Start">
            <summary>
            Starting position of the field.
            This is an offset from <see cref="F:CsvHelper.CsvParser.rowStartPosition"/>.
            </summary>
        </member>
        <member name="T:CsvHelper.CsvReader">
            <summary>
            Reads data that was parsed from <see cref="T:CsvHelper.IParser" />.
            </summary>
        </member>
        <member name="P:CsvHelper.CsvReader.ColumnCount">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.CsvReader.CurrentIndex">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.CsvReader.HeaderRecord">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.CsvReader.Context">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.CsvReader.Configuration">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.CsvReader.Parser">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.#ctor(System.IO.TextReader,System.Globalization.CultureInfo,System.Boolean)">
            <summary>
            Creates a new CSV reader using the given <see cref="T:System.IO.TextReader" />.
            </summary>
            <param name="reader">The reader.</param>
            <param name="culture">The culture.</param>
            <param name="leaveOpen"><c>true</c> to leave the <see cref="T:System.IO.TextReader"/> open after the <see cref="T:CsvHelper.CsvReader"/> object is disposed, otherwise <c>false</c>.</param>
        </member>
        <member name="M:CsvHelper.CsvReader.#ctor(System.IO.TextReader,CsvHelper.Configuration.IReaderConfiguration,System.Boolean)">
            <summary>
            Creates a new CSV reader using the given <see cref="T:System.IO.TextReader" /> and
            <see cref="T:CsvHelper.Configuration.CsvConfiguration" /> and <see cref="T:CsvHelper.CsvParser" /> as the default parser.
            </summary>
            <param name="reader">The reader.</param>
            <param name="configuration">The configuration.</param>
            <param name="leaveOpen"><c>true</c> to leave the <see cref="T:System.IO.TextReader"/> open after the <see cref="T:CsvHelper.CsvReader"/> object is disposed, otherwise <c>false</c>.</param>
        </member>
        <member name="M:CsvHelper.CsvReader.#ctor(CsvHelper.IParser)">
            <summary>
            Creates a new CSV reader using the given <see cref="T:CsvHelper.IParser" />.
            </summary>
            <param name="parser">The <see cref="T:CsvHelper.IParser" /> used to parse the CSV file.</param>
        </member>
        <member name="M:CsvHelper.CsvReader.ReadHeader">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.ValidateHeader``1">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.ValidateHeader(System.Type)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.ValidateHeader(CsvHelper.Configuration.ClassMap,System.Collections.Generic.List{CsvHelper.InvalidHeader})">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.Read">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.ReadAsync">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.CsvReader.Item(System.Int32)">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.CsvReader.Item(System.String)">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.CsvReader.Item(System.String,System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetField(System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetField(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetField(System.String,System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetField(System.Type,System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetField(System.Type,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetField(System.Type,System.String,System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetField(System.Type,System.Int32,CsvHelper.TypeConversion.ITypeConverter)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetField(System.Type,System.String,CsvHelper.TypeConversion.ITypeConverter)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetField(System.Type,System.String,System.Int32,CsvHelper.TypeConversion.ITypeConverter)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetField``1(System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetField``1(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetField``1(System.String,System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetField``1(System.Int32,CsvHelper.TypeConversion.ITypeConverter)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetField``1(System.String,CsvHelper.TypeConversion.ITypeConverter)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetField``1(System.String,System.Int32,CsvHelper.TypeConversion.ITypeConverter)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetField``2(System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetField``2(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetField``2(System.String,System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.TryGetField(System.Type,System.Int32,System.Object@)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.TryGetField(System.Type,System.String,System.Object@)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.TryGetField(System.Type,System.String,System.Int32,System.Object@)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.TryGetField(System.Type,System.Int32,CsvHelper.TypeConversion.ITypeConverter,System.Object@)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.TryGetField(System.Type,System.String,CsvHelper.TypeConversion.ITypeConverter,System.Object@)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.TryGetField(System.Type,System.String,System.Int32,CsvHelper.TypeConversion.ITypeConverter,System.Object@)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.TryGetField``1(System.Int32,``0@)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.TryGetField``1(System.String,``0@)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.TryGetField``1(System.String,System.Int32,``0@)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.TryGetField``1(System.Int32,CsvHelper.TypeConversion.ITypeConverter,``0@)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.TryGetField``1(System.String,CsvHelper.TypeConversion.ITypeConverter,``0@)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.TryGetField``1(System.String,System.Int32,CsvHelper.TypeConversion.ITypeConverter,``0@)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.TryGetField``2(System.Int32,``0@)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.TryGetField``2(System.String,``0@)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.TryGetField``2(System.String,System.Int32,``0@)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetRecord``1">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetRecord``1(``0)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetRecord(System.Type)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetRecords``1">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetRecords``1(``0)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetRecords(System.Type)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.EnumerateRecords``1(``0)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetRecordsAsync``1(System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetRecordsAsync``1(``0,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetRecordsAsync(System.Type,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.EnumerateRecordsAsync``1(``0,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetFieldIndex(System.String,System.Int32,System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.GetFieldIndex(System.Collections.Generic.IEnumerable{System.String},System.Int32,System.Boolean,System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.CanRead(CsvHelper.Configuration.MemberMap)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.CanRead(CsvHelper.Configuration.MemberReferenceMap)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.Dispose">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.Dispose(System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.CheckHasBeenRead">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvReader.ParseNamedIndexes">
            <inheritdoc/>
        </member>
        <member name="T:CsvHelper.CsvWriter">
            <summary>
            Used to write CSV files.
            </summary>
        </member>
        <member name="P:CsvHelper.CsvWriter.HeaderRecord">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.CsvWriter.Row">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.CsvWriter.Index">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.CsvWriter.Context">
            <inheritdoc/>
        </member>
        <member name="P:CsvHelper.CsvWriter.Configuration">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.#ctor(System.IO.TextWriter,System.Globalization.CultureInfo,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.CsvWriter"/> class.
            </summary>
            <param name="writer">The writer.</param>
            <param name="culture">The culture.</param>
            <param name="leaveOpen"><c>true</c> to leave the <see cref="T:System.IO.TextWriter"/> open after the <see cref="T:CsvHelper.CsvWriter"/> object is disposed, otherwise <c>false</c>.</param>
        </member>
        <member name="M:CsvHelper.CsvWriter.#ctor(System.IO.TextWriter,CsvHelper.Configuration.IWriterConfiguration,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.CsvWriter"/> class.
            </summary>
            <param name="writer">The writer.</param>
            <param name="configuration">The configuration.</param>
            <param name="leaveOpen"><c>true</c> to leave the <see cref="T:System.IO.TextWriter"/> open after the <see cref="T:CsvHelper.CsvWriter"/> object is disposed, otherwise <c>false</c>.</param>
        </member>
        <member name="M:CsvHelper.CsvWriter.WriteConvertedField(System.String,System.Type)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.WriteField(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.WriteField(System.String,System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.WriteField``1(``0)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.WriteField``1(``0,CsvHelper.TypeConversion.ITypeConverter)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.WriteField``2(``0)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.WriteComment(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.WriteHeader``1">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.WriteHeader(System.Type)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.WriteDynamicHeader(System.Dynamic.IDynamicMetaObjectProvider)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.WriteRecord``1(``0)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.WriteRecords(System.Collections.IEnumerable)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.WriteRecords``1(System.Collections.Generic.IEnumerable{``0})">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.WriteRecordsAsync(System.Collections.IEnumerable,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.WriteRecordsAsync``1(System.Collections.Generic.IEnumerable{``0},System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.WriteRecordsAsync``1(System.Collections.Generic.IAsyncEnumerable{``0},System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.NextRecord">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.NextRecordAsync">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.Flush">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.FlushAsync">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.FlushBuffer">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.FlushBufferAsync">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.CanWrite(CsvHelper.Configuration.MemberMap)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.GetTypeForRecord``1(``0)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.SanitizeForInjection(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.WriteToBuffer(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.Dispose">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.Dispose(System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.DisposeAsync">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.CsvWriter.DisposeAsync(System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="T:CsvHelper.BadDataFound">
            <summary>
            Function that gets called when bad data is found.
            </summary>
            <param name="args">The args.</param>
        </member>
        <member name="T:CsvHelper.BadDataFoundArgs">
            <summary>
            Information about the field that caused <see cref="T:CsvHelper.BadDataFound"/> to be called.
            </summary>
        </member>
        <member name="F:CsvHelper.BadDataFoundArgs.Field">
            <summary>
            The full field unedited.
            </summary>
        </member>
        <member name="F:CsvHelper.BadDataFoundArgs.RawRecord">
            <summary>
            The full row unedited.
            </summary>
        </member>
        <member name="F:CsvHelper.BadDataFoundArgs.Context">
            <summary>
            The context.
            </summary>
        </member>
        <member name="M:CsvHelper.BadDataFoundArgs.#ctor(System.String,System.String,CsvHelper.CsvContext)">
            <summary>
            Creates a new instance of BadDataFoundArgs.
            </summary>
            <param name="field">The full field unedited.</param>
            <param name="rawRecord">The full row unedited.</param>
            <param name="context">The context.</param>
        </member>
        <member name="T:CsvHelper.ConvertFromString`1">
            <summary>
            Function that converts a string into an object.
            </summary>
            <typeparam name="TMember">The type of the member.</typeparam>
            <param name="args">The args.</param>
            <returns>The class object.</returns>
        </member>
        <member name="T:CsvHelper.ConvertFromStringArgs">
            <summary>
            <see cref="T:CsvHelper.ConvertFromString`1"/> args.
            </summary>
        </member>
        <member name="F:CsvHelper.ConvertFromStringArgs.Row">
            <summary>
            The row.
            </summary>
        </member>
        <member name="M:CsvHelper.ConvertFromStringArgs.#ctor(CsvHelper.IReaderRow)">
            <summary>
            Creates a new instance of ConvertFromStringArgs.
            </summary>
            <param name="row">The row.</param>
        </member>
        <member name="T:CsvHelper.ConvertToString`1">
            <summary>
            Function that converts an object into a string.
            </summary>
            <typeparam name="TClass">The type of the class.</typeparam>
            <param name="args">The args.</param>
            <returns>The string.</returns>
        </member>
        <member name="T:CsvHelper.ConvertToStringArgs`1">
            <summary>
            <see cref="T:CsvHelper.ConvertToString`1"/> args.
            </summary>
            <typeparam name="TClass">The value to convert.</typeparam>
        </member>
        <member name="F:CsvHelper.ConvertToStringArgs`1.Value">
            <summary>
            The value to convert.
            </summary>
        </member>
        <member name="M:CsvHelper.ConvertToStringArgs`1.#ctor(`0)">
            <summary>
            Creates a new instance of ConvertToStringArgs{TClass}.
            </summary>
            <param name="value">The value to convert.</param>
        </member>
        <member name="T:CsvHelper.GetConstructor">
            <summary>
            Function that chooses the constructor to use for constructor mapping.
            </summary>
        </member>
        <member name="T:CsvHelper.GetConstructorArgs">
            <summary>
            GetConstructor args.
            </summary>
        </member>
        <member name="F:CsvHelper.GetConstructorArgs.ClassType">
            <summary>
            The class type.
            </summary>
        </member>
        <member name="M:CsvHelper.GetConstructorArgs.#ctor(System.Type)">
            <summary>
            Creates a new instance of GetConstructorArgs.
            </summary>
            <param name="classType">The class type.</param>
        </member>
        <member name="T:CsvHelper.Delegates.GetDelimiter">
            <summary>
            Function that resolves the delimiter from the given text.
            Returns null if no delimiter is found.
            </summary>
            <param name="args"></param>
            <returns></returns>
        </member>
        <member name="T:CsvHelper.Delegates.GetDelimiterArgs">
            <summary>
            GetDelimiter args.
            </summary>
        </member>
        <member name="F:CsvHelper.Delegates.GetDelimiterArgs.Text">
            <summary>
            The text to resolve the delimiter from.
            </summary>
        </member>
        <member name="F:CsvHelper.Delegates.GetDelimiterArgs.Configuration">
            <summary>
            The configuration.
            </summary>
        </member>
        <member name="M:CsvHelper.Delegates.GetDelimiterArgs.#ctor(System.String,CsvHelper.Configuration.IParserConfiguration)">
            <summary>
            Creates an instance of GetDelimiterArgs.
            </summary>
            <param name="text">The text to resolve the delimiter from.</param>
            <param name="configuration">The configuration.</param>
        </member>
        <member name="T:CsvHelper.GetDynamicPropertyName">
            <summary>
            Function that gets the name to use for the property of the dynamic object.
            </summary>
        </member>
        <member name="T:CsvHelper.GetDynamicPropertyNameArgs">
            <summary>
            GetDynamicPropertyName args.
            </summary>
        </member>
        <member name="F:CsvHelper.GetDynamicPropertyNameArgs.FieldIndex">
            <summary>
            The field index.
            </summary>
        </member>
        <member name="F:CsvHelper.GetDynamicPropertyNameArgs.Context">
            <summary>
            The context.
            </summary>
        </member>
        <member name="M:CsvHelper.GetDynamicPropertyNameArgs.#ctor(System.Int32,CsvHelper.CsvContext)">
            <summary>
            Creates a new instance of GetDynamicPropertyNameArgs.
            </summary>
            <param name="fieldIndex">The field index.</param>
            <param name="context">The context.</param>
        </member>
        <member name="T:CsvHelper.HeaderValidated">
            <summary>
            Function that is called when a header validation check is ran. The default function
            will throw a <see cref="T:CsvHelper.ValidationException"/> if there is no header for a given member mapping.
            You can supply your own function to do other things like logging the issue instead of throwing an exception.
            </summary>
        </member>
        <member name="T:CsvHelper.HeaderValidatedArgs">
            <summary>
            HeaderValidated args.
            </summary>
        </member>
        <member name="F:CsvHelper.HeaderValidatedArgs.InvalidHeaders">
            <summary>
            The invalid headers.
            </summary>
        </member>
        <member name="F:CsvHelper.HeaderValidatedArgs.Context">
            <summary>
            The context.
            </summary>
        </member>
        <member name="M:CsvHelper.HeaderValidatedArgs.#ctor(CsvHelper.InvalidHeader[],CsvHelper.CsvContext)">
            <summary>
            Creates a new instance of HeaderValidatedArgs.
            </summary>
            <param name="invalidHeaders">The invalid headers.</param>
            <param name="context">The context.</param>
        </member>
        <member name="T:CsvHelper.MissingFieldFound">
            <summary>
            Function that is called when a missing field is found. The default function will
            throw a <see cref="T:CsvHelper.MissingFieldException"/>. You can supply your own function to do other things
            like logging the issue instead of throwing an exception.
            </summary>
        </member>
        <member name="T:CsvHelper.MissingFieldFoundArgs">
            <summary>
            MissingFieldFound args.
            </summary>
        </member>
        <member name="F:CsvHelper.MissingFieldFoundArgs.HeaderNames">
            <summary>
            The header names.
            </summary>
        </member>
        <member name="F:CsvHelper.MissingFieldFoundArgs.Index">
            <summary>
            The index.
            </summary>
        </member>
        <member name="F:CsvHelper.MissingFieldFoundArgs.Context">
            <summary>
            The context.
            </summary>
        </member>
        <member name="M:CsvHelper.MissingFieldFoundArgs.#ctor(System.String[],System.Int32,CsvHelper.CsvContext)">
            <summary>
            Creates a new instance of MissingFieldFoundArgs.
            </summary>
            <param name="headerNames">The header names.</param>
            <param name="index">The index.</param>
            <param name="context">The context.</param>
        </member>
        <member name="T:CsvHelper.PrepareHeaderForMatch">
            <summary>
            Function that prepares the header field for matching against a member name.
            The header field and the member name are both ran through this function.
            You should do things like trimming, removing whitespace, removing underscores,
            and making casing changes to ignore case.
            </summary>
        </member>
        <member name="T:CsvHelper.PrepareHeaderForMatchArgs">
            <summary>
            PrepareHeaderForMatch args.
            </summary>
        </member>
        <member name="F:CsvHelper.PrepareHeaderForMatchArgs.Header">
            <summary>
            The header.
            </summary>
        </member>
        <member name="F:CsvHelper.PrepareHeaderForMatchArgs.FieldIndex">
            <summary>
            The field index.
            </summary>
        </member>
        <member name="M:CsvHelper.PrepareHeaderForMatchArgs.#ctor(System.String,System.Int32)">
            <summary>
            Creates a new instance of PrepareHeaderForMatchArgs.
            </summary>
            <param name="header">The header.</param>
            <param name="fieldIndex">The field index.</param>
        </member>
        <member name="T:CsvHelper.ReadingExceptionOccurred">
            <summary>
            Function that is called when a reading exception occurs.
            The default function will re-throw the given exception. If you want to ignore
            reading exceptions, you can supply your own function to do other things like
            logging the issue.
            </summary>
        </member>
        <member name="T:CsvHelper.ReadingExceptionOccurredArgs">
            <summary>
            ReadingExceptionOccurred args.
            </summary>
        </member>
        <member name="F:CsvHelper.ReadingExceptionOccurredArgs.Exception">
            <summary>
            The exception.
            </summary>
        </member>
        <member name="M:CsvHelper.ReadingExceptionOccurredArgs.#ctor(CsvHelper.CsvHelperException)">
            <summary>
            Creates a new instance of ReadingExceptionOccurredArgs.
            </summary>
            <param name="exception">The exception.</param>
        </member>
        <member name="T:CsvHelper.ReferenceHeaderPrefix">
            <summary>
            Function that will return the prefix for a reference header.
            </summary>
        </member>
        <member name="T:CsvHelper.ReferenceHeaderPrefixArgs">
            <summary>
            ReferenceHeaderPrefix args.
            </summary>
        </member>
        <member name="F:CsvHelper.ReferenceHeaderPrefixArgs.MemberType">
            <summary>
            The member type.
            </summary>
        </member>
        <member name="F:CsvHelper.ReferenceHeaderPrefixArgs.MemberName">
            <summary>
            The member name.
            </summary>
        </member>
        <member name="M:CsvHelper.ReferenceHeaderPrefixArgs.#ctor(System.Type,System.String)">
            <summary>
            Creates a new instance of ReferenceHeaderPrefixArgs.
            </summary>
            <param name="memberType">The member type.</param>
            <param name="memberName">The member name.</param>
        </member>
        <member name="T:CsvHelper.ShouldQuote">
            <summary>
            Function that is used to determine if a field should get quoted when writing.
            </summary>
        </member>
        <member name="T:CsvHelper.ShouldQuoteArgs">
            <summary>
            ShouldQuote args.
            </summary>
        </member>
        <member name="F:CsvHelper.ShouldQuoteArgs.Field">
            <summary>
            The field.
            </summary>
        </member>
        <member name="F:CsvHelper.ShouldQuoteArgs.FieldType">
            <summary>
            The field type.
            </summary>
        </member>
        <member name="F:CsvHelper.ShouldQuoteArgs.Row">
            <summary>
            The row.
            </summary>
        </member>
        <member name="M:CsvHelper.ShouldQuoteArgs.#ctor(System.String,System.Type,CsvHelper.IWriterRow)">
            <summary>
            Creates a new instance of ShouldQuoteArgs.
            </summary>
            <param name="field">The field.</param>
            <param name="fieldType">The field type.</param>
            <param name="row">The row.</param>
        </member>
        <member name="T:CsvHelper.ShouldSkipRecord">
            <summary>
            Function that determines whether to skip the given record or not.
            </summary>
        </member>
        <member name="T:CsvHelper.ShouldSkipRecordArgs">
            <summary>
            ShouldSkipRecord args.
            </summary>
        </member>
        <member name="F:CsvHelper.ShouldSkipRecordArgs.Row">
            <summary>
            The record.
            </summary>
        </member>
        <member name="M:CsvHelper.ShouldSkipRecordArgs.#ctor(CsvHelper.IReaderRow)">
            <summary>
            Creates a new instance of ShouldSkipRecordArgs.
            </summary>
            <param name="row">The row.</param>
        </member>
        <member name="T:CsvHelper.ShouldUseConstructorParameters">
            <summary>
            Function that determines if constructor parameters should be used to create
            the class instead of the default constructor and members.
            </summary>
        </member>
        <member name="T:CsvHelper.ShouldUseConstructorParametersArgs">
            <summary>
            ShouldUseConstructorParameters args.
            </summary>
        </member>
        <member name="F:CsvHelper.ShouldUseConstructorParametersArgs.ParameterType">
            <summary>
            The parameter type.
            </summary>
        </member>
        <member name="M:CsvHelper.ShouldUseConstructorParametersArgs.#ctor(System.Type)">
            <summary>
            Creates a new instance of ShouldUseConstructorParametersArgs.
            </summary>
            <param name="parameterType">The parameter type.</param>
        </member>
        <member name="T:CsvHelper.Validate">
            <summary>
            Function that validates a field.
            </summary>
            <param name="args">The args.</param>
            <returns><c>true</c> if the field is valid, otherwise <c>false</c>.</returns>
        </member>
        <member name="T:CsvHelper.ValidateMessage">
            <summary>
            Function that gets the exception message when validation fails.
            </summary>
            <param name="args">The args.</param>
            <returns>The exception message.</returns>
        </member>
        <member name="T:CsvHelper.ValidateArgs">
            <summary>
            Validate args.
            </summary>
        </member>
        <member name="F:CsvHelper.ValidateArgs.Field">
            <summary>
            The field.
            </summary>
        </member>
        <member name="F:CsvHelper.ValidateArgs.Row">
            <summary>
            The row.
            </summary>
        </member>
        <member name="M:CsvHelper.ValidateArgs.#ctor(System.String,CsvHelper.IReaderRow)">
            <summary>
            Creates a new instance of ValidateArgs.
            </summary>
            <param name="field">The field.</param>
            <param name="row">The row.</param>
        </member>
        <member name="T:CsvHelper.Expressions.DynamicRecordCreator">
            <summary>
            Creates dynamic records.
            </summary>
        </member>
        <member name="M:CsvHelper.Expressions.DynamicRecordCreator.#ctor(CsvHelper.CsvReader)">
            <summary>
            Initializes a new instance.
            </summary>
            <param name="reader">The reader.</param>
        </member>
        <member name="M:CsvHelper.Expressions.DynamicRecordCreator.CreateCreateRecordDelegate(System.Type)">
            <summary>
            Creates a <see cref="T:System.Delegate"/> of type <see cref="T:System.Func`1"/>
            that will create a record of the given type using the current
            reader row.
            </summary>
            <param name="recordType">The record type.</param>
        </member>
        <member name="M:CsvHelper.Expressions.DynamicRecordCreator.CreateDynamicRecord">
            <summary>
            Creates a dynamic record of the current reader row.
            </summary>
        </member>
        <member name="T:CsvHelper.Expressions.DynamicRecordWriter">
            <summary>
            Write dynamic records.
            </summary>
        </member>
        <member name="M:CsvHelper.Expressions.DynamicRecordWriter.#ctor(CsvHelper.CsvWriter)">
            <summary>
            Initializes a new instance using the given writer.
            </summary>
            <param name="writer">The writer.</param>
        </member>
        <member name="M:CsvHelper.Expressions.DynamicRecordWriter.CreateWriteDelegate``1(``0)">
            <summary>
            Creates a <see cref="T:System.Delegate"/> of type <see cref="T:System.Action`1"/>
            that will write the given record using the current writer row.
            </summary>
            <typeparam name="T">The record type.</typeparam>
            <param name="record">The record.</param>
        </member>
        <member name="T:CsvHelper.Expressions.ExpandoObjectRecordWriter">
            <summary>
            Writes expando objects.
            </summary>
        </member>
        <member name="M:CsvHelper.Expressions.ExpandoObjectRecordWriter.#ctor(CsvHelper.CsvWriter)">
            <summary>
            Initializes a new instance using the given writer.
            </summary>
            <param name="writer">The writer.</param>
        </member>
        <member name="M:CsvHelper.Expressions.ExpandoObjectRecordWriter.CreateWriteDelegate``1(``0)">
            <summary>
            Creates a <see cref="T:System.Delegate"/> of type <see cref="T:System.Action`1"/>
            that will write the given record using the current writer row.
            </summary>
            <typeparam name="T">The record type.</typeparam>
            <param name="record">The record.</param>
        </member>
        <member name="T:CsvHelper.Expressions.ExpressionManager">
            <summary>
            Manages expression creation.
            </summary>
        </member>
        <member name="M:CsvHelper.Expressions.ExpressionManager.#ctor(CsvHelper.CsvReader)">
            <summary>
            Initializes a new instance using the given reader.
            </summary>
            <param name="reader">The reader.</param>
        </member>
        <member name="M:CsvHelper.Expressions.ExpressionManager.#ctor(CsvHelper.CsvWriter)">
            <summary>
            Initializes a new instance using the given writer.
            </summary>
            <param name="writer">The writer.</param>
        </member>
        <member name="M:CsvHelper.Expressions.ExpressionManager.CreateConstructorArgumentExpressionsForMapping(CsvHelper.Configuration.ClassMap,System.Collections.Generic.List{System.Linq.Expressions.Expression})">
            <summary>
            Creates the constructor arguments used to create a type.
            </summary>
            <param name="map">The mapping to create the arguments for.</param>
            <param name="argumentExpressions">The arguments that will be added to the mapping.</param>
        </member>
        <member name="M:CsvHelper.Expressions.ExpressionManager.CreateMemberAssignmentsForMapping(CsvHelper.Configuration.ClassMap,System.Collections.Generic.List{System.Linq.Expressions.MemberAssignment})">
            <summary>
            Creates the member assignments for the given <see cref="T:CsvHelper.Configuration.ClassMap"/>.
            </summary>
            <param name="mapping">The mapping to create the assignments for.</param>
            <param name="assignments">The assignments that will be added to from the mapping.</param>
        </member>
        <member name="M:CsvHelper.Expressions.ExpressionManager.CreateGetFieldExpression(CsvHelper.Configuration.MemberMap)">
            <summary>
            Creates an expression the represents getting the field for the given
            member and converting it to the member's type.
            </summary>
            <param name="memberMap">The mapping for the member.</param>
        </member>
        <member name="M:CsvHelper.Expressions.ExpressionManager.CreateGetMemberExpression(System.Linq.Expressions.Expression,CsvHelper.Configuration.ClassMap,CsvHelper.Configuration.MemberMap)">
            <summary>
            Creates a member expression for the given member on the record.
            This will recursively traverse the mapping to find the member
            and create a safe member accessor for each level as it goes.
            </summary>
            <param name="recordExpression">The current member expression.</param>
            <param name="mapping">The mapping to look for the member to map on.</param>
            <param name="memberMap">The member map to look for on the mapping.</param>
            <returns>An Expression to access the given member.</returns>
        </member>
        <member name="M:CsvHelper.Expressions.ExpressionManager.CreateInstanceAndAssignMembers(System.Type,System.Collections.Generic.List{System.Linq.Expressions.MemberAssignment})">
            <summary>
            Creates an instance of the given type using <see cref="T:CsvHelper.IObjectResolver"/>, then assigns
            the given member assignments to that instance.
            </summary>
            <param name="recordType">The type of the record we're creating.</param>
            <param name="assignments">The member assignments that will be assigned to the created instance.</param>
            <returns>A <see cref="T:System.Linq.Expressions.BlockExpression"/> representing the instance creation and assignments.</returns>
        </member>
        <member name="M:CsvHelper.Expressions.ExpressionManager.CreateTypeConverterExpression(CsvHelper.Configuration.MemberMap,System.Linq.Expressions.Expression)">
            <summary>
            Creates an expression that converts the field expression using a type converter.
            </summary>
            <param name="memberMap">The mapping for the member.</param>
            <param name="fieldExpression">The field expression.</param>
        </member>
        <member name="M:CsvHelper.Expressions.ExpressionManager.CreateTypeConverterExpression(CsvHelper.Configuration.ParameterMap,System.Linq.Expressions.Expression)">
            <summary>
            Creates an expression that converts the field expression using a type converter.
            </summary>
            <param name="parameterMap">The mapping for the parameter.</param>
            <param name="fieldExpression">The field expression.</param>
        </member>
        <member name="M:CsvHelper.Expressions.ExpressionManager.CreateDefaultExpression(CsvHelper.Configuration.MemberMap,System.Linq.Expressions.Expression)">
            <summary>
            Creates a default expression if field expression is empty.
            </summary>
            <param name="memberMap">The mapping for the member.</param>
            <param name="fieldExpression">The field expression.</param>
        </member>
        <member name="M:CsvHelper.Expressions.ExpressionManager.CreateDefaultExpression(CsvHelper.Configuration.ParameterMap,System.Linq.Expressions.Expression)">
            <summary>
            Creates a default expression if field expression is empty.
            </summary>
            <param name="parameterMap">The mapping for the parameter.</param>
            <param name="fieldExpression">The field expression.</param>
        </member>
        <member name="T:CsvHelper.Expressions.ObjectRecordCreator">
            <summary>
            Creates objects.
            </summary>
        </member>
        <member name="M:CsvHelper.Expressions.ObjectRecordCreator.#ctor(CsvHelper.CsvReader)">
            <summary>
            Initializes a new instance using the given reader.
            </summary>
            <param name="reader"></param>
        </member>
        <member name="M:CsvHelper.Expressions.ObjectRecordCreator.CreateCreateRecordDelegate(System.Type)">
            <summary>
            Creates a <see cref="T:System.Delegate"/> of type <see cref="T:System.Func`1"/>
            that will create a record of the given type using the current
            reader row.
            </summary>
            <param name="recordType">The record type.</param>
        </member>
        <member name="T:CsvHelper.Expressions.ObjectRecordWriter">
            <summary>
            Writes objects.
            </summary>
        </member>
        <member name="M:CsvHelper.Expressions.ObjectRecordWriter.#ctor(CsvHelper.CsvWriter)">
            <summary>
            Initializes a new instance using the given writer.
            </summary>
            <param name="writer">The writer.</param>
        </member>
        <member name="M:CsvHelper.Expressions.ObjectRecordWriter.CreateWriteDelegate``1(``0)">
            <summary>
            Creates a <see cref="T:System.Delegate"/> of type <see cref="T:System.Action`1"/>
            that will write the given record using the current writer row.
            </summary>
            <typeparam name="T">The record type.</typeparam>
            <param name="record">The record.</param>
        </member>
        <member name="T:CsvHelper.Expressions.PrimitiveRecordCreator">
            <summary>
            Creates primitive records.
            </summary>
        </member>
        <member name="M:CsvHelper.Expressions.PrimitiveRecordCreator.#ctor(CsvHelper.CsvReader)">
            <summary>
            Initializes a new instance using the given reader.
            </summary>
            <param name="reader">The reader.</param>
        </member>
        <member name="M:CsvHelper.Expressions.PrimitiveRecordCreator.CreateCreateRecordDelegate(System.Type)">
            <summary>
            Creates a <see cref="T:System.Delegate"/> of type <see cref="T:System.Func`1"/>
            that will create a record of the given type using the current
            reader row.
            </summary>
            <param name="recordType">The record type.</param>
        </member>
        <member name="T:CsvHelper.Expressions.PrimitiveRecordWriter">
            <summary>
            Writes primitives.
            </summary>
        </member>
        <member name="M:CsvHelper.Expressions.PrimitiveRecordWriter.#ctor(CsvHelper.CsvWriter)">
            <summary>
            Initializes a new instance using the given writer.
            </summary>
            <param name="writer">The writer.</param>
        </member>
        <member name="M:CsvHelper.Expressions.PrimitiveRecordWriter.CreateWriteDelegate``1(``0)">
            <summary>
            Creates a <see cref="T:System.Delegate"/> of type <see cref="T:System.Action`1"/>
            that will write the given record using the current writer row.
            </summary>
            <typeparam name="T">The record type.</typeparam>
            <param name="record">The record.</param>
        </member>
        <member name="T:CsvHelper.Expressions.RecordCreator">
            <summary>
            Base implementation for classes that create records.
            </summary>
        </member>
        <member name="P:CsvHelper.Expressions.RecordCreator.Reader">
            <summary>
            The reader.
            </summary>
        </member>
        <member name="P:CsvHelper.Expressions.RecordCreator.ExpressionManager">
            <summary>
            The expression manager.
            </summary>
        </member>
        <member name="M:CsvHelper.Expressions.RecordCreator.#ctor(CsvHelper.CsvReader)">
            <summary>
            Initializes a new instance using the given reader.
            </summary>
            <param name="reader">The reader.</param>
        </member>
        <member name="M:CsvHelper.Expressions.RecordCreator.Create``1">
            <summary>
            Create a record of the given type using the current row.
            </summary>
            <typeparam name="T">The record type.</typeparam>
        </member>
        <member name="M:CsvHelper.Expressions.RecordCreator.Create(System.Type)">
            <summary>
            Create a record of the given type using the current row.
            </summary>
            <param name="recordType">The record type.</param>
        </member>
        <member name="M:CsvHelper.Expressions.RecordCreator.GetCreateRecordDelegate(System.Type)">
            <summary>
            Gets the delegate to create a record for the given record type. 
            If the delegate doesn't exist, one will be created and cached.
            </summary>
            <param name="recordType">The record type.</param>
        </member>
        <member name="M:CsvHelper.Expressions.RecordCreator.CreateCreateRecordDelegate(System.Type)">
            <summary>
            Creates a <see cref="T:System.Delegate"/> of type <see cref="T:System.Func`1"/>
            that will create a record of the given type using the current
            reader row.
            </summary>
            <param name="recordType">The record type.</param>
        </member>
        <member name="T:CsvHelper.Expressions.RecordCreatorFactory">
            <summary>
            Factory to create record creators.
            </summary>
        </member>
        <member name="M:CsvHelper.Expressions.RecordCreatorFactory.#ctor(CsvHelper.CsvReader)">
            <summary>
            Initializes a new instance using the given reader.
            </summary>
            <param name="reader">The reader.</param>
        </member>
        <member name="M:CsvHelper.Expressions.RecordCreatorFactory.MakeRecordCreator(System.Type)">
            <summary>
            Creates a record creator for the given record type.
            </summary>
            <param name="recordType">The record type.</param>
        </member>
        <member name="T:CsvHelper.Expressions.RecordHydrator">
            <summary>
            Hydrates members of an existing record.
            </summary>
        </member>
        <member name="M:CsvHelper.Expressions.RecordHydrator.#ctor(CsvHelper.CsvReader)">
            <summary>
            Creates a new instance using the given reader.
            </summary>
            <param name="reader">The reader.</param>
        </member>
        <member name="M:CsvHelper.Expressions.RecordHydrator.Hydrate``1(``0)">
            <summary>
            Hydrates members of the given record using the current reader row.
            </summary>
            <typeparam name="T">The record type.</typeparam>
            <param name="record">The record.</param>
        </member>
        <member name="M:CsvHelper.Expressions.RecordHydrator.GetHydrateRecordAction``1">
            <summary>
            Gets the action delegate used to hydrate a custom class object's members with data from the reader.
            </summary>
            <typeparam name="T">The record type.</typeparam>
        </member>
        <member name="M:CsvHelper.Expressions.RecordHydrator.CreateHydrateRecordAction``1">
            <summary>
            Creates the action delegate used to hydrate a record's members with data from the reader.
            </summary>
            <typeparam name="T">The record type.</typeparam>
        </member>
        <member name="T:CsvHelper.Expressions.RecordManager">
            <summary>
            Manages record manipulation.
            </summary>
        </member>
        <member name="M:CsvHelper.Expressions.RecordManager.#ctor(CsvHelper.CsvReader)">
            <summary>
            Initializes a new instance using the given reader.
            </summary>
            <param name="reader"></param>
        </member>
        <member name="M:CsvHelper.Expressions.RecordManager.#ctor(CsvHelper.CsvWriter)">
            <summary>
            Initializes a new instance using the given writer.
            </summary>
            <param name="writer">The writer.</param>
        </member>
        <member name="M:CsvHelper.Expressions.RecordManager.Create``1">
            <summary>
            Creates a record of the given type using the current reader row.
            </summary>
            <typeparam name="T">The type of record to create.</typeparam>
        </member>
        <member name="M:CsvHelper.Expressions.RecordManager.Create(System.Type)">
            <summary>
            Creates a record of the given type using the current reader row.
            </summary>
            <param name="recordType">The type of record to create.</param>
        </member>
        <member name="M:CsvHelper.Expressions.RecordManager.Hydrate``1(``0)">
            <summary>
            Hydrates the given record using the current reader row.
            </summary>
            <typeparam name="T">The type of the record.</typeparam>
            <param name="record">The record to hydrate.</param>
        </member>
        <member name="M:CsvHelper.Expressions.RecordManager.Write``1(``0)">
            <summary>
            Writes the given record to the current writer row.
            </summary>
            <typeparam name="T">The type of the record.</typeparam>
            <param name="record">The record.</param>
        </member>
        <member name="T:CsvHelper.Expressions.RecordWriter">
            <summary>
            Base implementation for classes that write records.
            </summary>
        </member>
        <member name="P:CsvHelper.Expressions.RecordWriter.Writer">
            <summary>
            Gets the writer.
            </summary>
        </member>
        <member name="P:CsvHelper.Expressions.RecordWriter.ExpressionManager">
            <summary>
            The expression manager.
            </summary>
        </member>
        <member name="M:CsvHelper.Expressions.RecordWriter.#ctor(CsvHelper.CsvWriter)">
            <summary>
            Initializes a new instance using the given writer.
            </summary>
            <param name="writer">The writer.</param>
        </member>
        <member name="M:CsvHelper.Expressions.RecordWriter.Write``1(``0)">
            <summary>
            Writes the record to the current row.
            </summary>
            <typeparam name="T">Type of the record.</typeparam>
            <param name="record">The record.</param>
        </member>
        <member name="M:CsvHelper.Expressions.RecordWriter.GetWriteDelegate``1(``0)">
            <summary>
            Gets the delegate to write the given record. 
            If the delegate doesn't exist, one will be created and cached.
            </summary>
            <typeparam name="T">The record type.</typeparam>
            <param name="record">The record.</param>
        </member>
        <member name="M:CsvHelper.Expressions.RecordWriter.CreateWriteDelegate``1(``0)">
            <summary>
            Creates a <see cref="T:System.Delegate"/> of type <see cref="T:System.Action`1"/>
            that will write the given record using the current writer row.
            </summary>
            <typeparam name="T">The record type.</typeparam>
            <param name="record">The record.</param>
        </member>
        <member name="M:CsvHelper.Expressions.RecordWriter.CombineDelegates``1(System.Collections.Generic.IEnumerable{System.Action{``0}})">
            <summary>
            Combines the delegates into a single multicast delegate.
            This is needed because Silverlight doesn't have the
            Delegate.Combine( params Delegate[] ) overload.
            </summary>
            <param name="delegates">The delegates to combine.</param>
            <returns>A multicast delegate combined from the given delegates.</returns>
        </member>
        <member name="T:CsvHelper.Expressions.RecordWriterFactory">
            <summary>
            Factory to create record writers.
            </summary>
        </member>
        <member name="M:CsvHelper.Expressions.RecordWriterFactory.#ctor(CsvHelper.CsvWriter)">
            <summary>
            Initializes a new instance using the given writer.
            </summary>
            <param name="writer">The writer.</param>
        </member>
        <member name="M:CsvHelper.Expressions.RecordWriterFactory.MakeRecordWriter``1(``0)">
            <summary>
            Creates a new record writer for the given record.
            </summary>
            <typeparam name="T">The type of the record.</typeparam>
            <param name="record">The record.</param>
        </member>
        <member name="T:CsvHelper.Factory">
            <summary>
            Creates CsvHelper classes.
            </summary>
        </member>
        <member name="M:CsvHelper.Factory.CreateParser(System.IO.TextReader,CsvHelper.Configuration.CsvConfiguration)">
            <summary>
            Creates an <see cref="T:CsvHelper.IParser"/>.
            </summary>
            <param name="reader">The text reader to use for the csv parser.</param>
            <param name="configuration">The configuration to use for the csv parser.</param>
            <returns>The created parser.</returns>
        </member>
        <member name="M:CsvHelper.Factory.CreateParser(System.IO.TextReader,System.Globalization.CultureInfo)">
            <summary>
            Creates an <see cref="T:CsvHelper.IParser" />.
            </summary>
            <param name="reader">The text reader to use for the csv parser.</param>
            <param name="cultureInfo">The culture information.</param>
            <returns>
            The created parser.
            </returns>
        </member>
        <member name="M:CsvHelper.Factory.CreateReader(System.IO.TextReader,CsvHelper.Configuration.CsvConfiguration)">
            <summary>
            Creates an <see cref="T:CsvHelper.IReader"/>.
            </summary>
            <param name="reader">The text reader to use for the csv reader.</param>
            <param name="configuration">The configuration to use for the reader.</param>
            <returns>The created reader.</returns>
        </member>
        <member name="M:CsvHelper.Factory.CreateReader(System.IO.TextReader,System.Globalization.CultureInfo)">
            <summary>
            Creates an <see cref="T:CsvHelper.IReader" />.
            </summary>
            <param name="reader">The text reader to use for the csv reader.</param>
            <param name="cultureInfo">The culture information.</param>
            <returns>
            The created reader.
            </returns>
        </member>
        <member name="M:CsvHelper.Factory.CreateReader(CsvHelper.IParser)">
            <summary>
            Creates an <see cref="T:CsvHelper.IReader"/>.
            </summary>
            <param name="parser">The parser used to create the reader.</param>
            <returns>The created reader.</returns>
        </member>
        <member name="M:CsvHelper.Factory.CreateWriter(System.IO.TextWriter,CsvHelper.Configuration.CsvConfiguration)">
            <summary>
            Creates an <see cref="T:CsvHelper.IWriter"/>.
            </summary>
            <param name="writer">The text writer to use for the csv writer.</param>
            <param name="configuration">The configuration to use for the writer.</param>
            <returns>The created writer.</returns>
        </member>
        <member name="M:CsvHelper.Factory.CreateWriter(System.IO.TextWriter,System.Globalization.CultureInfo)">
            <summary>
            Creates an <see cref="T:CsvHelper.IWriter" />.
            </summary>
            <param name="writer">The text writer to use for the csv writer.</param>
            <param name="cultureInfo">The culture information.</param>
            <returns>
            The created writer.
            </returns>
        </member>
        <member name="M:CsvHelper.Factory.CreateClassMapBuilder``1">
            <summary>
            Access point for fluent interface to dynamically build a <see cref="T:CsvHelper.Configuration.ClassMap`1"/>
            </summary>
            <typeparam name="T">Type you will be making a class map for</typeparam>
            <returns>Options to further configure the <see cref="T:CsvHelper.Configuration.ClassMap`1"/></returns>
        </member>
        <member name="T:CsvHelper.FieldCache">
            <summary>
            Caches fields.
            Based on C#'s <see cref="T:System.Collections.Generic.Dictionary`2"/>.
            </summary>
        </member>
        <member name="T:CsvHelper.FieldValidationException">
            <summary>
            Represents a user supplied field validation failure.
            </summary>
        </member>
        <member name="P:CsvHelper.FieldValidationException.Field">
            <summary>
            Gets the field that failed validation.
            </summary>
        </member>
        <member name="M:CsvHelper.FieldValidationException.#ctor(CsvHelper.CsvContext,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.ValidationException"/> class.
            </summary>
            <param name="context">The reading context.</param>
            <param name="field">The field that failed validation.</param>
        </member>
        <member name="M:CsvHelper.FieldValidationException.#ctor(CsvHelper.CsvContext,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.ValidationException"/> class
            with a specified error message.
            </summary>
            <param name="context">The reading context.</param>
            <param name="field">The field that failed validation.</param>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:CsvHelper.FieldValidationException.#ctor(CsvHelper.CsvContext,System.String,System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.ValidationException"/> class
            with a specified error message and a reference to the inner exception that 
            is the cause of this exception.
            </summary>
            <param name="context">The reading context.</param>
            <param name="field">The field that failed validation.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
        </member>
        <member name="T:CsvHelper.HeaderValidationException">
            <summary>
            Represents a header validation failure.
            </summary>
        </member>
        <member name="P:CsvHelper.HeaderValidationException.InvalidHeaders">
            <summary>
            Gets the invalid headers.
            </summary>
        </member>
        <member name="M:CsvHelper.HeaderValidationException.#ctor(CsvHelper.CsvContext,CsvHelper.InvalidHeader[])">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.ValidationException"/> class.
            </summary>
            <param name="context">The reading context.</param>
            <param name="invalidHeaders">The invalid headers.</param>
        </member>
        <member name="M:CsvHelper.HeaderValidationException.#ctor(CsvHelper.CsvContext,CsvHelper.InvalidHeader[],System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.ValidationException"/> class
            with a specified error message.
            </summary>
            <param name="context">The reading context.</param>
            <param name="invalidHeaders">The invalid headers.</param>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:CsvHelper.HeaderValidationException.#ctor(CsvHelper.CsvContext,CsvHelper.InvalidHeader[],System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.ValidationException"/> class
            with a specified error message and a reference to the inner exception that 
            is the cause of this exception.
            </summary>
            <param name="context">The reading context.</param>
            <param name="invalidHeaders">The invalid headers.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
        </member>
        <member name="T:CsvHelper.IFactory">
            <summary>
            Defines methods used to create
            CsvHelper classes.
            </summary>
        </member>
        <member name="M:CsvHelper.IFactory.CreateParser(System.IO.TextReader,CsvHelper.Configuration.CsvConfiguration)">
            <summary>
            Creates an <see cref="T:CsvHelper.IParser"/>.
            </summary>
            <param name="reader">The text reader to use for the csv parser.</param>
            <param name="configuration">The configuration to use for the csv parser.</param>
            <returns>The created parser.</returns>
        </member>
        <member name="M:CsvHelper.IFactory.CreateParser(System.IO.TextReader,System.Globalization.CultureInfo)">
            <summary>
            Creates an <see cref="T:CsvHelper.IParser" />.
            </summary>
            <param name="reader">The text reader to use for the csv parser.</param>
            <param name="cultureInfo">The culture information.</param>
            <returns>
            The created parser.
            </returns>
        </member>
        <member name="M:CsvHelper.IFactory.CreateReader(System.IO.TextReader,CsvHelper.Configuration.CsvConfiguration)">
            <summary>
            Creates an <see cref="T:CsvHelper.IReader"/>.
            </summary>
            <param name="reader">The text reader to use for the csv reader.</param>
            <param name="configuration">The configuration to use for the reader.</param>
            <returns>The created reader.</returns>
        </member>
        <member name="M:CsvHelper.IFactory.CreateReader(System.IO.TextReader,System.Globalization.CultureInfo)">
            <summary>
            Creates an <see cref="T:CsvHelper.IReader" />.
            </summary>
            <param name="reader">The text reader to use for the csv reader.</param>
            <param name="cultureInfo">The culture information.</param>
            <returns>
            The created reader.
            </returns>
        </member>
        <member name="M:CsvHelper.IFactory.CreateReader(CsvHelper.IParser)">
            <summary>
            Creates an <see cref="T:CsvHelper.IReader"/>.
            </summary>
            <param name="parser">The parser used to create the reader.</param>
            <returns>The created reader.</returns>
        </member>
        <member name="M:CsvHelper.IFactory.CreateWriter(System.IO.TextWriter,CsvHelper.Configuration.CsvConfiguration)">
            <summary>
            Creates an <see cref="T:CsvHelper.IWriter"/>.
            </summary>
            <param name="writer">The text writer to use for the csv writer.</param>
            <param name="configuration">The configuration to use for the writer.</param>
            <returns>The created writer.</returns>
        </member>
        <member name="M:CsvHelper.IFactory.CreateWriter(System.IO.TextWriter,System.Globalization.CultureInfo)">
            <summary>
            Creates an <see cref="T:CsvHelper.IWriter" />.
            </summary>
            <param name="writer">The text writer to use for the csv writer.</param>
            <param name="cultureInfo">The culture information.</param>
            <returns>
            The created writer.
            </returns>
        </member>
        <member name="M:CsvHelper.IFactory.CreateClassMapBuilder``1">
            <summary>
            Provides a fluent interface for dynamically creating <see cref="T:CsvHelper.Configuration.ClassMap`1"/>s 
            </summary>
            <typeparam name="T">Type of class to map</typeparam>
            <returns>Next available options</returns>
        </member>
        <member name="T:CsvHelper.InvalidHeader">
            <summary>
            Invalid header information.
            </summary>
        </member>
        <member name="P:CsvHelper.InvalidHeader.Names">
            <summary>
            Header names mapped to a CSV field that couldn't be found.
            </summary>
        </member>
        <member name="P:CsvHelper.InvalidHeader.Index">
            <summary>
            Header name index maped to a CSV field that couldn't be found.
            </summary>
        </member>
        <member name="T:CsvHelper.IObjectResolver">
            <summary>
            Defines the functionality of a class that creates objects
            from a given type.
            </summary>
        </member>
        <member name="P:CsvHelper.IObjectResolver.UseFallback">
            <summary>
            A value indicating if the resolver's <see cref="P:CsvHelper.IObjectResolver.CanResolve"/>
            returns false that an object will still be created using
            CsvHelper's object creation. True to fallback, otherwise false.
            Default value is true.
            </summary>
        </member>
        <member name="P:CsvHelper.IObjectResolver.CanResolve">
            <summary>
            A value indicating if the resolver is able to resolve
            the given type. True if the type can be resolved,
            otherwise false.
            </summary>
        </member>
        <member name="P:CsvHelper.IObjectResolver.ResolveFunction">
            <summary>
            The function that creates an object from a given type.
            </summary>
        </member>
        <member name="M:CsvHelper.IObjectResolver.Resolve(System.Type,System.Object[])">
            <summary>
            Creates an object from the given type using the <see cref="P:CsvHelper.IObjectResolver.ResolveFunction"/>
            function. If <see cref="P:CsvHelper.IObjectResolver.CanResolve"/> is false, the object will be
            created using CsvHelper's default object creation. If <see cref="P:CsvHelper.IObjectResolver.UseFallback"/>
            is false, an exception is thrown.
            </summary>
            <param name="type">The type to create an instance from. The created object
            may not be the same type as the given type.</param>
            <param name="constructorArgs">Constructor arguments used to create the type.</param>
        </member>
        <member name="M:CsvHelper.IObjectResolver.Resolve``1(System.Object[])">
            <summary>
            Creates an object from the given type using the <see cref="P:CsvHelper.IObjectResolver.ResolveFunction"/>
            function. If <see cref="P:CsvHelper.IObjectResolver.CanResolve"/> is false, the object will be
            created using CsvHelper's default object creation. If <see cref="P:CsvHelper.IObjectResolver.UseFallback"/>
            is false, an exception is thrown.
            </summary>
            <typeparam name="T">The type to create an instance from. The created object
            may not be the same type as the given type.</typeparam>
            <param name="constructorArgs">Constructor arguments used to create the type.</param>
        </member>
        <member name="T:CsvHelper.IParser">
            <summary>
            Defines methods used the parse a CSV file.
            </summary>
        </member>
        <member name="P:CsvHelper.IParser.ByteCount">
            <summary>
            Gets the count of how many bytes have been read.
            <see cref="P:CsvHelper.Configuration.IParserConfiguration.CountBytes"/> needs
            to be enabled for this value to be populated.
            </summary>
        </member>
        <member name="P:CsvHelper.IParser.CharCount">
            <summary>
            Gets the count of how many characters have been read.
            </summary>
        </member>
        <member name="P:CsvHelper.IParser.Count">
            <summary>
            Gets the number of fields for the current row.
            </summary>
        </member>
        <member name="P:CsvHelper.IParser.Item(System.Int32)">
            <summary>
            Gets the field at the specified index for the current row.
            </summary>
            <param name="index">The index.</param>
            <returns>The field.</returns>
        </member>
        <member name="P:CsvHelper.IParser.Record">
            <summary>
            Gets the record for the current row. Note:
            It is much more efficient to only get the fields you need. If
            you need all fields, then use this.
            </summary>
        </member>
        <member name="P:CsvHelper.IParser.RawRecord">
            <summary>
            Gets the raw record for the current row.
            </summary>
        </member>
        <member name="P:CsvHelper.IParser.Row">
            <summary>
            Gets the CSV row the parser is currently on.
            </summary>
        </member>
        <member name="P:CsvHelper.IParser.RawRow">
            <summary>
            Gets the raw row the parser is currently on.
            </summary>
        </member>
        <member name="P:CsvHelper.IParser.Delimiter">
            <summary>
            The delimiter the parser is using.
            </summary>
        </member>
        <member name="P:CsvHelper.IParser.Context">
            <summary>
            Gets the reading context.
            </summary>
        </member>
        <member name="P:CsvHelper.IParser.Configuration">
            <summary>
            Gets the configuration.
            </summary>
        </member>
        <member name="M:CsvHelper.IParser.Read">
            <summary>
            Reads a record from the CSV file.
            </summary>
            <returns>True if there are more records to read, otherwise false.</returns>
        </member>
        <member name="M:CsvHelper.IParser.ReadAsync">
            <summary>
            Reads a record from the CSV file asynchronously.
            </summary>
            <returns>True if there are more records to read, otherwise false.</returns>
        </member>
        <member name="T:CsvHelper.IReader">
            <summary>
            Defines methods used to read parsed data
            from a CSV file.
            </summary>
        </member>
        <member name="M:CsvHelper.IReader.ReadHeader">
            <summary>
            Reads the header record without reading the first row.
            </summary>
            <returns>True if there are more records, otherwise false.</returns>
        </member>
        <member name="M:CsvHelper.IReader.Read">
            <summary>
            Advances the reader to the next record. This will not read headers.
            You need to call <see cref="M:CsvHelper.IReader.Read"/> then <see cref="M:CsvHelper.IReader.ReadHeader"/> 
            for the headers to be read.
            </summary>
            <returns>True if there are more records, otherwise false.</returns>
        </member>
        <member name="M:CsvHelper.IReader.ReadAsync">
            <summary>
            Advances the reader to the next record. This will not read headers.
            You need to call <see cref="M:CsvHelper.IReader.ReadAsync"/> then <see cref="M:CsvHelper.IReader.ReadHeader"/> 
            for the headers to be read.
            </summary>
            <returns>True if there are more records, otherwise false.</returns>
        </member>
        <member name="M:CsvHelper.IReader.GetRecords``1">
            <summary>
            Gets all the records in the CSV file and
            converts each to <see cref="T:System.Type"/> T. The Read method
            should not be used when using this.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> of the record.</typeparam>
            <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of records.</returns>
        </member>
        <member name="M:CsvHelper.IReader.GetRecords``1(``0)">
            <summary>
            Gets all the records in the CSV file and converts
            each to <see cref="T:System.Type"/> T. The read method
            should not be used when using this.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> of the record.</typeparam>
            <param name="anonymousTypeDefinition">The anonymous type definition to use for the records.</param>
            <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1"/> of records.</returns>
        </member>
        <member name="M:CsvHelper.IReader.GetRecords(System.Type)">
            <summary>
            Gets all the records in the CSV file and
            converts each to <see cref="T:System.Type"/> T. The Read method
            should not be used when using this.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> of the record.</param>
            <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of records.</returns>
        </member>
        <member name="M:CsvHelper.IReader.EnumerateRecords``1(``0)">
            <summary>
            Enumerates the records hydrating the given record instance with row data.
            The record instance is re-used and not cleared on each enumeration. 
            This only works for streaming rows. If any methods are called on the projection
            that force the evaluation of the IEnumerable, such as ToList(), the entire list
            will contain the same instance of the record, which is the last row.
            </summary>
            <typeparam name="T">The type of the record.</typeparam>
            <param name="record">The record to fill each enumeration.</param>
            <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1"/> of records.</returns>
        </member>
        <member name="M:CsvHelper.IReader.GetRecordsAsync``1(System.Threading.CancellationToken)">
            <summary>
            Gets all the records in the CSV file and
            converts each to <see cref="T:System.Type"/> T. The Read method
            should not be used when using this.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> of the record.</typeparam>
            <param name="cancellationToken">The cancellation token to stop the writing.</param>
            <returns>An <see cref="T:System.Collections.Generic.IAsyncEnumerable`1" /> of records.</returns>
        </member>
        <member name="M:CsvHelper.IReader.GetRecordsAsync``1(``0,System.Threading.CancellationToken)">
            <summary>
            Gets all the records in the CSV file and converts
            each to <see cref="T:System.Type"/> T. The read method
            should not be used when using this.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> of the record.</typeparam>
            <param name="anonymousTypeDefinition">The anonymous type definition to use for the records.</param>
            <param name="cancellationToken">The cancellation token to stop the writing.</param>
            <returns>An <see cref="T:System.Collections.Generic.IAsyncEnumerable`1"/> of records.</returns>
        </member>
        <member name="M:CsvHelper.IReader.GetRecordsAsync(System.Type,System.Threading.CancellationToken)">
            <summary>
            Gets all the records in the CSV file and
            converts each to <see cref="T:System.Type"/> T. The Read method
            should not be used when using this.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> of the record.</param>
            <param name="cancellationToken">The cancellation token to stop the writing.</param>
            <returns>An <see cref="T:System.Collections.Generic.IAsyncEnumerable`1" /> of records.</returns>
        </member>
        <member name="M:CsvHelper.IReader.EnumerateRecordsAsync``1(``0,System.Threading.CancellationToken)">
            <summary>
            Enumerates the records hydrating the given record instance with row data.
            The record instance is re-used and not cleared on each enumeration. 
            This only works for streaming rows. If any methods are called on the projection
            that force the evaluation of the IEnumerable, such as ToList(), the entire list
            will contain the same instance of the record, which is the last row.
            </summary>
            <typeparam name="T">The type of the record.</typeparam>
            <param name="record">The record to fill each enumeration.</param>
            /// <param name="cancellationToken">The cancellation token to stop the writing.</param>
            <returns>An <see cref="T:System.Collections.Generic.IAsyncEnumerable`1"/> of records.</returns>
        </member>
        <member name="T:CsvHelper.IReaderRow">
            <summary>
            Defines methods used to read parsed data
            from a CSV file row.
            </summary>
        </member>
        <member name="P:CsvHelper.IReaderRow.ColumnCount">
            <summary>
            Gets the column count of the current row.
            This should match <see cref="P:CsvHelper.IParser.Count"/>.
            </summary>
        </member>
        <member name="P:CsvHelper.IReaderRow.CurrentIndex">
            <summary>
            Gets the field index the reader is currently on.
            </summary>
        </member>
        <member name="P:CsvHelper.IReaderRow.HeaderRecord">
            <summary>
            Gets the header record.
            </summary>
        </member>
        <member name="P:CsvHelper.IReaderRow.Parser">
            <summary>
            Gets the parser.
            </summary>
        </member>
        <member name="P:CsvHelper.IReaderRow.Context">
            <summary>
            Gets the reading context.
            </summary>
        </member>
        <member name="P:CsvHelper.IReaderRow.Configuration">
            <summary>
            Gets or sets the configuration.
            </summary>
        </member>
        <member name="P:CsvHelper.IReaderRow.Item(System.Int32)">
            <summary>
            Gets the raw field at position (column) index.
            </summary>
            <param name="index">The zero based index of the field.</param>
            <returns>The raw field.</returns>
        </member>
        <member name="P:CsvHelper.IReaderRow.Item(System.String)">
            <summary>
            Gets the raw field at position (column) name.
            </summary>
            <param name="name">The named index of the field.</param>
            <returns>The raw field.</returns>
        </member>
        <member name="P:CsvHelper.IReaderRow.Item(System.String,System.Int32)">
            <summary>
            Gets the raw field at position (column) name.
            </summary>
            <param name="name">The named index of the field.</param>
            <param name="index">The zero based index of the field.</param>
            <returns>The raw field.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.GetField(System.Int32)">
            <summary>
            Gets the raw field at position (column) index.
            </summary>
            <param name="index">The zero based index of the field.</param>
            <returns>The raw field.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.GetField(System.String)">
            <summary>
            Gets the raw field at position (column) name.
            </summary>
            <param name="name">The named index of the field.</param>
            <returns>The raw field.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.GetField(System.String,System.Int32)">
            <summary>
            Gets the raw field at position (column) name and the index
            instance of that field. The index is used when there are
            multiple columns with the same header name.
            </summary>
            <param name="name">The named index of the field.</param>
            <param name="index">The zero based index of the instance of the field.</param>
            <returns>The raw field.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.GetField(System.Type,System.Int32)">
            <summary>
            Gets the field converted to <see cref="T:System.Object"/> using
            the specified <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/>.
            </summary>
            <param name="type">The type of the field.</param>
            <param name="index">The index of the field.</param>
            <returns>The field converted to <see cref="T:System.Object"/>.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.GetField(System.Type,System.String)">
            <summary>
            Gets the field converted to <see cref="T:System.Object"/> using
            the specified <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/>.
            </summary>
            <param name="type">The type of the field.</param>
            <param name="name">The named index of the field.</param>
            <returns>The field converted to <see cref="T:System.Object"/>.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.GetField(System.Type,System.String,System.Int32)">
            <summary>
            Gets the field converted to <see cref="T:System.Object"/> using
            the specified <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/>.
            </summary>
            <param name="type">The type of the field.</param>
            <param name="name">The named index of the field.</param>
            <param name="index">The zero based index of the instance of the field.</param>
            <returns>The field converted to <see cref="T:System.Object"/>.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.GetField(System.Type,System.Int32,CsvHelper.TypeConversion.ITypeConverter)">
            <summary>
            Gets the field converted to <see cref="T:System.Object"/> using
            the specified <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/>.
            </summary>
            <param name="type">The type of the field.</param>
            <param name="index">The index of the field.</param>
            <param name="converter">The <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> used to convert the field to <see cref="T:System.Object"/>.</param>
            <returns>The field converted to <see cref="T:System.Object"/>.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.GetField(System.Type,System.String,CsvHelper.TypeConversion.ITypeConverter)">
            <summary>
            Gets the field converted to <see cref="T:System.Object"/> using
            the specified <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/>.
            </summary>
            <param name="type">The type of the field.</param>
            <param name="name">The named index of the field.</param>
            <param name="converter">The <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> used to convert the field to <see cref="T:System.Object"/>.</param>
            <returns>The field converted to <see cref="T:System.Object"/>.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.GetField(System.Type,System.String,System.Int32,CsvHelper.TypeConversion.ITypeConverter)">
            <summary>
            Gets the field converted to <see cref="T:System.Object"/> using
            the specified <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/>.
            </summary>
            <param name="type">The type of the field.</param>
            <param name="name">The named index of the field.</param>
            <param name="index">The zero based index of the instance of the field.</param>
            <param name="converter">The <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> used to convert the field to <see cref="T:System.Object"/>.</param>
            <returns>The field converted to <see cref="T:System.Object"/>.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.GetField``1(System.Int32)">
            <summary>
            Gets the field converted to <see cref="T:System.Type"/> T at position (column) index.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> of the field.</typeparam>
            <param name="index">The zero based index of the field.</param>
            <returns>The field converted to <see cref="T:System.Type"/> T.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.GetField``1(System.String)">
            <summary>
            Gets the field converted to <see cref="T:System.Type"/> T at position (column) name.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> of the field.</typeparam>
            <param name="name">The named index of the field.</param>
            <returns>The field converted to <see cref="T:System.Type"/> T.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.GetField``1(System.String,System.Int32)">
            <summary>
            Gets the field converted to <see cref="T:System.Type"/> T at position 
            (column) name and the index instance of that field. The index 
            is used when there are multiple columns with the same header name.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="name">The named index of the field.</param>
            <param name="index">The zero based index of the instance of the field.</param>
            <returns></returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.GetField``1(System.Int32,CsvHelper.TypeConversion.ITypeConverter)">
            <summary>
            Gets the field converted to <see cref="T:System.Type"/> T at position (column) index using
            the given <see cref="T:CsvHelper.TypeConversion.ITypeConverter" />.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> of the field.</typeparam>
            <param name="index">The zero based index of the field.</param>
            <param name="converter">The <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> used to convert the field to <see cref="T:System.Type"/> T.</param>
            <returns>The field converted to <see cref="T:System.Type"/> T.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.GetField``1(System.String,CsvHelper.TypeConversion.ITypeConverter)">
            <summary>
            Gets the field converted to <see cref="T:System.Type"/> T at position (column) name using
            the given <see cref="T:CsvHelper.TypeConversion.ITypeConverter" />.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> of the field.</typeparam>
            <param name="name">The named index of the field.</param>
            <param name="converter">The <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> used to convert the field to <see cref="T:System.Type"/> T.</param>
            <returns>The field converted to <see cref="T:System.Type"/> T.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.GetField``1(System.String,System.Int32,CsvHelper.TypeConversion.ITypeConverter)">
            <summary>
            Gets the field converted to <see cref="T:System.Type"/> T at position 
            (column) name and the index instance of that field. The index 
            is used when there are multiple columns with the same header name.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> of the field.</typeparam>
            <param name="name">The named index of the field.</param>
            <param name="index">The zero based index of the instance of the field.</param>
            <param name="converter">The <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> used to convert the field to <see cref="T:System.Type"/> T.</param>
            <returns>The field converted to <see cref="T:System.Type"/> T.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.GetField``2(System.Int32)">
            <summary>
            Gets the field converted to <see cref="T:System.Type"/> T at position (column) index using
            the given <see cref="T:CsvHelper.TypeConversion.ITypeConverter" />.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> of the field.</typeparam>
            <typeparam name="TConverter">The <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> used to convert the field to <see cref="T:System.Type"/> T.</typeparam>
            <param name="index">The zero based index of the field.</param>
            <returns>The field converted to <see cref="T:System.Type"/> T.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.GetField``2(System.String)">
            <summary>
            Gets the field converted to <see cref="T:System.Type"/> T at position (column) name using
            the given <see cref="T:CsvHelper.TypeConversion.ITypeConverter" />.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> of the field.</typeparam>
            <typeparam name="TConverter">The <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> used to convert the field to <see cref="T:System.Type"/> T.</typeparam>
            <param name="name">The named index of the field.</param>
            <returns>The field converted to <see cref="T:System.Type"/> T.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.GetField``2(System.String,System.Int32)">
            <summary>
            Gets the field converted to <see cref="T:System.Type"/> T at position 
            (column) name and the index instance of that field. The index 
            is used when there are multiple columns with the same header name.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> of the field.</typeparam>
            <typeparam name="TConverter">The <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> used to convert the field to <see cref="T:System.Type"/> T.</typeparam>
            <param name="name">The named index of the field.</param>
            <param name="index">The zero based index of the instance of the field.</param>
            <returns>The field converted to <see cref="T:System.Type"/> T.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.TryGetField(System.Type,System.Int32,System.Object@)">
            <summary>
            Gets the field converted to <see cref="T:System.Type"/> T at position (column) index.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> of the field.</param>
            <param name="index">The zero based index of the field.</param>
            <param name="field">The field converted to type T.</param>
            <returns>A value indicating if the get was successful.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.TryGetField(System.Type,System.String,System.Object@)">
            <summary>
            Gets the field converted to <see cref="T:System.Type"/> T at position (column) name.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> of the field.</param>
            <param name="name">The named index of the field.</param>
            <param name="field">The field converted to <see cref="T:System.Type"/> T.</param>
            <returns>A value indicating if the get was successful.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.TryGetField(System.Type,System.String,System.Int32,System.Object@)">
            <summary>
            Gets the field converted to <see cref="T:System.Type"/> T at position 
            (column) name and the index instance of that field. The index 
            is used when there are multiple columns with the same header name.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> of the field.</param>
            <param name="name">The named index of the field.</param>
            <param name="index">The zero based index of the instance of the field.</param>
            <param name="field">The field converted to <see cref="T:System.Type"/> T.</param>
            <returns>A value indicating if the get was successful.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.TryGetField(System.Type,System.Int32,CsvHelper.TypeConversion.ITypeConverter,System.Object@)">
            <summary>
            Gets the field converted to <see cref="T:System.Type"/> T at position (column) index
            using the specified <see cref="T:CsvHelper.TypeConversion.ITypeConverter" />.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> of the field.</param>
            <param name="index">The zero based index of the field.</param>
            <param name="converter">The <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> used to convert the field to <see cref="T:System.Type"/> T.</param>
            <param name="field">The field converted to <see cref="T:System.Type"/> T.</param>
            <returns>A value indicating if the get was successful.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.TryGetField(System.Type,System.String,CsvHelper.TypeConversion.ITypeConverter,System.Object@)">
            <summary>
            Gets the field converted to <see cref="T:System.Type"/> T at position (column) name
            using the specified <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/>.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> of the field.</param>
            <param name="name">The named index of the field.</param>
            <param name="converter">The <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> used to convert the field to <see cref="T:System.Type"/> T.</param>
            <param name="field">The field converted to <see cref="T:System.Type"/> T.</param>
            <returns>A value indicating if the get was successful.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.TryGetField(System.Type,System.String,System.Int32,CsvHelper.TypeConversion.ITypeConverter,System.Object@)">
            <summary>
            Gets the field converted to <see cref="T:System.Type"/> T at position (column) name
            using the specified <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/>.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> of the field.</param>
            <param name="name">The named index of the field.</param>
            <param name="index">The zero based index of the instance of the field.</param>
            <param name="converter">The <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> used to convert the field to <see cref="T:System.Type"/> T.</param>
            <param name="field">The field converted to <see cref="T:System.Type"/> T.</param>
            <returns>A value indicating if the get was successful.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.TryGetField``1(System.Int32,``0@)">
            <summary>
            Gets the field converted to <see cref="T:System.Type"/> T at position (column) index.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> of the field.</typeparam>
            <param name="index">The zero based index of the field.</param>
            <param name="field">The field converted to type T.</param>
            <returns>A value indicating if the get was successful.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.TryGetField``1(System.String,``0@)">
            <summary>
            Gets the field converted to <see cref="T:System.Type"/> T at position (column) name.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> of the field.</typeparam>
            <param name="name">The named index of the field.</param>
            <param name="field">The field converted to <see cref="T:System.Type"/> T.</param>
            <returns>A value indicating if the get was successful.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.TryGetField``1(System.String,System.Int32,``0@)">
            <summary>
            Gets the field converted to <see cref="T:System.Type"/> T at position 
            (column) name and the index instance of that field. The index 
            is used when there are multiple columns with the same header name.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="name">The named index of the field.</param>
            <param name="index">The zero based index of the instance of the field.</param>
            <param name="field">The field converted to <see cref="T:System.Type"/> T.</param>
            <returns>A value indicating if the get was successful.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.TryGetField``1(System.Int32,CsvHelper.TypeConversion.ITypeConverter,``0@)">
            <summary>
            Gets the field converted to <see cref="T:System.Type"/> T at position (column) index
            using the specified <see cref="T:CsvHelper.TypeConversion.ITypeConverter" />.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> of the field.</typeparam>
            <param name="index">The zero based index of the field.</param>
            <param name="converter">The <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> used to convert the field to <see cref="T:System.Type"/> T.</param>
            <param name="field">The field converted to <see cref="T:System.Type"/> T.</param>
            <returns>A value indicating if the get was successful.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.TryGetField``1(System.String,CsvHelper.TypeConversion.ITypeConverter,``0@)">
            <summary>
            Gets the field converted to <see cref="T:System.Type"/> T at position (column) name
            using the specified <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/>.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> of the field.</typeparam>
            <param name="name">The named index of the field.</param>
            <param name="converter">The <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> used to convert the field to <see cref="T:System.Type"/> T.</param>
            <param name="field">The field converted to <see cref="T:System.Type"/> T.</param>
            <returns>A value indicating if the get was successful.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.TryGetField``1(System.String,System.Int32,CsvHelper.TypeConversion.ITypeConverter,``0@)">
            <summary>
            Gets the field converted to <see cref="T:System.Type"/> T at position (column) name
            using the specified <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/>.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> of the field.</typeparam>
            <param name="name">The named index of the field.</param>
            <param name="index">The zero based index of the instance of the field.</param>
            <param name="converter">The <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> used to convert the field to <see cref="T:System.Type"/> T.</param>
            <param name="field">The field converted to <see cref="T:System.Type"/> T.</param>
            <returns>A value indicating if the get was successful.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.TryGetField``2(System.Int32,``0@)">
            <summary>
            Gets the field converted to <see cref="T:System.Type"/> T at position (column) index
            using the specified <see cref="T:CsvHelper.TypeConversion.ITypeConverter" />.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> of the field.</typeparam>
            <typeparam name="TConverter">The <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> used to convert the field to <see cref="T:System.Type"/> T.</typeparam>
            <param name="index">The zero based index of the field.</param>
            <param name="field">The field converted to <see cref="T:System.Type"/> T.</param>
            <returns>A value indicating if the get was successful.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.TryGetField``2(System.String,``0@)">
            <summary>
            Gets the field converted to <see cref="T:System.Type"/> T at position (column) name
            using the specified <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/>.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> of the field.</typeparam>
            <typeparam name="TConverter">The <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> used to convert the field to <see cref="T:System.Type"/> T.</typeparam>
            <param name="name">The named index of the field.</param>
            <param name="field">The field converted to <see cref="T:System.Type"/> T.</param>
            <returns>A value indicating if the get was successful.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.TryGetField``2(System.String,System.Int32,``0@)">
            <summary>
            Gets the field converted to <see cref="T:System.Type"/> T at position (column) name
            using the specified <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/>.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> of the field.</typeparam>
            <typeparam name="TConverter">The <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> used to convert the field to <see cref="T:System.Type"/> T.</typeparam>
            <param name="name">The named index of the field.</param>
            <param name="index">The zero based index of the instance of the field.</param>
            <param name="field">The field converted to <see cref="T:System.Type"/> T.</param>
            <returns>A value indicating if the get was successful.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.GetRecord``1">
            <summary>
            Gets the record converted into <see cref="T:System.Type"/> T.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> of the record.</typeparam>
            <returns>The record converted to <see cref="T:System.Type"/> T.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.GetRecord``1(``0)">
            <summary>
            Get the record converted into <see cref="T:System.Type"/> T.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> of the record.</typeparam>
            <param name="anonymousTypeDefinition">The anonymous type definition to use for the record.</param>
            <returns>The record converted to <see cref="T:System.Type"/> T.</returns>
        </member>
        <member name="M:CsvHelper.IReaderRow.GetRecord(System.Type)">
            <summary>
            Gets the record.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> of the record.</param>
            <returns>The record.</returns>
        </member>
        <member name="T:CsvHelper.IWriter">
            <summary>
            Defines methods used to write to a CSV file.
            </summary>
        </member>
        <member name="M:CsvHelper.IWriter.Flush">
            <summary>
            Flushes the internal buffer to the <see cref="T:System.IO.TextWriter"/> then
            flushes the <see cref="T:System.IO.TextWriter"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.IWriter.FlushAsync">
            <summary>
            Flushes the internal buffer to the <see cref="T:System.IO.TextWriter"/> then
            flushes the <see cref="T:System.IO.TextWriter"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.IWriter.NextRecord">
            <summary>
            Ends writing of the current record and starts a new record.
            This flushes the buffer to the <see cref="T:System.IO.TextWriter"/> but
            does not flush the <see cref="T:System.IO.TextWriter"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.IWriter.NextRecordAsync">
            <summary>
            Ends writing of the current record and starts a new record.
            This flushes the buffer to the <see cref="T:System.IO.TextWriter"/> but
            does not flush the <see cref="T:System.IO.TextWriter"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.IWriter.WriteRecords(System.Collections.IEnumerable)">
            <summary>
            Writes the list of records to the CSV file.
            </summary>
            <param name="records">The records to write.</param>
        </member>
        <member name="M:CsvHelper.IWriter.WriteRecords``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Writes the list of records to the CSV file.
            </summary>
            <typeparam name="T">Record type.</typeparam>
            <param name="records">The records to write.</param>
        </member>
        <member name="M:CsvHelper.IWriter.WriteRecordsAsync(System.Collections.IEnumerable,System.Threading.CancellationToken)">
            <summary>
            Writes the list of records to the CSV file.
            </summary>
            <param name="records">The records to write.</param>
            <param name="cancellationToken">The cancellation token to stop the writing.</param>
        </member>
        <member name="M:CsvHelper.IWriter.WriteRecordsAsync``1(System.Collections.Generic.IEnumerable{``0},System.Threading.CancellationToken)">
            <summary>
            Writes the list of records to the CSV file.
            </summary>
            <typeparam name="T">Record type.</typeparam>
            <param name="records">The records to write.</param>
            <param name="cancellationToken">The cancellation token to stop the writing.</param>
        </member>
        <member name="M:CsvHelper.IWriter.WriteRecordsAsync``1(System.Collections.Generic.IAsyncEnumerable{``0},System.Threading.CancellationToken)">
            <summary>
            Writes the list of records to the CSV file.
            </summary>
            <typeparam name="T">Record type.</typeparam>
            <param name="records">The records to write.</param>
            <param name="cancellationToken">The cancellation token to stop the writing.</param>
        </member>
        <member name="T:CsvHelper.IWriterRow">
            <summary>
            Defines methods used to write a CSV row.
            </summary>
        </member>
        <member name="P:CsvHelper.IWriterRow.HeaderRecord">
            <summary>
            The header record.
            </summary>
        </member>
        <member name="P:CsvHelper.IWriterRow.Row">
            <summary>
            The current row.
            </summary>
        </member>
        <member name="P:CsvHelper.IWriterRow.Index">
            <summary>
            The current field index.
            </summary>
        </member>
        <member name="P:CsvHelper.IWriterRow.Context">
            <summary>
            Gets the writing context.
            </summary>
        </member>
        <member name="P:CsvHelper.IWriterRow.Configuration">
            <summary>
            Gets or sets the configuration.
            </summary>
        </member>
        <member name="M:CsvHelper.IWriterRow.WriteConvertedField(System.String,System.Type)">
            <summary>
            Writes a field that has already been converted to a
            <see cref="T:System.String"/> from an <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/>.
            If the field is null, it won't get written. A type converter 
            will always return a string, even if field is null. If the 
            converter returns a null, it means that the converter has already
            written data, and the returned value should not be written.
            </summary>
            <param name="field">The converted field to write.</param>
            <param name="fieldType">The type of the field before it was converted into a string.</param>
        </member>
        <member name="M:CsvHelper.IWriterRow.WriteField(System.String)">
            <summary>
            Writes the field to the CSV file. The field
            may get quotes added to it.
            When all fields are written for a record,
            <see cref="M:CsvHelper.IWriter.NextRecord" /> must be called
            to complete writing of the current record.
            </summary>
            <param name="field">The field to write.</param>
        </member>
        <member name="M:CsvHelper.IWriterRow.WriteField(System.String,System.Boolean)">
            <summary>
            Writes the field to the CSV file. This will
            ignore any need to quote and ignore
            <see cref="P:CsvHelper.Configuration.CsvConfiguration.ShouldQuote"/>
            and just quote based on the shouldQuote
            parameter.
            When all fields are written for a record,
            <see cref="M:CsvHelper.IWriter.NextRecord" /> must be called
            to complete writing of the current record.
            </summary>
            <param name="field">The field to write.</param>
            <param name="shouldQuote">True to quote the field, otherwise false.</param>
        </member>
        <member name="M:CsvHelper.IWriterRow.WriteField``1(``0)">
            <summary>
            Writes the field to the CSV file.
            When all fields are written for a record,
            <see cref="M:CsvHelper.IWriter.NextRecord" /> must be called
            to complete writing of the current record.
            </summary>
            <typeparam name="T">The type of the field.</typeparam>
            <param name="field">The field to write.</param>
        </member>
        <member name="M:CsvHelper.IWriterRow.WriteField``1(``0,CsvHelper.TypeConversion.ITypeConverter)">
            <summary>
            Writes the field to the CSV file.
            When all fields are written for a record,
            <see cref="M:CsvHelper.IWriter.NextRecord" /> must be called
            to complete writing of the current record.
            </summary>
            <typeparam name="T">The type of the field.</typeparam>
            <param name="field">The field to write.</param>
            <param name="converter">The converter used to convert the field into a string.</param>
        </member>
        <member name="M:CsvHelper.IWriterRow.WriteField``2(``0)">
            <summary>
            Writes the field to the CSV file
            using the given <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/>.
            When all fields are written for a record,
            <see cref="M:CsvHelper.IWriter.NextRecord" /> must be called
            to complete writing of the current record.
            </summary>
            <typeparam name="T">The type of the field.</typeparam>
            <typeparam name="TConverter">The type of the converter.</typeparam>
            <param name="field">The field to write.</param>
        </member>
        <member name="M:CsvHelper.IWriterRow.WriteComment(System.String)">
            <summary>
            Writes a comment.
            </summary>
            <param name="comment">The comment to write.</param>
        </member>
        <member name="M:CsvHelper.IWriterRow.WriteHeader``1">
            <summary>
            Writes the header record from the given members.
            </summary>
            <typeparam name="T">The type of the record.</typeparam>
        </member>
        <member name="M:CsvHelper.IWriterRow.WriteHeader(System.Type)">
            <summary>
            Writes the header record from the given members.
            </summary>
            <param name="type">The type of the record.</param>
        </member>
        <member name="M:CsvHelper.IWriterRow.WriteRecord``1(``0)">
            <summary>
            Writes the record to the CSV file.
            </summary>
            <typeparam name="T">The type of the record.</typeparam>
            <param name="record">The record to write.</param>
        </member>
        <member name="T:CsvHelper.MaxFieldSizeException">
            <summary>
            Represents an error due to a field that is too large.
            </summary>
        </member>
        <member name="M:CsvHelper.MaxFieldSizeException.#ctor(CsvHelper.CsvContext)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.MaxFieldSizeException"/> class.
            </summary>
            <param name="context">The reading context.</param>
        </member>
        <member name="M:CsvHelper.MaxFieldSizeException.#ctor(CsvHelper.CsvContext,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.MaxFieldSizeException"/> class
            with a specified error message.
            </summary>
            <param name="context">The reading context.</param>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:CsvHelper.MaxFieldSizeException.#ctor(CsvHelper.CsvContext,System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.MaxFieldSizeException"/> class
            with a specified error message and a reference to the inner exception that 
            is the cause of this exception.
            </summary>
            <param name="context">The reading context.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
        </member>
        <member name="T:CsvHelper.MissingFieldException">
            <summary>
            Represents an error caused because a field is missing
            in the header while reading a CSV file.
            </summary>
        </member>
        <member name="M:CsvHelper.MissingFieldException.#ctor(CsvHelper.CsvContext)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.MissingFieldException"/> class.
            </summary>
            <param name="context">The reading context.</param>
        </member>
        <member name="M:CsvHelper.MissingFieldException.#ctor(CsvHelper.CsvContext,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.MissingFieldException"/> class
            with a specified error message.
            </summary>
            <param name="context">The reading context.</param>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:CsvHelper.MissingFieldException.#ctor(CsvHelper.CsvContext,System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.MissingFieldException"/> class
            with a specified error message and a reference to the inner exception that 
            is the cause of this exception.
            </summary>
            <param name="context">The reading context.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
        </member>
        <member name="T:CsvHelper.ObjectCreator">
            <summary>
            Efficiently creates instances of object types.
            </summary>
        </member>
        <member name="M:CsvHelper.ObjectCreator.CreateInstance``1(System.Object[])">
            <summary>
            Creates an instance of type T using the given arguments.
            </summary>
            <typeparam name="T">The type to create an instance of.</typeparam>
            <param name="args">The constrcutor arguments.</param>
        </member>
        <member name="M:CsvHelper.ObjectCreator.CreateInstance(System.Type,System.Object[])">
            <summary>
            Creates an instance of the given type using the given arguments.
            </summary>
            <param name="type">The type to create an instance of.</param>
            <param name="args">The constructor arguments.</param>
        </member>
        <member name="T:CsvHelper.ObjectResolver">
            <summary>
            Creates objects from a given type.
            </summary>
        </member>
        <member name="P:CsvHelper.ObjectResolver.Current">
            <summary>
            Gets or sets the current resolver.
            Use an instance of this instead if at all possible.
            </summary>
        </member>
        <member name="P:CsvHelper.ObjectResolver.UseFallback">
            <summary>
            A value indicating if the resolver's <see cref="P:CsvHelper.ObjectResolver.CanResolve"/>
            returns false that an object will still be created using
            CsvHelper's object creation. True to fallback, otherwise false.
            Default value is true.
            </summary>
        </member>
        <member name="P:CsvHelper.ObjectResolver.CanResolve">
            <summary>
            A function that returns a value indicating if the resolver 
            is able to resolve the given type. True if the type can be 
            resolved, otherwise false.
            </summary>
        </member>
        <member name="P:CsvHelper.ObjectResolver.ResolveFunction">
            <summary>
            The function that creates an object from a given type.
            </summary>
        </member>
        <member name="M:CsvHelper.ObjectResolver.#ctor">
            <summary>
            Creates an instance of the object resolver using default values.
            </summary>
        </member>
        <member name="M:CsvHelper.ObjectResolver.#ctor(System.Func{System.Type,System.Boolean},System.Func{System.Type,System.Object[],System.Object},System.Boolean)">
            <summary>
            Creates an instance of the object resolver using the given can create function
            and create function.
            </summary>
            <param name="canResolve">A function that returns a value indicating if the resolver
            is able to resolve the given type. True if the type can be
            resolved, otherwise false.</param>
            <param name="resolveFunction">The function that creates an object from a given type.</param>
            <param name="useFallback">A value indicating if the resolver's <see cref="P:CsvHelper.ObjectResolver.CanResolve"/>
            returns false that an object will still be created using
            CsvHelper's object creation. True to fallback, otherwise false.
            Default value is true.</param>
        </member>
        <member name="M:CsvHelper.ObjectResolver.Resolve(System.Type,System.Object[])">
            <summary>
            Creates an object from the given type using the <see cref="P:CsvHelper.ObjectResolver.ResolveFunction"/>
            function. If <see cref="P:CsvHelper.ObjectResolver.CanResolve"/> is false, the object will be
            created using CsvHelper's default object creation. If <see cref="P:CsvHelper.ObjectResolver.UseFallback"/>
            is false, an exception is thrown.
            </summary>
            <param name="type">The type to create an instance from. The created object
            may not be the same type as the given type.</param>
            <param name="constructorArgs">Constructor arguments used to create the type.</param>
        </member>
        <member name="M:CsvHelper.ObjectResolver.Resolve``1(System.Object[])">
            <summary>
            Creates an object from the given type using the <see cref="P:CsvHelper.ObjectResolver.ResolveFunction"/>
            function. If <see cref="P:CsvHelper.ObjectResolver.CanResolve"/> is false, the object will be
            created using CsvHelper's default object creation. If <see cref="P:CsvHelper.ObjectResolver.UseFallback"/>
            is false, an exception is thrown.
            </summary>
            <typeparam name="T">The type to create an instance from. The created object
            may not be the same type as the given type.</typeparam>
            <param name="constructorArgs">Constructor arguments used to create the type.</param>
        </member>
        <member name="T:CsvHelper.ParserException">
            <summary>
            Represents errors that occur while parsing a CSV file.
            </summary>
        </member>
        <member name="M:CsvHelper.ParserException.#ctor(CsvHelper.CsvContext)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.ParserException"/> class.
            </summary>
            <param name="context">The reading context.</param>
        </member>
        <member name="M:CsvHelper.ParserException.#ctor(CsvHelper.CsvContext,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.ParserException"/> class
            with a specified error message.
            </summary>
            <param name="context">The reading context.</param>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:CsvHelper.ParserException.#ctor(CsvHelper.CsvContext,System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.ParserException"/> class
            with a specified error message and a reference to the inner exception that 
            is the cause of this exception.
            </summary>
            <param name="context">The reading context.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
        </member>
        <member name="T:CsvHelper.ReaderException">
            <summary>
            Represents errors that occur while reading a CSV file.
            </summary>
        </member>
        <member name="M:CsvHelper.ReaderException.#ctor(CsvHelper.CsvContext)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.ReaderException"/> class.
            </summary>
            <param name="context">The reading context.</param>
        </member>
        <member name="M:CsvHelper.ReaderException.#ctor(CsvHelper.CsvContext,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.ReaderException"/> class
            with a specified error message.
            </summary>
            <param name="context">The reading context.</param>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:CsvHelper.ReaderException.#ctor(CsvHelper.CsvContext,System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.ReaderException"/> class
            with a specified error message and a reference to the inner exception that 
            is the cause of this exception.
            </summary>
            <param name="context">The reading context.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
        </member>
        <member name="T:CsvHelper.ReflectionExtensions">
            <summary>
            Extensions to help with reflection.
            </summary>
        </member>
        <member name="M:CsvHelper.ReflectionExtensions.MemberType(System.Reflection.MemberInfo)">
            <summary>
            Gets the type from the member.
            </summary>
            <param name="member">The member to get the type from.</param>
            <returns>The type.</returns>
        </member>
        <member name="M:CsvHelper.ReflectionExtensions.GetMemberExpression(System.Reflection.MemberInfo,System.Linq.Expressions.Expression)">
            <summary>
            Gets a member expression for the member.
            </summary>
            <param name="member">The member to get the expression for.</param>
            <param name="expression">The member expression.</param>
            <returns>The member expression.</returns>
        </member>
        <member name="M:CsvHelper.ReflectionExtensions.IsAnonymous(System.Type)">
            <summary>
            Gets a value indicating if the given type is anonymous.
            True for anonymous, otherwise false.
            </summary>
            <param name="type">The type.</param>
        </member>
        <member name="M:CsvHelper.ReflectionExtensions.HasParameterlessConstructor(System.Type)">
            <summary>
            Gets a value indicating if the given type has a parameterless constructor.
            True if it has a parameterless constructor, otherwise false.
            </summary>
            <param name="type">The type.</param>
        </member>
        <member name="M:CsvHelper.ReflectionExtensions.HasConstructor(System.Type)">
            <summary>
            Gets a value indicating if the given type has any constructors.
            </summary>
            <param name="type">The type.</param>
        </member>
        <member name="M:CsvHelper.ReflectionExtensions.GetConstructorWithMostParameters(System.Type)">
            <summary>
            Gets the constructor that contains the most parameters.
            </summary>
            <param name="type">The type.</param>
        </member>
        <member name="M:CsvHelper.ReflectionExtensions.IsUserDefinedStruct(System.Type)">
            <summary>
            Gets a value indicating if the type is a user defined struct.
            True if it is a user defined struct, otherwise false.
            </summary>
            <param name="type">The type.</param>
        </member>
        <member name="M:CsvHelper.ReflectionExtensions.GetDefinition(System.Reflection.ConstructorInfo)">
            <summary>
            Gets a string representation of the constructor.
            </summary>
            <param name="constructor">The constructor.</param>
        </member>
        <member name="M:CsvHelper.ReflectionExtensions.GetDefinition(System.Reflection.ParameterInfo)">
            <summary>
            Gets a string representation of the parameter.
            </summary>
            <param name="parameter">The parameter.</param>
        </member>
        <member name="T:CsvHelper.ReflectionHelper">
            <summary>
            Common reflection tasks.
            </summary>
        </member>
        <member name="M:CsvHelper.ReflectionHelper.GetDeclaringProperty(System.Type,System.Reflection.PropertyInfo,System.Reflection.BindingFlags)">
            <summary>
            Gets the <see cref="T:System.Reflection.PropertyInfo"/> from the type where the property was declared.
            </summary>
            <param name="type">The type the property belongs to.</param>
            <param name="property">The property to search.</param>
            <param name="flags">Flags for how the property is retrieved.</param>
        </member>
        <member name="M:CsvHelper.ReflectionHelper.GetDeclaringField(System.Type,System.Reflection.FieldInfo,System.Reflection.BindingFlags)">
            <summary>
            Gets the <see cref="T:System.Reflection.FieldInfo"/> from the type where the field was declared.
            </summary>
            <param name="type">The type the field belongs to.</param>
            <param name="field">The field to search.</param>
            <param name="flags">Flags for how the field is retrieved.</param>
        </member>
        <member name="M:CsvHelper.ReflectionHelper.GetUniqueProperties(System.Type,System.Reflection.BindingFlags,System.Boolean)">
            <summary>
            Walk up the inheritance tree collecting properties. This will get a unique set of properties in the
            case where parents have the same property names as children.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> to get properties for.</param>
            <param name="flags">The flags for getting the properties.</param>
            <param name="overwrite">If true, parent class properties that are hidden by `new` child properties will be overwritten.</param>
        </member>
        <member name="M:CsvHelper.ReflectionHelper.GetUniqueFields(System.Type,System.Reflection.BindingFlags,System.Boolean)">
            <summary>
            Walk up the inheritance tree collecting fields. This will get a unique set of fields in the
            case where parents have the same field names as children.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> to get fields for.</param>
            <param name="flags">The flags for getting the fields.</param>
            <param name="overwrite">If true, parent class fields that are hidden by `new` child fields will be overwritten.</param>
        </member>
        <member name="M:CsvHelper.ReflectionHelper.GetMember``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Gets the property from the expression.
            </summary>
            <typeparam name="TModel">The type of the model.</typeparam>
            <typeparam name="TProperty">The type of the property.</typeparam>
            <param name="expression">The expression.</param>
            <returns>The <see cref="T:System.Reflection.PropertyInfo"/> for the expression.</returns>
        </member>
        <member name="M:CsvHelper.ReflectionHelper.GetMembers``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Gets the member inheritance chain as a stack.
            </summary>
            <typeparam name="TModel">The type of the model.</typeparam>
            <typeparam name="TProperty">The type of the property.</typeparam>
            <param name="expression">The member expression.</param>
            <returns>The inheritance chain for the given member expression as a stack.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.ArrayConverter">
            <summary>
            Converts an <see cref="T:System.Array"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.ArrayConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.BigIntegerConverter">
            <summary>
            Converts a <see cref="T:System.Numerics.BigInteger"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.BigIntegerConverter.ConvertToString(System.Object,CsvHelper.IWriterRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the object to a string.
            </summary>
            <param name="value">The object to convert to a string.</param>
            <param name="row">The <see cref="T:CsvHelper.IWriterRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being written.</param>
            <returns>The string representation of the object.</returns>
        </member>
        <member name="M:CsvHelper.TypeConversion.BigIntegerConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.BooleanConverter">
            <summary>
            Converts a <see cref="T:System.Boolean"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.BooleanConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.TypeConversion.BooleanConverter.ConvertToString(System.Object,CsvHelper.IWriterRow,CsvHelper.Configuration.MemberMapData)">
            <inheritdoc/>
        </member>
        <member name="T:CsvHelper.TypeConversion.ByteArrayConverter">
            <summary>
            Converts a <see cref="T:Byte[]"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.ByteArrayConverter.#ctor(CsvHelper.TypeConversion.ByteArrayConverterOptions)">
            <summary>
            Creates a new ByteArrayConverter using the given <see cref="T:CsvHelper.TypeConversion.ByteArrayConverterOptions"/>.
            </summary>
            <param name="options">The options.</param>
        </member>
        <member name="M:CsvHelper.TypeConversion.ByteArrayConverter.ConvertToString(System.Object,CsvHelper.IWriterRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the object to a string.
            </summary>
            <param name="value">The object to convert to a string.</param>
            <param name="row">The <see cref="T:CsvHelper.IWriterRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being written.</param>
            <returns>The string representation of the object.</returns>
        </member>
        <member name="M:CsvHelper.TypeConversion.ByteArrayConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.ByteArrayConverterOptions">
            <summary>
            Options for converting byte arrays.
            </summary>
        </member>
        <member name="F:CsvHelper.TypeConversion.ByteArrayConverterOptions.None">
            <summary>
            No options.
            </summary>
        </member>
        <member name="F:CsvHelper.TypeConversion.ByteArrayConverterOptions.Hexadecimal">
            <summary>
            Hexadecimal encoding.
            </summary>
        </member>
        <member name="F:CsvHelper.TypeConversion.ByteArrayConverterOptions.Base64">
            <summary>
            Base64 encoding.
            </summary>
        </member>
        <member name="F:CsvHelper.TypeConversion.ByteArrayConverterOptions.HexDashes">
            <summary>
            Use dashes in between hex values.
            </summary>
        </member>
        <member name="F:CsvHelper.TypeConversion.ByteArrayConverterOptions.HexInclude0x">
            <summary>
            Prefix hex number with 0x.
            </summary>
        </member>
        <member name="T:CsvHelper.TypeConversion.ByteConverter">
            <summary>
            Converts a <see cref="T:System.Byte"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.ByteConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.CharConverter">
            <summary>
            Converts a <see cref="T:System.Char"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.CharConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.CollectionConverterFactory">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.TypeConversion.CollectionConverterFactory.CanCreate(System.Type)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.TypeConversion.CollectionConverterFactory.Create(System.Type,CsvHelper.TypeConversion.TypeConverterCache,CsvHelper.TypeConversion.ITypeConverter@)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.TypeConversion.CollectionGenericConverter">
            <summary>
            Converts a <see cref="T:System.Collections.ObjectModel.Collection`1"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.CollectionGenericConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.DateTimeConverter">
            <summary>
            Converts a <see cref="T:System.DateTime"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.DateTimeConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.DateTimeOffsetConverter">
            <summary>
            Converts a <see cref="T:System.DateTimeOffset"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.DateTimeOffsetConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.DecimalConverter">
            <summary>
            Converts a <see cref="T:System.Decimal"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.DecimalConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.DefaultTypeConverter">
            <summary>
            Converts an <see cref="T:System.Object"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.DefaultTypeConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.TypeConversion.DefaultTypeConverter.ConvertToString(System.Object,CsvHelper.IWriterRow,CsvHelper.Configuration.MemberMapData)">
            <inheritdoc/>
        </member>
        <member name="T:CsvHelper.TypeConversion.DoubleConverter">
            <summary>
            Converts a <see cref="T:System.Double"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.DoubleConverter.ConvertToString(System.Object,CsvHelper.IWriterRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the object to a string.
            </summary>
            <param name="value">The object to convert to a string.</param>
            <param name="row">The <see cref="T:CsvHelper.IWriterRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being written.</param>
            <returns>The string representation of the object.</returns>
        </member>
        <member name="M:CsvHelper.TypeConversion.DoubleConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.EnumConverter">
            <summary>
            Converts an <see cref="T:System.Enum"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.EnumConverter.#ctor(System.Type)">
            <summary>
            Creates a new <see cref="T:CsvHelper.TypeConversion.EnumConverter"/> for the given <see cref="T:System.Enum"/> <see cref="T:System.Type"/>.
            </summary>
            <param name="type">The type of the Enum.</param>
        </member>
        <member name="M:CsvHelper.TypeConversion.EnumConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <inheritdoc/>
        </member>
        <member name="M:CsvHelper.TypeConversion.EnumConverter.ConvertToString(System.Object,CsvHelper.IWriterRow,CsvHelper.Configuration.MemberMapData)">
            <inheritdoc/>
        </member>
        <member name="T:CsvHelper.TypeConversion.EnumConverterFactory">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.TypeConversion.EnumConverterFactory.CanCreate(System.Type)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.TypeConversion.EnumConverterFactory.Create(System.Type,CsvHelper.TypeConversion.TypeConverterCache,CsvHelper.TypeConversion.ITypeConverter@)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.TypeConversion.EnumerableConverter">
            <summary>
            Throws an exception when used. This is here so that it's apparent
            that there is no support for <see cref="T:System.Collections.IEnumerable"/> type conversion. A custom
            converter will need to be created to have a field convert to and
            from an IEnumerable.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.EnumerableConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Throws an exception.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="M:CsvHelper.TypeConversion.EnumerableConverter.ConvertToString(System.Object,CsvHelper.IWriterRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Throws an exception.
            </summary>
            <param name="value">The object to convert to a string.</param>
            <param name="row">The <see cref="T:CsvHelper.IWriterRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being written.</param>
            <returns>The string representation of the object.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.GuidConverter">
            <summary>
            Converts a <see cref="T:System.Guid"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.GuidConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.IDictionaryConverter">
            <summary>
            Converts an <see cref="T:System.Collections.IDictionary"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.IDictionaryConverter.ConvertToString(System.Object,CsvHelper.IWriterRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the object to a string.
            </summary>
            <param name="value">The object to convert to a string.</param>
            <param name="row">The <see cref="T:CsvHelper.IWriterRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being written.</param>
            <returns>The string representation of the object.</returns>
        </member>
        <member name="M:CsvHelper.TypeConversion.IDictionaryConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.IDictionaryGenericConverter">
            <summary>
            Converts an <see cref="T:System.Collections.Generic.IDictionary`2"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.IDictionaryGenericConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.IEnumerableConverter">
            <summary>
            Converts an <see cref="T:System.Collections.IEnumerable"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.IEnumerableConverter.ConvertToString(System.Object,CsvHelper.IWriterRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the object to a string.
            </summary>
            <param name="value">The object to convert to a string.</param>
            <param name="row"></param>
            <param name="memberMapData"></param>
            <returns>The string representation of the object.</returns>
        </member>
        <member name="M:CsvHelper.TypeConversion.IEnumerableConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.IEnumerableGenericConverter">
            <summary>
            Converts an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.IEnumerableGenericConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.Int16Converter">
            <summary>
            Converts a <see cref="T:System.Int16"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.Int16Converter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.Int32Converter">
            <summary>
            Converts an <see cref="T:System.Int32"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.Int32Converter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.Int64Converter">
            <summary>
            Converts an <see cref="T:System.Int64"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.Int64Converter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.ITypeConverter">
            <summary>
            Converts objects to and from strings.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.ITypeConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="M:CsvHelper.TypeConversion.ITypeConverter.ConvertToString(System.Object,CsvHelper.IWriterRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the object to a string.
            </summary>
            <param name="value">The object to convert to a string.</param>
            <param name="row">The <see cref="T:CsvHelper.IWriterRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being written.</param>
            <returns>The string representation of the object.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.ITypeConverterFactory">
            <summary>
            Produces <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> for the specified <see cref="T:System.Type"/>
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.ITypeConverterFactory.CanCreate(System.Type)">
            <summary>
            Determines if the factory can create a type converter for the given type.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> to be checked</param>
            <returns><c>true</c> if the factory can create the type, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:CsvHelper.TypeConversion.ITypeConverterFactory.Create(System.Type,CsvHelper.TypeConversion.TypeConverterCache,CsvHelper.TypeConversion.ITypeConverter@)">
            <summary>
            Creates a type converter for the given type and assigns it to the given out typeConverter parameter.
            </summary>
            <param name="type">The type to create the converter for.</param>
            <param name="cache">The type converter cache.</param>
            <param name="typeConverter">The parameter to set the converter to.</param>
            <returns><c>true</c> if the converter should be added to the cache, otherwise <c>false</c>.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.NullableConverter">
            <summary>
            Converts a <see cref="T:System.Nullable`1"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="P:CsvHelper.TypeConversion.NullableConverter.NullableType">
            <summary>
            Gets the type of the nullable.
            </summary>
            <value>
            The type of the nullable.
            </value>
        </member>
        <member name="P:CsvHelper.TypeConversion.NullableConverter.UnderlyingType">
            <summary>
            Gets the underlying type of the nullable.
            </summary>
            <value>
            The underlying type.
            </value>
        </member>
        <member name="P:CsvHelper.TypeConversion.NullableConverter.UnderlyingTypeConverter">
            <summary>
            Gets the type converter for the underlying type.
            </summary>
            <value>
            The type converter.
            </value>
        </member>
        <member name="M:CsvHelper.TypeConversion.NullableConverter.#ctor(System.Type,CsvHelper.TypeConversion.TypeConverterCache)">
            <summary>
            Creates a new <see cref="T:CsvHelper.TypeConversion.NullableConverter"/> for the given <see cref="T:System.Nullable`1"/> <see cref="T:System.Type"/>.
            </summary>
            <param name="type">The nullable type.</param>
            <param name="typeConverterFactory">The type converter factory.</param>
            <exception cref="T:System.ArgumentException">type is not a nullable type.</exception>
        </member>
        <member name="M:CsvHelper.TypeConversion.NullableConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="M:CsvHelper.TypeConversion.NullableConverter.ConvertToString(System.Object,CsvHelper.IWriterRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the object to a string.
            </summary>
            <param name="value">The object to convert to a string.</param>
            <param name="row"></param>
            <param name="memberMapData"></param>
            <returns>The string representation of the object.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.NullableConverterFactory">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.TypeConversion.NullableConverterFactory.CanCreate(System.Type)">
            <inheritdoc />
        </member>
        <member name="M:CsvHelper.TypeConversion.NullableConverterFactory.Create(System.Type,CsvHelper.TypeConversion.TypeConverterCache,CsvHelper.TypeConversion.ITypeConverter@)">
            <inheritdoc />
        </member>
        <member name="T:CsvHelper.TypeConversion.SByteConverter">
            <summary>
            Converts a <see cref="T:System.SByte"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.SByteConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.SingleConverter">
            <summary>
            Converts a <see cref="T:System.Single"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.SingleConverter.ConvertToString(System.Object,CsvHelper.IWriterRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the object to a string.
            </summary>
            <param name="value">The object to convert to a string.</param>
            <param name="row">The <see cref="T:CsvHelper.IWriterRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being written.</param>
            <returns>The string representation of the object.</returns>
        </member>
        <member name="M:CsvHelper.TypeConversion.SingleConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.StringConverter">
            <summary>
            Converts a <see cref="T:System.String"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.StringConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.TimeSpanConverter">
            <summary>
            Converts a <see cref="T:System.TimeSpan"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.TimeSpanConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.TypeConverter">
            <summary>
            Throws an exception when used. This is here so that it's apparent
            that there is no support for <see cref="T:System.Type"/> type conversion. A custom
            converter will need to be created to have a field convert to and
            from <see cref="T:System.Type"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Throws an exception.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverter.ConvertToString(System.Object,CsvHelper.IWriterRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Throws an exception.
            </summary>
            <param name="value">The object to convert to a string.</param>
            <param name="row">The <see cref="T:CsvHelper.IWriterRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being written.</param>
            <returns>The string representation of the object.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.TypeConverterCache">
            <summary>
            Caches <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/>s for a given type.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterCache.#ctor">
            <summary>
            Initializes the <see cref="T:CsvHelper.TypeConversion.TypeConverterCache" /> class.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterCache.Contains(System.Type)">
            <summary>
            Determines if there is a converter registered for the given type.
            </summary>
            <param name="type">The type to check.</param>
            <returns><c>true</c> if the converter is registered, otherwise false.</returns>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterCache.AddConverterFactory(CsvHelper.TypeConversion.ITypeConverterFactory)">
            <summary>
            Adds the <see cref="T:CsvHelper.TypeConversion.ITypeConverterFactory"/>.
            Factories are queried in order of being added and first factory that handles the type is used for creating the <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/>.
            </summary>
            <param name="typeConverterFactory">Type converter factory</param>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterCache.AddConverter(System.Type,CsvHelper.TypeConversion.ITypeConverter)">
            <summary>
            Adds the <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> for the given <see cref="T:System.Type"/>.
            </summary>
            <param name="type">The type the converter converts.</param>
            <param name="typeConverter">The type converter that converts the type.</param>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterCache.AddConverter``1(CsvHelper.TypeConversion.ITypeConverter)">
            <summary>
            Adds the <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> for the given <see cref="T:System.Type"/>.
            </summary>
            <typeparam name="T">The type the converter converts.</typeparam>
            <param name="typeConverter">The type converter that converts the type.</param>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterCache.AddConverter(CsvHelper.TypeConversion.ITypeConverter)">
            <summary>
            Adds the given <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> to all registered types.
            </summary>
            <param name="typeConverter">The type converter.</param>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterCache.RemoveConverter(System.Type)">
            <summary>
            Removes the <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> for the given <see cref="T:System.Type"/>.
            </summary>
            <param name="type">The type to remove the converter for.</param>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterCache.RemoveConverter``1">
            <summary>
            Removes the <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> for the given <see cref="T:System.Type"/>.
            </summary>
            <typeparam name="T">The type to remove the converter for.</typeparam>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterCache.RemoveConverterFactory(CsvHelper.TypeConversion.ITypeConverterFactory)">
            <summary>
            Removes the ITypeConverterFactory.
            </summary>
            <param name="typeConverterFactory">The ITypeConverterFactory to remove.</param>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterCache.GetConverter(System.Type)">
            <summary>
            Gets the converter for the given <see cref="T:System.Type"/>.
            </summary>
            <param name="type">The type to get the converter for.</param>
            <returns>The <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> for the given <see cref="T:System.Type"/>.</returns>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterCache.GetConverter(System.Reflection.MemberInfo)">
            <summary>
            Gets the converter for the given member. If an attribute is
            found on the member, that will be used, otherwise the cache
            will be used.
            </summary>
            <param name="member">The member to get the converter for.</param>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterCache.GetConverter``1">
            <summary>
            Gets the converter for the given <see cref="T:System.Type"/>.
            </summary>
            <typeparam name="T">The type to get the converter for.</typeparam>
            <returns>The <see cref="T:CsvHelper.TypeConversion.ITypeConverter"/> for the given <see cref="T:System.Type"/>.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.TypeConverterException">
            <summary>
            Represents errors that occur while reading a CSV file.
            </summary>
        </member>
        <member name="P:CsvHelper.TypeConversion.TypeConverterException.Text">
            <summary>
            The text used in ConvertFromString.
            </summary>
        </member>
        <member name="P:CsvHelper.TypeConversion.TypeConverterException.Value">
            <summary>
            The value used in ConvertToString.
            </summary>
        </member>
        <member name="P:CsvHelper.TypeConversion.TypeConverterException.TypeConverter">
            <summary>
            The type converter.
            </summary>
        </member>
        <member name="P:CsvHelper.TypeConversion.TypeConverterException.MemberMapData">
            <summary>
            The member map data used in ConvertFromString and ConvertToString.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterException.#ctor(CsvHelper.TypeConversion.ITypeConverter,CsvHelper.Configuration.MemberMapData,System.String,CsvHelper.CsvContext)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.TypeConversion.TypeConverterException"/> class.
            </summary>
            <param name="typeConverter">The type converter.</param>
            <param name="memberMapData">The member map data.</param>
            <param name="text">The text.</param>
            <param name="context">The reading context.</param>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterException.#ctor(CsvHelper.TypeConversion.ITypeConverter,CsvHelper.Configuration.MemberMapData,System.Object,CsvHelper.CsvContext)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.TypeConversion.TypeConverterException"/> class.
            </summary>
            <param name="typeConverter">The type converter.</param>
            <param name="memberMapData">The member map data.</param>
            <param name="value">The value.</param>
            <param name="context">The writing context.</param>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterException.#ctor(CsvHelper.TypeConversion.ITypeConverter,CsvHelper.Configuration.MemberMapData,System.String,CsvHelper.CsvContext,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.TypeConversion.TypeConverterException"/> class
            with a specified error message.
            </summary>
            <param name="typeConverter">The type converter.</param>
            <param name="memberMapData">The member map data.</param>
            <param name="text">The text.</param>
            <param name="context">The reading context.</param>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterException.#ctor(CsvHelper.TypeConversion.ITypeConverter,CsvHelper.Configuration.MemberMapData,System.Object,CsvHelper.CsvContext,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.TypeConversion.TypeConverterException"/> class
            with a specified error message.
            </summary>
            <param name="typeConverter">The type converter.</param>
            <param name="memberMapData">The member map data.</param>
            <param name="value">The value.</param>
            <param name="context">The writing context.</param>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterException.#ctor(CsvHelper.TypeConversion.ITypeConverter,CsvHelper.Configuration.MemberMapData,System.String,CsvHelper.CsvContext,System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.TypeConversion.TypeConverterException"/> class
            with a specified error message and a reference to the inner exception that 
            is the cause of this exception.
            </summary>
            <param name="typeConverter">The type converter.</param>
            <param name="memberMapData">The member map data.</param>
            <param name="text">The text.</param>
            <param name="context">The reading context.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterException.#ctor(CsvHelper.TypeConversion.ITypeConverter,CsvHelper.Configuration.MemberMapData,System.Object,CsvHelper.CsvContext,System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.TypeConversion.TypeConverterException"/> class
            with a specified error message and a reference to the inner exception that 
            is the cause of this exception.
            </summary>
            <param name="typeConverter">The type converter.</param>
            <param name="memberMapData">The member map data.</param>
            <param name="value">The value.</param>
            <param name="context">The writing context.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
        </member>
        <member name="T:CsvHelper.TypeConversion.TypeConverterOptions">
            <summary>
            Options used when doing type conversion.
            </summary>
        </member>
        <member name="P:CsvHelper.TypeConversion.TypeConverterOptions.CultureInfo">
            <summary>
            Gets or sets the culture info.
            </summary>
        </member>
        <member name="P:CsvHelper.TypeConversion.TypeConverterOptions.DateTimeStyle">
            <summary>
            Gets or sets the date time style.
            </summary>
        </member>
        <member name="P:CsvHelper.TypeConversion.TypeConverterOptions.TimeSpanStyle">
            <summary>
            Gets or sets the time span style.
            </summary>
        </member>
        <member name="P:CsvHelper.TypeConversion.TypeConverterOptions.NumberStyles">
            <summary>
            Gets or sets the number style.
            </summary>
        </member>
        <member name="P:CsvHelper.TypeConversion.TypeConverterOptions.Formats">
            <summary>
            Gets or sets the string format.
            </summary>
        </member>
        <member name="P:CsvHelper.TypeConversion.TypeConverterOptions.UriKind">
            <summary>
            Gets or sets the <see cref="P:CsvHelper.TypeConversion.TypeConverterOptions.UriKind"/>.
            </summary>
        </member>
        <member name="P:CsvHelper.TypeConversion.TypeConverterOptions.EnumIgnoreCase">
            <summary>
            Ingore case when parsing enums. Default is false.
            </summary>
        </member>
        <member name="P:CsvHelper.TypeConversion.TypeConverterOptions.BooleanTrueValues">
            <summary>
            Gets the list of values that can be
            used to represent a boolean of true.
            </summary>
        </member>
        <member name="P:CsvHelper.TypeConversion.TypeConverterOptions.BooleanFalseValues">
            <summary>
            Gets the list of values that can be
            used to represent a boolean of false.
            </summary>
        </member>
        <member name="P:CsvHelper.TypeConversion.TypeConverterOptions.NullValues">
            <summary>
            Gets the list of values that can be used to represent a null value.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterOptions.Merge(CsvHelper.TypeConversion.TypeConverterOptions[])">
            <summary>
            Merges TypeConverterOptions by applying the values of sources in order on to each other.
            The first object is the source object.
            </summary>
            <param name="sources">The sources that will be applied.</param>
            <returns>The updated source object.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.TypeConverterOptionsCache">
            <summary>
            Caches <see cref="T:CsvHelper.TypeConversion.TypeConverterOptions"/> for a given type.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterOptionsCache.AddOptions(System.Type,CsvHelper.TypeConversion.TypeConverterOptions)">
            <summary>
            Adds the <see cref="T:CsvHelper.TypeConversion.TypeConverterOptions"/> for the given <see cref="T:System.Type"/>.
            </summary>
            <param name="type">The type the options are for.</param>
            <param name="options">The options.</param>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterOptionsCache.AddOptions``1(CsvHelper.TypeConversion.TypeConverterOptions)">
            <summary>
            Adds the <see cref="T:CsvHelper.TypeConversion.TypeConverterOptions"/> for the given <see cref="T:System.Type"/>.
            </summary>
            <typeparam name="T">The type the options are for.</typeparam>
            <param name="options">The options.</param>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterOptionsCache.AddOptions(CsvHelper.TypeConversion.TypeConverterOptions)">
            <summary>
            Adds the given <see cref="T:CsvHelper.TypeConversion.TypeConverterOptions"/> to all registered types.
            </summary>
            <param name="options"></param>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterOptionsCache.RemoveOptions(System.Type)">
            <summary>
            Removes the <see cref="T:CsvHelper.TypeConversion.TypeConverterOptions"/> for the given type.
            </summary>
            <param name="type">The type to remove the options for.</param>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterOptionsCache.RemoveOptions``1">
            <summary>
            Removes the <see cref="T:CsvHelper.TypeConversion.TypeConverterOptions"/> for the given type.
            </summary>
            <typeparam name="T">The type to remove the options for.</typeparam>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterOptionsCache.GetOptions(System.Type)">
            <summary>
            Get the <see cref="T:CsvHelper.TypeConversion.TypeConverterOptions"/> for the given <see cref="T:System.Type"/>.
            </summary>
            <param name="type">The type the options are for.</param>
            <returns>The options for the given type.</returns>
        </member>
        <member name="M:CsvHelper.TypeConversion.TypeConverterOptionsCache.GetOptions``1">
            <summary>
            Get the <see cref="T:CsvHelper.TypeConversion.TypeConverterOptions"/> for the given <see cref="T:System.Type"/>.
            </summary>
            <typeparam name="T">The type the options are for.</typeparam>
            <returns>The options for the given type.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.UInt16Converter">
            <summary>
            Converts a <see cref="T:System.UInt16"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.UInt16Converter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.UInt32Converter">
            <summary>
            Converts a <see cref="T:System.UInt32"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.UInt32Converter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.UInt64Converter">
            <summary>
            Converts a <see cref="T:System.UInt64"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.UInt64Converter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the string to an object.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow"/> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData"/> for the member being created.</param>
            <returns>The object created from the string.</returns>
        </member>
        <member name="T:CsvHelper.TypeConversion.UriConverter">
            <summary>
            Converts a <see cref="T:System.Uri"/> to and from a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:CsvHelper.TypeConversion.UriConverter.ConvertFromString(System.String,CsvHelper.IReaderRow,CsvHelper.Configuration.MemberMapData)">
            <summary>
            Converts the <see cref="T:System.String"/>  to a <see cref="T:System.Uri"/>.
            </summary>
            <param name="text">The string to convert to an object.</param>
            <param name="row">The <see cref="T:CsvHelper.IReaderRow" /> for the current record.</param>
            <param name="memberMapData">The <see cref="T:CsvHelper.Configuration.MemberMapData" /> for the member being created.</param>
            <returns>
            The <see cref="T:System.Uri"/> created from the string.
            </returns>
        </member>
        <member name="T:CsvHelper.ValidationException">
            <summary>
            Represents a user supplied validation failure.
            </summary>
        </member>
        <member name="M:CsvHelper.ValidationException.#ctor(CsvHelper.CsvContext)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.ValidationException"/> class.
            </summary>
            <param name="context">The reading context.</param>
        </member>
        <member name="M:CsvHelper.ValidationException.#ctor(CsvHelper.CsvContext,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.ValidationException"/> class
            with a specified error message.
            </summary>
            <param name="context">The reading context.</param>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:CsvHelper.ValidationException.#ctor(CsvHelper.CsvContext,System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.ValidationException"/> class
            with a specified error message and a reference to the inner exception that 
            is the cause of this exception.
            </summary>
            <param name="context">The reading context.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
        </member>
        <member name="T:CsvHelper.WriterException">
            <summary>
            Represents errors that occur while writing a CSV file.
            </summary>
        </member>
        <member name="M:CsvHelper.WriterException.#ctor(CsvHelper.CsvContext)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.WriterException"/> class.
            </summary>
            <param name="context">The writing context.</param>
        </member>
        <member name="M:CsvHelper.WriterException.#ctor(CsvHelper.CsvContext,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.WriterException"/> class
            with a specified error message.
            </summary>
            <param name="context">The writing context.</param>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:CsvHelper.WriterException.#ctor(CsvHelper.CsvContext,System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:CsvHelper.WriterException"/> class
            with a specified error message and a reference to the inner exception that 
            is the cause of this exception.
            </summary>
            <param name="context">The writing context.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
        </member>
    </members>
</doc>
