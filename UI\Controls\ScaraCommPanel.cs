using System;
using System.Drawing;
using System.Windows.Forms;
using MyHMI.Helpers;

namespace MyHMI.UI.Controls
{
    /// <summary>
    /// Scara通信管理面板 - 按HTML原型设计
    /// </summary>
    public partial class ScaraCommPanel : UserControl
    {
        #region 私有字段

        private Panel _mainPanel;
        private Label _titleLabel;
        private Panel _commSettingsGroup;
        private Panel _commMonitorGroup;

        #endregion

        #region 构造函数

        public ScaraCommPanel()
        {
            InitializeComponent();
            InitializeInterface();
        }

        #endregion

        #region 界面初始化

        /// <summary>
        /// 初始化界面 - 按HTML原型精确设计
        /// </summary>
        private void InitializeInterface()
        {
            try
            {
                // 设置面板属性 - 按HTML原型 660x840 (700-40padding)
                this.Size = new Size(660, 840);
                this.BackColor = ColorTranslator.FromHtml("#34495e");
                this.Dock = DockStyle.Fill;
                this.Padding = new Padding(0);

                // 创建主面板 - 按HTML原型padding: 20px
                CreateMainPanel();

                // 创建标题 - 按HTML原型样式
                CreateTitle();

                // 创建通信设置组 - 按HTML原型样式
                CreateCommSettingsGroup();

                // 创建通信监控组 - 按HTML原型样式
                CreateCommMonitorGroup();

                LogHelper.Info("Scara通信管理面板初始化完成 - 按HTML原型设计");
            }
            catch (Exception ex)
            {
                LogHelper.Error("Scara通信管理面板初始化失败", ex);
            }
        }

        /// <summary>
        /// 创建主面板 - 按HTML原型
        /// </summary>
        private void CreateMainPanel()
        {
            _mainPanel = new Panel
            {
                Size = new Size(660, 840),
                Location = new Point(0, 0),
                BackColor = ColorTranslator.FromHtml("#34495e"),
                Padding = new Padding(20), // 按HTML原型 padding: 20px
                Dock = DockStyle.Fill
            };

            this.Controls.Add(_mainPanel);
        }

        /// <summary>
        /// 创建标题 - 按HTML原型样式
        /// </summary>
        private void CreateTitle()
        {
            _titleLabel = new Label
            {
                Text = "scara通信管理",
                Font = new Font("微软雅黑", 20F, FontStyle.Bold), // 按HTML原型 font-size: 20px
                ForeColor = ColorTranslator.FromHtml("#3498db"),   // 按HTML原型 color: #3498db
                Size = new Size(300, 30),
                Location = new Point(0, 0),
                AutoSize = true
            };

            _mainPanel.Controls.Add(_titleLabel);
        }

        /// <summary>
        /// 创建通信设置组 - 按HTML原型样式
        /// </summary>
        private void CreateCommSettingsGroup()
        {
            _commSettingsGroup = new Panel
            {
                Size = new Size(600, 200), // 调整尺寸确保不超出功能区域
                Location = new Point(0, 50), // 标题下方20px间距
                BackColor = ColorTranslator.FromHtml("#2c3e50"), // 按HTML原型
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15) // 按HTML原型 padding
            };

            // 设置边框颜色 - 按HTML原型 border: 1px solid #4a5661
            _commSettingsGroup.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _commSettingsGroup.Width - 1, _commSettingsGroup.Height - 1);
                }
            };

            // 创建组标题 - 按HTML原型 h3
            var groupTitle = new Label
            {
                Text = "通信设置",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold), // 按HTML原型 h3
                ForeColor = Color.White,
                Size = new Size(200, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };

            // 协议类型 - 按HTML原型
            CreateInputGroup(_commSettingsGroup, "协议类型:", "Modbus TCP", "", 35, true);

            // IP地址 - 按HTML原型
            CreateInputGroup(_commSettingsGroup, "IP地址:", "*************", "", 65, false);

            // 端口 - 按HTML原型
            CreateInputGroup(_commSettingsGroup, "端口:", "502", "", 95, false);

            // 从站ID - 按HTML原型
            CreateInputGroup(_commSettingsGroup, "从站ID:", "1", "", 125, false);

            // 连接按钮组 - 按HTML原型
            var btnConnect = CreateButton("连接", new Point(0, 155), ColorTranslator.FromHtml("#27ae60"));
            var btnDisconnect = CreateButton("断开", new Point(110, 155), ColorTranslator.FromHtml("#e74c3c"));
            var btnReconnect = CreateButton("重连", new Point(220, 155), ColorTranslator.FromHtml("#f39c12"));

            _commSettingsGroup.Controls.Add(groupTitle);
            _commSettingsGroup.Controls.Add(btnConnect);
            _commSettingsGroup.Controls.Add(btnDisconnect);
            _commSettingsGroup.Controls.Add(btnReconnect);

            _mainPanel.Controls.Add(_commSettingsGroup);
        }

        /// <summary>
        /// 创建通信监控组 - 按HTML原型样式
        /// </summary>
        private void CreateCommMonitorGroup()
        {
            _commMonitorGroup = new Panel
            {
                Size = new Size(600, 180), // 调整尺寸确保不超出功能区域
                Location = new Point(0, 270), // 通信设置组下方20px间距
                BackColor = ColorTranslator.FromHtml("#2c3e50"), // 按HTML原型
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15) // 按HTML原型 padding
            };

            // 设置边框颜色 - 按HTML原型 border: 1px solid #4a5661
            _commMonitorGroup.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _commMonitorGroup.Width - 1, _commMonitorGroup.Height - 1);
                }
            };

            // 创建组标题 - 按HTML原型 h3
            var groupTitle = new Label
            {
                Text = "通信监控",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold), // 按HTML原型 h3
                ForeColor = Color.White,
                Size = new Size(200, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };

            // 通信状态 - 按HTML原型
            var statusLabel = new Label
            {
                Text = "通信状态: ",
                Font = new Font("微软雅黑", 14F), // 按HTML原型
                ForeColor = Color.White,
                Size = new Size(100, 20),
                Location = new Point(0, 35),
                AutoSize = true
            };

            var statusValue = new Label
            {
                Text = "已连接",
                Font = new Font("微软雅黑", 14F), // 按HTML原型
                ForeColor = ColorTranslator.FromHtml("#27ae60"), // 按HTML原型 color: #27ae60
                Size = new Size(100, 20),
                Location = new Point(100, 35),
                AutoSize = true
            };

            // 监控文本框 - 按HTML原型
            var monitorTextBox = new TextBox
            {
                Font = new Font("Consolas", 12F), // 按HTML原型 font-size: 12px
                Size = new Size(570, 100), // 按HTML原型 height: 100px
                Location = new Point(0, 65),
                BackColor = ColorTranslator.FromHtml("#1e1e1e"), // 按HTML原型 background-color: #1e1e1e
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                Text = "[10:30:15] 连接成功\r\n[10:30:20] 读取寄存器 40001-40010\r\n[10:30:21] 返回数据: 1000,2000,3000..." // 按HTML原型示例数据
            };

            _commMonitorGroup.Controls.Add(groupTitle);
            _commMonitorGroup.Controls.Add(statusLabel);
            _commMonitorGroup.Controls.Add(statusValue);
            _commMonitorGroup.Controls.Add(monitorTextBox);

            _mainPanel.Controls.Add(_commMonitorGroup);
        }

        /// <summary>
        /// 创建输入组 - 按HTML原型样式
        /// </summary>
        private void CreateInputGroup(Panel parent, string labelText, string defaultValue, string unit, int yOffset, bool isSelect)
        {
            // 标签 - 按HTML原型
            var label = new Label
            {
                Text = labelText,
                Font = new Font("微软雅黑", 16F), // 按HTML原型 label
                ForeColor = Color.White,
                Size = new Size(100, 30), // 按HTML原型
                Location = new Point(0, yOffset),
                TextAlign = ContentAlignment.MiddleLeft
            };

            if (isSelect)
            {
                // 下拉框 - 按HTML原型 select
                var comboBox = new ComboBox
                {
                    Font = new Font("微软雅黑", 14F), // 按HTML原型
                    Size = new Size(150, 30), // 按HTML原型 width: 150px, height: 30px
                    Location = new Point(110, yOffset),
                    BackColor = ColorTranslator.FromHtml("#34495e"),
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat,
                    DropDownStyle = ComboBoxStyle.DropDownList
                };
                comboBox.Items.Add("Modbus TCP");
                comboBox.SelectedIndex = 0;

                parent.Controls.Add(comboBox);
            }
            else
            {
                // 输入框 - 按HTML原型
                var textBox = new TextBox
                {
                    Text = defaultValue,
                    Font = new Font("微软雅黑", 14F), // 按HTML原型
                    Size = new Size(150, 30), // 按HTML原型 width: 150px, height: 30px
                    Location = new Point(110, yOffset),
                    BackColor = ColorTranslator.FromHtml("#34495e"),
                    ForeColor = Color.White,
                    BorderStyle = BorderStyle.FixedSingle
                };

                parent.Controls.Add(textBox);
            }

            parent.Controls.Add(label);
        }

        /// <summary>
        /// 创建按钮 - 按HTML原型样式
        /// </summary>
        private Button CreateButton(string text, Point location, Color backColor)
        {
            var button = new Button
            {
                Text = text,
                Location = location,
                Size = new Size(100, 35), // 按HTML原型调整按钮尺寸
                BackColor = backColor,
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 14F, FontStyle.Bold), // 按HTML原型字体
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false,
                Cursor = Cursors.Hand
            };

            // 设置按钮边框 - 按HTML原型
            button.FlatAppearance.BorderSize = 1;
            button.FlatAppearance.BorderColor = ColorTranslator.FromHtml("#4a5661");

            return button;
        }

        #endregion
    }
}
