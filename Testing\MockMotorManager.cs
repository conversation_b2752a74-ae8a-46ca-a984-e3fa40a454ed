using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MyHMI.Events;
using MyHMI.Models;
using MyHMI.Helpers;

namespace MyHMI.Testing
{
    /// <summary>
    /// 电机管理器Mock类，用于无硬件测试
    /// </summary>
    public class MockMotorManager
    {
        #region 单例模式
        private static readonly Lazy<MockMotorManager> _instance = new Lazy<MockMotorManager>(() => new MockMotorManager());
        public static MockMotorManager Instance => _instance.Value;
        private MockMotorManager() { }
        #endregion

        #region 私有字段
        private bool _isInitialized = false;
        private Dictionary<int, MotorStatus> _motorStates = new Dictionary<int, MotorStatus>();
        private Dictionary<int, MotorParams> _motorParams = new Dictionary<int, MotorParams>();
        private Random _random = new Random();
        #endregion

        #region 公共属性
        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;
        #endregion

        #region 事件定义
        /// <summary>
        /// 电机位置变化事件
        /// </summary>
        public event EventHandler<MotorPositionEventArgs> MotorPositionChanged;

        /// <summary>
        /// 电机状态变化事件
        /// </summary>
        public event EventHandler<MotorStatusEventArgs> MotorStatusChanged;
        #endregion

        #region 初始化和释放
        /// <summary>
        /// 异步初始化Mock电机管理器
        /// </summary>
        /// <returns></returns>
        public async Task<bool> InitializeAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_isInitialized)
                {
                    LogHelper.Warning("MockMotorManager已经初始化，跳过重复初始化");
                    return true;
                }

                LogHelper.Info("开始初始化MockMotorManager...");

                // 模拟初始化延迟
                await Task.Delay(800);

                // 初始化4个轴的状态
                for (int i = 0; i < 4; i++)
                {
                    _motorStates[i] = new MotorStatus
                    {
                        AxisId = i,
                        CurrentPosition = 0,
                        TargetPosition = 0,
                        IsMoving = false,
                        IsHomed = false,
                        HasError = false,
                        ErrorMessage = "",
                        Speed = 0,
                        LastUpdateTime = DateTime.Now
                    };

                    _motorParams[i] = new MotorParams
                    {
                        AxisId = i,
                        MaxSpeed = 5000,
                        MaxAcceleration = 10000,
                        HomeSpeed = 500,
                        MaxPosition = 100000,
                        MinPosition = -100000,
                        PositionTolerance = 0.01
                    };
                }

                _isInitialized = true;
                LogHelper.Info("MockMotorManager初始化完成");
                return true;

            }, false, "MockMotorManager初始化");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public async Task DisposeAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info("开始释放MockMotorManager资源...");

                // 停止所有电机
                await StopAllMotorsAsync();

                await Task.Delay(200); // 模拟释放延迟

                _isInitialized = false;
                LogHelper.Info("MockMotorManager资源释放完成");

                return true;
            }, false, "MockMotorManager资源释放");
        }
        #endregion

        #region 电机控制方法
        /// <summary>
        /// 移动到指定位置
        /// </summary>
        /// <param name="axisId">轴号</param>
        /// <param name="position">目标位置</param>
        /// <param name="speed">运动速度</param>
        /// <returns>是否成功</returns>
        public async Task<bool> MoveToAsync(int axisId, double position, double speed)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (!_isInitialized)
                {
                    throw new InvalidOperationException("MockMotorManager未初始化");
                }

                if (!_motorStates.ContainsKey(axisId))
                {
                    throw new ArgumentException($"轴{axisId}不存在");
                }

                var motor = _motorStates[axisId];
                var param = _motorParams[axisId];

                // 检查位置范围
                if (position < param.MinPosition || position > param.MaxPosition)
                {
                    throw new ArgumentOutOfRangeException(nameof(position), 
                        $"位置{position}超出范围[{param.MinPosition}, {param.MaxPosition}]");
                }

                // 检查速度范围
                if (speed <= 0 || speed > param.MaxSpeed)
                {
                    throw new ArgumentOutOfRangeException(nameof(speed), 
                        $"速度{speed}超出范围(0, {param.MaxSpeed}]");
                }

                LogHelper.Info($"Mock轴{axisId}开始移动: {motor.CurrentPosition:F3} -> {position:F3}, 速度: {speed:F1}");

                // 更新电机状态
                motor.TargetPosition = position;
                motor.IsMoving = true;
                motor.Speed = speed;
                motor.HasError = false;
                motor.ErrorMessage = "";
                motor.LastUpdateTime = DateTime.Now;

                // 触发状态变化事件
                MotorStatusChanged?.Invoke(this, new MotorStatusEventArgs(axisId, motor));

                // 模拟运动过程
                await SimulateMotorMovementAsync(axisId, position, speed);

                return true;

            }, false, $"轴{axisId}移动到位置{position}");
        }

        /// <summary>
        /// 停止电机
        /// </summary>
        /// <param name="axisId">轴号</param>
        /// <returns>是否成功</returns>
        public async Task<bool> StopMotorAsync(int axisId)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (!_isInitialized)
                {
                    throw new InvalidOperationException("MockMotorManager未初始化");
                }

                if (!_motorStates.ContainsKey(axisId))
                {
                    throw new ArgumentException($"轴{axisId}不存在");
                }

                var motor = _motorStates[axisId];

                LogHelper.Info($"Mock轴{axisId}停止运动");

                // 模拟停止延迟
                await Task.Delay(100);

                // 更新电机状态
                motor.IsMoving = false;
                motor.Speed = 0;
                motor.TargetPosition = motor.CurrentPosition; // 当前位置作为目标位置
                motor.LastUpdateTime = DateTime.Now;

                // 触发状态变化事件
                MotorStatusChanged?.Invoke(this, new MotorStatusEventArgs(axisId, motor));

                return true;

            }, false, $"停止轴{axisId}");
        }

        /// <summary>
        /// 停止所有电机
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> StopAllMotorsAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info("Mock停止所有电机");

                bool allSuccess = true;
                foreach (var axisId in _motorStates.Keys)
                {
                    bool result = await StopMotorAsync(axisId);
                    if (!result)
                    {
                        allSuccess = false;
                    }
                }

                return allSuccess;

            }, false, "停止所有电机");
        }

        /// <summary>
        /// 回零操作
        /// </summary>
        /// <param name="axisId">轴号</param>
        /// <returns>是否成功</returns>
        public async Task<bool> HomeAsync(int axisId)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (!_isInitialized)
                {
                    throw new InvalidOperationException("MockMotorManager未初始化");
                }

                if (!_motorStates.ContainsKey(axisId))
                {
                    throw new ArgumentException($"轴{axisId}不存在");
                }

                var motor = _motorStates[axisId];
                var param = _motorParams[axisId];

                LogHelper.Info($"Mock轴{axisId}开始回零");

                // 模拟回零过程
                motor.IsMoving = true;
                motor.Speed = param.HomeSpeed;
                motor.TargetPosition = 0;
                motor.LastUpdateTime = DateTime.Now;

                // 触发状态变化事件
                MotorStatusChanged?.Invoke(this, new MotorStatusEventArgs(axisId, motor));

                // 模拟回零运动
                await SimulateMotorMovementAsync(axisId, 0, param.HomeSpeed);

                // 设置回零完成
                motor.IsHomed = true;
                motor.CurrentPosition = 0;
                motor.TargetPosition = 0;
                motor.IsMoving = false;
                motor.Speed = 0;
                motor.LastUpdateTime = DateTime.Now;

                // 触发状态变化事件
                MotorStatusChanged?.Invoke(this, new MotorStatusEventArgs(axisId, motor));

                LogHelper.Info($"Mock轴{axisId}回零完成");
                return true;

            }, false, $"轴{axisId}回零");
        }

        /// <summary>
        /// 获取电机状态
        /// </summary>
        /// <param name="axisId">轴号</param>
        /// <returns>电机状态</returns>
        public async Task<MotorStatus> GetMotorStatusAsync(int axisId)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (!_isInitialized)
                {
                    throw new InvalidOperationException("MockMotorManager未初始化");
                }

                if (!_motorStates.ContainsKey(axisId))
                {
                    throw new ArgumentException($"轴{axisId}不存在");
                }

                // 模拟读取延迟
                await Task.Delay(10);

                // 返回状态副本
                var motor = _motorStates[axisId];
                return new MotorStatus
                {
                    AxisId = motor.AxisId,
                    CurrentPosition = motor.CurrentPosition,
                    TargetPosition = motor.TargetPosition,
                    IsMoving = motor.IsMoving,
                    IsHomed = motor.IsHomed,
                    HasError = motor.HasError,
                    ErrorMessage = motor.ErrorMessage,
                    Speed = motor.Speed,
                    LastUpdateTime = motor.LastUpdateTime
                };

            }, null, $"获取轴{axisId}状态");
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 模拟电机运动过程
        /// </summary>
        /// <param name="axisId">轴号</param>
        /// <param name="targetPosition">目标位置</param>
        /// <param name="speed">运动速度</param>
        /// <returns></returns>
        private async Task SimulateMotorMovementAsync(int axisId, double targetPosition, double speed)
        {
            var motor = _motorStates[axisId];
            double startPosition = motor.CurrentPosition;
            double distance = Math.Abs(targetPosition - startPosition);
            
            if (distance < 0.001) // 距离太小，直接到达
            {
                motor.CurrentPosition = targetPosition;
                motor.IsMoving = false;
                motor.Speed = 0;
                motor.LastUpdateTime = DateTime.Now;
                
                MotorPositionChanged?.Invoke(this, new MotorPositionEventArgs(axisId, targetPosition));
                MotorStatusChanged?.Invoke(this, new MotorStatusEventArgs(axisId, motor));
                return;
            }

            // 计算运动时间（简化计算，忽略加减速）
            double moveTime = distance / speed * 1000; // 转换为毫秒
            int steps = Math.Max(10, (int)(moveTime / 100)); // 至少10步，每步100ms
            double stepTime = moveTime / steps;
            double stepDistance = distance / steps;
            int direction = targetPosition > startPosition ? 1 : -1;

            // 分步模拟运动
            for (int i = 1; i <= steps; i++)
            {
                if (!motor.IsMoving) // 检查是否被停止
                    break;

                // 计算当前位置
                double progress = (double)i / steps;
                motor.CurrentPosition = startPosition + direction * stepDistance * i;
                motor.LastUpdateTime = DateTime.Now;

                // 触发位置变化事件
                MotorPositionChanged?.Invoke(this, new MotorPositionEventArgs(axisId, motor.CurrentPosition));

                // 等待步进时间
                await Task.Delay((int)stepTime);
            }

            // 确保到达目标位置
            if (motor.IsMoving)
            {
                motor.CurrentPosition = targetPosition;
                motor.IsMoving = false;
                motor.Speed = 0;
                motor.LastUpdateTime = DateTime.Now;

                // 触发最终事件
                MotorPositionChanged?.Invoke(this, new MotorPositionEventArgs(axisId, targetPosition));
                MotorStatusChanged?.Invoke(this, new MotorStatusEventArgs(axisId, motor));

                LogHelper.Info($"Mock轴{axisId}到达目标位置: {targetPosition:F3}");
            }
        }
        #endregion

        #region 测试辅助方法
        /// <summary>
        /// 模拟电机错误（用于测试）
        /// </summary>
        /// <param name="axisId">轴号</param>
        /// <param name="errorMessage">错误消息</param>
        public void SimulateMotorError(int axisId, string errorMessage)
        {
            if (_motorStates.ContainsKey(axisId))
            {
                var motor = _motorStates[axisId];
                motor.HasError = true;
                motor.ErrorMessage = errorMessage;
                motor.IsMoving = false;
                motor.Speed = 0;
                motor.LastUpdateTime = DateTime.Now;

                LogHelper.Warning($"Mock轴{axisId}模拟错误: {errorMessage}");
                MotorStatusChanged?.Invoke(this, new MotorStatusEventArgs(axisId, motor));
            }
        }

        /// <summary>
        /// 清除电机错误（用于测试）
        /// </summary>
        /// <param name="axisId">轴号</param>
        public void ClearMotorError(int axisId)
        {
            if (_motorStates.ContainsKey(axisId))
            {
                var motor = _motorStates[axisId];
                motor.HasError = false;
                motor.ErrorMessage = "";
                motor.LastUpdateTime = DateTime.Now;

                LogHelper.Info($"Mock轴{axisId}清除错误");
                MotorStatusChanged?.Invoke(this, new MotorStatusEventArgs(axisId, motor));
            }
        }

        /// <summary>
        /// 获取所有电机状态（用于测试验证）
        /// </summary>
        /// <returns>所有电机状态</returns>
        public Dictionary<int, MotorStatus> GetAllMotorStates()
        {
            var result = new Dictionary<int, MotorStatus>();
            foreach (var kvp in _motorStates)
            {
                result[kvp.Key] = new MotorStatus
                {
                    AxisId = kvp.Value.AxisId,
                    CurrentPosition = kvp.Value.CurrentPosition,
                    TargetPosition = kvp.Value.TargetPosition,
                    IsMoving = kvp.Value.IsMoving,
                    IsHomed = kvp.Value.IsHomed,
                    HasError = kvp.Value.HasError,
                    ErrorMessage = kvp.Value.ErrorMessage,
                    Speed = kvp.Value.Speed,
                    LastUpdateTime = kvp.Value.LastUpdateTime
                };
            }
            return result;
        }
        #endregion
    }
}
