using System;
using System.Linq;

namespace MyHMI.Models
{
    /// <summary>
    /// 通信状态枚举
    /// </summary>
    public enum CommunicationStatus
    {
        /// <summary>
        /// 断开连接
        /// </summary>
        Disconnected,

        /// <summary>
        /// 连接中
        /// </summary>
        Connecting,

        /// <summary>
        /// 已连接
        /// </summary>
        Connected,

        /// <summary>
        /// 连接错误
        /// </summary>
        Error
    }

    /// <summary>
    /// 串口配置类
    /// </summary>
    public class SerialPortConfiguration
    {
        /// <summary>
        /// 端口名称
        /// </summary>
        public string PortName { get; set; }

        /// <summary>
        /// 波特率
        /// </summary>
        public int BaudRate { get; set; }

        /// <summary>
        /// 数据位
        /// </summary>
        public int DataBits { get; set; }

        /// <summary>
        /// 停止位
        /// </summary>
        public System.IO.Ports.StopBits StopBits { get; set; }

        /// <summary>
        /// 校验位
        /// </summary>
        public System.IO.Ports.Parity Parity { get; set; }

        /// <summary>
        /// 读取超时（毫秒）
        /// </summary>
        public int ReadTimeout { get; set; }

        /// <summary>
        /// 写入超时（毫秒）
        /// </summary>
        public int WriteTimeout { get; set; }

        /// <summary>
        /// 构造函数，设置默认值
        /// </summary>
        public SerialPortConfiguration()
        {
            PortName = "COM1";
            BaudRate = 9600;
            DataBits = 8;
            StopBits = System.IO.Ports.StopBits.One;
            Parity = System.IO.Ports.Parity.None;
            ReadTimeout = 1000;
            WriteTimeout = 1000;
        }
    }

    /// <summary>
    /// TCP/IP连接配置类
    /// </summary>
    public class TcpConfiguration
    {
        /// <summary>
        /// IP地址
        /// </summary>
        public string IPAddress { get; set; }

        /// <summary>
        /// 端口号
        /// </summary>
        public int Port { get; set; }

        /// <summary>
        /// 连接超时（毫秒）
        /// </summary>
        public int ConnectTimeout { get; set; }

        /// <summary>
        /// 接收超时（毫秒）
        /// </summary>
        public int ReceiveTimeout { get; set; }

        /// <summary>
        /// 发送超时（毫秒）
        /// </summary>
        public int SendTimeout { get; set; }

        /// <summary>
        /// 心跳间隔（毫秒）
        /// </summary>
        public int HeartbeatInterval { get; set; }

        /// <summary>
        /// 自动重连
        /// </summary>
        public bool AutoReconnect { get; set; }

        /// <summary>
        /// 重连间隔（毫秒）
        /// </summary>
        public int ReconnectInterval { get; set; }

        /// <summary>
        /// 最大重连次数
        /// </summary>
        public int MaxReconnectAttempts { get; set; }

        /// <summary>
        /// 构造函数，设置默认值
        /// </summary>
        public TcpConfiguration()
        {
            IPAddress = "127.0.0.1";
            Port = 8080;
            ConnectTimeout = 5000;
            ReceiveTimeout = 3000;
            SendTimeout = 3000;
            HeartbeatInterval = 10000;
            AutoReconnect = true;
            ReconnectInterval = 5000;
            MaxReconnectAttempts = 10;
        }
    }

    /// <summary>
    /// Modbus配置类
    /// </summary>
    public class ModbusConfiguration
    {
        /// <summary>
        /// 从站ID
        /// </summary>
        public byte SlaveId { get; set; }

        /// <summary>
        /// TCP配置
        /// </summary>
        public TcpConfiguration TcpConfig { get; set; }

        /// <summary>
        /// 读取超时（毫秒）
        /// </summary>
        public int ReadTimeout { get; set; }

        /// <summary>
        /// 写入超时（毫秒）
        /// </summary>
        public int WriteTimeout { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ModbusConfiguration()
        {
            SlaveId = 1;
            TcpConfig = new TcpConfiguration { Port = 502 };
            ReadTimeout = 1000;
            WriteTimeout = 1000;
        }
    }

    /// <summary>
    /// 机器人指令类
    /// </summary>
    public class RobotCommand
    {
        /// <summary>
        /// 指令ID
        /// </summary>
        public string CommandId { get; set; }

        /// <summary>
        /// 指令类型
        /// </summary>
        public RobotCommandType CommandType { get; set; }

        /// <summary>
        /// 目标坐标
        /// </summary>
        public double[] Coordinates { get; set; }

        /// <summary>
        /// 运动速度
        /// </summary>
        public double Speed { get; set; }

        /// <summary>
        /// 指令参数
        /// </summary>
        public string Parameters { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 超时时间（毫秒）
        /// </summary>
        public int TimeoutMs { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public RobotCommand()
        {
            CommandId = Guid.NewGuid().ToString();
            Coordinates = new double[6];
            CreateTime = DateTime.Now;
            TimeoutMs = 30000; // 默认30秒超时
            Parameters = string.Empty;
        }
    }

    /// <summary>
    /// 机器人指令类型枚举
    /// </summary>
    public enum RobotCommandType
    {
        /// <summary>
        /// 移动到指定位置
        /// </summary>
        MoveTo,

        /// <summary>
        /// 抓取
        /// </summary>
        Grip,

        /// <summary>
        /// 释放
        /// </summary>
        Release,

        /// <summary>
        /// 回到原点
        /// </summary>
        Home,

        /// <summary>
        /// 停止
        /// </summary>
        Stop,

        /// <summary>
        /// 复位
        /// </summary>
        Reset,

        /// <summary>
        /// 获取状态
        /// </summary>
        GetStatus
    }

    /// <summary>
    /// 机器人响应类
    /// </summary>
    public class RobotResponse
    {
        /// <summary>
        /// 对应的指令ID
        /// </summary>
        public string CommandId { get; set; }

        /// <summary>
        /// 响应是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 响应数据
        /// </summary>
        public string Data { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 响应时间
        /// </summary>
        public DateTime ResponseTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public RobotResponse()
        {
            Data = string.Empty;
            ErrorMessage = string.Empty;
            ResponseTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 扫描枪数据类
    /// </summary>
    public class ScannerData
    {
        /// <summary>
        /// 扫描到的条码数据
        /// </summary>
        public string BarcodeData { get; set; }

        /// <summary>
        /// 扫描时间
        /// </summary>
        public DateTime ScanTime { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; }

        /// <summary>
        /// 数据长度
        /// </summary>
        public int DataLength => string.IsNullOrEmpty(BarcodeData) ? 0 : BarcodeData.Length;

        /// <summary>
        /// 数据（兼容性属性）
        /// </summary>
        public string Data => BarcodeData;

        /// <summary>
        /// 构造函数
        /// </summary>
        public ScannerData()
        {
            BarcodeData = string.Empty;
            ScanTime = DateTime.Now;
            DataType = "Unknown";
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="data">条码数据</param>
        public ScannerData(string data)
        {
            BarcodeData = data ?? string.Empty;
            ScanTime = DateTime.Now;
            DataType = DetermineDataType(BarcodeData);
        }

        /// <summary>
        /// 判断数据类型
        /// </summary>
        /// <param name="data">数据</param>
        /// <returns>数据类型</returns>
        private string DetermineDataType(string data)
        {
            if (string.IsNullOrEmpty(data))
                return "Empty";

            // 简单的数据类型判断逻辑
            if (data.All(char.IsDigit))
                return "Numeric";
            else if (data.All(c => char.IsLetterOrDigit(c)))
                return "Alphanumeric";
            else
                return "Mixed";
        }
    }

    /// <summary>
    /// 工作流状态枚举
    /// </summary>
    public enum WorkflowState
    {
        /// <summary>
        /// 空闲状态
        /// </summary>
        Idle,

        /// <summary>
        /// 等待扫描
        /// </summary>
        WaitingForScan,

        /// <summary>
        /// 视觉检测中
        /// </summary>
        VisionDetecting,

        /// <summary>
        /// 电机运动中
        /// </summary>
        MotorMoving,

        /// <summary>
        /// 机器人操作中
        /// </summary>
        RobotOperating,

        /// <summary>
        /// 暂停状态
        /// </summary>
        Paused,

        /// <summary>
        /// 错误状态
        /// </summary>
        Error
    }

    /// <summary>
    /// 扫描枪端口配置类
    /// </summary>
    public class ScannerPortConfig
    {
        /// <summary>
        /// 端口名称
        /// </summary>
        public string PortName { get; set; } = "";

        /// <summary>
        /// 波特率
        /// </summary>
        public int BaudRate { get; set; } = 115200;

        /// <summary>
        /// 数据位
        /// </summary>
        public int DataBits { get; set; } = 8;

        /// <summary>
        /// 停止位
        /// </summary>
        public string StopBits { get; set; } = "One";

        /// <summary>
        /// 奇偶校验
        /// </summary>
        public string Parity { get; set; } = "None";

        /// <summary>
        /// 读取超时时间
        /// </summary>
        public int ReadTimeout { get; set; } = 1000;

        /// <summary>
        /// 写入超时时间
        /// </summary>
        public int WriteTimeout { get; set; } = 1000;
    }
}
