using System;
using System.Linq;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using System.Threading;
using System.Collections.Concurrent;

using MyHMI.Events;
using MyHMI.Models;
using MyHMI.Helpers;
using MyHMI.Settings;

namespace MyHMI.Managers
{
    /// <summary>
    /// Epson机器人2 TCP/IP通信管理器
    /// 负责与Epson机器人2的双TCP/IP连接通信，基于标准TCP/IP协议栈
    /// </summary>
    public class EpsonRobotManager2
    {
        #region 单例模式
        private static readonly Lazy<EpsonRobotManager2> _instance = new Lazy<EpsonRobotManager2>(() => new EpsonRobotManager2());
        public static EpsonRobotManager2 Instance => _instance.Value;
        private EpsonRobotManager2() { }
        #endregion

        #region 事件定义
        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        public event EventHandler<EpsonRobotConnectionStatusChangedEventArgs> ConnectionStatusChanged;

        /// <summary>
        /// 响应接收事件
        /// </summary>
        public event EventHandler<EpsonRobotResponseReceivedEventArgs> ResponseReceived;

        /// <summary>
        /// 命令发送事件
        /// </summary>
        public event EventHandler<EpsonRobotCommandSentEventArgs> CommandSent;

        /// <summary>
        /// 机器人状态变化事件
        /// </summary>
        public event EventHandler<EpsonRobotStatusChangedEventArgs> StatusChanged;

        /// <summary>
        /// 机器人错误事件
        /// </summary>
        public event EventHandler<EpsonRobotErrorEventArgs> RobotError;

        /// <summary>
        /// 特殊命令接收事件
        /// </summary>
        public event EventHandler<SpecialCommandReceivedEventArgs> SpecialCommandReceived;

        /// <summary>
        /// 机器人启动成功事件
        /// </summary>
        public event EventHandler<EpsonRobotStartedEventArgs> RobotStarted;

        /// <summary>
        /// 自动化流程状态变化事件
        /// </summary>
        public event EventHandler<AutomationStatusChangedEventArgs> AutomationStatusChanged;
        #endregion

        #region 私有字段
        // 启动/停止TCP/IP连接
        private TcpClient _startStopTcpClient;
        private NetworkStream _startStopStream;
        private bool _startStopConnected = false;
        private Task _startStopListenTask;
        private CancellationTokenSource _startStopCancellationTokenSource;

        // 数据收发TCP/IP连接
        private TcpClient _dataTcpClient;
        private NetworkStream _dataStream;
        private bool _dataConnected = false;
        private Task _dataListenTask;
        private CancellationTokenSource _dataCancellationTokenSource;

        // 配置和状态
        private EpsonRobotConfiguration _config;
        private EpsonRobotStatus _currentStatus;
        private bool _isInitialized = false;
        private bool _isLoggedIn = false;
        private bool _isStarted = false;

        // 自动化流程
        private bool _isScanning = false;
        private bool _isWaitingForManualInput = false;
        private SpecialCommandType? _currentSpecialCommand = null;
        private Timer _scanTimer;

        // 线程安全
        private readonly object _lockObject = new object();
        private readonly ConcurrentDictionary<string, TaskCompletionSource<EpsonRobotResponse>> _pendingCommands 
            = new ConcurrentDictionary<string, TaskCompletionSource<EpsonRobotResponse>>();
        #endregion

        #region 公共属性
        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// 启动/停止TCP/IP连接是否已连接
        /// </summary>
        public bool IsStartStopConnected => _startStopConnected;

        /// <summary>
        /// 数据收发TCP/IP连接是否已连接
        /// </summary>
        public bool IsDataConnected => _dataConnected;

        /// <summary>
        /// 是否已登录
        /// </summary>
        public bool IsLoggedIn => _isLoggedIn;

        /// <summary>
        /// 是否已启动
        /// </summary>
        public bool IsStarted => _isStarted;

        /// <summary>
        /// 当前机器人状态
        /// </summary>
        public EpsonRobotStatus CurrentStatus => _currentStatus;

        /// <summary>
        /// 配置信息
        /// </summary>
        public EpsonRobotConfiguration Configuration => _config;

        /// <summary>
        /// 是否正在扫描
        /// </summary>
        public bool IsScanning => _isScanning;

        /// <summary>
        /// 是否等待手动输入
        /// </summary>
        public bool IsWaitingForManualInput => _isWaitingForManualInput;

        /// <summary>
        /// 当前特殊命令
        /// </summary>
        public SpecialCommandType? CurrentSpecialCommand => _currentSpecialCommand;
        #endregion

        #region 初始化和释放
        /// <summary>
        /// 异步初始化Epson机器人管理器
        /// </summary>
        /// <param name="config">配置信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> InitializeAsync(EpsonRobotConfiguration config = null)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                await Task.CompletedTask; // 消除CS1998警告
                // 如果已经初始化，但传入了新配置，则更新配置
                if (_isInitialized && config != null)
                {
                    LogHelper.Info("EpsonRobotManager2已初始化，更新配置...");
                    _config = config;
                    LogHelper.Info($"配置已更新 - IP地址: {_config.IPAddress}, " +
                                  $"控制端口: {_config.ControlPort}, 数据端口: {_config.DataPort}");
                    return true;
                }
                else if (_isInitialized)
                {
                    LogHelper.Warning("EpsonRobotManager2已经初始化，跳过重复初始化");
                    return true;
                }

                LogHelper.Info("开始初始化EpsonRobotManager2...");

                // 加载配置
                _config = config ?? LoadConfigurationFromSystem();
                _currentStatus = new EpsonRobotStatus();

                LogHelper.Info($"Epson机器人配置 - IP地址: {_config.IPAddress}, " +
                              $"控制端口: {_config.ControlPort}, 数据端口: {_config.DataPort}");

                _isInitialized = true;
                LogHelper.Info("EpsonRobotManager2初始化完成");
                return true;

            }, false, "EpsonRobotManager2初始化");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public async Task DisposeAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info("开始释放EpsonRobotManager2资源...");

                // 停止自动化流程
                await StopAutomationAsync();

                // 断开连接
                await DisconnectAllAsync();

                _isInitialized = false;
                LogHelper.Info("EpsonRobotManager2资源释放完成");

                return true;
            }, false, "EpsonRobotManager2资源释放");
        }
        #endregion

        #region 连接管理
        /// <summary>
        /// 连接启动/停止TCP/IP
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> ConnectStartStopAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_startStopConnected)
                {
                    LogHelper.Info("启动/停止TCP/IP已经连接");
                    return true;
                }

                UpdateConnectionStatus("StartStop", CommunicationStatus.Connecting, "正在连接启动/停止TCP/IP...");

                lock (_lockObject)
                {
                    _startStopTcpClient = new TcpClient();
                }

                // 添加详细的连接日志
                LogHelper.Info($"开始TCP连接到 {_config.IPAddress}:{_config.ControlPort}");

                // 设置连接超时
                var connectTask = _startStopTcpClient.ConnectAsync(_config.IPAddress, _config.ControlPort);
                var timeoutTask = Task.Delay(_config.ConnectTimeout);

                var completedTask = await Task.WhenAny(connectTask, timeoutTask);

                if (completedTask == timeoutTask)
                {
                    LogHelper.Error($"TCP连接超时到 {_config.IPAddress}:{_config.ControlPort}，超时时间: {_config.ConnectTimeout}ms");
                    throw new TimeoutException($"控制端口连接超时: {_config.IPAddress}:{_config.ControlPort}");
                }

                if (connectTask.IsFaulted)
                {
                    LogHelper.Error($"TCP连接失败到 {_config.IPAddress}:{_config.ControlPort}: {connectTask.Exception?.InnerException?.Message}");
                    throw connectTask.Exception?.InnerException ?? new Exception("启动/停止TCP/IP连接失败");
                }

                LogHelper.Info($"TCP连接成功到 {_config.IPAddress}:{_config.ControlPort}");

                lock (_lockObject)
                {
                    _startStopStream = _startStopTcpClient.GetStream();
                    _startStopStream.ReadTimeout = _config.ReceiveTimeout;
                    _startStopStream.WriteTimeout = _config.SendTimeout;
                }

                // 启动监听任务
                _startStopCancellationTokenSource = new CancellationTokenSource();
                _startStopListenTask = Task.Run(async () => await StartStopListenLoopAsync(_startStopCancellationTokenSource.Token));

                _startStopConnected = true;
                UpdateConnectionStatus("StartStop", CommunicationStatus.Connected, "启动/停止TCP/IP连接成功");
                LogHelper.Info($"控制端口连接成功: {_config.IPAddress}:{_config.ControlPort}");
                return true;

            }, false, "连接启动/停止TCP/IP");
        }

        /// <summary>
        /// 连接数据收发TCP/IP
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> ConnectDataAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_dataConnected)
                {
                    LogHelper.Info("数据收发TCP/IP已经连接");
                    return true;
                }

                UpdateConnectionStatus("Data", CommunicationStatus.Connecting, "正在连接数据收发TCP/IP...");

                lock (_lockObject)
                {
                    _dataTcpClient = new TcpClient();
                }

                // 设置连接超时
                var connectTask = _dataTcpClient.ConnectAsync(_config.IPAddress, _config.DataPort);
                var timeoutTask = Task.Delay(_config.ConnectTimeout);

                var completedTask = await Task.WhenAny(connectTask, timeoutTask);
                
                if (completedTask == timeoutTask)
                {
                    throw new TimeoutException($"数据端口连接超时: {_config.IPAddress}:{_config.DataPort}");
                }

                if (connectTask.IsFaulted)
                {
                    throw connectTask.Exception?.InnerException ?? new Exception("数据收发TCP/IP连接失败");
                }

                lock (_lockObject)
                {
                    _dataStream = _dataTcpClient.GetStream();
                    _dataStream.ReadTimeout = _config.ReceiveTimeout;
                    _dataStream.WriteTimeout = _config.SendTimeout;
                }

                // 启动监听任务
                _dataCancellationTokenSource = new CancellationTokenSource();
                _dataListenTask = Task.Run(async () => await DataListenLoopAsync(_dataCancellationTokenSource.Token));

                _dataConnected = true;
                UpdateConnectionStatus("Data", CommunicationStatus.Connected, "数据收发TCP/IP连接成功");
                LogHelper.Info($"数据端口连接成功: {_config.IPAddress}:{_config.DataPort}");
                return true;

            }, false, "连接数据收发TCP/IP");
        }

        /// <summary>
        /// 断开所有连接
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> DisconnectAllAsync()
        {
            var startStopResult = await DisconnectStartStopAsync();
            var dataResult = await DisconnectDataAsync();
            return startStopResult && dataResult;
        }

        /// <summary>
        /// 断开启动/停止TCP/IP连接
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> DisconnectStartStopAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (!_startStopConnected)
                    return true;

                // 停止监听
                _startStopCancellationTokenSource?.Cancel();
                if (_startStopListenTask != null)
                {
                    await _startStopListenTask;
                }

                await Task.Run(() =>
                {
                    lock (_lockObject)
                    {
                        _startStopStream?.Close();
                        _startStopStream?.Dispose();
                        _startStopStream = null;

                        _startStopTcpClient?.Close();
                        _startStopTcpClient?.Dispose();
                        _startStopTcpClient = null;
                    }
                });

                _startStopConnected = false;
                _isLoggedIn = false;
                UpdateConnectionStatus("StartStop", CommunicationStatus.Disconnected, "启动/停止TCP/IP连接已断开");
                LogHelper.Info("启动/停止TCP/IP连接已断开");
                return true;

            }, false, "断开启动/停止TCP/IP连接");
        }

        /// <summary>
        /// 断开数据收发TCP/IP连接
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> DisconnectDataAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (!_dataConnected)
                    return true;

                // 停止监听
                _dataCancellationTokenSource?.Cancel();
                if (_dataListenTask != null)
                {
                    await _dataListenTask;
                }

                await Task.Run(() =>
                {
                    lock (_lockObject)
                    {
                        _dataStream?.Close();
                        _dataStream?.Dispose();
                        _dataStream = null;

                        _dataTcpClient?.Close();
                        _dataTcpClient?.Dispose();
                        _dataTcpClient = null;
                    }
                });

                _dataConnected = false;
                UpdateConnectionStatus("Data", CommunicationStatus.Disconnected, "数据收发TCP/IP连接已断开");
                LogHelper.Info("数据收发TCP/IP连接已断开");
                return true;

            }, false, "断开数据收发TCP/IP连接");
        }
        #endregion

        #region 命令操作
        /// <summary>
        /// 登录机器人
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> LoginAsync()
        {
            if (!_startStopConnected)
            {
                LogHelper.Error("启动/停止TCP/IP未连接，无法登录");
                return false;
            }

            // 使用异步命令发送，但增加超时和重试机制
            var command = new EpsonRobotCommand(EpsonRobotCommandType.Login, _config.Password)
            {
                TimeoutMs = 10000 // 增加登录超时到10秒
            };

            LogHelper.Info($"发送登录命令: $Login,{_config.Password}");
            var response = await SendCommandAsync(command, "StartStop");

            if (response != null && response.IsSuccess)
            {
                _isLoggedIn = true;
                LogHelper.Info("Epson机器人登录成功");
                return true;
            }
            else
            {
                LogHelper.Error($"Epson机器人登录失败: {response?.ErrorMessage}");
                RobotError?.Invoke(this, new EpsonRobotErrorEventArgs(
                    EpsonRobotErrorType.LoginFailed, response?.ErrorMessage ?? "登录失败", "StartStop"));
                return false;
            }
        }

        /// <summary>
        /// 退出登录机器人
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> LogoutAsync()
        {
            if (!_startStopConnected)
            {
                LogHelper.Warning("启动/停止TCP/IP未连接，无需退出登录");
                return true;
            }

            if (!_isLoggedIn)
            {
                LogHelper.Warning("未登录状态，无需退出登录");
                return true;
            }

            // 根据EPSON RC+文档，发送Logout命令
            var command = new EpsonRobotCommand(EpsonRobotCommandType.Logout)
            {
                TimeoutMs = 5000 // 退出登录超时5秒
            };

            LogHelper.Info("发送退出登录命令: $Logout");
            var response = await SendCommandAsync(command, "StartStop");

            if (response != null && response.IsSuccess)
            {
                _isLoggedIn = false;
                LogHelper.Info("Epson机器人退出登录成功");
                return true;
            }
            else
            {
                LogHelper.Error($"Epson机器人退出登录失败: {response?.ErrorMessage}");
                // 即使退出登录失败，也设置为未登录状态
                _isLoggedIn = false;
                return false;
            }
        }

        /// <summary>
        /// 启动机器人
        /// </summary>
        /// <param name="functionNumber">函数编号</param>
        /// <returns>是否成功</returns>
        public async Task<bool> StartRobotAsync(int functionNumber = 0)
        {
            if (!_isLoggedIn)
            {
                LogHelper.Error("未登录，无法启动机器人");
                return false;
            }

            // 根据EPSON RC+文档要求，执行Start命令前必须等待Auto为ON
            LogHelper.Info("检查机器人状态，等待Auto为ON...");

            // 最多等待30秒
            int maxRetries = 30;
            int retryCount = 0;

            while (retryCount < maxRetries)
            {
                var status = await GetStatusAsync();
                if (status != null && status.IsAutoMode)
                {
                    LogHelper.Info("机器人Auto状态已就绪，可以执行Start命令");
                    break;
                }

                LogHelper.Info($"等待机器人Auto状态就绪... ({retryCount + 1}/{maxRetries})");
                await Task.Delay(1000); // 等待1秒
                retryCount++;
            }

            if (retryCount >= maxRetries)
            {
                string errorMsg = "机器人Auto状态未就绪，无法启动机器人。请检查机器人控制器状态。";
                LogHelper.Error(errorMsg);
                RobotStarted?.Invoke(this, new EpsonRobotStartedEventArgs(false, errorMsg));
                return false;
            }

            // 检查其他必要条件：Ready开, Error关, EStop关, Safeguard开
            var currentStatus = await GetStatusAsync();
            if (currentStatus != null)
            {
                if (!currentStatus.IsReady)
                {
                    string errorMsg = "机器人Ready状态未就绪，无法启动";
                    LogHelper.Error(errorMsg);
                    RobotStarted?.Invoke(this, new EpsonRobotStartedEventArgs(false, errorMsg));
                    return false;
                }

                if (currentStatus.HasError)
                {
                    string errorMsg = "机器人存在错误状态，请先执行Reset命令清除错误";
                    LogHelper.Error(errorMsg);
                    RobotStarted?.Invoke(this, new EpsonRobotStartedEventArgs(false, errorMsg));
                    return false;
                }

                if (currentStatus.IsEmergencyStop)
                {
                    string errorMsg = "机器人处于紧急停止状态，无法启动";
                    LogHelper.Error(errorMsg);
                    RobotStarted?.Invoke(this, new EpsonRobotStartedEventArgs(false, errorMsg));
                    return false;
                }

                if (currentStatus.IsSafeguardOpen)
                {
                    string errorMsg = "机器人安全门打开，无法启动";
                    LogHelper.Error(errorMsg);
                    RobotStarted?.Invoke(this, new EpsonRobotStartedEventArgs(false, errorMsg));
                    return false;
                }
            }

            var command = new EpsonRobotCommand(EpsonRobotCommandType.Start, functionNumber.ToString());
            var response = await SendCommandAsync(command, "StartStop");

            if (response != null && response.IsSuccess)
            {
                _isStarted = true;
                LogHelper.Info("Epson机器人启动成功");
                RobotStarted?.Invoke(this, new EpsonRobotStartedEventArgs(true, "机器人启动成功"));
                return true;
            }
            else
            {
                LogHelper.Error($"Epson机器人启动失败: {response?.ErrorMessage}");
                RobotStarted?.Invoke(this, new EpsonRobotStartedEventArgs(false, response?.ErrorMessage ?? "启动失败"));
                return false;
            }
        }

        /// <summary>
        /// 停止机器人
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> StopRobotAsync()
        {
            if (!_isLoggedIn)
            {
                LogHelper.Error("未登录，无法停止机器人");
                return false;
            }

            var command = new EpsonRobotCommand(EpsonRobotCommandType.Stop);
            var response = await SendCommandAsync(command, "StartStop");

            if (response != null && response.IsSuccess)
            {
                _isStarted = false;
                LogHelper.Info("Epson机器人停止成功");
                return true;
            }
            else
            {
                LogHelper.Error($"Epson机器人停止失败: {response?.ErrorMessage}");
                return false;
            }
        }

        /// <summary>
        /// 获取机器人状态
        /// </summary>
        /// <returns>机器人状态</returns>
        public async Task<EpsonRobotStatus> GetStatusAsync()
        {
            if (!_isLoggedIn)
            {
                LogHelper.Warning("未登录，无法获取机器人状态");
                return _currentStatus;
            }

            var command = new EpsonRobotCommand(EpsonRobotCommandType.GetStatus);
            var response = await SendCommandAsync(command, "StartStop");

            if (response != null && response.IsSuccess)
            {
                // 解析状态响应
                // 格式: #GetStatus,[状态],[错误/警告代码]<CR><LF>
                var parts = response.Data.Split(',');
                if (parts.Length >= 2)
                {
                    var previousStatus = new EpsonRobotStatus
                    {
                        IsTestMode = _currentStatus.IsTestMode,
                        IsTeachMode = _currentStatus.IsTeachMode,
                        IsAutoMode = _currentStatus.IsAutoMode,
                        HasWarning = _currentStatus.HasWarning,
                        HasSevereError = _currentStatus.HasSevereError,
                        IsSafeguardOpen = _currentStatus.IsSafeguardOpen,
                        IsEmergencyStop = _currentStatus.IsEmergencyStop,
                        HasError = _currentStatus.HasError,
                        IsPaused = _currentStatus.IsPaused,
                        IsRunning = _currentStatus.IsRunning,
                        IsReady = _currentStatus.IsReady,
                        ErrorWarningCode = _currentStatus.ErrorWarningCode
                    };

                    _currentStatus.ParseFromStatusString(parts[0], parts.Length > 1 ? parts[1] : "0000");

                    // 触发状态变化事件
                    StatusChanged?.Invoke(this, new EpsonRobotStatusChangedEventArgs(_currentStatus, previousStatus));
                }
            }

            return _currentStatus;
        }

        /// <summary>
        /// 发送自定义命令
        /// </summary>
        /// <param name="commandString">命令字符串</param>
        /// <param name="connectionType">连接类型</param>
        /// <returns>响应</returns>
        public async Task<EpsonRobotResponse> SendCustomCommandAsync(string commandString, string connectionType = "Data")
        {
            if (string.IsNullOrEmpty(commandString))
                return null;

            var command = new EpsonRobotCommand(EpsonRobotCommandType.Custom, "")
            {
                RawCommand = commandString.StartsWith("$") ? commandString : $"${commandString}"
            };

            return await SendCommandAsync(command, connectionType);
        }

        /// <summary>
        /// 发送命令
        /// </summary>
        /// <param name="command">命令</param>
        /// <param name="connectionType">连接类型</param>
        /// <returns>响应</returns>
        public async Task<EpsonRobotResponse> SendCommandAsync(EpsonRobotCommand command, string connectionType = "StartStop")
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (command == null)
                    throw new ArgumentNullException(nameof(command));

                bool isConnected = connectionType == "StartStop" ? _startStopConnected : _dataConnected;
                if (!isConnected)
                {
                    throw new InvalidOperationException($"{connectionType}TCP/IP未连接");
                }

                LogHelper.Info($"发送Epson机器人命令: {command.GetFullCommand().Trim()} ({connectionType})");

                // 发送命令
                await SendDataAsync(command.GetFullCommand(), connectionType);

                // 触发命令发送事件
                CommandSent?.Invoke(this, new EpsonRobotCommandSentEventArgs(command, connectionType));

                // 等待响应
                if (command.RequiresResponse)
                {
                    var tcs = new TaskCompletionSource<EpsonRobotResponse>();
                    _pendingCommands[command.CommandId] = tcs;

                    try
                    {
                        var timeoutTask = Task.Delay(command.TimeoutMs);
                        var responseTask = tcs.Task;

                        var completedTask = await Task.WhenAny(responseTask, timeoutTask);

                        if (completedTask == timeoutTask)
                        {
                            _pendingCommands.TryRemove(command.CommandId, out _);
                            throw new TimeoutException($"命令响应超时: {command.GetFullCommand().Trim()}");
                        }

                        return await responseTask;
                    }
                    finally
                    {
                        _pendingCommands.TryRemove(command.CommandId, out _);
                    }
                }

                return new EpsonRobotResponse { IsSuccess = true, CommandId = command.CommandId };

            }, new EpsonRobotResponse
            {
                CommandId = command?.CommandId ?? "",
                IsSuccess = false,
                ErrorMessage = "发送命令失败"
            }, $"发送Epson机器人命令: {command?.CommandType}");
        }
        #endregion

        #region 自动化流程
        /// <summary>
        /// 启动自动化流程
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> StartAutomationAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                await Task.CompletedTask; // 消除CS1998警告
                if (_isScanning)
                {
                    LogHelper.Warning("自动化流程已经在运行");
                    return true;
                }

                // 确保数据连接已建立
                if (!_dataConnected)
                {
                    LogHelper.Error("数据收发TCP/IP未连接，无法启动自动化流程");
                    return false;
                }

                _isScanning = true;
                _isWaitingForManualInput = false;
                _currentSpecialCommand = null;

                // 启动扫描定时器
                _scanTimer = new Timer(ScanCallback, null, 0, _config.ScanInterval);

                UpdateAutomationStatus(true, false, null, "自动化流程已启动，开始扫描数据收发IP");
                LogHelper.Info("Epson机器人自动化流程已启动");
                return true;

            }, false, "启动自动化流程");
        }

        /// <summary>
        /// 停止自动化流程
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> StopAutomationAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                await Task.CompletedTask; // 消除CS1998警告
                if (!_isScanning)
                    return true;

                _scanTimer?.Dispose();
                _scanTimer = null;

                _isScanning = false;
                _isWaitingForManualInput = false;
                _currentSpecialCommand = null;

                UpdateAutomationStatus(false, false, null, "自动化流程已停止");
                LogHelper.Info("Epson机器人自动化流程已停止");
                return true;

            }, false, "停止自动化流程");
        }

        /// <summary>
        /// 继续扫描（在手动输入完成后调用）
        /// </summary>
        public async Task ContinueScanningAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                await Task.CompletedTask; // 消除CS1998警告
                if (!_isScanning)
                    return true;

                _isWaitingForManualInput = false;
                _currentSpecialCommand = null;

                UpdateAutomationStatus(true, false, null, "继续扫描数据收发IP");
                LogHelper.Info("继续扫描数据收发IP");
                return true;

            }, false, "继续扫描");
        }

        /// <summary>
        /// 扫描回调
        /// </summary>
        /// <param name="state">状态</param>
        private void ScanCallback(object state)
        {
            if (!_isScanning || _isWaitingForManualInput || !_dataConnected)
                return;

            // 这里实际上是通过监听数据连接来检测特殊命令
            // 具体的检测逻辑在DataListenLoopAsync中实现
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 从系统配置加载机器人配置
        /// </summary>
        /// <returns>机器人配置</returns>
        private EpsonRobotConfiguration LoadConfigurationFromSystem()
        {
            try
            {
                // 从新的Settings系统加载配置
                var communicationSettings = Settings.Settings.Current.Communication;

                var config = new EpsonRobotConfiguration
                {
                    IPAddress = communicationSettings.EpsonRobot2IP,
                    ControlPort = communicationSettings.EpsonRobot2ControlPort,
                    DataPort = communicationSettings.EpsonRobot2DataPort,
                    Password = communicationSettings.EpsonRobot2Password,
                    ConnectTimeout = communicationSettings.EpsonRobot2ConnectTimeout,
                    ReceiveTimeout = communicationSettings.EpsonRobot2ReceiveTimeout,
                    SendTimeout = communicationSettings.EpsonRobot2SendTimeout,
                    ScanInterval = communicationSettings.EpsonRobot2ScanInterval,
                    AutoReconnect = communicationSettings.EpsonRobot2AutoReconnect,
                    ReconnectInterval = communicationSettings.EpsonRobot2ReconnectInterval,
                    MaxReconnectAttempts = communicationSettings.EpsonRobot2MaxReconnectAttempts
                };

                LogHelper.Info("从Settings系统加载Epson机器人2配置成功");
                return config;
            }
            catch (Exception ex)
            {
                LogHelper.Error("从Settings系统加载Epson机器人2配置失败", ex);
                return LoadDefaultConfiguration();
            }
        }

        /// <summary>
        /// 保存机器人配置到系统配置
        /// </summary>
        /// <param name="config">机器人配置</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SaveConfigurationAsync(EpsonRobotConfiguration config)
        {
            return await Task.Run(() =>
            {
                try
                {
                    // 保存配置到新的Settings系统
                    var communicationSettings = Settings.Settings.Current.Communication;

                    communicationSettings.EpsonRobot2IP = config.IPAddress;
                    communicationSettings.EpsonRobot2ControlPort = config.ControlPort;
                    communicationSettings.EpsonRobot2DataPort = config.DataPort;
                    communicationSettings.EpsonRobot2Password = config.Password;
                    communicationSettings.EpsonRobot2ConnectTimeout = config.ConnectTimeout;
                    communicationSettings.EpsonRobot2ReceiveTimeout = config.ReceiveTimeout;
                    communicationSettings.EpsonRobot2SendTimeout = config.SendTimeout;
                    communicationSettings.EpsonRobot2ScanInterval = config.ScanInterval;
                    communicationSettings.EpsonRobot2AutoReconnect = config.AutoReconnect;
                    communicationSettings.EpsonRobot2ReconnectInterval = config.ReconnectInterval;
                    communicationSettings.EpsonRobot2MaxReconnectAttempts = config.MaxReconnectAttempts;

                    _config = config;

                    // 保存到Settings系统
                    bool saveSuccess = Settings.Settings.Save();
                    if (saveSuccess)
                    {
                        LogHelper.Info($"Epson机器人2配置保存到Settings系统成功: IP={config.IPAddress}, 控制端口={config.ControlPort}, 数据端口={config.DataPort}");
                    }
                    else
                    {
                        LogHelper.Warning($"Epson机器人2配置保存到Settings系统失败: IP={config.IPAddress}, 控制端口={config.ControlPort}, 数据端口={config.DataPort}");
                    }
                    return saveSuccess;
                }
                catch (Exception ex)
                {
                    LogHelper.Error("保存Epson机器人2配置到Settings系统失败", ex);
                    return false;
                }
            });
        }

        /// <summary>
        /// 从EpsonRobotSettings转换为EpsonRobotConfiguration
        /// </summary>
        /// <param name="settings">设置对象</param>
        /// <returns>配置对象</returns>
        private EpsonRobotConfiguration ConvertFromSettings(CommunicationSettings settings)
        {
            return new EpsonRobotConfiguration
            {
                IPAddress = settings.EpsonRobot2IP,
                ControlPort = settings.EpsonRobot2ControlPort,
                DataPort = settings.EpsonRobot2DataPort,
                Password = settings.EpsonRobot2Password,
                ConnectTimeout = settings.EpsonRobot2ConnectTimeout,
                ReceiveTimeout = settings.EpsonRobot2ReceiveTimeout,
                SendTimeout = settings.EpsonRobot2SendTimeout,
                ScanInterval = settings.EpsonRobot2ScanInterval,
                AutoReconnect = settings.EpsonRobot2AutoReconnect,
                ReconnectInterval = settings.EpsonRobot2ReconnectInterval,
                MaxReconnectAttempts = settings.EpsonRobot2MaxReconnectAttempts
            };
        }

        /// <summary>
        /// 从EpsonRobotConfiguration转换为EpsonRobotSettings
        /// </summary>
        /// <param name="config">配置对象</param>
        /// <returns>设置对象</returns>
        private CommunicationSettings ConvertToSettings(EpsonRobotConfiguration config)
        {
            return new CommunicationSettings
            {
                EpsonRobot2IP = config.IPAddress,
                EpsonRobot2ControlPort = config.ControlPort,
                EpsonRobot2DataPort = config.DataPort,
                EpsonRobot2Password = config.Password,
                EpsonRobot2ConnectTimeout = config.ConnectTimeout,
                EpsonRobot2ReceiveTimeout = config.ReceiveTimeout,
                EpsonRobot2SendTimeout = config.SendTimeout,
                EpsonRobot2ScanInterval = config.ScanInterval,
                EpsonRobot2AutoReconnect = config.AutoReconnect,
                EpsonRobot2ReconnectInterval = config.ReconnectInterval,
                EpsonRobot2MaxReconnectAttempts = config.MaxReconnectAttempts
            };
        }

        /// <summary>
        /// 加载默认配置
        /// </summary>
        /// <returns>默认配置</returns>
        private EpsonRobotConfiguration LoadDefaultConfiguration()
        {
            // 使用Settings系统中的默认值
            var communicationSettings = Settings.Settings.Current.Communication;

            return new EpsonRobotConfiguration
            {
                IPAddress = communicationSettings.EpsonRobot2IP,
                ControlPort = communicationSettings.EpsonRobot2ControlPort,
                DataPort = communicationSettings.EpsonRobot2DataPort,
                Password = communicationSettings.EpsonRobot2Password,
                ConnectTimeout = communicationSettings.EpsonRobot2ConnectTimeout,
                ReceiveTimeout = communicationSettings.EpsonRobot2ReceiveTimeout,
                SendTimeout = communicationSettings.EpsonRobot2SendTimeout,
                ScanInterval = communicationSettings.EpsonRobot2ScanInterval,
                AutoReconnect = communicationSettings.EpsonRobot2AutoReconnect,
                ReconnectInterval = communicationSettings.EpsonRobot2ReconnectInterval,
                MaxReconnectAttempts = communicationSettings.EpsonRobot2MaxReconnectAttempts
            };
        }

        /// <summary>
        /// 更新连接状态
        /// </summary>
        /// <param name="connectionType">连接类型</param>
        /// <param name="status">状态</param>
        /// <param name="description">描述</param>
        private void UpdateConnectionStatus(string connectionType, CommunicationStatus status, string description)
        {
            LogHelper.Info($"Epson机器人{connectionType}连接状态变更: {status} - {description}");
            ConnectionStatusChanged?.Invoke(this, new EpsonRobotConnectionStatusChangedEventArgs(connectionType, status, description));
        }

        /// <summary>
        /// 更新自动化状态
        /// </summary>
        /// <param name="isScanning">是否正在扫描</param>
        /// <param name="isWaitingForManualInput">是否等待手动输入</param>
        /// <param name="currentSpecialCommand">当前特殊命令</param>
        /// <param name="statusDescription">状态描述</param>
        private void UpdateAutomationStatus(bool isScanning, bool isWaitingForManualInput,
            SpecialCommandType? currentSpecialCommand, string statusDescription)
        {
            AutomationStatusChanged?.Invoke(this, new AutomationStatusChangedEventArgs(
                isScanning, isWaitingForManualInput, currentSpecialCommand, statusDescription));
        }

        /// <summary>
        /// 发送数据
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="connectionType">连接类型</param>
        /// <returns>是否成功</returns>
        private async Task<bool> SendDataAsync(string data, string connectionType)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                await Task.CompletedTask; // 消除CS1998警告
                if (string.IsNullOrEmpty(data))
                    return true;

                byte[] buffer = Encoding.UTF8.GetBytes(data);

                lock (_lockObject)
                {
                    NetworkStream stream = connectionType == "StartStop" ? _startStopStream : _dataStream;

                    if (stream != null && stream.CanWrite)
                    {
                        stream.Write(buffer, 0, buffer.Length);
                        stream.Flush();
                    }
                    else
                    {
                        throw new InvalidOperationException($"{connectionType}网络流不可写");
                    }
                }

                LogHelper.Debug($"发送{connectionType}TCP/IP数据: {data.Trim()}");
                return true;

            }, false, $"发送{connectionType}TCP/IP数据");
        }

        /// <summary>
        /// 启动/停止连接监听循环
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        private async Task StartStopListenLoopAsync(CancellationToken cancellationToken)
        {
            LogHelper.Info("启动/停止TCP监听循环开始");

            try
            {
                var buffer = new byte[4096];
                var messageBuffer = new StringBuilder();

                while (!cancellationToken.IsCancellationRequested && _startStopConnected)
                {
                    try
                    {
                        if (_startStopStream != null && _startStopStream.DataAvailable)
                        {
                            int bytesRead = await _startStopStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);

                            if (bytesRead > 0)
                            {
                                string receivedData = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                                messageBuffer.Append(receivedData);

                                // 处理完整消息（以\r\n分隔）
                                await ProcessReceivedDataAsync(messageBuffer, "StartStop");
                            }
                        }
                        else
                        {
                            await Task.Delay(50, cancellationToken);
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error("启动/停止TCP/IP数据接收异常", ex);
                        RobotError?.Invoke(this, new EpsonRobotErrorEventArgs(
                            EpsonRobotErrorType.CommunicationTimeout, ex.Message, "StartStop", true));

                        if (_config.AutoReconnect)
                        {
                            LogHelper.Info("尝试自动重连启动/停止TCP/IP...");
                            await Task.Delay(_config.ReconnectInterval, cancellationToken);
                            await ConnectStartStopAsync();
                        }
                        else
                        {
                            break;
                        }
                    }
                }
            }
            catch (OperationCanceledException)
            {
                LogHelper.Info("启动/停止TCP监听循环被取消");
            }
            catch (Exception ex)
            {
                LogHelper.Error("启动/停止TCP监听循环异常", ex);
            }

            LogHelper.Info("启动/停止TCP监听循环结束");
        }

        /// <summary>
        /// 数据收发连接监听循环
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        private async Task DataListenLoopAsync(CancellationToken cancellationToken)
        {
            LogHelper.Info("数据收发TCP监听循环开始");

            try
            {
                var buffer = new byte[4096];
                var messageBuffer = new StringBuilder();

                while (!cancellationToken.IsCancellationRequested && _dataConnected)
                {
                    try
                    {
                        if (_dataStream != null && _dataStream.DataAvailable)
                        {
                            int bytesRead = await _dataStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);

                            if (bytesRead > 0)
                            {
                                string receivedData = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                                messageBuffer.Append(receivedData);

                                // 处理完整消息（以\r\n分隔）
                                await ProcessReceivedDataAsync(messageBuffer, "Data");
                            }
                        }
                        else
                        {
                            await Task.Delay(50, cancellationToken);
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error("数据收发TCP/IP数据接收异常", ex);
                        RobotError?.Invoke(this, new EpsonRobotErrorEventArgs(
                            EpsonRobotErrorType.CommunicationTimeout, ex.Message, "Data", true));

                        if (_config.AutoReconnect)
                        {
                            LogHelper.Info("尝试自动重连数据收发TCP/IP...");
                            await Task.Delay(_config.ReconnectInterval, cancellationToken);
                            await ConnectDataAsync();
                        }
                        else
                        {
                            break;
                        }
                    }
                }
            }
            catch (OperationCanceledException)
            {
                LogHelper.Info("数据收发TCP监听循环被取消");
            }
            catch (Exception ex)
            {
                LogHelper.Error("数据收发TCP监听循环异常", ex);
            }

            LogHelper.Info("数据收发TCP监听循环结束");
        }

        /// <summary>
        /// 处理接收到的数据
        /// </summary>
        /// <param name="messageBuffer">消息缓冲区</param>
        /// <param name="connectionType">连接类型</param>
        /// <returns></returns>
        private async Task ProcessReceivedDataAsync(StringBuilder messageBuffer, string connectionType)
        {
            await Task.Run(() =>
            {
                string data = messageBuffer.ToString();
                string[] lines = data.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

                foreach (string line in lines)
                {
                    if (!string.IsNullOrWhiteSpace(line))
                    {
                        ProcessSingleMessage(line.Trim(), connectionType);
                    }
                }

                // 清空已处理的数据
                messageBuffer.Clear();

                // 如果还有未完整的数据，保留在缓冲区中
                int lastNewLineIndex = data.LastIndexOfAny(new[] { '\r', '\n' });
                if (lastNewLineIndex >= 0 && lastNewLineIndex < data.Length - 1)
                {
                    messageBuffer.Append(data.Substring(lastNewLineIndex + 1));
                }
            });
        }

        /// <summary>
        /// 处理单条消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="connectionType">连接类型</param>
        private void ProcessSingleMessage(string message, string connectionType)
        {
            try
            {
                LogHelper.Debug($"接收到{connectionType}消息: {message}");

                // 检查是否是特殊命令（用于自动化流程）
                if (connectionType == "Data" && _isScanning && !_isWaitingForManualInput)
                {
                    if (CheckSpecialCommand(message))
                    {
                        return; // 特殊命令已处理，不需要继续处理响应
                    }
                }

                // 解析Epson机器人响应
                var response = new EpsonRobotResponse(message);

                // 触发响应接收事件
                ResponseReceived?.Invoke(this, new EpsonRobotResponseReceivedEventArgs(response, connectionType));

                // 如果有等待的命令，完成对应的TaskCompletionSource
                // 按照FIFO顺序处理（最早的命令先处理）
                var pendingCommand = _pendingCommands.Values.FirstOrDefault(tcs => tcs != null && !tcs.Task.IsCompleted);
                if (pendingCommand != null)
                {
                    pendingCommand.SetResult(response);
                    LogHelper.Debug($"命令响应已处理: {message}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"处理{connectionType}消息失败: {message}", ex);
            }
        }

        /// <summary>
        /// 检查特殊命令
        /// </summary>
        /// <param name="message">消息</param>
        /// <returns>是否是特殊命令</returns>
        private bool CheckSpecialCommand(string message)
        {
            var specialCommands = new[] { "GETPICK", "INPICK", "GETNGPUT", "NGPUTFULL", "GETOKPUT" };

            foreach (var cmd in specialCommands)
            {
                if (message.Contains(cmd))
                {
                    if (Enum.TryParse<SpecialCommandType>(cmd, out var commandType))
                    {
                        _isWaitingForManualInput = true;
                        _currentSpecialCommand = commandType;

                        UpdateAutomationStatus(false, true, commandType, $"收到型号：{cmd}，等待手动发送输入框命令");
                        LogHelper.Info($"检测到特殊命令: {cmd}，停止扫描，等待手动输入");

                        // 触发特殊命令接收事件
                        SpecialCommandReceived?.Invoke(this, new SpecialCommandReceivedEventArgs(commandType, message));
                        return true;
                    }
                }
            }

            return false;
        }
        #endregion
    }
}
