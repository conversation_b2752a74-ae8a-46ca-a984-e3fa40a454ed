using System;
using System.Collections.Generic;

namespace MyHMI.Models
{
    /// <summary>
    /// IO端口定义类
    /// 根据端口定义文件.md定义的IO端口映射
    /// </summary>
    public class IOPortDefinition
    {
        /// <summary>
        /// IO编号
        /// </summary>
        public string IONumber { get; set; }

        /// <summary>
        /// 脚位号
        /// </summary>
        public int PinNumber { get; set; }

        /// <summary>
        /// IO名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 连接位置描述
        /// </summary>
        public string Connection { get; set; }

        /// <summary>
        /// IO类型
        /// </summary>
        public IOPortType Type { get; set; }

        /// <summary>
        /// 是否为扩展IO
        /// </summary>
        public bool IsExtended { get; set; }

        /// <summary>
        /// 位号（用于DMC1000B函数调用）
        /// </summary>
        public int BitNumber { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 轴号（仅用于专用轴信号，如ORG、PEL、NEL等）
        /// </summary>
        public int AxisNumber { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public IOPortDefinition()
        {
            IONumber = string.Empty;
            Name = string.Empty;
            Connection = string.Empty;
            Remark = string.Empty;
        }
    }

    /// <summary>
    /// IO端口类型枚举
    /// </summary>
    public enum IOPortType
    {
        /// <summary>
        /// 输入端口
        /// </summary>
        Input,

        /// <summary>
        /// 输出端口
        /// </summary>
        Output,

        /// <summary>
        /// 专用轴输入信号（如ORG、PEL、NEL等）
        /// </summary>
        AxisSpecialInput
    }

    /// <summary>
    /// IO状态类
    /// </summary>
    public class IOStatus
    {
        /// <summary>
        /// IO编号
        /// </summary>
        public string IONumber { get; set; }

        /// <summary>
        /// IO名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 当前状态（true: 高电平/ON, false: 低电平/OFF）
        /// </summary>
        public bool State { get; set; }

        /// <summary>
        /// 上次状态
        /// </summary>
        public bool PreviousState { get; set; }

        /// <summary>
        /// 状态更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; }

        /// <summary>
        /// IO类型
        /// </summary>
        public IOPortType Type { get; set; }

        /// <summary>
        /// 是否为扩展IO
        /// </summary>
        public bool IsExtended { get; set; }

        /// <summary>
        /// 位号
        /// </summary>
        public int BitNumber { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public IOStatus()
        {
            IONumber = string.Empty;
            Name = string.Empty;
            UpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="ioNumber">IO编号</param>
        /// <param name="name">IO名称</param>
        /// <param name="state">当前状态</param>
        /// <param name="type">IO类型</param>
        /// <param name="bitNumber">位号</param>
        /// <param name="isExtended">是否为扩展IO</param>
        public IOStatus(string ioNumber, string name, bool state, IOPortType type, int bitNumber, bool isExtended = false)
        {
            IONumber = ioNumber;
            Name = name;
            State = state;
            PreviousState = state;
            Type = type;
            BitNumber = bitNumber;
            IsExtended = isExtended;
            UpdateTime = DateTime.Now;
        }
    }

    /// <summary>
    /// IO配置类
    /// 定义所有IO端口的配置信息
    /// </summary>
    public static class IOConfiguration
    {
        /// <summary>
        /// 基础输入IO配置（I0001-I0016）
        /// </summary>
        public static readonly List<IOPortDefinition> BasicInputPorts = new List<IOPortDefinition>
        {
            new IOPortDefinition { IONumber = "I0001", PinNumber = 30, Name = "启动", Connection = "启动按钮", Type = IOPortType.Input, IsExtended = false, BitNumber = 1 },
            new IOPortDefinition { IONumber = "I0002", PinNumber = 31, Name = "暂停", Connection = "暂停/停止按钮", Type = IOPortType.Input, IsExtended = false, BitNumber = 2 },
            new IOPortDefinition { IONumber = "I0003", PinNumber = 32, Name = "急停", Connection = "急停按钮", Type = IOPortType.Input, IsExtended = false, BitNumber = 3 },
            new IOPortDefinition { IONumber = "I0004", PinNumber = 33, Name = "来料感应", Connection = "来料检测传感器", Type = IOPortType.Input, IsExtended = false, BitNumber = 4 },
            new IOPortDefinition { IONumber = "I0005", PinNumber = 55, Name = "左夹料夹感应", Connection = "左夹料气缸夹紧传感器", Type = IOPortType.Input, IsExtended = false, BitNumber = 5 },
            new IOPortDefinition { IONumber = "I0006", PinNumber = 56, Name = "左夹料松感应", Connection = "左夹料气缸松开传感器", Type = IOPortType.Input, IsExtended = false, BitNumber = 6 },
            new IOPortDefinition { IONumber = "I0007", PinNumber = 57, Name = "左顶料进感应", Connection = "左顶料气缸伸出传感器", Type = IOPortType.Input, IsExtended = false, BitNumber = 7 },
            new IOPortDefinition { IONumber = "I0008", PinNumber = 58, Name = "左顶料退感应", Connection = "左顶料气缸缩回传感器", Type = IOPortType.Input, IsExtended = false, BitNumber = 8 },
            new IOPortDefinition { IONumber = "I0009", PinNumber = 59, Name = "右夹料夹感应", Connection = "右夹料气缸夹紧传感器", Type = IOPortType.Input, IsExtended = false, BitNumber = 9 },
            new IOPortDefinition { IONumber = "I0010", PinNumber = 60, Name = "右夹料松感应", Connection = "右夹料气缸松开传感器", Type = IOPortType.Input, IsExtended = false, BitNumber = 10 },
            new IOPortDefinition { IONumber = "I0011", PinNumber = 61, Name = "右顶料进感应", Connection = "右顶料气缸伸出传感器", Type = IOPortType.Input, IsExtended = false, BitNumber = 11 },
            new IOPortDefinition { IONumber = "I0012", PinNumber = 62, Name = "右顶料退感应", Connection = "右顶料气缸缩回传感器", Type = IOPortType.Input, IsExtended = false, BitNumber = 12 },
            new IOPortDefinition { IONumber = "I0013", PinNumber = 63, Name = "左NG盘感应", Connection = "左NG料盘检测传感器", Type = IOPortType.Input, IsExtended = false, BitNumber = 13 },
            new IOPortDefinition { IONumber = "I0014", PinNumber = 64, Name = "右NG盘感应", Connection = "右NG料盘检测传感器", Type = IOPortType.Input, IsExtended = false, BitNumber = 14 },
            // I0015 在端口定义文件中没有连接位置，已移除
            new IOPortDefinition { IONumber = "I0016", PinNumber = 68, Name = "板卡上电检测", Connection = "控制卡电源状态", Type = IOPortType.Input, IsExtended = false, BitNumber = 16 }
        };

        /// <summary>
        /// 基础输出IO配置（O0001-O0012）
        /// </summary>
        public static readonly List<IOPortDefinition> BasicOutputPorts = new List<IOPortDefinition>
        {
            new IOPortDefinition { IONumber = "O0001", PinNumber = 43, Name = "左夹料气缸", Connection = "左夹料气缸控制", Type = IOPortType.Output, IsExtended = false, BitNumber = 1 },
            new IOPortDefinition { IONumber = "O0002", PinNumber = 44, Name = "左顶料气缸", Connection = "左顶料气缸控制", Type = IOPortType.Output, IsExtended = false, BitNumber = 2 },
            new IOPortDefinition { IONumber = "O0003", PinNumber = 45, Name = "右夹料气缸", Connection = "右夹料气缸控制", Type = IOPortType.Output, IsExtended = false, BitNumber = 3 },
            new IOPortDefinition { IONumber = "O0004", PinNumber = 46, Name = "右顶料气缸", Connection = "右顶料气缸控制", Type = IOPortType.Output, IsExtended = false, BitNumber = 4 },
            new IOPortDefinition { IONumber = "O0005", PinNumber = 47, Name = "主光源", Connection = "主要照明光源", Type = IOPortType.Output, IsExtended = false, BitNumber = 5 },
            new IOPortDefinition { IONumber = "O0006", PinNumber = 48, Name = "左NG灯", Connection = "左侧NG指示灯", Type = IOPortType.Output, IsExtended = false, BitNumber = 6 },
            new IOPortDefinition { IONumber = "O0007", PinNumber = 49, Name = "右NG灯", Connection = "右侧NG指示灯", Type = IOPortType.Output, IsExtended = false, BitNumber = 7 },
            new IOPortDefinition { IONumber = "O0008", PinNumber = 50, Name = "三色灯红", Connection = "三色灯红色", Type = IOPortType.Output, IsExtended = false, BitNumber = 8 },
            new IOPortDefinition { IONumber = "O0009", PinNumber = 51, Name = "三色灯绿", Connection = "三色灯绿色", Type = IOPortType.Output, IsExtended = false, BitNumber = 9 },
            new IOPortDefinition { IONumber = "O0010", PinNumber = 52, Name = "三色灯黄", Connection = "三色灯黄色", Type = IOPortType.Output, IsExtended = false, BitNumber = 10 },
            new IOPortDefinition { IONumber = "O0011", PinNumber = 53, Name = "上料光源", Connection = "上料区域光源", Type = IOPortType.Output, IsExtended = false, BitNumber = 11 }
            // O0012 在端口定义文件中没有连接位置，已移除
        };

        /// <summary>
        /// 扩展输入IO配置（I0101-I0116）
        /// </summary>
        public static readonly List<IOPortDefinition> ExtendedInputPorts = new List<IOPortDefinition>
        {
            // 只包含端口定义文件中有连接位置的扩展输入IO
            new IOPortDefinition { IONumber = "I0101", PinNumber = 0, Name = "安全门", Connection = "安全门开关", Type = IOPortType.Input, IsExtended = true, BitNumber = 17 },
            new IOPortDefinition { IONumber = "I0102", PinNumber = 0, Name = "光栅1", Connection = "第一道光栅", Type = IOPortType.Input, IsExtended = true, BitNumber = 18 },
            new IOPortDefinition { IONumber = "I0103", PinNumber = 0, Name = "光栅2", Connection = "第二道光栅", Type = IOPortType.Input, IsExtended = true, BitNumber = 19 },
            new IOPortDefinition { IONumber = "I0104", PinNumber = 0, Name = "左NG确认", Connection = "左NG确认按钮", Type = IOPortType.Input, IsExtended = true, BitNumber = 20 },
            new IOPortDefinition { IONumber = "I0105", PinNumber = 0, Name = "右NG确认", Connection = "右NG确认按钮", Type = IOPortType.Input, IsExtended = true, BitNumber = 21 },
            new IOPortDefinition { IONumber = "I0106", PinNumber = 0, Name = "出料感应", Connection = "出料检测传感器", Type = IOPortType.Input, IsExtended = true, BitNumber = 22 },
            new IOPortDefinition { IONumber = "I0107", PinNumber = 0, Name = "出料完成", Connection = "出料完成信号", Type = IOPortType.Input, IsExtended = true, BitNumber = 23 },
            // I0108-I0112 在端口定义文件中没有连接位置，已移除
            new IOPortDefinition { IONumber = "I0113", PinNumber = 0, Name = "后站对接输入2", Connection = "后工位对接信号2", Type = IOPortType.Input, IsExtended = true, BitNumber = 29 },
            new IOPortDefinition { IONumber = "I0114", PinNumber = 0, Name = "前站对接输入", Connection = "前工位对接信号", Type = IOPortType.Input, IsExtended = true, BitNumber = 30 },
            new IOPortDefinition { IONumber = "I0115", PinNumber = 0, Name = "后站对接输入1", Connection = "后工位对接信号1", Type = IOPortType.Input, IsExtended = true, BitNumber = 31 }
            // I0116 在端口定义文件中没有连接位置，已移除
        };

        /// <summary>
        /// 扩展输出IO配置（O0101-O0115）
        /// </summary>
        public static readonly List<IOPortDefinition> ExtendedOutputPorts = new List<IOPortDefinition>
        {
            // 只包含端口定义文件中有连接位置的扩展输出IO
            // O0101-O0112 在端口定义文件中没有连接位置，已移除
            new IOPortDefinition { IONumber = "O0113", PinNumber = 0, Name = "后站对接输出2", Connection = "后工位对接输出2", Type = IOPortType.Output, IsExtended = true, BitNumber = 25 },
            new IOPortDefinition { IONumber = "O0114", PinNumber = 0, Name = "前站对接输出", Connection = "前工位对接输出", Type = IOPortType.Output, IsExtended = true, BitNumber = 26 },
            new IOPortDefinition { IONumber = "O0115", PinNumber = 0, Name = "后站对接输出", Connection = "后工位对接输出", Type = IOPortType.Output, IsExtended = true, BitNumber = 27 }
        };

        /// <summary>
        /// 专用轴信号配置（ORG、PEL、NEL等）
        /// </summary>
        public static readonly List<IOPortDefinition> AxisSpecialInputPorts = new List<IOPortDefinition>
        {
            // 根据端口定义文件和DMC1000B轴信号定义
            new IOPortDefinition { IONumber = "ORG0", PinNumber = 14, Name = "左翻转电机原点", Connection = "左翻转电机原点开关", Type = IOPortType.AxisSpecialInput, IsExtended = false, BitNumber = 2, AxisNumber = 0 },
            new IOPortDefinition { IONumber = "ORG1", PinNumber = 19, Name = "右翻转电机原点", Connection = "右翻转电机原点开关", Type = IOPortType.AxisSpecialInput, IsExtended = false, BitNumber = 2, AxisNumber = 1 },
            // 可以继续添加其他专用轴信号
            // new IOPortDefinition { IONumber = "PEL0", PinNumber = 0, Name = "左翻转电机正限位", Connection = "左翻转电机正限位开关", Type = IOPortType.AxisSpecialInput, IsExtended = false, BitNumber = 1, AxisNumber = 0 },
            // new IOPortDefinition { IONumber = "NEL0", PinNumber = 0, Name = "左翻转电机负限位", Connection = "左翻转电机负限位开关", Type = IOPortType.AxisSpecialInput, IsExtended = false, BitNumber = 0, AxisNumber = 0 },
            // new IOPortDefinition { IONumber = "PEL1", PinNumber = 0, Name = "右翻转电机正限位", Connection = "右翻转电机正限位开关", Type = IOPortType.AxisSpecialInput, IsExtended = false, BitNumber = 1, AxisNumber = 1 },
            // new IOPortDefinition { IONumber = "NEL1", PinNumber = 0, Name = "右翻转电机负限位", Connection = "右翻转电机负限位开关", Type = IOPortType.AxisSpecialInput, IsExtended = false, BitNumber = 0, AxisNumber = 1 }
        };

        /// <summary>
        /// 获取所有输入IO配置（包含专用轴信号）
        /// </summary>
        /// <returns>所有输入IO配置列表</returns>
        public static List<IOPortDefinition> GetAllInputPorts()
        {
            var allInputs = new List<IOPortDefinition>();
            allInputs.AddRange(BasicInputPorts);
            allInputs.AddRange(ExtendedInputPorts);
            allInputs.AddRange(AxisSpecialInputPorts);
            return allInputs;
        }

        /// <summary>
        /// 获取所有输出IO配置
        /// </summary>
        /// <returns>所有输出IO配置列表</returns>
        public static List<IOPortDefinition> GetAllOutputPorts()
        {
            var allOutputs = new List<IOPortDefinition>();
            allOutputs.AddRange(BasicOutputPorts);
            allOutputs.AddRange(ExtendedOutputPorts);
            return allOutputs;
        }

        /// <summary>
        /// 根据IO编号获取IO配置
        /// </summary>
        /// <param name="ioNumber">IO编号</param>
        /// <returns>IO配置，未找到返回null</returns>
        public static IOPortDefinition GetIOPortByNumber(string ioNumber)
        {
            var allPorts = new List<IOPortDefinition>();
            allPorts.AddRange(GetAllInputPorts());
            allPorts.AddRange(GetAllOutputPorts());

            return allPorts.Find(p => p.IONumber == ioNumber);
        }

        /// <summary>
        /// 根据位号获取IO配置
        /// </summary>
        /// <param name="bitNumber">位号</param>
        /// <param name="type">IO类型</param>
        /// <returns>IO配置，未找到返回null</returns>
        public static IOPortDefinition GetIOPortByBitNumber(int bitNumber, IOPortType type)
        {
            var ports = type == IOPortType.Input ? GetAllInputPorts() : GetAllOutputPorts();
            return ports.Find(p => p.BitNumber == bitNumber);
        }
    }

    /// <summary>
    /// IO输出状态变化事件参数
    /// </summary>
    public class IOOutputChangedEventArgs : EventArgs
    {
        /// <summary>
        /// IO编号
        /// </summary>
        public string IONumber { get; }

        /// <summary>
        /// 新状态
        /// </summary>
        public bool NewState { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="ioNumber">IO编号</param>
        /// <param name="newState">新状态</param>
        public IOOutputChangedEventArgs(string ioNumber, bool newState)
        {
            IONumber = ioNumber;
            NewState = newState;
        }
    }

    /// <summary>
    /// IO输入状态变化事件参数
    /// </summary>
    public class IOInputStateChangedEventArgs : EventArgs
    {
        /// <summary>
        /// IO编号
        /// </summary>
        public string IONumber { get; }

        /// <summary>
        /// 新状态
        /// </summary>
        public bool NewState { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="ioNumber">IO编号</param>
        /// <param name="newState">新状态</param>
        public IOInputStateChangedEventArgs(string ioNumber, bool newState)
        {
            IONumber = ioNumber;
            NewState = newState;
        }
    }

    /// <summary>
    /// IO输出状态变化事件参数
    /// </summary>
    public class IOOutputStateChangedEventArgs : EventArgs
    {
        /// <summary>
        /// IO编号
        /// </summary>
        public string IONumber { get; }

        /// <summary>
        /// 新状态
        /// </summary>
        public bool NewState { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="ioNumber">IO编号</param>
        /// <param name="newState">新状态</param>
        public IOOutputStateChangedEventArgs(string ioNumber, bool newState)
        {
            IONumber = ioNumber;
            NewState = newState;
        }
    }

    /// <summary>
    /// 批量IO状态变化事件参数
    /// </summary>
    public class BatchIOStateChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 变化的IO状态字典
        /// </summary>
        public Dictionary<string, bool> ChangedStates { get; }

        /// <summary>
        /// IO类型
        /// </summary>
        public IOPortType IOType { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="changedStates">变化的IO状态字典</param>
        /// <param name="ioType">IO类型</param>
        public BatchIOStateChangedEventArgs(Dictionary<string, bool> changedStates, IOPortType ioType)
        {
            ChangedStates = changedStates ?? new Dictionary<string, bool>();
            IOType = ioType;
        }
    }
}
