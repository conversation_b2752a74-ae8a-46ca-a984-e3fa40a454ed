# WorkflowManager重构后API文档

## 文档版本
- 版本：1.0
- 创建时间：2025-09-27
- 适用范围：WorkflowManager重构后的API接口

## 概述

本文档详细描述了WorkflowManager重构后的API接口，包括WorkflowManager和BeltMotorAutoModeController的所有公共方法和事件。

## WorkflowManager API

### 类概述
```csharp
public class WorkflowManager
```
工作流管理器，负责协调和管理各种AutoMode控制器的工作流程。

### 单例模式
```csharp
public static WorkflowManager Instance { get; }
```
获取WorkflowManager的单例实例。

### 属性

#### IsInitialized
```csharp
public bool IsInitialized { get; }
```
获取工作流管理器是否已初始化。

#### CurrentState
```csharp
public WorkflowState CurrentState { get; }
```
获取当前工作流状态。

#### IsWorkflowEnabled
```csharp
public bool IsWorkflowEnabled { get; }
```
获取工作流是否启用。

### 方法

#### InitializeAsync
```csharp
public async Task<bool> InitializeAsync()
```
**功能**: 初始化工作流管理器
**返回值**: 初始化是否成功
**说明**: 初始化所有AutoMode控制器并订阅相关事件

#### StartWorkflowAsync
```csharp
public async Task<bool> StartWorkflowAsync(string productId = "")
```
**功能**: 启动工作流
**参数**: 
- `productId`: 产品ID（可选）
**返回值**: 启动是否成功
**说明**: 启动完整的工作流程，包括所有AutoMode控制器

#### StopWorkflowAsync
```csharp
public async Task<bool> StopWorkflowAsync()
```
**功能**: 停止工作流
**返回值**: 停止是否成功
**说明**: 停止所有正在运行的工作流程

#### ResetWorkflowAsync
```csharp
public async Task<bool> ResetWorkflowAsync()
```
**功能**: 重置工作流
**返回值**: 重置是否成功
**说明**: 将工作流重置到初始状态

### 事件

#### WorkflowStateChanged
```csharp
public event EventHandler<WorkflowStateChangedEventArgs> WorkflowStateChanged;
```
**功能**: 工作流状态变化事件
**参数**: `WorkflowStateChangedEventArgs`包含新旧状态信息

#### WorkflowCompleted
```csharp
public event EventHandler<WorkflowCompletedEventArgs> WorkflowCompleted;
```
**功能**: 工作流完成事件
**参数**: `WorkflowCompletedEventArgs`包含完成信息

#### WorkflowError
```csharp
public event EventHandler<WorkflowErrorEventArgs> WorkflowError;
```
**功能**: 工作流错误事件
**参数**: `WorkflowErrorEventArgs`包含错误信息

## BeltMotorAutoModeController API

### 类概述
```csharp
public class BeltMotorAutoModeController
```
皮带电机自动模式控制器，专门负责皮带电机的自动控制功能。

### 单例模式
```csharp
public static BeltMotorAutoModeController Instance { get; }
```
获取BeltMotorAutoModeController的单例实例。

### 属性

#### IsInitialized
```csharp
public bool IsInitialized { get; }
```
获取控制器是否已初始化。

#### IsRunning
```csharp
public bool IsRunning { get; }
```
获取控制器是否正在运行。

#### CurrentState
```csharp
public BeltMotorState CurrentState { get; }
```
获取当前皮带电机状态。

### 方法

#### InitializeAsync
```csharp
public async Task<bool> InitializeAsync()
```
**功能**: 初始化皮带电机控制器
**返回值**: 初始化是否成功
**说明**: 初始化电机管理器和传感器监控

#### StartAsync
```csharp
public async Task<bool> StartAsync()
```
**功能**: 启动自动模式
**返回值**: 启动是否成功
**说明**: 启动皮带电机自动控制流程

#### StopAsync
```csharp
public async Task<bool> StopAsync()
```
**功能**: 停止自动模式
**返回值**: 停止是否成功
**说明**: 停止皮带电机自动控制流程

#### ResetAsync
```csharp
public async Task<bool> ResetAsync()
```
**功能**: 重置控制器
**返回值**: 重置是否成功
**说明**: 将控制器重置到初始状态

#### StartInputBeltAsync
```csharp
public async Task<bool> StartInputBeltAsync()
```
**功能**: 启动输入皮带
**返回值**: 启动是否成功
**说明**: 手动启动输入皮带电机

#### StopInputBeltAsync
```csharp
public async Task<bool> StopInputBeltAsync()
```
**功能**: 停止输入皮带
**返回值**: 停止是否成功
**说明**: 手动停止输入皮带电机

#### StartOutputBeltAsync
```csharp
public async Task<bool> StartOutputBeltAsync()
```
**功能**: 启动输出皮带
**返回值**: 启动是否成功
**说明**: 手动启动输出皮带电机

#### StopOutputBeltAsync
```csharp
public async Task<bool> StopOutputBeltAsync()
```
**功能**: 停止输出皮带
**返回值**: 停止是否成功
**说明**: 手动停止输出皮带电机

### 事件

#### StateChanged
```csharp
public event EventHandler<BeltMotorStateChangedEventArgs> StateChanged;
```
**功能**: 状态变化事件
**参数**: `BeltMotorStateChangedEventArgs`包含状态变化信息

#### ErrorOccurred
```csharp
public event EventHandler<BeltMotorErrorEventArgs> ErrorOccurred;
```
**功能**: 错误发生事件
**参数**: `BeltMotorErrorEventArgs`包含错误信息

## 枚举类型

### WorkflowState
```csharp
public enum WorkflowState
{
    Idle,           // 空闲状态
    WaitingForScan, // 等待扫码
    MotorMoving,    // 电机运行中
    VisionDetecting,// 视觉检测中
    RobotOperating, // 机器人操作中
    Error           // 错误状态
}
```

### BeltMotorState
```csharp
public enum BeltMotorState
{
    Idle,    // 空闲状态
    Running, // 运行状态
    Error    // 错误状态
}
```

## 事件参数类

### WorkflowStateChangedEventArgs
```csharp
public class WorkflowStateChangedEventArgs : EventArgs
{
    public WorkflowState OldState { get; }      // 旧状态
    public WorkflowState NewState { get; }      // 新状态
    public DateTime Timestamp { get; }          // 时间戳
    public string Message { get; }              // 消息
}
```

### WorkflowCompletedEventArgs
```csharp
public class WorkflowCompletedEventArgs : EventArgs
{
    public string ProductId { get; }            // 产品ID
    public TimeSpan Duration { get; }           // 执行时长
    public DateTime CompletedTime { get; }      // 完成时间
    public bool Success { get; }                // 是否成功
}
```

### WorkflowErrorEventArgs
```csharp
public class WorkflowErrorEventArgs : EventArgs
{
    public string ErrorMessage { get; }         // 错误消息
    public Exception Exception { get; }         // 异常对象
    public DateTime Timestamp { get; }          // 时间戳
    public string Source { get; }               // 错误源
}
```

### BeltMotorStateChangedEventArgs
```csharp
public class BeltMotorStateChangedEventArgs : EventArgs
{
    public BeltMotorState OldState { get; }     // 旧状态
    public BeltMotorState NewState { get; }     // 新状态
    public DateTime Timestamp { get; }          // 时间戳
    public string Message { get; }              // 消息
}
```

### BeltMotorErrorEventArgs
```csharp
public class BeltMotorErrorEventArgs : EventArgs
{
    public string ErrorMessage { get; }         // 错误消息
    public Exception Exception { get; }         // 异常对象
    public DateTime Timestamp { get; }          // 时间戳
    public string MotorType { get; }            // 电机类型
}
```

## 使用示例

### 基本工作流控制
```csharp
// 获取WorkflowManager实例
var workflowManager = WorkflowManager.Instance;

// 订阅事件
workflowManager.WorkflowStateChanged += OnWorkflowStateChanged;
workflowManager.WorkflowCompleted += OnWorkflowCompleted;
workflowManager.WorkflowError += OnWorkflowError;

// 初始化
bool initResult = await workflowManager.InitializeAsync();
if (!initResult)
{
    Console.WriteLine("工作流管理器初始化失败");
    return;
}

// 启动工作流
bool startResult = await workflowManager.StartWorkflowAsync("PRODUCT_001");
if (startResult)
{
    Console.WriteLine("工作流启动成功");
}

// 停止工作流
await workflowManager.StopWorkflowAsync();

// 重置工作流
await workflowManager.ResetWorkflowAsync();
```

### 皮带电机控制
```csharp
// 获取BeltMotorAutoModeController实例
var beltController = BeltMotorAutoModeController.Instance;

// 订阅事件
beltController.StateChanged += OnBeltMotorStateChanged;
beltController.ErrorOccurred += OnBeltMotorError;

// 初始化
bool initResult = await beltController.InitializeAsync();
if (!initResult)
{
    Console.WriteLine("皮带电机控制器初始化失败");
    return;
}

// 启动自动模式
bool startResult = await beltController.StartAsync();
if (startResult)
{
    Console.WriteLine("皮带电机自动模式启动成功");
}

// 手动控制输入皮带
await beltController.StartInputBeltAsync();
await Task.Delay(5000); // 运行5秒
await beltController.StopInputBeltAsync();

// 停止自动模式
await beltController.StopAsync();
```

### 事件处理示例
```csharp
private void OnWorkflowStateChanged(object sender, WorkflowStateChangedEventArgs e)
{
    Console.WriteLine($"工作流状态变化: {e.OldState} -> {e.NewState}");
    Console.WriteLine($"时间: {e.Timestamp}, 消息: {e.Message}");
}

private void OnWorkflowCompleted(object sender, WorkflowCompletedEventArgs e)
{
    Console.WriteLine($"工作流完成: 产品ID={e.ProductId}, 耗时={e.Duration.TotalSeconds}秒");
}

private void OnWorkflowError(object sender, WorkflowErrorEventArgs e)
{
    Console.WriteLine($"工作流错误: {e.ErrorMessage}");
    Console.WriteLine($"错误源: {e.Source}, 时间: {e.Timestamp}");
}

private void OnBeltMotorStateChanged(object sender, BeltMotorStateChangedEventArgs e)
{
    Console.WriteLine($"皮带电机状态变化: {e.OldState} -> {e.NewState}");
}
```

## 最佳实践

### 1. 初始化顺序
```csharp
// 推荐的初始化顺序
var workflowManager = WorkflowManager.Instance;
var beltController = BeltMotorAutoModeController.Instance;

// 先初始化各个控制器
await beltController.InitializeAsync();

// 再初始化工作流管理器（会自动获取控制器引用）
await workflowManager.InitializeAsync();
```

### 2. 异常处理
```csharp
try
{
    bool result = await workflowManager.StartWorkflowAsync("PRODUCT_001");
    if (!result)
    {
        // 处理启动失败
        LogHelper.Warning("工作流启动失败");
    }
}
catch (Exception ex)
{
    // 处理异常
    LogHelper.Error("工作流启动异常", ex);
}
```

### 3. 资源清理
```csharp
// 在应用程序关闭时清理资源
public void Cleanup()
{
    // 取消事件订阅
    workflowManager.WorkflowStateChanged -= OnWorkflowStateChanged;
    beltController.StateChanged -= OnBeltMotorStateChanged;
    
    // 停止所有操作
    workflowManager.StopWorkflowAsync().Wait();
    beltController.StopAsync().Wait();
}
```

## 注意事项

1. **线程安全**: 所有API都是线程安全的，可以在多线程环境中使用
2. **异步操作**: 所有操作都是异步的，请使用await关键字
3. **事件订阅**: 记得在适当的时候取消事件订阅，避免内存泄漏
4. **错误处理**: 建议对所有API调用进行适当的错误处理
5. **状态检查**: 在执行操作前检查相关状态，确保操作的有效性

## 版本历史

- **v1.0** (2025-09-27): 初始版本，包含WorkflowManager重构后的完整API文档
