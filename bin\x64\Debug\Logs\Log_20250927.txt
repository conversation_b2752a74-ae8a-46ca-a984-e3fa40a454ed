[2025-09-27 14:41:13.024] [INFO] 程序启动开始
[2025-09-27 14:41:13.027] [INFO] 配置系统初始化成功
[2025-09-27 14:41:13.679] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-27 14:41:13.689] [INFO] 配置系统初始化完成
[2025-09-27 14:41:13.693] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-27 14:41:14.224] [INFO] 开始初始化各个Manager...
[2025-09-27 14:41:14.225] [INFO] 初始化DMC1000B控制卡...
[2025-09-27 14:41:14.241] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-27 14:41:14.392] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-27 14:41:14.392] [WARN] DMC1000B控制卡初始化失败，相关功能可能无法正常工作
[2025-09-27 14:41:14.392] [INFO] 初始化基础Manager...
[2025-09-27 14:41:14.462] [INFO] IO状态缓存初始化完成
[2025-09-27 14:41:14.489] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-27 14:41:14.489] [WARN] DMC1000BIO管理器初始化失败
[2025-09-27 14:41:14.518] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-27 14:41:14.754] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 136
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:41:14.820] [WARN] DMC1000B电机管理器初始化失败
[2025-09-27 14:41:14.821] [INFO] 初始化系统模式管理器...
[2025-09-27 14:41:14.840] [INFO] 安全管理器实例已创建
[2025-09-27 14:41:14.844] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-27 14:41:14.846] [INFO] 当前模式已经是 Manual，无需切换
[2025-09-27 14:41:14.869] [INFO] 开始初始化MotorManager...
[2025-09-27 14:41:14.883] [INFO] 模拟初始化运动控制卡
[2025-09-27 14:41:15.391] [INFO] 加载了8个电机的默认配置
[2025-09-27 14:41:15.392] [INFO] 电机监控任务已启动
[2025-09-27 14:41:15.392] [INFO] MotorManager初始化完成
[2025-09-27 14:41:15.392] [INFO] 初始化通信Manager...
[2025-09-27 14:41:15.395] [INFO] 电机监控循环开始
[2025-09-27 14:41:15.396] [INFO] 开始初始化ScannerManager...
[2025-09-27 14:41:15.398] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-27 14:41:15.401] [INFO] 串口初始化完成
[2025-09-27 14:41:15.404] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-27 14:41:26.078] [ERROR] 连接扫描枪 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerManager.<ConnectAsync>b__23_1() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 144
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerManager.<<ConnectAsync>b__23_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 138
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:41:26.081] [WARN] 扫描枪连接失败，但初始化完成，可稍后重试连接
[2025-09-27 14:41:26.081] [INFO] ScannerManager初始化完成
[2025-09-27 14:41:26.086] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-27 14:41:26.086] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-27 14:41:26.086] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-27 14:41:26.088] [INFO] SCARA通信管理器已初始化
[2025-09-27 14:41:26.090] [INFO] ScannerAutoModeManager管理器引用初始化完成
[2025-09-27 14:41:26.092] [INFO] 开始初始化ScannerAutoModeManager...
[2025-09-27 14:41:26.095] [INFO] 开始初始化MultiScannerManager...
[2025-09-27 14:41:26.096] [INFO] 开始初始化扫描枪1...
[2025-09-27 14:41:26.097] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-27 14:41:26.099] [INFO] 扫描枪1串口初始化完成
[2025-09-27 14:41:26.099] [INFO] 扫描枪1初始化完成
[2025-09-27 14:41:26.100] [INFO] 开始初始化扫描枪2...
[2025-09-27 14:41:26.100] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-27 14:41:26.100] [INFO] 扫描枪2串口初始化完成
[2025-09-27 14:41:26.101] [INFO] 扫描枪2初始化完成
[2025-09-27 14:41:26.101] [INFO] 开始初始化扫描枪3...
[2025-09-27 14:41:26.101] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-27 14:41:26.101] [INFO] 扫描枪3串口初始化完成
[2025-09-27 14:41:26.102] [INFO] 扫描枪3初始化完成
[2025-09-27 14:41:26.102] [INFO] MultiScannerManager初始化完成
[2025-09-27 14:41:26.104] [INFO] 开始自动连接并验证所有扫码器...
[2025-09-27 14:41:26.104] [INFO] 正在连接扫码器1...
[2025-09-27 14:41:26.107] [INFO] 扫描枪1状态变更: Connecting - 正在连接扫描枪1...
[2025-09-27 14:41:26.108] [INFO] 扫码器1状态变化: Connecting - 正在连接扫描枪1...
[2025-09-27 14:41:27.194] [ERROR] 连接扫描枪1 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerInstance.<ConnectAsync>b__28_1() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 743
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerInstance.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 737
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:41:27.195] [WARN] 扫码器1连接失败
[2025-09-27 14:41:27.709] [INFO] 正在连接扫码器2...
[2025-09-27 14:41:27.709] [INFO] 扫描枪2状态变更: Connecting - 正在连接扫描枪2...
[2025-09-27 14:41:27.709] [INFO] 扫码器2状态变化: Connecting - 正在连接扫描枪2...
[2025-09-27 14:41:27.711] [INFO] 扫描枪2连接成功: COM2
[2025-09-27 14:41:27.712] [INFO] 扫描枪2状态变更: Connected - 扫描枪2连接成功
[2025-09-27 14:41:27.712] [INFO] 扫码器2状态变化: Connected - 扫描枪2连接成功
[2025-09-27 14:41:27.712] [INFO] 扫码器2连接成功
[2025-09-27 14:41:27.717] [INFO] 扫描枪2发送数据: hello
[2025-09-27 14:41:28.720] [INFO] 扫码器2hello验证完成
[2025-09-27 14:41:28.720] [INFO] 扫码器2通信验证成功
[2025-09-27 14:41:29.233] [INFO] 正在连接扫码器3...
[2025-09-27 14:41:29.234] [INFO] 扫描枪3状态变更: Connecting - 正在连接扫描枪3...
[2025-09-27 14:41:29.235] [INFO] 扫码器3状态变化: Connecting - 正在连接扫描枪3...
[2025-09-27 14:51:30.608] [INFO] 程序启动开始
[2025-09-27 14:51:30.610] [INFO] 配置系统初始化成功
[2025-09-27 14:51:30.697] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-27 14:51:30.702] [INFO] 配置系统初始化完成
[2025-09-27 14:51:30.703] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-27 14:51:30.765] [INFO] 开始初始化各个Manager...
[2025-09-27 14:51:30.766] [INFO] 初始化DMC1000B控制卡...
[2025-09-27 14:51:30.771] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-27 14:51:30.774] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-27 14:51:30.774] [WARN] DMC1000B控制卡初始化失败，相关功能可能无法正常工作
[2025-09-27 14:51:30.774] [INFO] 初始化基础Manager...
[2025-09-27 14:51:30.780] [INFO] IO状态缓存初始化完成
[2025-09-27 14:51:30.783] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-27 14:51:30.783] [WARN] DMC1000BIO管理器初始化失败
[2025-09-27 14:51:30.789] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-27 14:51:30.836] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 136
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:51:30.854] [WARN] DMC1000B电机管理器初始化失败
[2025-09-27 14:51:30.854] [INFO] 初始化系统模式管理器...
[2025-09-27 14:51:30.865] [INFO] 安全管理器实例已创建
[2025-09-27 14:51:30.869] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-27 14:51:30.870] [INFO] 开始初始化MotorManager...
[2025-09-27 14:51:30.870] [INFO] 当前模式已经是 Manual，无需切换
[2025-09-27 14:51:30.872] [INFO] 模拟初始化运动控制卡
[2025-09-27 14:51:31.091] [INFO] 加载了8个电机的默认配置
[2025-09-27 14:51:31.093] [INFO] 电机监控任务已启动
[2025-09-27 14:51:31.094] [INFO] MotorManager初始化完成
[2025-09-27 14:51:31.094] [INFO] 初始化通信Manager...
[2025-09-27 14:51:31.095] [INFO] 电机监控循环开始
[2025-09-27 14:51:31.099] [INFO] 开始初始化ScannerManager...
[2025-09-27 14:51:31.119] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-27 14:51:31.121] [INFO] 串口初始化完成
[2025-09-27 14:51:31.128] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-27 14:51:45.414] [ERROR] 连接扫描枪 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerManager.<ConnectAsync>b__23_1() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 144
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerManager.<<ConnectAsync>b__23_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 138
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:51:45.416] [WARN] 扫描枪连接失败，但初始化完成，可稍后重试连接
[2025-09-27 14:51:45.417] [INFO] ScannerManager初始化完成
[2025-09-27 14:51:45.421] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-27 14:51:45.421] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-27 14:51:45.421] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-27 14:51:45.422] [INFO] SCARA通信管理器已初始化
[2025-09-27 14:51:45.424] [INFO] ScannerAutoModeManager管理器引用初始化完成
[2025-09-27 14:51:45.426] [INFO] 开始初始化ScannerAutoModeManager...
[2025-09-27 14:51:45.428] [INFO] 开始初始化MultiScannerManager...
[2025-09-27 14:51:45.429] [INFO] 开始初始化扫描枪1...
[2025-09-27 14:51:45.429] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-27 14:51:45.431] [INFO] 扫描枪1串口初始化完成
[2025-09-27 14:51:45.431] [INFO] 扫描枪1初始化完成
[2025-09-27 14:51:45.431] [INFO] 开始初始化扫描枪2...
[2025-09-27 14:51:45.432] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-27 14:51:45.432] [INFO] 扫描枪2串口初始化完成
[2025-09-27 14:51:45.432] [INFO] 扫描枪2初始化完成
[2025-09-27 14:51:45.432] [INFO] 开始初始化扫描枪3...
[2025-09-27 14:51:45.433] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-27 14:51:45.433] [INFO] 扫描枪3串口初始化完成
[2025-09-27 14:51:45.433] [INFO] 扫描枪3初始化完成
[2025-09-27 14:51:45.433] [INFO] MultiScannerManager初始化完成
[2025-09-27 14:51:45.435] [INFO] 开始自动连接并验证所有扫码器...
[2025-09-27 14:51:45.435] [INFO] 正在连接扫码器1...
[2025-09-27 14:51:45.438] [INFO] 扫描枪1状态变更: Connecting - 正在连接扫描枪1...
[2025-09-27 14:51:45.439] [INFO] 扫码器1状态变化: Connecting - 正在连接扫描枪1...
[2025-09-27 14:51:47.175] [ERROR] 连接扫描枪1 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerInstance.<ConnectAsync>b__28_1() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 743
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerInstance.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 737
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:51:47.177] [WARN] 扫码器1连接失败
[2025-09-27 14:51:47.684] [INFO] 正在连接扫码器2...
[2025-09-27 14:51:47.685] [INFO] 扫描枪2状态变更: Connecting - 正在连接扫描枪2...
[2025-09-27 14:51:47.686] [INFO] 扫码器2状态变化: Connecting - 正在连接扫描枪2...
[2025-09-27 14:51:47.690] [INFO] 扫描枪2连接成功: COM2
[2025-09-27 14:51:47.692] [INFO] 扫描枪2状态变更: Connected - 扫描枪2连接成功
[2025-09-27 14:51:47.692] [INFO] 扫码器2状态变化: Connected - 扫描枪2连接成功
[2025-09-27 14:51:47.693] [INFO] 扫码器2连接成功
[2025-09-27 14:51:47.702] [INFO] 扫描枪2发送数据: hello
[2025-09-27 14:51:48.711] [INFO] 扫码器2hello验证完成
[2025-09-27 14:51:48.712] [INFO] 扫码器2通信验证成功
[2025-09-27 14:51:49.227] [INFO] 正在连接扫码器3...
[2025-09-27 14:51:49.227] [INFO] 扫描枪3状态变更: Connecting - 正在连接扫描枪3...
[2025-09-27 14:51:49.227] [INFO] 扫码器3状态变化: Connecting - 正在连接扫描枪3...
[2025-09-27 14:51:52.072] [ERROR] 连接扫描枪3 执行失败
异常详情: 端口“COM3”不存在。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerInstance.<ConnectAsync>b__28_1() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 743
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerInstance.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 737
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:51:52.073] [WARN] 扫码器3连接失败
[2025-09-27 14:51:52.576] [WARN] 部分扫码器连接或验证失败，但系统将继续运行
[2025-09-27 14:51:52.577] [INFO] ScannerAutoModeManager初始化完成
[2025-09-27 14:51:52.581] [INFO] 开始初始化ModbusTcpManager...
[2025-09-27 14:51:52.584] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-27 14:51:52.589] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-27 14:51:57.692] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:51:57.693] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-27 14:51:57.694] [INFO] ModbusTcpManager初始化完成
[2025-09-27 14:51:57.698] [INFO] 开始初始化EpsonRobotManager...
[2025-09-27 14:51:57.699] [INFO] 从配置文件加载Epson机器人1配置成功
[2025-09-27 14:51:57.700] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-27 14:51:57.700] [INFO] EpsonRobotManager初始化完成
[2025-09-27 14:51:57.701] [INFO] 初始化视觉Manager...
[2025-09-27 14:51:57.703] [INFO] 开始初始化VisionManager...
[2025-09-27 14:51:57.704] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-27 14:51:57.705] [INFO] 模拟初始化相机，索引: 0
[2025-09-27 14:51:58.223] [INFO] 相机初始化成功
[2025-09-27 14:51:58.224] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-27 14:51:58.224] [INFO] 视觉配置加载完成
[2025-09-27 14:51:58.225] [INFO] VisionManager初始化完成
[2025-09-27 14:51:58.225] [INFO] 初始化数据Manager...
[2025-09-27 14:51:58.229] [INFO] 开始初始化StatisticsManager...
[2025-09-27 14:51:58.230] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-27 14:51:58.235] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-27 14:51:58.235] [INFO] 历史数据加载完成
[2025-09-27 14:51:58.235] [INFO] 自动保存任务已启动
[2025-09-27 14:51:58.236] [INFO] StatisticsManager初始化完成
[2025-09-27 14:51:58.236] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-27 14:51:58.236] [INFO] 所有Manager初始化完成
[2025-09-27 14:51:58.237] [INFO] 自动保存循环开始
[2025-09-27 14:51:58.384] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 14:51:58.385] [INFO] 主界面布局创建完成
[2025-09-27 14:51:58.390] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-27 14:51:58.486] [INFO] 开始初始化系统
[2025-09-27 14:51:58.488] [INFO] 初始化业务逻辑
[2025-09-27 14:51:58.489] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-27 14:51:58.490] [INFO] IO管理器初始化完成
[2025-09-27 14:51:58.490] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-27 14:51:58.559] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 136
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:51:58.559] [INFO] 电机管理器初始化完成
[2025-09-27 14:51:58.560] [INFO] IO事件订阅完成
[2025-09-27 14:51:58.560] [INFO] 电机事件订阅完成
[2025-09-27 14:51:58.561] [INFO] 业务层交互机制建立完成
[2025-09-27 14:51:58.561] [INFO] 业务逻辑初始化完成
[2025-09-27 14:51:58.562] [INFO] 执行UI界面刷新
[2025-09-27 14:51:58.564] [INFO] UI界面刷新完成
[2025-09-27 14:51:58.565] [INFO] 系统初始化完成
[2025-09-27 14:52:06.222] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 14:52:06.223] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-27 14:52:06.228] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-27 14:52:06.228] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-27 14:52:07.603] [INFO] 翻转电机控制面板资源释放完成
[2025-09-27 14:52:07.610] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-27 14:52:07.614] [INFO] Epson机器人管理器初始化完成
[2025-09-27 14:52:07.629] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-27 14:52:09.400] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 14:52:09.400] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-27 14:52:09.400] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-27 14:52:09.400] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-27 14:52:10.794] [INFO] 翻转电机控制面板资源释放完成
[2025-09-27 14:52:10.797] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 14:52:11.877] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 14:52:11.877] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-27 14:52:11.878] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-27 14:52:11.878] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-27 14:52:12.411] [INFO] 翻转电机控制面板资源释放完成
[2025-09-27 14:52:12.414] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-27 14:52:12.414] [INFO] Epson机器人管理器初始化完成
[2025-09-27 14:52:12.417] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-27 14:52:13.698] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-27 14:52:15.379] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-27 14:52:15.379] [INFO] Epson机器人管理器初始化完成
[2025-09-27 14:52:15.382] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-27 14:52:19.799] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 14:52:19.799] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-27 14:52:19.799] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-27 14:52:19.800] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-27 14:52:26.975] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-27 14:52:26.979] [INFO] 开始按顺序释放各个Manager资源...
[2025-09-27 14:52:26.979] [INFO] 清理UI资源...
[2025-09-27 14:52:26.979] [INFO] UI资源清理完成
[2025-09-27 14:52:26.979] [INFO] 释放工作流管理器资源...
[2025-09-27 14:52:26.982] [INFO] 开始释放WorkflowManager资源...
[2025-09-27 14:52:26.984] [WARN] 皮带电机自动控制未在运行
[2025-09-27 14:52:26.985] [INFO] 工作流事件取消订阅完成
[2025-09-27 14:52:26.986] [INFO] WorkflowManager资源释放完成
[2025-09-27 14:52:26.986] [INFO] 工作流管理器资源释放完成
[2025-09-27 14:52:26.986] [INFO] 释放启动自检管理器资源...
[2025-09-27 14:52:26.988] [INFO] 开始释放启动自检管理器资源...
[2025-09-27 14:52:26.988] [INFO] 启动自检管理器资源释放完成
[2025-09-27 14:52:26.988] [INFO] 启动自检管理器资源释放完成
[2025-09-27 14:52:26.988] [INFO] 释放DMC1000B电机管理器资源...
[2025-09-27 14:52:26.990] [INFO] 开始释放DMC1000B资源...
[2025-09-27 14:52:26.998] [INFO] 停止所有电机部分失败
[2025-09-27 14:52:26.999] [INFO] DMC1000B资源释放完成
[2025-09-27 14:52:27.000] [INFO] DMC1000B电机管理器资源释放完成
[2025-09-27 14:52:27.000] [INFO] 释放DMC1000BIO管理器资源...
[2025-09-27 14:52:27.002] [INFO] DMC1000BIO管理器资源释放完成
[2025-09-27 14:52:27.003] [INFO] 释放其他Manager资源...
[2025-09-27 14:52:27.004] [INFO] 开始释放MotorManager资源...
[2025-09-27 14:52:27.008] [INFO] 电机0停止运动
[2025-09-27 14:52:27.009] [INFO] 电机1停止运动
[2025-09-27 14:52:27.010] [INFO] 电机3停止运动
[2025-09-27 14:52:27.043] [INFO] 电机5停止运动
[2025-09-27 14:52:27.043] [INFO] 电机2停止运动
[2025-09-27 14:52:27.044] [INFO] 电机4停止运动
[2025-09-27 14:52:27.044] [INFO] 电机6停止运动
[2025-09-27 14:52:27.044] [INFO] 电机7停止运动
[2025-09-27 14:52:27.045] [INFO] 所有电机已停止
[2025-09-27 14:52:27.078] [INFO] 电机监控循环被取消
[2025-09-27 14:52:27.078] [INFO] 电机监控循环结束
[2025-09-27 14:52:27.079] [INFO] 电机监控任务已停止
[2025-09-27 14:52:27.079] [INFO] 模拟释放运动控制卡资源
[2025-09-27 14:52:27.079] [INFO] MotorManager资源释放完成
[2025-09-27 14:52:27.081] [INFO] 开始释放ScannerManager资源...
[2025-09-27 14:52:27.082] [INFO] 扫描枪状态变更: Disconnected - 扫描枪连接已断开
[2025-09-27 14:52:27.083] [INFO] ScannerManager资源释放完成
[2025-09-27 14:52:27.084] [INFO] 开始释放ModbusTcpManager资源...
[2025-09-27 14:52:27.087] [INFO] Modbus TCP状态变更: Disconnected - Modbus TCP连接已断开
[2025-09-27 14:52:27.087] [INFO] Modbus TCP连接已断开
[2025-09-27 14:52:27.087] [INFO] ModbusTcpManager资源释放完成
[2025-09-27 14:52:27.089] [INFO] 开始释放EpsonRobotManager资源...
[2025-09-27 14:52:27.093] [INFO] EpsonRobotManager资源释放完成
[2025-09-27 14:52:27.095] [INFO] 开始释放EpsonRobotManager2资源...
[2025-09-27 14:52:27.099] [INFO] EpsonRobotManager2资源释放完成
[2025-09-27 14:52:27.101] [INFO] 开始释放VisionManager资源...
[2025-09-27 14:52:27.103] [INFO] 模拟释放相机资源
[2025-09-27 14:52:27.103] [INFO] VisionManager资源释放完成
[2025-09-27 14:52:27.105] [INFO] 开始释放StatisticsManager资源...
[2025-09-27 14:52:27.143] [INFO] 自动保存循环被取消
[2025-09-27 14:52:27.144] [INFO] 自动保存循环结束
[2025-09-27 14:52:27.144] [INFO] 自动保存任务已停止
[2025-09-27 14:52:27.147] [INFO] StatisticsManager资源释放完成
[2025-09-27 14:52:27.147] [INFO] 其他Manager资源释放完成
[2025-09-27 14:52:27.148] [INFO] 释放DMC1000B控制卡资源...
[2025-09-27 14:52:27.149] [INFO] DMC1000B控制卡未初始化，无需释放
[2025-09-27 14:52:27.149] [INFO] DMC1000B控制卡资源释放完成
[2025-09-27 14:52:27.149] [INFO] 所有Manager资源释放流程完成
[2025-09-27 14:52:27.150] [INFO] 所有资源释放完成，程序即将退出
[2025-09-27 14:52:27.152] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-27 14:52:27.153] [INFO] 翻转电机控制面板资源释放完成
[2025-09-27 14:52:38.366] [INFO] 程序启动开始
[2025-09-27 14:52:38.368] [INFO] 配置系统初始化成功
[2025-09-27 14:52:38.406] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-27 14:52:38.407] [INFO] 配置系统初始化完成
[2025-09-27 14:52:38.407] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-27 14:52:38.415] [INFO] 开始初始化各个Manager...
[2025-09-27 14:52:38.416] [INFO] 初始化DMC1000B控制卡...
[2025-09-27 14:52:38.419] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-27 14:52:38.421] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-27 14:52:38.421] [WARN] DMC1000B控制卡初始化失败，相关功能可能无法正常工作
[2025-09-27 14:52:38.421] [INFO] 初始化基础Manager...
[2025-09-27 14:52:38.426] [INFO] IO状态缓存初始化完成
[2025-09-27 14:52:38.428] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-27 14:52:38.428] [WARN] DMC1000BIO管理器初始化失败
[2025-09-27 14:52:38.431] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-27 14:52:38.432] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 136
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:52:38.447] [WARN] DMC1000B电机管理器初始化失败
[2025-09-27 14:52:38.448] [INFO] 初始化系统模式管理器...
[2025-09-27 14:52:38.457] [INFO] 安全管理器实例已创建
[2025-09-27 14:52:38.460] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-27 14:52:38.461] [INFO] 开始初始化MotorManager...
[2025-09-27 14:52:38.461] [INFO] 当前模式已经是 Manual，无需切换
[2025-09-27 14:52:38.463] [INFO] 模拟初始化运动控制卡
[2025-09-27 14:52:38.676] [INFO] 加载了8个电机的默认配置
[2025-09-27 14:52:38.677] [INFO] 电机监控任务已启动
[2025-09-27 14:52:38.677] [INFO] MotorManager初始化完成
[2025-09-27 14:52:38.677] [INFO] 初始化通信Manager...
[2025-09-27 14:52:38.678] [INFO] 电机监控循环开始
[2025-09-27 14:52:38.679] [INFO] 开始初始化ScannerManager...
[2025-09-27 14:52:38.681] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-27 14:52:38.682] [INFO] 串口初始化完成
[2025-09-27 14:52:38.683] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-27 14:52:38.687] [ERROR] 连接扫描枪 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerManager.<ConnectAsync>b__23_1() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 144
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerManager.<<ConnectAsync>b__23_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 138
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:52:38.689] [WARN] 扫描枪连接失败，但初始化完成，可稍后重试连接
[2025-09-27 14:52:38.689] [INFO] ScannerManager初始化完成
[2025-09-27 14:52:38.695] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-27 14:52:38.696] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-27 14:52:38.696] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-27 14:52:38.697] [INFO] SCARA通信管理器已初始化
[2025-09-27 14:52:38.698] [INFO] ScannerAutoModeManager管理器引用初始化完成
[2025-09-27 14:52:38.700] [INFO] 开始初始化ScannerAutoModeManager...
[2025-09-27 14:52:38.702] [INFO] 开始初始化MultiScannerManager...
[2025-09-27 14:52:38.703] [INFO] 开始初始化扫描枪1...
[2025-09-27 14:52:38.704] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-27 14:52:38.706] [INFO] 扫描枪1串口初始化完成
[2025-09-27 14:52:38.706] [INFO] 扫描枪1初始化完成
[2025-09-27 14:52:38.706] [INFO] 开始初始化扫描枪2...
[2025-09-27 14:52:38.706] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-27 14:52:38.706] [INFO] 扫描枪2串口初始化完成
[2025-09-27 14:52:38.707] [INFO] 扫描枪2初始化完成
[2025-09-27 14:52:38.707] [INFO] 开始初始化扫描枪3...
[2025-09-27 14:52:38.707] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-27 14:52:38.707] [INFO] 扫描枪3串口初始化完成
[2025-09-27 14:52:38.707] [INFO] 扫描枪3初始化完成
[2025-09-27 14:52:38.707] [INFO] MultiScannerManager初始化完成
[2025-09-27 14:52:38.710] [INFO] 开始自动连接并验证所有扫码器...
[2025-09-27 14:52:38.710] [INFO] 正在连接扫码器1...
[2025-09-27 14:52:38.713] [INFO] 扫描枪1状态变更: Connecting - 正在连接扫描枪1...
[2025-09-27 14:52:38.714] [INFO] 扫码器1状态变化: Connecting - 正在连接扫描枪1...
[2025-09-27 14:52:38.715] [ERROR] 连接扫描枪1 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerInstance.<ConnectAsync>b__28_1() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 743
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerInstance.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 737
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:52:38.716] [WARN] 扫码器1连接失败
[2025-09-27 14:52:39.218] [INFO] 正在连接扫码器2...
[2025-09-27 14:52:39.218] [INFO] 扫描枪2状态变更: Connecting - 正在连接扫描枪2...
[2025-09-27 14:52:39.218] [INFO] 扫码器2状态变化: Connecting - 正在连接扫描枪2...
[2025-09-27 14:52:39.221] [INFO] 扫描枪2连接成功: COM2
[2025-09-27 14:52:39.222] [INFO] 扫描枪2状态变更: Connected - 扫描枪2连接成功
[2025-09-27 14:52:39.222] [INFO] 扫码器2状态变化: Connected - 扫描枪2连接成功
[2025-09-27 14:52:39.222] [INFO] 扫码器2连接成功
[2025-09-27 14:52:39.231] [INFO] 扫描枪2发送数据: hello
[2025-09-27 14:52:40.243] [INFO] 扫码器2hello验证完成
[2025-09-27 14:52:40.243] [INFO] 扫码器2通信验证成功
[2025-09-27 14:52:40.758] [INFO] 正在连接扫码器3...
[2025-09-27 14:52:40.759] [INFO] 扫描枪3状态变更: Connecting - 正在连接扫描枪3...
[2025-09-27 14:52:40.760] [INFO] 扫码器3状态变化: Connecting - 正在连接扫描枪3...
[2025-09-27 14:52:40.761] [ERROR] 连接扫描枪3 执行失败
异常详情: 端口“COM3”不存在。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerInstance.<ConnectAsync>b__28_1() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 743
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerInstance.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 737
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:52:40.762] [WARN] 扫码器3连接失败
[2025-09-27 14:52:41.273] [WARN] 部分扫码器连接或验证失败，但系统将继续运行
[2025-09-27 14:52:41.273] [INFO] ScannerAutoModeManager初始化完成
[2025-09-27 14:52:41.282] [INFO] 开始初始化ModbusTcpManager...
[2025-09-27 14:52:41.287] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-27 14:52:41.298] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-27 14:52:46.334] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:52:46.337] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-27 14:52:46.338] [INFO] ModbusTcpManager初始化完成
[2025-09-27 14:52:46.350] [INFO] 开始初始化EpsonRobotManager...
[2025-09-27 14:52:46.352] [INFO] 从配置文件加载Epson机器人1配置成功
[2025-09-27 14:52:46.357] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-27 14:52:46.371] [INFO] EpsonRobotManager初始化完成
[2025-09-27 14:52:46.372] [INFO] 初始化视觉Manager...
[2025-09-27 14:52:46.381] [INFO] 开始初始化VisionManager...
[2025-09-27 14:52:46.382] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-27 14:52:46.382] [INFO] 模拟初始化相机，索引: 0
[2025-09-27 14:52:46.895] [INFO] 相机初始化成功
[2025-09-27 14:52:46.895] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-27 14:52:46.896] [INFO] 视觉配置加载完成
[2025-09-27 14:52:46.896] [INFO] VisionManager初始化完成
[2025-09-27 14:52:46.896] [INFO] 初始化数据Manager...
[2025-09-27 14:52:46.898] [INFO] 开始初始化StatisticsManager...
[2025-09-27 14:52:46.899] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-27 14:52:46.902] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-27 14:52:46.902] [INFO] 历史数据加载完成
[2025-09-27 14:52:46.903] [INFO] 自动保存任务已启动
[2025-09-27 14:52:46.903] [INFO] StatisticsManager初始化完成
[2025-09-27 14:52:46.903] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-27 14:52:46.903] [INFO] 所有Manager初始化完成
[2025-09-27 14:52:46.904] [INFO] 自动保存循环开始
[2025-09-27 14:52:46.950] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 14:52:46.950] [INFO] 主界面布局创建完成
[2025-09-27 14:52:46.951] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-27 14:52:47.007] [INFO] 开始初始化系统
[2025-09-27 14:52:47.008] [INFO] 初始化业务逻辑
[2025-09-27 14:52:47.009] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-27 14:52:47.009] [INFO] IO管理器初始化完成
[2025-09-27 14:52:47.010] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-27 14:52:47.010] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 136
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:52:47.010] [INFO] 电机管理器初始化完成
[2025-09-27 14:52:47.011] [INFO] IO事件订阅完成
[2025-09-27 14:52:47.011] [INFO] 电机事件订阅完成
[2025-09-27 14:52:47.011] [INFO] 业务层交互机制建立完成
[2025-09-27 14:52:47.011] [INFO] 业务逻辑初始化完成
[2025-09-27 14:52:47.012] [INFO] 执行UI界面刷新
[2025-09-27 14:52:47.014] [INFO] UI界面刷新完成
[2025-09-27 14:52:47.014] [INFO] 系统初始化完成
[2025-09-27 14:52:58.492] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 14:52:58.492] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-27 14:52:58.496] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-27 14:52:58.496] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-27 14:53:01.416] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 起始速度必须小于最大速度
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass48_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 195
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:53:01.417] [INFO] 左翻转电机参数已更新: 脉冲当量=0.012, 起始速度=5, 最大速度=4, 最大加速度=120, 加速时间=0.1
[2025-09-27 14:53:01.613] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass48_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 195
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:53:01.614] [INFO] 左翻转电机参数已更新: 脉冲当量=0.012, 起始速度=5, 最大速度=40, 最大加速度=120, 加速时间=0.1
[2025-09-27 14:53:03.594] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 起始速度必须小于最大速度
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass48_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 195
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:53:03.595] [INFO] 右翻转电机参数已更新: 脉冲当量=0.012, 起始速度=5, 最大速度=3, 最大加速度=120, 加速时间=0.1
[2025-09-27 14:53:03.773] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass48_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 195
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:53:03.774] [INFO] 右翻转电机参数已更新: 脉冲当量=0.012, 起始速度=5, 最大速度=33, 最大加速度=120, 加速时间=0.1
[2025-09-27 14:53:30.687] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-27 14:53:30.690] [INFO] 开始按顺序释放各个Manager资源...
[2025-09-27 14:53:30.690] [INFO] 清理UI资源...
[2025-09-27 14:53:30.691] [INFO] UI资源清理完成
[2025-09-27 14:53:30.691] [INFO] 释放工作流管理器资源...
[2025-09-27 14:53:30.693] [INFO] 开始释放WorkflowManager资源...
[2025-09-27 14:53:30.694] [WARN] 皮带电机自动控制未在运行
[2025-09-27 14:53:30.695] [INFO] 工作流事件取消订阅完成
[2025-09-27 14:53:30.695] [INFO] WorkflowManager资源释放完成
[2025-09-27 14:53:30.695] [INFO] 工作流管理器资源释放完成
[2025-09-27 14:53:30.695] [INFO] 释放启动自检管理器资源...
[2025-09-27 14:53:30.697] [INFO] 开始释放启动自检管理器资源...
[2025-09-27 14:53:30.697] [INFO] 启动自检管理器资源释放完成
[2025-09-27 14:53:30.698] [INFO] 启动自检管理器资源释放完成
[2025-09-27 14:53:30.698] [INFO] 释放DMC1000B电机管理器资源...
[2025-09-27 14:53:30.699] [INFO] 开始释放DMC1000B资源...
[2025-09-27 14:53:30.705] [INFO] 停止所有电机部分失败
[2025-09-27 14:53:30.706] [INFO] DMC1000B资源释放完成
[2025-09-27 14:53:30.706] [INFO] DMC1000B电机管理器资源释放完成
[2025-09-27 14:53:30.706] [INFO] 释放DMC1000BIO管理器资源...
[2025-09-27 14:53:30.708] [INFO] DMC1000BIO管理器资源释放完成
[2025-09-27 14:53:30.709] [INFO] 释放其他Manager资源...
[2025-09-27 14:53:30.710] [INFO] 开始释放MotorManager资源...
[2025-09-27 14:53:30.714] [INFO] 电机0停止运动
[2025-09-27 14:53:30.714] [INFO] 电机6停止运动
[2025-09-27 14:53:30.714] [INFO] 电机2停止运动
[2025-09-27 14:53:30.714] [INFO] 电机4停止运动
[2025-09-27 14:53:30.714] [INFO] 电机1停止运动
[2025-09-27 14:53:30.715] [INFO] 电机7停止运动
[2025-09-27 14:53:30.715] [INFO] 电机5停止运动
[2025-09-27 14:53:30.715] [INFO] 电机3停止运动
[2025-09-27 14:53:30.715] [INFO] 所有电机已停止
[2025-09-27 14:53:30.716] [INFO] 电机监控循环被取消
[2025-09-27 14:53:30.716] [INFO] 电机监控循环结束
[2025-09-27 14:53:30.716] [INFO] 电机监控任务已停止
[2025-09-27 14:53:30.717] [INFO] 模拟释放运动控制卡资源
[2025-09-27 14:53:30.717] [INFO] MotorManager资源释放完成
[2025-09-27 14:53:30.718] [INFO] 开始释放ScannerManager资源...
[2025-09-27 14:53:30.720] [INFO] 扫描枪状态变更: Disconnected - 扫描枪连接已断开
[2025-09-27 14:53:30.720] [INFO] ScannerManager资源释放完成
[2025-09-27 14:53:30.721] [INFO] 开始释放ModbusTcpManager资源...
[2025-09-27 14:53:30.723] [INFO] Modbus TCP状态变更: Disconnected - Modbus TCP连接已断开
[2025-09-27 14:53:30.723] [INFO] Modbus TCP连接已断开
[2025-09-27 14:53:30.723] [INFO] ModbusTcpManager资源释放完成
[2025-09-27 14:53:30.724] [INFO] 开始释放EpsonRobotManager资源...
[2025-09-27 14:53:30.728] [INFO] EpsonRobotManager资源释放完成
[2025-09-27 14:53:30.730] [INFO] 开始释放EpsonRobotManager2资源...
[2025-09-27 14:53:30.734] [INFO] EpsonRobotManager2资源释放完成
[2025-09-27 14:53:30.735] [INFO] 开始释放VisionManager资源...
[2025-09-27 14:53:30.737] [INFO] 模拟释放相机资源
[2025-09-27 14:53:30.737] [INFO] VisionManager资源释放完成
[2025-09-27 14:53:30.738] [INFO] 开始释放StatisticsManager资源...
[2025-09-27 14:53:30.743] [INFO] 自动保存循环被取消
[2025-09-27 14:53:30.743] [INFO] 自动保存循环结束
[2025-09-27 14:53:30.743] [INFO] 自动保存任务已停止
[2025-09-27 14:53:30.746] [INFO] StatisticsManager资源释放完成
[2025-09-27 14:53:30.747] [INFO] 其他Manager资源释放完成
[2025-09-27 14:53:30.747] [INFO] 释放DMC1000B控制卡资源...
[2025-09-27 14:53:30.748] [INFO] DMC1000B控制卡未初始化，无需释放
[2025-09-27 14:53:30.748] [INFO] DMC1000B控制卡资源释放完成
[2025-09-27 14:53:30.748] [INFO] 所有Manager资源释放流程完成
[2025-09-27 14:53:30.749] [INFO] 所有资源释放完成，程序即将退出
[2025-09-27 14:53:30.749] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-27 14:53:30.750] [INFO] 翻转电机控制面板资源释放完成
[2025-09-27 14:55:23.354] [INFO] 程序启动开始
[2025-09-27 14:55:23.356] [INFO] 配置系统初始化成功
[2025-09-27 14:55:23.394] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-27 14:55:23.394] [INFO] 配置系统初始化完成
[2025-09-27 14:55:23.394] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-27 14:55:23.402] [INFO] 开始初始化各个Manager...
[2025-09-27 14:55:23.402] [INFO] 初始化DMC1000B控制卡...
[2025-09-27 14:55:23.406] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-27 14:55:23.407] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-27 14:55:23.408] [WARN] DMC1000B控制卡初始化失败，相关功能可能无法正常工作
[2025-09-27 14:55:23.408] [INFO] 初始化基础Manager...
[2025-09-27 14:55:23.412] [INFO] IO状态缓存初始化完成
[2025-09-27 14:55:23.414] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-27 14:55:23.414] [WARN] DMC1000BIO管理器初始化失败
[2025-09-27 14:55:23.418] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-27 14:55:23.418] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 136
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:55:23.433] [WARN] DMC1000B电机管理器初始化失败
[2025-09-27 14:55:23.433] [INFO] 初始化系统模式管理器...
[2025-09-27 14:55:23.442] [INFO] 安全管理器实例已创建
[2025-09-27 14:55:23.445] [INFO] 开始初始化MotorManager...
[2025-09-27 14:55:23.446] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-27 14:55:23.446] [INFO] 当前模式已经是 Manual，无需切换
[2025-09-27 14:55:23.447] [INFO] 模拟初始化运动控制卡
[2025-09-27 14:55:23.665] [INFO] 加载了8个电机的默认配置
[2025-09-27 14:55:23.666] [INFO] 电机监控任务已启动
[2025-09-27 14:55:23.667] [INFO] MotorManager初始化完成
[2025-09-27 14:55:23.667] [INFO] 初始化通信Manager...
[2025-09-27 14:55:23.669] [INFO] 电机监控循环开始
[2025-09-27 14:55:23.672] [INFO] 开始初始化ScannerManager...
[2025-09-27 14:55:23.675] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-27 14:55:23.678] [INFO] 串口初始化完成
[2025-09-27 14:55:23.681] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-27 14:55:23.687] [ERROR] 连接扫描枪 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerManager.<ConnectAsync>b__23_1() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 144
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerManager.<<ConnectAsync>b__23_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 138
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:55:23.690] [WARN] 扫描枪连接失败，但初始化完成，可稍后重试连接
[2025-09-27 14:55:23.690] [INFO] ScannerManager初始化完成
[2025-09-27 14:55:23.698] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-27 14:55:23.698] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-27 14:55:23.699] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-27 14:55:23.701] [INFO] SCARA通信管理器已初始化
[2025-09-27 14:55:23.703] [INFO] ScannerAutoModeManager管理器引用初始化完成
[2025-09-27 14:55:23.705] [INFO] 开始初始化ScannerAutoModeManager...
[2025-09-27 14:55:23.707] [INFO] 开始初始化MultiScannerManager...
[2025-09-27 14:55:23.708] [INFO] 开始初始化扫描枪1...
[2025-09-27 14:55:23.709] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-27 14:55:23.710] [INFO] 扫描枪1串口初始化完成
[2025-09-27 14:55:23.710] [INFO] 扫描枪1初始化完成
[2025-09-27 14:55:23.711] [INFO] 开始初始化扫描枪2...
[2025-09-27 14:55:23.711] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-27 14:55:23.711] [INFO] 扫描枪2串口初始化完成
[2025-09-27 14:55:23.711] [INFO] 扫描枪2初始化完成
[2025-09-27 14:55:23.712] [INFO] 开始初始化扫描枪3...
[2025-09-27 14:55:23.712] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-27 14:55:23.712] [INFO] 扫描枪3串口初始化完成
[2025-09-27 14:55:23.712] [INFO] 扫描枪3初始化完成
[2025-09-27 14:55:23.712] [INFO] MultiScannerManager初始化完成
[2025-09-27 14:55:23.714] [INFO] 开始自动连接并验证所有扫码器...
[2025-09-27 14:55:23.714] [INFO] 正在连接扫码器1...
[2025-09-27 14:55:23.717] [INFO] 扫描枪1状态变更: Connecting - 正在连接扫描枪1...
[2025-09-27 14:55:23.718] [INFO] 扫码器1状态变化: Connecting - 正在连接扫描枪1...
[2025-09-27 14:55:23.719] [ERROR] 连接扫描枪1 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerInstance.<ConnectAsync>b__28_1() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 743
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerInstance.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 737
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:55:23.719] [WARN] 扫码器1连接失败
[2025-09-27 14:55:24.224] [INFO] 正在连接扫码器2...
[2025-09-27 14:55:24.225] [INFO] 扫描枪2状态变更: Connecting - 正在连接扫描枪2...
[2025-09-27 14:55:24.225] [INFO] 扫码器2状态变化: Connecting - 正在连接扫描枪2...
[2025-09-27 14:55:24.230] [INFO] 扫描枪2连接成功: COM2
[2025-09-27 14:55:24.230] [INFO] 扫描枪2状态变更: Connected - 扫描枪2连接成功
[2025-09-27 14:55:24.231] [INFO] 扫码器2状态变化: Connected - 扫描枪2连接成功
[2025-09-27 14:55:24.232] [INFO] 扫码器2连接成功
[2025-09-27 14:55:24.244] [INFO] 扫描枪2发送数据: hello
[2025-09-27 14:55:25.266] [INFO] 扫码器2hello验证完成
[2025-09-27 14:55:25.267] [INFO] 扫码器2通信验证成功
[2025-09-27 14:55:25.780] [INFO] 正在连接扫码器3...
[2025-09-27 14:55:25.781] [INFO] 扫描枪3状态变更: Connecting - 正在连接扫描枪3...
[2025-09-27 14:55:25.782] [INFO] 扫码器3状态变化: Connecting - 正在连接扫描枪3...
[2025-09-27 14:55:25.783] [ERROR] 连接扫描枪3 执行失败
异常详情: 端口“COM3”不存在。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerInstance.<ConnectAsync>b__28_1() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 743
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerInstance.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 737
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:55:25.785] [WARN] 扫码器3连接失败
[2025-09-27 14:55:26.295] [WARN] 部分扫码器连接或验证失败，但系统将继续运行
[2025-09-27 14:55:26.295] [INFO] ScannerAutoModeManager初始化完成
[2025-09-27 14:55:26.304] [INFO] 开始初始化ModbusTcpManager...
[2025-09-27 14:55:26.310] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-27 14:55:26.320] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-27 14:55:31.343] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:55:31.345] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-27 14:55:31.346] [INFO] ModbusTcpManager初始化完成
[2025-09-27 14:55:31.358] [INFO] 开始初始化EpsonRobotManager...
[2025-09-27 14:55:31.359] [INFO] 从配置文件加载Epson机器人1配置成功
[2025-09-27 14:55:31.365] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-27 14:55:31.378] [INFO] EpsonRobotManager初始化完成
[2025-09-27 14:55:31.379] [INFO] 初始化视觉Manager...
[2025-09-27 14:55:31.385] [INFO] 开始初始化VisionManager...
[2025-09-27 14:55:31.386] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-27 14:55:31.388] [INFO] 模拟初始化相机，索引: 0
[2025-09-27 14:55:31.902] [INFO] 相机初始化成功
[2025-09-27 14:55:31.903] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-27 14:55:31.903] [INFO] 视觉配置加载完成
[2025-09-27 14:55:31.904] [INFO] VisionManager初始化完成
[2025-09-27 14:55:31.904] [INFO] 初始化数据Manager...
[2025-09-27 14:55:31.906] [INFO] 开始初始化StatisticsManager...
[2025-09-27 14:55:31.907] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-27 14:55:31.909] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-27 14:55:31.909] [INFO] 历史数据加载完成
[2025-09-27 14:55:31.910] [INFO] 自动保存任务已启动
[2025-09-27 14:55:31.910] [INFO] StatisticsManager初始化完成
[2025-09-27 14:55:31.910] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-27 14:55:31.910] [INFO] 所有Manager初始化完成
[2025-09-27 14:55:31.911] [INFO] 自动保存循环开始
[2025-09-27 14:55:31.941] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 14:55:31.941] [INFO] 主界面布局创建完成
[2025-09-27 14:55:31.943] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-27 14:55:32.024] [INFO] 开始初始化系统
[2025-09-27 14:55:32.025] [INFO] 初始化业务逻辑
[2025-09-27 14:55:32.026] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-27 14:55:32.027] [INFO] IO管理器初始化完成
[2025-09-27 14:55:32.027] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-27 14:55:32.027] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 136
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 14:55:32.028] [INFO] 电机管理器初始化完成
[2025-09-27 14:55:32.028] [INFO] IO事件订阅完成
[2025-09-27 14:55:32.028] [INFO] 电机事件订阅完成
[2025-09-27 14:55:32.029] [INFO] 业务层交互机制建立完成
[2025-09-27 14:55:32.029] [INFO] 业务逻辑初始化完成
[2025-09-27 14:55:32.030] [INFO] 执行UI界面刷新
[2025-09-27 14:55:32.033] [INFO] UI界面刷新完成
[2025-09-27 14:55:32.033] [INFO] 系统初始化完成
[2025-09-27 14:55:33.248] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 14:55:33.248] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-27 14:55:33.252] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-27 14:55:33.253] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-27 14:55:34.611] [INFO] 翻转电机控制面板资源释放完成
[2025-09-27 14:55:34.616] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-27 14:55:34.616] [INFO] Epson机器人管理器初始化完成
[2025-09-27 14:55:34.630] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-27 14:55:38.943] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-27 14:55:38.946] [INFO] 开始按顺序释放各个Manager资源...
[2025-09-27 14:55:38.947] [INFO] 清理UI资源...
[2025-09-27 14:55:38.947] [INFO] UI资源清理完成
[2025-09-27 14:55:38.947] [INFO] 释放工作流管理器资源...
[2025-09-27 14:55:38.949] [INFO] 开始释放WorkflowManager资源...
[2025-09-27 14:55:38.950] [WARN] 皮带电机自动控制未在运行
[2025-09-27 14:55:38.951] [INFO] 工作流事件取消订阅完成
[2025-09-27 14:55:38.951] [INFO] WorkflowManager资源释放完成
[2025-09-27 14:55:38.952] [INFO] 工作流管理器资源释放完成
[2025-09-27 14:55:38.952] [INFO] 释放启动自检管理器资源...
[2025-09-27 14:55:38.953] [INFO] 开始释放启动自检管理器资源...
[2025-09-27 14:55:38.953] [INFO] 启动自检管理器资源释放完成
[2025-09-27 14:55:38.953] [INFO] 启动自检管理器资源释放完成
[2025-09-27 14:55:38.953] [INFO] 释放DMC1000B电机管理器资源...
[2025-09-27 14:55:38.954] [INFO] 开始释放DMC1000B资源...
[2025-09-27 14:55:38.961] [INFO] 停止所有电机部分失败
[2025-09-27 14:55:38.962] [INFO] DMC1000B资源释放完成
[2025-09-27 14:55:38.962] [INFO] DMC1000B电机管理器资源释放完成
[2025-09-27 14:55:38.962] [INFO] 释放DMC1000BIO管理器资源...
[2025-09-27 14:55:38.964] [INFO] DMC1000BIO管理器资源释放完成
[2025-09-27 14:55:38.964] [INFO] 释放其他Manager资源...
[2025-09-27 14:55:38.966] [INFO] 开始释放MotorManager资源...
[2025-09-27 14:55:38.969] [INFO] 电机7停止运动
[2025-09-27 14:55:38.969] [INFO] 电机6停止运动
[2025-09-27 14:55:38.970] [INFO] 电机3停止运动
[2025-09-27 14:55:38.970] [INFO] 电机0停止运动
[2025-09-27 14:55:38.970] [INFO] 电机4停止运动
[2025-09-27 14:55:38.970] [INFO] 电机5停止运动
[2025-09-27 14:55:38.971] [INFO] 电机2停止运动
[2025-09-27 14:55:38.971] [INFO] 电机1停止运动
[2025-09-27 14:55:38.971] [INFO] 所有电机已停止
[2025-09-27 14:55:38.972] [INFO] 电机监控循环被取消
[2025-09-27 14:55:38.972] [INFO] 电机监控循环结束
[2025-09-27 14:55:38.972] [INFO] 电机监控任务已停止
[2025-09-27 14:55:38.972] [INFO] 模拟释放运动控制卡资源
[2025-09-27 14:55:38.973] [INFO] MotorManager资源释放完成
[2025-09-27 14:55:38.975] [INFO] 开始释放ScannerManager资源...
[2025-09-27 14:55:38.976] [INFO] 扫描枪状态变更: Disconnected - 扫描枪连接已断开
[2025-09-27 14:55:38.976] [INFO] ScannerManager资源释放完成
[2025-09-27 14:55:38.977] [INFO] 开始释放ModbusTcpManager资源...
[2025-09-27 14:55:38.979] [INFO] Modbus TCP状态变更: Disconnected - Modbus TCP连接已断开
[2025-09-27 14:55:38.979] [INFO] Modbus TCP连接已断开
[2025-09-27 14:55:38.979] [INFO] ModbusTcpManager资源释放完成
[2025-09-27 14:55:38.981] [INFO] 开始释放EpsonRobotManager资源...
[2025-09-27 14:55:38.984] [INFO] EpsonRobotManager资源释放完成
[2025-09-27 14:55:38.986] [INFO] 开始释放EpsonRobotManager2资源...
[2025-09-27 14:55:38.990] [INFO] EpsonRobotManager2资源释放完成
[2025-09-27 14:55:38.991] [INFO] 开始释放VisionManager资源...
[2025-09-27 14:55:38.993] [INFO] 模拟释放相机资源
[2025-09-27 14:55:38.993] [INFO] VisionManager资源释放完成
[2025-09-27 14:55:38.994] [INFO] 开始释放StatisticsManager资源...
[2025-09-27 14:55:38.995] [INFO] 自动保存循环被取消
[2025-09-27 14:55:38.995] [INFO] 自动保存循环结束
[2025-09-27 14:55:38.995] [INFO] 自动保存任务已停止
[2025-09-27 14:55:38.998] [INFO] StatisticsManager资源释放完成
[2025-09-27 14:55:38.998] [INFO] 其他Manager资源释放完成
[2025-09-27 14:55:38.998] [INFO] 释放DMC1000B控制卡资源...
[2025-09-27 14:55:39.000] [INFO] DMC1000B控制卡未初始化，无需释放
[2025-09-27 14:55:39.000] [INFO] DMC1000B控制卡资源释放完成
[2025-09-27 14:55:39.000] [INFO] 所有Manager资源释放流程完成
[2025-09-27 14:55:39.000] [INFO] 所有资源释放完成，程序即将退出
[2025-09-27 14:55:39.001] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-27 18:02:56.539] [INFO] 程序启动开始
[2025-09-27 18:02:56.540] [INFO] 加载Settings系统配置...
[2025-09-27 18:02:56.544] [INFO] 设置文件不存在，使用默认设置: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 18:02:56.546] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 18:02:56.547] [INFO] Settings系统配置加载完成
[2025-09-27 18:02:56.547] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-27 18:02:56.579] [INFO] 开始初始化各个Manager...
[2025-09-27 18:02:56.579] [INFO] 初始化DMC1000B控制卡...
[2025-09-27 18:02:56.583] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-27 18:02:56.584] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-27 18:02:56.584] [WARN] DMC1000B控制卡初始化失败，相关功能可能无法正常工作
[2025-09-27 18:02:56.585] [INFO] 初始化基础Manager...
[2025-09-27 18:02:56.590] [INFO] IO状态缓存初始化完成
[2025-09-27 18:02:56.593] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-27 18:02:56.593] [WARN] DMC1000BIO管理器初始化失败
[2025-09-27 18:02:56.597] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-27 18:02:56.642] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:02:56.659] [WARN] DMC1000B电机管理器初始化失败
[2025-09-27 18:02:56.659] [INFO] 初始化系统模式管理器...
[2025-09-27 18:02:56.671] [INFO] 从Settings系统加载安全管理器配置成功
[2025-09-27 18:02:56.674] [INFO] 安全管理器实例已创建
[2025-09-27 18:02:56.678] [INFO] 开始初始化MotorManager...
[2025-09-27 18:02:56.678] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-27 18:02:56.679] [INFO] 当前模式已经是 Manual，无需切换
[2025-09-27 18:02:56.680] [INFO] 模拟初始化运动控制卡
[2025-09-27 18:02:56.887] [INFO] 加载了8个电机的默认配置
[2025-09-27 18:02:56.889] [INFO] 电机监控任务已启动
[2025-09-27 18:02:56.890] [INFO] MotorManager初始化完成
[2025-09-27 18:02:56.890] [INFO] 初始化通信Manager...
[2025-09-27 18:02:56.893] [INFO] 开始初始化ScannerManager...
[2025-09-27 18:02:56.895] [INFO] 电机监控循环开始
[2025-09-27 18:02:56.895] [INFO] 扫描枪配置从Settings系统加载: COM1, 9600, 8, One, None
[2025-09-27 18:02:56.898] [INFO] 串口初始化完成
[2025-09-27 18:02:56.900] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-27 18:03:05.064] [ERROR] 连接扫描枪 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerManager.<ConnectAsync>b__23_1() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 145
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerManager.<<ConnectAsync>b__23_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 139
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:03:05.067] [WARN] 扫描枪连接失败，但初始化完成，可稍后重试连接
[2025-09-27 18:03:05.067] [INFO] ScannerManager初始化完成
[2025-09-27 18:03:05.074] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-27 18:03:05.074] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-27 18:03:05.075] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-27 18:03:05.076] [INFO] SCARA通信管理器已初始化
[2025-09-27 18:03:05.079] [INFO] ScannerAutoModeManager管理器引用初始化完成
[2025-09-27 18:03:05.082] [INFO] 开始初始化ScannerAutoModeManager...
[2025-09-27 18:03:05.086] [INFO] 开始初始化MultiScannerManager...
[2025-09-27 18:03:05.088] [INFO] 开始初始化扫描枪1...
[2025-09-27 18:03:05.089] [INFO] 从Settings系统加载扫描枪1配置: COM1, 115200
[2025-09-27 18:03:05.091] [INFO] 扫描枪1串口初始化完成
[2025-09-27 18:03:05.091] [INFO] 扫描枪1初始化完成
[2025-09-27 18:03:05.092] [INFO] 开始初始化扫描枪2...
[2025-09-27 18:03:05.092] [INFO] 从Settings系统加载扫描枪2配置: COM2, 115200
[2025-09-27 18:03:05.092] [INFO] 扫描枪2串口初始化完成
[2025-09-27 18:03:05.092] [INFO] 扫描枪2初始化完成
[2025-09-27 18:03:05.093] [INFO] 开始初始化扫描枪3...
[2025-09-27 18:03:05.093] [INFO] 从Settings系统加载扫描枪3配置: COM3, 115200
[2025-09-27 18:03:05.093] [INFO] 扫描枪3串口初始化完成
[2025-09-27 18:03:05.093] [INFO] 扫描枪3初始化完成
[2025-09-27 18:03:05.094] [INFO] MultiScannerManager初始化完成
[2025-09-27 18:03:05.096] [INFO] 开始自动连接并验证所有扫码器...
[2025-09-27 18:03:05.096] [INFO] 正在连接扫码器1...
[2025-09-27 18:03:05.099] [INFO] 扫描枪1状态变更: Connecting - 正在连接扫描枪1...
[2025-09-27 18:03:05.101] [INFO] 扫码器1状态变化: Connecting - 正在连接扫描枪1...
[2025-09-27 18:03:06.822] [ERROR] 连接扫描枪1 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerInstance.<ConnectAsync>b__28_1() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 767
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerInstance.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 761
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:03:06.823] [WARN] 扫码器1连接失败
[2025-09-27 18:03:07.327] [INFO] 正在连接扫码器2...
[2025-09-27 18:03:07.327] [INFO] 扫描枪2状态变更: Connecting - 正在连接扫描枪2...
[2025-09-27 18:03:07.327] [INFO] 扫码器2状态变化: Connecting - 正在连接扫描枪2...
[2025-09-27 18:03:07.329] [INFO] 扫描枪2连接成功: COM2
[2025-09-27 18:03:07.330] [INFO] 扫描枪2状态变更: Connected - 扫描枪2连接成功
[2025-09-27 18:03:07.330] [INFO] 扫码器2状态变化: Connected - 扫描枪2连接成功
[2025-09-27 18:03:07.331] [INFO] 扫码器2连接成功
[2025-09-27 18:03:07.336] [INFO] 扫描枪2发送数据: hello
[2025-09-27 18:03:08.353] [INFO] 扫码器2hello验证完成
[2025-09-27 18:03:08.354] [INFO] 扫码器2通信验证成功
[2025-09-27 18:03:08.868] [INFO] 正在连接扫码器3...
[2025-09-27 18:03:08.868] [INFO] 扫描枪3状态变更: Connecting - 正在连接扫描枪3...
[2025-09-27 18:03:08.868] [INFO] 扫码器3状态变化: Connecting - 正在连接扫描枪3...
[2025-09-27 18:03:14.255] [ERROR] 连接扫描枪3 执行失败
异常详情: 端口“COM3”不存在。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerInstance.<ConnectAsync>b__28_1() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 767
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerInstance.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 761
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:03:14.256] [WARN] 扫码器3连接失败
[2025-09-27 18:03:14.765] [WARN] 部分扫码器连接或验证失败，但系统将继续运行
[2025-09-27 18:03:14.765] [INFO] ScannerAutoModeManager初始化完成
[2025-09-27 18:03:14.768] [INFO] 开始初始化ModbusTcpManager...
[2025-09-27 18:03:14.769] [INFO] Modbus TCP配置: *************:502, SlaveId: 1
[2025-09-27 18:03:14.772] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-27 18:03:19.814] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: *************:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 153
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:03:19.816] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-27 18:03:19.816] [INFO] ModbusTcpManager初始化完成
[2025-09-27 18:03:19.820] [INFO] 开始初始化EpsonRobotManager...
[2025-09-27 18:03:19.822] [INFO] 从Settings系统加载Epson机器人1配置成功
[2025-09-27 18:03:19.822] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-27 18:03:19.822] [INFO] EpsonRobotManager初始化完成
[2025-09-27 18:03:19.822] [INFO] 初始化视觉Manager...
[2025-09-27 18:03:19.825] [INFO] 开始初始化VisionManager...
[2025-09-27 18:03:19.826] [INFO] 视觉系统配置从Settings系统加载: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms, 置信度阈值=0.8
[2025-09-27 18:03:19.827] [INFO] 模拟初始化相机，索引: 0
[2025-09-27 18:03:20.343] [INFO] 相机初始化成功
[2025-09-27 18:03:20.344] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-27 18:03:20.345] [INFO] 视觉配置加载完成
[2025-09-27 18:03:20.345] [INFO] VisionManager初始化完成
[2025-09-27 18:03:20.345] [INFO] 初始化数据Manager...
[2025-09-27 18:03:20.348] [INFO] 开始初始化StatisticsManager...
[2025-09-27 18:03:20.348] [INFO] 统计管理器配置: 数据目录=Export, 缓存大小=1000, 自动保存间隔=24分钟
[2025-09-27 18:03:20.350] [INFO] 创建数据目录: Export
[2025-09-27 18:03:20.496] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-27 18:03:20.497] [INFO] 历史数据加载完成
[2025-09-27 18:03:20.497] [INFO] StatisticsManager初始化完成
[2025-09-27 18:03:20.497] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-27 18:03:20.497] [INFO] 所有Manager初始化完成
[2025-09-27 18:03:20.560] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 18:03:20.560] [INFO] 主界面布局创建完成
[2025-09-27 18:03:20.561] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-27 18:03:20.649] [INFO] 开始初始化系统
[2025-09-27 18:03:20.650] [INFO] 初始化业务逻辑
[2025-09-27 18:03:20.652] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-27 18:03:20.652] [INFO] IO管理器初始化完成
[2025-09-27 18:03:20.652] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-27 18:03:20.732] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:03:20.733] [INFO] 电机管理器初始化完成
[2025-09-27 18:03:20.734] [INFO] IO事件订阅完成
[2025-09-27 18:03:20.734] [INFO] 电机事件订阅完成
[2025-09-27 18:03:20.734] [INFO] 业务层交互机制建立完成
[2025-09-27 18:03:20.734] [INFO] 业务逻辑初始化完成
[2025-09-27 18:03:20.736] [INFO] 执行UI界面刷新
[2025-09-27 18:03:20.739] [INFO] UI界面刷新完成
[2025-09-27 18:03:20.739] [INFO] 系统初始化完成
[2025-09-27 18:03:22.390] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-27 18:03:22.390] [INFO] Epson机器人管理器初始化完成
[2025-09-27 18:03:22.436] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-27 18:03:23.092] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 18:03:23.093] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-27 18:03:23.099] [INFO] UI参数显示已从Settings系统更新
[2025-09-27 18:03:23.099] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-27 18:03:23.100] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-27 18:03:24.291] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 18:03:24.381] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass48_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 196
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:03:24.382] [INFO] 左翻转电机参数已更新并保存: 脉冲当量=0.012, 起始速度=10, 最大速度=60, 最大加速度=120, 加速时间=0.1
[2025-09-27 18:03:25.965] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 18:03:26.035] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 起始速度必须小于最大速度
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass48_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 196
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:03:26.036] [INFO] 左翻转电机参数已更新并保存: 脉冲当量=0.012, 起始速度=10, 最大速度=2, 最大加速度=120, 加速时间=0.1
[2025-09-27 18:03:26.150] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 18:03:26.221] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass48_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 196
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:03:26.222] [INFO] 左翻转电机参数已更新并保存: 脉冲当量=0.012, 起始速度=10, 最大速度=22, 最大加速度=120, 加速时间=0.1
[2025-09-27 18:03:27.174] [INFO] 翻转电机控制面板资源释放完成
[2025-09-27 18:03:27.178] [INFO] 翻转电机示教面板业务逻辑初始化完成
[2025-09-27 18:03:27.185] [INFO] 翻转电机示教面板初始化完成 - 按HTML原型设计
[2025-09-27 18:03:27.917] [INFO] 翻转电机示教面板资源释放完成
[2025-09-27 18:03:27.922] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 18:03:27.922] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-27 18:03:27.922] [INFO] UI参数显示已从Settings系统更新
[2025-09-27 18:03:27.923] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-27 18:03:27.923] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-27 18:03:29.149] [INFO] 翻转电机控制面板资源释放完成
[2025-09-27 18:03:29.152] [INFO] 翻转电机示教面板业务逻辑初始化完成
[2025-09-27 18:03:29.158] [INFO] 翻转电机示教面板初始化完成 - 按HTML原型设计
[2025-09-27 18:03:30.485] [INFO] 翻转电机示教面板资源释放完成
[2025-09-27 18:03:30.497] [INFO] 皮带电机控制面板初始化完成 - 双电机设计
[2025-09-27 18:03:30.498] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-27 18:03:30.505] [INFO] 皮带电机轴3参数设置成功: 脉冲当量=0.01mm/pulse, 最大速度=100mm/s
[2025-09-27 18:03:30.505] [INFO] 皮带电机轴2参数设置成功: 脉冲当量=0.01mm/pulse, 最大速度=100mm/s
[2025-09-27 18:03:30.509] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 18:03:30.509] [INFO] 皮带电机配置保存到Settings系统成功
[2025-09-27 18:03:30.510] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 18:03:30.510] [INFO] 皮带电机配置保存到Settings系统成功
[2025-09-27 18:03:30.511] [INFO] 电机参数显示刷新完成
[2025-09-27 18:03:30.511] [INFO] 皮带电机参数初始化完成
[2025-09-27 18:03:30.511] [INFO] 皮带电机控制面板业务逻辑初始化完成
[2025-09-27 18:03:30.958] [INFO] 皮带电机控制面板资源释放完成
[2025-09-27 18:03:30.962] [INFO] 翻转电机示教面板业务逻辑初始化完成
[2025-09-27 18:03:30.963] [INFO] 翻转电机示教面板初始化完成 - 按HTML原型设计
[2025-09-27 18:03:31.637] [INFO] 翻转电机示教面板资源释放完成
[2025-09-27 18:03:31.642] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 18:03:31.642] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-27 18:03:31.643] [INFO] UI参数显示已从Settings系统更新
[2025-09-27 18:03:31.643] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-27 18:03:31.643] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-27 18:03:37.118] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-27 18:03:37.119] [INFO] 保存Settings系统配置...
[2025-09-27 18:03:37.120] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 18:03:37.121] [INFO] Settings系统配置保存完成
[2025-09-27 18:03:37.123] [INFO] 开始按顺序释放各个Manager资源...
[2025-09-27 18:03:37.124] [INFO] 清理UI资源...
[2025-09-27 18:03:37.124] [INFO] UI资源清理完成
[2025-09-27 18:03:37.124] [INFO] 释放工作流管理器资源...
[2025-09-27 18:03:37.126] [INFO] 开始释放WorkflowManager资源...
[2025-09-27 18:03:37.128] [WARN] 皮带电机自动控制未在运行
[2025-09-27 18:03:37.129] [INFO] 工作流事件取消订阅完成
[2025-09-27 18:03:37.130] [INFO] WorkflowManager资源释放完成
[2025-09-27 18:03:37.130] [INFO] 工作流管理器资源释放完成
[2025-09-27 18:03:37.130] [INFO] 释放启动自检管理器资源...
[2025-09-27 18:03:37.131] [INFO] 开始释放启动自检管理器资源...
[2025-09-27 18:03:37.131] [INFO] 启动自检管理器资源释放完成
[2025-09-27 18:03:37.131] [INFO] 启动自检管理器资源释放完成
[2025-09-27 18:03:37.131] [INFO] 释放DMC1000B电机管理器资源...
[2025-09-27 18:03:37.133] [INFO] 开始释放DMC1000B资源...
[2025-09-27 18:03:37.143] [INFO] 停止所有电机部分失败
[2025-09-27 18:03:37.144] [INFO] DMC1000B资源释放完成
[2025-09-27 18:03:37.144] [INFO] DMC1000B电机管理器资源释放完成
[2025-09-27 18:03:37.144] [INFO] 释放DMC1000BIO管理器资源...
[2025-09-27 18:03:37.147] [INFO] DMC1000BIO管理器资源释放完成
[2025-09-27 18:03:37.147] [INFO] 释放其他Manager资源...
[2025-09-27 18:03:37.149] [INFO] 开始释放MotorManager资源...
[2025-09-27 18:03:37.153] [INFO] 电机1停止运动
[2025-09-27 18:03:37.154] [INFO] 电机0停止运动
[2025-09-27 18:03:37.155] [INFO] 电机5停止运动
[2025-09-27 18:03:37.156] [INFO] 电机4停止运动
[2025-09-27 18:03:37.156] [INFO] 电机6停止运动
[2025-09-27 18:03:37.157] [INFO] 电机3停止运动
[2025-09-27 18:03:37.159] [INFO] 电机2停止运动
[2025-09-27 18:03:37.162] [INFO] 电机7停止运动
[2025-09-27 18:03:37.163] [INFO] 所有电机已停止
[2025-09-27 18:03:37.211] [INFO] 电机监控循环被取消
[2025-09-27 18:03:37.211] [INFO] 电机监控循环结束
[2025-09-27 18:03:37.211] [INFO] 电机监控任务已停止
[2025-09-27 18:03:37.211] [INFO] 模拟释放运动控制卡资源
[2025-09-27 18:03:37.211] [INFO] MotorManager资源释放完成
[2025-09-27 18:03:37.214] [INFO] 开始释放ScannerManager资源...
[2025-09-27 18:03:37.216] [INFO] 扫描枪状态变更: Disconnected - 扫描枪连接已断开
[2025-09-27 18:03:37.216] [INFO] ScannerManager资源释放完成
[2025-09-27 18:03:37.218] [INFO] 开始释放ModbusTcpManager资源...
[2025-09-27 18:03:37.220] [INFO] Modbus TCP状态变更: Disconnected - Modbus TCP连接已断开
[2025-09-27 18:03:37.220] [INFO] Modbus TCP连接已断开
[2025-09-27 18:03:37.221] [INFO] ModbusTcpManager资源释放完成
[2025-09-27 18:03:37.222] [INFO] 开始释放EpsonRobotManager资源...
[2025-09-27 18:03:37.227] [INFO] EpsonRobotManager资源释放完成
[2025-09-27 18:03:37.230] [INFO] 开始释放EpsonRobotManager2资源...
[2025-09-27 18:03:37.235] [INFO] EpsonRobotManager2资源释放完成
[2025-09-27 18:03:37.236] [INFO] 开始释放VisionManager资源...
[2025-09-27 18:03:37.239] [INFO] 模拟释放相机资源
[2025-09-27 18:03:37.239] [INFO] VisionManager资源释放完成
[2025-09-27 18:03:37.241] [INFO] 开始释放StatisticsManager资源...
[2025-09-27 18:03:37.242] [INFO] 自动保存任务已停止
[2025-09-27 18:03:37.246] [INFO] StatisticsManager资源释放完成
[2025-09-27 18:03:37.246] [INFO] 其他Manager资源释放完成
[2025-09-27 18:03:37.246] [INFO] 释放DMC1000B控制卡资源...
[2025-09-27 18:03:37.248] [INFO] DMC1000B控制卡未初始化，无需释放
[2025-09-27 18:03:37.248] [INFO] DMC1000B控制卡资源释放完成
[2025-09-27 18:03:37.248] [INFO] 所有Manager资源释放流程完成
[2025-09-27 18:03:37.249] [INFO] 所有资源释放完成，程序即将退出
[2025-09-27 18:03:37.252] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-27 18:03:37.253] [INFO] 翻转电机控制面板资源释放完成
[2025-09-27 18:56:50.968] [INFO] 程序启动开始
[2025-09-27 18:56:50.969] [INFO] 加载Settings系统配置...
[2025-09-27 18:56:50.975] [INFO] 设置加载成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 18:56:50.976] [INFO] Settings系统配置加载完成
[2025-09-27 18:56:50.976] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-27 18:56:50.988] [INFO] 开始初始化各个Manager...
[2025-09-27 18:56:50.988] [INFO] 初始化DMC1000B控制卡...
[2025-09-27 18:56:50.991] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-27 18:56:50.993] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-27 18:56:50.993] [WARN] DMC1000B控制卡初始化失败，相关功能可能无法正常工作
[2025-09-27 18:56:50.993] [INFO] 初始化基础Manager...
[2025-09-27 18:56:50.997] [INFO] IO状态缓存初始化完成
[2025-09-27 18:56:50.999] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-27 18:56:50.999] [WARN] DMC1000BIO管理器初始化失败
[2025-09-27 18:56:51.002] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-27 18:56:51.003] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:56:51.022] [WARN] DMC1000B电机管理器初始化失败
[2025-09-27 18:56:51.022] [INFO] 初始化系统模式管理器...
[2025-09-27 18:56:51.029] [INFO] 从Settings系统加载安全管理器配置成功
[2025-09-27 18:56:51.031] [INFO] 安全管理器实例已创建
[2025-09-27 18:56:51.035] [INFO] 开始初始化MotorManager...
[2025-09-27 18:56:51.036] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-27 18:56:51.037] [INFO] 当前模式已经是 Manual，无需切换
[2025-09-27 18:56:51.038] [INFO] 模拟初始化运动控制卡
[2025-09-27 18:56:51.253] [INFO] 加载了8个电机的默认配置
[2025-09-27 18:56:51.254] [INFO] 电机监控任务已启动
[2025-09-27 18:56:51.254] [INFO] MotorManager初始化完成
[2025-09-27 18:56:51.254] [INFO] 初始化通信Manager...
[2025-09-27 18:56:51.255] [INFO] 电机监控循环开始
[2025-09-27 18:56:51.256] [INFO] 开始初始化ScannerManager...
[2025-09-27 18:56:51.258] [INFO] 扫描枪配置从Settings系统加载: COM1, 9600, 8, One, None
[2025-09-27 18:56:51.259] [INFO] 串口初始化完成
[2025-09-27 18:56:51.260] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-27 18:56:51.264] [ERROR] 连接扫描枪 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerManager.<ConnectAsync>b__23_1() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 145
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerManager.<<ConnectAsync>b__23_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 139
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:56:51.265] [WARN] 扫描枪连接失败，但初始化完成，可稍后重试连接
[2025-09-27 18:56:51.265] [INFO] ScannerManager初始化完成
[2025-09-27 18:56:51.270] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-27 18:56:51.271] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-27 18:56:51.272] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-27 18:56:51.273] [INFO] SCARA通信管理器已初始化
[2025-09-27 18:56:51.274] [INFO] ScannerAutoModeManager管理器引用初始化完成
[2025-09-27 18:56:51.276] [INFO] 开始初始化ScannerAutoModeManager...
[2025-09-27 18:56:51.277] [INFO] 开始初始化MultiScannerManager...
[2025-09-27 18:56:51.279] [INFO] 开始初始化扫描枪1...
[2025-09-27 18:56:51.279] [INFO] 从Settings系统加载扫描枪1配置: COM1, 115200
[2025-09-27 18:56:51.281] [INFO] 扫描枪1串口初始化完成
[2025-09-27 18:56:51.281] [INFO] 扫描枪1初始化完成
[2025-09-27 18:56:51.281] [INFO] 开始初始化扫描枪2...
[2025-09-27 18:56:51.281] [INFO] 从Settings系统加载扫描枪2配置: COM2, 115200
[2025-09-27 18:56:51.282] [INFO] 扫描枪2串口初始化完成
[2025-09-27 18:56:51.282] [INFO] 扫描枪2初始化完成
[2025-09-27 18:56:51.282] [INFO] 开始初始化扫描枪3...
[2025-09-27 18:56:51.282] [INFO] 从Settings系统加载扫描枪3配置: COM3, 115200
[2025-09-27 18:56:51.282] [INFO] 扫描枪3串口初始化完成
[2025-09-27 18:56:51.282] [INFO] 扫描枪3初始化完成
[2025-09-27 18:56:51.282] [INFO] MultiScannerManager初始化完成
[2025-09-27 18:56:51.284] [INFO] 开始自动连接并验证所有扫码器...
[2025-09-27 18:56:51.284] [INFO] 正在连接扫码器1...
[2025-09-27 18:56:51.287] [INFO] 扫描枪1状态变更: Connecting - 正在连接扫描枪1...
[2025-09-27 18:56:51.289] [INFO] 扫码器1状态变化: Connecting - 正在连接扫描枪1...
[2025-09-27 18:56:51.290] [ERROR] 连接扫描枪1 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerInstance.<ConnectAsync>b__28_1() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 767
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerInstance.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 761
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:56:51.290] [WARN] 扫码器1连接失败
[2025-09-27 18:56:51.796] [INFO] 正在连接扫码器2...
[2025-09-27 18:56:51.796] [INFO] 扫描枪2状态变更: Connecting - 正在连接扫描枪2...
[2025-09-27 18:56:51.796] [INFO] 扫码器2状态变化: Connecting - 正在连接扫描枪2...
[2025-09-27 18:56:51.797] [INFO] 扫描枪2连接成功: COM2
[2025-09-27 18:56:51.797] [INFO] 扫描枪2状态变更: Connected - 扫描枪2连接成功
[2025-09-27 18:56:51.797] [INFO] 扫码器2状态变化: Connected - 扫描枪2连接成功
[2025-09-27 18:56:51.798] [INFO] 扫码器2连接成功
[2025-09-27 18:56:51.801] [INFO] 扫描枪2发送数据: hello
[2025-09-27 18:56:52.804] [INFO] 扫码器2hello验证完成
[2025-09-27 18:56:52.804] [INFO] 扫码器2通信验证成功
[2025-09-27 18:56:53.317] [INFO] 正在连接扫码器3...
[2025-09-27 18:56:53.317] [INFO] 扫描枪3状态变更: Connecting - 正在连接扫描枪3...
[2025-09-27 18:56:53.317] [INFO] 扫码器3状态变化: Connecting - 正在连接扫描枪3...
[2025-09-27 18:56:53.318] [ERROR] 连接扫描枪3 执行失败
异常详情: 端口“COM3”不存在。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerInstance.<ConnectAsync>b__28_1() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 767
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerInstance.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 761
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:56:53.318] [WARN] 扫码器3连接失败
[2025-09-27 18:56:53.821] [WARN] 部分扫码器连接或验证失败，但系统将继续运行
[2025-09-27 18:56:53.821] [INFO] ScannerAutoModeManager初始化完成
[2025-09-27 18:56:53.823] [INFO] 开始初始化ModbusTcpManager...
[2025-09-27 18:56:53.825] [INFO] Modbus TCP配置: *************:502, SlaveId: 1
[2025-09-27 18:56:53.827] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-27 18:56:58.849] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: *************:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 153
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:56:58.850] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-27 18:56:58.850] [INFO] ModbusTcpManager初始化完成
[2025-09-27 18:56:58.853] [INFO] 开始初始化EpsonRobotManager...
[2025-09-27 18:56:58.854] [INFO] 从Settings系统加载Epson机器人1配置成功
[2025-09-27 18:56:58.854] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-27 18:56:58.855] [INFO] EpsonRobotManager初始化完成
[2025-09-27 18:56:58.855] [INFO] 初始化视觉Manager...
[2025-09-27 18:56:58.857] [INFO] 开始初始化VisionManager...
[2025-09-27 18:56:58.858] [INFO] 视觉系统配置从Settings系统加载: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms, 置信度阈值=0.8
[2025-09-27 18:56:58.859] [INFO] 模拟初始化相机，索引: 0
[2025-09-27 18:56:59.362] [INFO] 相机初始化成功
[2025-09-27 18:56:59.363] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-27 18:56:59.364] [INFO] 视觉配置加载完成
[2025-09-27 18:56:59.364] [INFO] VisionManager初始化完成
[2025-09-27 18:56:59.364] [INFO] 初始化数据Manager...
[2025-09-27 18:56:59.366] [INFO] 开始初始化StatisticsManager...
[2025-09-27 18:56:59.367] [INFO] 统计管理器配置: 数据目录=Export, 缓存大小=1000, 自动保存间隔=24分钟
[2025-09-27 18:56:59.372] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-27 18:56:59.373] [INFO] 历史数据加载完成
[2025-09-27 18:56:59.373] [INFO] StatisticsManager初始化完成
[2025-09-27 18:56:59.373] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-27 18:56:59.373] [INFO] 所有Manager初始化完成
[2025-09-27 18:56:59.424] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 18:56:59.424] [INFO] 主界面布局创建完成
[2025-09-27 18:56:59.425] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-27 18:56:59.515] [INFO] 开始初始化系统
[2025-09-27 18:56:59.516] [INFO] 初始化业务逻辑
[2025-09-27 18:56:59.517] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-27 18:56:59.517] [INFO] IO管理器初始化完成
[2025-09-27 18:56:59.517] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-27 18:56:59.518] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:56:59.518] [INFO] 电机管理器初始化完成
[2025-09-27 18:56:59.519] [INFO] IO事件订阅完成
[2025-09-27 18:56:59.520] [INFO] 电机事件订阅完成
[2025-09-27 18:56:59.520] [INFO] 业务层交互机制建立完成
[2025-09-27 18:56:59.520] [INFO] 业务逻辑初始化完成
[2025-09-27 18:56:59.521] [INFO] 执行UI界面刷新
[2025-09-27 18:56:59.524] [INFO] UI界面刷新完成
[2025-09-27 18:56:59.524] [INFO] 系统初始化完成
[2025-09-27 18:57:01.330] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 18:57:01.331] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-27 18:57:01.337] [INFO] UI参数显示已从Settings系统更新
[2025-09-27 18:57:01.337] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-27 18:57:01.337] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-27 18:57:05.575] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-27 18:57:05.576] [INFO] 保存Settings系统配置...
[2025-09-27 18:57:05.577] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 18:57:05.577] [INFO] Settings系统配置保存完成
[2025-09-27 18:57:05.580] [INFO] 开始按顺序释放各个Manager资源...
[2025-09-27 18:57:05.581] [INFO] 清理UI资源...
[2025-09-27 18:57:05.581] [INFO] UI资源清理完成
[2025-09-27 18:57:05.581] [INFO] 释放工作流管理器资源...
[2025-09-27 18:57:05.583] [INFO] 开始释放WorkflowManager资源...
[2025-09-27 18:57:05.584] [WARN] 皮带电机自动控制未在运行
[2025-09-27 18:57:05.585] [INFO] 工作流事件取消订阅完成
[2025-09-27 18:57:05.585] [INFO] WorkflowManager资源释放完成
[2025-09-27 18:57:05.585] [INFO] 工作流管理器资源释放完成
[2025-09-27 18:57:05.585] [INFO] 释放启动自检管理器资源...
[2025-09-27 18:57:05.586] [INFO] 开始释放启动自检管理器资源...
[2025-09-27 18:57:05.586] [INFO] 启动自检管理器资源释放完成
[2025-09-27 18:57:05.586] [INFO] 启动自检管理器资源释放完成
[2025-09-27 18:57:05.586] [INFO] 释放DMC1000B电机管理器资源...
[2025-09-27 18:57:05.588] [INFO] 开始释放DMC1000B资源...
[2025-09-27 18:57:05.593] [INFO] 停止所有电机部分失败
[2025-09-27 18:57:05.594] [INFO] DMC1000B资源释放完成
[2025-09-27 18:57:05.594] [INFO] DMC1000B电机管理器资源释放完成
[2025-09-27 18:57:05.594] [INFO] 释放DMC1000BIO管理器资源...
[2025-09-27 18:57:05.597] [INFO] DMC1000BIO管理器资源释放完成
[2025-09-27 18:57:05.597] [INFO] 释放其他Manager资源...
[2025-09-27 18:57:05.599] [INFO] 开始释放MotorManager资源...
[2025-09-27 18:57:05.602] [INFO] 电机0停止运动
[2025-09-27 18:57:05.602] [INFO] 电机6停止运动
[2025-09-27 18:57:05.602] [INFO] 电机2停止运动
[2025-09-27 18:57:05.603] [INFO] 电机1停止运动
[2025-09-27 18:57:05.603] [INFO] 电机4停止运动
[2025-09-27 18:57:05.603] [INFO] 电机7停止运动
[2025-09-27 18:57:05.603] [INFO] 电机5停止运动
[2025-09-27 18:57:05.603] [INFO] 电机3停止运动
[2025-09-27 18:57:05.603] [INFO] 所有电机已停止
[2025-09-27 18:57:05.604] [INFO] 电机监控循环被取消
[2025-09-27 18:57:05.604] [INFO] 电机监控循环结束
[2025-09-27 18:57:05.604] [INFO] 电机监控任务已停止
[2025-09-27 18:57:05.605] [INFO] 模拟释放运动控制卡资源
[2025-09-27 18:57:05.605] [INFO] MotorManager资源释放完成
[2025-09-27 18:57:05.606] [INFO] 开始释放ScannerManager资源...
[2025-09-27 18:57:05.608] [INFO] 扫描枪状态变更: Disconnected - 扫描枪连接已断开
[2025-09-27 18:57:05.608] [INFO] ScannerManager资源释放完成
[2025-09-27 18:57:05.610] [INFO] 开始释放ModbusTcpManager资源...
[2025-09-27 18:57:05.611] [INFO] Modbus TCP状态变更: Disconnected - Modbus TCP连接已断开
[2025-09-27 18:57:05.611] [INFO] Modbus TCP连接已断开
[2025-09-27 18:57:05.611] [INFO] ModbusTcpManager资源释放完成
[2025-09-27 18:57:05.612] [INFO] 开始释放EpsonRobotManager资源...
[2025-09-27 18:57:05.616] [INFO] EpsonRobotManager资源释放完成
[2025-09-27 18:57:05.618] [INFO] 开始释放EpsonRobotManager2资源...
[2025-09-27 18:57:05.622] [INFO] EpsonRobotManager2资源释放完成
[2025-09-27 18:57:05.623] [INFO] 开始释放VisionManager资源...
[2025-09-27 18:57:05.625] [INFO] 模拟释放相机资源
[2025-09-27 18:57:05.625] [INFO] VisionManager资源释放完成
[2025-09-27 18:57:05.627] [INFO] 开始释放StatisticsManager资源...
[2025-09-27 18:57:05.628] [INFO] 自动保存任务已停止
[2025-09-27 18:57:05.631] [INFO] StatisticsManager资源释放完成
[2025-09-27 18:57:05.631] [INFO] 其他Manager资源释放完成
[2025-09-27 18:57:05.631] [INFO] 释放DMC1000B控制卡资源...
[2025-09-27 18:57:05.632] [INFO] DMC1000B控制卡未初始化，无需释放
[2025-09-27 18:57:05.632] [INFO] DMC1000B控制卡资源释放完成
[2025-09-27 18:57:05.632] [INFO] 所有Manager资源释放流程完成
[2025-09-27 18:57:05.633] [INFO] 所有资源释放完成，程序即将退出
[2025-09-27 18:57:05.633] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-27 18:57:05.634] [INFO] 翻转电机控制面板资源释放完成
[2025-09-27 18:57:08.573] [INFO] 程序启动开始
[2025-09-27 18:57:08.573] [INFO] 加载Settings系统配置...
[2025-09-27 18:57:08.579] [INFO] 设置加载成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 18:57:08.579] [INFO] Settings系统配置加载完成
[2025-09-27 18:57:08.580] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-27 18:57:08.589] [INFO] 开始初始化各个Manager...
[2025-09-27 18:57:08.590] [INFO] 初始化DMC1000B控制卡...
[2025-09-27 18:57:08.593] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-27 18:57:08.594] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-27 18:57:08.594] [WARN] DMC1000B控制卡初始化失败，相关功能可能无法正常工作
[2025-09-27 18:57:08.594] [INFO] 初始化基础Manager...
[2025-09-27 18:57:08.599] [INFO] IO状态缓存初始化完成
[2025-09-27 18:57:08.601] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-27 18:57:08.601] [WARN] DMC1000BIO管理器初始化失败
[2025-09-27 18:57:08.604] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-27 18:57:08.605] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:57:08.626] [WARN] DMC1000B电机管理器初始化失败
[2025-09-27 18:57:08.626] [INFO] 初始化系统模式管理器...
[2025-09-27 18:57:08.633] [INFO] 从Settings系统加载安全管理器配置成功
[2025-09-27 18:57:08.636] [INFO] 安全管理器实例已创建
[2025-09-27 18:57:08.639] [INFO] 开始初始化MotorManager...
[2025-09-27 18:57:08.640] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-27 18:57:08.640] [INFO] 当前模式已经是 Manual，无需切换
[2025-09-27 18:57:08.641] [INFO] 模拟初始化运动控制卡
[2025-09-27 18:57:08.845] [INFO] 加载了8个电机的默认配置
[2025-09-27 18:57:08.846] [INFO] 电机监控任务已启动
[2025-09-27 18:57:08.846] [INFO] MotorManager初始化完成
[2025-09-27 18:57:08.846] [INFO] 初始化通信Manager...
[2025-09-27 18:57:08.847] [INFO] 电机监控循环开始
[2025-09-27 18:57:08.848] [INFO] 开始初始化ScannerManager...
[2025-09-27 18:57:08.850] [INFO] 扫描枪配置从Settings系统加载: COM1, 9600, 8, One, None
[2025-09-27 18:57:08.851] [INFO] 串口初始化完成
[2025-09-27 18:57:08.853] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-27 18:57:08.857] [ERROR] 连接扫描枪 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerManager.<ConnectAsync>b__23_1() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 145
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerManager.<<ConnectAsync>b__23_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 139
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:57:08.858] [WARN] 扫描枪连接失败，但初始化完成，可稍后重试连接
[2025-09-27 18:57:08.859] [INFO] ScannerManager初始化完成
[2025-09-27 18:57:08.863] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-27 18:57:08.863] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-27 18:57:08.864] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-27 18:57:08.865] [INFO] SCARA通信管理器已初始化
[2025-09-27 18:57:08.867] [INFO] ScannerAutoModeManager管理器引用初始化完成
[2025-09-27 18:57:08.870] [INFO] 开始初始化ScannerAutoModeManager...
[2025-09-27 18:57:08.872] [INFO] 开始初始化MultiScannerManager...
[2025-09-27 18:57:08.874] [INFO] 开始初始化扫描枪1...
[2025-09-27 18:57:08.875] [INFO] 从Settings系统加载扫描枪1配置: COM1, 115200
[2025-09-27 18:57:08.876] [INFO] 扫描枪1串口初始化完成
[2025-09-27 18:57:08.876] [INFO] 扫描枪1初始化完成
[2025-09-27 18:57:08.877] [INFO] 开始初始化扫描枪2...
[2025-09-27 18:57:08.877] [INFO] 从Settings系统加载扫描枪2配置: COM2, 115200
[2025-09-27 18:57:08.877] [INFO] 扫描枪2串口初始化完成
[2025-09-27 18:57:08.877] [INFO] 扫描枪2初始化完成
[2025-09-27 18:57:08.877] [INFO] 开始初始化扫描枪3...
[2025-09-27 18:57:08.877] [INFO] 从Settings系统加载扫描枪3配置: COM3, 115200
[2025-09-27 18:57:08.877] [INFO] 扫描枪3串口初始化完成
[2025-09-27 18:57:08.878] [INFO] 扫描枪3初始化完成
[2025-09-27 18:57:08.878] [INFO] MultiScannerManager初始化完成
[2025-09-27 18:57:08.880] [INFO] 开始自动连接并验证所有扫码器...
[2025-09-27 18:57:08.880] [INFO] 正在连接扫码器1...
[2025-09-27 18:57:08.883] [INFO] 扫描枪1状态变更: Connecting - 正在连接扫描枪1...
[2025-09-27 18:57:08.885] [INFO] 扫码器1状态变化: Connecting - 正在连接扫描枪1...
[2025-09-27 18:57:08.885] [ERROR] 连接扫描枪1 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerInstance.<ConnectAsync>b__28_1() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 767
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerInstance.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 761
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:57:08.886] [WARN] 扫码器1连接失败
[2025-09-27 18:57:09.388] [INFO] 正在连接扫码器2...
[2025-09-27 18:57:09.388] [INFO] 扫描枪2状态变更: Connecting - 正在连接扫描枪2...
[2025-09-27 18:57:09.388] [INFO] 扫码器2状态变化: Connecting - 正在连接扫描枪2...
[2025-09-27 18:57:09.389] [INFO] 扫描枪2连接成功: COM2
[2025-09-27 18:57:09.389] [INFO] 扫描枪2状态变更: Connected - 扫描枪2连接成功
[2025-09-27 18:57:09.389] [INFO] 扫码器2状态变化: Connected - 扫描枪2连接成功
[2025-09-27 18:57:09.390] [INFO] 扫码器2连接成功
[2025-09-27 18:57:09.393] [INFO] 扫描枪2发送数据: hello
[2025-09-27 18:57:10.399] [INFO] 扫码器2hello验证完成
[2025-09-27 18:57:10.399] [INFO] 扫码器2通信验证成功
[2025-09-27 18:57:10.912] [INFO] 正在连接扫码器3...
[2025-09-27 18:57:10.912] [INFO] 扫描枪3状态变更: Connecting - 正在连接扫描枪3...
[2025-09-27 18:57:10.913] [INFO] 扫码器3状态变化: Connecting - 正在连接扫描枪3...
[2025-09-27 18:57:10.913] [ERROR] 连接扫描枪3 执行失败
异常详情: 端口“COM3”不存在。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerInstance.<ConnectAsync>b__28_1() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 767
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerInstance.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 761
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:57:10.914] [WARN] 扫码器3连接失败
[2025-09-27 18:57:11.425] [WARN] 部分扫码器连接或验证失败，但系统将继续运行
[2025-09-27 18:57:11.425] [INFO] ScannerAutoModeManager初始化完成
[2025-09-27 18:57:11.427] [INFO] 开始初始化ModbusTcpManager...
[2025-09-27 18:57:11.429] [INFO] Modbus TCP配置: *************:502, SlaveId: 1
[2025-09-27 18:57:11.431] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-27 18:57:16.447] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: *************:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 153
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:57:16.448] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-27 18:57:16.448] [INFO] ModbusTcpManager初始化完成
[2025-09-27 18:57:16.451] [INFO] 开始初始化EpsonRobotManager...
[2025-09-27 18:57:16.452] [INFO] 从Settings系统加载Epson机器人1配置成功
[2025-09-27 18:57:16.452] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-27 18:57:16.452] [INFO] EpsonRobotManager初始化完成
[2025-09-27 18:57:16.452] [INFO] 初始化视觉Manager...
[2025-09-27 18:57:16.454] [INFO] 开始初始化VisionManager...
[2025-09-27 18:57:16.455] [INFO] 视觉系统配置从Settings系统加载: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms, 置信度阈值=0.8
[2025-09-27 18:57:16.456] [INFO] 模拟初始化相机，索引: 0
[2025-09-27 18:57:16.960] [INFO] 相机初始化成功
[2025-09-27 18:57:16.961] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-27 18:57:16.962] [INFO] 视觉配置加载完成
[2025-09-27 18:57:16.962] [INFO] VisionManager初始化完成
[2025-09-27 18:57:16.962] [INFO] 初始化数据Manager...
[2025-09-27 18:57:16.964] [INFO] 开始初始化StatisticsManager...
[2025-09-27 18:57:16.965] [INFO] 统计管理器配置: 数据目录=Export, 缓存大小=1000, 自动保存间隔=24分钟
[2025-09-27 18:57:16.970] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-27 18:57:16.970] [INFO] 历史数据加载完成
[2025-09-27 18:57:16.970] [INFO] StatisticsManager初始化完成
[2025-09-27 18:57:16.970] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-27 18:57:16.970] [INFO] 所有Manager初始化完成
[2025-09-27 18:57:17.002] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 18:57:17.003] [INFO] 主界面布局创建完成
[2025-09-27 18:57:17.004] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-27 18:57:17.099] [INFO] 开始初始化系统
[2025-09-27 18:57:17.100] [INFO] 初始化业务逻辑
[2025-09-27 18:57:17.101] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-27 18:57:17.101] [INFO] IO管理器初始化完成
[2025-09-27 18:57:17.101] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-27 18:57:17.101] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:57:17.102] [INFO] 电机管理器初始化完成
[2025-09-27 18:57:17.103] [INFO] IO事件订阅完成
[2025-09-27 18:57:17.103] [INFO] 电机事件订阅完成
[2025-09-27 18:57:17.103] [INFO] 业务层交互机制建立完成
[2025-09-27 18:57:17.103] [INFO] 业务逻辑初始化完成
[2025-09-27 18:57:17.104] [INFO] 执行UI界面刷新
[2025-09-27 18:57:17.107] [INFO] UI界面刷新完成
[2025-09-27 18:57:17.108] [INFO] 系统初始化完成
[2025-09-27 18:57:20.357] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
[2025-09-27 18:57:23.360] [INFO] IO输出控制面板初始化完成 - 支持基础IO和扩展IO
[2025-09-27 18:57:24.079] [WARN] IO管理器未初始化，仅更新UI显示 - O0001: ON
[2025-09-27 18:57:24.534] [WARN] IO管理器未初始化，仅更新UI显示 - O0002: ON
[2025-09-27 18:57:25.659] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 18:57:25.660] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-27 18:57:25.665] [INFO] UI参数显示已从Settings系统更新
[2025-09-27 18:57:25.665] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-27 18:57:25.665] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-27 18:57:25.823] [ERROR] IO管理器初始化超时，无法启动IO监控
[2025-09-27 18:57:28.248] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 18:57:28.253] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 起始速度必须小于最大速度
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass48_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 196
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:57:28.253] [INFO] 右翻转电机参数已更新并保存: 脉冲当量=0.012, 起始速度=10, 最大速度=4, 最大加速度=120, 加速时间=0.1
[2025-09-27 18:57:28.391] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 18:57:28.392] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass48_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 196
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:57:28.392] [INFO] 右翻转电机参数已更新并保存: 脉冲当量=0.012, 起始速度=10, 最大速度=44, 最大加速度=120, 加速时间=0.1
[2025-09-27 18:57:28.829] [ERROR] IO管理器初始化超时，无法启动IO监控
[2025-09-27 18:57:30.384] [INFO] 翻转电机控制面板资源释放完成
[2025-09-27 18:57:30.394] [INFO] 皮带电机控制面板初始化完成 - 双电机设计
[2025-09-27 18:57:30.394] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-27 18:57:30.399] [INFO] 皮带电机轴3参数设置成功: 脉冲当量=0.01mm/pulse, 最大速度=100mm/s
[2025-09-27 18:57:30.400] [INFO] 皮带电机轴2参数设置成功: 脉冲当量=0.01mm/pulse, 最大速度=100mm/s
[2025-09-27 18:57:30.403] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 18:57:30.403] [INFO] 皮带电机配置保存到Settings系统成功
[2025-09-27 18:57:30.404] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 18:57:30.404] [INFO] 皮带电机配置保存到Settings系统成功
[2025-09-27 18:57:30.404] [INFO] 电机参数显示刷新完成
[2025-09-27 18:57:30.404] [INFO] 皮带电机参数初始化完成
[2025-09-27 18:57:30.405] [INFO] 皮带电机控制面板业务逻辑初始化完成
[2025-09-27 18:57:33.547] [INFO] 皮带电机轴3参数设置成功: 脉冲当量=0.01mm/pulse, 最大速度=100mm/s
[2025-09-27 18:57:33.547] [INFO] 输入皮带电机参数已更新: 脉冲当量=0.01, 最大速度=100, 加速度=500, 点动距离=10
[2025-09-27 18:57:33.548] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 18:57:33.548] [INFO] 皮带电机配置保存到Settings系统成功
[2025-09-27 18:57:35.454] [ERROR] 设置皮带电机轴3参数 执行失败
异常详情: 皮带电机参数无效: 计算的加速时间过长(14.29s)，请提高加速度
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass55_0.<<SetBeltMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 567
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:57:35.455] [INFO] 输入皮带电机参数已更新: 脉冲当量=0.01, 最大速度=100, 加速度=7, 点动距离=10
[2025-09-27 18:57:35.574] [INFO] 皮带电机轴3参数设置成功: 脉冲当量=0.01mm/pulse, 最大速度=100mm/s
[2025-09-27 18:57:35.576] [INFO] 输入皮带电机参数已更新: 脉冲当量=0.01, 最大速度=100, 加速度=77, 点动距离=10
[2025-09-27 18:57:35.576] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 18:57:35.576] [INFO] 皮带电机配置保存到Settings系统成功
[2025-09-27 18:57:42.623] [INFO] 皮带电机轴3参数设置成功: 脉冲当量=0.01mm/pulse, 最大速度=100mm/s
[2025-09-27 18:57:42.623] [INFO] 输入皮带电机参数已更新: 脉冲当量=0.01, 最大速度=100, 加速度=177, 点动距离=10
[2025-09-27 18:57:42.624] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 18:57:42.624] [INFO] 皮带电机配置保存到Settings系统成功
[2025-09-27 18:57:46.783] [ERROR] 设置皮带电机轴2参数 执行失败
异常详情: 皮带电机参数无效: 计算的加速时间过长(50.00s)，请提高加速度
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass55_0.<<SetBeltMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 567
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:57:46.784] [INFO] 输出皮带电机参数已更新: 脉冲当量=0.01, 最大速度=100, 加速度=2, 点动距离=10
[2025-09-27 18:57:46.927] [INFO] 皮带电机轴2参数设置成功: 脉冲当量=0.01mm/pulse, 最大速度=100mm/s
[2025-09-27 18:57:46.927] [INFO] 输出皮带电机参数已更新: 脉冲当量=0.01, 最大速度=100, 加速度=22, 点动距离=10
[2025-09-27 18:57:46.928] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 18:57:46.928] [INFO] 皮带电机配置保存到Settings系统成功
[2025-09-27 18:57:48.047] [INFO] 皮带电机轴2参数设置成功: 脉冲当量=0.01mm/pulse, 最大速度=100mm/s
[2025-09-27 18:57:48.047] [INFO] 输出皮带电机参数已更新: 脉冲当量=0.01, 最大速度=100, 加速度=228, 点动距离=10
[2025-09-27 18:57:48.048] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 18:57:48.048] [INFO] 皮带电机配置保存到Settings系统成功
[2025-09-27 18:57:52.287] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-27 18:57:52.288] [INFO] 保存Settings系统配置...
[2025-09-27 18:57:52.289] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 18:57:52.289] [INFO] Settings系统配置保存完成
[2025-09-27 18:57:52.291] [INFO] 开始按顺序释放各个Manager资源...
[2025-09-27 18:57:52.292] [INFO] 清理UI资源...
[2025-09-27 18:57:52.292] [INFO] UI资源清理完成
[2025-09-27 18:57:52.292] [INFO] 释放工作流管理器资源...
[2025-09-27 18:57:52.294] [INFO] 开始释放WorkflowManager资源...
[2025-09-27 18:57:52.295] [WARN] 皮带电机自动控制未在运行
[2025-09-27 18:57:52.296] [INFO] 工作流事件取消订阅完成
[2025-09-27 18:57:52.296] [INFO] WorkflowManager资源释放完成
[2025-09-27 18:57:52.300] [INFO] 工作流管理器资源释放完成
[2025-09-27 18:57:52.301] [INFO] 释放启动自检管理器资源...
[2025-09-27 18:57:52.302] [INFO] 开始释放启动自检管理器资源...
[2025-09-27 18:57:52.302] [INFO] 启动自检管理器资源释放完成
[2025-09-27 18:57:52.302] [INFO] 启动自检管理器资源释放完成
[2025-09-27 18:57:52.302] [INFO] 释放DMC1000B电机管理器资源...
[2025-09-27 18:57:52.303] [INFO] 开始释放DMC1000B资源...
[2025-09-27 18:57:52.310] [INFO] 停止所有电机部分失败
[2025-09-27 18:57:52.311] [INFO] DMC1000B资源释放完成
[2025-09-27 18:57:52.311] [INFO] DMC1000B电机管理器资源释放完成
[2025-09-27 18:57:52.311] [INFO] 释放DMC1000BIO管理器资源...
[2025-09-27 18:57:52.313] [INFO] DMC1000BIO管理器资源释放完成
[2025-09-27 18:57:52.313] [INFO] 释放其他Manager资源...
[2025-09-27 18:57:52.314] [INFO] 开始释放MotorManager资源...
[2025-09-27 18:57:52.317] [INFO] 电机0停止运动
[2025-09-27 18:57:52.318] [INFO] 电机6停止运动
[2025-09-27 18:57:52.318] [INFO] 电机4停止运动
[2025-09-27 18:57:52.318] [INFO] 电机7停止运动
[2025-09-27 18:57:52.318] [INFO] 电机1停止运动
[2025-09-27 18:57:52.318] [INFO] 电机2停止运动
[2025-09-27 18:57:52.319] [INFO] 电机5停止运动
[2025-09-27 18:57:52.319] [INFO] 电机3停止运动
[2025-09-27 18:57:52.319] [INFO] 所有电机已停止
[2025-09-27 18:57:52.320] [INFO] 电机监控循环被取消
[2025-09-27 18:57:52.320] [INFO] 电机监控循环结束
[2025-09-27 18:57:52.320] [INFO] 电机监控任务已停止
[2025-09-27 18:57:52.321] [INFO] 模拟释放运动控制卡资源
[2025-09-27 18:57:52.321] [INFO] MotorManager资源释放完成
[2025-09-27 18:57:52.322] [INFO] 开始释放ScannerManager资源...
[2025-09-27 18:57:52.323] [INFO] 扫描枪状态变更: Disconnected - 扫描枪连接已断开
[2025-09-27 18:57:52.324] [INFO] ScannerManager资源释放完成
[2025-09-27 18:57:52.325] [INFO] 开始释放ModbusTcpManager资源...
[2025-09-27 18:57:52.326] [INFO] Modbus TCP状态变更: Disconnected - Modbus TCP连接已断开
[2025-09-27 18:57:52.326] [INFO] Modbus TCP连接已断开
[2025-09-27 18:57:52.326] [INFO] ModbusTcpManager资源释放完成
[2025-09-27 18:57:52.328] [INFO] 开始释放EpsonRobotManager资源...
[2025-09-27 18:57:52.333] [INFO] EpsonRobotManager资源释放完成
[2025-09-27 18:57:52.335] [INFO] 开始释放EpsonRobotManager2资源...
[2025-09-27 18:57:52.339] [INFO] EpsonRobotManager2资源释放完成
[2025-09-27 18:57:52.341] [INFO] 开始释放VisionManager资源...
[2025-09-27 18:57:52.342] [INFO] 模拟释放相机资源
[2025-09-27 18:57:52.342] [INFO] VisionManager资源释放完成
[2025-09-27 18:57:52.344] [INFO] 开始释放StatisticsManager资源...
[2025-09-27 18:57:52.344] [INFO] 自动保存任务已停止
[2025-09-27 18:57:52.348] [INFO] StatisticsManager资源释放完成
[2025-09-27 18:57:52.348] [INFO] 其他Manager资源释放完成
[2025-09-27 18:57:52.348] [INFO] 释放DMC1000B控制卡资源...
[2025-09-27 18:57:52.349] [INFO] DMC1000B控制卡未初始化，无需释放
[2025-09-27 18:57:52.350] [INFO] DMC1000B控制卡资源释放完成
[2025-09-27 18:57:52.350] [INFO] 所有Manager资源释放流程完成
[2025-09-27 18:57:52.350] [INFO] 所有资源释放完成，程序即将退出
[2025-09-27 18:57:52.350] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-27 18:57:52.351] [INFO] 皮带电机控制面板资源释放完成
[2025-09-27 18:57:53.874] [INFO] 程序启动开始
[2025-09-27 18:57:53.875] [INFO] 加载Settings系统配置...
[2025-09-27 18:57:53.880] [INFO] 设置加载成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 18:57:53.880] [INFO] Settings系统配置加载完成
[2025-09-27 18:57:53.880] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-27 18:57:53.891] [INFO] 开始初始化各个Manager...
[2025-09-27 18:57:53.891] [INFO] 初始化DMC1000B控制卡...
[2025-09-27 18:57:53.896] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-27 18:57:53.898] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-27 18:57:53.898] [WARN] DMC1000B控制卡初始化失败，相关功能可能无法正常工作
[2025-09-27 18:57:53.898] [INFO] 初始化基础Manager...
[2025-09-27 18:57:53.905] [INFO] IO状态缓存初始化完成
[2025-09-27 18:57:53.907] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-27 18:57:53.908] [WARN] DMC1000BIO管理器初始化失败
[2025-09-27 18:57:53.911] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-27 18:57:53.911] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:57:53.930] [WARN] DMC1000B电机管理器初始化失败
[2025-09-27 18:57:53.930] [INFO] 初始化系统模式管理器...
[2025-09-27 18:57:53.938] [INFO] 从Settings系统加载安全管理器配置成功
[2025-09-27 18:57:53.941] [INFO] 安全管理器实例已创建
[2025-09-27 18:57:53.944] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-27 18:57:53.944] [INFO] 开始初始化MotorManager...
[2025-09-27 18:57:53.945] [INFO] 当前模式已经是 Manual，无需切换
[2025-09-27 18:57:53.946] [INFO] 模拟初始化运动控制卡
[2025-09-27 18:57:54.161] [INFO] 加载了8个电机的默认配置
[2025-09-27 18:57:54.161] [INFO] 电机监控任务已启动
[2025-09-27 18:57:54.162] [INFO] MotorManager初始化完成
[2025-09-27 18:57:54.162] [INFO] 初始化通信Manager...
[2025-09-27 18:57:54.163] [INFO] 电机监控循环开始
[2025-09-27 18:57:54.165] [INFO] 开始初始化ScannerManager...
[2025-09-27 18:57:54.167] [INFO] 扫描枪配置从Settings系统加载: COM1, 9600, 8, One, None
[2025-09-27 18:57:54.169] [INFO] 串口初始化完成
[2025-09-27 18:57:54.171] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-27 18:57:54.176] [ERROR] 连接扫描枪 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerManager.<ConnectAsync>b__23_1() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 145
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerManager.<<ConnectAsync>b__23_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 139
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:57:54.177] [WARN] 扫描枪连接失败，但初始化完成，可稍后重试连接
[2025-09-27 18:57:54.177] [INFO] ScannerManager初始化完成
[2025-09-27 18:57:54.182] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-27 18:57:54.183] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-27 18:57:54.184] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-27 18:57:54.185] [INFO] SCARA通信管理器已初始化
[2025-09-27 18:57:54.187] [INFO] ScannerAutoModeManager管理器引用初始化完成
[2025-09-27 18:57:54.190] [INFO] 开始初始化ScannerAutoModeManager...
[2025-09-27 18:57:54.193] [INFO] 开始初始化MultiScannerManager...
[2025-09-27 18:57:54.195] [INFO] 开始初始化扫描枪1...
[2025-09-27 18:57:54.196] [INFO] 从Settings系统加载扫描枪1配置: COM1, 115200
[2025-09-27 18:57:54.198] [INFO] 扫描枪1串口初始化完成
[2025-09-27 18:57:54.198] [INFO] 扫描枪1初始化完成
[2025-09-27 18:57:54.198] [INFO] 开始初始化扫描枪2...
[2025-09-27 18:57:54.198] [INFO] 从Settings系统加载扫描枪2配置: COM2, 115200
[2025-09-27 18:57:54.198] [INFO] 扫描枪2串口初始化完成
[2025-09-27 18:57:54.199] [INFO] 扫描枪2初始化完成
[2025-09-27 18:57:54.199] [INFO] 开始初始化扫描枪3...
[2025-09-27 18:57:54.199] [INFO] 从Settings系统加载扫描枪3配置: COM3, 115200
[2025-09-27 18:57:54.199] [INFO] 扫描枪3串口初始化完成
[2025-09-27 18:57:54.199] [INFO] 扫描枪3初始化完成
[2025-09-27 18:57:54.199] [INFO] MultiScannerManager初始化完成
[2025-09-27 18:57:54.201] [INFO] 开始自动连接并验证所有扫码器...
[2025-09-27 18:57:54.201] [INFO] 正在连接扫码器1...
[2025-09-27 18:57:54.204] [INFO] 扫描枪1状态变更: Connecting - 正在连接扫描枪1...
[2025-09-27 18:57:54.205] [INFO] 扫码器1状态变化: Connecting - 正在连接扫描枪1...
[2025-09-27 18:57:54.205] [ERROR] 连接扫描枪1 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerInstance.<ConnectAsync>b__28_1() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 767
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerInstance.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 761
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:57:54.206] [WARN] 扫码器1连接失败
[2025-09-27 18:57:54.715] [INFO] 正在连接扫码器2...
[2025-09-27 18:57:54.715] [INFO] 扫描枪2状态变更: Connecting - 正在连接扫描枪2...
[2025-09-27 18:57:54.715] [INFO] 扫码器2状态变化: Connecting - 正在连接扫描枪2...
[2025-09-27 18:57:54.716] [INFO] 扫描枪2连接成功: COM2
[2025-09-27 18:57:54.716] [INFO] 扫描枪2状态变更: Connected - 扫描枪2连接成功
[2025-09-27 18:57:54.717] [INFO] 扫码器2状态变化: Connected - 扫描枪2连接成功
[2025-09-27 18:57:54.717] [INFO] 扫码器2连接成功
[2025-09-27 18:57:54.720] [INFO] 扫描枪2发送数据: hello
[2025-09-27 18:57:55.724] [INFO] 扫码器2hello验证完成
[2025-09-27 18:57:55.724] [INFO] 扫码器2通信验证成功
[2025-09-27 18:57:56.237] [INFO] 正在连接扫码器3...
[2025-09-27 18:57:56.237] [INFO] 扫描枪3状态变更: Connecting - 正在连接扫描枪3...
[2025-09-27 18:57:56.237] [INFO] 扫码器3状态变化: Connecting - 正在连接扫描枪3...
[2025-09-27 18:57:56.237] [ERROR] 连接扫描枪3 执行失败
异常详情: 端口“COM3”不存在。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerInstance.<ConnectAsync>b__28_1() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 767
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerInstance.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 761
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:57:56.238] [WARN] 扫码器3连接失败
[2025-09-27 18:57:56.749] [WARN] 部分扫码器连接或验证失败，但系统将继续运行
[2025-09-27 18:57:56.749] [INFO] ScannerAutoModeManager初始化完成
[2025-09-27 18:57:56.751] [INFO] 开始初始化ModbusTcpManager...
[2025-09-27 18:57:56.753] [INFO] Modbus TCP配置: *************:502, SlaveId: 1
[2025-09-27 18:57:56.755] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-27 18:58:01.776] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: *************:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 153
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:58:01.778] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-27 18:58:01.778] [INFO] ModbusTcpManager初始化完成
[2025-09-27 18:58:01.781] [INFO] 开始初始化EpsonRobotManager...
[2025-09-27 18:58:01.783] [INFO] 从Settings系统加载Epson机器人1配置成功
[2025-09-27 18:58:01.783] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-27 18:58:01.783] [INFO] EpsonRobotManager初始化完成
[2025-09-27 18:58:01.783] [INFO] 初始化视觉Manager...
[2025-09-27 18:58:01.786] [INFO] 开始初始化VisionManager...
[2025-09-27 18:58:01.787] [INFO] 视觉系统配置从Settings系统加载: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms, 置信度阈值=0.8
[2025-09-27 18:58:01.788] [INFO] 模拟初始化相机，索引: 0
[2025-09-27 18:58:02.289] [INFO] 相机初始化成功
[2025-09-27 18:58:02.290] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-27 18:58:02.290] [INFO] 视觉配置加载完成
[2025-09-27 18:58:02.291] [INFO] VisionManager初始化完成
[2025-09-27 18:58:02.291] [INFO] 初始化数据Manager...
[2025-09-27 18:58:02.293] [INFO] 开始初始化StatisticsManager...
[2025-09-27 18:58:02.294] [INFO] 统计管理器配置: 数据目录=Export, 缓存大小=1000, 自动保存间隔=24分钟
[2025-09-27 18:58:02.299] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-27 18:58:02.299] [INFO] 历史数据加载完成
[2025-09-27 18:58:02.299] [INFO] StatisticsManager初始化完成
[2025-09-27 18:58:02.300] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-27 18:58:02.300] [INFO] 所有Manager初始化完成
[2025-09-27 18:58:02.332] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 18:58:02.332] [INFO] 主界面布局创建完成
[2025-09-27 18:58:02.333] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-27 18:58:02.379] [INFO] 开始初始化系统
[2025-09-27 18:58:02.380] [INFO] 初始化业务逻辑
[2025-09-27 18:58:02.381] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-27 18:58:02.381] [INFO] IO管理器初始化完成
[2025-09-27 18:58:02.381] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-27 18:58:02.382] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 18:58:02.382] [INFO] 电机管理器初始化完成
[2025-09-27 18:58:02.383] [INFO] IO事件订阅完成
[2025-09-27 18:58:02.383] [INFO] 电机事件订阅完成
[2025-09-27 18:58:02.383] [INFO] 业务层交互机制建立完成
[2025-09-27 18:58:02.383] [INFO] 业务逻辑初始化完成
[2025-09-27 18:58:02.384] [INFO] 执行UI界面刷新
[2025-09-27 18:58:02.387] [INFO] UI界面刷新完成
[2025-09-27 18:58:02.387] [INFO] 系统初始化完成
[2025-09-27 18:58:07.370] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 18:58:07.371] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-27 18:58:07.376] [INFO] UI参数显示已从Settings系统更新
[2025-09-27 18:58:07.376] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-27 18:58:07.376] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-27 18:58:12.274] [INFO] 翻转电机控制面板资源释放完成
[2025-09-27 18:58:12.277] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 18:58:13.060] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 18:58:13.060] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-27 18:58:13.060] [INFO] UI参数显示已从Settings系统更新
[2025-09-27 18:58:13.061] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-27 18:58:13.061] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-27 18:58:16.202] [INFO] 翻转电机控制面板资源释放完成
[2025-09-27 18:58:16.209] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
[2025-09-27 18:58:17.103] [INFO] IO输出控制面板初始化完成 - 支持基础IO和扩展IO
[2025-09-27 18:58:21.651] [ERROR] IO管理器初始化超时，无法启动IO监控
[2025-09-27 18:58:22.552] [ERROR] IO管理器初始化超时，无法启动IO监控
[2025-09-27 19:00:43.696] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-27 19:00:43.697] [INFO] 保存Settings系统配置...
[2025-09-27 19:00:43.699] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 19:00:43.699] [INFO] Settings系统配置保存完成
[2025-09-27 19:00:43.702] [INFO] 开始按顺序释放各个Manager资源...
[2025-09-27 19:00:43.703] [INFO] 清理UI资源...
[2025-09-27 19:00:43.703] [INFO] UI资源清理完成
[2025-09-27 19:00:43.703] [INFO] 释放工作流管理器资源...
[2025-09-27 19:00:43.705] [INFO] 开始释放WorkflowManager资源...
[2025-09-27 19:00:43.707] [WARN] 皮带电机自动控制未在运行
[2025-09-27 19:00:43.708] [INFO] 工作流事件取消订阅完成
[2025-09-27 19:00:43.708] [INFO] WorkflowManager资源释放完成
[2025-09-27 19:00:43.709] [INFO] 工作流管理器资源释放完成
[2025-09-27 19:00:43.709] [INFO] 释放启动自检管理器资源...
[2025-09-27 19:00:43.710] [INFO] 开始释放启动自检管理器资源...
[2025-09-27 19:00:43.710] [INFO] 启动自检管理器资源释放完成
[2025-09-27 19:00:43.710] [INFO] 启动自检管理器资源释放完成
[2025-09-27 19:00:43.710] [INFO] 释放DMC1000B电机管理器资源...
[2025-09-27 19:00:43.711] [INFO] 开始释放DMC1000B资源...
[2025-09-27 19:00:43.717] [INFO] 停止所有电机部分失败
[2025-09-27 19:00:43.718] [INFO] DMC1000B资源释放完成
[2025-09-27 19:00:43.718] [INFO] DMC1000B电机管理器资源释放完成
[2025-09-27 19:00:43.718] [INFO] 释放DMC1000BIO管理器资源...
[2025-09-27 19:00:43.720] [INFO] DMC1000BIO管理器资源释放完成
[2025-09-27 19:00:43.721] [INFO] 释放其他Manager资源...
[2025-09-27 19:00:43.722] [INFO] 开始释放MotorManager资源...
[2025-09-27 19:00:43.725] [INFO] 电机0停止运动
[2025-09-27 19:00:43.726] [INFO] 电机5停止运动
[2025-09-27 19:00:43.726] [INFO] 电机6停止运动
[2025-09-27 19:00:43.726] [INFO] 电机3停止运动
[2025-09-27 19:00:43.726] [INFO] 电机1停止运动
[2025-09-27 19:00:43.727] [INFO] 电机4停止运动
[2025-09-27 19:00:43.727] [INFO] 电机2停止运动
[2025-09-27 19:00:43.727] [INFO] 电机7停止运动
[2025-09-27 19:00:43.727] [INFO] 所有电机已停止
[2025-09-27 19:00:43.728] [INFO] 电机监控循环被取消
[2025-09-27 19:00:43.728] [INFO] 电机监控循环结束
[2025-09-27 19:00:43.728] [INFO] 电机监控任务已停止
[2025-09-27 19:00:43.728] [INFO] 模拟释放运动控制卡资源
[2025-09-27 19:00:43.728] [INFO] MotorManager资源释放完成
[2025-09-27 19:00:43.730] [INFO] 开始释放ScannerManager资源...
[2025-09-27 19:00:43.732] [INFO] 扫描枪状态变更: Disconnected - 扫描枪连接已断开
[2025-09-27 19:00:43.732] [INFO] ScannerManager资源释放完成
[2025-09-27 19:00:43.733] [INFO] 开始释放ModbusTcpManager资源...
[2025-09-27 19:00:43.735] [INFO] Modbus TCP状态变更: Disconnected - Modbus TCP连接已断开
[2025-09-27 19:00:43.735] [INFO] Modbus TCP连接已断开
[2025-09-27 19:00:43.735] [INFO] ModbusTcpManager资源释放完成
[2025-09-27 19:00:43.736] [INFO] 开始释放EpsonRobotManager资源...
[2025-09-27 19:00:43.740] [INFO] EpsonRobotManager资源释放完成
[2025-09-27 19:00:43.743] [INFO] 开始释放EpsonRobotManager2资源...
[2025-09-27 19:00:43.747] [INFO] EpsonRobotManager2资源释放完成
[2025-09-27 19:00:43.748] [INFO] 开始释放VisionManager资源...
[2025-09-27 19:00:43.750] [INFO] 模拟释放相机资源
[2025-09-27 19:00:43.750] [INFO] VisionManager资源释放完成
[2025-09-27 19:00:43.751] [INFO] 开始释放StatisticsManager资源...
[2025-09-27 19:00:43.752] [INFO] 自动保存任务已停止
[2025-09-27 19:00:43.755] [INFO] StatisticsManager资源释放完成
[2025-09-27 19:00:43.755] [INFO] 其他Manager资源释放完成
[2025-09-27 19:00:43.755] [INFO] 释放DMC1000B控制卡资源...
[2025-09-27 19:00:43.756] [INFO] DMC1000B控制卡未初始化，无需释放
[2025-09-27 19:00:43.756] [INFO] DMC1000B控制卡资源释放完成
[2025-09-27 19:00:43.756] [INFO] 所有Manager资源释放流程完成
[2025-09-27 19:00:43.757] [INFO] 所有资源释放完成，程序即将退出
[2025-09-27 19:00:43.758] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-27 20:10:02.231] [INFO] 程序启动开始
[2025-09-27 20:10:02.232] [INFO] 加载Settings系统配置...
[2025-09-27 20:10:02.237] [INFO] 设置加载成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 20:10:02.238] [INFO] Settings系统配置加载完成
[2025-09-27 20:10:02.238] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-27 20:10:02.266] [INFO] 开始初始化各个Manager...
[2025-09-27 20:10:02.267] [INFO] 初始化DMC1000B控制卡...
[2025-09-27 20:10:02.270] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-27 20:10:02.272] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-27 20:10:02.273] [WARN] DMC1000B控制卡初始化失败，相关功能可能无法正常工作
[2025-09-27 20:10:02.273] [INFO] 初始化基础Manager...
[2025-09-27 20:10:02.278] [INFO] IO状态缓存初始化完成
[2025-09-27 20:10:02.280] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-27 20:10:02.280] [WARN] DMC1000BIO管理器初始化失败
[2025-09-27 20:10:02.284] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-27 20:10:02.321] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 20:10:02.351] [WARN] DMC1000B电机管理器初始化失败
[2025-09-27 20:10:02.352] [INFO] 初始化系统模式管理器...
[2025-09-27 20:10:02.365] [INFO] 从Settings系统加载安全管理器配置成功
[2025-09-27 20:10:02.369] [INFO] 安全管理器实例已创建
[2025-09-27 20:10:02.374] [INFO] 开始初始化MotorManager...
[2025-09-27 20:10:02.389] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-27 20:10:02.396] [INFO] 当前模式已经是 Manual，无需切换
[2025-09-27 20:10:02.400] [INFO] 模拟初始化运动控制卡
[2025-09-27 20:10:02.607] [INFO] 加载了8个电机的默认配置
[2025-09-27 20:10:02.608] [INFO] 电机监控任务已启动
[2025-09-27 20:10:02.608] [INFO] MotorManager初始化完成
[2025-09-27 20:10:02.609] [INFO] 初始化通信Manager...
[2025-09-27 20:10:02.609] [INFO] 电机监控循环开始
[2025-09-27 20:10:02.615] [INFO] 开始初始化ScannerManager...
[2025-09-27 20:10:02.618] [INFO] 扫描枪配置从Settings系统加载: COM1, 9600, 8, One, None
[2025-09-27 20:10:02.635] [INFO] 串口初始化完成
[2025-09-27 20:10:02.638] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-27 20:10:04.701] [ERROR] 连接扫描枪 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerManager.<ConnectAsync>b__23_1() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 145
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerManager.<<ConnectAsync>b__23_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 139
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 20:10:04.703] [WARN] 扫描枪连接失败，但初始化完成，可稍后重试连接
[2025-09-27 20:10:04.703] [INFO] ScannerManager初始化完成
[2025-09-27 20:10:04.709] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-27 20:10:04.710] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-27 20:10:04.712] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-27 20:10:04.713] [INFO] SCARA通信管理器已初始化
[2025-09-27 20:10:04.717] [INFO] ScannerAutoModeManager管理器引用初始化完成
[2025-09-27 20:10:04.719] [INFO] 开始初始化ScannerAutoModeManager...
[2025-09-27 20:10:04.722] [INFO] 开始初始化MultiScannerManager...
[2025-09-27 20:10:04.724] [INFO] 开始初始化扫描枪1...
[2025-09-27 20:10:04.725] [INFO] 从Settings系统加载扫描枪1配置: COM1, 115200
[2025-09-27 20:10:04.727] [INFO] 扫描枪1串口初始化完成
[2025-09-27 20:10:04.728] [INFO] 扫描枪1初始化完成
[2025-09-27 20:10:04.729] [INFO] 开始初始化扫描枪2...
[2025-09-27 20:10:04.729] [INFO] 从Settings系统加载扫描枪2配置: COM2, 115200
[2025-09-27 20:10:04.732] [INFO] 扫描枪2串口初始化完成
[2025-09-27 20:10:04.733] [INFO] 扫描枪2初始化完成
[2025-09-27 20:10:04.733] [INFO] 开始初始化扫描枪3...
[2025-09-27 20:10:04.734] [INFO] 从Settings系统加载扫描枪3配置: COM3, 115200
[2025-09-27 20:10:04.734] [INFO] 扫描枪3串口初始化完成
[2025-09-27 20:10:04.734] [INFO] 扫描枪3初始化完成
[2025-09-27 20:10:04.734] [INFO] MultiScannerManager初始化完成
[2025-09-27 20:10:04.737] [INFO] 开始自动连接并验证所有扫码器...
[2025-09-27 20:10:04.737] [INFO] 正在连接扫码器1...
[2025-09-27 20:10:04.741] [INFO] 扫描枪1状态变更: Connecting - 正在连接扫描枪1...
[2025-09-27 20:10:04.743] [INFO] 扫码器1状态变化: Connecting - 正在连接扫描枪1...
[2025-09-27 20:10:06.482] [ERROR] 连接扫描枪1 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerInstance.<ConnectAsync>b__28_1() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 767
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerInstance.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\MultiScannerManager.cs:行号 761
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 20:10:06.484] [WARN] 扫码器1连接失败
[2025-09-27 20:10:07.044] [INFO] 正在连接扫码器2...
[2025-09-27 20:10:07.045] [INFO] 扫描枪2状态变更: Connecting - 正在连接扫描枪2...
[2025-09-27 20:10:07.045] [INFO] 扫码器2状态变化: Connecting - 正在连接扫描枪2...
[2025-09-27 20:10:07.066] [INFO] 扫描枪2连接成功: COM2
[2025-09-27 20:10:07.066] [INFO] 扫描枪2状态变更: Connected - 扫描枪2连接成功
[2025-09-27 20:10:07.066] [INFO] 扫码器2状态变化: Connected - 扫描枪2连接成功
[2025-09-27 20:10:07.067] [INFO] 扫码器2连接成功
[2025-09-27 20:10:07.124] [INFO] 扫描枪2发送数据: hello
[2025-09-27 20:10:08.135] [INFO] 扫码器2hello验证完成
[2025-09-27 20:10:08.135] [INFO] 扫码器2通信验证成功
[2025-09-27 20:10:08.651] [INFO] 正在连接扫码器3...
[2025-09-27 20:10:08.651] [INFO] 扫描枪3状态变更: Connecting - 正在连接扫描枪3...
[2025-09-27 20:10:08.651] [INFO] 扫码器3状态变化: Connecting - 正在连接扫描枪3...
[2025-09-27 20:50:55.244] [INFO] 程序启动开始
[2025-09-27 20:50:55.245] [INFO] 加载Settings系统配置...
[2025-09-27 20:50:55.250] [INFO] 设置加载成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 20:50:55.250] [INFO] Settings系统配置加载完成
[2025-09-27 20:50:55.250] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-27 20:50:55.260] [INFO] 开始初始化各个Manager...
[2025-09-27 20:50:55.260] [INFO] 初始化DMC1000B控制卡...
[2025-09-27 20:50:55.265] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-27 20:50:55.266] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-27 20:50:55.266] [WARN] DMC1000B控制卡初始化失败，相关功能可能无法正常工作
[2025-09-27 20:50:55.266] [INFO] 初始化基础Manager...
[2025-09-27 20:50:55.271] [INFO] IO状态缓存初始化完成
[2025-09-27 20:50:55.274] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-27 20:50:55.274] [WARN] DMC1000BIO管理器初始化失败
[2025-09-27 20:50:55.277] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-27 20:50:55.277] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 20:50:55.297] [WARN] DMC1000B电机管理器初始化失败
[2025-09-27 20:50:55.297] [INFO] 初始化系统模式管理器...
[2025-09-27 20:50:55.305] [INFO] 从Settings系统加载安全管理器配置成功
[2025-09-27 20:50:55.306] [INFO] 安全管理器实例已创建
[2025-09-27 20:50:55.309] [INFO] 开始初始化MotorManager...
[2025-09-27 20:50:55.310] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-27 20:50:55.310] [INFO] 当前模式已经是 Manual，无需切换
[2025-09-27 20:50:55.312] [INFO] 模拟初始化运动控制卡
[2025-09-27 20:50:55.527] [INFO] 加载了8个电机的默认配置
[2025-09-27 20:50:55.530] [INFO] 电机监控任务已启动
[2025-09-27 20:50:55.530] [INFO] MotorManager初始化完成
[2025-09-27 20:50:55.530] [INFO] 初始化通信Manager...
[2025-09-27 20:50:55.533] [INFO] 电机监控循环开始
[2025-09-27 20:50:55.536] [INFO] 开始初始化ScannerManager...
[2025-09-27 20:50:55.541] [INFO] 扫描枪配置从Settings系统加载: COM1, 9600, 8, One, None
[2025-09-27 20:50:55.546] [INFO] 串口初始化完成
[2025-09-27 20:50:55.551] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-27 20:50:55.560] [ERROR] 连接扫描枪 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerManager.<ConnectAsync>b__23_1() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 145
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerManager.<<ConnectAsync>b__23_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 139
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 20:50:55.564] [WARN] 扫描枪连接失败，但初始化完成，可稍后重试连接
[2025-09-27 20:50:55.564] [INFO] ScannerManager初始化完成
[2025-09-27 20:50:55.571] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-27 20:50:55.571] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-27 20:50:55.572] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-27 20:50:55.573] [INFO] SCARA通信管理器已初始化
[2025-09-27 20:50:55.575] [INFO] ScannerAutoModeManager管理器引用初始化完成
[2025-09-27 20:50:55.576] [INFO] 开始初始化ScannerAutoModeManager...
[2025-09-27 20:50:55.578] [INFO] 开始初始化MultiScannerManager...
[2025-09-27 20:50:55.580] [INFO] 开始初始化扫描枪1...
[2025-09-27 20:50:55.581] [INFO] 从Settings系统加载扫描枪1配置: COM1, 115200
[2025-09-27 20:50:55.582] [INFO] 扫描枪1串口初始化完成
[2025-09-27 20:50:55.582] [INFO] 扫描枪1初始化完成
[2025-09-27 20:50:55.583] [INFO] 开始初始化扫描枪2...
[2025-09-27 20:50:55.583] [INFO] 从Settings系统加载扫描枪2配置: COM2, 115200
[2025-09-27 20:50:55.583] [INFO] 扫描枪2串口初始化完成
[2025-09-27 20:50:55.583] [INFO] 扫描枪2初始化完成
[2025-09-27 20:50:55.583] [INFO] 开始初始化扫描枪3...
[2025-09-27 20:50:55.583] [INFO] 从Settings系统加载扫描枪3配置: COM3, 115200
[2025-09-27 20:50:55.584] [INFO] 扫描枪3串口初始化完成
[2025-09-27 20:50:55.584] [INFO] 扫描枪3初始化完成
[2025-09-27 20:50:55.584] [INFO] MultiScannerManager初始化完成
[2025-09-27 20:50:55.584] [INFO] ScannerAutoModeManager初始化完成
[2025-09-27 20:50:55.586] [INFO] 开始初始化ModbusTcpManager...
[2025-09-27 20:50:55.587] [INFO] Modbus TCP配置: *************:502, SlaveId: 1
[2025-09-27 20:50:55.590] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-27 20:51:00.604] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: *************:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 153
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 20:51:00.606] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-27 20:51:00.606] [INFO] ModbusTcpManager初始化完成
[2025-09-27 20:51:00.619] [INFO] 开始初始化EpsonRobotManager...
[2025-09-27 20:51:00.624] [INFO] 从Settings系统加载Epson机器人1配置成功
[2025-09-27 20:51:00.626] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-27 20:51:00.626] [INFO] EpsonRobotManager初始化完成
[2025-09-27 20:51:00.627] [INFO] 初始化视觉Manager...
[2025-09-27 20:51:00.634] [INFO] 开始初始化VisionManager...
[2025-09-27 20:51:00.635] [INFO] 视觉系统配置从Settings系统加载: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms, 置信度阈值=0.8
[2025-09-27 20:51:00.637] [INFO] 模拟初始化相机，索引: 0
[2025-09-27 20:51:01.140] [INFO] 相机初始化成功
[2025-09-27 20:51:01.143] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-27 20:51:01.144] [INFO] 视觉配置加载完成
[2025-09-27 20:51:01.144] [INFO] VisionManager初始化完成
[2025-09-27 20:51:01.145] [INFO] 初始化数据Manager...
[2025-09-27 20:51:01.154] [INFO] 开始初始化StatisticsManager...
[2025-09-27 20:51:01.156] [INFO] 统计管理器配置: 数据目录=Export, 缓存大小=1000, 自动保存间隔=24分钟
[2025-09-27 20:51:01.174] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-27 20:51:01.174] [INFO] 历史数据加载完成
[2025-09-27 20:51:01.174] [INFO] StatisticsManager初始化完成
[2025-09-27 20:51:01.174] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-27 20:51:01.174] [INFO] 所有Manager初始化完成
[2025-09-27 20:51:01.217] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 20:51:01.217] [INFO] 创建并缓存面板: vision-position
[2025-09-27 20:51:01.218] [INFO] 主界面布局创建完成
[2025-09-27 20:51:01.219] [INFO] 时间更新定时器初始化完成
[2025-09-27 20:51:01.219] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-27 20:51:01.302] [INFO] 开始初始化系统
[2025-09-27 20:51:01.303] [INFO] 初始化业务逻辑
[2025-09-27 20:51:01.304] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-27 20:51:01.304] [INFO] IO管理器初始化完成
[2025-09-27 20:51:01.304] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-27 20:51:01.304] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 20:51:01.304] [INFO] 电机管理器初始化完成
[2025-09-27 20:51:01.305] [INFO] IO事件订阅完成
[2025-09-27 20:51:01.305] [INFO] 电机事件订阅完成
[2025-09-27 20:51:01.305] [INFO] 业务层交互机制建立完成
[2025-09-27 20:51:01.305] [INFO] 业务逻辑初始化完成
[2025-09-27 20:51:01.306] [INFO] 执行UI界面刷新
[2025-09-27 20:51:01.308] [INFO] UI界面刷新完成
[2025-09-27 20:51:01.308] [INFO] 系统初始化完成
[2025-09-27 20:51:02.828] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 20:51:02.828] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-27 20:51:02.834] [INFO] UI参数显示已从Settings系统更新
[2025-09-27 20:51:02.834] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-27 20:51:02.835] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-27 20:51:02.835] [INFO] 创建并缓存面板: motor-flip-params
[2025-09-27 20:51:04.240] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-27 20:51:04.240] [INFO] Epson机器人管理器初始化完成
[2025-09-27 20:51:04.255] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-27 20:51:04.255] [INFO] 创建并缓存面板: robot6-control
[2025-09-27 20:51:04.974] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-27 20:51:04.974] [INFO] 创建并缓存面板: scara-comm
[2025-09-27 20:51:05.810] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
[2025-09-27 20:51:05.810] [INFO] 创建并缓存面板: io-read
[2025-09-27 20:51:06.964] [INFO] 生产日志管理面板初始化完成 - 按HTML原型设计
[2025-09-27 20:51:06.964] [INFO] 创建并缓存面板: log-time
[2025-09-27 20:51:11.272] [ERROR] IO管理器初始化超时，无法启动IO监控
[2025-09-27 20:51:15.503] [INFO] Scara示教功能面板初始化完成 - 按HTML原型设计
[2025-09-27 20:51:15.503] [INFO] 创建并缓存面板: scara-teach
[2025-09-27 20:51:34.643] [INFO] 翻转电机示教面板业务逻辑初始化完成
[2025-09-27 20:51:34.649] [INFO] 翻转电机示教面板初始化完成 - 按HTML原型设计
[2025-09-27 20:51:34.649] [INFO] 创建并缓存面板: motor-flip-teach
[2025-09-27 21:05:26.301] [INFO] 程序启动开始
[2025-09-27 21:05:26.303] [INFO] 加载Settings系统配置...
[2025-09-27 21:05:26.311] [INFO] 设置加载成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 21:05:26.312] [INFO] Settings系统配置加载完成
[2025-09-27 21:05:26.312] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-27 21:05:26.383] [INFO] 开始初始化各个Manager...
[2025-09-27 21:05:26.384] [INFO] 初始化DMC1000B控制卡...
[2025-09-27 21:05:26.393] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-27 21:05:26.396] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-27 21:05:26.397] [WARN] DMC1000B控制卡初始化失败，相关功能可能无法正常工作
[2025-09-27 21:05:26.397] [INFO] 初始化基础Manager...
[2025-09-27 21:05:26.404] [INFO] IO状态缓存初始化完成
[2025-09-27 21:05:26.407] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-27 21:05:26.408] [WARN] DMC1000BIO管理器初始化失败
[2025-09-27 21:05:26.412] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-27 21:05:26.460] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 21:05:26.478] [WARN] DMC1000B电机管理器初始化失败
[2025-09-27 21:05:26.479] [INFO] 初始化系统模式管理器...
[2025-09-27 21:05:26.491] [INFO] 从Settings系统加载安全管理器配置成功
[2025-09-27 21:05:26.494] [INFO] 安全管理器实例已创建
[2025-09-27 21:05:26.498] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-27 21:05:26.499] [INFO] 开始初始化MotorManager...
[2025-09-27 21:05:26.500] [INFO] 当前模式已经是 Manual，无需切换
[2025-09-27 21:05:26.502] [INFO] 模拟初始化运动控制卡
[2025-09-27 21:05:26.710] [INFO] 加载了8个电机的默认配置
[2025-09-27 21:05:26.712] [INFO] 电机监控任务已启动
[2025-09-27 21:05:26.712] [INFO] MotorManager初始化完成
[2025-09-27 21:05:26.712] [INFO] 初始化通信Manager...
[2025-09-27 21:05:26.714] [INFO] 电机监控循环开始
[2025-09-27 21:05:26.715] [INFO] 开始初始化ScannerManager...
[2025-09-27 21:05:26.719] [INFO] 扫描枪配置从Settings系统加载: COM1, 9600, 8, One, None
[2025-09-27 21:05:26.730] [INFO] 串口初始化完成
[2025-09-27 21:05:26.731] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-27 21:05:30.503] [ERROR] 连接扫描枪 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerManager.<ConnectAsync>b__23_1() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 145
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerManager.<<ConnectAsync>b__23_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ScannerManager.cs:行号 139
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 21:05:30.507] [WARN] 扫描枪连接失败，但初始化完成，可稍后重试连接
[2025-09-27 21:05:30.507] [INFO] ScannerManager初始化完成
[2025-09-27 21:05:30.515] [INFO] 扫描枪1使用硬编码默认配置: COM1, 115200
[2025-09-27 21:05:30.516] [INFO] 扫描枪2使用硬编码默认配置: COM2, 115200
[2025-09-27 21:05:30.517] [INFO] 扫描枪3使用硬编码默认配置: COM3, 115200
[2025-09-27 21:05:30.519] [INFO] SCARA通信管理器已初始化
[2025-09-27 21:05:30.521] [INFO] ScannerAutoModeManager管理器引用初始化完成
[2025-09-27 21:05:30.524] [INFO] 开始初始化ScannerAutoModeManager...
[2025-09-27 21:05:30.527] [INFO] 开始初始化MultiScannerManager...
[2025-09-27 21:05:30.529] [INFO] 开始初始化扫描枪1...
[2025-09-27 21:05:30.531] [INFO] 从Settings系统加载扫描枪1配置: COM1, 115200
[2025-09-27 21:05:30.534] [INFO] 扫描枪1串口初始化完成
[2025-09-27 21:05:30.535] [INFO] 扫描枪1初始化完成
[2025-09-27 21:05:30.536] [INFO] 开始初始化扫描枪2...
[2025-09-27 21:05:30.536] [INFO] 从Settings系统加载扫描枪2配置: COM2, 115200
[2025-09-27 21:05:30.536] [INFO] 扫描枪2串口初始化完成
[2025-09-27 21:05:30.537] [INFO] 扫描枪2初始化完成
[2025-09-27 21:05:30.537] [INFO] 开始初始化扫描枪3...
[2025-09-27 21:05:30.537] [INFO] 从Settings系统加载扫描枪3配置: COM3, 115200
[2025-09-27 21:05:30.538] [INFO] 扫描枪3串口初始化完成
[2025-09-27 21:05:30.538] [INFO] 扫描枪3初始化完成
[2025-09-27 21:05:30.539] [INFO] MultiScannerManager初始化完成
[2025-09-27 21:05:30.539] [INFO] ScannerAutoModeManager初始化完成
[2025-09-27 21:05:30.543] [INFO] 开始初始化ModbusTcpManager...
[2025-09-27 21:05:30.545] [INFO] Modbus TCP配置: *************:502, SlaveId: 1
[2025-09-27 21:05:30.549] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-27 21:05:35.663] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: *************:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 153
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 21:05:35.666] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-27 21:05:35.666] [INFO] ModbusTcpManager初始化完成
[2025-09-27 21:05:35.673] [INFO] 开始初始化EpsonRobotManager...
[2025-09-27 21:05:35.675] [INFO] 从Settings系统加载Epson机器人1配置成功
[2025-09-27 21:05:35.675] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-27 21:05:35.675] [INFO] EpsonRobotManager初始化完成
[2025-09-27 21:05:35.675] [INFO] 初始化视觉Manager...
[2025-09-27 21:05:35.678] [INFO] 开始初始化VisionManager...
[2025-09-27 21:05:35.679] [INFO] 视觉系统配置从Settings系统加载: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms, 置信度阈值=0.8
[2025-09-27 21:05:35.680] [INFO] 模拟初始化相机，索引: 0
[2025-09-27 21:05:36.190] [INFO] 相机初始化成功
[2025-09-27 21:05:36.194] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-27 21:05:36.195] [INFO] 视觉配置加载完成
[2025-09-27 21:05:36.196] [INFO] VisionManager初始化完成
[2025-09-27 21:05:36.197] [INFO] 初始化数据Manager...
[2025-09-27 21:05:36.213] [INFO] 开始初始化StatisticsManager...
[2025-09-27 21:05:36.215] [INFO] 统计管理器配置: 数据目录=Export, 缓存大小=1000, 自动保存间隔=24分钟
[2025-09-27 21:05:36.243] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-27 21:05:36.243] [INFO] 历史数据加载完成
[2025-09-27 21:05:36.243] [INFO] StatisticsManager初始化完成
[2025-09-27 21:05:36.243] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-27 21:05:36.244] [INFO] 所有Manager初始化完成
[2025-09-27 21:05:36.280] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 21:05:36.280] [INFO] 创建并缓存面板: vision-position
[2025-09-27 21:05:36.281] [INFO] 主界面布局创建完成
[2025-09-27 21:05:36.283] [INFO] 时间更新定时器初始化完成
[2025-09-27 21:05:36.283] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-27 21:05:36.354] [INFO] 开始初始化系统
[2025-09-27 21:05:36.355] [INFO] 初始化业务逻辑
[2025-09-27 21:05:36.356] [ERROR] DMC1000B控制卡不可用，IO管理器初始化失败
[2025-09-27 21:05:36.356] [INFO] IO管理器初始化完成
[2025-09-27 21:05:36.356] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-27 21:05:36.425] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡不可用，电机管理器初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-27 21:05:36.425] [INFO] 电机管理器初始化完成
[2025-09-27 21:05:36.427] [INFO] IO事件订阅完成
[2025-09-27 21:05:36.427] [INFO] 电机事件订阅完成
[2025-09-27 21:05:36.427] [INFO] 业务层交互机制建立完成
[2025-09-27 21:05:36.427] [INFO] 业务逻辑初始化完成
[2025-09-27 21:05:36.428] [INFO] 执行UI界面刷新
[2025-09-27 21:05:36.430] [INFO] UI界面刷新完成
[2025-09-27 21:05:36.430] [INFO] 系统初始化完成
[2025-09-27 21:05:39.023] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-27 21:05:39.024] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-27 21:05:39.030] [INFO] UI参数显示已从Settings系统更新
[2025-09-27 21:05:39.031] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-27 21:05:39.031] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-27 21:05:39.031] [INFO] 创建并缓存面板: motor-flip-params
[2025-09-27 21:05:42.141] [INFO] 翻转电机示教面板业务逻辑初始化完成
[2025-09-27 21:05:42.151] [INFO] 翻转电机示教面板初始化完成 - 按HTML原型设计
[2025-09-27 21:05:42.152] [INFO] 创建并缓存面板: motor-flip-teach
[2025-09-27 21:06:09.795] [INFO] 皮带电机控制面板初始化完成 - 双电机设计
[2025-09-27 21:06:09.795] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-27 21:06:09.802] [INFO] 皮带电机轴3参数设置成功: 脉冲当量=0.01mm/pulse, 最大速度=100mm/s
[2025-09-27 21:06:09.803] [INFO] 皮带电机轴2参数设置成功: 脉冲当量=0.01mm/pulse, 最大速度=100mm/s
[2025-09-27 21:06:09.807] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 21:06:09.807] [INFO] 皮带电机配置保存到Settings系统成功
[2025-09-27 21:06:09.808] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 21:06:09.808] [INFO] 皮带电机配置保存到Settings系统成功
[2025-09-27 21:06:09.808] [INFO] 电机参数显示刷新完成
[2025-09-27 21:06:09.809] [INFO] 皮带电机参数初始化完成
[2025-09-27 21:06:09.809] [INFO] 皮带电机控制面板业务逻辑初始化完成
[2025-09-27 21:06:09.809] [INFO] 创建并缓存面板: motor-belt
[2025-09-27 21:06:24.749] [INFO] 皮带电机轴3参数设置成功: 脉冲当量=0.01mm/pulse, 最大速度=100mm/s
[2025-09-27 21:06:24.750] [INFO] 输入皮带电机参数已更新: 脉冲当量=0.01, 最大速度=100, 加速度=300, 点动距离=10
[2025-09-27 21:06:24.752] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 21:06:24.754] [INFO] 皮带电机配置保存到Settings系统成功
[2025-09-27 21:06:33.340] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-27 21:06:33.341] [INFO] 保存Settings系统配置...
[2025-09-27 21:06:33.342] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-27 21:06:33.342] [INFO] Settings系统配置保存完成
[2025-09-27 21:06:33.345] [INFO] 开始按顺序释放各个Manager资源...
[2025-09-27 21:06:33.345] [INFO] 清理UI资源和面板缓存...
[2025-09-27 21:06:33.346] [INFO] 时间更新定时器资源已释放
[2025-09-27 21:06:33.347] [INFO] 开始清理面板缓存，共4个面板
[2025-09-27 21:06:33.357] [INFO] 翻转电机控制面板资源释放完成
[2025-09-27 21:06:33.362] [INFO] 翻转电机示教面板资源释放完成
[2025-09-27 21:06:33.364] [INFO] 皮带电机控制面板资源释放完成
[2025-09-27 21:06:33.368] [INFO] 面板缓存字典已清空
[2025-09-27 21:06:33.368] [INFO] 面板缓存清理完成
[2025-09-27 21:06:33.368] [INFO] UI资源清理完成
[2025-09-27 21:06:33.368] [INFO] 释放工作流管理器资源...
[2025-09-27 21:06:33.371] [INFO] 开始释放WorkflowManager资源...
[2025-09-27 21:06:33.373] [WARN] 皮带电机自动控制未在运行
[2025-09-27 21:06:33.374] [INFO] 工作流事件取消订阅完成
[2025-09-27 21:06:33.374] [INFO] WorkflowManager资源释放完成
[2025-09-27 21:06:33.374] [INFO] 工作流管理器资源释放完成
[2025-09-27 21:06:33.374] [INFO] 释放启动自检管理器资源...
[2025-09-27 21:06:33.376] [INFO] 开始释放启动自检管理器资源...
[2025-09-27 21:06:33.376] [INFO] 启动自检管理器资源释放完成
[2025-09-27 21:06:33.376] [INFO] 启动自检管理器资源释放完成
[2025-09-27 21:06:33.376] [INFO] 释放DMC1000B电机管理器资源...
[2025-09-27 21:06:33.378] [INFO] 开始释放DMC1000B资源...
[2025-09-27 21:06:33.384] [INFO] 停止所有电机部分失败
[2025-09-27 21:06:33.386] [INFO] DMC1000B资源释放完成
[2025-09-27 21:06:33.386] [INFO] DMC1000B电机管理器资源释放完成
[2025-09-27 21:06:33.386] [INFO] 释放DMC1000BIO管理器资源...
[2025-09-27 21:06:33.389] [INFO] DMC1000BIO管理器资源释放完成
[2025-09-27 21:06:33.389] [INFO] 释放其他Manager资源...
[2025-09-27 21:06:33.390] [INFO] 开始释放MotorManager资源...
[2025-09-27 21:06:33.395] [INFO] 电机0停止运动
[2025-09-27 21:06:33.395] [INFO] 电机3停止运动
[2025-09-27 21:06:33.395] [INFO] 电机5停止运动
[2025-09-27 21:06:33.397] [INFO] 电机1停止运动
[2025-09-27 21:06:33.397] [INFO] 电机2停止运动
[2025-09-27 21:06:33.397] [INFO] 电机6停止运动
[2025-09-27 21:06:33.397] [INFO] 电机7停止运动
[2025-09-27 21:06:33.398] [INFO] 电机4停止运动
[2025-09-27 21:06:33.398] [INFO] 所有电机已停止
[2025-09-27 21:06:33.435] [INFO] 电机监控循环被取消
[2025-09-27 21:06:33.436] [INFO] 电机监控循环结束
[2025-09-27 21:06:33.436] [INFO] 电机监控任务已停止
[2025-09-27 21:06:33.436] [INFO] 模拟释放运动控制卡资源
[2025-09-27 21:06:33.436] [INFO] MotorManager资源释放完成
[2025-09-27 21:06:33.438] [INFO] 开始释放ScannerManager资源...
[2025-09-27 21:06:33.440] [INFO] 扫描枪状态变更: Disconnected - 扫描枪连接已断开
[2025-09-27 21:06:33.440] [INFO] ScannerManager资源释放完成
[2025-09-27 21:06:33.441] [INFO] 开始释放ModbusTcpManager资源...
[2025-09-27 21:06:33.444] [INFO] Modbus TCP状态变更: Disconnected - Modbus TCP连接已断开
[2025-09-27 21:06:33.444] [INFO] Modbus TCP连接已断开
[2025-09-27 21:06:33.444] [INFO] ModbusTcpManager资源释放完成
[2025-09-27 21:06:33.446] [INFO] 开始释放EpsonRobotManager资源...
[2025-09-27 21:06:33.452] [INFO] EpsonRobotManager资源释放完成
[2025-09-27 21:06:33.456] [INFO] 开始释放EpsonRobotManager2资源...
[2025-09-27 21:06:33.460] [INFO] EpsonRobotManager2资源释放完成
[2025-09-27 21:06:33.461] [INFO] 开始释放VisionManager资源...
[2025-09-27 21:06:33.463] [INFO] 模拟释放相机资源
[2025-09-27 21:06:33.463] [INFO] VisionManager资源释放完成
[2025-09-27 21:06:33.465] [INFO] 开始释放StatisticsManager资源...
[2025-09-27 21:06:33.466] [INFO] 自动保存任务已停止
[2025-09-27 21:06:33.469] [INFO] StatisticsManager资源释放完成
[2025-09-27 21:06:33.470] [INFO] 其他Manager资源释放完成
[2025-09-27 21:06:33.470] [INFO] 释放DMC1000B控制卡资源...
[2025-09-27 21:06:33.471] [INFO] DMC1000B控制卡未初始化，无需释放
[2025-09-27 21:06:33.472] [INFO] DMC1000B控制卡资源释放完成
[2025-09-27 21:06:33.472] [INFO] 所有Manager资源释放流程完成
[2025-09-27 21:06:33.472] [INFO] 所有资源释放完成，程序即将退出
[2025-09-27 21:06:33.473] [INFO] 程序正在关闭，开始释放所有Manager资源...
