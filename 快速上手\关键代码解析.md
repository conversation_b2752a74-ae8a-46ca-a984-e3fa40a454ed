# HR2项目关键代码解析

## 📋 概述

本文档深入解析HR2项目中的关键类和方法，提供详细的代码示例和实现逻辑说明。

## 🎛️ WorkflowManager核心实现

### 单例模式实现
```csharp
public class WorkflowManager
{
    #region 单例模式
    private static readonly Lazy<WorkflowManager> _instance = 
        new Lazy<WorkflowManager>(() => new WorkflowManager());
    public static WorkflowManager Instance => _instance.Value;
    private WorkflowManager() { }
    #endregion
}
```

**设计要点**:
- 使用`Lazy<T>`确保线程安全的延迟初始化
- 私有构造函数防止外部实例化
- 全局唯一实例，便于状态管理

### 智能启动逻辑
```csharp
public async Task<bool> StartWorkflowAsync(string productId = "")
{
    return await ExceptionHelper.SafeExecuteAsync(async () =>
    {
        if (!_isWorkflowEnabled)
        {
            LogHelper.Warning("工作流已禁用，无法启动");
            return false;
        }

        // 智能启动逻辑：如果当前是暂停状态，则发送恢复命令
        if (_currentState == WorkflowState.Paused)
        {
            LogHelper.Info("检测到暂停状态，执行恢复操作");
            return await ResumeWorkflowAsync();
        }
        else if (_currentState != WorkflowState.Idle)
        {
            LogHelper.Warning($"工作流正在运行中，当前状态: {_currentState}");
            return false;
        }

        // 正常启动流程
        return await ExecuteWorkflowStartSequenceAsync(productId);
    }, false, "启动工作流");
}
```

**关键特性**:
- **智能判断**: 根据当前状态选择启动或恢复
- **状态检查**: 防止重复启动
- **异常安全**: 使用SafeExecuteAsync包装
- **日志记录**: 完整的操作日志

### 事件订阅机制
```csharp
private Task SubscribeToManagerEventsAsync()
{
    try
    {
        // 订阅皮带电机控制器事件
        if (_beltMotorController != null)
        {
            _beltMotorController.StateChanged += OnBeltMotorStateChanged;
            _beltMotorController.ErrorOccurred += OnBeltMotorErrorOccurred;
        }

        // 订阅扫码器自动模式管理器事件
        if (_scannerAutoModeManager != null)
        {
            _scannerAutoModeManager.AllScannersCompleted += OnAllScannersCompleted;
            _scannerAutoModeManager.AutoModeStatusChanged += OnScannerAutoModeStatusChanged;
        }

        return Task.CompletedTask;
    }
    catch (Exception ex)
    {
        LogHelper.Error("订阅Manager事件时发生异常", ex);
        return Task.CompletedTask;
    }
}
```

## 🔧 BeltMotorAutoModeController实现

### 传感器控制循环
```csharp
private async Task InputBeltControlLoopAsync(CancellationToken cancellationToken)
{
    bool lastSensorState = false;
    
    try
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                // 读取传感器状态
                bool currentSensorState = await _ioManager.ReadInputAsync(INPUT_SENSOR_IO);

                // 检查电机运行状态
                bool isMotorRunning = false;
                try
                {
                    short motionStatus = _motorManager.GetMotorMotionStatus(INPUT_BELT_AXIS);
                    isMotorRunning = (motionStatus == DMC1000BMotorManager.MOTION_RUNNING);
                }
                catch (Exception statusEx)
                {
                    LogHelper.Debug($"获取输入皮带电机状态失败: {statusEx.Message}");
                    isMotorRunning = false; // 脱机模式假设未运行
                }

                // 根据传感器状态控制电机
                if (currentSensorState == false && isMotorRunning)
                {
                    // 传感器为0（检测到产品），停止皮带让产品停留
                    LogHelper.Info("输入皮带传感器为0（检测到产品），停止皮带电机");
                    bool stopResult = await _motorManager.StopMotorAsync(INPUT_BELT_AXIS);
                    if (!stopResult)
                    {
                        LogHelper.Error("输入皮带电机停止失败");
                    }
                    await Task.Delay(MOTOR_STOP_DELAY_MS, cancellationToken);
                }
                else if (currentSensorState == true && !isMotorRunning)
                {
                    // 传感器为1（无产品），启动皮带继续传送
                    LogHelper.Info("输入皮带传感器为1（无产品），启动皮带电机");
                    bool startResult = await _motorManager.BeltMotorContinuousRunAsync(INPUT_BELT_AXIS, false);
                    if (!startResult)
                    {
                        LogHelper.Error("输入皮带电机启动失败");
                    }
                }

                await Task.Delay(SENSOR_CHECK_INTERVAL_MS, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                break; // 正常取消
            }
            catch (Exception ex)
            {
                LogHelper.Error("输入皮带控制循环异常", ex);
                await Task.Delay(1000, cancellationToken); // 异常时延迟1秒
            }
        }
    }
    catch (OperationCanceledException)
    {
        LogHelper.Info("输入皮带控制循环已取消");
    }
}
```

**实现要点**:
- **50ms检查间隔**: 确保及时响应传感器变化
- **状态检查**: 避免重复操作电机
- **异常处理**: 区分正常取消和异常情况
- **脱机模式**: 支持无硬件的开发调试

## 🔌 DMC1000BIOManager实现

### IO状态监控
```csharp
private async Task MonitorIOStatesAsync()
{
    while (!_monitoringCancellationToken.IsCancellationRequested)
    {
        try
        {
            // 读取所有IO状态
            var currentStates = await ReadAllIOStatesAsync();
            
            // 检测状态变化
            var changedIOs = new List<(string ioNumber, bool newState, bool oldState)>();
            
            lock (_lockObject)
            {
                foreach (var kvp in currentStates)
                {
                    string ioNumber = kvp.Key;
                    bool newState = kvp.Value;
                    
                    if (_lastIOStates.TryGetValue(ioNumber, out bool oldState))
                    {
                        if (oldState != newState)
                        {
                            changedIOs.Add((ioNumber, newState, oldState));
                        }
                    }
                    else
                    {
                        // 首次读取，记录初始状态
                        changedIOs.Add((ioNumber, newState, false));
                    }
                    
                    _lastIOStates[ioNumber] = newState;
                }
            }

            // 触发状态变化事件
            foreach (var (ioNumber, newState, oldState) in changedIOs)
            {
                var eventArgs = new IOInputStateChangedEventArgs(ioNumber, newState, oldState);
                IOInputStateChanged?.Invoke(this, eventArgs);
            }

            // 触发批量状态变化事件
            if (changedIOs.Count > 0)
            {
                var batchEventArgs = new BatchIOStateChangedEventArgs(currentStates, changedIOs);
                BatchIOStateChanged?.Invoke(this, batchEventArgs);
            }

            await Task.Delay(_monitorIntervalMs, _monitoringCancellationToken);
        }
        catch (OperationCanceledException)
        {
            break;
        }
        catch (Exception ex)
        {
            LogHelper.Error("IO状态监控异常", ex);
            await Task.Delay(1000, _monitoringCancellationToken);
        }
    }
}
```

### IO读写操作
```csharp
public async Task<bool> ReadInputAsync(string ioNumber)
{
    return await ExceptionHelper.SafeExecuteAsync(async () =>
    {
        await Task.CompletedTask; // 消除CS1998警告
        
        if (!_ioPortDefinitions.TryGetValue(ioNumber, out IOPortDefinition portDef))
        {
            throw new ArgumentException($"未找到IO端口定义: {ioNumber}");
        }

        if (portDef.Type != IOPortType.Input && portDef.Type != IOPortType.AxisSpecialInput)
        {
            throw new InvalidOperationException($"IO {ioNumber} 不是输入类型");
        }

        bool result;
        if (portDef.Type == IOPortType.AxisSpecialInput)
        {
            // 专用轴信号读取
            result = ReadAxisSpecialInput(portDef);
        }
        else
        {
            // 普通IO读取
            var ioResult = csDmc1000.DMC1000.d1000_in_bit(portDef.BitNumber);
            result = (ioResult == 1);
        }

        LogHelper.Debug($"读取输入IO {ioNumber} (位号{portDef.BitNumber}): {(result ? "ON" : "OFF")}");
        return result;

    }, false, $"读取输入IO {ioNumber}");
}
```

## 🤖 EpsonRobotManager通信实现

### TCP连接管理
```csharp
public async Task<bool> ConnectAsync()
{
    return await ExceptionHelper.SafeExecuteAsync(async () =>
    {
        if (_startStopConnected && _dataConnected)
        {
            LogHelper.Info("Epson机器人已连接");
            return true;
        }

        LogHelper.Info($"开始连接Epson机器人: {_config.IPAddress}");

        // 连接启动/停止端口
        if (!_startStopConnected)
        {
            _startStopClient = new TcpClient();
            await _startStopClient.ConnectAsync(_config.IPAddress, _config.ControlPort);
            _startStopStream = _startStopClient.GetStream();
            _startStopConnected = true;
            LogHelper.Info($"启动/停止TCP连接成功: {_config.IPAddress}:{_config.ControlPort}");
        }

        // 连接数据端口
        if (!_dataConnected)
        {
            _dataClient = new TcpClient();
            await _dataClient.ConnectAsync(_config.IPAddress, _config.DataPort);
            _dataStream = _dataClient.GetStream();
            _dataConnected = true;
            LogHelper.Info($"数据TCP连接成功: {_config.IPAddress}:{_config.DataPort}");
        }

        LogHelper.Info("Epson机器人连接完成");
        return true;

    }, false, "连接Epson机器人");
}
```

### 异步命令发送
```csharp
private async Task<EpsonRobotResponse> SendCommandAsync(EpsonRobotCommand command, string connectionType)
{
    try
    {
        NetworkStream stream = connectionType == "StartStop" ? _startStopStream : _dataStream;
        
        if (stream == null)
        {
            throw new InvalidOperationException($"{connectionType}连接未建立");
        }

        // 构建命令字符串
        string commandString = BuildCommandString(command);
        byte[] commandBytes = Encoding.ASCII.GetBytes(commandString + "\r\n");

        // 发送命令
        await stream.WriteAsync(commandBytes, 0, commandBytes.Length);
        LogHelper.Debug($"发送{connectionType}命令: {commandString}");

        // 等待响应
        using (var cts = new CancellationTokenSource(command.TimeoutMs))
        {
            byte[] buffer = new byte[1024];
            int bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length, cts.Token);
            
            string responseString = Encoding.ASCII.GetString(buffer, 0, bytesRead).Trim();
            LogHelper.Debug($"接收{connectionType}响应: {responseString}");

            return ParseResponse(responseString, command);
        }
    }
    catch (Exception ex)
    {
        LogHelper.Error($"发送{connectionType}命令失败", ex);
        return new EpsonRobotResponse
        {
            IsSuccess = false,
            ErrorMessage = ex.Message,
            CommandId = command.CommandId
        };
    }
}
```

## 🖥️ MainForm UI实现

### 线程安全UI更新
```csharp
private void OnIOInputStateChanged(object sender, IOInputStateChangedEventArgs e)
{
    try
    {
        // 确保在UI线程中执行
        if (this.InvokeRequired)
        {
            this.Invoke(new Action<object, IOInputStateChangedEventArgs>(OnIOInputStateChanged), sender, e);
            return;
        }

        // 更新UI显示
        UpdateIODisplaySafe(e.IONumber, e.NewState);
        
        LogHelper.Debug($"UI更新IO状态: {e.IONumber} = {(e.NewState ? "ON" : "OFF")}");
    }
    catch (Exception ex)
    {
        LogHelper.Error("处理IO状态变化事件失败", ex);
    }
}

private void UpdateIODisplaySafe(string ioNumber, bool state)
{
    // 查找对应的UI控件并更新
    if (_currentPanel is IOReadPanel ioPanel)
    {
        ioPanel.UpdateIOState(ioNumber, state);
    }
}
```

### 全局控制按钮实现
```csharp
private async void StartBtn_Click(object sender, EventArgs e)
{
    try
    {
        var currentMode = SystemModeManager.Instance.CurrentMode;

        // 检查是否在自动模式下
        if (currentMode != SystemMode.Automatic)
        {
            MessageBox.Show("启动功能只能在自动模式下使用。\n\n请先通过\"安全设置\"切换到自动模式。",
                "模式错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        _statusLabel.Text = "系统状态: 正在启动自动化流程...";
        _statusLabel.ForeColor = ColorTranslator.FromHtml("#f39c12");

        // 在自动模式下启动自动化流程
        bool result = await SystemModeManager.Instance.StartAutomationAsync();

        if (result)
        {
            _statusLabel.Text = "系统状态: 自动化流程运行中";
            _statusLabel.ForeColor = ColorTranslator.FromHtml("#27ae60");
        }
        else
        {
            _statusLabel.Text = "系统状态: 启动失败";
            _statusLabel.ForeColor = ColorTranslator.FromHtml("#e74c3c");
            MessageBox.Show("自动化流程启动失败，请检查系统状态", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
    catch (Exception ex)
    {
        LogHelper.Error("启动按钮点击处理失败", ex);
        _statusLabel.Text = "系统状态: 启动异常";
        _statusLabel.ForeColor = ColorTranslator.FromHtml("#e74c3c");
        MessageBox.Show($"启动异常：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
    }
}
```

## 🛡️ 异常处理机制

### SafeExecuteAsync实现
```csharp
public static async Task<T> SafeExecuteAsync<T>(Func<Task<T>> operation, T defaultValue, string operationName)
{
    try
    {
        return await operation();
    }
    catch (OperationCanceledException)
    {
        LogHelper.Info($"{operationName}操作被取消");
        return defaultValue;
    }
    catch (Exception ex)
    {
        LogHelper.Error($"{operationName}操作失败", ex);
        return defaultValue;
    }
}
```

**设计优势**:
- **统一异常处理**: 所有异步操作使用相同的异常处理模式
- **操作取消支持**: 正确处理CancellationToken
- **日志记录**: 自动记录操作名称和异常信息
- **默认值返回**: 异常时返回安全的默认值

这些关键代码展示了HR2项目的核心实现思路，体现了工业软件对稳定性、可靠性和可维护性的高要求。
