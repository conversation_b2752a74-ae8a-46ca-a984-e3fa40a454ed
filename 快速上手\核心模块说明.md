# HR2项目核心模块说明

## 📋 模块概览

HR2项目按功能划分为以下核心模块，每个模块都有明确的职责和边界：

## 🎛️ 工作流管理模块

### WorkflowManager（工作流协调器）
**文件位置**: `Managers/WorkflowManager.cs`  
**职责**: 统一协调各种AutoMode控制器的工作流程

#### 核心功能
- **工作流控制**: StartWorkflowAsync、StopWorkflowAsync、PauseWorkflowAsync、ResetWorkflowAsync
- **状态管理**: WorkflowState状态机（Idle、WaitingForScan、MotorMoving、RobotOperating、VisionDetecting、Error）
- **智能启动**: 根据当前状态自动选择启动或恢复操作
- **事件协调**: 订阅和处理各AutoMode控制器的事件

#### 关键方法
```csharp
// 启动工作流（智能判断启动或恢复）
public async Task<bool> StartWorkflowAsync(string productId = "")

// 暂停工作流（保存当前状态）
public async Task<bool> PauseWorkflowAsync()

// 停止工作流（重置到空闲状态）
public async Task<bool> StopWorkflowAsync()

// 重置工作流（清理所有状态）
public async Task<bool> ResetWorkflowAsync()
```

#### 管理的控制器
- BeltMotorAutoModeController（皮带电机）
- ScannerAutoModeManager（扫码器）
- EpsonRobotAutoModeController（6轴机器人）
- ScaraAutoModeController（SCARA翻转电机）

## 🔧 硬件控制模块

### 1. DMC1000B控制卡管理

#### DMC1000BCardManager（控制卡管理器）
**文件位置**: `Managers/DMC1000BCardManager.cs`  
**职责**: DMC1000B控制卡的统一管理和资源控制

**核心功能**:
- 控制卡初始化和连接管理
- 全局唯一的控制卡实例
- 控制卡状态监控
- 资源释放和错误处理

#### DMC1000BMotorManager（电机管理器）
**文件位置**: `Managers/DMC1000BMotorManager.cs`  
**职责**: 4轴步进电机的精确控制

**支持的电机轴**:
- **轴0**: 左翻转电机
- **轴1**: 右翻转电机  
- **轴2**: 输出皮带电机
- **轴3**: 输入皮带电机

**核心功能**:
```csharp
// 翻转电机控制
Task<bool> RotateFlipMotorAsync(short axis, double angle)  // 角度旋转
Task<bool> HomeFlipMotorAsync(short axis)                  // 回原点
Task<bool> SaveFlipMotorPositionAsync(short axis, int positionIndex)  // 保存位置
Task<bool> MoveToSavedPositionAsync(short axis, int positionIndex)    // 移动到保存位置

// 皮带电机控制
Task<bool> BeltMotorContinuousRunAsync(short axis, bool direction)    // 连续运行
Task<bool> BeltMotorJogAsync(short axis, double distance, bool direction)  // 点动
Task<bool> StopMotorAsync(short axis)                     // 停止电机
```

#### DMC1000BIOManager（IO管理器）
**文件位置**: `Managers/DMC1000BIOManager.cs`  
**职责**: IO输入输出的统一管理

**支持的IO类型**:
- **基础IO**: I0000-I0015, O0000-O0015
- **扩展IO**: I0100-I0115, O0100-O0115  
- **专用轴信号**: ORG、PEL、NEL

**核心功能**:
```csharp
// IO读写操作
Task<bool> ReadInputAsync(string ioNumber)               // 读取输入IO
Task<bool> WriteOutputAsync(string ioNumber, bool state) // 写入输出IO
Task<Dictionary<string, bool>> ReadAllInputsAsync()      // 批量读取输入
Task<bool> WriteMultipleOutputsAsync(Dictionary<string, bool> outputs)  // 批量写入输出

// IO监控
event EventHandler<IOInputStateChangedEventArgs> IOInputStateChanged;   // IO状态变化事件
event EventHandler<BatchIOStateChangedEventArgs> BatchIOStateChanged;   // 批量IO状态变化事件
```

### 2. 皮带电机自动控制模块

#### BeltMotorAutoModeController（皮带电机自动控制器）
**文件位置**: `Managers/BeltMotorAutoModeController.cs`  
**职责**: 输入和输出皮带电机的自动控制逻辑

**控制逻辑**:
- **输入皮带**: 监控传感器I0004，无产品时启动，检测到产品时停止
- **输出皮带**: 监控传感器I0106，检测到产品时启动，无产品时停止
- **传感器检查间隔**: 50ms
- **电机停止延迟**: 100ms

**核心功能**:
```csharp
// 统一AutoMode接口
Task<bool> InitializeAsync()     // 初始化控制器
Task<bool> StartAsync()          // 启动自动控制
Task<bool> StopAsync()           // 停止自动控制
Task<bool> ResetAsync()          // 重置控制器

// 手动控制接口
Task<bool> StartOutputBeltAsync()  // 手动启动输出皮带
Task<bool> StopOutputBeltAsync()   // 手动停止输出皮带
```

**状态管理**:
- BeltMotorState枚举：Idle（空闲）、Running（运行）、Error（错误）
- 线程安全的状态变更
- 完整的事件通知机制

### 3. 机器人控制模块

#### EpsonRobotManager（Epson机器人管理器）
**文件位置**: `Managers/EpsonRobotManager.cs`  
**职责**: Epson 6轴机器人的TCP/IP通信和控制

**通信模式**: 单IP双端口模式
- **控制端口**: 5000（启动/停止指令）
- **数据端口**: 5001（数据收发）

**核心功能**:
```csharp
// 连接管理
Task<bool> ConnectAsync()        // 建立TCP连接
Task<bool> DisconnectAsync()     // 断开连接
Task<bool> LoginAsync()          // 登录机器人
Task<bool> LogoutAsync()         // 登出机器人

// 机器人控制
Task<bool> StartRobotAsync(int functionNumber = 0)  // 启动机器人程序
Task<bool> StopRobotAsync()                          // 停止机器人
Task<bool> ResetRobotAsync()                         // 复位机器人
Task<EpsonRobotStatus> GetStatusAsync()              // 获取机器人状态
```

**状态监控**:
- 实时状态获取（Auto模式、运行状态、错误状态、安全门状态）
- 异步命令响应处理
- 完整的错误处理和重连机制

### 4. 扫码器管理模块

#### ScannerManager（扫码器管理器）
**文件位置**: `Managers/ScannerManager.cs`  
**职责**: 多扫码器的统一管理和数据处理

#### ScannerAutoModeManager（扫码器自动模式管理器）
**文件位置**: `Managers/ScannerAutoModeManager.cs`  
**职责**: 扫码器自动模式的流程控制

**核心功能**:
- 自动模式启动和停止
- 扫码完成事件处理
- 多扫码器协调管理

## 🖥️ 用户界面模块

### MainForm（主窗体）
**文件位置**: `UI/MainForm.cs`  
**设计规格**: 1920×1080工业HMI界面

#### 界面布局
```
┌─────────────────────────────────────────────────────────────┐
│                    标题栏 (60px)                            │
├─────────────────────────────────────────────────────────────┤
│                    系统菜单栏 (40px)                        │
├─────┬─────────────────────────────────────┬─────────────────┤
│左侧 │              功能显示区              │    右侧区域     │
│菜单 │  ┌─────────────────────────────────┐ │  ┌─────────────┐│
│     │  │        二级Tab菜单 (40px)       │ │  │工业相机显示 ││
│200px│  ├─────────────────────────────────┤ │  │   (460px)   ││
│     │  │                                 │ │  ├─────────────┤│
│     │  │        功能面板区 (880px)       │ │  │控制操作区   ││
│     │  │                                 │ │  │  (230px)    ││
│     │  │                                 │ │  ├─────────────┤│
│     │  └─────────────────────────────────┘ │  │产量统计区   ││
│     │              (700px)                │  │  (230px)    ││
└─────┴─────────────────────────────────────┴─────────────────┘
│                    状态栏 (60px)                            │
└─────────────────────────────────────────────────────────────┘
```

#### 功能模块
- **视觉控制**: 定位相机控制、对位相机控制
- **电机控制**: 翻转电机参数、翻转电机示教、皮带电机
- **6轴机器人控制**: 机器人控制、机器人2、扫描器控制
- **SCARA控制**: 通信管理、示教功能
- **IO控制**: 读取IO、写入IO
- **生产日志管理**: 日志时间选择

#### 全局控制按钮
- **启动按钮**: 启动自动化流程（仅自动模式可用）
- **暂停按钮**: 暂停当前流程
- **停止按钮**: 停止流程并切换到手动模式
- **复位按钮**: 重置系统状态

### 用户控件（UserControls）
**文件位置**: `UI/Controls/`

#### 主要控件
- **MotorControlPanel**: 电机控制面板
- **IOControlPanel**: IO控制面板
- **VisionPanel**: 视觉控制面板
- **Robot6AxisPanel**: 6轴机器人控制面板
- **ScannerControlPanel**: 扫码器控制面板
- **StatisticsPanel**: 统计数据面板

## 📊 数据管理模块

### 统计管理
#### StatisticsManager（统计管理器）
**文件位置**: `Managers/StatisticsManager.cs`  
**职责**: 生产数据的统计和分析

**功能特性**:
- 实时生产统计（总数、良品数、不良品数、错误数）
- 班次统计（白班、中班、夜班）
- 数据导出（Excel、CSV格式）
- 历史数据管理

### 配置管理
#### Settings（配置管理器）
**文件位置**: `Settings/Settings.cs`  
**职责**: 系统配置的统一管理

**配置类型**:
- 电机参数配置
- IO端口配置
- 通信参数配置
- 系统运行参数

## 🔧 辅助工具模块

### 日志管理
#### LogHelper（日志辅助类）
**文件位置**: `Helpers/LogHelper.cs`  
**功能**: 基于NLog的统一日志管理

### 异常处理
#### ExceptionHelper（异常处理辅助类）
**文件位置**: `Helpers/ExceptionHelper.cs`  
**功能**: 统一的异常处理和安全执行

### UI辅助
#### UIHelper（UI辅助类）
**文件位置**: `Helpers/UIHelper.cs`  
**功能**: 线程安全的UI更新和消息框显示

## 🧪 测试模块

### 测试框架
**文件位置**: `Testing/`

#### 主要测试类
- **BeltMotorAutoModeController_Test**: 皮带电机控制器测试
- **WorkflowManager_Refactoring_Test**: 工作流管理器测试
- **SystemTests**: 系统集成测试
- **PerformanceTestExecutor**: 性能测试

## 🔄 模块间交互关系

### 事件驱动通信
```
DMC1000BIOManager → IOInputStateChanged → UI控件更新
DMC1000BMotorManager → MotorStatusChanged → UI状态显示
BeltMotorAutoModeController → StateChanged → WorkflowManager
ScannerAutoModeManager → AllScannersCompleted → WorkflowManager
WorkflowManager → WorkflowStateChanged → UI状态更新
```

### 依赖关系
```
WorkflowManager
├── BeltMotorAutoModeController
│   ├── DMC1000BMotorManager
│   └── DMC1000BIOManager
├── ScannerAutoModeManager
│   └── ScannerManager
├── EpsonRobotAutoModeController
│   └── EpsonRobotManager
└── ScaraAutoModeController
    ├── DMC1000BMotorManager
    └── DMC1000BIOManager
```

### 数据流向
```
传感器 → DMC1000BIOManager → BeltMotorAutoModeController → WorkflowManager → UI显示
扫码器 → ScannerManager → ScannerAutoModeManager → WorkflowManager → 生产记录
机器人 → EpsonRobotManager → EpsonRobotAutoModeController → WorkflowManager → 状态更新
```

每个模块都遵循统一的设计原则，具有清晰的职责边界和完整的错误处理机制，为系统的稳定运行提供了坚实的基础。
