using System;
using MyHMI.Models;

namespace MyHMI.Events
{
    /// <summary>
    /// 电机位置变化事件参数
    /// </summary>
    public class MotorPositionEventArgs : EventArgs
    {
        /// <summary>
        /// 电机ID
        /// </summary>
        public int MotorId { get; set; }

        /// <summary>
        /// 当前位置（毫米）
        /// </summary>
        public double Position { get; set; }

        /// <summary>
        /// 当前速度（脉冲/秒）
        /// </summary>
        public double Speed { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="motorId">电机ID</param>
        /// <param name="position">位置</param>
        /// <param name="speed">速度</param>
        public MotorPositionEventArgs(int motorId, double position, double speed = 0)
        {
            MotorId = motorId;
            Position = position;
            Speed = speed;
            EventTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 电机状态变化事件参数
    /// </summary>
    public class MotorStatusEventArgs : EventArgs
    {
        /// <summary>
        /// 电机状态
        /// </summary>
        public MotorStatus Status { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="status">电机状态</param>
        public MotorStatusEventArgs(MotorStatus status)
        {
            Status = status;
            EventTime = DateTime.Now;
        }

        /// <summary>
        /// 构造函数（兼容性）
        /// </summary>
        /// <param name="motorId">电机ID</param>
        /// <param name="status">电机状态</param>
        public MotorStatusEventArgs(int motorId, MotorStatus status)
        {
            Status = status;
            if (Status != null)
            {
                Status.MotorId = motorId; // 确保电机ID一致
            }
            EventTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 电机运动完成事件参数
    /// </summary>
    public class MotorMovementCompletedEventArgs : EventArgs
    {
        /// <summary>
        /// 电机ID
        /// </summary>
        public int MotorId { get; set; }

        /// <summary>
        /// 目标位置
        /// </summary>
        public double TargetPosition { get; set; }

        /// <summary>
        /// 实际位置
        /// </summary>
        public double ActualPosition { get; set; }

        /// <summary>
        /// 是否成功到达
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 运动时间（毫秒）
        /// </summary>
        public double MovementTimeMs { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="motorId">电机ID</param>
        /// <param name="targetPosition">目标位置</param>
        /// <param name="actualPosition">实际位置</param>
        /// <param name="isSuccess">是否成功</param>
        /// <param name="movementTimeMs">运动时间</param>
        /// <param name="errorMessage">错误信息</param>
        public MotorMovementCompletedEventArgs(int motorId, double targetPosition, 
            double actualPosition, bool isSuccess, double movementTimeMs, string errorMessage = "")
        {
            MotorId = motorId;
            TargetPosition = targetPosition;
            ActualPosition = actualPosition;
            IsSuccess = isSuccess;
            MovementTimeMs = movementTimeMs;
            ErrorMessage = errorMessage ?? string.Empty;
            EventTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 电机报警事件参数
    /// </summary>
    public class MotorAlarmEventArgs : EventArgs
    {
        /// <summary>
        /// 电机ID
        /// </summary>
        public int MotorId { get; set; }

        /// <summary>
        /// 报警类型
        /// </summary>
        public MotorAlarmType AlarmType { get; set; }

        /// <summary>
        /// 报警信息
        /// </summary>
        public string AlarmMessage { get; set; }

        /// <summary>
        /// 是否为严重报警
        /// </summary>
        public bool IsCritical { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="motorId">电机ID</param>
        /// <param name="alarmType">报警类型</param>
        /// <param name="alarmMessage">报警信息</param>
        /// <param name="isCritical">是否严重</param>
        public MotorAlarmEventArgs(int motorId, MotorAlarmType alarmType, 
            string alarmMessage, bool isCritical = false)
        {
            MotorId = motorId;
            AlarmType = alarmType;
            AlarmMessage = alarmMessage ?? string.Empty;
            IsCritical = isCritical;
            EventTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 电机报警类型枚举
    /// </summary>
    public enum MotorAlarmType
    {
        /// <summary>
        /// 限位触发
        /// </summary>
        LimitTriggered,

        /// <summary>
        /// 位置偏差过大
        /// </summary>
        PositionError,

        /// <summary>
        /// 通信异常
        /// </summary>
        CommunicationError,

        /// <summary>
        /// 驱动器报警
        /// </summary>
        DriverAlarm,

        /// <summary>
        /// 超时
        /// </summary>
        Timeout,

        /// <summary>
        /// 其他
        /// </summary>
        Other
    }
}
