上位机程序功能丰富，涉及硬件控制、通信、视觉、数据统计和安全日志等多个模块。采用 **“UI线程 + 后台 Task + 事件回调”** 模式，保证界面响应流畅，解耦业务逻辑，便于维护和扩展。

---
## 技术栈
- winforms + .net frameworks 4.8

## ✅ 设计目标

- **UI线程**：只负责界面更新、用户交互，不阻塞。
- **后台 Task**：所有耗时操作（IO、通信、视觉处理等）在后台线程执行。
- **事件回调**：模块间通过事件通信，避免强耦合。
- **结构清晰、职责单一、易于维护和扩展**。

---

## 🧱 整体架构设计（分层 + 模块化）

```
┌──────────────────────────────────┐
│           UI Layer               │ ← WinForms（只负责界面绑定和事件触发）
└──────────────┬───────────────────┘
               │ 事件触发 / 数据绑定
┌──────────────▼───────────────────┐
│        Business Logic Layer      │ ← 各功能模块管理器（后台 Task + 事件回调）
│  - IOManager                     │
│  - MotorManager                  │
│  - VisionManager                 │
│  - ModbusTcpManager              │
│  - ScannerManager                │
│  - EpsonRobotManager             │
│  - StatisticsManager             │
│  - LogManager                    │
└──────────────┬───────────────────┘
               │ 调用底层驱动/SDK/通信库
┌──────────────▼───────────────────┐
│        Hardware/Driver Layer     │ ← 雷赛SDK、Modbus库、串口库、Socket等
└──────────────────────────────────┘
```

---

## 🧩 模块设计详解

### 1. 🖥️ UI层（WinForms）

- 每个功能对应一个 `UserControl` 或 `Form`（如：IOControlPanel, MotorControlPanel, VisionPanel等）
- 使用 `BindingSource` 或手动绑定更新UI
- 所有按钮点击、参数设置等，只触发事件或调用 `Manager` 的异步方法
- UI更新通过 `Control.Invoke` 或 `SynchronizationContext` 安全回调

✅ 示例：

```csharp
private async void btnStartMotor_Click(object sender, EventArgs e)
{
    await MotorManager.Instance.StartMotorAsync(motorId, targetPosition);
}

// 回调更新UI
private void OnMotorPositionChanged(object sender, MotorPositionEventArgs e)
{
    if (lblPosition.InvokeRequired)
    {
        lblPosition.Invoke(new Action(() => lblPosition.Text = e.Position.ToString()));
    }
    else
    {
        lblPosition.Text = e.Position.ToString();
    }
}
```

---

### 2. 🧠 业务逻辑层（Manager 单例模式 + 事件驱动）

> 每个 Manager 是单例，负责初始化、后台任务调度、状态管理、事件发布。

#### ① IOManager（雷赛IO控制）

- 封装雷赛SDK的IO读写
- 提供 `ReadInputAsync(int ioIndex)`, `WriteOutputAsync(int ioIndex, bool value)`
- 触发 `IoStateChanged` 事件供UI订阅

```csharp
public class IOManager
{
    public static IOManager Instance { get; } = new IOManager();
    public event EventHandler<IoStateChangedEventArgs> IoStateChanged;

    private Task backgroundMonitorTask;

    public async Task InitializeAsync()
    {
        // 初始化雷赛卡
        DMC1000.Initialize();
        backgroundMonitorTask = Task.Run(MonitorIoLoop);
    }

    private async Task MonitorIoLoop()
    {
        while (true)
        {
            var state = ReadAllInputs(); // 假设SDK提供
            IoStateChanged?.Invoke(this, new IoStateChangedEventArgs(state));
            await Task.Delay(50); // 50ms轮询
        }
    }
}
```

---

#### ② MotorManager（闭环步进电机控制）

- 设置参数：速度、加速度、脉冲当量、限位等
- 支持点位运动、连续运动、回零
- 事件：`MotorMoving`, `MotorStopped`, `MotorPositionUpdated`

```csharp
public async Task SetMotorParamsAsync(int motorId, MotorParams param)
{
    await Task.Run(() => DMC1000.SetMotorParams(motorId, param));
}

public async Task MoveToAsync(int motorId, double position)
{
    await Task.Run(() => DMC1000.MoveTo(motorId, position));
}
```

---

#### ③ VisionManager（视觉定位）

- 调用相机SDK（如Halcon、OpenCVSharp、海康等）
- 提供 `StartCapture()`, `StopCapture()`, `GetLatestPosition()`
- 事件：`VisionResultReady`（含坐标、置信度等）

> 注意：视觉处理耗时，必须在后台线程，结果通过事件回调给UI或MotorManager做联动

---

#### ④ ModbusTcpManager（控制SCARA机器人）

- 使用 `NModbus4` 或 `EasyModbus` 库
- 封装写寄存器、读寄存器方法
- 提供 `SendRobotCommandAsync(string cmd, double[] coords)`
- 事件：`RobotResponseReceived`, `RobotError`

```csharp
public async Task SendRobotCommandAsync(string action, params double[] coords)
{
    await Task.Run(() =>
    {
        var client = new TcpClient(robotIp, 502);
        var factory = new ModbusFactory();
        var master = factory.CreateMaster(client);
        
        // 示例：写坐标到保持寄存器
        master.WriteMultipleRegisters(slaveId, startAddress, ConvertToRegisters(coords));
    });
}
```

---

#### ⑤ ScannerManager（串口扫描枪）

- 使用 `System.IO.Ports.SerialPort`
- 自动监听串口数据，解析条码
- 事件：`BarcodeScanned`

```csharp
private void serialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
{
    string data = serialPort.ReadLine();
    BarcodeScanned?.Invoke(this, new BarcodeEventArgs(data));
}
```

---

#### ⑥ EpsonRobotManager（Epson六轴机器人TCP/IP通信）

- 使用双TCP/IP连接（启动/停止TCP/IP和数据收发TCP/IP），基于标准TCP/IP协议栈
- 实现Epson RC+协议（Login、Start、Stop、GetStatus等命令）
- 支持自动化流程控制（100ms扫描、特殊命令处理）
- 事件：`ConnectionStatusChanged`, `ResponseReceived`, `RobotStarted`, `SpecialCommandReceived`

---

#### ⑦ StatisticsManager（生产统计）

- 记录：产品ID、时间、操作员、良品/不良品、视觉结果、机器人动作等
- 支持导出CSV/Excel（使用 `EPPlus` 或 `CsvHelper`）
- 内存中用 `List<ProductionRecord>` 缓存，定时或按需持久化到文件/数据库

```csharp
public void AddRecord(ProductionRecord record)
{
    records.Add(record);
    // 可选：写入SQLite或CSV
}

public void ExportToExcel(string filePath)
{
    // 使用EPPlus导出
}
```

---

#### ⑧ LogManager（安全日志）

- 使用 `NLog` 或简单文本日志
- 记录：用户操作、报警、通信错误、电机异常等
- 支持按日期归档、级别过滤（Info/Warning/Error）

```csharp
public static class LogManager
{
    private static Logger logger = LogManager.GetCurrentClassLogger();

    public static void Info(string message) => logger.Info(message);
    public static void Error(string message, Exception ex = null) => logger.Error(ex, message);
}
```

---

## 🔄 事件驱动通信机制

所有模块通过 **事件** 与其他模块或UI通信，避免直接调用。

✅ 示例：扫描枪扫到条码 → 触发视觉定位 → 视觉完成 → 触发电机运动 → 运动完成 → 通知机器人抓取 → 记录统计

```csharp
ScannerManager.Instance.BarcodeScanned += OnBarcodeScanned;
VisionManager.Instance.VisionResultReady += OnVisionResult;
MotorManager.Instance.MotorStopped += OnMotorArrived;
```

---

## 🧵 线程与异步处理规范

- 所有硬件操作、通信、视觉处理必须 `Task.Run(...)` 或 `async/await` 封装
- UI更新必须通过 `Invoke` 或 `BeginInvoke`
- 使用 `CancellationToken` 支持取消长时间操作
- 避免在UI线程中 `Thread.Sleep` 或同步阻塞调用

---

## 🗃️ 配置与初始化

- 使用 `app.config` 或 `JSON` 配置文件保存：
  - 串口号、波特率
  - 机器人IP、端口
  - 电机参数默认值
  - 视觉参数路径
- 程序启动时自动加载配置，失败时弹出设置向导

---

## 🧪 异常处理与重试机制

- 所有通信模块需包含：
  - 超时重试（如Modbus、TCP）
  - 连接断开自动重连
  - 异常捕获 + 日志记录 + UI提示（通过事件）

---

## 📁 项目结构建议（Visual Studio）

```
MyHMI/
├── UI/                  ← 所有WinForm窗体和控件
├── Managers/            ← 所有XXXManager.cs
├── Models/              ← 数据模型（MotorParams, ProductionRecord等）
├── Events/              ← 自定义事件参数类
├── Helpers/             ← 工具类（串口助手、Modbus转换等）
├── Logs/                ← 日志输出目录
├── Config/              ← 配置文件
├── References/          ← 第三方DLL（雷赛SDK、NModbus、EPPlus等）
└── Program.cs           ← 入口，初始化所有Manager
```

---

## 🚀 启动流程（Program.cs）

```csharp
static class Program
{
    [STAThread]
    static void Main()
    {
        Application.EnableVisualStyles();
        Application.SetCompatibleTextRenderingDefault(false);

        // 初始化日志
        LogManager.Info("程序启动");

        // 初始化各Manager（异步）
        Task.Run(async () =>
        {
            await IOManager.Instance.InitializeAsync();
            await MotorManager.Instance.InitializeAsync();
            await ScannerManager.Instance.InitializeAsync();
            // ... 其他初始化

            // 启动主窗体（必须在UI线程）
            Application.Run(new MainForm());
        }).Wait();
    }
}
```

---

## ✅ 维护性 & 扩展性保障

- **新增功能**：只需新增一个 `XXXManager` + 对应UI控件 + 订阅相关事件
- **替换硬件**：只需修改对应Manager内部实现，不影响其他模块
- **调试方便**：每个模块可独立测试，事件流清晰
- **日志完备**：所有关键操作、异常均有日志，便于追溯

---

## 📌 开发流程

1. **先搭建框架**：先实现Manager基类、事件机制、日志系统
2. **逐个模块开发**：从IO和电机控制开始，再视觉、通信
3. **测试**：在没有硬件时，用Mock类模拟返回值，有硬件时直接测试
4. **文档化接口**：每个Manager的Public方法和事件写XML注释

