using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using MyHMI.Events;
using MyHMI.Models;
using MyHMI.Helpers;
using MyHMI.Settings;

namespace MyHMI.Managers
{
    /// <summary>
    /// 闭环步进电机控制管理器
    /// 负责电机参数设置、运动控制、位置监控和事件发布
    /// </summary>
    public class MotorManager
    {
        #region 单例模式
        private static readonly Lazy<MotorManager> _instance = new Lazy<MotorManager>(() => new MotorManager());
        public static MotorManager Instance => _instance.Value;
        private MotorManager() { }
        #endregion

        #region 事件定义
        /// <summary>
        /// 电机位置变化事件
        /// </summary>
        public event EventHandler<MotorPositionEventArgs> MotorPositionChanged;

        /// <summary>
        /// 电机状态变化事件
        /// </summary>
        public event EventHandler<MotorStatusEventArgs> MotorStatusChanged;

        /// <summary>
        /// 电机运动完成事件
        /// </summary>
        public event EventHandler<MotorMovementCompletedEventArgs> MotorMovementCompleted;

        /// <summary>
        /// 电机报警事件
        /// </summary>
        public event EventHandler<MotorAlarmEventArgs> MotorAlarm;
        #endregion

        #region 私有字段
        private bool _isInitialized = false;
        private bool _isMonitoring = false;
        private Task _monitorTask;
        private CancellationTokenSource _cancellationTokenSource;
        
        // 电机配置和状态
        private readonly Dictionary<int, MotorParams> _motorParams = new Dictionary<int, MotorParams>();
        private readonly Dictionary<int, MotorStatus> _motorStatus = new Dictionary<int, MotorStatus>();
        private readonly Dictionary<int, Task> _movementTasks = new Dictionary<int, Task>();
        private readonly object _motorLock = new object();
        
        // 配置参数
        private int _monitorIntervalMs = 100;
        private int _maxMotorCount = 8;
        #endregion

        #region 公共属性
        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// 是否正在监控
        /// </summary>
        public bool IsMonitoring => _isMonitoring;

        /// <summary>
        /// 最大电机数量
        /// </summary>
        public int MaxMotorCount => _maxMotorCount;
        #endregion

        #region 初始化和释放
        /// <summary>
        /// 异步初始化电机管理器
        /// </summary>
        /// <returns></returns>
        public async Task<bool> InitializeAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_isInitialized)
                {
                    LogHelper.Warning("MotorManager已经初始化，跳过重复初始化");
                    return true;
                }

                LogHelper.Info("开始初始化MotorManager...");

                // 从新的Settings系统读取参数
                var systemSettings = Settings.Settings.Current.System;
                _monitorIntervalMs = systemSettings.MotorMonitorInterval;
                _maxMotorCount = systemSettings.MaxMotorCount;

                // 初始化雷赛运动控制卡
                bool initResult = await InitializeMotionCardAsync();
                if (!initResult)
                {
                    throw new Exception("运动控制卡初始化失败");
                }

                // 加载电机配置
                await LoadMotorConfigurationsAsync();

                // 启动监控任务
                await StartMonitoringAsync();

                _isInitialized = true;
                LogHelper.Info("MotorManager初始化完成");
                return true;

            }, false, "MotorManager初始化");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public async Task DisposeAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info("开始释放MotorManager资源...");

                // 停止所有电机
                await StopAllMotorsAsync();

                // 停止监控
                await StopMonitoringAsync();

                // 释放运动控制卡资源
                ReleaseMotionCard();

                _isInitialized = false;
                LogHelper.Info("MotorManager资源释放完成");

                return true;
            }, false, "MotorManager资源释放");
        }
        #endregion

        #region 电机配置管理
        /// <summary>
        /// 设置电机参数
        /// </summary>
        /// <param name="motorId">电机ID</param>
        /// <param name="parameters">电机参数</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SetMotorParamsAsync(int motorId, MotorParams parameters)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (!_isInitialized)
                    throw new InvalidOperationException("MotorManager未初始化");

                if (motorId < 0 || motorId >= _maxMotorCount)
                    throw new ArgumentOutOfRangeException(nameof(motorId), $"电机ID超出范围: {motorId}");

                if (parameters == null)
                    throw new ArgumentNullException(nameof(parameters));

                // 验证参数
                var validation = parameters.Validate();
                if (!validation.IsValid)
                    throw new ArgumentException($"电机参数无效: {validation.ErrorMessage}");

                // 设置电机参数到硬件
                bool result = await SetMotorParamsToHardwareAsync(motorId, parameters);
                if (result)
                {
                    lock (_motorLock)
                    {
                        _motorParams[motorId] = parameters;
                    }
                    LogHelper.Info($"电机{motorId}参数设置成功");
                }

                return result;

            }, false, $"设置电机{motorId}参数");
        }

        /// <summary>
        /// 获取电机参数
        /// </summary>
        /// <param name="motorId">电机ID</param>
        /// <returns>电机参数</returns>
        public MotorParams GetMotorParams(int motorId)
        {
            lock (_motorLock)
            {
                return _motorParams.ContainsKey(motorId) ? _motorParams[motorId] : null;
            }
        }

        /// <summary>
        /// 获取电机状态
        /// </summary>
        /// <param name="motorId">电机ID</param>
        /// <returns>电机状态</returns>
        public MotorStatus GetMotorStatus(int motorId)
        {
            lock (_motorLock)
            {
                return _motorStatus.ContainsKey(motorId) ? _motorStatus[motorId] : null;
            }
        }

        /// <summary>
        /// 异步获取电机状态
        /// </summary>
        /// <param name="motorId">电机ID</param>
        /// <returns>电机状态</returns>
        public async Task<MotorStatus> GetMotorStatusAsync(int motorId)
        {
            return await Task.FromResult(GetMotorStatus(motorId));
        }
        #endregion

        #region 电机运动控制
        /// <summary>
        /// 电机点位运动
        /// </summary>
        /// <param name="motorId">电机ID</param>
        /// <param name="targetPosition">目标位置（毫米）</param>
        /// <param name="speed">运动速度（脉冲/秒），0表示使用默认速度</param>
        /// <returns>是否成功启动运动</returns>
        public async Task<bool> MoveToAsync(int motorId, double targetPosition, double speed = 0)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (!_isInitialized)
                    throw new InvalidOperationException("MotorManager未初始化");

                ValidateMotorId(motorId);

                var motorParams = GetMotorParams(motorId);
                if (motorParams == null)
                    throw new InvalidOperationException($"电机{motorId}参数未设置");

                var motorStatus = GetMotorStatus(motorId);
                if (motorStatus != null && motorStatus.IsMoving)
                {
                    LogHelper.Warning($"电机{motorId}正在运动中，停止当前运动");
                    await StopMotorAsync(motorId);
                }

                // 使用默认速度或指定速度
                double moveSpeed = speed > 0 ? speed : motorParams.MaxSpeed;

                // 检查软件限位
                if (motorParams.EnableSoftLimit)
                {
                    if (targetPosition > motorParams.SoftPositiveLimit || 
                        targetPosition < motorParams.SoftNegativeLimit)
                    {
                        throw new ArgumentOutOfRangeException(nameof(targetPosition), 
                            $"目标位置超出软件限位范围: {targetPosition}");
                    }
                }

                // 启动运动任务
                var movementTask = ExecuteMovementAsync(motorId, targetPosition, moveSpeed, false);
                
                lock (_motorLock)
                {
                    _movementTasks[motorId] = movementTask;
                }

                LogHelper.Info($"电机{motorId}开始点位运动，目标位置: {targetPosition}mm，速度: {moveSpeed}");
                return true;

            }, false, $"电机{motorId}点位运动");
        }

        /// <summary>
        /// 电机连续运动
        /// </summary>
        /// <param name="motorId">电机ID</param>
        /// <param name="direction">运动方向（true: 正向, false: 负向）</param>
        /// <param name="speed">运动速度（脉冲/秒），0表示使用默认速度</param>
        /// <returns>是否成功启动运动</returns>
        public async Task<bool> ContinuousMoveAsync(int motorId, bool direction, double speed = 0)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (!_isInitialized)
                    throw new InvalidOperationException("MotorManager未初始化");

                ValidateMotorId(motorId);

                var motorParams = GetMotorParams(motorId);
                if (motorParams == null)
                    throw new InvalidOperationException($"电机{motorId}参数未设置");

                // 停止当前运动
                await StopMotorAsync(motorId);

                // 使用默认速度或指定速度
                double moveSpeed = speed > 0 ? speed : motorParams.MaxSpeed;

                // 启动连续运动
                bool result = await StartContinuousMovementAsync(motorId, direction, moveSpeed);
                
                LogHelper.Info($"电机{motorId}开始连续运动，方向: {(direction ? "正向" : "负向")}，速度: {moveSpeed}");
                return result;

            }, false, $"电机{motorId}连续运动");
        }

        /// <summary>
        /// 停止电机运动
        /// </summary>
        /// <param name="motorId">电机ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> StopMotorAsync(int motorId)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                ValidateMotorId(motorId);

                // 停止硬件运动
                bool result = await StopMotorHardwareAsync(motorId);
                
                // 取消运动任务
                lock (_motorLock)
                {
                    if (_movementTasks.ContainsKey(motorId))
                    {
                        _movementTasks.Remove(motorId);
                    }
                }

                LogHelper.Info($"电机{motorId}停止运动");
                return result;

            }, false, $"停止电机{motorId}");
        }

        /// <summary>
        /// 电机回零
        /// </summary>
        /// <param name="motorId">电机ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> HomeMotorAsync(int motorId)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                ValidateMotorId(motorId);

                var motorParams = GetMotorParams(motorId);
                if (motorParams == null)
                    throw new InvalidOperationException($"电机{motorId}参数未设置");

                if (motorParams.HomeIO < 0)
                    throw new InvalidOperationException($"电机{motorId}未配置原点开关");

                // 停止当前运动
                await StopMotorAsync(motorId);

                // 执行回零操作
                bool result = await ExecuteHomingAsync(motorId, motorParams);
                
                if (result)
                {
                    // 更新状态
                    lock (_motorLock)
                    {
                        if (_motorStatus.ContainsKey(motorId))
                        {
                            _motorStatus[motorId].IsHomed = true;
                            _motorStatus[motorId].CurrentPosition = 0;
                        }
                    }
                }

                LogHelper.Info($"电机{motorId}回零{(result ? "成功" : "失败")}");
                return result;

            }, false, $"电机{motorId}回零");
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 验证电机ID有效性
        /// </summary>
        /// <param name="motorId">电机ID</param>
        private void ValidateMotorId(int motorId)
        {
            if (motorId < 0 || motorId >= _maxMotorCount)
                throw new ArgumentOutOfRangeException(nameof(motorId), $"电机ID超出范围: {motorId}");
        }

        /// <summary>
        /// 初始化运动控制卡（模拟实现）
        /// </summary>
        /// <returns></returns>
        private async Task<bool> InitializeMotionCardAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    // 模拟运动控制卡初始化
                    LogHelper.Info("模拟初始化运动控制卡");
                    Thread.Sleep(200);
                    return true;
                }
                catch (Exception ex)
                {
                    LogHelper.Error("运动控制卡初始化失败", ex);
                    return false;
                }
            });
        }

        /// <summary>
        /// 释放运动控制卡资源（模拟实现）
        /// </summary>
        private void ReleaseMotionCard()
        {
            try
            {
                LogHelper.Info("模拟释放运动控制卡资源");
            }
            catch (Exception ex)
            {
                LogHelper.Error("释放运动控制卡资源失败", ex);
            }
        }

        /// <summary>
        /// 加载电机配置
        /// </summary>
        /// <returns></returns>
        private async Task LoadMotorConfigurationsAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    // 加载默认电机配置
                    for (int i = 0; i < _maxMotorCount; i++)
                    {
                        var motorParams = new MotorParams
                        {
                            MotorId = i,
                            MotorName = $"电机{i}",
                            MaxSpeed = 10000,
                            Acceleration = 50000,
                            Deceleration = 50000,
                            PulseEquivalent = 1000
                        };

                        var motorStatus = new MotorStatus
                        {
                            MotorId = i,
                            CurrentPosition = 0,
                            IsEnabled = false,
                            IsHomed = false
                        };

                        lock (_motorLock)
                        {
                            _motorParams[i] = motorParams;
                            _motorStatus[i] = motorStatus;
                        }
                    }

                    LogHelper.Info($"加载了{_maxMotorCount}个电机的默认配置");
                }
                catch (Exception ex)
                {
                    LogHelper.Error("加载电机配置失败", ex);
                }
            });
        }

        /// <summary>
        /// 设置电机参数到硬件（模拟实现）
        /// </summary>
        /// <param name="motorId">电机ID</param>
        /// <param name="parameters">电机参数</param>
        /// <returns></returns>
        private async Task<bool> SetMotorParamsToHardwareAsync(int motorId, MotorParams parameters)
        {
            return await Task.Run(() =>
            {
                try
                {
                    // 模拟设置电机参数到硬件
                    LogHelper.Debug($"模拟设置电机{motorId}参数: 最大速度={parameters.MaxSpeed}, 加速度={parameters.Acceleration}");
                    return true;
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"设置电机{motorId}参数到硬件失败", ex);
                    return false;
                }
            });
        }

        /// <summary>
        /// 执行运动任务
        /// </summary>
        /// <param name="motorId">电机ID</param>
        /// <param name="targetPosition">目标位置</param>
        /// <param name="speed">运动速度</param>
        /// <param name="isRelative">是否相对运动</param>
        /// <returns></returns>
        private async Task ExecuteMovementAsync(int motorId, double targetPosition, double speed, bool isRelative)
        {
            var startTime = DateTime.Now;
            var startPosition = GetMotorStatus(motorId)?.CurrentPosition ?? 0;

            try
            {
                // 模拟运动执行
                await SimulateMovementAsync(motorId, targetPosition, speed);

                var endTime = DateTime.Now;
                var actualPosition = targetPosition; // 模拟到达目标位置
                var movementTime = (endTime - startTime).TotalMilliseconds;

                // 触发运动完成事件
                MotorMovementCompleted?.Invoke(this, new MotorMovementCompletedEventArgs(
                    motorId, targetPosition, actualPosition, true, movementTime));

                LogHelper.Info($"电机{motorId}运动完成，耗时: {movementTime:F1}ms");
            }
            catch (Exception ex)
            {
                var endTime = DateTime.Now;
                var movementTime = (endTime - startTime).TotalMilliseconds;

                // 触发运动失败事件
                MotorMovementCompleted?.Invoke(this, new MotorMovementCompletedEventArgs(
                    motorId, targetPosition, startPosition, false, movementTime, ex.Message));

                LogHelper.Error($"电机{motorId}运动失败", ex);
            }
        }

        /// <summary>
        /// 模拟运动过程
        /// </summary>
        /// <param name="motorId">电机ID</param>
        /// <param name="targetPosition">目标位置</param>
        /// <param name="speed">运动速度</param>
        /// <returns></returns>
        private async Task SimulateMovementAsync(int motorId, double targetPosition, double speed)
        {
            var currentStatus = GetMotorStatus(motorId);
            if (currentStatus == null) return;

            var startPosition = currentStatus.CurrentPosition;
            var distance = Math.Abs(targetPosition - startPosition);
            var moveTime = (distance / speed) * 1000; // 转换为毫秒

            // 模拟运动过程中的位置更新
            var steps = Math.Max(10, (int)(moveTime / 100)); // 至少10步，每100ms更新一次
            var stepTime = moveTime / steps;
            var stepDistance = (targetPosition - startPosition) / steps;

            for (int i = 0; i <= steps; i++)
            {
                var currentPosition = startPosition + stepDistance * i;

                // 更新状态
                lock (_motorLock)
                {
                    if (_motorStatus.ContainsKey(motorId))
                    {
                        _motorStatus[motorId].CurrentPosition = currentPosition;
                        _motorStatus[motorId].CurrentSpeed = i < steps ? speed : 0;
                        _motorStatus[motorId].IsMoving = i < steps;
                        _motorStatus[motorId].UpdateTime = DateTime.Now;
                    }
                }

                // 触发位置变化事件
                MotorPositionChanged?.Invoke(this, new MotorPositionEventArgs(motorId, currentPosition,
                    i < steps ? speed : 0));

                if (i < steps)
                {
                    await Task.Delay((int)stepTime);
                }
            }
        }

        /// <summary>
        /// 启动连续运动（模拟实现）
        /// </summary>
        /// <param name="motorId">电机ID</param>
        /// <param name="direction">运动方向</param>
        /// <param name="speed">运动速度</param>
        /// <returns></returns>
        private async Task<bool> StartContinuousMovementAsync(int motorId, bool direction, double speed)
        {
            return await Task.Run(() =>
            {
                try
                {
                    // 模拟启动连续运动
                    LogHelper.Debug($"模拟电机{motorId}连续运动: 方向={direction}, 速度={speed}");

                    // 更新状态
                    lock (_motorLock)
                    {
                        if (_motorStatus.ContainsKey(motorId))
                        {
                            _motorStatus[motorId].IsMoving = true;
                            _motorStatus[motorId].CurrentSpeed = direction ? speed : -speed;
                        }
                    }

                    return true;
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"启动电机{motorId}连续运动失败", ex);
                    return false;
                }
            });
        }

        /// <summary>
        /// 停止电机硬件运动（模拟实现）
        /// </summary>
        /// <param name="motorId">电机ID</param>
        /// <returns></returns>
        private async Task<bool> StopMotorHardwareAsync(int motorId)
        {
            return await Task.Run(() =>
            {
                try
                {
                    // 模拟停止电机运动
                    LogHelper.Debug($"模拟停止电机{motorId}运动");

                    // 更新状态
                    lock (_motorLock)
                    {
                        if (_motorStatus.ContainsKey(motorId))
                        {
                            _motorStatus[motorId].IsMoving = false;
                            _motorStatus[motorId].CurrentSpeed = 0;
                        }
                    }

                    return true;
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"停止电机{motorId}运动失败", ex);
                    return false;
                }
            });
        }

        /// <summary>
        /// 执行回零操作（模拟实现）
        /// </summary>
        /// <param name="motorId">电机ID</param>
        /// <param name="motorParams">电机参数</param>
        /// <returns></returns>
        private async Task<bool> ExecuteHomingAsync(int motorId, MotorParams motorParams)
        {
            return await Task.Run(async () =>
            {
                try
                {
                    LogHelper.Info($"电机{motorId}开始回零操作");

                    // 模拟回零过程
                    await Task.Delay(2000); // 模拟回零时间

                    // 模拟回零成功
                    LogHelper.Info($"电机{motorId}回零完成");
                    return true;
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"电机{motorId}回零失败", ex);
                    return false;
                }
            });
        }

        /// <summary>
        /// 停止所有电机
        /// </summary>
        /// <returns></returns>
        public async Task StopAllMotorsAsync()
        {
            var tasks = new List<Task>();

            for (int i = 0; i < _maxMotorCount; i++)
            {
                tasks.Add(StopMotorAsync(i));
            }

            await Task.WhenAll(tasks);
            LogHelper.Info("所有电机已停止");
        }

        /// <summary>
        /// 启动监控任务
        /// </summary>
        /// <returns></returns>
        private async Task StartMonitoringAsync()
        {
            await Task.CompletedTask; // 消除CS1998警告
            if (_isMonitoring)
            {
                LogHelper.Warning("电机监控已经在运行");
                return;
            }

            _cancellationTokenSource = new CancellationTokenSource();
            _monitorTask = Task.Run(async () => await MonitorMotorsLoopAsync(_cancellationTokenSource.Token));
            _isMonitoring = true;

            LogHelper.Info("电机监控任务已启动");
        }

        /// <summary>
        /// 停止监控任务
        /// </summary>
        /// <returns></returns>
        private async Task StopMonitoringAsync()
        {
            if (!_isMonitoring)
                return;

            try
            {
                _cancellationTokenSource?.Cancel();

                if (_monitorTask != null)
                {
                    await _monitorTask;
                }

                _isMonitoring = false;
                LogHelper.Info("电机监控任务已停止");
            }
            catch (Exception ex)
            {
                LogHelper.Error("停止电机监控任务失败", ex);
            }
        }

        /// <summary>
        /// 电机监控循环
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        private async Task MonitorMotorsLoopAsync(CancellationToken cancellationToken)
        {
            LogHelper.Info("电机监控循环开始");

            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    await MonitorAllMotorsAsync();
                    await Task.Delay(_monitorIntervalMs, cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                LogHelper.Info("电机监控循环被取消");
            }
            catch (Exception ex)
            {
                LogHelper.Error("电机监控循环异常", ex);
            }

            LogHelper.Info("电机监控循环结束");
        }

        /// <summary>
        /// 监控所有电机状态
        /// </summary>
        /// <returns></returns>
        private async Task MonitorAllMotorsAsync()
        {
            try
            {
                for (int i = 0; i < _maxMotorCount; i++)
                {
                    if (_motorParams.ContainsKey(i))
                    {
                        await MonitorSingleMotorAsync(i);
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("监控电机状态失败", ex);
            }
        }

        /// <summary>
        /// 监控单个电机状态
        /// </summary>
        /// <param name="motorId">电机ID</param>
        /// <returns></returns>
        private async Task MonitorSingleMotorAsync(int motorId)
        {
            await Task.Run(() =>
            {
                try
                {
                    lock (_motorLock)
                    {
                        if (_motorStatus.ContainsKey(motorId))
                        {
                            var status = _motorStatus[motorId];
                            var previousUpdateTime = status.UpdateTime;

                            // 模拟读取硬件状态
                            // 实际实现应该从硬件读取真实状态

                            // 如果状态有变化，触发事件
                            if (DateTime.Now - previousUpdateTime > TimeSpan.FromMilliseconds(_monitorIntervalMs * 2))
                            {
                                status.UpdateTime = DateTime.Now;
                                MotorStatusChanged?.Invoke(this, new MotorStatusEventArgs(status));
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"监控电机{motorId}状态失败", ex);
                }
            });
        }
        #endregion
    }
}
