[2025-09-22 14:48:43.872] [INFO] 程序启动开始
[2025-09-22 14:48:43.874] [INFO] 配置系统初始化成功
[2025-09-22 14:48:44.097] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-22 14:48:44.097] [INFO] 配置系统初始化完成
[2025-09-22 14:48:44.098] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-22 14:48:44.127] [INFO] 开始初始化各个Manager...
[2025-09-22 14:48:44.127] [INFO] 初始化基础Manager...
[2025-09-22 14:48:44.134] [INFO] IO状态缓存初始化完成
[2025-09-22 14:48:44.142] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-22 14:48:44.142] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-22 14:48:44.145] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-22 14:48:44.145] [ERROR] DMC1000B控制卡初始化失败
[2025-09-22 14:48:44.145] [WARN] DMC1000BIO管理器初始化失败
[2025-09-22 14:48:44.152] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-22 14:48:44.152] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-22 14:48:44.152] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-22 14:48:44.153] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-22 14:48:44.201] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__42_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 130
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 14:48:44.227] [WARN] DMC1000B电机管理器初始化失败
[2025-09-22 14:48:44.227] [INFO] 初始化系统模式管理器...
[2025-09-22 14:48:44.232] [INFO] 开始初始化MotorManager...
[2025-09-22 14:48:44.235] [INFO] 模拟初始化运动控制卡
[2025-09-22 14:48:44.453] [INFO] 加载了8个电机的默认配置
[2025-09-22 14:48:44.454] [INFO] 电机监控任务已启动
[2025-09-22 14:48:44.454] [INFO] MotorManager初始化完成
[2025-09-22 14:48:44.454] [INFO] 初始化通信Manager...
[2025-09-22 14:48:44.455] [INFO] 电机监控循环开始
[2025-09-22 14:48:44.457] [INFO] 开始初始化ScannerManager...
[2025-09-22 14:48:44.460] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-22 14:48:44.462] [INFO] 串口初始化完成
[2025-09-22 14:48:44.464] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-22 14:48:44.467] [INFO] 扫描枪连接成功: COM1
[2025-09-22 14:48:44.468] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-22 14:48:44.468] [INFO] ScannerManager初始化完成
[2025-09-22 14:48:44.470] [INFO] 开始初始化ModbusTcpManager...
[2025-09-22 14:48:44.472] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-22 14:48:44.474] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-22 14:48:49.546] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 14:48:49.547] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-22 14:48:49.547] [INFO] ModbusTcpManager初始化完成
[2025-09-22 14:48:49.551] [INFO] 开始初始化EpsonRobotManager...
[2025-09-22 14:48:49.553] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-22 14:48:49.553] [INFO] EpsonRobotManager初始化完成
[2025-09-22 14:48:49.553] [INFO] 初始化视觉Manager...
[2025-09-22 14:48:49.556] [INFO] 开始初始化VisionManager...
[2025-09-22 14:48:49.557] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-22 14:48:49.558] [INFO] 模拟初始化相机，索引: 0
[2025-09-22 14:48:50.068] [INFO] 相机初始化成功
[2025-09-22 14:48:50.074] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-22 14:48:50.075] [INFO] 视觉配置加载完成
[2025-09-22 14:48:50.076] [INFO] VisionManager初始化完成
[2025-09-22 14:48:50.077] [INFO] 初始化数据Manager...
[2025-09-22 14:48:50.090] [INFO] 开始初始化StatisticsManager...
[2025-09-22 14:48:50.091] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-22 14:48:50.112] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-22 14:48:50.113] [INFO] 历史数据加载完成
[2025-09-22 14:48:50.114] [INFO] 自动保存任务已启动
[2025-09-22 14:48:50.114] [INFO] StatisticsManager初始化完成
[2025-09-22 14:48:50.115] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-22 14:48:50.116] [INFO] 所有Manager初始化完成
[2025-09-22 14:48:50.118] [INFO] 自动保存循环开始
[2025-09-22 14:48:50.174] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-22 14:48:50.175] [INFO] 主界面布局创建完成
[2025-09-22 14:48:50.176] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-22 14:49:02.519] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-22 14:49:03.477] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-22 14:49:03.477] [INFO] Epson机器人管理器初始化完成
[2025-09-22 14:49:03.510] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-22 14:49:04.349] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-22 14:49:06.602] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-22 14:49:06.602] [INFO] Epson机器人管理器初始化完成
[2025-09-22 14:49:06.606] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-22 14:49:11.541] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-22 14:49:11.542] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-22 14:49:11.639] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 14:49:11.696] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 14:49:11.700] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-22 14:49:11.700] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-22 14:49:13.488] [INFO] 翻转电机控制面板资源释放完成
[2025-09-22 14:49:13.508] [INFO] 皮带电机控制面板初始化完成 - 双电机设计
[2025-09-22 14:49:13.509] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-22 14:49:13.572] [ERROR] 设置皮带电机轴3参数 执行失败
异常详情: 皮带电机参数无效: 脉冲当量不能大于0.1
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass51_0.<<SetBeltMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 536
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 14:49:13.625] [ERROR] 设置皮带电机轴2参数 执行失败
异常详情: 皮带电机参数无效: 脉冲当量不能大于0.1
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass51_0.<<SetBeltMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 536
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 14:49:13.625] [INFO] 皮带电机参数初始化完成
[2025-09-22 14:49:13.626] [INFO] 皮带电机控制面板业务逻辑初始化完成
[2025-09-22 15:01:58.556] [INFO] 皮带电机控制面板资源释放完成
[2025-09-22 15:01:58.559] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-22 15:01:58.559] [INFO] Epson机器人管理器初始化完成
[2025-09-22 15:01:58.564] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-22 15:01:59.749] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-22 15:01:59.750] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-22 15:01:59.806] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 15:01:59.862] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 15:01:59.863] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-22 15:01:59.863] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-22 15:02:01.050] [INFO] 翻转电机控制面板资源释放完成
[2025-09-22 15:02:01.056] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-22 15:02:01.057] [INFO] Epson机器人管理器初始化完成
[2025-09-22 15:02:01.060] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-22 15:02:03.724] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-22 15:02:03.725] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-22 15:02:03.782] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 15:02:03.837] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 15:02:03.838] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-22 15:02:03.838] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-22 15:02:05.105] [INFO] 翻转电机控制面板资源释放完成
[2025-09-22 15:02:05.111] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-22 15:02:05.111] [INFO] Epson机器人管理器初始化完成
[2025-09-22 15:02:05.116] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-22 16:04:09.318] [INFO] 程序启动开始
[2025-09-22 16:04:09.320] [INFO] 配置系统初始化成功
[2025-09-22 16:04:09.370] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-22 16:04:09.370] [INFO] 配置系统初始化完成
[2025-09-22 16:04:09.371] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-22 16:04:09.396] [INFO] 开始初始化各个Manager...
[2025-09-22 16:04:09.397] [INFO] 初始化基础Manager...
[2025-09-22 16:04:09.401] [INFO] IO状态缓存初始化完成
[2025-09-22 16:04:09.408] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-22 16:04:09.409] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-22 16:04:09.410] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-22 16:04:09.411] [ERROR] DMC1000B控制卡初始化失败
[2025-09-22 16:04:09.411] [WARN] DMC1000BIO管理器初始化失败
[2025-09-22 16:04:09.415] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-22 16:04:09.415] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-22 16:04:09.415] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-22 16:04:09.416] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-22 16:04:09.452] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__42_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 130
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:04:09.466] [WARN] DMC1000B电机管理器初始化失败
[2025-09-22 16:04:09.466] [INFO] 初始化系统模式管理器...
[2025-09-22 16:04:09.470] [INFO] 开始初始化MotorManager...
[2025-09-22 16:04:09.472] [INFO] 模拟初始化运动控制卡
[2025-09-22 16:04:09.683] [INFO] 加载了8个电机的默认配置
[2025-09-22 16:04:09.685] [INFO] 电机监控任务已启动
[2025-09-22 16:04:09.685] [INFO] MotorManager初始化完成
[2025-09-22 16:04:09.686] [INFO] 初始化通信Manager...
[2025-09-22 16:04:09.687] [INFO] 电机监控循环开始
[2025-09-22 16:04:09.691] [INFO] 开始初始化ScannerManager...
[2025-09-22 16:04:09.696] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-22 16:04:09.700] [INFO] 串口初始化完成
[2025-09-22 16:04:09.702] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-22 16:04:09.704] [INFO] 扫描枪连接成功: COM1
[2025-09-22 16:04:09.705] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-22 16:04:09.706] [INFO] ScannerManager初始化完成
[2025-09-22 16:04:09.708] [INFO] 开始初始化ModbusTcpManager...
[2025-09-22 16:04:09.710] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-22 16:04:09.713] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-22 16:04:14.814] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:04:14.815] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-22 16:04:14.816] [INFO] ModbusTcpManager初始化完成
[2025-09-22 16:04:14.819] [INFO] 开始初始化EpsonRobotManager...
[2025-09-22 16:04:14.821] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-22 16:04:14.821] [INFO] EpsonRobotManager初始化完成
[2025-09-22 16:04:14.821] [INFO] 初始化视觉Manager...
[2025-09-22 16:04:14.824] [INFO] 开始初始化VisionManager...
[2025-09-22 16:04:14.824] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-22 16:04:14.825] [INFO] 模拟初始化相机，索引: 0
[2025-09-22 16:04:15.344] [INFO] 相机初始化成功
[2025-09-22 16:04:15.349] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-22 16:04:15.350] [INFO] 视觉配置加载完成
[2025-09-22 16:04:15.351] [INFO] VisionManager初始化完成
[2025-09-22 16:04:15.352] [INFO] 初始化数据Manager...
[2025-09-22 16:04:15.365] [INFO] 开始初始化StatisticsManager...
[2025-09-22 16:04:15.367] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-22 16:04:15.388] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-22 16:04:15.388] [INFO] 历史数据加载完成
[2025-09-22 16:04:15.389] [INFO] 自动保存任务已启动
[2025-09-22 16:04:15.389] [INFO] StatisticsManager初始化完成
[2025-09-22 16:04:15.389] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-22 16:04:15.390] [INFO] 所有Manager初始化完成
[2025-09-22 16:04:15.392] [INFO] 自动保存循环开始
[2025-09-22 16:04:15.434] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-22 16:04:15.435] [INFO] 主界面布局创建完成
[2025-09-22 16:04:15.436] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-22 16:04:22.713] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-22 16:04:22.713] [INFO] Epson机器人管理器初始化完成
[2025-09-22 16:04:22.734] [INFO] 扫描枪1默认配置: COM1, 115200, 8, One, None
[2025-09-22 16:04:22.735] [INFO] 扫描枪2默认配置: COM2, 115200, 8, One, None
[2025-09-22 16:04:22.735] [INFO] 扫描枪3默认配置: COM3, 115200, 8, One, None
[2025-09-22 16:04:22.735] [INFO] 扫描器控制面板获取MultiScannerManager实例成功
[2025-09-22 16:04:22.754] [INFO] 扫描枪1模块界面初始化完成
[2025-09-22 16:04:22.757] [INFO] 扫描枪2模块界面初始化完成
[2025-09-22 16:04:22.759] [INFO] 扫描枪3模块界面初始化完成
[2025-09-22 16:04:22.759] [INFO] 扫描器控制面板初始化完成
[2025-09-22 16:04:22.763] [INFO] 6轴机器人控制面板初始化完成 - Tab结构
[2025-09-22 16:04:22.766] [INFO] 开始初始化MultiScannerManager...
[2025-09-22 16:04:22.769] [INFO] 开始初始化扫描枪1...
[2025-09-22 16:04:22.770] [INFO] 扫描枪1串口初始化完成
[2025-09-22 16:04:22.771] [INFO] 扫描枪1初始化完成
[2025-09-22 16:04:22.771] [INFO] 开始初始化扫描枪2...
[2025-09-22 16:04:22.771] [INFO] 扫描枪2串口初始化完成
[2025-09-22 16:04:22.772] [INFO] 扫描枪2初始化完成
[2025-09-22 16:04:22.772] [INFO] 开始初始化扫描枪3...
[2025-09-22 16:04:22.772] [INFO] 扫描枪3串口初始化完成
[2025-09-22 16:04:22.773] [INFO] 扫描枪3初始化完成
[2025-09-22 16:04:22.773] [INFO] MultiScannerManager初始化完成
[2025-09-22 16:04:22.776] [INFO] 扫描器控制面板异步初始化完成
[2025-09-22 16:04:22.776] [INFO] 扫描器控制面板异步初始化完成
[2025-09-22 16:04:28.754] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-22 16:04:29.634] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-22 16:04:29.635] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-22 16:04:29.707] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:04:29.798] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:04:29.803] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-22 16:04:29.803] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-22 16:04:30.313] [INFO] 翻转电机控制面板资源释放完成
[2025-09-22 16:04:30.317] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-22 16:04:30.317] [INFO] Epson机器人管理器初始化完成
[2025-09-22 16:04:30.320] [INFO] 扫描器控制面板获取MultiScannerManager实例成功
[2025-09-22 16:04:30.323] [INFO] 扫描枪1模块界面初始化完成
[2025-09-22 16:04:30.325] [INFO] 扫描枪2模块界面初始化完成
[2025-09-22 16:04:30.328] [INFO] 扫描枪3模块界面初始化完成
[2025-09-22 16:04:30.328] [INFO] 扫描器控制面板初始化完成
[2025-09-22 16:04:30.332] [INFO] 6轴机器人控制面板初始化完成 - Tab结构
[2025-09-22 16:04:30.333] [INFO] 扫描器控制面板异步初始化完成
[2025-09-22 16:04:30.333] [INFO] 扫描器控制面板异步初始化完成
[2025-09-22 16:04:30.928] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-22 16:04:30.928] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-22 16:04:30.985] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:04:31.042] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:04:31.043] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-22 16:04:31.043] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-22 16:04:32.183] [INFO] 翻转电机控制面板资源释放完成
[2025-09-22 16:04:32.187] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-22 16:04:32.188] [INFO] Epson机器人管理器初始化完成
[2025-09-22 16:04:32.191] [INFO] 扫描器控制面板获取MultiScannerManager实例成功
[2025-09-22 16:04:32.193] [INFO] 扫描枪1模块界面初始化完成
[2025-09-22 16:04:32.196] [INFO] 扫描枪2模块界面初始化完成
[2025-09-22 16:04:32.199] [INFO] 扫描枪3模块界面初始化完成
[2025-09-22 16:04:32.199] [INFO] 扫描器控制面板初始化完成
[2025-09-22 16:04:32.203] [INFO] 6轴机器人控制面板初始化完成 - Tab结构
[2025-09-22 16:04:32.204] [INFO] 扫描器控制面板异步初始化完成
[2025-09-22 16:04:32.204] [INFO] 扫描器控制面板异步初始化完成
[2025-09-22 16:04:35.245] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-22 16:04:35.245] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-22 16:04:35.317] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:04:35.373] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:04:35.374] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-22 16:04:35.374] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-22 16:04:36.119] [INFO] 翻转电机控制面板资源释放完成
[2025-09-22 16:04:36.123] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-22 16:04:36.124] [INFO] Epson机器人管理器初始化完成
[2025-09-22 16:04:36.126] [INFO] 扫描器控制面板获取MultiScannerManager实例成功
[2025-09-22 16:04:36.129] [INFO] 扫描枪1模块界面初始化完成
[2025-09-22 16:04:36.131] [INFO] 扫描枪2模块界面初始化完成
[2025-09-22 16:04:36.134] [INFO] 扫描枪3模块界面初始化完成
[2025-09-22 16:04:36.135] [INFO] 扫描器控制面板初始化完成
[2025-09-22 16:04:36.139] [INFO] 6轴机器人控制面板初始化完成 - Tab结构
[2025-09-22 16:04:36.140] [INFO] 扫描器控制面板异步初始化完成
[2025-09-22 16:04:36.140] [INFO] 扫描器控制面板异步初始化完成
[2025-09-22 16:04:44.776] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-22 16:04:44.776] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-22 16:04:44.833] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:04:44.888] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:04:44.889] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-22 16:04:44.890] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-22 16:04:45.312] [INFO] 翻转电机控制面板资源释放完成
[2025-09-22 16:04:45.317] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-22 16:04:45.317] [INFO] Epson机器人管理器初始化完成
[2025-09-22 16:04:45.320] [INFO] 扫描器控制面板获取MultiScannerManager实例成功
[2025-09-22 16:04:45.323] [INFO] 扫描枪1模块界面初始化完成
[2025-09-22 16:04:45.328] [INFO] 扫描枪2模块界面初始化完成
[2025-09-22 16:04:45.331] [INFO] 扫描枪3模块界面初始化完成
[2025-09-22 16:04:45.331] [INFO] 扫描器控制面板初始化完成
[2025-09-22 16:04:45.342] [INFO] 6轴机器人控制面板初始化完成 - Tab结构
[2025-09-22 16:04:45.343] [INFO] 扫描器控制面板异步初始化完成
[2025-09-22 16:04:45.343] [INFO] 扫描器控制面板异步初始化完成
[2025-09-22 16:04:48.279] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-22 16:04:48.280] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-22 16:04:48.335] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:04:48.393] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:04:48.394] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-22 16:04:48.394] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-22 16:04:48.815] [INFO] 翻转电机控制面板资源释放完成
[2025-09-22 16:04:48.819] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-22 16:04:48.820] [INFO] Epson机器人管理器初始化完成
[2025-09-22 16:04:48.822] [INFO] 扫描器控制面板获取MultiScannerManager实例成功
[2025-09-22 16:04:48.825] [INFO] 扫描枪1模块界面初始化完成
[2025-09-22 16:04:48.828] [INFO] 扫描枪2模块界面初始化完成
[2025-09-22 16:04:48.832] [INFO] 扫描枪3模块界面初始化完成
[2025-09-22 16:04:48.833] [INFO] 扫描器控制面板初始化完成
[2025-09-22 16:04:48.836] [INFO] 6轴机器人控制面板初始化完成 - Tab结构
[2025-09-22 16:04:48.837] [INFO] 扫描器控制面板异步初始化完成
[2025-09-22 16:04:48.837] [INFO] 扫描器控制面板异步初始化完成
[2025-09-22 16:04:49.996] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-22 16:04:49.997] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-22 16:04:50.057] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:04:50.114] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:04:50.115] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-22 16:04:50.115] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-22 16:04:50.783] [INFO] 翻转电机控制面板资源释放完成
[2025-09-22 16:04:50.787] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-22 16:04:50.787] [INFO] Epson机器人管理器初始化完成
[2025-09-22 16:04:50.795] [INFO] 扫描器控制面板获取MultiScannerManager实例成功
[2025-09-22 16:04:50.797] [INFO] 扫描枪1模块界面初始化完成
[2025-09-22 16:04:50.800] [INFO] 扫描枪2模块界面初始化完成
[2025-09-22 16:04:50.803] [INFO] 扫描枪3模块界面初始化完成
[2025-09-22 16:04:50.803] [INFO] 扫描器控制面板初始化完成
[2025-09-22 16:04:50.807] [INFO] 6轴机器人控制面板初始化完成 - Tab结构
[2025-09-22 16:04:50.808] [INFO] 扫描器控制面板异步初始化完成
[2025-09-22 16:04:50.808] [INFO] 扫描器控制面板异步初始化完成
[2025-09-22 16:04:53.940] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-22 16:04:53.940] [INFO] Epson机器人管理器初始化完成
[2025-09-22 16:04:53.943] [INFO] 扫描器控制面板获取MultiScannerManager实例成功
[2025-09-22 16:04:53.947] [INFO] 扫描枪1模块界面初始化完成
[2025-09-22 16:04:53.949] [INFO] 扫描枪2模块界面初始化完成
[2025-09-22 16:04:53.952] [INFO] 扫描枪3模块界面初始化完成
[2025-09-22 16:04:53.952] [INFO] 扫描器控制面板初始化完成
[2025-09-22 16:04:53.956] [INFO] 6轴机器人控制面板初始化完成 - Tab结构
[2025-09-22 16:04:53.957] [INFO] 扫描器控制面板异步初始化完成
[2025-09-22 16:04:53.957] [INFO] 扫描器控制面板异步初始化完成
[2025-09-22 16:05:10.635] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-22 16:05:10.635] [INFO] Epson机器人管理器初始化完成
[2025-09-22 16:05:10.638] [INFO] 扫描器控制面板获取MultiScannerManager实例成功
[2025-09-22 16:05:10.641] [INFO] 扫描枪1模块界面初始化完成
[2025-09-22 16:05:10.643] [INFO] 扫描枪2模块界面初始化完成
[2025-09-22 16:05:10.645] [INFO] 扫描枪3模块界面初始化完成
[2025-09-22 16:05:10.646] [INFO] 扫描器控制面板初始化完成
[2025-09-22 16:05:10.650] [INFO] 6轴机器人控制面板初始化完成 - Tab结构
[2025-09-22 16:05:10.652] [INFO] 扫描器控制面板异步初始化完成
[2025-09-22 16:05:10.653] [INFO] 扫描器控制面板异步初始化完成
[2025-09-22 16:17:13.135] [INFO] 程序启动开始
[2025-09-22 16:17:13.136] [INFO] 配置系统初始化成功
[2025-09-22 16:17:13.186] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-22 16:17:13.186] [INFO] 配置系统初始化完成
[2025-09-22 16:17:13.187] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-22 16:17:13.207] [INFO] 开始初始化各个Manager...
[2025-09-22 16:17:13.207] [INFO] 初始化基础Manager...
[2025-09-22 16:17:13.214] [INFO] IO状态缓存初始化完成
[2025-09-22 16:17:13.222] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-22 16:17:13.222] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-22 16:17:13.224] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-22 16:17:13.224] [ERROR] DMC1000B控制卡初始化失败
[2025-09-22 16:17:13.224] [WARN] DMC1000BIO管理器初始化失败
[2025-09-22 16:17:13.228] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-22 16:17:13.229] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-22 16:17:13.229] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-22 16:17:13.229] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-22 16:17:13.269] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__42_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 130
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:17:13.283] [WARN] DMC1000B电机管理器初始化失败
[2025-09-22 16:17:13.283] [INFO] 初始化系统模式管理器...
[2025-09-22 16:17:13.287] [INFO] 开始初始化MotorManager...
[2025-09-22 16:17:13.289] [INFO] 模拟初始化运动控制卡
[2025-09-22 16:17:13.496] [INFO] 加载了8个电机的默认配置
[2025-09-22 16:17:13.497] [INFO] 电机监控任务已启动
[2025-09-22 16:17:13.497] [INFO] MotorManager初始化完成
[2025-09-22 16:17:13.497] [INFO] 初始化通信Manager...
[2025-09-22 16:17:13.498] [INFO] 电机监控循环开始
[2025-09-22 16:17:13.500] [INFO] 开始初始化ScannerManager...
[2025-09-22 16:17:13.663] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-22 16:17:13.666] [INFO] 串口初始化完成
[2025-09-22 16:17:13.667] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-22 16:17:13.670] [INFO] 扫描枪连接成功: COM1
[2025-09-22 16:17:13.671] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-22 16:17:13.671] [INFO] ScannerManager初始化完成
[2025-09-22 16:17:13.673] [INFO] 开始初始化ModbusTcpManager...
[2025-09-22 16:17:13.678] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-22 16:17:13.681] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-22 16:17:18.743] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:17:18.744] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-22 16:17:18.744] [INFO] ModbusTcpManager初始化完成
[2025-09-22 16:17:18.748] [INFO] 开始初始化EpsonRobotManager...
[2025-09-22 16:17:18.750] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-22 16:17:18.750] [INFO] EpsonRobotManager初始化完成
[2025-09-22 16:17:18.750] [INFO] 初始化视觉Manager...
[2025-09-22 16:17:18.753] [INFO] 开始初始化VisionManager...
[2025-09-22 16:17:18.753] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-22 16:17:18.755] [INFO] 模拟初始化相机，索引: 0
[2025-09-22 16:17:19.260] [INFO] 相机初始化成功
[2025-09-22 16:17:19.263] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-22 16:17:19.263] [INFO] 视觉配置加载完成
[2025-09-22 16:17:19.264] [INFO] VisionManager初始化完成
[2025-09-22 16:17:19.264] [INFO] 初始化数据Manager...
[2025-09-22 16:17:19.271] [INFO] 开始初始化StatisticsManager...
[2025-09-22 16:17:19.272] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-22 16:17:19.290] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-22 16:17:19.291] [INFO] 历史数据加载完成
[2025-09-22 16:17:19.291] [INFO] 自动保存任务已启动
[2025-09-22 16:17:19.292] [INFO] StatisticsManager初始化完成
[2025-09-22 16:17:19.293] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-22 16:17:19.293] [INFO] 所有Manager初始化完成
[2025-09-22 16:17:19.297] [INFO] 自动保存循环开始
[2025-09-22 16:17:19.341] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-22 16:17:19.342] [INFO] 主界面布局创建完成
[2025-09-22 16:17:19.344] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-22 16:17:58.210] [INFO] 扫描枪1默认配置: COM1, 115200, 8, One, None
[2025-09-22 16:17:58.211] [INFO] 扫描枪2默认配置: COM2, 115200, 8, One, None
[2025-09-22 16:17:58.211] [INFO] 扫描枪3默认配置: COM3, 115200, 8, One, None
[2025-09-22 16:17:58.211] [INFO] 扫描器控制面板获取MultiScannerManager实例成功
[2025-09-22 16:17:58.221] [INFO] 扫描枪1模块界面初始化完成
[2025-09-22 16:17:58.224] [INFO] 扫描枪2模块界面初始化完成
[2025-09-22 16:17:58.226] [INFO] 扫描枪3模块界面初始化完成
[2025-09-22 16:17:58.227] [INFO] 扫描器控制面板初始化完成
[2025-09-22 16:17:58.694] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-22 16:17:58.694] [INFO] Epson机器人管理器初始化完成
[2025-09-22 16:17:58.706] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-22 16:18:03.922] [INFO] 扫描器控制面板获取MultiScannerManager实例成功
[2025-09-22 16:18:03.926] [INFO] 扫描枪1模块界面初始化完成
[2025-09-22 16:18:03.928] [INFO] 扫描枪2模块界面初始化完成
[2025-09-22 16:18:03.931] [INFO] 扫描枪3模块界面初始化完成
[2025-09-22 16:18:03.931] [INFO] 扫描器控制面板初始化完成
[2025-09-22 16:18:05.905] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-22 16:18:06.643] [INFO] 扫描器控制面板获取MultiScannerManager实例成功
[2025-09-22 16:18:06.646] [INFO] 扫描枪1模块界面初始化完成
[2025-09-22 16:18:06.649] [INFO] 扫描枪2模块界面初始化完成
[2025-09-22 16:18:06.652] [INFO] 扫描枪3模块界面初始化完成
[2025-09-22 16:18:06.652] [INFO] 扫描器控制面板初始化完成
[2025-09-22 16:18:14.284] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-22 16:18:14.285] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-22 16:18:14.358] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:18:14.417] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:18:14.422] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-22 16:18:14.422] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-22 16:18:14.810] [INFO] 翻转电机控制面板资源释放完成
[2025-09-22 16:18:14.817] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-22 16:18:15.343] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-22 16:18:15.344] [INFO] Epson机器人管理器初始化完成
[2025-09-22 16:18:15.347] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-22 16:18:15.840] [INFO] 扫描器控制面板获取MultiScannerManager实例成功
[2025-09-22 16:18:15.843] [INFO] 扫描枪1模块界面初始化完成
[2025-09-22 16:18:15.846] [INFO] 扫描枪2模块界面初始化完成
[2025-09-22 16:18:15.848] [INFO] 扫描枪3模块界面初始化完成
[2025-09-22 16:18:15.849] [INFO] 扫描器控制面板初始化完成
[2025-09-22 16:18:20.755] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-22 16:18:20.755] [INFO] Epson机器人管理器初始化完成
[2025-09-22 16:18:20.758] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-22 16:19:27.735] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-22 16:19:27.735] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-22 16:19:27.795] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:19:27.852] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:19:27.853] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-22 16:19:27.853] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-22 16:19:31.048] [INFO] 翻转电机控制面板资源释放完成
[2025-09-22 16:19:31.057] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-22 16:19:31.057] [INFO] Epson机器人管理器初始化完成
[2025-09-22 16:19:31.060] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-22 16:19:32.656] [INFO] 扫描器控制面板获取MultiScannerManager实例成功
[2025-09-22 16:19:32.659] [INFO] 扫描枪1模块界面初始化完成
[2025-09-22 16:19:32.662] [INFO] 扫描枪2模块界面初始化完成
[2025-09-22 16:19:32.665] [INFO] 扫描枪3模块界面初始化完成
[2025-09-22 16:19:32.665] [INFO] 扫描器控制面板初始化完成
[2025-09-22 16:23:24.667] [INFO] 程序启动开始
[2025-09-22 16:23:24.669] [INFO] 配置系统初始化成功
[2025-09-22 16:23:24.730] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-22 16:23:24.730] [INFO] 配置系统初始化完成
[2025-09-22 16:23:24.730] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-22 16:23:24.752] [INFO] 开始初始化各个Manager...
[2025-09-22 16:23:24.752] [INFO] 初始化基础Manager...
[2025-09-22 16:23:24.757] [INFO] IO状态缓存初始化完成
[2025-09-22 16:23:24.763] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-22 16:23:24.763] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-22 16:23:24.765] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-22 16:23:24.765] [ERROR] DMC1000B控制卡初始化失败
[2025-09-22 16:23:24.765] [WARN] DMC1000BIO管理器初始化失败
[2025-09-22 16:23:24.769] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-22 16:23:24.769] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-22 16:23:24.769] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-22 16:23:24.770] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-22 16:23:24.808] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__42_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 130
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:23:24.821] [WARN] DMC1000B电机管理器初始化失败
[2025-09-22 16:23:24.821] [INFO] 初始化系统模式管理器...
[2025-09-22 16:23:24.825] [INFO] 开始初始化MotorManager...
[2025-09-22 16:23:24.827] [INFO] 模拟初始化运动控制卡
[2025-09-22 16:23:25.035] [INFO] 加载了8个电机的默认配置
[2025-09-22 16:23:25.036] [INFO] 电机监控任务已启动
[2025-09-22 16:23:25.036] [INFO] MotorManager初始化完成
[2025-09-22 16:23:25.037] [INFO] 初始化通信Manager...
[2025-09-22 16:23:25.038] [INFO] 电机监控循环开始
[2025-09-22 16:23:25.039] [INFO] 开始初始化ScannerManager...
[2025-09-22 16:23:25.042] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-22 16:23:25.044] [INFO] 串口初始化完成
[2025-09-22 16:23:25.046] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-22 16:23:25.049] [INFO] 扫描枪连接成功: COM1
[2025-09-22 16:23:25.050] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-22 16:23:25.050] [INFO] ScannerManager初始化完成
[2025-09-22 16:23:25.052] [INFO] 开始初始化ModbusTcpManager...
[2025-09-22 16:23:25.054] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-22 16:23:25.056] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-22 16:23:30.150] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:23:30.152] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-22 16:23:30.152] [INFO] ModbusTcpManager初始化完成
[2025-09-22 16:23:30.154] [INFO] 开始初始化EpsonRobotManager...
[2025-09-22 16:23:30.156] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-22 16:23:30.156] [INFO] EpsonRobotManager初始化完成
[2025-09-22 16:23:30.156] [INFO] 初始化视觉Manager...
[2025-09-22 16:23:30.159] [INFO] 开始初始化VisionManager...
[2025-09-22 16:23:30.159] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-22 16:23:30.160] [INFO] 模拟初始化相机，索引: 0
[2025-09-22 16:23:30.669] [INFO] 相机初始化成功
[2025-09-22 16:23:30.671] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-22 16:23:30.671] [INFO] 视觉配置加载完成
[2025-09-22 16:23:30.672] [INFO] VisionManager初始化完成
[2025-09-22 16:23:30.672] [INFO] 初始化数据Manager...
[2025-09-22 16:23:30.676] [INFO] 开始初始化StatisticsManager...
[2025-09-22 16:23:30.677] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-22 16:23:30.688] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-22 16:23:30.688] [INFO] 历史数据加载完成
[2025-09-22 16:23:30.689] [INFO] 自动保存任务已启动
[2025-09-22 16:23:30.689] [INFO] StatisticsManager初始化完成
[2025-09-22 16:23:30.689] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-22 16:23:30.690] [INFO] 所有Manager初始化完成
[2025-09-22 16:23:30.692] [INFO] 自动保存循环开始
[2025-09-22 16:23:30.731] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-22 16:23:30.731] [INFO] 主界面布局创建完成
[2025-09-22 16:23:30.732] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-22 16:23:36.802] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-22 16:23:36.802] [INFO] Epson机器人管理器初始化完成
[2025-09-22 16:23:36.815] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-22 16:23:38.227] [INFO] 扫描枪1默认配置: COM1, 115200, 8, One, None
[2025-09-22 16:23:38.227] [INFO] 扫描枪2默认配置: COM2, 115200, 8, One, None
[2025-09-22 16:23:38.227] [INFO] 扫描枪3默认配置: COM3, 115200, 8, One, None
[2025-09-22 16:23:38.228] [INFO] 扫描器控制面板获取MultiScannerManager实例成功
[2025-09-22 16:23:38.237] [INFO] 扫描枪1模块界面初始化完成
[2025-09-22 16:23:38.240] [INFO] 扫描枪2模块界面初始化完成
[2025-09-22 16:23:38.243] [INFO] 扫描枪3模块界面初始化完成
[2025-09-22 16:23:38.243] [INFO] 扫描器控制面板初始化完成
[2025-09-22 16:37:46.753] [INFO] 程序启动开始
[2025-09-22 16:37:46.755] [INFO] 配置系统初始化成功
[2025-09-22 16:37:46.804] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-22 16:37:46.804] [INFO] 配置系统初始化完成
[2025-09-22 16:37:46.804] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-22 16:37:46.831] [INFO] 开始初始化各个Manager...
[2025-09-22 16:37:46.832] [INFO] 初始化基础Manager...
[2025-09-22 16:37:46.836] [INFO] IO状态缓存初始化完成
[2025-09-22 16:37:46.843] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-22 16:37:46.843] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-22 16:37:46.845] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-22 16:37:46.845] [ERROR] DMC1000B控制卡初始化失败
[2025-09-22 16:37:46.846] [WARN] DMC1000BIO管理器初始化失败
[2025-09-22 16:37:46.849] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-22 16:37:46.850] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-22 16:37:46.850] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-22 16:37:46.850] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-22 16:37:46.888] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__42_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 130
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:37:46.903] [WARN] DMC1000B电机管理器初始化失败
[2025-09-22 16:37:46.903] [INFO] 初始化系统模式管理器...
[2025-09-22 16:37:46.907] [INFO] 开始初始化MotorManager...
[2025-09-22 16:37:46.909] [INFO] 模拟初始化运动控制卡
[2025-09-22 16:37:47.122] [INFO] 加载了8个电机的默认配置
[2025-09-22 16:37:47.124] [INFO] 电机监控任务已启动
[2025-09-22 16:37:47.124] [INFO] MotorManager初始化完成
[2025-09-22 16:37:47.125] [INFO] 初始化通信Manager...
[2025-09-22 16:37:47.129] [INFO] 开始初始化ScannerManager...
[2025-09-22 16:37:47.131] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-22 16:37:47.136] [INFO] 电机监控循环开始
[2025-09-22 16:37:47.136] [INFO] 串口初始化完成
[2025-09-22 16:37:47.139] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-22 16:37:47.152] [INFO] 扫描枪连接成功: COM1
[2025-09-22 16:37:47.153] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-22 16:37:47.153] [INFO] ScannerManager初始化完成
[2025-09-22 16:37:47.156] [INFO] 开始初始化ModbusTcpManager...
[2025-09-22 16:37:47.159] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-22 16:37:47.163] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-22 16:37:52.215] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:37:52.216] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-22 16:37:52.216] [INFO] ModbusTcpManager初始化完成
[2025-09-22 16:37:52.219] [INFO] 开始初始化EpsonRobotManager...
[2025-09-22 16:37:52.220] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-22 16:37:52.220] [INFO] EpsonRobotManager初始化完成
[2025-09-22 16:37:52.220] [INFO] 初始化视觉Manager...
[2025-09-22 16:37:52.222] [INFO] 开始初始化VisionManager...
[2025-09-22 16:37:52.223] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-22 16:37:52.224] [INFO] 模拟初始化相机，索引: 0
[2025-09-22 16:37:52.735] [INFO] 相机初始化成功
[2025-09-22 16:37:52.739] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-22 16:37:52.740] [INFO] 视觉配置加载完成
[2025-09-22 16:37:52.741] [INFO] VisionManager初始化完成
[2025-09-22 16:37:52.742] [INFO] 初始化数据Manager...
[2025-09-22 16:37:52.756] [INFO] 开始初始化StatisticsManager...
[2025-09-22 16:37:52.758] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-22 16:37:52.779] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-22 16:37:52.780] [INFO] 历史数据加载完成
[2025-09-22 16:37:52.796] [INFO] 自动保存任务已启动
[2025-09-22 16:37:52.796] [INFO] StatisticsManager初始化完成
[2025-09-22 16:37:52.797] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-22 16:37:52.797] [INFO] 所有Manager初始化完成
[2025-09-22 16:37:52.800] [INFO] 自动保存循环开始
[2025-09-22 16:37:52.839] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-22 16:37:52.839] [INFO] 主界面布局创建完成
[2025-09-22 16:37:52.840] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-22 16:37:58.378] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-22 16:37:58.379] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-22 16:37:58.473] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:37:58.534] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-22 16:37:58.538] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-22 16:37:58.538] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-22 16:38:00.000] [INFO] 翻转电机控制面板资源释放完成
[2025-09-22 16:38:00.007] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-22 16:38:00.007] [INFO] Epson机器人管理器初始化完成
[2025-09-22 16:38:00.021] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-22 16:38:00.876] [INFO] 扫描枪1默认配置: COM1, 115200, 8, One, None
[2025-09-22 16:38:00.877] [INFO] 扫描枪2默认配置: COM2, 115200, 8, One, None
[2025-09-22 16:38:00.877] [INFO] 扫描枪3默认配置: COM3, 115200, 8, One, None
[2025-09-22 16:38:00.877] [INFO] 扫描器控制面板获取MultiScannerManager实例成功
[2025-09-22 16:38:00.887] [INFO] 扫描枪1模块界面初始化完成
[2025-09-22 16:38:00.891] [INFO] 扫描枪2模块界面初始化完成
[2025-09-22 16:38:00.893] [INFO] 扫描枪3模块界面初始化完成
[2025-09-22 16:38:00.893] [INFO] 扫描器控制面板初始化完成
