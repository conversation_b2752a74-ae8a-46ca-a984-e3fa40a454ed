# 参数结构分析报告

## 概述
本文档分析了HR2项目中所有现有的参数结构，为重新设计统一的参数管理系统提供基础。

## 1. App.config 参数分析

### 1.1 串口配置
- `ScannerComPort`: "COM1" - 扫描器串口号
- `ScannerBaudRate`: 9600 - 扫描器波特率

### 1.2 Epson机器人配置
- `EpsonRobotIP`: "*************" - 机器人IP地址
- `EpsonControlPort`: 5000 - 控制端口
- `EpsonDataPort`: 5001 - 数据端口
- `EpsonPassword`: "EPSON" - 连接密码
- `EpsonConnectTimeout`: 5000 - 连接超时(ms)
- `EpsonReceiveTimeout`: 3000 - 接收超时(ms)
- `EpsonSendTimeout`: 3000 - 发送超时(ms)
- `EpsonScanInterval`: 100 - 扫描间隔(ms)
- `EpsonAutoReconnect`: true - 自动重连
- `EpsonReconnectInterval`: 5000 - 重连间隔(ms)
- `EpsonMaxReconnectAttempts`: 10 - 最大重连次数

### 1.3 Modbus TCP配置
- `ModbusTcpIP`: "*************" - Modbus服务器IP
- `ModbusTcpPort`: 502 - Modbus端口
- `ModbusSlaveId`: 1 - 从站ID

### 1.4 硬件配置
- `DMC1000CardIndex`: 0 - 雷赛卡索引
- `VisionCameraIndex`: 0 - 视觉相机索引

### 1.5 路径配置
- `VisionConfigPath`: "Config\VisionConfig.json" - 视觉配置文件路径
- `LogPath`: "Logs\" - 日志路径
- `StatisticsDataPath`: "Data\Statistics.csv" - 统计数据路径

### 1.6 其他配置
- `LogLevel`: "Info" - 日志级别
- `AutoSaveInterval`: 300 - 自动保存间隔(秒)

## 2. Manager类期望的参数结构

### 2.1 EpsonRobotManager 期望参数
```csharp
// 期望从CommunicationSettings获取以下属性：
- IPAddress (string)
- ControlPort (int)
- DataPort (int)
- Password (string)
- ConnectTimeout (int)
- ReceiveTimeout (int)
- SendTimeout (int)
- ScanInterval (int)
- AutoReconnect (bool)
- ReconnectInterval (int)
- MaxReconnectAttempts (int)
```

### 2.2 MultiScannerManager 期望参数
```csharp
// 对于每个扫描器(1,2,3)，期望以下参数：
- MultiScanner{N}ComPort (string)
- MultiScanner{N}BaudRate (int)
- MultiScanner{N}DataBits (int)
- MultiScanner{N}StopBits (int)
- MultiScanner{N}Parity (string)
- MultiScanner{N}Timeout (int)
```

### 2.3 ModbusTcpManager 期望参数
```csharp
// 期望从CommunicationSettings获取：
- ModbusTcpIP (string)
- ModbusTcpPort (int)
- ModbusSlaveId (int)
- ModbusConnectTimeout (int)
- ModbusReceiveTimeout (int)
- ModbusSendTimeout (int)
- ModbusReadTimeout (int)
- ModbusWriteTimeout (int)
```

### 2.4 VisionManager 期望参数
```csharp
// 期望从VisionSettings获取：
- CameraIndex (int)
- ConfigPath (string) // 注意：代码中使用ConfigPath，不是VisionConfigPath
- DetectionInterval (int)
- ConfidenceThreshold (double)
```

### 2.5 DMC1000BMotorManager 期望参数
```csharp
// 期望从MotorSettings获取：
// 翻转电机参数
- LeftFlipPulseEquivalent (double)
- LeftFlipMaxSpeed (double)
- LeftFlipStartSpeed (double)
- LeftFlipAcceleration (double)
- LeftFlipHomeSpeed (double)
- LeftFlipHomeDirection (int)
- LeftFlipHomeIO (int)
- LeftFlipPositiveLimitIO (int)
- LeftFlipNegativeLimitIO (int)
- LeftFlipHomeTimeout (int)

// 右翻转电机参数（类似左侧，RightFlip前缀）
- RightFlip... (相同结构)

// 翻转电机位置参数
- LeftFlipHomePosition (double)
- LeftFlipWorkPosition (double)
- LeftFlipSafePosition (double)
- LeftFlipTestPosition (double)
- RightFlipHomePosition (double)
- RightFlipWorkPosition (double)
- RightFlipSafePosition (double)
- RightFlipTestPosition (double)

// 皮带电机参数
- InputBeltPulseEquivalent (double)
- InputBeltMaxSpeed (double)
- InputBeltStartSpeed (double)
- InputBeltAcceleration (double)
- InputBeltJogDistance (double)
- OutputBeltPulseEquivalent (double)
- OutputBeltMaxSpeed (double)
- OutputBeltStartSpeed (double)
- OutputBeltAcceleration (double)
- OutputBeltJogDistance (double)
```

### 2.6 MotorManager 期望参数
```csharp
// 期望从SystemSettings获取：
- MotorMonitorInterval (int)
- MaxMotorCount (int)
```

### 2.7 ScannerManager 期望参数
```csharp
// 期望从CommunicationSettings获取：
- ScannerComPort (string)
- ScannerBaudRate (int)
- ScannerDataBits (int)
- ScannerStopBits (int)
- ScannerParity (string)
- ScannerTimeout (int)
```

### 2.8 ScannerControlPanel 期望参数
```csharp
// 期望从CommunicationSettings获取（根据扫描器ID）：
- MultiScanner1BaudRate (int)
- MultiScanner2BaudRate (int)
- MultiScanner3BaudRate (int)
```

## 3. 缺失的类型定义

### 3.1 FlipMotorPositions
```csharp
public class FlipMotorPositions
{
    public double HomePosition { get; set; }
    public double WorkPosition { get; set; }
    public double SafePosition { get; set; }
    public double TestPosition { get; set; }
}
```

### 3.2 ScannerPortConfig
```csharp
public class ScannerPortConfig
{
    public string PortName { get; set; }
    public int BaudRate { get; set; }
    public int DataBits { get; set; }
    public string StopBits { get; set; }
    public string Parity { get; set; }
    public int ReadTimeout { get; set; }
    public int WriteTimeout { get; set; }
}
```

## 4. 参数分组建议

基于以上分析，建议采用以下层次化结构：

### 4.1 SystemSettings
- Name, Version, LogLevel, LogPath
- MotorMonitorInterval, MaxMotorCount
- AutoSaveInterval

### 4.2 CommunicationSettings
- EpsonRobot1: EpsonRobotConfig
- EpsonRobot2: EpsonRobotConfig
- Scanner: ScannerConfig
- MultiScanner1/2/3: ScannerConfig
- Modbus: ModbusConfig

### 4.3 MotorSettings
- 翻转电机配置（左右）
- 皮带电机配置（输入输出）
- 位置参数

### 4.4 VisionSettings
- CameraIndex, ConfigPath
- DetectionInterval, ConfidenceThreshold

### 4.5 IOSettings
- DMC1000CardIndex
- 其他IO相关配置

## 5. 下一步行动

1. 基于此分析重新设计AppSettings.cs结构
2. 确保所有参数都被正确包含
3. 保持与现有Manager类的兼容性
4. 实现必要的配置类型定义
