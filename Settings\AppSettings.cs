using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.Serialization.Formatters.Binary;

namespace MyHMI.Settings
{
    /// <summary>
    /// 应用程序设置根类 - 包含所有参数分组
    /// </summary>
    [Serializable]
    public class AppSettings
    {
        /// <summary>
        /// 电机相关参数
        /// </summary>
        public MotorSettings Motor { get; set; } = new MotorSettings();

        /// <summary>
        /// 通信相关参数
        /// </summary>
        public CommunicationSettings Communication { get; set; } = new CommunicationSettings();

        /// <summary>
        /// UI相关参数
        /// </summary>
        public UISettings UI { get; set; } = new UISettings();

        /// <summary>
        /// 系统相关参数
        /// </summary>
        public SystemSettings System { get; set; } = new SystemSettings();

        /// <summary>
        /// IO相关参数
        /// </summary>
        public IOSettings IO { get; set; } = new IOSettings();

        /// <summary>
        /// 视觉相关参数
        /// </summary>
        public VisionSettings Vision { get; set; } = new VisionSettings();

        /// <summary>
        /// 统计相关参数
        /// </summary>
        public StatisticsSettings Statistics { get; set; } = new StatisticsSettings();

        /// <summary>
        /// 工作流相关参数
        /// </summary>
        public WorkflowSettings Workflow { get; set; } = new WorkflowSettings();
    }

    /// <summary>
    /// 电机设置类
    /// </summary>
    [Serializable]
    public class MotorSettings
    {
        // 翻转电机参数
        public double LeftFlipPulseEquivalent { get; set; } = 0.012;
        public double LeftFlipMaxSpeed { get; set; } = 60;
        public double LeftFlipStartSpeed { get; set; } = 10;
        public double LeftFlipAcceleration { get; set; } = 120;
        public double LeftFlipAccelerationTime { get; set; } = 0.1;
        public double LeftFlipHomeSpeed { get; set; } = 20;
        public bool LeftFlipHomeDirection { get; set; } = false;
        public int LeftFlipHomeIO { get; set; } = 0;
        public int LeftFlipPositiveLimitIO { get; set; } = 8;
        public int LeftFlipNegativeLimitIO { get; set; } = 16;
        public int LeftFlipHomeTimeout { get; set; } = 30000;
        public double LeftFlipJogAngle { get; set; } = 5.0; // 左翻转电机点动角度，默认5度

        public double RightFlipPulseEquivalent { get; set; } = 0.012;
        public double RightFlipMaxSpeed { get; set; } = 60;
        public double RightFlipStartSpeed { get; set; } = 10;
        public double RightFlipAcceleration { get; set; } = 120;
        public double RightFlipAccelerationTime { get; set; } = 0.1;
        public double RightFlipHomeSpeed { get; set; } = 20;
        public bool RightFlipHomeDirection { get; set; } = false;
        public int RightFlipHomeIO { get; set; } = 1;
        public int RightFlipPositiveLimitIO { get; set; } = 9;
        public int RightFlipNegativeLimitIO { get; set; } = 17;
        public int RightFlipHomeTimeout { get; set; } = 30000;
        public double RightFlipJogAngle { get; set; } = 5.0; // 右翻转电机点动角度，默认5度

        // 翻转电机位置保存
        public double LeftFlipPosition1 { get; set; } = double.NaN;
        public double LeftFlipPosition2 { get; set; } = double.NaN;
        public double LeftFlipPosition3 { get; set; } = double.NaN;
        public double LeftFlipPosition4 { get; set; } = double.NaN;
        public double LeftFlipPosition5 { get; set; } = double.NaN;

        public double RightFlipPosition1 { get; set; } = double.NaN;
        public double RightFlipPosition2 { get; set; } = double.NaN;
        public double RightFlipPosition3 { get; set; } = double.NaN;
        public double RightFlipPosition4 { get; set; } = double.NaN;
        public double RightFlipPosition5 { get; set; } = double.NaN;

        // IO输出逻辑反转配置（解决硬件接线反逻辑问题）
        /// <summary>
        /// 是否反转所有输出IO逻辑（解决硬件接线反逻辑问题）
        /// true: IO输出逻辑ON对应硬件OFF，IO输出逻辑OFF对应硬件ON
        /// false: IO输出逻辑与硬件状态一致
        ///
        /// 重要：根据硬件接线特性，此值应设为true以正确处理反逻辑
        /// </summary>
        public bool InvertAllOutputs { get; set; } = true;

        /// <summary>
        /// 程序关闭时的安全状态（逻辑状态）
        /// true: 程序关闭时所有IO设置为逻辑ON状态
        /// false: 程序关闭时所有IO设置为逻辑OFF状态
        ///
        /// 重要说明：由于硬件接线反逻辑特性，要让硬件关闭需要：
        /// - 逻辑OFF + 反转启用 → 物理ON → 硬件关闭
        /// - 逻辑ON + 反转启用 → 物理OFF → 硬件激活
        /// 因此当InvertAllOutputs=true时，此值应设为false以确保硬件安全关闭
        /// </summary>
        public bool OutputSafeState { get; set; } = false;

        // 皮带电机参数
        public double InputBeltPulseEquivalent { get; set; } = 0.01;
        public double InputBeltMaxSpeed { get; set; } = 100;
        public double InputBeltStartSpeed { get; set; } = 10;
        public double InputBeltAcceleration { get; set; } = 500;
        public double InputBeltAccelerationTime { get; set; } = 0.1;
        public double InputBeltJogDistance { get; set; } = 10;

        public double OutputBeltPulseEquivalent { get; set; } = 0.01;
        public double OutputBeltMaxSpeed { get; set; } = 100;
        public double OutputBeltStartSpeed { get; set; } = 10;
        public double OutputBeltAcceleration { get; set; } = 500;
        public double OutputBeltAccelerationTime { get; set; } = 0.1;
        public double OutputBeltJogDistance { get; set; } = 10;

        // 通用电机参数
        public int AxisCount { get; set; } = 4;
        public double DefaultSpeed { get; set; } = 1000;
        public double DefaultAcceleration { get; set; } = 5000;
        public double HomeSpeed { get; set; } = 500;
        public double MaxPosition { get; set; } = 100000;
        public double MinPosition { get; set; } = -100000;
        public double PositionTolerance { get; set; } = 0.01;

        // 为了兼容DMC1000BMotorManager，提供位置参数别名
        public double LeftFlipHomePosition { get => LeftFlipPosition1; set => LeftFlipPosition1 = value; }
        public double LeftFlipWorkPosition { get => LeftFlipPosition2; set => LeftFlipPosition2 = value; }
        public double LeftFlipSafePosition { get => LeftFlipPosition3; set => LeftFlipPosition3 = value; }
        public double LeftFlipTestPosition { get => LeftFlipPosition4; set => LeftFlipPosition4 = value; }

        public double RightFlipHomePosition { get => RightFlipPosition1; set => RightFlipPosition1 = value; }
        public double RightFlipWorkPosition { get => RightFlipPosition2; set => RightFlipPosition2 = value; }
        public double RightFlipSafePosition { get => RightFlipPosition3; set => RightFlipPosition3 = value; }
        public double RightFlipTestPosition { get => RightFlipPosition4; set => RightFlipPosition4 = value; }
    }

    /// <summary>
    /// 通信设置类
    /// </summary>
    [Serializable]
    public class CommunicationSettings
    {
        // 扫描器设置
        public string ScannerComPort { get; set; } = "COM1";
        public int ScannerBaudRate { get; set; } = 9600;
        public int ScannerDataBits { get; set; } = 8;
        public int ScannerStopBits { get; set; } = 1;
        public string ScannerParity { get; set; } = "None";
        public int ScannerTimeout { get; set; } = 3000;
        public bool ScannerAutoReconnect { get; set; } = true;
        public int ScannerReconnectInterval { get; set; } = 5000;

        // Epson机器人1设置
        public string EpsonRobot1IP { get; set; } = "*************";
        public int EpsonRobot1ControlPort { get; set; } = 5000;
        public int EpsonRobot1DataPort { get; set; } = 5001;
        public string EpsonRobot1Password { get; set; } = "EPSON";
        public int EpsonRobot1ConnectTimeout { get; set; } = 5000;
        public int EpsonRobot1ReceiveTimeout { get; set; } = 3000;
        public int EpsonRobot1SendTimeout { get; set; } = 3000;
        public int EpsonRobot1ScanInterval { get; set; } = 100;
        public bool EpsonRobot1AutoReconnect { get; set; } = true;
        public int EpsonRobot1ReconnectInterval { get; set; } = 5000;
        public int EpsonRobot1MaxReconnectAttempts { get; set; } = 10;

        // Epson机器人2设置
        public string EpsonRobot2IP { get; set; } = "*************";
        public int EpsonRobot2ControlPort { get; set; } = 5000;
        public int EpsonRobot2DataPort { get; set; } = 5001;
        public string EpsonRobot2Password { get; set; } = "EPSON";
        public int EpsonRobot2ConnectTimeout { get; set; } = 5000;
        public int EpsonRobot2ReceiveTimeout { get; set; } = 3000;
        public int EpsonRobot2SendTimeout { get; set; } = 3000;
        public int EpsonRobot2ScanInterval { get; set; } = 100;
        public bool EpsonRobot2AutoReconnect { get; set; } = true;
        public int EpsonRobot2ReconnectInterval { get; set; } = 5000;
        public int EpsonRobot2MaxReconnectAttempts { get; set; } = 10;

        // Modbus TCP设置
        public string ModbusTcpIP { get; set; } = "*************";
        public int ModbusTcpPort { get; set; } = 502;
        public int ModbusSlaveId { get; set; } = 1;
        public int ModbusConnectTimeout { get; set; } = 5000;
        public int ModbusReceiveTimeout { get; set; } = 3000;
        public int ModbusSendTimeout { get; set; } = 3000;
        public int ModbusReadTimeout { get; set; } = 3000;
        public int ModbusWriteTimeout { get; set; } = 3000;
        public int ModbusTcpTimeout { get; set; } = 3000;
        public bool ModbusTcpAutoReconnect { get; set; } = true;
        public int ModbusTcpReconnectInterval { get; set; } = 5000;
        public int ModbusTcpMaxRetries { get; set; } = 3;

        // 多扫描器设置
        public string Scanner1ComPort { get; set; } = "COM1";
        public int Scanner1BaudRate { get; set; } = 115200;
        public int Scanner1DataBits { get; set; } = 8;
        public string Scanner1StopBits { get; set; } = "1";
        public string Scanner1Parity { get; set; } = "无校验";
        public int Scanner1ReadTimeout { get; set; } = 1000;
        public int Scanner1WriteTimeout { get; set; } = 1000;

        public string Scanner2ComPort { get; set; } = "COM2";
        public int Scanner2BaudRate { get; set; } = 115200;
        public int Scanner2DataBits { get; set; } = 8;
        public string Scanner2StopBits { get; set; } = "1";
        public string Scanner2Parity { get; set; } = "无校验";
        public int Scanner2ReadTimeout { get; set; } = 1000;
        public int Scanner2WriteTimeout { get; set; } = 1000;

        public string Scanner3ComPort { get; set; } = "COM3";
        public int Scanner3BaudRate { get; set; } = 115200;
        public int Scanner3DataBits { get; set; } = 8;
        public string Scanner3StopBits { get; set; } = "1";
        public string Scanner3Parity { get; set; } = "无校验";
        public int Scanner3ReadTimeout { get; set; } = 1000;
        public int Scanner3WriteTimeout { get; set; } = 1000;

        // 为了兼容MultiScannerManager，提供MultiScanner别名属性
        public string MultiScanner1PortName { get => Scanner1ComPort; set => Scanner1ComPort = value; }
        public string MultiScanner1ComPort { get => Scanner1ComPort; set => Scanner1ComPort = value; }
        public int MultiScanner1BaudRate { get => Scanner1BaudRate; set => Scanner1BaudRate = value; }
        public int MultiScanner1DataBits { get => Scanner1DataBits; set => Scanner1DataBits = value; }
        public string MultiScanner1StopBits { get => Scanner1StopBits; set => Scanner1StopBits = value; }
        public string MultiScanner1Parity { get => Scanner1Parity; set => Scanner1Parity = value; }
        public int MultiScanner1Timeout { get => Scanner1ReadTimeout; set => Scanner1ReadTimeout = value; }

        public string MultiScanner2PortName { get => Scanner2ComPort; set => Scanner2ComPort = value; }
        public string MultiScanner2ComPort { get => Scanner2ComPort; set => Scanner2ComPort = value; }
        public int MultiScanner2BaudRate { get => Scanner2BaudRate; set => Scanner2BaudRate = value; }
        public int MultiScanner2DataBits { get => Scanner2DataBits; set => Scanner2DataBits = value; }
        public string MultiScanner2StopBits { get => Scanner2StopBits; set => Scanner2StopBits = value; }
        public string MultiScanner2Parity { get => Scanner2Parity; set => Scanner2Parity = value; }
        public int MultiScanner2Timeout { get => Scanner2ReadTimeout; set => Scanner2ReadTimeout = value; }

        public string MultiScanner3PortName { get => Scanner3ComPort; set => Scanner3ComPort = value; }
        public string MultiScanner3ComPort { get => Scanner3ComPort; set => Scanner3ComPort = value; }
        public int MultiScanner3BaudRate { get => Scanner3BaudRate; set => Scanner3BaudRate = value; }
        public int MultiScanner3DataBits { get => Scanner3DataBits; set => Scanner3DataBits = value; }
        public string MultiScanner3StopBits { get => Scanner3StopBits; set => Scanner3StopBits = value; }
        public string MultiScanner3Parity { get => Scanner3Parity; set => Scanner3Parity = value; }
        public int MultiScanner3Timeout { get => Scanner3ReadTimeout; set => Scanner3ReadTimeout = value; }
    }

    /// <summary>
    /// UI设置类
    /// </summary>
    [Serializable]
    public class UISettings
    {
        public string Theme { get; set; } = "Default";
        public string Language { get; set; } = "zh-CN";
        public int AutoRefreshInterval { get; set; } = 1000;
        public bool ShowDebugInfo { get; set; } = false;
        public string WindowState { get; set; } = "Maximized";
        public string LastSelectedPath { get; set; } = "";
        public bool AutoStartEnabled { get; set; } = false;
    }

    /// <summary>
    /// 系统设置类
    /// </summary>
    [Serializable]
    public class SystemSettings
    {
        public string Name { get; set; } = "上位机控制系统";
        public string Version { get; set; } = "1.0.0";
        public string LogLevel { get; set; } = "Info";
        public int MaxLogFiles { get; set; } = 30;
        public bool AutoStartWorkflow { get; set; } = false;
        public string LastSystemMode { get; set; } = "Manual";
        public bool LoggingEnabled { get; set; } = true;
        public string LogPath { get; set; } = "Logs\\";
        public int AutoSaveInterval { get; set; } = 300;
        public int MotorMonitorInterval { get; set; } = 100;
        public int MaxMotorCount { get; set; } = 8;

        // 安全管理器配置
        public int SafetyMonitoringInterval { get; set; } = 20;
        public int SafetyDebounceTime { get; set; } = 50;
        public bool SafetyVerboseLogging { get; set; } = false;
        public int SafetyEmergencyRecoveryDelay { get; set; } = 1000;
        public int SafetyIndicatorSwitchDelay { get; set; } = 100;
        public int SafetyMaxErrorRetryCount { get; set; } = 3;
    }

    /// <summary>
    /// IO设置类
    /// </summary>
    [Serializable]
    public class IOSettings
    {
        public string CardType { get; set; } = "DMC1000";
        public int InputChannels { get; set; } = 16;
        public int OutputChannels { get; set; } = 16;
        public int MonitorInterval { get; set; } = 100;
        public int DebounceTime { get; set; } = 50;
        public int DMC1000CardIndex { get; set; } = 0;
        public Dictionary<string, bool> OutputStates { get; set; } = new Dictionary<string, bool>();
    }

    /// <summary>
    /// 视觉设置类
    /// </summary>
    [Serializable]
    public class VisionSettings
    {
        public int CameraIndex { get; set; } = 0;
        public int ImageWidth { get; set; } = 1920;
        public int ImageHeight { get; set; } = 1080;
        public int DetectionInterval { get; set; } = 1000;
        public double ConfidenceThreshold { get; set; } = 0.8;
        public int MaxDetectionTime { get; set; } = 5000;
        public bool SaveImages { get; set; } = true;
        public string ImageSavePath { get; set; } = "Images";
        public string VisionConfigPath { get; set; } = "Config\\VisionConfig.json";

        // 为了兼容VisionManager，提供ConfigPath别名属性
        public string ConfigPath { get => VisionConfigPath; set => VisionConfigPath = value; }
    }

    /// <summary>
    /// 统计设置类
    /// </summary>
    [Serializable]
    public class StatisticsSettings
    {
        public int DataRetentionDays { get; set; } = 90;
        public bool AutoExport { get; set; } = false;
        public int ExportInterval { get; set; } = 24;
        public string ExportPath { get; set; } = "Export";
        public int CacheSize { get; set; } = 1000;
        public string StatisticsDataPath { get; set; } = "Data\\Statistics.csv";
    }

    /// <summary>
    /// 工作流设置类
    /// </summary>
    [Serializable]
    public class WorkflowSettings
    {
        public bool Enabled { get; set; } = true;
        public bool AutoStart { get; set; } = false;
        public int TimeoutSeconds { get; set; } = 60;
        public int RetryCount { get; set; } = 3;
        public int StepDelayMs { get; set; } = 100;
    }


}
