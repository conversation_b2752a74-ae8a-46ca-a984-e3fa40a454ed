using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using MyHMI.Helpers;
using MyHMI.Managers;
using MyHMI.Models;

namespace MyHMI.UI.Controls
{
    /// <summary>
    /// IO读取状态面板 - 根据端口定义文件设计
    /// 支持基础IO（I0001-I0016）和扩展IO（I0101-I0116）
    /// </summary>
    public partial class IOReadPanel : UserControl
    {
        #region 私有字段

        private Panel _mainPanel;
        private Label _titleLabel;
        private Panel _basicInputGroup;
        private Panel _extendedInputGroup;

        // IO配置数据 - 根据端口定义文件
        private readonly List<IOPortDefinition> _basicInputPorts;
        private readonly List<IOPortDefinition> _extendedInputPorts;

        // IO状态显示控件
        private readonly Dictionary<string, Label> _ioLabels = new Dictionary<string, Label>();
        private readonly Dictionary<string, Panel> _ioIndicators = new Dictionary<string, Panel>();
        private readonly Dictionary<string, bool> _ioStates = new Dictionary<string, bool>();

        // IO管理器引用
        private DMC1000BIOManager _ioManager;

        #endregion

        #region 构造函数

        public IOReadPanel()
        {
            InitializeComponent();

            // 初始化IO配置数据
            _basicInputPorts = IOConfiguration.BasicInputPorts;
            _extendedInputPorts = IOConfiguration.ExtendedInputPorts;

            // 获取IO管理器实例
            _ioManager = DMC1000BIOManager.Instance;

            // 初始化IO状态
            InitializeIOStates();

            // 初始化界面
            InitializeInterface();

            // 订阅IO状态变化事件
            SubscribeToIOEvents();

            // 异步初始化IO监控和状态同步
            _ = Task.Run(async () => await InitializeIOMonitoringAsync());
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        private void CleanupResources()
        {
            try
            {
                // 取消订阅事件
                UnsubscribeFromIOEvents();
            }
            catch (Exception ex)
            {
                LogHelper.Error("清理IOReadPanel资源失败", ex);
            }
        }

        #endregion

        #region 界面初始化

        /// <summary>
        /// 初始化IO状态字典
        /// </summary>
        private void InitializeIOStates()
        {
            try
            {
                // 初始化基础IO状态
                foreach (var port in _basicInputPorts)
                {
                    _ioStates[port.IONumber] = false;
                }

                // 初始化扩展IO状态
                foreach (var port in _extendedInputPorts)
                {
                    _ioStates[port.IONumber] = false;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("初始化IO状态失败", ex);
            }
        }

        /// <summary>
        /// 初始化界面 - 根据端口定义文件设计
        /// </summary>
        private void InitializeInterface()
        {
            try
            {
                // 设置面板属性 - 按HTML原型 660x840 (700-40padding)
                this.Size = new Size(660, 840);
                this.BackColor = ColorTranslator.FromHtml("#34495e");
                this.Dock = DockStyle.Fill;
                this.Padding = new Padding(0);

                // 创建主面板 - 按HTML原型padding: 20px
                CreateMainPanel();
                
                // 创建标题 - 按HTML原型样式
                CreateTitle();
                
                // 创建基础输入端口状态组
                CreateBasicInputGroup();

                // 创建扩展输入端口状态组
                CreateExtendedInputGroup();

                LogHelper.Info("IO读取状态面板初始化完成 - 支持基础IO和扩展IO");
            }
            catch (Exception ex)
            {
                LogHelper.Error("IO读取状态面板初始化失败", ex);
            }
        }
        
        /// <summary>
        /// 创建主面板 - 按HTML原型
        /// </summary>
        private void CreateMainPanel()
        {
            _mainPanel = new Panel
            {
                Size = new Size(660, 840),
                Location = new Point(0, 0),
                BackColor = ColorTranslator.FromHtml("#34495e"),
                Padding = new Padding(20), // 按HTML原型 padding: 20px
                Dock = DockStyle.Fill
            };
            
            this.Controls.Add(_mainPanel);
        }
        
        /// <summary>
        /// 创建标题
        /// </summary>
        private void CreateTitle()
        {
            _titleLabel = new Label
            {
                Text = "IO输入状态监控",
                Font = new Font("微软雅黑", 20F, FontStyle.Bold),
                ForeColor = ColorTranslator.FromHtml("#3498db"),
                Size = new Size(300, 30),
                Location = new Point(0, 0),
                AutoSize = true
            };

            _mainPanel.Controls.Add(_titleLabel);
        }
        
        /// <summary>
        /// 创建基础输入端口状态组
        /// </summary>
        private void CreateBasicInputGroup()
        {
            _basicInputGroup = new Panel
            {
                Size = new Size(600, 350),
                Location = new Point(0, 50),
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15)
            };

            // 设置边框颜色
            _basicInputGroup.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _basicInputGroup.Width - 1, _basicInputGroup.Height - 1);
                }
            };

            // 创建组标题
            var groupTitle = new Label
            {
                Text = "基础输入端口状态 (I0001-I0016)",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(400, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };

            // 创建基础IO网格
            CreateBasicIOGrid();

            _basicInputGroup.Controls.Add(groupTitle);
            _mainPanel.Controls.Add(_basicInputGroup);
        }

        /// <summary>
        /// 创建扩展输入端口状态组
        /// </summary>
        private void CreateExtendedInputGroup()
        {
            _extendedInputGroup = new Panel
            {
                Size = new Size(600, 350),
                Location = new Point(0, 420),
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15)
            };

            // 设置边框颜色
            _extendedInputGroup.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _extendedInputGroup.Width - 1, _extendedInputGroup.Height - 1);
                }
            };

            // 创建组标题
            var groupTitle = new Label
            {
                Text = "扩展输入端口状态 (I0101-I0116)",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(400, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };

            // 创建扩展IO网格
            CreateExtendedIOGrid();

            _extendedInputGroup.Controls.Add(groupTitle);
            _mainPanel.Controls.Add(_extendedInputGroup);
        }
        
        /// <summary>
        /// 创建基础IO网格
        /// </summary>
        private void CreateBasicIOGrid()
        {
            int itemWidth = (570 - 30) / 4; // 4列布局
            int itemHeight = 40;
            int gap = 10;

            for (int i = 0; i < _basicInputPorts.Count; i++)
            {
                int row = i / 4;
                int col = i % 4;

                int x = col * (itemWidth + gap);
                int y = 35 + row * (itemHeight + gap);

                CreateIOItem(_basicInputPorts[i], new Point(x, y), new Size(itemWidth, itemHeight), _basicInputGroup);
            }
        }

        /// <summary>
        /// 创建扩展IO网格
        /// </summary>
        private void CreateExtendedIOGrid()
        {
            int itemWidth = (570 - 30) / 4; // 4列布局
            int itemHeight = 40;
            int gap = 10;

            for (int i = 0; i < _extendedInputPorts.Count; i++)
            {
                int row = i / 4;
                int col = i % 4;

                int x = col * (itemWidth + gap);
                int y = 35 + row * (itemHeight + gap);

                CreateIOItem(_extendedInputPorts[i], new Point(x, y), new Size(itemWidth, itemHeight), _extendedInputGroup);
            }
        }
        
        /// <summary>
        /// 创建IO项目
        /// </summary>
        /// <param name="portDef">IO端口定义</param>
        /// <param name="location">位置</param>
        /// <param name="size">大小</param>
        /// <param name="parentPanel">父面板</param>
        private void CreateIOItem(IOPortDefinition portDef, Point location, Size size, Panel parentPanel)
        {
            var ioItem = new Panel
            {
                Size = size,
                Location = location,
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                BorderStyle = BorderStyle.None,
                Padding = new Padding(8)
            };

            // 状态指示器
            var statusIndicator = new Panel
            {
                Size = new Size(16, 16),
                Location = new Point(0, (size.Height - 16) / 2),
                BackColor = _ioStates[portDef.IONumber] ?
                    ColorTranslator.FromHtml("#27ae60") : // 绿色表示高电平
                    ColorTranslator.FromHtml("#95a5a6"),  // 灰色表示低电平
                BorderStyle = BorderStyle.None
            };

            // 设置圆形效果
            statusIndicator.Paint += (s, e) => {
                using (var brush = new SolidBrush(statusIndicator.BackColor))
                {
                    e.Graphics.FillEllipse(brush, 0, 0, 16, 16);
                }
            };

            // IO标签
            var ioLabel = new Label
            {
                Text = $"{portDef.IONumber} {portDef.Name}",
                Font = new Font("微软雅黑", 10F),
                ForeColor = Color.White,
                Size = new Size(size.Width - 24, 16),
                Location = new Point(24, (size.Height - 16) / 2),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // 保存控件引用以便后续更新
            _ioIndicators[portDef.IONumber] = statusIndicator;
            _ioLabels[portDef.IONumber] = ioLabel;

            ioItem.Controls.Add(statusIndicator);
            ioItem.Controls.Add(ioLabel);
            parentPanel.Controls.Add(ioItem);
        }
        
        #endregion

        #region 事件处理方法

        /// <summary>
        /// 订阅IO事件
        /// </summary>
        private void SubscribeToIOEvents()
        {
            try
            {
                if (_ioManager != null)
                {
                    _ioManager.IOInputStateChanged += OnIOInputStateChanged;
                    _ioManager.BatchIOStateChanged += OnBatchIOStateChanged;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("订阅IO事件失败", ex);
            }
        }

        /// <summary>
        /// 取消订阅IO事件
        /// </summary>
        private void UnsubscribeFromIOEvents()
        {
            try
            {
                if (_ioManager != null)
                {
                    _ioManager.IOInputStateChanged -= OnIOInputStateChanged;
                    _ioManager.BatchIOStateChanged -= OnBatchIOStateChanged;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("取消订阅IO事件失败", ex);
            }
        }

        /// <summary>
        /// IO输入状态变化事件处理
        /// </summary>
        private void OnIOInputStateChanged(object sender, IOInputStateChangedEventArgs e)
        {
            try
            {
                // 在UI线程中更新显示
                if (InvokeRequired)
                {
                    Invoke(new Action(() => UpdateIOStatus(e.IONumber, e.NewState)));
                }
                else
                {
                    UpdateIOStatus(e.IONumber, e.NewState);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"处理IO输入状态变化事件失败 - {e.IONumber}", ex);
            }
        }

        /// <summary>
        /// 批量IO状态变化事件处理
        /// </summary>
        private void OnBatchIOStateChanged(object sender, BatchIOStateChangedEventArgs e)
        {
            try
            {
                if (e.IOType == IOPortType.Input)
                {
                    // 在UI线程中批量更新显示
                    if (InvokeRequired)
                    {
                        Invoke(new Action(() => UpdateIOStates(e.ChangedStates)));
                    }
                    else
                    {
                        UpdateIOStates(e.ChangedStates);
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理批量IO状态变化事件失败", ex);
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 更新IO状态
        /// </summary>
        /// <param name="ioNumber">IO编号</param>
        /// <param name="status">状态</param>
        public void UpdateIOStatus(string ioNumber, bool status)
        {
            try
            {
                if (_ioStates.ContainsKey(ioNumber))
                {
                    _ioStates[ioNumber] = status;

                    // 更新UI显示
                    if (_ioIndicators.ContainsKey(ioNumber))
                    {
                        var indicator = _ioIndicators[ioNumber];
                        indicator.BackColor = status ?
                            ColorTranslator.FromHtml("#27ae60") : // 绿色表示高电平
                            ColorTranslator.FromHtml("#95a5a6");  // 灰色表示低电平
                        indicator.Invalidate(); // 重绘指示器
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"更新IO状态失败 - IO编号:{ioNumber}", ex);
            }
        }

        /// <summary>
        /// 批量更新IO状态
        /// </summary>
        /// <param name="ioStates">IO状态字典</param>
        public void UpdateIOStates(Dictionary<string, bool> ioStates)
        {
            try
            {
                foreach (var kvp in ioStates)
                {
                    UpdateIOStatus(kvp.Key, kvp.Value);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("批量更新IO状态失败", ex);
            }
        }

        /// <summary>
        /// 获取IO状态
        /// </summary>
        /// <param name="ioNumber">IO编号</param>
        /// <returns>IO状态</returns>
        public bool GetIOStatus(string ioNumber)
        {
            return _ioStates.ContainsKey(ioNumber) ? _ioStates[ioNumber] : false;
        }

        /// <summary>
        /// 获取所有IO状态
        /// </summary>
        /// <returns>IO状态字典</returns>
        public Dictionary<string, bool> GetAllIOStates()
        {
            return new Dictionary<string, bool>(_ioStates);
        }

        /// <summary>
        /// 从IO管理器刷新所有IO状态
        /// </summary>
        /// <returns>刷新结果</returns>
        public async Task<bool> RefreshIOStatesAsync()
        {
            try
            {
                if (_ioManager == null || !_ioManager.IsInitialized)
                {
                    LogHelper.Warning("IO管理器未初始化，无法刷新IO状态");
                    return false;
                }

                // 从IO管理器获取最新状态
                var inputStates = await _ioManager.ReadAllInputsAsync();

                // 更新UI显示
                UpdateIOStates(inputStates);

                LogHelper.Info("IO状态刷新完成");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("刷新IO状态失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 异步初始化IO监控和状态同步
        /// </summary>
        private async Task InitializeIOMonitoringAsync()
        {
            try
            {
                // 等待IO管理器初始化完成
                int retryCount = 0;
                while (!_ioManager.IsInitialized && retryCount < 50) // 最多等待5秒
                {
                    await Task.Delay(100);
                    retryCount++;
                }

                if (!_ioManager.IsInitialized)
                {
                    LogHelper.Error("IO管理器初始化超时，无法启动IO监控");
                    return;
                }

                // 启动IO状态监控
                bool monitoringResult = await StartMonitoringAsync();
                if (!monitoringResult)
                {
                    LogHelper.Warning("IO状态监控启动失败");
                }

                // 刷新初始IO状态
                await RefreshIOStatesAsync();

                LogHelper.Info("IOReadPanel初始化完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("IOReadPanel初始化失败", ex);
            }
        }

        /// <summary>
        /// 启动IO状态监控
        /// </summary>
        /// <returns>启动结果</returns>
        public async Task<bool> StartMonitoringAsync()
        {
            try
            {
                if (_ioManager == null)
                {
                    LogHelper.Error("IO管理器未初始化");
                    return false;
                }

                bool result = await _ioManager.StartMonitoringAsync();
                if (result)
                {
                    LogHelper.Info("IO状态监控启动成功");
                }
                else
                {
                    LogHelper.Error("IO状态监控启动失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                LogHelper.Error("启动IO状态监控失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 停止IO状态监控
        /// </summary>
        /// <returns>停止结果</returns>
        public async Task<bool> StopMonitoringAsync()
        {
            try
            {
                if (_ioManager == null)
                {
                    LogHelper.Warning("IO管理器未初始化");
                    return true;
                }

                bool result = await _ioManager.StopMonitoringAsync();
                if (result)
                {
                    LogHelper.Info("IO状态监控停止成功");
                }
                else
                {
                    LogHelper.Error("IO状态监控停止失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                LogHelper.Error("停止IO状态监控失败", ex);
                return false;
            }
        }

        #endregion
    }
}
