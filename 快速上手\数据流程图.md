# HR2项目数据流程图

## 📋 概述

本文档详细说明HR2项目中主要业务流程的数据流向和处理逻辑，帮助快速理解系统的工作原理。

## 🔄 主要业务流程

### 1. 系统启动流程

```mermaid
graph TD
    A[Program.Main] --> B[初始化日志系统]
    B --> C[加载Settings配置]
    C --> D[异步初始化Manager]
    D --> E[DMC1000BCardManager]
    D --> F[DMC1000BIOManager]
    D --> G[DMC1000BMotorManager]
    D --> H[EpsonRobotManager]
    D --> I[ScannerManager]
    D --> J[VisionManager]
    D --> K[StatisticsManager]
    E --> L[启动MainForm]
    F --> L
    G --> L
    H --> L
    I --> L
    J --> L
    K --> L
    L --> M[建立事件订阅]
    M --> N[系统就绪]
```

### 2. 自动化工作流程

```mermaid
graph TD
    A[用户点击启动] --> B{检查系统模式}
    B -->|手动模式| C[提示切换到自动模式]
    B -->|自动模式| D[WorkflowManager.StartWorkflowAsync]
    D --> E[初始化所有AutoMode控制器]
    E --> F[启动扫码器自动模式]
    F --> G[启动皮带电机控制器]
    G --> H[启动机器人控制器]
    H --> I[等待系统稳定]
    I --> J[工作流运行中]
    J --> K[监听完成事件]
    K --> L[AllScannersCompleted事件]
    L --> M[执行工作流完成序列]
    M --> N[重置状态为Idle]
```

### 3. 皮带电机自动控制流程

```mermaid
graph TD
    A[BeltMotorAutoModeController.StartAsync] --> B[初始化依赖管理器]
    B --> C[启动输入皮带控制线程]
    B --> D[启动输出皮带控制线程]
    
    C --> E[监控传感器I0004]
    E --> F{传感器状态}
    F -->|0检测到产品| G[停止输入皮带电机]
    F -->|1无产品| H[启动输入皮带电机]
    G --> I[延迟100ms]
    H --> I
    I --> E
    
    D --> J[监控传感器I0106]
    J --> K{传感器状态}
    K -->|0检测到产品| L[启动输出皮带电机]
    K -->|1无产品| M[停止输出皮带电机]
    L --> N[延迟100ms]
    M --> N
    N --> J
```

### 4. IO数据流程

```mermaid
graph TD
    A[DMC1000BIOManager] --> B[10Hz监控循环]
    B --> C[读取所有输入IO]
    C --> D[检测状态变化]
    D --> E{状态是否变化}
    E -->|是| F[触发IOInputStateChanged事件]
    E -->|否| G[继续监控]
    F --> H[UI控件更新显示]
    F --> I[业务逻辑处理]
    H --> G
    I --> G
    G --> J[等待100ms]
    J --> B
```

### 5. 机器人通信流程

```mermaid
graph TD
    A[EpsonRobotManager] --> B[建立TCP连接]
    B --> C[控制端口5000]
    B --> D[数据端口5001]
    C --> E[发送登录命令]
    E --> F[等待响应]
    F --> G{登录成功}
    G -->|是| H[发送启动命令]
    G -->|否| I[重试登录]
    H --> J[监控机器人状态]
    J --> K[接收状态数据]
    K --> L[解析状态信息]
    L --> M[触发状态变化事件]
    M --> N[UI状态更新]
```

## 📊 数据流向图

### 1. 传感器数据流

```
物理传感器 → DMC1000B控制卡 → DMC1000BIOManager → 事件通知 → 业务逻辑处理
                                      ↓
                                 UI控件更新 ← UIHelper ← 线程安全调用
```

### 2. 电机控制数据流

```
用户操作 → UI控件 → DMC1000BMotorManager → DMC1000B控制卡 → 物理电机
                           ↓
                    状态监控 → 事件通知 → UI状态更新
```

### 3. 生产数据流

```
扫码器 → ScannerManager → 产品ID → ProductionRecord → StatisticsManager
                                        ↓
                                   数据存储 → Excel/CSV导出
```

### 4. 配置数据流

```
配置文件 → Settings.Load() → 内存配置对象 → 各Manager使用
                                ↓
                           UI参数设置 → Settings.Save() → 配置文件
```

## 🔄 事件驱动数据流

### 1. IO事件流
```
DMC1000BIOManager.IOInputStateChanged
    ↓
├── UI/Controls/IOReadPanel → 显示更新
├── BeltMotorAutoModeController → 传感器逻辑
└── 其他订阅者 → 相应处理
```

### 2. 电机事件流
```
DMC1000BMotorManager.MotorStatusChanged
    ↓
├── UI/Controls/MotorControlPanel → 状态显示
├── BeltMotorAutoModeController → 状态监控
└── WorkflowManager → 流程协调
```

### 3. 工作流事件流
```
WorkflowManager.WorkflowStateChanged
    ↓
├── UI/MainForm → 全局状态显示
├── UI/Controls → 按钮状态更新
└── StatisticsManager → 统计记录
```

## 📈 数据处理时序

### 1. 实时数据处理
- **IO状态监控**: 100ms间隔，10Hz频率
- **电机状态监控**: 50ms间隔，20Hz频率
- **传感器检查**: 50ms间隔，20Hz频率
- **UI时间更新**: 1000ms间隔，1Hz频率

### 2. 批量数据处理
- **统计数据保存**: 每小时自动保存
- **日志文件轮转**: 每日创建新文件
- **配置文件保存**: 参数变更时立即保存
- **生产记录导出**: 用户手动触发

## 🔧 数据验证和错误处理

### 1. 输入数据验证
```
用户输入 → 参数验证 → 范围检查 → 业务逻辑验证 → 执行操作
              ↓              ↓              ↓
           格式错误      超出范围      逻辑冲突
              ↓              ↓              ↓
           错误提示      警告信息      操作拒绝
```

### 2. 通信数据验证
```
接收数据 → 格式检查 → 校验和验证 → 业务逻辑检查 → 数据处理
             ↓             ↓              ↓
          格式错误      校验失败       逻辑错误
             ↓             ↓              ↓
          丢弃数据      请求重发       错误处理
```

### 3. 硬件状态验证
```
硬件操作 → 状态检查 → 执行命令 → 结果验证 → 状态更新
             ↓             ↓             ↓
          设备未就绪    执行失败      结果异常
             ↓             ↓             ↓
          等待就绪      重试操作      错误恢复
```

## 📝 数据存储结构

### 1. 配置数据
- **文件格式**: JSON
- **存储位置**: Settings/
- **备份策略**: 修改前自动备份
- **加载时机**: 程序启动时

### 2. 生产数据
- **文件格式**: CSV/Excel
- **存储位置**: Data/
- **分类方式**: 按日期和班次
- **清理策略**: 保留90天数据

### 3. 日志数据
- **文件格式**: 文本文件
- **存储位置**: Logs/
- **命名规则**: Log_YYYYMMDD.txt
- **轮转策略**: 每日创建新文件

### 4. 统计数据
- **文件格式**: JSON/Excel
- **存储位置**: Data/Statistics/
- **更新频率**: 实时更新，定时保存
- **历史保留**: 永久保存

这些数据流程确保了系统的高效运行和数据的完整性，为生产过程提供了可靠的数据支撑。
