using System;
using MyHMI.Managers;

namespace MyHMI.Events
{
    /// <summary>
    /// 系统模式变化事件参数
    /// </summary>
    public class SystemModeChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 之前的模式
        /// </summary>
        public SystemMode PreviousMode { get; }

        /// <summary>
        /// 当前的模式
        /// </summary>
        public SystemMode CurrentMode { get; }

        /// <summary>
        /// 模式变化时间
        /// </summary>
        public DateTime ChangeTime { get; }

        public SystemModeChangedEventArgs(SystemMode previousMode, SystemMode currentMode)
        {
            PreviousMode = previousMode;
            CurrentMode = currentMode;
            ChangeTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 自动化流程开始事件参数
    /// </summary>
    public class AutomationStartedEventArgs : EventArgs
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; }

        public AutomationStartedEventArgs()
        {
            StartTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 自动化流程完成事件参数
    /// </summary>
    public class AutomationCompletedEventArgs : EventArgs
    {
        /// <summary>
        /// 是否成功完成
        /// </summary>
        public bool IsSuccess { get; }

        /// <summary>
        /// 完成消息
        /// </summary>
        public string Message { get; }

        /// <summary>
        /// 完成时间
        /// </summary>
        public DateTime CompletionTime { get; }

        public AutomationCompletedEventArgs(bool isSuccess, string message)
        {
            IsSuccess = isSuccess;
            Message = message;
            CompletionTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 自动化流程错误事件参数
    /// </summary>
    public class AutomationErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 错误异常
        /// </summary>
        public Exception Exception { get; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; }

        /// <summary>
        /// 错误时间
        /// </summary>
        public DateTime ErrorTime { get; }

        public AutomationErrorEventArgs(Exception exception)
        {
            Exception = exception;
            ErrorMessage = exception?.Message ?? "未知错误";
            ErrorTime = DateTime.Now;
        }

        public AutomationErrorEventArgs(Exception exception, string detailedMessage)
        {
            Exception = exception;
            ErrorMessage = detailedMessage ?? exception?.Message ?? "未知错误";
            ErrorTime = DateTime.Now;
        }
    }
}
