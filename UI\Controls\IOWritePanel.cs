using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using MyHMI.Helpers;
using MyHMI.Managers;
using MyHMI.Models;

namespace MyHMI.UI.Controls
{
    /// <summary>
    /// IO输出控制面板 - 根据端口定义文件设计
    /// 支持基础IO（O0001-O0012）和扩展IO（O0101-O0115）
    /// </summary>
    public partial class IOWritePanel : UserControl
    {
        #region 私有字段

        private Panel _mainPanel;
        private Label _titleLabel;
        private Panel _basicOutputGroup;
        private Panel _extendedOutputGroup;

        // IO配置数据 - 根据端口定义文件
        private readonly List<IOPortDefinition> _basicOutputPorts;
        private readonly List<IOPortDefinition> _extendedOutputPorts;

        // IO控制按钮和状态显示控件
        private readonly Dictionary<string, Button> _ioButtons = new Dictionary<string, Button>();
        private readonly Dictionary<string, Panel> _ioIndicators = new Dictionary<string, Panel>();
        private readonly Dictionary<string, Label> _ioLabels = new Dictionary<string, Label>();
        private readonly Dictionary<string, bool> _ioStates = new Dictionary<string, bool>();

        // IO管理器引用
        private DMC1000BIOManager _ioManager;

        #endregion

        #region 事件定义

        /// <summary>
        /// IO输出状态变化事件
        /// </summary>
        public event EventHandler<IOOutputChangedEventArgs> IOOutputChanged;

        #endregion

        #region 构造函数

        public IOWritePanel()
        {
            InitializeComponent();

            // 初始化IO配置数据
            _basicOutputPorts = IOConfiguration.BasicOutputPorts;
            _extendedOutputPorts = IOConfiguration.ExtendedOutputPorts;

            // 获取IO管理器实例
            _ioManager = DMC1000BIOManager.Instance;

            // 初始化IO状态
            InitializeIOStates();

            // 初始化界面
            InitializeInterface();

            // 订阅IO状态变化事件
            SubscribeToIOEvents();

            // 异步初始化IO监控和状态同步
            _ = Task.Run(async () => await InitializeIOMonitoringAsync());
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        private void CleanupResources()
        {
            try
            {
                // 取消订阅事件
                UnsubscribeFromIOEvents();
            }
            catch (Exception ex)
            {
                LogHelper.Error("清理IOWritePanel资源失败", ex);
            }
        }

        #endregion

        #region 界面初始化

        /// <summary>
        /// 初始化IO状态字典
        /// </summary>
        private void InitializeIOStates()
        {
            try
            {
                // 初始化基础IO状态
                foreach (var port in _basicOutputPorts)
                {
                    _ioStates[port.IONumber] = false;
                }

                // 初始化扩展IO状态
                foreach (var port in _extendedOutputPorts)
                {
                    _ioStates[port.IONumber] = false;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("初始化IO状态失败", ex);
            }
        }

        /// <summary>
        /// 初始化界面 - 根据端口定义文件设计
        /// </summary>
        private void InitializeInterface()
        {
            try
            {
                // 设置面板属性
                this.Size = new Size(660, 900); // 增加总高度以适应新布局
                this.BackColor = ColorTranslator.FromHtml("#34495e");
                this.Dock = DockStyle.Fill;
                this.Padding = new Padding(0);

                // 创建主面板
                CreateMainPanel();

                // 创建标题
                CreateTitle();

                // 创建基础输出端口控制组
                CreateBasicOutputGroup();

                // 创建扩展输出端口控制组
                CreateExtendedOutputGroup();

                LogHelper.Info("IO输出控制面板初始化完成 - 支持基础IO和扩展IO");
            }
            catch (Exception ex)
            {
                LogHelper.Error("IO输出控制面板初始化失败", ex);
            }
        }
        
        /// <summary>
        /// 创建主面板 - 按HTML原型
        /// </summary>
        private void CreateMainPanel()
        {
            _mainPanel = new Panel
            {
                Size = new Size(660, 900), // 增加主面板高度
                Location = new Point(0, 0),
                BackColor = ColorTranslator.FromHtml("#34495e"),
                Padding = new Padding(20), // 按HTML原型 padding: 20px
                Dock = DockStyle.Fill
            };
            
            this.Controls.Add(_mainPanel);
        }
        
        /// <summary>
        /// 创建标题
        /// </summary>
        private void CreateTitle()
        {
            _titleLabel = new Label
            {
                Text = "IO输出控制",
                Font = new Font("微软雅黑", 20F, FontStyle.Bold),
                ForeColor = ColorTranslator.FromHtml("#3498db"),
                Size = new Size(300, 30),
                Location = new Point(0, 0),
                AutoSize = true
            };

            _mainPanel.Controls.Add(_titleLabel);
        }
        
        /// <summary>
        /// 创建基础输出端口控制组
        /// </summary>
        private void CreateBasicOutputGroup()
        {
            _basicOutputGroup = new Panel
            {
                Size = new Size(600, 380), // 增加高度以适应3列布局
                Location = new Point(0, 50),
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15)
            };

            // 设置边框颜色
            _basicOutputGroup.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _basicOutputGroup.Width - 1, _basicOutputGroup.Height - 1);
                }
            };

            // 创建组标题
            var groupTitle = new Label
            {
                Text = "基础输出端口控制 (O0001-O0012)",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(400, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };

            // 创建基础IO网格
            CreateBasicIOGrid();

            _basicOutputGroup.Controls.Add(groupTitle);
            _mainPanel.Controls.Add(_basicOutputGroup);
        }

        /// <summary>
        /// 创建扩展输出端口控制组
        /// </summary>
        private void CreateExtendedOutputGroup()
        {
            _extendedOutputGroup = new Panel
            {
                Size = new Size(600, 420), // 增加高度以适应3列布局和15个输出
                Location = new Point(0, 450), // 调整位置以适应上面面板的高度变化
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15)
            };

            // 设置边框颜色
            _extendedOutputGroup.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _extendedOutputGroup.Width - 1, _extendedOutputGroup.Height - 1);
                }
            };

            // 创建组标题
            var groupTitle = new Label
            {
                Text = "扩展输出端口控制 (O0101-O0115)",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(400, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };

            // 创建扩展IO网格
            CreateExtendedIOGrid();

            _extendedOutputGroup.Controls.Add(groupTitle);
            _mainPanel.Controls.Add(_extendedOutputGroup);
        }
        
        /// <summary>
        /// 创建基础IO网格
        /// </summary>
        private void CreateBasicIOGrid()
        {
            int itemWidth = (570 - 30) / 3; // 改为3列布局以适应更长的文本
            int itemHeight = 60; // 增加高度以显示完整文本
            int gap = 10;

            for (int i = 0; i < _basicOutputPorts.Count; i++)
            {
                int row = i / 3; // 3列布局
                int col = i % 3;

                int x = col * (itemWidth + gap);
                int y = 35 + row * (itemHeight + gap);

                CreateIOItem(_basicOutputPorts[i], new Point(x, y), new Size(itemWidth, itemHeight), _basicOutputGroup);
            }
        }

        /// <summary>
        /// 创建扩展IO网格
        /// </summary>
        private void CreateExtendedIOGrid()
        {
            int itemWidth = (570 - 30) / 3; // 改为3列布局以适应更长的文本
            int itemHeight = 60; // 增加高度以显示完整文本
            int gap = 10;

            for (int i = 0; i < _extendedOutputPorts.Count; i++)
            {
                int row = i / 3; // 3列布局
                int col = i % 3;

                int x = col * (itemWidth + gap);
                int y = 35 + row * (itemHeight + gap);

                CreateIOItem(_extendedOutputPorts[i], new Point(x, y), new Size(itemWidth, itemHeight), _extendedOutputGroup);
            }
        }
        
        /// <summary>
        /// 创建IO项目
        /// </summary>
        /// <param name="portDef">IO端口定义</param>
        /// <param name="location">位置</param>
        /// <param name="size">大小</param>
        /// <param name="parentPanel">父面板</param>
        private void CreateIOItem(IOPortDefinition portDef, Point location, Size size, Panel parentPanel)
        {
            var ioItem = new Panel
            {
                Size = size,
                Location = location,
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                BorderStyle = BorderStyle.None,
                Padding = new Padding(4)
            };

            // 状态指示器
            var statusIndicator = new Panel
            {
                Size = new Size(12, 12),
                Location = new Point(4, 4),
                BackColor = _ioStates[portDef.IONumber] ?
                    ColorTranslator.FromHtml("#27ae60") : // 绿色表示高电平
                    ColorTranslator.FromHtml("#95a5a6"),  // 灰色表示低电平
                BorderStyle = BorderStyle.None
            };

            // 设置圆形效果
            statusIndicator.Paint += (s, e) => {
                using (var brush = new SolidBrush(statusIndicator.BackColor))
                {
                    e.Graphics.FillEllipse(brush, 0, 0, 12, 12);
                }
            };

            // 控制按钮
            var controlButton = new Button
            {
                Text = portDef.Name, // 只显示IO名称，不显示编号
                Font = new Font("微软雅黑", 10F), // 增大字体以提高可读性
                Size = new Size(size.Width - 8, size.Height - 20),
                Location = new Point(4, 16),
                BackColor = _ioStates[portDef.IONumber] ?
                    ColorTranslator.FromHtml("#27ae60") : // 绿色表示ON
                    ColorTranslator.FromHtml("#3498db"),  // 蓝色表示OFF
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false,
                Cursor = Cursors.Hand,
                Tag = portDef.IONumber,
                TextAlign = ContentAlignment.MiddleCenter // 文本居中对齐
            };

            // 设置按钮边框
            controlButton.FlatAppearance.BorderSize = 1;
            controlButton.FlatAppearance.BorderColor = ColorTranslator.FromHtml("#4a5661");

            // 添加点击事件
            controlButton.Click += ControlButton_Click;

            // 保存控件引用以便后续更新
            _ioButtons[portDef.IONumber] = controlButton;
            _ioIndicators[portDef.IONumber] = statusIndicator;

            ioItem.Controls.Add(statusIndicator);
            ioItem.Controls.Add(controlButton);
            parentPanel.Controls.Add(ioItem);
        }
        
        #endregion

        #region 事件订阅和处理

        /// <summary>
        /// 订阅IO事件
        /// </summary>
        private void SubscribeToIOEvents()
        {
            try
            {
                if (_ioManager != null)
                {
                    _ioManager.IOOutputStateChanged += OnIOOutputStateChanged;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("订阅IO事件失败", ex);
            }
        }

        /// <summary>
        /// 取消订阅IO事件
        /// </summary>
        private void UnsubscribeFromIOEvents()
        {
            try
            {
                if (_ioManager != null)
                {
                    _ioManager.IOOutputStateChanged -= OnIOOutputStateChanged;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("取消订阅IO事件失败", ex);
            }
        }

        /// <summary>
        /// IO输出状态变化事件处理
        /// </summary>
        private void OnIOOutputStateChanged(object sender, IOOutputStateChangedEventArgs e)
        {
            try
            {
                // 在UI线程中更新显示
                if (InvokeRequired)
                {
                    Invoke(new Action(() => SetIOOutputStatus(e.IONumber, e.NewState)));
                }
                else
                {
                    SetIOOutputStatus(e.IONumber, e.NewState);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"处理IO输出状态变化事件失败 - {e.IONumber}", ex);
            }
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 控制按钮点击事件
        /// </summary>
        private async void ControlButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is string ioNumber)
                {
                    // 切换IO状态
                    bool currentState = _ioStates[ioNumber];
                    bool newState = !currentState;

                    // 调用IO管理器设置输出状态
                    if (_ioManager != null && _ioManager.IsInitialized)
                    {
                        bool success = await _ioManager.SetOutputAsync(ioNumber, newState);
                        if (success)
                        {
                            // 更新UI状态（IO管理器会触发事件自动更新）
                            LogHelper.Info($"IO输出控制成功 - {ioNumber}: {(newState ? "ON" : "OFF")}");
                        }
                        else
                        {
                            LogHelper.Error($"IO输出控制失败 - {ioNumber}");
                        }
                    }
                    else
                    {
                        // 如果IO管理器未初始化，仅更新UI显示
                        SetIOOutputStatus(ioNumber, newState);
                        LogHelper.Warning($"IO管理器未初始化，仅更新UI显示 - {ioNumber}: {(newState ? "ON" : "OFF")}");
                    }

                    // 触发事件
                    IOOutputChanged?.Invoke(this, new IOOutputChangedEventArgs(ioNumber, newState));
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("IO输出控制失败", ex);
            }
        }

        #endregion
        
        #region 公共方法

        /// <summary>
        /// 设置IO输出状态
        /// </summary>
        /// <param name="ioNumber">IO编号</param>
        /// <param name="isActive">是否激活</param>
        public void SetIOOutputStatus(string ioNumber, bool isActive)
        {
            try
            {
                if (_ioStates.ContainsKey(ioNumber))
                {
                    _ioStates[ioNumber] = isActive;

                    // 更新按钮显示
                    if (_ioButtons.ContainsKey(ioNumber))
                    {
                        var button = _ioButtons[ioNumber];
                        button.BackColor = isActive ?
                            ColorTranslator.FromHtml("#27ae60") : // 绿色表示ON
                            ColorTranslator.FromHtml("#3498db");  // 蓝色表示OFF
                    }

                    // 更新状态指示器
                    if (_ioIndicators.ContainsKey(ioNumber))
                    {
                        var indicator = _ioIndicators[ioNumber];
                        indicator.BackColor = isActive ?
                            ColorTranslator.FromHtml("#27ae60") : // 绿色表示高电平
                            ColorTranslator.FromHtml("#95a5a6");  // 灰色表示低电平
                        indicator.Invalidate(); // 重绘指示器
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"设置IO输出状态失败 - IO编号:{ioNumber}", ex);
            }
        }

        /// <summary>
        /// 批量设置IO输出状态
        /// </summary>
        /// <param name="ioStates">IO状态字典</param>
        public void SetIOOutputStates(Dictionary<string, bool> ioStates)
        {
            try
            {
                foreach (var kvp in ioStates)
                {
                    SetIOOutputStatus(kvp.Key, kvp.Value);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("批量设置IO输出状态失败", ex);
            }
        }

        /// <summary>
        /// 获取IO输出状态
        /// </summary>
        /// <param name="ioNumber">IO编号</param>
        /// <returns>IO状态</returns>
        public bool GetIOOutputStatus(string ioNumber)
        {
            return _ioStates.ContainsKey(ioNumber) ? _ioStates[ioNumber] : false;
        }

        /// <summary>
        /// 获取所有IO输出状态
        /// </summary>
        /// <returns>IO状态字典</returns>
        public Dictionary<string, bool> GetAllIOOutputStates()
        {
            return new Dictionary<string, bool>(_ioStates);
        }

        /// <summary>
        /// 异步初始化IO监控和状态同步
        /// </summary>
        private async Task InitializeIOMonitoringAsync()
        {
            try
            {
                // 等待IO管理器初始化完成
                int retryCount = 0;
                while (!_ioManager.IsInitialized && retryCount < 50) // 最多等待5秒
                {
                    await Task.Delay(100);
                    retryCount++;
                }

                if (!_ioManager.IsInitialized)
                {
                    LogHelper.Error("IO管理器初始化超时，无法启动IO监控");
                    return;
                }

                // 刷新初始IO输出状态
                await RefreshIOOutputStatesAsync();

                LogHelper.Info("IOWritePanel初始化完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("IOWritePanel初始化失败", ex);
            }
        }

        /// <summary>
        /// 从IO管理器刷新所有IO输出状态
        /// </summary>
        /// <returns>刷新结果</returns>
        public async Task<bool> RefreshIOOutputStatesAsync()
        {
            try
            {
                if (_ioManager == null || !_ioManager.IsInitialized)
                {
                    LogHelper.Warning("IO管理器未初始化，无法刷新IO输出状态");
                    return false;
                }

                // 从IO管理器获取最新状态
                var outputStates = await _ioManager.ReadAllOutputsAsync();

                // 更新UI显示
                SetIOOutputStates(outputStates);

                LogHelper.Info("IO输出状态刷新完成");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("刷新IO输出状态失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 通过IO管理器设置输出状态
        /// </summary>
        /// <param name="ioNumber">IO编号</param>
        /// <param name="isActive">是否激活</param>
        /// <returns>操作结果</returns>
        public async Task<bool> SetIOOutputAsync(string ioNumber, bool isActive)
        {
            try
            {
                if (_ioManager == null || !_ioManager.IsInitialized)
                {
                    LogHelper.Warning("IO管理器未初始化，仅更新UI显示");
                    SetIOOutputStatus(ioNumber, isActive);
                    return false;
                }

                bool result = await _ioManager.SetOutputAsync(ioNumber, isActive);
                if (result)
                {
                    LogHelper.Info($"设置IO输出成功 - {ioNumber}: {(isActive ? "ON" : "OFF")}");
                }
                else
                {
                    LogHelper.Error($"设置IO输出失败 - {ioNumber}");
                }

                return result;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"设置IO输出异常 - {ioNumber}", ex);
                return false;
            }
        }

        /// <summary>
        /// 紧急停止所有输出
        /// </summary>
        /// <returns>操作结果</returns>
        public async Task<bool> EmergencyStopAllOutputsAsync()
        {
            try
            {
                if (_ioManager == null || !_ioManager.IsInitialized)
                {
                    LogHelper.Warning("IO管理器未初始化，仅更新UI显示");

                    // 仅更新UI显示为全部关闭
                    var allOffStates = new Dictionary<string, bool>();
                    foreach (var port in _basicOutputPorts.Concat(_extendedOutputPorts))
                    {
                        allOffStates[port.IONumber] = false;
                    }
                    SetIOOutputStates(allOffStates);
                    return false;
                }

                bool result = await _ioManager.EmergencyStopAllOutputsAsync();
                if (result)
                {
                    LogHelper.Info("紧急停止所有输出成功");
                }
                else
                {
                    LogHelper.Error("紧急停止所有输出失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                LogHelper.Error("紧急停止所有输出异常", ex);
                return false;
            }
        }

        #endregion
    }
}
