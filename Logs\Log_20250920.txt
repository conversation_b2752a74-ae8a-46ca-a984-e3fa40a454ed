[2025-09-20 15:21:52.364] [INFO] 程序启动开始
[2025-09-20 15:21:52.366] [INFO] 配置系统初始化成功
[2025-09-20 15:21:52.409] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-20 15:21:52.409] [INFO] 配置系统初始化完成
[2025-09-20 15:21:52.409] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-20 15:21:52.419] [INFO] 开始初始化各个Manager...
[2025-09-20 15:21:52.419] [INFO] 初始化基础Manager...
[2025-09-20 15:21:52.431] [INFO] IO状态缓存初始化完成
[2025-09-20 15:21:52.438] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-20 15:21:52.439] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-20 15:21:52.441] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-20 15:21:52.441] [ERROR] DMC1000B控制卡初始化失败
[2025-09-20 15:21:52.442] [WARN] DMC1000BIO管理器初始化失败
[2025-09-20 15:21:52.447] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-20 15:21:52.447] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-20 15:21:52.448] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-20 15:21:52.448] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-20 15:21:52.449] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__42_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 130
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 15:21:52.476] [WARN] DMC1000B电机管理器初始化失败
[2025-09-20 15:21:52.476] [INFO] 初始化系统模式管理器...
[2025-09-20 15:21:52.481] [INFO] 开始初始化MotorManager...
[2025-09-20 15:21:52.483] [INFO] 模拟初始化运动控制卡
[2025-09-20 15:21:52.701] [INFO] 加载了8个电机的默认配置
[2025-09-20 15:21:52.702] [INFO] 电机监控任务已启动
[2025-09-20 15:21:52.702] [INFO] MotorManager初始化完成
[2025-09-20 15:21:52.702] [INFO] 初始化通信Manager...
[2025-09-20 15:21:52.703] [INFO] 电机监控循环开始
[2025-09-20 15:21:52.704] [INFO] 开始初始化ScannerManager...
[2025-09-20 15:21:52.706] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-20 15:21:52.708] [INFO] 串口初始化完成
[2025-09-20 15:21:52.710] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-20 15:21:52.713] [INFO] 扫描枪连接成功: COM1
[2025-09-20 15:21:52.713] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-20 15:21:52.713] [INFO] ScannerManager初始化完成
[2025-09-20 15:21:52.716] [INFO] 开始初始化ModbusTcpManager...
[2025-09-20 15:21:52.718] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-20 15:21:52.724] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-20 15:21:57.748] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 15:21:57.748] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-20 15:21:57.749] [INFO] ModbusTcpManager初始化完成
[2025-09-20 15:21:57.752] [INFO] 开始初始化EpsonRobotManager...
[2025-09-20 15:21:57.753] [INFO] Epson机器人配置 - 启动/停止: 192.168.1.100:5000, 数据收发: 192.168.1.101:5001
[2025-09-20 15:21:57.753] [INFO] EpsonRobotManager初始化完成
[2025-09-20 15:21:57.753] [INFO] 初始化视觉Manager...
[2025-09-20 15:21:57.755] [INFO] 开始初始化VisionManager...
[2025-09-20 15:21:57.756] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-20 15:21:57.756] [INFO] 模拟初始化相机，索引: 0
[2025-09-20 15:21:58.268] [INFO] 相机初始化成功
[2025-09-20 15:21:58.269] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-20 15:21:58.269] [INFO] 视觉配置加载完成
[2025-09-20 15:21:58.269] [INFO] VisionManager初始化完成
[2025-09-20 15:21:58.270] [INFO] 初始化数据Manager...
[2025-09-20 15:21:58.272] [INFO] 开始初始化StatisticsManager...
[2025-09-20 15:21:58.273] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-20 15:21:58.276] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-20 15:21:58.276] [INFO] 历史数据加载完成
[2025-09-20 15:21:58.276] [INFO] 自动保存任务已启动
[2025-09-20 15:21:58.277] [INFO] StatisticsManager初始化完成
[2025-09-20 15:21:58.277] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-20 15:21:58.277] [INFO] 所有Manager初始化完成
[2025-09-20 15:21:58.278] [INFO] 自动保存循环开始
[2025-09-20 15:21:58.319] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-20 15:21:58.319] [INFO] 主界面布局创建完成
[2025-09-20 15:21:58.321] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-20 15:22:20.837] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-20 15:22:20.837] [INFO] Epson机器人管理器初始化完成
[2025-09-20 15:22:20.846] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-20 15:22:23.078] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-20 15:22:23.081] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP...
[2025-09-20 15:22:23.084] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP...
[2025-09-20 15:22:24.335] [ERROR] 未登录，无法停止机器人
[2025-09-20 15:22:24.340] [INFO] 机器人已停止
[2025-09-20 15:22:28.092] [ERROR] 连接启动/停止TCP 执行失败
异常详情: 启动/停止TCP连接超时: 192.168.1.100:5000
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<<ConnectStartStopAsync>b__71_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 241
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
