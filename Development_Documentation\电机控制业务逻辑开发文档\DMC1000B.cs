 public partial class DMC1000
    {

        /// <summary>
        /// 初始化控制卡，为控制卡分配系统资源，并初始化控制卡
        /// </summary>
        /// <returns>卡数：0~12，其中0表示没有卡</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_board_init", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_board_init();
        /// <summary>
        /// 关闭控制卡，释放系统资源
        /// </summary>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_board_close", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_board_close();

        /// <summary>
        /// 设置控制卡脉冲输出模式，用户可以根据驱动器具体接收脉冲的模式来选择控制卡不同脉冲输出模式
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="pls_outmode">脉冲输出模式：0-pulse/dir模式脉冲上升沿有效；1-pulse/dir模式脉冲下降沿有效；2-CW/CCW模式脉冲上升沿有效；3-CW/CCW模式脉冲下降沿有效</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_set_pls_outmode", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_set_pls_outmode(short axis, short pls_outmode);

        /// <summary>
        /// 读取指定轴当前脉冲输出速度
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <returns>指定轴当前的运动速度，单位pps</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_get_speed", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern int d1000_get_speed(short  axis);
        /// <summary>
        /// 改变指定轴当前脉冲输出速度
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="NewVel">新设置的速度，单位：pps，取值范围：1~409550</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        /// <remarks>变速后的速度与实际设定速度之间有一个允许的误差，误差值为0~50 pps，具体的误差值与当前速度及速度改变量ΔV有关，其绝对值|ΔV|越小，则误差越小</remarks>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_change_speed", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_change_speed(short axis, int NewVel);
        /// <summary>
        /// 减速停止指定轴脉冲输出
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_decel_stop", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_decel_stop(short  axis);
        /// <summary>
        /// 急停指定轴脉冲输出
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_immediate_stop", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_immediate_stop(short  axis);

        /// <summary>
        /// 以梯形速度曲线控制指定轴至运行速度，并以相对坐标运行一段指定距离
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="Dist">相对运动距离，单位：pulse，其值的正负表示运动方向</param>
        /// <param name="StrVel">初始速度，单位：pps</param>
        /// <param name="MaxVel">运行速度，单位：pps</param>
        /// <param name="Tacc">加速时间，单位：s</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_start_t_move", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_start_t_move(short axis, int Dist, int StrVel, int MaxVel, double Tacc);
        /// <summary>
        /// 以梯形速度曲线控制指定轴至运行速度，并以绝对坐标运行一段指定距离
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="Pos">绝对运动位置，单位：pulse</param>
        /// <param name="StrVel">初始速度，单位：pps</param>
        /// <param name="MaxVel">运行速度，单位：pps</param>
        /// <param name="Tacc">加速时间，单位：s</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_start_ta_move", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_start_ta_move(short axis, int Pos, double StrVel, double MaxVel, double Tacc);

        /// <summary>
        /// 以S形速度曲线控制指定轴至运行速度，并以相对坐标运行一段指定距离
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="Dist">相对运动距离，单位：pulse，其值的正负表示运动方向</param>
        /// <param name="StrVel">初始速度，单位：pps</param>
        /// <param name="MaxVel">运行速度，单位：pps</param>
        /// <param name="Tacc">加速时间，单位：s</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_start_s_move", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_start_s_move(short axis, int Dist, int StrVel, int MaxVel, double Tacc);
        /// <summary>
        /// 以S形速度曲线控制指定轴至运行速度，并以绝对坐标运行一段指定距离
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="Pos">绝对运动位置，单位：pulse</param>
        /// <param name="StrVel">初始速度，单位：pps</param>
        /// <param name="MaxVel">运行速度，单位：pps</param>
        /// <param name="Tacc">加速时间，单位：s</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_start_sa_move", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_start_sa_move(short axis, int Pos, int StrVel, int MaxVel, double Tacc);

        /// <summary>
        /// 以梯形速度曲线控制指定轴至运行速度，并以运行速度连续运行
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="StrVel">初始速度，单位：pps</param>
        /// <param name="MaxVel">运行速度，单位：pps，其值的正负表示运动方向</param>
        /// <param name="Tacc">加速时间，单位：s</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_start_tv_move", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_start_tv_move(short axis, int StrVel, int MaxVel, double Tacc);
        /// <summary>
        /// 以S形速度曲线控制指定轴至运行速度，并以运行速度连续运行
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="StrVel">初始速度，单位：pps</param>
        /// <param name="MaxVel">运行速度，单位：pps，其值的正负表示运动方向</param>
        /// <param name="Tacc">加速时间，单位：s</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_start_sv_move", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_start_sv_move(short axis, int StrVel, int MaxVel, double Tacc);

        /// <summary>
        /// 设置S形运动曲线参数
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="s_para">S形曲线参数</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_set_s_profile", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_set_s_profile(short axis, double s_para);
        /// <summary>
        /// 读取S形运动曲线参数
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="s_para">返回S形曲线参数</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_get_s_profile", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_get_s_profile(short axis, ref double s_para);

        /// <summary>
        /// 启动多轴相对坐标的直线插补运动
        /// </summary>
        /// <param name="TotalAxis">插补轴数，范围2～4</param>
        /// <param name="AxisArray">轴号列表</param>
        /// <param name="DistArray">对应轴号列表各轴的相对坐标的距离列表</param>
        /// <param name="StrVel">初始速度，单位：pps</param>
        /// <param name="MaxVel">运行速度，单位：pps</param>
        /// <param name="Tacc">加速时间，单位：s</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_start_t_line", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_start_t_line(short TotalAxis, ushort[] AxisArray, ushort[] DistArray, int StrVel, int MaxVel, double Tacc);
        /// <summary>
        /// 启动多轴绝对坐标的直线插补运动
        /// </summary>
        /// <param name="TotalAxis">插补轴数，范围2～4</param>
        /// <param name="AxisArray">轴号列表</param>
        /// <param name="DistArray">对应轴号列表各轴的绝对坐标的位置列表</param>
        /// <param name="StrVel">初始速度，单位：pps</param>
        /// <param name="MaxVel">运行速度，单位：pps</param>
        /// <param name="Tacc">加速时间，单位：s</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_start_ta_line", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_start_ta_line(short TotalAxis, ushort[] AxisArray, int[] DistArray, int StrVel, int MaxVel, double Tacc);

        /// <summary>
        /// 启动指定轴进行回原点运动
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="StrVel">回原点运动初始速度，单位：pps</param>
        /// <param name="MaxVel">回原点运动速度，负值表示往负方向找原点，正值表示往正方向找原点，单位：pps</param>
        /// <param name="Tacc">加速时间，单位：s</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_home_move", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_home_move(short axis, int StrVel, int MaxVel, double Tacc);

        /// <summary>
        /// 检测指定轴的运动状态
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <returns>0：正在运行；1：脉冲输出完毕停止；2：指令停止（如调用了d1000_decel_stop函数）；3：遇限位停止；4：遇原点停止</returns>
        /// <remarks>
        /// 注意：
        /// 1. 当指定轴遇限位信号停止时，d1000_check_done函数返回3，若限位信号撤去，d1000_check_done函数返回值变为1。
        /// 2. 当指定轴遇原点信号停止时，d1000_check_done函数返回4，若原点信号撤去，d1000_check_done函数返回值变为1。
        /// </remarks>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_check_done", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_check_done(short  axis);

        /// <summary>
        /// 读取指令位置计数器计数值
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <returns>指定轴当前指令位置计数器值，单位：pulse</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_get_command_pos", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern int d1000_get_command_pos(short  axis);
        /// <summary>
        /// 设置指令位置计数器计数值
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="Pos">设置指令位置计数器值，单位：pulse</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_set_command_pos", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_set_command_pos(short axis, double Pos);

        /// <summary>
        /// 输出通用输出信号
        /// </summary>
        /// <param name="BitNo">表示要输出的通用输出口的位号，DMC1000B多卡时：卡n的范围为(n-1)*32+(1~27)</param>
        /// <param name="BitData">输出信号：0-表示低电平；1-表示高电平</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_out_bit", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_out_bit(short BitNo, short BitData);
        /// <summary>
        /// 读取通用输入信号状态
        /// </summary>
        /// <param name="BitNo">表示要读取的通用输入口的位号，DMC1000B多卡时：卡n的范围为(n-1)*32+(1~32)</param>
        /// <returns>输入口状态：0-表示低电平；1-表示高电平</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_in_bit", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_in_bit(short BitNo);
        /// <summary>
        /// 读取通用输出信号状态
        /// </summary>
        /// <param name="BitNo">通用输出口位号，DMC1000B多卡时：卡n的范围为(n-1)*32+(1~27)</param>
        /// <returns>输出口状态：0-表示低电平；1-表示高电平</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_get_outbit", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_get_outbit(short BitNo);
        /// <summary>
        /// 设置输入使能控制
        /// </summary>
        /// <param name="CardNo">卡号</param>
        /// <param name="InputEn">输入使能设置</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_in_enable", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_in_enable(int CardNo, ushort InputEn);

        /// <summary>
        /// 设置减速信号是否使能
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <param name="SdMode">减速使能模式：0-SD信号无效；1-SD信号有效</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_set_sd", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_set_sd(short axis, short SdMode);
        /// <summary>
        /// 读取指定轴的专用接口信号状态，包括EL+、EL-、STP、STA、SD+、SD-等信号状态
        /// </summary>
        /// <param name="axis">轴号，范围0～(n×4-1)，n为卡数</param>
        /// <returns>指定轴专用信号接口状态，取低字节，其二进制位号与信号的对应关系：位0(-EL)、位1(+EL)、位2(ORG)、位3(STP)、位4(STA)、位5(-SD)、位6(+SD)、位7(保留)</returns>
        /// <remarks>所有信号的电平状态：0表示高电平，1表示低电平</remarks>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_get_axis_status", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_get_axis_status(short  axis);

        /// <summary>
        /// 向指定地址写入双字数据
        /// </summary>
        /// <param name="addr">地址</param>
        /// <param name="data">要写入的双字数据</param>
        /// <returns>正确：返回ERR_NoError；错误：返回相关错误码</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_WriteDWord", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_WriteDWord(int addr, int data);
        /// <summary>
        /// 从指定地址读取双字数据
        /// </summary>
        /// <param name="addr">地址</param>
        /// <returns>读取到的双字数据</returns>
        [DllImport("Dmc1000.dll", EntryPoint = "d1000_ReadDWord", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        public static extern short d1000_ReadDWord(int addr);
    }
