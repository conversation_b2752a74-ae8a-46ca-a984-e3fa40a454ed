using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using MyHMI.Helpers;
using MyHMI.Events;
using MyHMI.Models;

namespace MyHMI.UI.Controls
{
    /// <summary>
    /// 系统日志面板
    /// </summary>
    public partial class LogPanel : UserControl
    {
        #region 私有字段
        private GroupBox _logControlGroupBox;
        private GroupBox _logDisplayGroupBox;
        
        private ComboBox _logLevelComboBox;
        private Button _clearButton;
        private Button _saveButton;
        private CheckBox _autoScrollCheckBox;
        
        private RichTextBox _logRichTextBox;
        private Timer _refreshTimer;
        
        private int _maxLogLines = 1000;
        #endregion

        #region 构造函数
        public LogPanel()
        {
            InitializeComponent();
            InitializeUI();
            InitializeTimer();
        }
        #endregion

        #region 初始化方法
        /// <summary>
        /// 初始化UI组件
        /// </summary>
        private void InitializeUI()
        {
            this.Dock = DockStyle.Fill;
            this.BackColor = Color.White;

            CreateLogControlGroup();
            CreateLogDisplayGroup();
            
            LayoutControls();
        }

        /// <summary>
        /// 创建日志控制组
        /// </summary>
        private void CreateLogControlGroup()
        {
            _logControlGroupBox = new GroupBox
            {
                Text = "日志控制",
                Font = new Font("微软雅黑", 10F, FontStyle.Bold),
                Size = new Size(920, 60),
                Location = new Point(10, 10)
            };

            var levelLabel = new Label
            {
                Text = "日志级别:",
                Size = new Size(70, 20),
                Location = new Point(20, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            _logLevelComboBox = new ComboBox
            {
                Size = new Size(100, 25),
                Location = new Point(100, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _logLevelComboBox.Items.AddRange(new string[] { "全部", "调试", "信息", "警告", "错误" });
            _logLevelComboBox.SelectedIndex = 0;
            _logLevelComboBox.SelectedIndexChanged += LogLevelComboBox_SelectedIndexChanged;

            _clearButton = new Button
            {
                Text = "清空日志",
                Size = new Size(80, 25),
                Location = new Point(220, 23),
                UseVisualStyleBackColor = true
            };
            _clearButton.Click += ClearButton_Click;

            _saveButton = new Button
            {
                Text = "保存日志",
                Size = new Size(80, 25),
                Location = new Point(310, 23),
                UseVisualStyleBackColor = true
            };
            _saveButton.Click += SaveButton_Click;

            _autoScrollCheckBox = new CheckBox
            {
                Text = "自动滚动",
                Size = new Size(80, 25),
                Location = new Point(410, 23),
                Checked = true
            };

            _logControlGroupBox.Controls.AddRange(new Control[] 
            { 
                levelLabel, _logLevelComboBox, _clearButton, _saveButton, _autoScrollCheckBox
            });

            this.Controls.Add(_logControlGroupBox);
        }

        /// <summary>
        /// 创建日志显示组
        /// </summary>
        private void CreateLogDisplayGroup()
        {
            _logDisplayGroupBox = new GroupBox
            {
                Text = "系统日志",
                Font = new Font("微软雅黑", 10F, FontStyle.Bold),
                Size = new Size(920, 450),
                Location = new Point(10, 80)
            };

            _logRichTextBox = new RichTextBox
            {
                Size = new Size(900, 420),
                Location = new Point(10, 25),
                ReadOnly = true,
                Font = new Font("Consolas", 9F),
                BackColor = Color.Black,
                ForeColor = Color.LightGreen,
                ScrollBars = RichTextBoxScrollBars.Vertical,
                WordWrap = false
            };

            _logDisplayGroupBox.Controls.Add(_logRichTextBox);
            this.Controls.Add(_logDisplayGroupBox);
        }

        /// <summary>
        /// 布局控件
        /// </summary>
        private void LayoutControls()
        {
            this.MinimumSize = new Size(940, 540);
        }

        /// <summary>
        /// 初始化定时器
        /// </summary>
        private void InitializeTimer()
        {
            _refreshTimer = new Timer
            {
                Interval = 1000 // 1秒刷新一次
            };
            _refreshTimer.Tick += RefreshTimer_Tick;
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 异步初始化面板
        /// </summary>
        public async Task InitializeAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                // 启动刷新定时器
                _refreshTimer.Start();

                // 加载初始日志
                await RefreshLogsAsync();

                LogHelper.Info("日志面板初始化完成");

                return true;
            }, false, "日志面板初始化");
        }

        /// <summary>
        /// 异步释放资源
        /// </summary>
        public async Task DisposeAsync()
        {
            await Task.Run(() =>
            {
                // 停止定时器
                _refreshTimer?.Stop();
                _refreshTimer?.Dispose();

                LogHelper.Info("日志面板资源释放完成");
            });
        }

        /// <summary>
        /// 刷新日志
        /// </summary>
        public async Task RefreshLogs()
        {
            await RefreshLogsAsync();
        }
        #endregion

        #region 事件处理器
        /// <summary>
        /// 日志级别选择变化事件
        /// </summary>
        private async void LogLevelComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            await RefreshLogsAsync();
        }

        /// <summary>
        /// 清空按钮点击事件
        /// </summary>
        private void ClearButton_Click(object sender, EventArgs e)
        {
            _logRichTextBox.Clear();
            LogHelper.Info("日志显示已清空");
        }

        /// <summary>
        /// 保存按钮点击事件
        /// </summary>
        private void SaveButton_Click(object sender, EventArgs e)
        {
            SaveLogs();
        }

        /// <summary>
        /// 刷新定时器事件
        /// </summary>
        private async void RefreshTimer_Tick(object sender, EventArgs e)
        {
            await RefreshLogsAsync();
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 刷新日志显示
        /// </summary>
        private async Task RefreshLogsAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                await Task.Run(() =>
                {
                    // 这里应该从LogHelper获取日志内容
                    // 由于LogHelper是静态类，我们模拟获取日志
                    var logs = GetFilteredLogs();
                    
                    UIHelper.SafeInvoke(() =>
                    {
                        UpdateLogDisplay(logs);
                    });
                });

                return true;
            }, false, "刷新日志显示");
        }

        /// <summary>
        /// 获取过滤后的日志
        /// </summary>
        private string[] GetFilteredLogs()
        {
            // 这里应该实现从日志文件或内存中获取日志的逻辑
            // 为了演示，返回模拟日志
            var sampleLogs = new[]
            {
                $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [INFO] 系统启动完成",
                $"[{DateTime.Now.AddMinutes(-1):yyyy-MM-dd HH:mm:ss}] [DEBUG] IO管理器初始化",
                $"[{DateTime.Now.AddMinutes(-2):yyyy-MM-dd HH:mm:ss}] [INFO] 电机管理器初始化完成",
                $"[{DateTime.Now.AddMinutes(-3):yyyy-MM-dd HH:mm:ss}] [WARN] 相机连接超时",
                $"[{DateTime.Now.AddMinutes(-4):yyyy-MM-dd HH:mm:ss}] [ERROR] Modbus连接失败"
            };

            // 根据选择的日志级别过滤
            string selectedLevel = _logLevelComboBox.SelectedItem?.ToString() ?? "全部";
            if (selectedLevel == "全部")
            {
                return sampleLogs;
            }
            else
            {
                string levelFilter = GetLogLevelFilter(selectedLevel);
                var filteredLogs = new System.Collections.Generic.List<string>();
                
                foreach (var log in sampleLogs)
                {
                    if (log.Contains($"[{levelFilter}]"))
                    {
                        filteredLogs.Add(log);
                    }
                }
                
                return filteredLogs.ToArray();
            }
        }

        /// <summary>
        /// 获取日志级别过滤器
        /// </summary>
        private string GetLogLevelFilter(string selectedLevel)
        {
            switch (selectedLevel)
            {
                case "调试": return "DEBUG";
                case "信息": return "INFO";
                case "警告": return "WARN";
                case "错误": return "ERROR";
                default: return "";
            }
        }

        /// <summary>
        /// 更新日志显示
        /// </summary>
        private void UpdateLogDisplay(string[] logs)
        {
            // 保存当前滚动位置
            bool shouldAutoScroll = _autoScrollCheckBox.Checked;
            int currentSelectionStart = _logRichTextBox.SelectionStart;
            
            // 清空并重新添加日志
            _logRichTextBox.Clear();
            
            foreach (var log in logs)
            {
                AppendLogLine(log);
            }

            // 限制日志行数
            if (_logRichTextBox.Lines.Length > _maxLogLines)
            {
                var lines = _logRichTextBox.Lines;
                var newLines = new string[_maxLogLines / 2];
                Array.Copy(lines, lines.Length - newLines.Length, newLines, 0, newLines.Length);
                _logRichTextBox.Lines = newLines;
            }

            // 自动滚动到底部
            if (shouldAutoScroll)
            {
                _logRichTextBox.SelectionStart = _logRichTextBox.Text.Length;
                _logRichTextBox.ScrollToCaret();
            }
        }

        /// <summary>
        /// 添加日志行
        /// </summary>
        private void AppendLogLine(string logLine)
        {
            // 根据日志级别设置颜色
            Color logColor = GetLogColor(logLine);
            
            _logRichTextBox.SelectionStart = _logRichTextBox.TextLength;
            _logRichTextBox.SelectionLength = 0;
            _logRichTextBox.SelectionColor = logColor;
            _logRichTextBox.AppendText(logLine + Environment.NewLine);
        }

        /// <summary>
        /// 获取日志颜色
        /// </summary>
        private Color GetLogColor(string logLine)
        {
            if (logLine.Contains("[ERROR]"))
                return Color.Red;
            else if (logLine.Contains("[WARN]"))
                return Color.Yellow;
            else if (logLine.Contains("[INFO]"))
                return Color.LightGreen;
            else if (logLine.Contains("[DEBUG]"))
                return Color.Gray;
            else
                return Color.White;
        }

        /// <summary>
        /// 保存日志
        /// </summary>
        private void SaveLogs()
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "文本文件|*.txt|所有文件|*.*",
                    FileName = $"系统日志_{DateTime.Now:yyyyMMdd_HHmmss}.txt"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    System.IO.File.WriteAllText(saveDialog.FileName, _logRichTextBox.Text, System.Text.Encoding.UTF8);
                    MessageBox.Show("日志保存成功", "保存完成", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存日志失败: {ex.Message}", "保存错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        #endregion
    }
}
