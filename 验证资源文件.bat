@echo off
echo ========================================
echo MyHMI 项目资源文件验证
echo ========================================

echo.
echo 检查主窗体资源文件...
if exist "UI\MainForm.resx" (
    echo ✓ UI\MainForm.resx 存在
) else (
    echo ✗ UI\MainForm.resx 缺失
)

if exist "UI\MainForm.Designer.cs" (
    echo ✓ UI\MainForm.Designer.cs 存在
) else (
    echo ✗ UI\MainForm.Designer.cs 缺失
)

echo.
echo 检查用户控件资源文件...

set CONTROLS=IOControlPanel MotorControlPanel VisionPanel CommunicationPanel StatisticsPanel LogPanel

for %%C in (%CONTROLS%) do (
    if exist "UI\Controls\%%C.resx" (
        echo ✓ UI\Controls\%%C.resx 存在
    ) else (
        echo ✗ UI\Controls\%%C.resx 缺失
    )
    
    if exist "UI\Controls\%%C.Designer.cs" (
        echo ✓ UI\Controls\%%C.Designer.cs 存在
    ) else (
        echo ✗ UI\Controls\%%C.Designer.cs 缺失
    )
)

echo.
echo 检查测试窗体资源文件...
if exist "Testing\TestRunnerForm.resx" (
    echo ✓ Testing\TestRunnerForm.resx 存在
) else (
    echo ✗ Testing\TestRunnerForm.resx 缺失
)

if exist "Testing\TestRunnerForm.Designer.cs" (
    echo ✓ Testing\TestRunnerForm.Designer.cs 存在
) else (
    echo ✗ Testing\TestRunnerForm.Designer.cs 缺失
)

echo.
echo 检查项目文件...
if exist "MyHMI.csproj" (
    echo ✓ MyHMI.csproj 存在
) else (
    echo ✗ MyHMI.csproj 缺失
)

if exist "MyHMI.sln" (
    echo ✓ MyHMI.sln 存在
) else (
    echo ✗ MyHMI.sln 缺失
)

echo.
echo 检查配置文件...
if exist "App.config" (
    echo ✓ App.config 存在
) else (
    echo ✗ App.config 缺失
)

if exist "Config\SystemConfig.json" (
    echo ✓ Config\SystemConfig.json 存在
) else (
    echo ✗ Config\SystemConfig.json 缺失
)

echo.
echo ========================================
echo 验证完成！
echo ========================================
echo.
echo 现在可以在 Visual Studio 中：
echo 1. 打开 MyHMI.sln 解决方案
echo 2. 选择 x64 平台和 Debug 配置
echo 3. 编译项目 (Ctrl+Shift+B)
echo 4. 在设计器中查看 MainForm.cs
echo.
pause
