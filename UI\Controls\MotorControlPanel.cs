using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using MyHMI.Managers;
using MyHMI.Events;
using MyHMI.Helpers;
using MyHMI.Models;

namespace MyHMI.UI.Controls
{
    /// <summary>
    /// 电机控制面板
    /// </summary>
    public partial class MotorControlPanel : UserControl
    {
        #region 私有字段
        private GroupBox _motorSelectGroupBox;
        private GroupBox _positionControlGroupBox;
        private GroupBox _continuousControlGroupBox;
        private GroupBox _statusGroupBox;
        
        private ComboBox _motorComboBox;
        private NumericUpDown _positionNumericUpDown;
        private NumericUpDown _speedNumericUpDown;
        private Button _moveToButton;
        private Button _homeButton;
        private Button _stopButton;
        
        private Button _forwardButton;
        private Button _backwardButton;
        private Button _emergencyStopButton;
        
        private Label _currentPositionLabel;
        private Label _motorStatusLabel;
        private Label _isMovingLabel;
        private ProgressBar _progressBar;
        
        private Timer _statusUpdateTimer;
        private int _selectedMotorId = 0;
        #endregion

        #region 构造函数
        public MotorControlPanel()
        {
            InitializeComponent();
            InitializeUI();
            InitializeTimer();
        }
        #endregion

        #region 初始化方法
        /// <summary>
        /// 初始化UI组件
        /// </summary>
        private void InitializeUI()
        {
            this.Dock = DockStyle.Fill;
            this.BackColor = Color.White;

            CreateMotorSelectGroup();
            CreatePositionControlGroup();
            CreateContinuousControlGroup();
            CreateStatusGroup();
            
            LayoutControls();
        }

        /// <summary>
        /// 创建电机选择组
        /// </summary>
        private void CreateMotorSelectGroup()
        {
            _motorSelectGroupBox = new GroupBox
            {
                Text = "电机选择",
                Font = new Font("微软雅黑", 10F, FontStyle.Bold),
                Size = new Size(200, 80),
                Location = new Point(10, 10)
            };

            var motorLabel = new Label
            {
                Text = "电机:",
                Size = new Size(40, 20),
                Location = new Point(10, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            _motorComboBox = new ComboBox
            {
                Size = new Size(120, 25),
                Location = new Point(60, 28),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            
            // 添加电机选项
            for (int i = 0; i < 4; i++)
            {
                _motorComboBox.Items.Add($"电机 {i}");
            }
            _motorComboBox.SelectedIndex = 0;
            _motorComboBox.SelectedIndexChanged += MotorComboBox_SelectedIndexChanged;

            _motorSelectGroupBox.Controls.AddRange(new Control[] { motorLabel, _motorComboBox });
            this.Controls.Add(_motorSelectGroupBox);
        }

        /// <summary>
        /// 创建位置控制组
        /// </summary>
        private void CreatePositionControlGroup()
        {
            _positionControlGroupBox = new GroupBox
            {
                Text = "位置控制",
                Font = new Font("微软雅黑", 10F, FontStyle.Bold),
                Size = new Size(300, 120),
                Location = new Point(220, 10)
            };

            var positionLabel = new Label
            {
                Text = "目标位置:",
                Size = new Size(70, 20),
                Location = new Point(10, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            _positionNumericUpDown = new NumericUpDown
            {
                Size = new Size(100, 25),
                Location = new Point(90, 28),
                Minimum = -999999,
                Maximum = 999999,
                DecimalPlaces = 2,
                Value = 0
            };

            var speedLabel = new Label
            {
                Text = "速度:",
                Size = new Size(40, 20),
                Location = new Point(200, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            _speedNumericUpDown = new NumericUpDown
            {
                Size = new Size(80, 25),
                Location = new Point(210, 28),
                Minimum = 1,
                Maximum = 10000,
                Value = 1000
            };

            _moveToButton = new Button
            {
                Text = "移动到位置",
                Size = new Size(90, 30),
                Location = new Point(10, 65),
                UseVisualStyleBackColor = true
            };
            _moveToButton.Click += MoveToButton_Click;

            _homeButton = new Button
            {
                Text = "回零",
                Size = new Size(60, 30),
                Location = new Point(110, 65),
                UseVisualStyleBackColor = true
            };
            _homeButton.Click += HomeButton_Click;

            _stopButton = new Button
            {
                Text = "停止",
                Size = new Size(60, 30),
                Location = new Point(180, 65),
                UseVisualStyleBackColor = true,
                BackColor = Color.LightCoral
            };
            _stopButton.Click += StopButton_Click;

            _positionControlGroupBox.Controls.AddRange(new Control[] 
            { 
                positionLabel, _positionNumericUpDown, speedLabel, _speedNumericUpDown,
                _moveToButton, _homeButton, _stopButton 
            });

            this.Controls.Add(_positionControlGroupBox);
        }

        /// <summary>
        /// 创建连续控制组
        /// </summary>
        private void CreateContinuousControlGroup()
        {
            _continuousControlGroupBox = new GroupBox
            {
                Text = "连续运动控制",
                Font = new Font("微软雅黑", 10F, FontStyle.Bold),
                Size = new Size(300, 80),
                Location = new Point(530, 10)
            };

            _forwardButton = new Button
            {
                Text = "正向运动",
                Size = new Size(80, 40),
                Location = new Point(20, 25),
                UseVisualStyleBackColor = true
            };
            _forwardButton.MouseDown += ForwardButton_MouseDown;
            _forwardButton.MouseUp += ForwardButton_MouseUp;

            _backwardButton = new Button
            {
                Text = "反向运动",
                Size = new Size(80, 40),
                Location = new Point(110, 25),
                UseVisualStyleBackColor = true
            };
            _backwardButton.MouseDown += BackwardButton_MouseDown;
            _backwardButton.MouseUp += BackwardButton_MouseUp;

            _emergencyStopButton = new Button
            {
                Text = "急停",
                Size = new Size(60, 40),
                Location = new Point(200, 25),
                UseVisualStyleBackColor = true,
                BackColor = Color.Red,
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 9F, FontStyle.Bold)
            };
            _emergencyStopButton.Click += EmergencyStopButton_Click;

            _continuousControlGroupBox.Controls.AddRange(new Control[] 
            { 
                _forwardButton, _backwardButton, _emergencyStopButton 
            });

            this.Controls.Add(_continuousControlGroupBox);
        }

        /// <summary>
        /// 创建状态组
        /// </summary>
        private void CreateStatusGroup()
        {
            _statusGroupBox = new GroupBox
            {
                Text = "电机状态",
                Font = new Font("微软雅黑", 10F, FontStyle.Bold),
                Size = new Size(820, 120),
                Location = new Point(10, 140)
            };

            _currentPositionLabel = new Label
            {
                Text = "当前位置: 0.00",
                Size = new Size(150, 20),
                Location = new Point(20, 30),
                Font = new Font("微软雅黑", 9F)
            };

            _motorStatusLabel = new Label
            {
                Text = "电机状态: 停止",
                Size = new Size(150, 20),
                Location = new Point(200, 30),
                Font = new Font("微软雅黑", 9F)
            };

            _isMovingLabel = new Label
            {
                Text = "运动状态: 静止",
                Size = new Size(150, 20),
                Location = new Point(380, 30),
                Font = new Font("微软雅黑", 9F)
            };

            var progressLabel = new Label
            {
                Text = "运动进度:",
                Size = new Size(70, 20),
                Location = new Point(20, 70),
                Font = new Font("微软雅黑", 9F)
            };

            _progressBar = new ProgressBar
            {
                Size = new Size(400, 20),
                Location = new Point(100, 70),
                Style = ProgressBarStyle.Continuous
            };

            _statusGroupBox.Controls.AddRange(new Control[] 
            { 
                _currentPositionLabel, _motorStatusLabel, _isMovingLabel,
                progressLabel, _progressBar
            });

            this.Controls.Add(_statusGroupBox);
        }

        /// <summary>
        /// 布局控件
        /// </summary>
        private void LayoutControls()
        {
            this.MinimumSize = new Size(840, 270);
        }

        /// <summary>
        /// 初始化定时器
        /// </summary>
        private void InitializeTimer()
        {
            _statusUpdateTimer = new Timer
            {
                Interval = 200 // 200ms更新一次
            };
            _statusUpdateTimer.Tick += StatusUpdateTimer_Tick;
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 异步初始化面板
        /// </summary>
        public async Task InitializeAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                // 订阅电机事件
                MotorManager.Instance.MotorPositionChanged += MotorManager_MotorPositionChanged;
                MotorManager.Instance.MotorStatusChanged += MotorManager_MotorStatusChanged;

                // 启动状态更新定时器
                _statusUpdateTimer.Start();

                // 更新初始状态
                await UpdateMotorStatusAsync();

                LogHelper.Info("电机控制面板初始化完成");

                return true;
            }, false, "电机控制面板初始化");
        }

        /// <summary>
        /// 异步释放资源
        /// </summary>
        public async Task DisposeAsync()
        {
            await Task.Run(() =>
            {
                // 停止定时器
                _statusUpdateTimer?.Stop();
                _statusUpdateTimer?.Dispose();

                // 取消订阅事件
                MotorManager.Instance.MotorPositionChanged -= MotorManager_MotorPositionChanged;
                MotorManager.Instance.MotorStatusChanged -= MotorManager_MotorStatusChanged;

                LogHelper.Info("电机控制面板资源释放完成");
            });
        }
        #endregion

        #region 事件处理器
        /// <summary>
        /// 电机选择变化事件
        /// </summary>
        private async void MotorComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            _selectedMotorId = _motorComboBox.SelectedIndex;
            await UpdateMotorStatusAsync();
        }

        /// <summary>
        /// 移动到位置按钮点击事件
        /// </summary>
        private async void MoveToButton_Click(object sender, EventArgs e)
        {
            await MoveToPositionAsync();
        }

        /// <summary>
        /// 回零按钮点击事件
        /// </summary>
        private async void HomeButton_Click(object sender, EventArgs e)
        {
            await HomeMotorAsync();
        }

        /// <summary>
        /// 停止按钮点击事件
        /// </summary>
        private async void StopButton_Click(object sender, EventArgs e)
        {
            await StopMotorAsync();
        }

        /// <summary>
        /// 正向运动按钮按下事件
        /// </summary>
        private async void ForwardButton_MouseDown(object sender, MouseEventArgs e)
        {
            await StartContinuousMovementAsync(true);
        }

        /// <summary>
        /// 正向运动按钮释放事件
        /// </summary>
        private async void ForwardButton_MouseUp(object sender, MouseEventArgs e)
        {
            await StopMotorAsync();
        }

        /// <summary>
        /// 反向运动按钮按下事件
        /// </summary>
        private async void BackwardButton_MouseDown(object sender, MouseEventArgs e)
        {
            await StartContinuousMovementAsync(false);
        }

        /// <summary>
        /// 反向运动按钮释放事件
        /// </summary>
        private async void BackwardButton_MouseUp(object sender, MouseEventArgs e)
        {
            await StopMotorAsync();
        }

        /// <summary>
        /// 急停按钮点击事件
        /// </summary>
        private async void EmergencyStopButton_Click(object sender, EventArgs e)
        {
            await EmergencyStopAsync();
        }

        /// <summary>
        /// 电机位置变化事件处理
        /// </summary>
        private void MotorManager_MotorPositionChanged(object sender, MotorPositionEventArgs e)
        {
            if (e.MotorId == _selectedMotorId)
            {
                UIHelper.SafeInvoke(() =>
                {
                    _currentPositionLabel.Text = $"当前位置: {e.Position:F2}";
                });
            }
        }

        /// <summary>
        /// 电机状态变化事件处理
        /// </summary>
        private void MotorManager_MotorStatusChanged(object sender, MotorStatusEventArgs e)
        {
            if (e.Status.MotorId == _selectedMotorId)
            {
                UIHelper.SafeInvoke(() =>
                {
                    _motorStatusLabel.Text = $"电机状态: {(e.Status.IsEnabled ? "使能" : "失能")}";
                    _isMovingLabel.Text = $"运动状态: {(e.Status.IsMoving ? "运动中" : "静止")}";

                    // 更新进度条颜色
                    if (e.Status.IsMoving)
                    {
                        _progressBar.Style = ProgressBarStyle.Marquee;
                    }
                    else
                    {
                        _progressBar.Style = ProgressBarStyle.Continuous;
                        _progressBar.Value = 0;
                    }
                });
            }
        }

        /// <summary>
        /// 状态更新定时器事件
        /// </summary>
        private async void StatusUpdateTimer_Tick(object sender, EventArgs e)
        {
            await UpdateMotorStatusAsync();
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 移动到指定位置
        /// </summary>
        private async Task MoveToPositionAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                double targetPosition = (double)_positionNumericUpDown.Value;
                double speed = (double)_speedNumericUpDown.Value;

                bool result = await MotorManager.Instance.MoveToAsync(_selectedMotorId, targetPosition, speed);

                if (result)
                {
                    LogHelper.Info($"电机{_selectedMotorId}开始移动到位置: {targetPosition}");
                }
                else
                {
                    MessageBox.Show($"电机{_selectedMotorId}移动失败", "错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

                return true;
            }, false, $"移动电机{_selectedMotorId}到位置");
        }

        /// <summary>
        /// 电机回零
        /// </summary>
        private async Task HomeMotorAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                bool result = await MotorManager.Instance.HomeMotorAsync(_selectedMotorId);

                if (result)
                {
                    LogHelper.Info($"电机{_selectedMotorId}开始回零");
                }
                else
                {
                    MessageBox.Show($"电机{_selectedMotorId}回零失败", "错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

                return true;
            }, false, $"电机{_selectedMotorId}回零");
        }

        /// <summary>
        /// 停止电机
        /// </summary>
        private async Task StopMotorAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                bool result = await MotorManager.Instance.StopMotorAsync(_selectedMotorId);

                if (result)
                {
                    LogHelper.Debug($"电机{_selectedMotorId}已停止");
                }

                return true;
            }, false, $"停止电机{_selectedMotorId}");
        }

        /// <summary>
        /// 开始连续运动
        /// </summary>
        private async Task StartContinuousMovementAsync(bool forward)
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                double speed = (double)_speedNumericUpDown.Value;

                bool result = await MotorManager.Instance.ContinuousMoveAsync(_selectedMotorId, forward, speed);

                if (result)
                {
                    LogHelper.Debug($"电机{_selectedMotorId}开始连续运动: {(forward ? "正向" : "反向")}");
                }

                return true;
            }, false, $"电机{_selectedMotorId}连续运动");
        }

        /// <summary>
        /// 急停
        /// </summary>
        private async Task EmergencyStopAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                // 停止所有电机
                for (int i = 0; i < 4; i++)
                {
                    await MotorManager.Instance.StopMotorAsync(i);
                }

                LogHelper.Warning("执行急停操作");
                MessageBox.Show("急停操作已执行", "急停",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);

                return true;
            }, false, "急停操作");
        }

        /// <summary>
        /// 更新电机状态
        /// </summary>
        private async Task UpdateMotorStatusAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                var status = await MotorManager.Instance.GetMotorStatusAsync(_selectedMotorId);

                if (status != null)
                {
                    UIHelper.SafeInvoke(() =>
                    {
                        _currentPositionLabel.Text = $"当前位置: {status.CurrentPosition:F2}";
                        _motorStatusLabel.Text = $"电机状态: {(status.IsEnabled ? "使能" : "失能")}";
                        _isMovingLabel.Text = $"运动状态: {(status.IsMoving ? "运动中" : "静止")}";
                    });
                }

                return true;
            }, false, $"更新电机{_selectedMotorId}状态");
        }
        #endregion
    }
}
