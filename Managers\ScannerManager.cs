using System;
using System.IO.Ports;
using System.Threading.Tasks;
using MyHMI.Events;
using MyHMI.Models;
using MyHMI.Helpers;
using MyHMI.Settings;

namespace MyHMI.Managers
{
    /// <summary>
    /// 串口扫描枪管理器
    /// 负责串口通信、数据接收和事件发布
    /// </summary>
    public class ScannerManager
    {
        #region 单例模式
        private static readonly Lazy<ScannerManager> _instance = new Lazy<ScannerManager>(() => new ScannerManager());
        public static ScannerManager Instance => _instance.Value;
        private ScannerManager() { }
        #endregion

        #region 事件定义
        /// <summary>
        /// 条码扫描事件
        /// </summary>
        public event EventHandler<BarcodeScannedEventArgs> BarcodeScanned;

        /// <summary>
        /// 通信状态变化事件
        /// </summary>
        public event EventHandler<CommunicationStatusChangedEventArgs> StatusChanged;
        #endregion

        #region 私有字段
        private SerialPort _serialPort;
        private bool _isInitialized = false;
        private CommunicationStatus _status = CommunicationStatus.Disconnected;
        private SerialPortConfiguration _config;
        private readonly object _lockObject = new object();
        #endregion

        #region 公共属性
        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// 当前连接状态
        /// </summary>
        public CommunicationStatus Status => _status;

        /// <summary>
        /// 串口配置
        /// </summary>
        public SerialPortConfiguration Configuration => _config;
        #endregion

        #region 初始化和释放
        /// <summary>
        /// 异步初始化扫描枪管理器
        /// </summary>
        /// <returns></returns>
        public async Task<bool> InitializeAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_isInitialized)
                {
                    LogHelper.Warning("ScannerManager已经初始化，跳过重复初始化");
                    return true;
                }

                LogHelper.Info("开始初始化ScannerManager...");

                // 加载配置
                LoadConfiguration();

                // 初始化串口
                await InitializeSerialPortAsync();

                // 尝试连接
                bool connectResult = await ConnectAsync();
                if (!connectResult)
                {
                    LogHelper.Warning("扫描枪连接失败，但初始化完成，可稍后重试连接");
                }

                _isInitialized = true;
                LogHelper.Info("ScannerManager初始化完成");
                return true;

            }, false, "ScannerManager初始化");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public async Task DisposeAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info("开始释放ScannerManager资源...");

                await DisconnectAsync();
                
                lock (_lockObject)
                {
                    _serialPort?.Dispose();
                    _serialPort = null;
                }

                _isInitialized = false;
                LogHelper.Info("ScannerManager资源释放完成");

                return true;
            }, false, "ScannerManager资源释放");
        }
        #endregion

        #region 连接管理
        /// <summary>
        /// 异步连接扫描枪
        /// </summary>
        /// <returns></returns>
        public async Task<bool> ConnectAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_status == CommunicationStatus.Connected)
                {
                    LogHelper.Info("扫描枪已经连接");
                    return true;
                }

                UpdateStatus(CommunicationStatus.Connecting, "正在连接扫描枪...");

                await Task.Run(() =>
                {
                    lock (_lockObject)
                    {
                        if (_serialPort != null && !_serialPort.IsOpen)
                        {
                            _serialPort.Open();
                            LogHelper.Info($"扫描枪连接成功: {_config.PortName}");
                        }
                    }
                });

                UpdateStatus(CommunicationStatus.Connected, "扫描枪连接成功");
                return true;

            }, false, "连接扫描枪");
        }

        /// <summary>
        /// 异步断开连接
        /// </summary>
        /// <returns></returns>
        public async Task<bool> DisconnectAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_status == CommunicationStatus.Disconnected)
                {
                    return true;
                }

                await Task.Run(() =>
                {
                    lock (_lockObject)
                    {
                        if (_serialPort != null && _serialPort.IsOpen)
                        {
                            _serialPort.Close();
                            LogHelper.Info("扫描枪连接已断开");
                        }
                    }
                });

                UpdateStatus(CommunicationStatus.Disconnected, "扫描枪连接已断开");
                return true;

            }, false, "断开扫描枪连接");
        }

        /// <summary>
        /// 重新连接
        /// </summary>
        /// <returns></returns>
        public async Task<bool> ReconnectAsync()
        {
            LogHelper.Info("尝试重新连接扫描枪...");
            
            await DisconnectAsync();
            await Task.Delay(1000); // 等待1秒
            
            return await ConnectAsync();
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfiguration()
        {
            // 从新的Settings系统加载配置
            var communicationSettings = Settings.Settings.Current.Communication;

            _config = new SerialPortConfiguration
            {
                PortName = communicationSettings.ScannerComPort,
                BaudRate = communicationSettings.ScannerBaudRate,
                DataBits = communicationSettings.ScannerDataBits,
                StopBits = (StopBits)communicationSettings.ScannerStopBits,
                Parity = ParseParity(communicationSettings.ScannerParity),
                ReadTimeout = communicationSettings.ScannerTimeout,
                WriteTimeout = communicationSettings.ScannerTimeout
            };

            LogHelper.Info($"扫描枪配置从Settings系统加载: {_config.PortName}, {_config.BaudRate}, {_config.DataBits}, {_config.StopBits}, {_config.Parity}");
        }

        /// <summary>
        /// 解析奇偶校验字符串
        /// </summary>
        /// <param name="parityString">奇偶校验字符串</param>
        /// <returns>奇偶校验枚举</returns>
        private Parity ParseParity(string parityString)
        {
            switch (parityString?.ToUpper())
            {
                case "EVEN": return Parity.Even;
                case "ODD": return Parity.Odd;
                case "MARK": return Parity.Mark;
                case "SPACE": return Parity.Space;
                default: return Parity.None;
            }
        }

        /// <summary>
        /// 初始化串口
        /// </summary>
        /// <returns></returns>
        private async Task InitializeSerialPortAsync()
        {
            await Task.Run(() =>
            {
                lock (_lockObject)
                {
                    _serialPort = new SerialPort
                    {
                        PortName = _config.PortName,
                        BaudRate = _config.BaudRate,
                        DataBits = _config.DataBits,
                        StopBits = _config.StopBits,
                        Parity = _config.Parity,
                        ReadTimeout = _config.ReadTimeout,
                        WriteTimeout = _config.WriteTimeout,
                        Encoding = System.Text.Encoding.UTF8
                    };

                    // 订阅数据接收事件
                    _serialPort.DataReceived += SerialPort_DataReceived;
                    _serialPort.ErrorReceived += SerialPort_ErrorReceived;

                    LogHelper.Info("串口初始化完成");
                }
            });
        }

        /// <summary>
        /// 串口数据接收事件处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void SerialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            ExceptionHelper.SafeExecute(() =>
            {
                if (_serialPort == null || !_serialPort.IsOpen)
                    return;

                try
                {
                    string data = _serialPort.ReadLine().Trim();
                    
                    if (!string.IsNullOrEmpty(data))
                    {
                        LogHelper.Info($"接收到扫描数据: {data}");
                        
                        var scannerData = new ScannerData(data);
                        BarcodeScanned?.Invoke(this, new BarcodeScannedEventArgs(scannerData));
                    }
                }
                catch (TimeoutException)
                {
                    LogHelper.Warning("串口读取超时");
                }
                catch (Exception ex)
                {
                    LogHelper.Error("处理串口数据失败", ex);
                }

            }, "处理串口数据接收");
        }

        /// <summary>
        /// 串口错误事件处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void SerialPort_ErrorReceived(object sender, SerialErrorReceivedEventArgs e)
        {
            LogHelper.Error($"串口错误: {e.EventType}");
            UpdateStatus(CommunicationStatus.Error, $"串口错误: {e.EventType}");
        }

        /// <summary>
        /// 更新连接状态
        /// </summary>
        /// <param name="status">新状态</param>
        /// <param name="description">状态描述</param>
        private void UpdateStatus(CommunicationStatus status, string description)
        {
            if (_status != status)
            {
                _status = status;
                LogHelper.Info($"扫描枪状态变更: {status} - {description}");
                StatusChanged?.Invoke(this, new CommunicationStatusChangedEventArgs("Scanner", status, description));
            }
        }
        #endregion
    }
}
