using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

using MyHMI.Events;
using MyHMI.Helpers;
using MyHMI.Models;

namespace MyHMI.Managers
{
    /// <summary>
    /// DMC1000B IO控制管理器
    /// 负责DMC1000B控制卡的IO输入输出控制和监控
    /// 基于编程手册和功能封装指南实现
    /// </summary>
    public class DMC1000BIOManager
    {
        #region 单例模式
        private static readonly Lazy<DMC1000BIOManager> _instance = new Lazy<DMC1000BIOManager>(() => new DMC1000BIOManager());
        public static DMC1000BIOManager Instance => _instance.Value;
        #endregion

        #region 事件定义
        /// <summary>
        /// IO输入状态变化事件
        /// </summary>
        public event EventHandler<Models.IOInputStateChangedEventArgs> IOInputStateChanged;

        /// <summary>
        /// IO输出状态变化事件
        /// </summary>
        public event EventHandler<Models.IOOutputStateChangedEventArgs> IOOutputStateChanged;

        /// <summary>
        /// 批量IO状态变化事件
        /// </summary>
        public event EventHandler<BatchIOStateChangedEventArgs> BatchIOStateChanged;
        #endregion

        #region 私有字段
        private readonly object _lockObject = new object();
        private bool _isInitialized = false;
        private bool _isMonitoring = false;
        private CancellationTokenSource _monitoringCancellationTokenSource;
        private Thread _monitoringThread;

        // IO状态缓存
        private readonly Dictionary<string, bool> _inputStates = new Dictionary<string, bool>();
        private readonly Dictionary<string, bool> _outputStates = new Dictionary<string, bool>();
        private readonly Dictionary<string, bool> _previousInputStates = new Dictionary<string, bool>();

        // IO配置
        private readonly List<IOPortDefinition> _allInputPorts;
        private readonly List<IOPortDefinition> _allOutputPorts;

        // 监控参数 - 优化后的监控频率
        private const int MONITORING_INTERVAL_MS = 250; // 4Hz监控频率，平衡响应性和性能
        #endregion

        #region 构造函数
        /// <summary>
        /// 私有构造函数（单例模式）
        /// </summary>
        private DMC1000BIOManager()
        {
            // 初始化IO配置
            _allInputPorts = IOConfiguration.GetAllInputPorts();
            _allOutputPorts = IOConfiguration.GetAllOutputPorts();

            // 初始化IO状态缓存
            InitializeIOStates();
        }
        #endregion

        #region 初始化方法
        /// <summary>
        /// 初始化IO状态缓存
        /// </summary>
        private void InitializeIOStates()
        {
            try
            {
                // 初始化输入IO状态
                foreach (var port in _allInputPorts)
                {
                    _inputStates[port.IONumber] = false;
                    _previousInputStates[port.IONumber] = false;
                }

                // 初始化输出IO状态
                foreach (var port in _allOutputPorts)
                {
                    _outputStates[port.IONumber] = false;
                }

                LogHelper.Info("IO状态缓存初始化完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("IO状态缓存初始化失败", ex);
            }
        }

        /// <summary>
        /// 初始化IO管理器
        /// 遵循DMC1000B标准开发流程：初始化控制卡 -> 脉冲模式设置 -> 功能使用
        /// </summary>
        /// <returns>初始化结果</returns>
        public async Task<bool> InitializeAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync<bool>(async () =>
            {
                // 检查是否已初始化
                lock (_lockObject)
                {
                    if (_isInitialized)
                    {
                        LogHelper.Warning("IO管理器已经初始化");
                        return true;
                    }
                }

                try
                {
                    // 检查控制卡是否可用
                    if (!DMC1000BCardManager.Instance.IsCardAvailable())
                    {
                        LogHelper.Error("DMC1000B控制卡不可用，IO管理器初始化失败");
                        return false;
                    }

                    // 步骤1：设置脉冲输出模式（虽然IO不需要，但遵循标准流程）
                    // 为所有轴设置默认脉冲模式
                    for (short axis = 0; axis < 4; axis++)
                    {
                        var pulseResult = csDmc1000.DMC1000.d1000_set_pls_outmode(axis, 0);
                        if (pulseResult != 0)
                        {
                            LogHelper.Warning($"设置轴{axis}脉冲模式失败，错误码: {pulseResult}");
                        }
                    }

                    // 步骤2：读取当前IO状态
                    await ReadAllIOStatesAsync();

                    // 设置初始化标志
                    lock (_lockObject)
                    {
                        _isInitialized = true;
                    }

                    // 步骤3：启动IO状态监控
                    bool monitoringResult = await StartMonitoringAsync();
                    if (!monitoringResult)
                    {
                        LogHelper.Warning("IO状态监控启动失败，但不影响基本功能");
                    }

                    LogHelper.Info("DMC1000B IO管理器初始化成功");
                    return true;
                }
                catch (Exception ex)
                {
                    LogHelper.Error("DMC1000B IO管理器初始化失败", ex);
                    return false;
                }
            }, false, "初始化IO管理器");
        }

        /// <summary>
        /// 释放IO管理器资源
        /// 遵循DMC1000B标准开发流程：停止功能使用 -> 释放控制卡资源
        /// </summary>
        public async Task ReleaseAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                // 检查是否已初始化
                bool needsRelease;
                lock (_lockObject)
                {
                    needsRelease = _isInitialized;
                }

                if (!needsRelease)
                {
                    return;
                }

                try
                {
                    // 步骤1：停止IO监控
                    await StopMonitoringAsync();

                    // 步骤2：清理内存资源（移除紧急停止调用，避免与安全状态设置冲突）
                    // 注意：程序关闭时的IO安全状态已在MainForm中通过SetAllOutputsToSafeStateAsync()设置
                    lock (_lockObject)
                    {
                        _monitoringThread = null;
                        _monitoringCancellationTokenSource?.Dispose();
                        _inputStates.Clear();
                        _outputStates.Clear();
                        _previousInputStates.Clear();
                    }

                    // 步骤3：设置释放标志
                    lock (_lockObject)
                    {
                        _isInitialized = false;
                    }

                    LogHelper.Info("DMC1000B IO管理器资源释放完成");
                }
                catch (Exception ex)
                {
                    LogHelper.Error("DMC1000B IO管理器资源释放失败", ex);
                    // 确保状态正确
                    lock (_lockObject)
                    {
                        _isInitialized = false;
                    }
                }
            }, "释放IO管理器资源");
        }
        #endregion

        #region 简单直接测试方法（类似官方Demo）
        /// <summary>
        /// 直接读取IO输入位状态（类似官方Demo的简单方式）
        /// </summary>
        /// <param name="bitNumber">硬件位号（1-32）</param>
        /// <returns>IO状态</returns>
        public bool ReadInputBitDirect(int bitNumber)
        {
            try
            {
                if (!_isInitialized)
                {
                    LogHelper.Error("IO管理器未初始化");
                    return false;
                }

                var result = csDmc1000.DMC1000.d1000_in_bit(bitNumber);
                bool state = result == 1;

                LogHelper.Debug($"直接读取输入位 {bitNumber}: 原始值={result}, 状态={state}");
                return state;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"直接读取输入位 {bitNumber} 失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 直接设置IO输出位状态（类似官方Demo的简单方式）
        /// </summary>
        /// <param name="bitNumber">硬件位号（1-32）</param>
        /// <param name="state">输出状态</param>
        /// <returns>是否成功</returns>
        public bool SetOutputBitDirect(int bitNumber, bool state)
        {
            try
            {
                if (!_isInitialized)
                {
                    LogHelper.Error("IO管理器未初始化");
                    return false;
                }

                var result = csDmc1000.DMC1000.d1000_out_bit(bitNumber, state ? 1 : 0);
                bool success = result == 0;

                LogHelper.Debug($"直接设置输出位 {bitNumber} 为 {(state ? "ON" : "OFF")}: 返回值={result}, 成功={success}");
                return success;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"直接设置输出位 {bitNumber} 失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 测试IO功能（类似官方Demo）
        /// </summary>
        public void TestIOFunctionality()
        {
            LogHelper.Info("开始测试IO功能...");

            // 测试输入IO 1-4
            for (int i = 1; i <= 4; i++)
            {
                bool inputState = ReadInputBitDirect(i);
                LogHelper.Info($"输入IO{i}: {(inputState ? "ON" : "OFF")}");
            }

            // 测试输出IO 1-4
            for (int i = 1; i <= 4; i++)
            {
                LogHelper.Info($"测试输出IO{i}...");

                // 设置为ON
                bool result1 = SetOutputBitDirect(i, true);
                LogHelper.Info($"设置输出IO{i}为ON: {(result1 ? "成功" : "失败")}");

                System.Threading.Thread.Sleep(500); // 等待500ms

                // 设置为OFF
                bool result2 = SetOutputBitDirect(i, false);
                LogHelper.Info($"设置输出IO{i}为OFF: {(result2 ? "成功" : "失败")}");
            }

            LogHelper.Info("IO功能测试完成");
        }
        #endregion

        #region IO输入控制方法
        /// <summary>
        /// 读取单个输入IO状态
        /// </summary>
        /// <param name="ioNumber">IO编号</param>
        /// <returns>IO状态</returns>
        public async Task<bool> ReadInputAsync(string ioNumber)
        {
            return await ExceptionHelper.SafeExecuteAsync<bool>(async () =>
            {
                if (!_isInitialized)
                {
                    throw new InvalidOperationException("IO管理器未初始化");
                }

                var portDef = IOConfiguration.GetIOPortByNumber(ioNumber);
                if (portDef == null)
                {
                    throw new ArgumentException($"无效的IO编号: {ioNumber}");
                }

                // 根据IO类型选择不同的读取方法
                bool state = false;
                if (portDef.Type == IOPortType.Input)
                {
                    // 普通输入IO，使用d1000_in_bit函数
                    var result = csDmc1000.DMC1000.d1000_in_bit(portDef.BitNumber);
                    state = result == 1;
                    LogHelper.Debug($"读取输入IO {ioNumber} (位号{portDef.BitNumber}): 原始值={result}, 状态={state}");
                }
                else if (portDef.Type == IOPortType.AxisSpecialInput)
                {
                    // 专用轴信号，使用专门的方法
                    state = await ReadAxisSpecialInputAsync(ioNumber);
                    return state; // 直接返回，避免重复缓存更新
                }
                else
                {
                    throw new ArgumentException($"不支持的IO类型: {portDef.Type}");
                }

                // 更新缓存
                lock (_lockObject)
                {
                    _inputStates[ioNumber] = state;
                }

                return state;
            }, false, $"读取输入IO {ioNumber}");
        }

        /// <summary>
        /// 批量读取所有输入IO状态
        /// </summary>
        /// <returns>输入IO状态字典</returns>
        public async Task<Dictionary<string, bool>> ReadAllInputsAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync<Dictionary<string, bool>>(async () =>
            {
                if (!_isInitialized)
                {
                    throw new InvalidOperationException("IO管理器未初始化");
                }

                var inputStates = new Dictionary<string, bool>();

                foreach (var port in _allInputPorts)
                {
                    try
                    {
                        bool state = false;

                        if (port.Type == IOPortType.Input)
                        {
                            // 普通输入IO
                            var result = csDmc1000.DMC1000.d1000_in_bit(port.BitNumber);
                            state = result == 1;
                            LogHelper.Debug($"批量读取输入IO {port.IONumber} (位号{port.BitNumber}): 原始值={result}, 状态={state}");
                        }
                        else if (port.Type == IOPortType.AxisSpecialInput)
                        {
                            // 专用轴信号
                            var axisState = csDmc1000.DMC1000.d1000_get_axis_status((short)port.AxisNumber);

                            // 根据信号类型解析对应的位
                            switch (port.IONumber.ToUpper())
                            {
                                case "ORG0":
                                case "ORG1":
                                    state = (axisState & 0x04) != 0; // 位2是ORG信号
                                    break;
                                case "PEL0":
                                case "PEL1":
                                    state = (axisState & 0x02) != 0; // 位1是+EL信号
                                    break;
                                case "NEL0":
                                case "NEL1":
                                    state = (axisState & 0x01) != 0; // 位0是-EL信号
                                    break;
                            }
                            LogHelper.Debug($"批量读取专用轴信号 {port.IONumber} (轴{port.AxisNumber}): 原始状态=0x{axisState:X2}, 信号状态={state}");
                        }

                        inputStates[port.IONumber] = state;

                        // 更新缓存
                        lock (_lockObject)
                        {
                            _inputStates[port.IONumber] = state;
                        }
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error($"读取输入IO {port.IONumber} 失败", ex);
                        inputStates[port.IONumber] = false;
                    }
                }

                await Task.CompletedTask; // 消除CS1998警告
                return inputStates;
            }, new Dictionary<string, bool>(), "批量读取所有输入IO");
        }

        /// <summary>
        /// 读取专用轴信号状态（如ORG、PEL、NEL等）
        /// </summary>
        /// <param name="ioNumber">IO编号（如ORG0、ORG1等）</param>
        /// <returns>信号状态</returns>
        public async Task<bool> ReadAxisSpecialInputAsync(string ioNumber)
        {
            return await ExceptionHelper.SafeExecuteAsync<bool>(async () =>
            {
                if (!_isInitialized)
                {
                    throw new InvalidOperationException("IO管理器未初始化");
                }

                var portDef = IOConfiguration.GetIOPortByNumber(ioNumber);
                if (portDef == null || portDef.Type != IOPortType.AxisSpecialInput)
                {
                    throw new ArgumentException($"无效的专用轴信号编号: {ioNumber}");
                }

                // 使用d1000_get_axis_status读取轴专用信号
                var axisState = csDmc1000.DMC1000.d1000_get_axis_status((short)portDef.AxisNumber);

                // 根据信号类型解析对应的位
                bool signalState = false;
                switch (ioNumber.ToUpper())
                {
                    case "ORG0":
                    case "ORG1":
                        // 位2是ORG信号，根据DMC1000B编程手册
                        signalState = (axisState & 0x04) != 0;
                        break;
                    case "PEL0":
                    case "PEL1":
                        // 位1是+EL信号（正限位）
                        signalState = (axisState & 0x02) != 0;
                        break;
                    case "NEL0":
                    case "NEL1":
                        // 位0是-EL信号（负限位）
                        signalState = (axisState & 0x01) != 0;
                        break;
                    default:
                        throw new ArgumentException($"不支持的专用轴信号: {ioNumber}");
                }

                // 更新缓存
                lock (_lockObject)
                {
                    _inputStates[ioNumber] = signalState;
                }

                LogHelper.Debug($"读取专用轴信号 {ioNumber} (轴{portDef.AxisNumber}): 原始状态=0x{axisState:X2}, 信号状态={signalState}");
                await Task.CompletedTask; // 消除CS1998警告
                return signalState;
            }, false, $"读取专用轴信号 {ioNumber}");
        }
        #endregion

        #region IO输出控制方法
        /// <summary>
        /// 设置单个输出IO状态（支持逻辑反转）
        /// </summary>
        /// <param name="ioNumber">IO编号</param>
        /// <param name="logicalState">逻辑状态（业务层使用的状态）</param>
        /// <returns>操作结果</returns>
        public async Task<bool> SetOutputAsync(string ioNumber, bool logicalState)
        {
            return await ExceptionHelper.SafeExecuteAsync<bool>(async () =>
            {
                if (!_isInitialized)
                {
                    throw new InvalidOperationException("IO管理器未初始化");
                }

                var portDef = IOConfiguration.GetIOPortByNumber(ioNumber);
                if (portDef == null || portDef.Type != IOPortType.Output)
                {
                    throw new ArgumentException($"无效的输出IO编号: {ioNumber}");
                }

                // 应用逻辑反转（解决硬件接线反逻辑问题）
                bool invertOutputs = Settings.Settings.Current.Motor.InvertAllOutputs;
                bool physicalState = invertOutputs ? !logicalState : logicalState;

                LogHelper.Debug($"IO输出控制: {ioNumber} 逻辑状态={logicalState}, InvertAllOutputs={invertOutputs}, 物理状态={physicalState}");

                // 调用DMC1000B的d1000_out_bit函数
                var result = csDmc1000.DMC1000.d1000_out_bit(portDef.BitNumber, physicalState ? 1 : 0);

                LogHelper.Debug($"设置输出IO {ioNumber} (位号{portDef.BitNumber}): 逻辑{(logicalState ? "ON" : "OFF")}, 物理{(physicalState ? "ON" : "OFF")}, 返回值={result}");

                if (result == 0) // ERR_NoError
                {
                    // 更新缓存（存储逻辑状态）
                    lock (_lockObject)
                    {
                        _outputStates[ioNumber] = logicalState;
                    }

                    // 触发事件（传递逻辑状态）
                    IOOutputStateChanged?.Invoke(this, new Models.IOOutputStateChangedEventArgs(ioNumber, logicalState));

                    // 异步保存到配置文件（不等待结果，避免阻塞）
                    _ = Task.Run(async () =>
                    {
                        bool saveSuccess = await SaveIOOutputStatesAsync();
                        if (!saveSuccess)
                        {
                            LogHelper.Warning($"IO输出状态保存到配置文件失败");
                        }
                    });

                    LogHelper.Info($"设置输出IO {ioNumber} 成功: 逻辑{(logicalState ? "ON" : "OFF")}, 物理{(physicalState ? "ON" : "OFF")}");
                    return true;
                }
                else
                {
                    LogHelper.Error($"设置输出IO {ioNumber} 失败，错误码: {result}");
                    await Task.CompletedTask; // 消除CS1998警告
                    return false;
                }
            }, false, $"设置输出IO {ioNumber}");
        }

        /// <summary>
        /// 批量设置输出IO状态
        /// </summary>
        /// <param name="ioStates">IO状态字典</param>
        /// <returns>操作结果</returns>
        public async Task<bool> SetOutputsAsync(Dictionary<string, bool> ioStates)
        {
            return await ExceptionHelper.SafeExecuteAsync<bool>(async () =>
            {
                if (!_isInitialized)
                {
                    throw new InvalidOperationException("IO管理器未初始化");
                }

                bool allSuccess = true;
                var changedStates = new Dictionary<string, bool>();

                foreach (var kvp in ioStates)
                {
                    try
                    {
                        bool success = await SetOutputAsync(kvp.Key, kvp.Value);
                        if (success)
                        {
                            changedStates[kvp.Key] = kvp.Value;
                        }
                        else
                        {
                            allSuccess = false;
                        }
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error($"批量设置输出IO {kvp.Key} 失败", ex);
                        allSuccess = false;
                    }
                }

                // 触发批量变化事件
                if (changedStates.Count > 0)
                {
                    BatchIOStateChanged?.Invoke(this, new BatchIOStateChangedEventArgs(changedStates, IOPortType.Output));
                }

                return allSuccess;
            }, false, "批量设置输出IO");
        }

        /// <summary>
        /// 读取单个输出IO状态（支持逻辑反转）
        /// </summary>
        /// <param name="ioNumber">IO编号</param>
        /// <returns>逻辑状态（业务层使用的状态）</returns>
        public async Task<bool> ReadOutputAsync(string ioNumber)
        {
            return await ExceptionHelper.SafeExecuteAsync<bool>(async () =>
            {
                if (!_isInitialized)
                {
                    throw new InvalidOperationException("IO管理器未初始化");
                }

                var portDef = IOConfiguration.GetIOPortByNumber(ioNumber);
                if (portDef == null || portDef.Type != IOPortType.Output)
                {
                    throw new ArgumentException($"无效的输出IO编号: {ioNumber}");
                }

                // 调用DMC1000B的d1000_get_outbit函数读取物理状态
                var result = csDmc1000.DMC1000.d1000_get_outbit((short)portDef.BitNumber);
                bool physicalState = result == 1;

                // 应用逻辑反转（解决硬件接线反逻辑问题）
                bool logicalState = Settings.Settings.Current.Motor.InvertAllOutputs ? !physicalState : physicalState;

                // 更新缓存（存储逻辑状态）
                lock (_lockObject)
                {
                    _outputStates[ioNumber] = logicalState;
                }

                LogHelper.Debug($"读取输出IO {ioNumber} (位号{portDef.BitNumber}): 物理状态={physicalState}, 逻辑状态={logicalState}");
                await Task.CompletedTask; // 消除CS1998警告
                return logicalState;
            }, false, $"读取输出IO {ioNumber}");
        }

        /// <summary>
        /// 批量读取所有输出IO状态
        /// </summary>
        /// <returns>输出IO状态字典</returns>
        public async Task<Dictionary<string, bool>> ReadAllOutputsAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync<Dictionary<string, bool>>(async () =>
            {
                if (!_isInitialized)
                {
                    throw new InvalidOperationException("IO管理器未初始化");
                }

                var outputStates = new Dictionary<string, bool>();

                foreach (var port in _allOutputPorts)
                {
                    try
                    {
                        var result = csDmc1000.DMC1000.d1000_get_outbit((short)port.BitNumber);
                        bool physicalState = result == 1;

                        // 应用逻辑反转（解决硬件接线反逻辑问题）
                        bool logicalState = Settings.Settings.Current.Motor.InvertAllOutputs ? !physicalState : physicalState;

                        outputStates[port.IONumber] = logicalState;

                        // 更新缓存（存储逻辑状态）
                        lock (_lockObject)
                        {
                            _outputStates[port.IONumber] = logicalState;
                        }

                        LogHelper.Debug($"批量读取输出IO {port.IONumber}: 物理状态={physicalState}, 逻辑状态={logicalState}");
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error($"读取输出IO {port.IONumber} 失败", ex);
                        outputStates[port.IONumber] = false;
                    }
                }

                await Task.CompletedTask; // 消除CS1998警告
                return outputStates;
            }, new Dictionary<string, bool>(), "批量读取所有输出IO");
        }

        /// <summary>
        /// 设置所有输出IO到安全状态（程序关闭时调用）
        /// </summary>
        /// <returns>操作结果</returns>
        public async Task<bool> SetAllOutputsToSafeStateAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync<bool>(async () =>
            {
                if (!_isInitialized)
                {
                    LogHelper.Warning("IO管理器未初始化，跳过安全状态设置");
                    return true;
                }

                LogHelper.Info("开始设置所有输出IO到安全状态...");

                var allOutputs = IOConfiguration.GetAllOutputPorts();
                bool safeState = Settings.Settings.Current.Motor.OutputSafeState;
                bool invertOutputs = Settings.Settings.Current.Motor.InvertAllOutputs;

                LogHelper.Info($"安全状态配置: OutputSafeState={safeState}, InvertAllOutputs={invertOutputs}");

                // 根据硬件特性和配置计算正确的安全状态
                // 硬件接线反逻辑：控制卡OFF → 硬件ON，控制卡ON → 硬件OFF
                // 要让硬件关闭，需要控制卡输出ON

                // 计算目标物理状态：要让硬件关闭，物理输出应该为ON
                bool targetPhysicalState = true;

                // 根据逻辑反转配置计算对应的逻辑状态
                bool targetLogicalState;
                if (invertOutputs)
                {
                    // 如果启用逻辑反转：逻辑OFF → 物理ON
                    targetLogicalState = !targetPhysicalState; // false
                }
                else
                {
                    // 如果未启用逻辑反转：逻辑ON → 物理ON
                    targetLogicalState = targetPhysicalState; // true
                }

                LogHelper.Info($"安全状态计算: 目标物理状态={targetPhysicalState}(硬件关闭), 目标逻辑状态={targetLogicalState}, 逻辑反转={invertOutputs}");

                int successCount = 0;
                int totalCount = allOutputs.Count;

                foreach (var port in allOutputs)
                {
                    try
                    {
                        bool result = await SetOutputAsync(port.IONumber, targetLogicalState);
                        if (result)
                        {
                            successCount++;
                            LogHelper.Debug($"IO {port.IONumber} 设置为安全状态: 逻辑{(targetLogicalState ? "ON" : "OFF")}");
                        }
                        else
                        {
                            LogHelper.Warning($"设置IO {port.IONumber} 安全状态失败");
                        }
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error($"设置IO {port.IONumber} 安全状态异常", ex);
                    }
                }

                LogHelper.Info($"输出IO安全状态设置完成: 成功{successCount}/{totalCount}个");
                return successCount == totalCount;

            }, false, "设置所有输出IO到安全状态");
        }
        #endregion

        #region IO状态监控方法
        /// <summary>
        /// 开始IO状态监控
        /// </summary>
        /// <returns>操作结果</returns>
        public async Task<bool> StartMonitoringAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync<bool>(async () =>
            {
                await Task.CompletedTask; // 消除CS1998警告
                lock (_lockObject)
                {
                    if (!_isInitialized)
                    {
                        throw new InvalidOperationException("IO管理器未初始化");
                    }

                    if (_isMonitoring)
                    {
                        LogHelper.Warning("IO状态监控已经启动");
                        return true;
                    }

                    try
                    {
                        _monitoringCancellationTokenSource = new CancellationTokenSource();

                        // 创建并启动专用的IO监控后台线程
                        _monitoringThread = new Thread(() => IOMonitoringThreadWorker(_monitoringCancellationTokenSource.Token))
                        {
                            Name = "IO状态监控线程",
                            IsBackground = true, // 设置为后台线程，程序退出时自动结束
                            Priority = ThreadPriority.AboveNormal // 提高优先级确保及时响应IO变化
                        };

                        _monitoringThread.Start();
                        _isMonitoring = true;
                        LogHelper.Info($"IO状态监控线程启动成功，监控频率: {1000.0 / MONITORING_INTERVAL_MS:F1}Hz");
                        return true;
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error("启动IO状态监控失败", ex);
                        return false;
                    }
                }
            }, false, "启动IO状态监控");
        }

        /// <summary>
        /// 停止IO状态监控
        /// </summary>
        /// <returns>操作结果</returns>
        public async Task<bool> StopMonitoringAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync<bool>(async () =>
            {
                await Task.CompletedTask; // 消除CS1998警告
                lock (_lockObject)
                {
                    if (!_isMonitoring)
                    {
                        LogHelper.Warning("IO状态监控未启动");
                        return true;
                    }

                    try
                    {
                        // 取消监控线程
                        _monitoringCancellationTokenSource?.Cancel();

                        // 等待监控线程结束（最多等待2秒）
                        if (_monitoringThread != null && _monitoringThread.IsAlive)
                        {
                            if (!_monitoringThread.Join(2000))
                            {
                                LogHelper.Warning("IO监控线程未能在2秒内正常结束");
                            }
                        }

                        _monitoringCancellationTokenSource?.Dispose();
                        _monitoringCancellationTokenSource = null;
                        _monitoringThread = null;

                        _isMonitoring = false;
                        LogHelper.Info("IO状态监控线程停止成功");
                        return true;
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error("停止IO状态监控失败", ex);
                        return false;
                    }
                }
            }, false, "停止IO状态监控");
        }

        /// <summary>
        /// IO监控线程工作方法 - 专用后台线程进行IO状态扫描
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        private void IOMonitoringThreadWorker(CancellationToken cancellationToken)
        {
            LogHelper.Info("IO监控线程开始工作");

            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    try
                    {
                        // 执行IO状态监控
                        MonitorIOStatesSync();

                        // 等待指定的监控间隔，同时响应取消请求
                        if (cancellationToken.WaitHandle.WaitOne(MONITORING_INTERVAL_MS))
                        {
                            // 收到取消信号，退出循环
                            break;
                        }
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error("IO监控线程执行异常", ex);

                        // 发生异常时稍作延迟，避免快速重试
                        if (!cancellationToken.WaitHandle.WaitOne(1000))
                        {
                            continue;
                        }
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("IO监控线程异常退出", ex);
            }
            finally
            {
                LogHelper.Info("IO监控线程结束工作");
            }
        }

        /// <summary>
        /// 监控IO状态变化 - 同步版本，用于专用线程
        /// </summary>
        private void MonitorIOStatesSync()
        {
            try
            {
                // 读取当前所有输入IO状态
                var currentInputStates = ReadAllInputIOSync();
                if (currentInputStates == null || currentInputStates.Count == 0)
                {
                    return;
                }

                // 检查状态变化
                var changedInputs = new Dictionary<string, bool>();

                lock (_lockObject)
                {
                    foreach (var kvp in currentInputStates)
                    {
                        // 检查是否有状态变化
                        if (!_previousInputStates.ContainsKey(kvp.Key) ||
                            _previousInputStates[kvp.Key] != kvp.Value)
                        {
                            changedInputs[kvp.Key] = kvp.Value;

                            // 立即触发单个输入状态变化事件
                            IOInputStateChanged?.Invoke(this, new Models.IOInputStateChangedEventArgs(kvp.Key, kvp.Value));

                            LogHelper.Debug($"IO输入状态变化: {kvp.Key} = {kvp.Value}");
                        }

                        _previousInputStates[kvp.Key] = kvp.Value;
                    }
                }

                // 如果有批量变化，触发批量状态变化事件
                if (changedInputs.Count > 0)
                {
                    BatchIOStateChanged?.Invoke(this, new BatchIOStateChangedEventArgs(changedInputs, IOPortType.Input));
                    LogHelper.Debug($"批量IO状态变化，共{changedInputs.Count}个输入发生变化");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("监控IO状态失败", ex);
            }
        }

        /// <summary>
        /// 同步读取所有输入IO状态 - 用于专用监控线程
        /// </summary>
        /// <returns>输入IO状态字典</returns>
        private Dictionary<string, bool> ReadAllInputIOSync()
        {
            try
            {
                var inputStates = new Dictionary<string, bool>();

                // 读取所有输入IO状态
                foreach (var inputPort in _allInputPorts)
                {
                    try
                    {
                        bool state = ReadInputBitDirect(inputPort.BitNumber);
                        inputStates[inputPort.IONumber] = state;

                        // 更新内部状态缓存
                        lock (_lockObject)
                        {
                            _inputStates[inputPort.IONumber] = state;
                        }
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Warning($"读取输入IO {inputPort.IONumber} 状态失败: {ex.Message}");
                        // 继续读取其他IO，不因单个IO失败而中断
                    }
                }

                return inputStates;
            }
            catch (Exception ex)
            {
                LogHelper.Error("同步读取所有输入IO状态失败", ex);
                return new Dictionary<string, bool>();
            }
        }

        /// <summary>
        /// 监控IO状态变化 - 异步版本（保留兼容性）
        /// </summary>
        private async Task MonitorIOStatesAsync()
        {
            try
            {
                if (!_isInitialized || !_isMonitoring)
                {
                    return;
                }

                // 读取所有输入IO状态
                var currentInputStates = await ReadAllInputsAsync();
                var changedInputs = new Dictionary<string, bool>();

                // 检查输入IO状态变化
                lock (_lockObject)
                {
                    foreach (var kvp in currentInputStates)
                    {
                        if (_previousInputStates.ContainsKey(kvp.Key) &&
                            _previousInputStates[kvp.Key] != kvp.Value)
                        {
                            changedInputs[kvp.Key] = kvp.Value;

                            // 触发单个输入状态变化事件
                            IOInputStateChanged?.Invoke(this, new Models.IOInputStateChangedEventArgs(kvp.Key, kvp.Value));
                        }

                        _previousInputStates[kvp.Key] = kvp.Value;
                    }
                }

                // 触发批量输入状态变化事件
                if (changedInputs.Count > 0)
                {
                    BatchIOStateChanged?.Invoke(this, new BatchIOStateChangedEventArgs(changedInputs, IOPortType.Input));
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("监控IO状态失败", ex);
            }
        }

        /// <summary>
        /// 读取所有IO状态
        /// </summary>
        private async Task ReadAllIOStatesAsync()
        {
            try
            {
                // 读取所有输入IO状态
                await ReadAllInputsAsync();

                // 读取所有输出IO状态
                await ReadAllOutputsAsync();
            }
            catch (Exception ex)
            {
                LogHelper.Error("读取所有IO状态失败", ex);
            }
        }
        #endregion

        #region 公共属性和方法
        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// 是否正在监控
        /// </summary>
        public bool IsMonitoring => _isMonitoring;

        /// <summary>
        /// 获取输入IO状态
        /// </summary>
        /// <param name="ioNumber">IO编号</param>
        /// <returns>IO状态</returns>
        public bool GetInputState(string ioNumber)
        {
            lock (_lockObject)
            {
                return _inputStates.ContainsKey(ioNumber) ? _inputStates[ioNumber] : false;
            }
        }

        /// <summary>
        /// 获取输出IO状态
        /// </summary>
        /// <param name="ioNumber">IO编号</param>
        /// <returns>IO状态</returns>
        public bool GetOutputState(string ioNumber)
        {
            lock (_lockObject)
            {
                return _outputStates.ContainsKey(ioNumber) ? _outputStates[ioNumber] : false;
            }
        }

        /// <summary>
        /// 获取所有输入IO状态
        /// </summary>
        /// <returns>输入IO状态字典</returns>
        public Dictionary<string, bool> GetAllInputStates()
        {
            lock (_lockObject)
            {
                return new Dictionary<string, bool>(_inputStates);
            }
        }

        /// <summary>
        /// 获取所有输出IO状态
        /// </summary>
        /// <returns>输出IO状态字典</returns>
        public Dictionary<string, bool> GetAllOutputStates()
        {
            lock (_lockObject)
            {
                return new Dictionary<string, bool>(_outputStates);
            }
        }

        /// <summary>
        /// 紧急停止所有输出
        /// </summary>
        /// <returns>操作结果</returns>
        public async Task<bool> EmergencyStopAllOutputsAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync<bool>(async () =>
            {
                LogHelper.Warning("执行紧急停止所有输出");

                var allOffStates = new Dictionary<string, bool>();
                foreach (var port in _allOutputPorts)
                {
                    allOffStates[port.IONumber] = false;
                }

                bool result = await SetOutputsAsync(allOffStates);

                if (result)
                {
                    LogHelper.Info("紧急停止所有输出完成");
                }
                else
                {
                    LogHelper.Error("紧急停止所有输出失败");
                }

                return result;
            }, false, "紧急停止所有输出");
        }
        #endregion

        #region 配置管理
        /// <summary>
        /// 从系统配置加载IO输出状态
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> LoadIOOutputStatesAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    var ioSettings = Settings.Settings.Current.IO;
                    if (ioSettings?.OutputStates != null)
                    {
                        lock (_lockObject)
                        {
                            foreach (var kvp in ioSettings.OutputStates)
                            {
                                _outputStates[kvp.Key] = kvp.Value;
                            }
                        }
                        LogHelper.Info($"从配置文件加载IO输出状态成功，共{ioSettings.OutputStates.Count}个状态");
                        return true;
                    }
                    else
                    {
                        LogHelper.Info("配置文件中未找到IO输出状态配置");
                        return true; // 没有配置也算成功
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Error("从配置文件加载IO输出状态失败", ex);
                    return false;
                }
            });
        }

        /// <summary>
        /// 保存IO输出状态到系统配置
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> SaveIOOutputStatesAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    var ioSettings = Settings.Settings.Current.IO;
                    if (ioSettings == null)
                    {
                        LogHelper.Warning("系统配置中IO输出配置节点不存在，无法保存IO输出状态");
                        return false;
                    }

                    // 复制当前输出状态
                    Dictionary<string, bool> currentStates;
                    lock (_lockObject)
                    {
                        currentStates = new Dictionary<string, bool>(_outputStates);
                    }

                    // 保存到配置
                    ioSettings.OutputStates = currentStates;

                    // 保存配置文件
                    Settings.Settings.Save();
                    bool success = true;
                    if (success)
                    {
                        LogHelper.Debug($"IO输出状态保存成功，共{currentStates.Count}个状态");
                    }
                    else
                    {
                        LogHelper.Error("IO输出状态保存失败");
                    }
                    return success;
                }
                catch (Exception ex)
                {
                    LogHelper.Error("保存IO输出状态失败", ex);
                    return false;
                }
            });
        }

        /// <summary>
        /// 恢复持久化的IO输出状态
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> RestorePersistentIOOutputStatesAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (!_isInitialized)
                {
                    LogHelper.Warning("IO管理器未初始化，无法恢复IO输出状态");
                    return false;
                }

                // 先加载配置
                bool loadSuccess = await LoadIOOutputStatesAsync();
                if (!loadSuccess)
                {
                    LogHelper.Warning("加载IO输出状态配置失败");
                    return false;
                }

                // 持久化IO输出功能暂时简化
                LogHelper.Info("持久化IO输出功能暂时简化，跳过恢复");
                return true;

            }, false, "恢复持久化IO输出状态");
        }
        #endregion
    }
}
