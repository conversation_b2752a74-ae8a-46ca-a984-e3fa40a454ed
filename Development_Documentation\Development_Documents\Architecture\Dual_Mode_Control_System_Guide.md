# 双模式控制系统开发指南

## 概述

本文档详细介绍了HR2项目中新实现的双模式控制系统，包括架构设计、使用方法和扩展指南。

## 系统架构

### 核心组件

1. **DMC1000BCardManager** - 统一控制卡管理器
2. **SystemModeManager** - 系统模式管理器
3. **DMC1000BIOManager** - IO管理器（重构版）
4. **DMC1000BMotorManager** - 电机管理器（重构版）

### 架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    双模式控制系统                              │
├─────────────────────────────────────────────────────────────┤
│  MainForm (UI层)                                            │
│  ├── 启动按钮 → 切换到自动模式                                │
│  ├── 停止按钮 → 切换到手动模式                                │
│  ├── 暂停按钮 → 暂停自动化流程                                │
│  └── 复位按钮 → 系统复位                                     │
├─────────────────────────────────────────────────────────────┤
│  SystemModeManager (模式管理层)                              │
│  ├── Manual Mode (手动模式)                                  │
│  ├── Automatic Mode (自动模式)                               │
│  ├── 模式切换逻辑                                            │
│  └── 自动化流程控制                                          │
├─────────────────────────────────────────────────────────────┤
│  业务管理层                                                  │
│  ├── DMC1000BIOManager (IO管理)                             │
│  ├── DMC1000BMotorManager (电机管理)                        │
│  ├── ScannerManager (扫描管理)                               │
│  └── VisionManager (视觉管理)                                │
├─────────────────────────────────────────────────────────────┤
│  DMC1000BCardManager (硬件抽象层)                            │
│  ├── 控制卡生命周期管理                                      │
│  ├── 引用计数机制                                            │
│  └── 线程安全保护                                            │
├─────────────────────────────────────────────────────────────┤
│  DMC1000B控制卡 (硬件层)                                     │
└─────────────────────────────────────────────────────────────┘
```

## 双模式设计

### 手动模式 (Manual Mode)

**特点**:
- 系统默认模式
- 用户可以手动控制IO和电机
- 适合调试、测试和单步操作
- 所有控制按钮可用（除暂停按钮）

**使用场景**:
- 设备调试和测试
- 手动操作和验证
- 故障排查和维护
- 参数设置和校准

### 自动模式 (Automatic Mode)

**特点**:
- 执行预定义的自动化业务流程
- 系统自动控制所有设备
- 用户只能监控和干预
- 启动按钮禁用，暂停和停止按钮可用

**使用场景**:
- 正常生产运行
- 批量处理任务
- 无人值守操作
- 标准化作业流程

## 使用方法

### 基本操作流程

1. **系统启动**
   ```
   程序启动 → 初始化各管理器 → 进入手动模式 → 等待用户操作
   ```

2. **启动自动化**
   ```
   手动模式 → 点击启动按钮 → 系统检查 → 切换到自动模式 → 执行自动化流程
   ```

3. **停止自动化**
   ```
   自动模式 → 点击停止按钮 → 停止自动化流程 → 切换到手动模式
   ```

### 代码示例

#### 1. 获取当前系统模式
```csharp
var currentMode = SystemModeManager.Instance.CurrentMode;
if (currentMode == SystemMode.Manual)
{
    // 手动模式操作
    LogHelper.Info("系统处于手动模式");
}
else
{
    // 自动模式操作
    LogHelper.Info("系统处于自动模式");
}
```

#### 2. 订阅模式变化事件
```csharp
SystemModeManager.Instance.ModeChanged += (sender, e) =>
{
    LogHelper.Info($"系统模式从 {e.PreviousMode} 切换到 {e.CurrentMode}");
    // 根据模式变化更新UI或执行相应逻辑
};
```

#### 3. 手动切换模式
```csharp
// 切换到自动模式
bool result = await SystemModeManager.Instance.SwitchToAutomaticModeAsync();
if (result)
{
    LogHelper.Info("成功切换到自动模式");
}

// 切换到手动模式
bool result = await SystemModeManager.Instance.SwitchToManualModeAsync();
if (result)
{
    LogHelper.Info("成功切换到手动模式");
}
```

## 自动化流程扩展指南

### 1. 自定义自动化工作流程

在`SystemModeManager.cs`中找到`ExecuteAutomationWorkflowAsync`方法，这是自动化流程的核心实现：

```csharp
private async Task ExecuteAutomationWorkflowAsync(CancellationToken cancellationToken)
{
    try
    {
        LogHelper.Info("开始执行自动化工作流程...");

        // 步骤1：系统初始化检查
        await CheckSystemInitializationAsync(cancellationToken);
        
        // 步骤2：扫描条码
        await ExecuteBarcodeScanning(cancellationToken);
        
        // 步骤3：视觉检测
        await ExecuteVisionInspection(cancellationToken);
        
        // 步骤4：电机运动控制
        await ExecuteMotorMovement(cancellationToken);
        
        // 步骤5：数据处理和结果输出
        await ProcessDataAndOutput(cancellationToken);

        // 触发完成事件
        AutomationCompleted?.Invoke(this, new AutomationCompletedEventArgs(true, "自动化流程执行完成"));
    }
    catch (OperationCanceledException)
    {
        LogHelper.Info("自动化工作流程被取消");
        AutomationCompleted?.Invoke(this, new AutomationCompletedEventArgs(false, "自动化流程被取消"));
    }
    catch (Exception ex)
    {
        LogHelper.Error("自动化工作流程执行异常", ex);
        AutomationError?.Invoke(this, new AutomationErrorEventArgs(ex));
    }
}
```

### 2. 添加新的自动化步骤

#### 步骤1：定义自动化步骤方法
```csharp
/// <summary>
/// 执行条码扫描步骤
/// </summary>
private async Task ExecuteBarcodeScanning(CancellationToken cancellationToken)
{
    LogHelper.Info("执行自动化步骤：条码扫描");
    
    try
    {
        // 检查扫描枪是否就绪
        if (!ScannerManager.Instance.IsInitialized)
        {
            throw new InvalidOperationException("扫描枪未初始化");
        }
        
        // 等待扫描结果
        var scanResult = await ScannerManager.Instance.WaitForScanAsync(cancellationToken);
        
        if (string.IsNullOrEmpty(scanResult))
        {
            throw new InvalidOperationException("扫描失败或超时");
        }
        
        LogHelper.Info($"扫描成功，条码: {scanResult}");
        
        // 验证条码格式
        if (!ValidateBarcode(scanResult))
        {
            throw new InvalidOperationException($"条码格式无效: {scanResult}");
        }
        
        // 保存扫描结果供后续步骤使用
        // 可以使用类成员变量或参数传递
        
    }
    catch (OperationCanceledException)
    {
        throw; // 重新抛出取消异常
    }
    catch (Exception ex)
    {
        LogHelper.Error("条码扫描步骤执行失败", ex);
        throw new InvalidOperationException($"条码扫描失败: {ex.Message}", ex);
    }
}
```

#### 步骤2：添加步骤验证方法
```csharp
/// <summary>
/// 验证条码格式
/// </summary>
private bool ValidateBarcode(string barcode)
{
    // 实现具体的条码验证逻辑
    if (string.IsNullOrWhiteSpace(barcode))
        return false;
        
    // 例如：检查长度、格式、校验位等
    if (barcode.Length < 8 || barcode.Length > 20)
        return false;
        
    // 可以添加更多验证规则
    return true;
}
```

### 3. 添加可配置的自动化参数

#### 步骤1：定义配置类
```csharp
public class AutomationConfig
{
    public int ScanTimeout { get; set; } = 30000; // 扫描超时时间(ms)
    public int VisionTimeout { get; set; } = 10000; // 视觉检测超时时间(ms)
    public double MotorSpeed { get; set; } = 50.0; // 电机运行速度
    public int RetryCount { get; set; } = 3; // 重试次数
    public bool EnableQualityCheck { get; set; } = true; // 是否启用质量检查
}
```

#### 步骤2：在SystemModeManager中使用配置
```csharp
public class SystemModeManager
{
    private AutomationConfig _config;
    
    private SystemModeManager() 
    {
        _currentMode = SystemMode.Manual;
        _config = LoadAutomationConfig(); // 从配置文件加载
    }
    
    private AutomationConfig LoadAutomationConfig()
    {
        // 从配置文件、数据库或其他来源加载配置
        return new AutomationConfig
        {
            ScanTimeout = ConfigHelper.GetAppSettingInt("ScanTimeout", 30000),
            VisionTimeout = ConfigHelper.GetAppSettingInt("VisionTimeout", 10000),
            MotorSpeed = ConfigHelper.GetAppSettingDouble("MotorSpeed", 50.0),
            RetryCount = ConfigHelper.GetAppSettingInt("RetryCount", 3),
            EnableQualityCheck = ConfigHelper.GetAppSettingBool("EnableQualityCheck", true)
        };
    }
}
```

### 4. 实现步骤间的数据传递

#### 使用上下文对象
```csharp
public class AutomationContext
{
    public string ScannedBarcode { get; set; }
    public VisionResult VisionResult { get; set; }
    public Dictionary<string, object> StepData { get; set; } = new Dictionary<string, object>();
    public DateTime StartTime { get; set; }
    public int CurrentStep { get; set; }
}

private async Task ExecuteAutomationWorkflowAsync(CancellationToken cancellationToken)
{
    var context = new AutomationContext
    {
        StartTime = DateTime.Now,
        CurrentStep = 0
    };
    
    try
    {
        // 步骤1：扫描条码
        context.CurrentStep = 1;
        await ExecuteBarcodeScanning(context, cancellationToken);
        
        // 步骤2：视觉检测
        context.CurrentStep = 2;
        await ExecuteVisionInspection(context, cancellationToken);
        
        // 其他步骤...
    }
    catch (Exception ex)
    {
        LogHelper.Error($"自动化流程在步骤{context.CurrentStep}失败", ex);
        throw;
    }
}
```

### 5. 添加进度报告和状态更新

```csharp
/// <summary>
/// 自动化进度事件参数
/// </summary>
public class AutomationProgressEventArgs : EventArgs
{
    public int CurrentStep { get; }
    public int TotalSteps { get; }
    public string StepDescription { get; }
    public double ProgressPercentage { get; }
    
    public AutomationProgressEventArgs(int currentStep, int totalSteps, string stepDescription)
    {
        CurrentStep = currentStep;
        TotalSteps = totalSteps;
        StepDescription = stepDescription;
        ProgressPercentage = (double)currentStep / totalSteps * 100;
    }
}

// 在SystemModeManager中添加进度事件
public event EventHandler<AutomationProgressEventArgs> AutomationProgress;

// 在自动化流程中报告进度
private void ReportProgress(int currentStep, int totalSteps, string description)
{
    AutomationProgress?.Invoke(this, new AutomationProgressEventArgs(currentStep, totalSteps, description));
}
```

## 最佳实践

### 1. 错误处理
- 每个自动化步骤都应该有适当的异常处理
- 使用特定的异常类型来区分不同类型的错误
- 提供详细的错误信息和恢复建议

### 2. 日志记录
- 记录每个步骤的开始和结束
- 记录关键参数和中间结果
- 使用不同的日志级别（Info、Warning、Error）

### 3. 取消支持
- 所有长时间运行的操作都应该支持取消
- 正确处理OperationCanceledException
- 在适当的位置检查取消令牌

### 4. 资源管理
- 确保所有资源在使用后正确释放
- 使用using语句或try-finally块
- 避免资源泄漏

### 5. 线程安全
- 自动化流程可能在后台线程中运行
- UI更新必须在UI线程中进行
- 使用适当的同步机制

## API参考

### DMC1000BCardManager

#### 主要方法
```csharp
// 初始化控制卡
Task<bool> InitializeCardAsync(string managerName = "Unknown")

// 释放控制卡引用
Task<bool> ReleaseCardAsync(string managerName = "Unknown")

// 强制释放控制卡
Task<bool> ForceReleaseCardAsync()

// 获取控制卡状态
(bool IsInitialized, short CardCount, int ReferenceCount) GetCardStatus()

// 检查控制卡是否可用
bool IsCardAvailable()
```

#### 属性
```csharp
bool IsInitialized { get; }      // 控制卡是否已初始化
short CardCount { get; }         // 检测到的控制卡数量
int ReferenceCount { get; }      // 当前引用计数
```

### SystemModeManager

#### 主要方法
```csharp
// 切换到手动模式
Task<bool> SwitchToManualModeAsync()

// 切换到自动模式
Task<bool> SwitchToAutomaticModeAsync()

// 获取模式状态
(SystemMode CurrentMode, bool IsTransitioning, bool IsAutomationRunning) GetModeStatus()
```

#### 属性
```csharp
SystemMode CurrentMode { get; }     // 当前系统运行模式
bool IsTransitioning { get; }       // 是否正在模式切换中
bool IsManualMode { get; }          // 是否为手动模式
bool IsAutomaticMode { get; }       // 是否为自动模式
bool IsAutomationRunning { get; }   // 自动化流程是否正在运行
```

#### 事件
```csharp
event EventHandler<SystemModeChangedEventArgs> ModeChanged;
event EventHandler<AutomationStartedEventArgs> AutomationStarted;
event EventHandler<AutomationCompletedEventArgs> AutomationCompleted;
event EventHandler<AutomationErrorEventArgs> AutomationError;
```

## 故障排除

### 常见问题

1. **控制卡初始化失败**
   - 检查硬件连接
   - 验证驱动程序安装
   - 确认控制卡电源

2. **模式切换失败**
   - 检查系统准备状态
   - 查看日志错误信息
   - 验证管理器初始化状态

3. **自动化流程异常**
   - 检查各管理器状态
   - 验证硬件设备连接
   - 查看详细错误日志

### 调试技巧

1. **启用详细日志**
   ```csharp
   LogHelper.SetLogLevel(LogLevel.Debug);
   ```

2. **检查管理器状态**
   ```csharp
   var cardStatus = DMC1000BCardManager.Instance.GetCardStatus();
   LogHelper.Info($"控制卡状态: 初始化={cardStatus.IsInitialized}, 引用计数={cardStatus.ReferenceCount}");
   ```

3. **监控模式变化**
   ```csharp
   SystemModeManager.Instance.ModeChanged += (s, e) =>
   {
       LogHelper.Info($"模式变化: {e.PreviousMode} -> {e.CurrentMode}");
   };
   ```

---
**文档版本**: 1.0
**最后更新**: 2025-09-19
**维护人员**: Augment Agent
