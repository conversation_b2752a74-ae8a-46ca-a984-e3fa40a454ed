using System;
using System.Threading;
using System.Threading.Tasks;
using MyHMI.Helpers;

namespace MyHMI.Managers
{
    /// <summary>
    /// DMC1000B控制卡统一管理器
    /// 负责控制卡的生命周期管理，程序启动时初始化一次，程序退出时释放一次
    /// 实现单例模式，确保全局只有一个控制卡管理实例
    /// </summary>
    public class DMC1000BCardManager
    {
        #region 单例模式
        private static readonly Lazy<DMC1000BCardManager> _instance = new Lazy<DMC1000BCardManager>(() => new DMC1000BCardManager());
        public static DMC1000BCardManager Instance => _instance.Value;
        private DMC1000BCardManager() { }
        #endregion

        #region 私有字段
        private static bool _cardInitialized = false;
        private static readonly object _cardInitLock = new object();
        private static short _cardCount = 0;
        #endregion

        #region 公共属性
        /// <summary>
        /// 控制卡是否已初始化
        /// </summary>
        public bool IsInitialized => _cardInitialized;

        /// <summary>
        /// 检测到的控制卡数量
        /// </summary>
        public short CardCount => _cardCount;
        #endregion

        #region 控制卡生命周期管理
        /// <summary>
        /// 初始化控制卡（程序启动时调用一次）
        /// 只能初始化一次，重复调用会返回当前状态
        /// </summary>
        /// <returns>初始化结果</returns>
        public async Task<bool> InitializeCardAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync<bool>(async () =>
            {
                await Task.CompletedTask; // 消除CS1998警告
                lock (_cardInitLock)
                {
                    // 如果已经初始化，直接返回成功
                    if (_cardInitialized)
                    {
                        LogHelper.Info("DMC1000B控制卡已经初始化，跳过重复初始化");
                        return true;
                    }

                    try
                    {
                        LogHelper.Info("开始初始化DMC1000B控制卡...");

                        // 调用DMC1000B初始化函数
                        _cardCount = csDmc1000.DMC1000.d1000_board_init();

                        if (_cardCount <= 0)
                        {
                            LogHelper.Error($"DMC1000B控制卡初始化失败：未检测到控制卡，返回值: {_cardCount}");
                            return false;
                        }

                        _cardInitialized = true;
                        LogHelper.Info($"DMC1000B控制卡初始化成功，检测到 {_cardCount} 张控制卡");
                        return true;
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error("DMC1000B控制卡初始化异常", ex);
                        return false;
                    }
                }
            }, false, "初始化DMC1000B控制卡");
        }

        /// <summary>
        /// 释放控制卡资源（程序退出时调用一次）
        /// 只能释放一次，重复调用会返回当前状态
        /// </summary>
        /// <returns>释放结果</returns>
        public async Task<bool> ReleaseCardAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync<bool>(async () =>
            {
                await Task.CompletedTask; // 消除CS1998警告
                lock (_cardInitLock)
                {
                    // 如果未初始化，直接返回成功
                    if (!_cardInitialized)
                    {
                        LogHelper.Info("DMC1000B控制卡未初始化，无需释放");
                        return true;
                    }

                    try
                    {
                        LogHelper.Info("开始释放DMC1000B控制卡资源...");

                        var closeResult = csDmc1000.DMC1000.d1000_board_close();

                        if (closeResult == 0) // ERR_NoError
                        {
                            LogHelper.Info("DMC1000B控制卡资源释放成功");
                        }
                        else
                        {
                            LogHelper.Warning($"DMC1000B控制卡资源释放警告，错误码: {closeResult}");
                        }

                        _cardInitialized = false;
                        _cardCount = 0;
                        return true;
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error("DMC1000B控制卡资源释放异常", ex);
                        // 即使释放失败，也要重置状态
                        _cardInitialized = false;
                        _cardCount = 0;
                        return false;
                    }
                }
            }, false, "释放DMC1000B控制卡");
        }


        #endregion

        #region 控制卡状态查询
        /// <summary>
        /// 获取控制卡状态信息
        /// </summary>
        /// <returns>状态信息</returns>
        public (bool IsInitialized, short CardCount) GetCardStatus()
        {
            lock (_cardInitLock)
            {
                return (_cardInitialized, _cardCount);
            }
        }

        /// <summary>
        /// 检查控制卡是否可用
        /// </summary>
        /// <returns>是否可用</returns>
        public bool IsCardAvailable()
        {
            lock (_cardInitLock)
            {
                return _cardInitialized && _cardCount > 0;
            }
        }
        #endregion
    }
}
