using System;
using System.Threading.Tasks;
using MyHMI.Helpers;
using MyHMI.Settings;

namespace MyHMI.Testing
{
    /// <summary>
    /// IO配置测试类 - 用于验证IO逻辑反转配置
    /// </summary>
    public static class IOConfigTest
    {
        /// <summary>
        /// 测试IO配置值
        /// </summary>
        public static void TestIOConfiguration()
        {
            try
            {
                LogHelper.Info("=== IO配置测试开始 ===");
                
                // 测试配置值读取
                bool invertOutputs = Settings.Settings.Current.Motor.InvertAllOutputs;
                bool outputSafeState = Settings.Settings.Current.Motor.OutputSafeState;
                
                LogHelper.Info($"当前配置值:");
                LogHelper.Info($"  InvertAllOutputs = {invertOutputs}");
                LogHelper.Info($"  OutputSafeState = {outputSafeState}");
                
                // 测试逻辑反转计算
                bool logicalState = false; // 安全状态通常是OFF
                bool physicalState = invertOutputs ? !logicalState : logicalState;
                
                LogHelper.Info($"逻辑反转测试:");
                LogHelper.Info($"  逻辑状态 = {logicalState}");
                LogHelper.Info($"  物理状态 = {physicalState}");
                LogHelper.Info($"  预期结果: 如果硬件反逻辑，物理状态应该是 {!logicalState}");
                
                // 验证配置文件路径
                string settingsPath = Settings.Settings.GetSettingsFilePath();
                LogHelper.Info($"配置文件路径: {settingsPath}");
                
                LogHelper.Info("=== IO配置测试完成 ===");
            }
            catch (Exception ex)
            {
                LogHelper.Error("IO配置测试失败", ex);
            }
        }
        
        /// <summary>
        /// 测试安全状态逻辑
        /// </summary>
        public static void TestSafeStateLogic()
        {
            try
            {
                LogHelper.Info("=== 安全状态逻辑测试开始 ===");
                
                bool invertOutputs = Settings.Settings.Current.Motor.InvertAllOutputs;
                bool outputSafeState = Settings.Settings.Current.Motor.OutputSafeState;
                
                LogHelper.Info($"硬件特性分析:");
                LogHelper.Info($"  硬件接线反逻辑: 控制卡OFF → 硬件ON, 控制卡ON → 硬件OFF");
                LogHelper.Info($"  要让硬件关闭，需要控制卡输出ON");
                
                LogHelper.Info($"当前配置分析:");
                LogHelper.Info($"  OutputSafeState = {outputSafeState} (程序关闭时的逻辑状态)");
                LogHelper.Info($"  InvertAllOutputs = {invertOutputs} (是否启用逻辑反转)");
                
                // 计算实际的物理输出
                bool physicalState = invertOutputs ? !outputSafeState : outputSafeState;
                LogHelper.Info($"  计算结果: 逻辑{(outputSafeState ? "ON" : "OFF")} → 物理{(physicalState ? "ON" : "OFF")}");
                
                // 分析硬件最终状态
                string hardwareState = "未知";
                if (physicalState) // 控制卡输出ON
                {
                    hardwareState = "硬件关闭 (安全)";
                }
                else // 控制卡输出OFF
                {
                    hardwareState = "硬件激活 (危险!)";
                }
                
                LogHelper.Info($"  硬件最终状态: {hardwareState}");
                
                // 给出建议
                if (!physicalState)
                {
                    LogHelper.Warning("警告: 当前配置会导致程序关闭时硬件被激活!");
                    LogHelper.Info("建议修复方案:");
                    if (invertOutputs)
                    {
                        LogHelper.Info("  方案1: 将 OutputSafeState 设置为 true");
                    }
                    else
                    {
                        LogHelper.Info("  方案1: 将 InvertAllOutputs 设置为 true");
                        LogHelper.Info("  方案2: 将 OutputSafeState 设置为 true");
                    }
                }
                else
                {
                    LogHelper.Info("配置正确: 程序关闭时硬件将处于安全状态");
                }
                
                LogHelper.Info("=== 安全状态逻辑测试完成 ===");
            }
            catch (Exception ex)
            {
                LogHelper.Error("安全状态逻辑测试失败", ex);
            }
        }
    }
}
