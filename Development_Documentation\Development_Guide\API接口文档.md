# MyHMI 上位机控制系统 - API接口文档

## 概述

本文档详细说明了 MyHMI 系统中各 Manager 类的主要接口和使用方法。所有 Manager 类都采用单例模式，提供异步接口，并支持事件驱动通信。

## 通用接口规范

### 基础接口模式
```csharp
public interface IManager
{
    bool IsInitialized { get; }
    Task<bool> InitializeAsync();
    Task DisposeAsync();
}
```

### 异步操作模式
```csharp
// 所有异步操作都返回 Task<T>
public async Task<bool> OperationAsync(parameters...)
{
    return await ExceptionHelper.SafeExecuteAsync(async () =>
    {
        // 具体实现
    }, false, "操作名称");
}
```

## IOManager - IO控制管理器

### 类概述
负责数字 IO 的读写控制，支持雷赛 DMC1000 运动控制卡。

### 主要接口

#### 初始化和释放
```csharp
// 异步初始化 IO 管理器
Task<bool> InitializeAsync()

// 释放资源
Task DisposeAsync()

// 检查初始化状态
bool IsInitialized { get; }
```

#### IO 读写操作
```csharp
// 读取输入状态
Task<bool> ReadInputAsync(int channel)

// 设置输出状态
Task<bool> SetOutputAsync(int channel, bool state)

// 读取输出状态
Task<bool> ReadOutputAsync(int channel)

// 批量读取所有输入
Task<bool[]> ReadAllInputsAsync()

// 批量读取所有输出
Task<bool[]> ReadAllOutputsAsync()
```

#### 使用示例
```csharp
// 初始化
var ioManager = IOManager.Instance;
await ioManager.InitializeAsync();

// 设置输出
await ioManager.SetOutputAsync(0, true);  // 设置输出通道0为高电平

// 读取输入
bool inputState = await ioManager.ReadInputAsync(1);  // 读取输入通道1状态

// 批量读取
bool[] allInputs = await ioManager.ReadAllInputsAsync();
```

#### 事件接口
```csharp
// IO状态变化事件
event EventHandler<IOStateChangedEventArgs> IOStateChanged;

// 事件参数
public class IOStateChangedEventArgs : EventArgs
{
    public int Channel { get; }      // 通道号
    public bool IsInput { get; }     // 是否为输入通道
    public bool State { get; }       // 当前状态
}
```

## MotorManager - 电机控制管理器

### 类概述
负责闭环步进电机的控制，支持多轴运动控制。

### 主要接口

#### 电机控制
```csharp
// 移动到指定位置
Task<bool> MoveToAsync(int axisId, double position, double speed)

// 相对移动
Task<bool> MoveRelativeAsync(int axisId, double distance, double speed)

// 连续运动
Task<bool> StartContinuousMoveAsync(int axisId, double speed, bool direction)

// 停止电机
Task<bool> StopMotorAsync(int axisId)

// 停止所有电机
Task<bool> StopAllMotorsAsync()

// 回零操作
Task<bool> HomeAsync(int axisId)
```

#### 状态查询
```csharp
// 获取电机状态
Task<MotorStatus> GetMotorStatusAsync(int axisId)

// 获取当前位置
Task<double> GetCurrentPositionAsync(int axisId)

// 检查是否在运动
Task<bool> IsMovingAsync(int axisId)

// 检查是否已回零
Task<bool> IsHomedAsync(int axisId)
```

#### 参数设置
```csharp
// 设置电机参数
Task<bool> SetMotorParamsAsync(int axisId, MotorParams parameters)

// 获取电机参数
Task<MotorParams> GetMotorParamsAsync(int axisId)
```

#### 使用示例
```csharp
// 初始化电机管理器
var motorManager = MotorManager.Instance;
await motorManager.InitializeAsync();

// 回零操作
await motorManager.HomeAsync(0);

// 移动到指定位置
await motorManager.MoveToAsync(0, 100.0, 1000.0);  // 轴0移动到100mm位置，速度1000

// 获取电机状态
var status = await motorManager.GetMotorStatusAsync(0);
Console.WriteLine($"当前位置: {status.CurrentPosition}");
```

#### 事件接口
```csharp
// 电机位置变化事件
event EventHandler<MotorPositionEventArgs> MotorPositionChanged;

// 电机状态变化事件
event EventHandler<MotorStatusEventArgs> MotorStatusChanged;
```

## VisionManager - 视觉系统管理器

### 类概述
负责视觉定位系统的控制，包括相机控制、图像处理和坐标计算。

### 主要接口

#### 相机控制
```csharp
// 连接相机
Task<bool> ConnectCameraAsync(int cameraId)

// 断开相机
Task<bool> DisconnectCameraAsync()

// 拍照
Task<byte[]> CaptureImageAsync()

// 开始实时预览
Task<bool> StartPreviewAsync()

// 停止实时预览
Task<bool> StopPreviewAsync()
```

#### 视觉定位
```csharp
// 开始定位
Task<bool> StartPositioningAsync(string templatePath)

// 停止定位
Task<bool> StopPositioningAsync()

// 获取定位结果
Task<VisionResult> GetLastResultAsync()

// 设置定位参数
Task<bool> SetPositioningParamsAsync(VisionParams parameters)
```

#### 使用示例
```csharp
// 初始化视觉管理器
var visionManager = VisionManager.Instance;
await visionManager.InitializeAsync();

// 连接相机
await visionManager.ConnectCameraAsync(0);

// 开始定位
await visionManager.StartPositioningAsync("template.bmp");

// 获取定位结果
var result = await visionManager.GetLastResultAsync();
if (result.IsSuccess)
{
    Console.WriteLine($"定位成功: X={result.X}, Y={result.Y}, 角度={result.Angle}");
}
```

#### 事件接口
```csharp
// 定位完成事件
event EventHandler<VisionResultEventArgs> PositioningCompleted;

// 相机状态变化事件
event EventHandler<CameraStatusEventArgs> CameraStatusChanged;
```

## ModbusTcpManager - Modbus TCP通信管理器

### 类概述
负责与 SCARA 机器人的 Modbus TCP 通信。

### 主要接口

#### 连接管理
```csharp
// 连接到设备
Task<bool> ConnectAsync(string ipAddress, int port)

// 断开连接
Task<bool> DisconnectAsync()

// 检查连接状态
bool IsConnected { get; }
```

#### 数据读写
```csharp
// 读取保持寄存器
Task<ushort[]> ReadHoldingRegistersAsync(ushort startAddress, ushort count)

// 写入单个寄存器
Task<bool> WriteSingleRegisterAsync(ushort address, ushort value)

// 写入多个寄存器
Task<bool> WriteMultipleRegistersAsync(ushort startAddress, ushort[] values)

// 读取输入寄存器
Task<ushort[]> ReadInputRegistersAsync(ushort startAddress, ushort count)
```

#### 使用示例
```csharp
// 初始化 Modbus 管理器
var modbusManager = ModbusTcpManager.Instance;
await modbusManager.InitializeAsync();

// 连接到设备
await modbusManager.ConnectAsync("*************", 502);

// 读取寄存器
var values = await modbusManager.ReadHoldingRegistersAsync(0, 10);

// 写入寄存器
await modbusManager.WriteSingleRegisterAsync(100, 1234);
```

## ScannerManager - 扫描枪管理器

### 类概述
负责串口扫描枪的通信控制。

### 主要接口

#### 串口控制
```csharp
// 打开串口
Task<bool> OpenPortAsync(string portName, int baudRate)

// 关闭串口
Task<bool> ClosePortAsync()

// 检查串口状态
bool IsPortOpen { get; }
```

#### 扫描控制
```csharp
// 开始扫描
Task<bool> StartScanningAsync()

// 停止扫描
Task<bool> StopScanningAsync()

// 手动触发扫描
Task<bool> TriggerScanAsync()
```

#### 使用示例
```csharp
// 初始化扫描枪管理器
var scannerManager = ScannerManager.Instance;
await scannerManager.InitializeAsync();

// 打开串口
await scannerManager.OpenPortAsync("COM3", 9600);

// 开始扫描
await scannerManager.StartScanningAsync();

// 订阅扫描事件
scannerManager.BarcodeScanned += (sender, e) =>
{
    Console.WriteLine($"扫描到条码: {e.Barcode}");
};
```

#### 事件接口
```csharp
// 条码扫描事件
event EventHandler<BarcodeScannedEventArgs> BarcodeScanned;

// 扫描枪状态变化事件
event EventHandler<ScannerStatusEventArgs> ScannerStatusChanged;
```

## WorkflowManager - 工作流管理器

### 类概述
负责协调各个管理器，实现完整的工作流程。

### 主要接口

#### 工作流控制
```csharp
// 启动工作流
Task<bool> StartWorkflowAsync()

// 停止工作流
Task<bool> StopWorkflowAsync()

// 重置工作流
Task<bool> ResetWorkflowAsync()

// 暂停工作流
Task<bool> PauseWorkflowAsync()

// 恢复工作流
Task<bool> ResumeWorkflowAsync()
```

#### 状态查询
```csharp
// 获取工作流状态
WorkflowState GetCurrentState()

// 检查是否运行中
bool IsRunning { get; }

// 获取当前步骤
string GetCurrentStep()
```

#### 使用示例
```csharp
// 初始化工作流管理器
var workflowManager = WorkflowManager.Instance;
await workflowManager.InitializeAsync();

// 启动工作流
await workflowManager.StartWorkflowAsync();

// 监控工作流状态
workflowManager.WorkflowStateChanged += (sender, e) =>
{
    Console.WriteLine($"工作流状态变化: {e.NewState}");
};
```

## StatisticsManager - 统计管理器

### 类概述
负责生产数据的统计和管理。

### 主要接口

#### 数据记录
```csharp
// 记录生产数据
Task<bool> RecordProductionDataAsync(ProductionRecord record)

// 记录操作日志
Task<bool> RecordOperationLogAsync(string operation, string details)
```

#### 数据查询
```csharp
// 获取今日统计
Task<ProductionStatistics> GetTodayStatisticsAsync()

// 获取指定日期统计
Task<ProductionStatistics> GetStatisticsByDateAsync(DateTime date)

// 获取历史记录
Task<List<ProductionRecord>> GetHistoryRecordsAsync(DateTime startDate, DateTime endDate)
```

#### 数据导出
```csharp
// 导出到 Excel
Task<bool> ExportToExcelAsync(string filePath, DateTime startDate, DateTime endDate)

// 导出到 CSV
Task<bool> ExportToCsvAsync(string filePath, DateTime startDate, DateTime endDate)
```

## 错误处理和日志

### 统一错误处理
所有接口都使用 `ExceptionHelper.SafeExecuteAsync` 进行错误处理：

```csharp
public async Task<bool> SomeOperationAsync()
{
    return await ExceptionHelper.SafeExecuteAsync(async () =>
    {
        // 具体操作实现
        return true;
    }, false, "操作名称");
}
```

### 日志记录
```csharp
// 在接口实现中添加日志
LogHelper.Info("操作开始");
LogHelper.Debug($"参数: {parameter}");
LogHelper.Warning("警告信息");
LogHelper.Error("错误信息", exception);
```

## 最佳实践

### 1. 异步调用
```csharp
// 推荐：使用异步调用
await manager.OperationAsync();

// 避免：阻塞调用
manager.OperationAsync().Wait();
```

### 2. 异常处理
```csharp
try
{
    var result = await manager.OperationAsync();
    if (!result)
    {
        // 处理操作失败
    }
}
catch (Exception ex)
{
    LogHelper.Error("操作异常", ex);
}
```

### 3. 事件订阅
```csharp
// 订阅事件
manager.SomeEvent += OnSomeEvent;

// 记得取消订阅
manager.SomeEvent -= OnSomeEvent;
```

这些接口为 MyHMI 系统提供了完整的功能支持，开发者可以根据具体需求调用相应的接口实现业务逻辑。
