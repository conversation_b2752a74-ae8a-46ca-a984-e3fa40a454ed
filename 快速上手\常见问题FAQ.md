# HR2项目常见问题FAQ

## 📋 概述

本文档收集了HR2项目开发和使用过程中的常见问题及解决方案，帮助快速排查和解决问题。

## 🚀 系统启动问题

### Q1: 程序启动时提示"DMC1000B控制卡初始化失败"
**问题现象**: 程序启动时显示控制卡不可用，相关功能无法正常工作。

**可能原因**:
1. DMC1000B控制卡未正确安装
2. 控制卡驱动程序未安装
3. 硬件连接问题
4. 权限不足

**解决方案**:
```csharp
// 检查控制卡状态
bool isAvailable = DMC1000BCardManager.Instance.IsCardAvailable();
if (!isAvailable)
{
    // 脱机模式运行
    LogHelper.Warning("控制卡不可用，切换到脱机模式");
}
```

**操作步骤**:
1. 检查设备管理器中是否有DMC1000B设备
2. 重新安装控制卡驱动程序
3. 以管理员权限运行程序
4. 检查硬件连接线缆

### Q2: 程序启动后界面显示不完整
**问题现象**: 主界面部分控件缺失或显示异常。

**解决方案**:
1. 检查屏幕分辨率是否为1920×1080
2. 检查Windows显示缩放设置（推荐100%）
3. 重新编译项目
4. 清理bin和obj文件夹后重新生成

### Q3: Manager初始化失败
**问题现象**: 某些Manager初始化失败，功能不可用。

**排查步骤**:
```csharp
// 查看初始化状态
var status = InitializationStatusManager.Instance.GetAllModuleStatus();
foreach (var module in status)
{
    if (!module.IsInitialized)
    {
        LogHelper.Error($"模块 {module.ModuleName} 初始化失败: {module.ErrorMessage}");
    }
}
```

## 🔧 硬件控制问题

### Q4: 电机无法正常运动
**问题现象**: 发送电机运动命令后，电机不动作或动作异常。

**排查清单**:
1. **检查电机参数**:
```csharp
// 验证电机参数
var motorParams = GlobalMotorParameterManager.Instance.GetFlipMotorParams(axis);
if (motorParams == null)
{
    LogHelper.Error($"轴{axis}参数未配置");
}
```

2. **检查电机状态**:
```csharp
// 获取电机状态
short motionStatus = DMC1000BMotorManager.Instance.GetMotorMotionStatus(axis);
if (motionStatus == DMC1000BMotorManager.MOTION_ERROR)
{
    LogHelper.Error($"轴{axis}处于错误状态");
}
```

3. **检查限位开关**:
- 正向限位（PEL）
- 负向限位（NEL）
- 原点信号（ORG）

**常见解决方案**:
- 重新设置电机参数
- 执行电机回原点操作
- 检查硬件接线
- 清除电机报警状态

### Q5: IO读写异常
**问题现象**: IO状态读取错误或输出控制无效。

**调试方法**:
```csharp
// 测试IO读写
try
{
    bool inputState = await DMC1000BIOManager.Instance.ReadInputAsync("I0001");
    LogHelper.Info($"IO I0001 状态: {inputState}");
    
    bool writeResult = await DMC1000BIOManager.Instance.WriteOutputAsync("O0001", true);
    LogHelper.Info($"IO O0001 写入结果: {writeResult}");
}
catch (Exception ex)
{
    LogHelper.Error("IO操作异常", ex);
}
```

**检查要点**:
1. IO端口定义是否正确
2. 硬件接线是否正常
3. 控制卡是否正常工作
4. IO配置是否匹配实际硬件

### Q6: 机器人连接失败
**问题现象**: Epson机器人TCP连接建立失败。

**排查步骤**:
1. **网络连通性测试**:
```bash
ping *************
telnet ************* 5000
```

2. **检查机器人配置**:
```csharp
var config = Settings.Settings.Current.EpsonRobot;
LogHelper.Info($"机器人IP: {config.IPAddress}");
LogHelper.Info($"控制端口: {config.ControlPort}");
LogHelper.Info($"数据端口: {config.DataPort}");
```

3. **检查机器人状态**:
- 机器人是否上电
- 示教器是否显示正常
- 网络设置是否正确

## 🖥️ UI界面问题

### Q7: 界面卡顿或无响应
**问题现象**: 操作界面时出现卡顿，按钮点击无响应。

**原因分析**:
- UI线程被阻塞
- 同步操作占用时间过长
- 内存泄漏导致性能下降

**解决方案**:
```csharp
// ✅ 正确：异步操作
private async void Button_Click(object sender, EventArgs e)
{
    try
    {
        // 显示进度提示
        statusLabel.Text = "正在处理...";
        
        // 异步执行耗时操作
        bool result = await SomeTimeConsumingOperationAsync();
        
        // 更新UI
        statusLabel.Text = result ? "操作成功" : "操作失败";
    }
    catch (Exception ex)
    {
        LogHelper.Error("操作失败", ex);
        statusLabel.Text = "操作异常";
    }
}

// ❌ 错误：同步阻塞
private void Button_Click(object sender, EventArgs e)
{
    // 同步操作会阻塞UI线程
    bool result = SomeTimeConsumingOperation();
}
```

### Q8: 控件显示异常
**问题现象**: 某些控件显示不正确或数据不更新。

**检查要点**:
1. **事件订阅是否正确**:
```csharp
// 检查事件订阅
private void InitializeEvents()
{
    SomeManager.Instance.SomeEvent += OnSomeEvent;
}

private void OnSomeEvent(object sender, SomeEventArgs e)
{
    // 确保在UI线程中执行
    UIHelper.InvokeOnUIThread(this, () =>
    {
        UpdateUI(e.Data);
    });
}
```

2. **数据绑定是否有效**
3. **控件是否正确初始化**

## 📊 数据处理问题

### Q9: 配置文件损坏或丢失
**问题现象**: 程序启动时提示配置文件错误，参数重置为默认值。

**恢复方法**:
```csharp
// 备份和恢复配置
public static void BackupSettings()
{
    try
    {
        string backupPath = $"{ConfigFilePath}.backup.{DateTime.Now:yyyyMMdd_HHmmss}";
        File.Copy(ConfigFilePath, backupPath);
        LogHelper.Info($"配置文件已备份到: {backupPath}");
    }
    catch (Exception ex)
    {
        LogHelper.Error("配置文件备份失败", ex);
    }
}

public static void RestoreFromBackup(string backupPath)
{
    try
    {
        File.Copy(backupPath, ConfigFilePath, true);
        Load(); // 重新加载配置
        LogHelper.Info("配置文件恢复成功");
    }
    catch (Exception ex)
    {
        LogHelper.Error("配置文件恢复失败", ex);
    }
}
```

### Q10: 生产数据导出失败
**问题现象**: 导出Excel或CSV文件时出现错误。

**常见原因**:
1. 文件被其他程序占用
2. 磁盘空间不足
3. 权限不足
4. 数据格式异常

**解决方案**:
```csharp
public async Task<bool> ExportDataSafeAsync(string filePath, List<ProductionRecord> data)
{
    try
    {
        // 检查文件是否被占用
        if (IsFileInUse(filePath))
        {
            throw new IOException($"文件被占用: {filePath}");
        }
        
        // 检查磁盘空间
        if (!HasEnoughDiskSpace(filePath, data.Count * 1024))
        {
            throw new IOException("磁盘空间不足");
        }
        
        // 执行导出
        await ExportToExcelAsync(filePath, data);
        return true;
    }
    catch (Exception ex)
    {
        LogHelper.Error("数据导出失败", ex);
        return false;
    }
}
```

## 🔄 工作流问题

### Q11: 自动模式无法启动
**问题现象**: 点击启动按钮后，系统提示无法启动自动模式。

**排查步骤**:
1. **检查系统模式**:
```csharp
var currentMode = SystemModeManager.Instance.CurrentMode;
if (currentMode != SystemMode.Automatic)
{
    LogHelper.Warning("当前不在自动模式，无法启动自动化流程");
}
```

2. **检查工作流状态**:
```csharp
var workflowState = WorkflowManager.Instance.CurrentState;
if (workflowState != WorkflowState.Idle)
{
    LogHelper.Warning($"工作流正在运行，当前状态: {workflowState}");
}
```

3. **检查设备就绪状态**:
- 所有Manager是否初始化成功
- 硬件设备是否正常连接
- 安全条件是否满足

### Q12: 工作流执行中断
**问题现象**: 自动化流程执行过程中突然停止。

**分析方法**:
```csharp
// 查看工作流事件日志
WorkflowManager.Instance.WorkflowError += (sender, e) =>
{
    LogHelper.Error($"工作流错误: {e.ErrorMessage}", e.Exception);
    // 分析错误原因并采取相应措施
};
```

**常见原因**:
- 传感器状态异常
- 电机运动超时
- 机器人通信中断
- 安全保护触发

## 🛠️ 调试技巧

### Q13: 如何启用详细日志
**配置方法**:
```xml
<!-- NLog.config -->
<nlog>
  <targets>
    <target name="file" type="File" 
            fileName="Logs/Log_${shortdate}.txt"
            layout="${longdate} ${level:uppercase=true} ${message} ${exception:format=tostring}" />
  </targets>
  <rules>
    <logger name="*" minlevel="Debug" writeTo="file" />
  </rules>
</nlog>
```

### Q14: 如何进行脱机调试
**脱机模式设置**:
```csharp
// 在Manager中添加脱机模式支持
public async Task<bool> ReadInputAsync(string ioNumber)
{
    if (!DMC1000BCardManager.Instance.IsCardAvailable())
    {
        // 脱机模式：返回模拟数据
        return SimulateIOInput(ioNumber);
    }
    
    // 正常硬件操作
    return await ReadFromHardware(ioNumber);
}

private bool SimulateIOInput(string ioNumber)
{
    // 根据IO编号返回模拟状态
    return ioNumber.EndsWith("1") || ioNumber.EndsWith("3");
}
```

## 📞 技术支持

### 获取帮助
1. **查看开发文档**: `Development_Documents/`文件夹
2. **查看日志文件**: `Logs/`文件夹中的最新日志
3. **运行测试程序**: 使用`Testing/TestRunner.cs`进行系统测试
4. **查看任务状态**: `Development_Documents/Tasks.md`

### 报告问题
提供以下信息：
1. 问题详细描述
2. 重现步骤
3. 相关日志文件
4. 系统环境信息
5. 硬件配置信息

### 紧急问题处理
1. 立即停止自动化流程
2. 切换到手动模式
3. 检查安全状态
4. 记录问题现象
5. 联系技术支持

通过这些FAQ，可以快速定位和解决HR2项目中的常见问题，提高开发和使用效率。
