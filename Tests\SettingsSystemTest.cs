using System;
using System.IO;
using MyHMI.Settings;
using MyHMI.Helpers;

namespace MyHMI.Tests
{
    /// <summary>
    /// Settings系统基本功能测试
    /// </summary>
    public static class SettingsSystemTest
    {
        /// <summary>
        /// 运行Settings系统基本功能测试
        /// </summary>
        public static void RunBasicTest()
        {
            try
            {
                LogHelper.Info("开始Settings系统基本功能测试...");

                // 测试1：加载Settings
                TestLoadSettings();

                // 测试2：访问各个模块的参数
                TestParameterAccess();

                // 测试3：修改参数并保存
                TestParameterModification();

                // 测试4：重新加载验证
                TestReloadVerification();

                LogHelper.Info("Settings系统基本功能测试完成，所有测试通过！");
            }
            catch (Exception ex)
            {
                LogHelper.Error("Settings系统测试失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 测试Settings加载
        /// </summary>
        private static void TestLoadSettings()
        {
            LogHelper.Info("测试1：Settings加载...");
            
            Settings.Settings.Load();
            var settings = Settings.Settings.Current;
            
            if (settings == null)
                throw new Exception("Settings加载失败，Current为null");
                
            LogHelper.Info("✓ Settings加载成功");
        }

        /// <summary>
        /// 测试参数访问
        /// </summary>
        private static void TestParameterAccess()
        {
            LogHelper.Info("测试2：参数访问...");
            
            var settings = Settings.Settings.Current;
            
            // 测试Motor参数
            var motorSettings = settings.Motor;
            if (motorSettings == null)
                throw new Exception("Motor设置为null");
            LogHelper.Info($"✓ Motor参数访问成功，左翻转脉冲当量: {motorSettings.LeftFlipPulseEquivalent}");
            
            // 测试Communication参数
            var commSettings = settings.Communication;
            if (commSettings == null)
                throw new Exception("Communication设置为null");
            LogHelper.Info($"✓ Communication参数访问成功，EpsonRobot1 IP: {commSettings.EpsonRobot1IP}");
            
            // 测试System参数
            var systemSettings = settings.System;
            if (systemSettings == null)
                throw new Exception("System设置为null");
            LogHelper.Info($"✓ System参数访问成功，系统名称: {systemSettings.Name}");
            
            // 测试IO参数
            var ioSettings = settings.IO;
            if (ioSettings == null)
                throw new Exception("IO设置为null");
            LogHelper.Info($"✓ IO参数访问成功，输入通道数: {ioSettings.InputChannels}");
            
            LogHelper.Info("✓ 所有模块参数访问成功");
        }

        /// <summary>
        /// 测试参数修改
        /// </summary>
        private static void TestParameterModification()
        {
            LogHelper.Info("测试3：参数修改和保存...");
            
            var settings = Settings.Settings.Current;
            
            // 保存原始值
            string originalName = settings.System.Name;
            double originalPulse = settings.Motor.LeftFlipPulseEquivalent;
            
            // 修改参数
            string testName = "测试系统_" + DateTime.Now.ToString("HHmmss");
            double testPulse = 0.12345;
            
            settings.System.Name = testName;
            settings.Motor.LeftFlipPulseEquivalent = testPulse;
            
            // 保存
            Settings.Settings.Save();
            LogHelper.Info("✓ 参数修改和保存成功");
            
            // 恢复原始值
            settings.System.Name = originalName;
            settings.Motor.LeftFlipPulseEquivalent = originalPulse;
            Settings.Settings.Save();
            LogHelper.Info("✓ 参数恢复成功");
        }

        /// <summary>
        /// 测试重新加载验证
        /// </summary>
        private static void TestReloadVerification()
        {
            LogHelper.Info("测试4：重新加载验证...");
            
            // 修改一个参数
            var settings = Settings.Settings.Current;
            string originalVersion = settings.System.Version;
            string testVersion = "Test_" + DateTime.Now.ToString("HHmmss");
            
            settings.System.Version = testVersion;
            Settings.Settings.Save();
            
            // 重新加载
            Settings.Settings.Load();
            var reloadedSettings = Settings.Settings.Current;
            
            if (reloadedSettings.System.Version != testVersion)
                throw new Exception($"重新加载验证失败，期望: {testVersion}, 实际: {reloadedSettings.System.Version}");
            
            LogHelper.Info("✓ 重新加载验证成功");
            
            // 恢复原始值
            reloadedSettings.System.Version = originalVersion;
            Settings.Settings.Save();
            LogHelper.Info("✓ 参数恢复成功");
        }
    }
}
