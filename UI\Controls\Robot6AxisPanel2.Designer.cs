using MyHMI.Events;

namespace MyHMI.UI.Controls
{
    partial class Robot6AxisPanel2
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // 取消订阅Epson机器人事件
                UnsubscribeFromEpsonRobotEvents();

                // 取消订阅配置变更事件
                UnsubscribeFromConfigurationChanges();

                if (components != null)
                {
                    components.Dispose();
                }
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // Robot6AxisPanel
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Name = "Robot6AxisPanel";
            this.Size = new System.Drawing.Size(660, 840);
            this.ResumeLayout(false);

        }

        #endregion
    }
}
