[2025-09-19 08:18:00.951] [INFO] 程序启动开始
[2025-09-19 08:18:00.954] [INFO] 配置系统初始化成功
[2025-09-19 08:18:01.008] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-19 08:18:01.009] [INFO] 配置系统初始化完成
[2025-09-19 08:18:01.010] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-19 08:18:01.032] [INFO] 开始初始化各个Manager...
[2025-09-19 08:18:01.032] [INFO] 初始化基础Manager...
[2025-09-19 08:18:01.037] [INFO] 开始初始化IOManager...
[2025-09-19 08:18:01.039] [INFO] 模拟初始化雷赛卡，卡号: 0
[2025-09-19 08:18:01.154] [INFO] IO监控任务已启动
[2025-09-19 08:18:01.154] [INFO] IOManager初始化完成
[2025-09-19 08:18:01.155] [INFO] IO监控循环开始
[2025-09-19 08:18:01.158] [INFO] 开始初始化MotorManager...
[2025-09-19 08:18:01.160] [INFO] 模拟初始化运动控制卡
[2025-09-19 08:18:01.382] [INFO] 加载了8个电机的默认配置
[2025-09-19 08:18:01.383] [INFO] 电机监控任务已启动
[2025-09-19 08:18:01.383] [INFO] MotorManager初始化完成
[2025-09-19 08:18:01.383] [INFO] 初始化通信Manager...
[2025-09-19 08:18:01.385] [INFO] 电机监控循环开始
[2025-09-19 08:18:01.386] [INFO] 开始初始化ScannerManager...
[2025-09-19 08:18:01.387] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-19 08:18:01.391] [INFO] 串口初始化完成
[2025-09-19 08:18:01.392] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-19 08:18:01.396] [INFO] 扫描枪连接成功: COM1
[2025-09-19 08:18:01.396] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-19 08:18:01.396] [INFO] ScannerManager初始化完成
[2025-09-19 08:18:01.399] [INFO] 开始初始化ModbusTcpManager...
[2025-09-19 08:18:01.400] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-19 08:18:01.402] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-19 08:18:06.496] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 08:18:06.512] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 08:18:06.512] [INFO] ModbusTcpManager初始化完成
[2025-09-19 08:18:06.513] [INFO] 开始初始化RobotTcpManager...
[2025-09-19 08:18:06.514] [INFO] 机器人TCP配置: 192.168.1.100:8080
[2025-09-19 08:18:06.516] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-19 08:18:11.585] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: 192.168.1.100:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 08:18:11.586] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 08:18:11.587] [INFO] RobotTcpManager初始化完成
[2025-09-19 08:18:11.587] [INFO] 初始化视觉Manager...
[2025-09-19 08:18:11.589] [INFO] 开始初始化VisionManager...
[2025-09-19 08:18:11.590] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-19 08:18:11.591] [INFO] 模拟初始化相机，索引: 0
[2025-09-19 08:18:12.132] [INFO] 相机初始化成功
[2025-09-19 08:18:12.142] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-19 08:18:12.143] [INFO] 视觉配置加载完成
[2025-09-19 08:18:12.144] [INFO] VisionManager初始化完成
[2025-09-19 08:18:12.145] [INFO] 初始化数据Manager...
[2025-09-19 08:18:12.162] [INFO] 开始初始化StatisticsManager...
[2025-09-19 08:18:12.164] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-19 08:18:12.174] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-19 08:18:12.175] [INFO] 历史数据加载完成
[2025-09-19 08:18:12.176] [INFO] 自动保存任务已启动
[2025-09-19 08:18:12.176] [INFO] StatisticsManager初始化完成
[2025-09-19 08:18:12.176] [INFO] Manager初始化完成，成功率: 100%
[2025-09-19 08:18:12.180] [INFO] 所有Manager初始化完成
[2025-09-19 08:18:12.181] [INFO] 自动保存循环开始
[2025-09-19 08:18:12.232] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 08:18:12.233] [INFO] 主界面布局创建完成
[2025-09-19 08:18:12.233] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-19 08:18:17.250] [INFO] 对位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 08:18:18.244] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 08:18:19.494] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-19 08:18:20.574] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-19 08:18:22.972] [INFO] IO读取状态面板初始化完成 - 按HTML原型设计
[2025-09-19 08:18:24.802] [INFO] 生产日志管理面板初始化完成 - 按HTML原型设计
[2025-09-19 08:18:40.084] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 08:18:40.846] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 08:23:13.773] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-19 08:26:27.454] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 08:26:33.163] [INFO] 皮带电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 08:26:55.028] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 08:26:55.822] [INFO] 皮带电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 08:27:32.398] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 08:27:33.749] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 08:34:18.942] [INFO] 皮带电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 09:19:19.111] [INFO] 程序启动开始
[2025-09-19 09:19:19.113] [INFO] 配置系统初始化成功
[2025-09-19 09:19:19.311] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-19 09:19:19.311] [INFO] 配置系统初始化完成
[2025-09-19 09:19:19.312] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-19 09:19:19.334] [INFO] 开始初始化各个Manager...
[2025-09-19 09:19:19.334] [INFO] 初始化基础Manager...
[2025-09-19 09:19:19.339] [INFO] 开始初始化IOManager...
[2025-09-19 09:19:19.342] [INFO] 模拟初始化雷赛卡，卡号: 0
[2025-09-19 09:19:19.459] [INFO] IO监控任务已启动
[2025-09-19 09:19:19.459] [INFO] IOManager初始化完成
[2025-09-19 09:19:19.461] [INFO] IO监控循环开始
[2025-09-19 09:19:19.464] [INFO] 开始初始化MotorManager...
[2025-09-19 09:19:19.467] [INFO] 模拟初始化运动控制卡
[2025-09-19 09:19:19.692] [INFO] 加载了8个电机的默认配置
[2025-09-19 09:19:19.693] [INFO] 电机监控任务已启动
[2025-09-19 09:19:19.693] [INFO] MotorManager初始化完成
[2025-09-19 09:19:19.694] [INFO] 初始化通信Manager...
[2025-09-19 09:19:19.695] [INFO] 电机监控循环开始
[2025-09-19 09:19:19.696] [INFO] 开始初始化ScannerManager...
[2025-09-19 09:19:19.698] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-19 09:19:19.703] [INFO] 串口初始化完成
[2025-09-19 09:19:19.706] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-19 09:19:19.709] [INFO] 扫描枪连接成功: COM1
[2025-09-19 09:19:19.710] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-19 09:19:19.710] [INFO] ScannerManager初始化完成
[2025-09-19 09:19:19.712] [INFO] 开始初始化ModbusTcpManager...
[2025-09-19 09:19:19.715] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-19 09:19:19.717] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-19 09:19:24.834] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 09:19:24.852] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 09:19:24.853] [INFO] ModbusTcpManager初始化完成
[2025-09-19 09:19:24.855] [INFO] 开始初始化RobotTcpManager...
[2025-09-19 09:19:24.856] [INFO] 机器人TCP配置: 192.168.1.100:8080
[2025-09-19 09:19:24.858] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-19 09:19:29.902] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: 192.168.1.100:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 09:19:29.903] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 09:19:29.903] [INFO] RobotTcpManager初始化完成
[2025-09-19 09:19:29.903] [INFO] 初始化视觉Manager...
[2025-09-19 09:19:29.906] [INFO] 开始初始化VisionManager...
[2025-09-19 09:19:29.907] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-19 09:19:29.908] [INFO] 模拟初始化相机，索引: 0
[2025-09-19 09:19:30.454] [INFO] 相机初始化成功
[2025-09-19 09:19:30.458] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-19 09:19:30.459] [INFO] 视觉配置加载完成
[2025-09-19 09:19:30.460] [INFO] VisionManager初始化完成
[2025-09-19 09:19:30.461] [INFO] 初始化数据Manager...
[2025-09-19 09:19:30.473] [INFO] 开始初始化StatisticsManager...
[2025-09-19 09:19:30.474] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-19 09:19:30.493] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-19 09:19:30.493] [INFO] 历史数据加载完成
[2025-09-19 09:19:30.494] [INFO] 自动保存任务已启动
[2025-09-19 09:19:30.494] [INFO] StatisticsManager初始化完成
[2025-09-19 09:19:30.494] [INFO] Manager初始化完成，成功率: 100%
[2025-09-19 09:19:30.495] [INFO] 所有Manager初始化完成
[2025-09-19 09:19:30.496] [INFO] 自动保存循环开始
[2025-09-19 09:19:30.536] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 09:19:30.536] [INFO] 主界面布局创建完成
[2025-09-19 09:19:30.536] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-19 09:19:32.496] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 09:19:36.330] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-19 09:19:37.917] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 09:19:38.486] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-19 09:19:39.439] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 09:19:41.866] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 09:19:46.914] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 09:19:53.552] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 09:37:12.445] [INFO] 程序启动开始
[2025-09-19 09:37:12.447] [INFO] 配置系统初始化成功
[2025-09-19 09:37:12.497] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-19 09:37:12.497] [INFO] 配置系统初始化完成
[2025-09-19 09:37:12.497] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-19 09:37:12.519] [INFO] 开始初始化各个Manager...
[2025-09-19 09:37:12.520] [INFO] 初始化基础Manager...
[2025-09-19 09:37:12.524] [INFO] 开始初始化IOManager...
[2025-09-19 09:37:12.527] [INFO] 模拟初始化雷赛卡，卡号: 0
[2025-09-19 09:37:12.634] [INFO] IO监控任务已启动
[2025-09-19 09:37:12.634] [INFO] IOManager初始化完成
[2025-09-19 09:37:12.636] [INFO] IO监控循环开始
[2025-09-19 09:37:12.638] [INFO] 开始初始化MotorManager...
[2025-09-19 09:37:12.640] [INFO] 模拟初始化运动控制卡
[2025-09-19 09:37:12.897] [INFO] 加载了8个电机的默认配置
[2025-09-19 09:37:12.898] [INFO] 电机监控任务已启动
[2025-09-19 09:37:12.898] [INFO] MotorManager初始化完成
[2025-09-19 09:37:12.899] [INFO] 初始化通信Manager...
[2025-09-19 09:37:12.901] [INFO] 电机监控循环开始
[2025-09-19 09:37:12.903] [INFO] 开始初始化ScannerManager...
[2025-09-19 09:37:12.906] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-19 09:37:12.912] [INFO] 串口初始化完成
[2025-09-19 09:37:12.920] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-19 09:37:12.922] [INFO] 扫描枪连接成功: COM1
[2025-09-19 09:37:12.923] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-19 09:37:12.924] [INFO] ScannerManager初始化完成
[2025-09-19 09:37:12.926] [INFO] 开始初始化ModbusTcpManager...
[2025-09-19 09:37:12.927] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-19 09:37:12.930] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-19 09:37:18.005] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 09:37:18.021] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 09:37:18.021] [INFO] ModbusTcpManager初始化完成
[2025-09-19 09:37:18.023] [INFO] 开始初始化RobotTcpManager...
[2025-09-19 09:37:18.024] [INFO] 机器人TCP配置: 192.168.1.100:8080
[2025-09-19 09:37:18.026] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-19 09:37:23.100] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: 192.168.1.100:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 09:37:23.104] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 09:37:23.105] [INFO] RobotTcpManager初始化完成
[2025-09-19 09:37:23.105] [INFO] 初始化视觉Manager...
[2025-09-19 09:37:23.114] [INFO] 开始初始化VisionManager...
[2025-09-19 09:37:23.115] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-19 09:37:23.117] [INFO] 模拟初始化相机，索引: 0
[2025-09-19 09:37:23.670] [INFO] 相机初始化成功
[2025-09-19 09:37:23.673] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-19 09:37:23.673] [INFO] 视觉配置加载完成
[2025-09-19 09:37:23.674] [INFO] VisionManager初始化完成
[2025-09-19 09:37:23.675] [INFO] 初始化数据Manager...
[2025-09-19 09:37:23.700] [INFO] 开始初始化StatisticsManager...
[2025-09-19 09:37:23.702] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-19 09:37:23.711] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-19 09:37:23.711] [INFO] 历史数据加载完成
[2025-09-19 09:37:23.712] [INFO] 自动保存任务已启动
[2025-09-19 09:37:23.712] [INFO] StatisticsManager初始化完成
[2025-09-19 09:37:23.713] [INFO] Manager初始化完成，成功率: 100%
[2025-09-19 09:37:23.713] [INFO] 所有Manager初始化完成
[2025-09-19 09:37:23.715] [INFO] 自动保存循环开始
[2025-09-19 09:37:23.759] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 09:37:23.760] [INFO] 主界面布局创建完成
[2025-09-19 09:37:23.760] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-19 09:37:27.716] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 09:37:29.085] [INFO] 皮带电机控制面板初始化完成 - 双电机设计
[2025-09-19 09:38:34.757] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 09:50:27.086] [INFO] 程序启动开始
[2025-09-19 09:50:27.087] [INFO] 配置系统初始化成功
[2025-09-19 09:50:27.135] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-19 09:50:27.135] [INFO] 配置系统初始化完成
[2025-09-19 09:50:27.135] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-19 09:50:27.154] [INFO] 开始初始化各个Manager...
[2025-09-19 09:50:27.155] [INFO] 初始化基础Manager...
[2025-09-19 09:50:27.159] [INFO] 开始初始化IOManager...
[2025-09-19 09:50:27.181] [INFO] 模拟初始化雷赛卡，卡号: 0
[2025-09-19 09:50:27.288] [INFO] IO监控任务已启动
[2025-09-19 09:50:27.288] [INFO] IOManager初始化完成
[2025-09-19 09:50:27.289] [INFO] IO监控循环开始
[2025-09-19 09:50:27.292] [INFO] 开始初始化MotorManager...
[2025-09-19 09:50:27.315] [INFO] 模拟初始化运动控制卡
[2025-09-19 09:50:27.529] [INFO] 加载了8个电机的默认配置
[2025-09-19 09:50:27.530] [INFO] 电机监控任务已启动
[2025-09-19 09:50:27.530] [INFO] MotorManager初始化完成
[2025-09-19 09:50:27.531] [INFO] 初始化通信Manager...
[2025-09-19 09:50:27.532] [INFO] 电机监控循环开始
[2025-09-19 09:50:27.534] [INFO] 开始初始化ScannerManager...
[2025-09-19 09:50:27.537] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-19 09:50:27.539] [INFO] 串口初始化完成
[2025-09-19 09:50:27.541] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-19 09:50:27.546] [INFO] 扫描枪连接成功: COM1
[2025-09-19 09:50:27.546] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-19 09:50:27.546] [INFO] ScannerManager初始化完成
[2025-09-19 09:50:27.549] [INFO] 开始初始化ModbusTcpManager...
[2025-09-19 09:50:27.551] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-19 09:50:27.554] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-19 09:50:32.646] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 09:50:32.666] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 09:50:32.666] [INFO] ModbusTcpManager初始化完成
[2025-09-19 09:50:32.669] [INFO] 开始初始化RobotTcpManager...
[2025-09-19 09:50:32.669] [INFO] 机器人TCP配置: 192.168.1.100:8080
[2025-09-19 09:50:32.673] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-19 09:50:37.713] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: 192.168.1.100:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 09:50:37.713] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 09:50:37.713] [INFO] RobotTcpManager初始化完成
[2025-09-19 09:50:37.714] [INFO] 初始化视觉Manager...
[2025-09-19 09:50:37.717] [INFO] 开始初始化VisionManager...
[2025-09-19 09:50:37.718] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-19 09:50:37.719] [INFO] 模拟初始化相机，索引: 0
[2025-09-19 09:50:38.237] [INFO] 相机初始化成功
[2025-09-19 09:50:38.241] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-19 09:50:38.241] [INFO] 视觉配置加载完成
[2025-09-19 09:50:38.242] [INFO] VisionManager初始化完成
[2025-09-19 09:50:38.242] [INFO] 初始化数据Manager...
[2025-09-19 09:50:38.245] [INFO] 开始初始化StatisticsManager...
[2025-09-19 09:50:38.245] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-19 09:50:38.248] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-19 09:50:38.248] [INFO] 历史数据加载完成
[2025-09-19 09:50:38.248] [INFO] 自动保存任务已启动
[2025-09-19 09:50:38.249] [INFO] StatisticsManager初始化完成
[2025-09-19 09:50:38.249] [INFO] Manager初始化完成，成功率: 100%
[2025-09-19 09:50:38.249] [INFO] 所有Manager初始化完成
[2025-09-19 09:50:38.250] [INFO] 自动保存循环开始
[2025-09-19 09:50:38.286] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 09:50:38.286] [INFO] 主界面布局创建完成
[2025-09-19 09:50:38.286] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-19 09:50:40.509] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 09:50:42.889] [INFO] 皮带电机控制面板初始化完成 - 双电机设计
[2025-09-19 14:01:29.062] [INFO] 程序启动开始
[2025-09-19 14:01:29.065] [INFO] 配置系统初始化成功
[2025-09-19 14:01:29.334] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-19 14:01:29.334] [INFO] 配置系统初始化完成
[2025-09-19 14:01:29.334] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-19 14:01:29.355] [INFO] 开始初始化各个Manager...
[2025-09-19 14:01:29.356] [INFO] 初始化基础Manager...
[2025-09-19 14:01:29.361] [INFO] 开始初始化IOManager...
[2025-09-19 14:01:29.363] [INFO] 模拟初始化雷赛卡，卡号: 0
[2025-09-19 14:01:29.470] [INFO] IO监控任务已启动
[2025-09-19 14:01:29.470] [INFO] IOManager初始化完成
[2025-09-19 14:01:29.472] [INFO] IO监控循环开始
[2025-09-19 14:01:29.474] [INFO] 开始初始化MotorManager...
[2025-09-19 14:01:29.476] [INFO] 模拟初始化运动控制卡
[2025-09-19 14:01:29.689] [INFO] 加载了8个电机的默认配置
[2025-09-19 14:01:29.690] [INFO] 电机监控任务已启动
[2025-09-19 14:01:29.690] [INFO] MotorManager初始化完成
[2025-09-19 14:01:29.690] [INFO] 初始化通信Manager...
[2025-09-19 14:01:29.692] [INFO] 电机监控循环开始
[2025-09-19 14:01:29.693] [INFO] 开始初始化ScannerManager...
[2025-09-19 14:01:29.696] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-19 14:01:29.697] [INFO] 串口初始化完成
[2025-09-19 14:01:29.699] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-19 14:01:29.702] [INFO] 扫描枪连接成功: COM1
[2025-09-19 14:01:29.702] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-19 14:01:29.702] [INFO] ScannerManager初始化完成
[2025-09-19 14:01:29.704] [INFO] 开始初始化ModbusTcpManager...
[2025-09-19 14:01:29.706] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-19 14:01:29.708] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-19 14:01:34.818] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 14:01:34.838] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 14:01:34.838] [INFO] ModbusTcpManager初始化完成
[2025-09-19 14:01:34.840] [INFO] 开始初始化RobotTcpManager...
[2025-09-19 14:01:34.841] [INFO] 机器人TCP配置: 192.168.1.100:8080
[2025-09-19 14:01:34.844] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-19 14:01:39.896] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: 192.168.1.100:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 14:01:39.897] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 14:01:39.897] [INFO] RobotTcpManager初始化完成
[2025-09-19 14:01:39.897] [INFO] 初始化视觉Manager...
[2025-09-19 14:01:39.899] [INFO] 开始初始化VisionManager...
[2025-09-19 14:01:39.900] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-19 14:01:39.901] [INFO] 模拟初始化相机，索引: 0
[2025-09-19 14:01:40.465] [INFO] 相机初始化成功
[2025-09-19 14:01:40.468] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-19 14:01:40.468] [INFO] 视觉配置加载完成
[2025-09-19 14:01:40.469] [INFO] VisionManager初始化完成
[2025-09-19 14:01:40.469] [INFO] 初始化数据Manager...
[2025-09-19 14:01:40.475] [INFO] 开始初始化StatisticsManager...
[2025-09-19 14:01:40.476] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-19 14:01:40.485] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-19 14:01:40.485] [INFO] 历史数据加载完成
[2025-09-19 14:01:40.486] [INFO] 自动保存任务已启动
[2025-09-19 14:01:40.487] [INFO] StatisticsManager初始化完成
[2025-09-19 14:01:40.487] [INFO] Manager初始化完成，成功率: 100%
[2025-09-19 14:01:40.488] [INFO] 所有Manager初始化完成
[2025-09-19 14:01:40.490] [INFO] 自动保存循环开始
[2025-09-19 14:01:40.559] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 14:01:40.559] [INFO] 主界面布局创建完成
[2025-09-19 14:01:40.559] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-19 14:01:58.729] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 14:01:58.737] [INFO] 翻转电机轴0参数设置成功: 脉冲当量=1000pulse/°, 最大速度=90°/s
[2025-09-19 14:01:58.737] [INFO] 翻转电机轴1参数设置成功: 脉冲当量=1000pulse/°, 最大速度=90°/s
[2025-09-19 14:01:58.737] [INFO] 翻转电机参数初始化完成
[2025-09-19 14:01:58.737] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-19 14:02:00.254] [INFO] 翻转电机控制面板资源释放完成
[2025-09-19 14:02:00.259] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 14:02:00.913] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 14:02:00.916] [INFO] 翻转电机轴0参数设置成功: 脉冲当量=1000pulse/°, 最大速度=90°/s
[2025-09-19 14:02:00.916] [INFO] 翻转电机轴1参数设置成功: 脉冲当量=1000pulse/°, 最大速度=90°/s
[2025-09-19 14:02:00.916] [INFO] 翻转电机参数初始化完成
[2025-09-19 14:02:00.916] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-19 14:02:01.669] [INFO] 翻转电机控制面板资源释放完成
[2025-09-19 14:02:01.699] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-19 14:02:55.726] [INFO] IO状态缓存初始化完成
[2025-09-19 14:02:55.763] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
[2025-09-19 14:03:05.091] [INFO] IO输出控制面板初始化完成 - 支持基础IO和扩展IO
[2025-09-19 14:03:12.180] [WARN] IO管理器未初始化，仅更新UI显示 - O0001: ON
[2025-09-19 14:03:12.860] [WARN] IO管理器未初始化，仅更新UI显示 - O0001: OFF
[2025-09-19 14:03:13.618] [WARN] IO管理器未初始化，仅更新UI显示 - O0002: ON
[2025-09-19 14:03:13.963] [WARN] IO管理器未初始化，仅更新UI显示 - O0002: OFF
[2025-09-19 14:03:15.659] [WARN] IO管理器未初始化，仅更新UI显示 - O0003: ON
[2025-09-19 14:03:16.090] [WARN] IO管理器未初始化，仅更新UI显示 - O0003: OFF
[2025-09-19 14:03:16.698] [WARN] IO管理器未初始化，仅更新UI显示 - O0004: ON
[2025-09-19 14:03:17.074] [WARN] IO管理器未初始化，仅更新UI显示 - O0004: OFF
[2025-09-19 14:03:17.754] [WARN] IO管理器未初始化，仅更新UI显示 - O0005: ON
[2025-09-19 14:03:18.107] [WARN] IO管理器未初始化，仅更新UI显示 - O0005: OFF
[2025-09-19 14:03:18.602] [WARN] IO管理器未初始化，仅更新UI显示 - O0006: ON
[2025-09-19 14:03:18.914] [WARN] IO管理器未初始化，仅更新UI显示 - O0006: OFF
[2025-09-19 14:03:43.535] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
[2025-09-19 14:03:49.004] [INFO] IO输出控制面板初始化完成 - 支持基础IO和扩展IO
[2025-09-19 14:23:03.601] [INFO] 程序启动开始
[2025-09-19 14:23:03.603] [INFO] 配置系统初始化成功
[2025-09-19 14:23:03.653] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-19 14:23:03.653] [INFO] 配置系统初始化完成
[2025-09-19 14:23:03.654] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-19 14:23:03.676] [INFO] 开始初始化各个Manager...
[2025-09-19 14:23:03.676] [INFO] 初始化基础Manager...
[2025-09-19 14:23:03.681] [INFO] 开始初始化IOManager...
[2025-09-19 14:23:03.683] [INFO] 模拟初始化雷赛卡，卡号: 0
[2025-09-19 14:23:03.796] [INFO] IO监控任务已启动
[2025-09-19 14:23:03.797] [INFO] IOManager初始化完成
[2025-09-19 14:23:03.798] [INFO] IO监控循环开始
[2025-09-19 14:23:03.799] [INFO] 开始初始化MotorManager...
[2025-09-19 14:23:03.813] [INFO] 模拟初始化运动控制卡
[2025-09-19 14:23:04.033] [INFO] 加载了8个电机的默认配置
[2025-09-19 14:23:04.034] [INFO] 电机监控任务已启动
[2025-09-19 14:23:04.034] [INFO] MotorManager初始化完成
[2025-09-19 14:23:04.034] [INFO] 初始化通信Manager...
[2025-09-19 14:23:04.035] [INFO] 电机监控循环开始
[2025-09-19 14:23:04.037] [INFO] 开始初始化ScannerManager...
[2025-09-19 14:23:04.039] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-19 14:23:04.042] [INFO] 串口初始化完成
[2025-09-19 14:23:04.049] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-19 14:23:04.053] [INFO] 扫描枪连接成功: COM1
[2025-09-19 14:23:04.054] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-19 14:23:04.054] [INFO] ScannerManager初始化完成
[2025-09-19 14:23:04.059] [INFO] 开始初始化ModbusTcpManager...
[2025-09-19 14:23:04.060] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-19 14:23:04.063] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-19 14:23:09.156] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 14:23:09.168] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 14:23:09.168] [INFO] ModbusTcpManager初始化完成
[2025-09-19 14:23:09.170] [INFO] 开始初始化RobotTcpManager...
[2025-09-19 14:23:09.171] [INFO] 机器人TCP配置: 192.168.1.100:8080
[2025-09-19 14:23:09.173] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-19 14:23:14.260] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: 192.168.1.100:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 14:23:14.262] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 14:23:14.262] [INFO] RobotTcpManager初始化完成
[2025-09-19 14:23:14.263] [INFO] 初始化视觉Manager...
[2025-09-19 14:23:14.266] [INFO] 开始初始化VisionManager...
[2025-09-19 14:23:14.266] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-19 14:23:14.267] [INFO] 模拟初始化相机，索引: 0
[2025-09-19 14:23:14.822] [INFO] 相机初始化成功
[2025-09-19 14:23:14.824] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-19 14:23:14.824] [INFO] 视觉配置加载完成
[2025-09-19 14:23:14.824] [INFO] VisionManager初始化完成
[2025-09-19 14:23:14.824] [INFO] 初始化数据Manager...
[2025-09-19 14:23:14.828] [INFO] 开始初始化StatisticsManager...
[2025-09-19 14:23:14.828] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-19 14:23:14.832] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-19 14:23:14.833] [INFO] 历史数据加载完成
[2025-09-19 14:23:14.833] [INFO] 自动保存任务已启动
[2025-09-19 14:23:14.833] [INFO] StatisticsManager初始化完成
[2025-09-19 14:23:14.834] [INFO] Manager初始化完成，成功率: 100%
[2025-09-19 14:23:14.834] [INFO] 所有Manager初始化完成
[2025-09-19 14:23:14.835] [INFO] 自动保存循环开始
[2025-09-19 14:23:14.875] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 14:23:14.875] [INFO] 主界面布局创建完成
[2025-09-19 14:23:14.876] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-19 14:23:20.807] [INFO] IO状态缓存初始化完成
[2025-09-19 14:23:20.814] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
[2025-09-19 14:23:23.062] [INFO] IO输出控制面板初始化完成 - 支持基础IO和扩展IO
[2025-09-19 14:23:47.408] [INFO] 程序启动开始
[2025-09-19 14:23:47.410] [INFO] 配置系统初始化成功
[2025-09-19 14:23:47.458] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-19 14:23:47.458] [INFO] 配置系统初始化完成
[2025-09-19 14:23:47.458] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-19 14:23:47.479] [INFO] 开始初始化各个Manager...
[2025-09-19 14:23:47.480] [INFO] 初始化基础Manager...
[2025-09-19 14:23:47.484] [INFO] 开始初始化IOManager...
[2025-09-19 14:23:47.486] [INFO] 模拟初始化雷赛卡，卡号: 0
[2025-09-19 14:23:47.598] [INFO] IO监控任务已启动
[2025-09-19 14:23:47.598] [INFO] IOManager初始化完成
[2025-09-19 14:23:47.600] [INFO] IO监控循环开始
[2025-09-19 14:23:47.601] [INFO] 开始初始化MotorManager...
[2025-09-19 14:23:47.604] [INFO] 模拟初始化运动控制卡
[2025-09-19 14:23:47.836] [INFO] 加载了8个电机的默认配置
[2025-09-19 14:23:47.837] [INFO] 电机监控任务已启动
[2025-09-19 14:23:47.838] [INFO] MotorManager初始化完成
[2025-09-19 14:23:47.838] [INFO] 初始化通信Manager...
[2025-09-19 14:23:47.850] [INFO] 开始初始化ScannerManager...
[2025-09-19 14:23:47.851] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-19 14:23:47.854] [INFO] 串口初始化完成
[2025-09-19 14:23:47.856] [INFO] 电机监控循环开始
[2025-09-19 14:23:47.857] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-19 14:23:47.863] [INFO] 扫描枪连接成功: COM1
[2025-09-19 14:23:47.864] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-19 14:23:47.864] [INFO] ScannerManager初始化完成
[2025-09-19 14:23:47.868] [INFO] 开始初始化ModbusTcpManager...
[2025-09-19 14:23:47.870] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-19 14:23:47.874] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-19 14:23:52.945] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 14:23:52.960] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 14:23:52.960] [INFO] ModbusTcpManager初始化完成
[2025-09-19 14:23:52.963] [INFO] 开始初始化RobotTcpManager...
[2025-09-19 14:23:52.963] [INFO] 机器人TCP配置: 192.168.1.100:8080
[2025-09-19 14:23:52.965] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-19 14:23:58.007] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: 192.168.1.100:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 14:23:58.008] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 14:23:58.008] [INFO] RobotTcpManager初始化完成
[2025-09-19 14:23:58.008] [INFO] 初始化视觉Manager...
[2025-09-19 14:23:58.011] [INFO] 开始初始化VisionManager...
[2025-09-19 14:23:58.011] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-19 14:23:58.012] [INFO] 模拟初始化相机，索引: 0
[2025-09-19 14:23:58.528] [INFO] 相机初始化成功
[2025-09-19 14:23:58.530] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-19 14:23:58.530] [INFO] 视觉配置加载完成
[2025-09-19 14:23:58.531] [INFO] VisionManager初始化完成
[2025-09-19 14:23:58.531] [INFO] 初始化数据Manager...
[2025-09-19 14:23:58.535] [INFO] 开始初始化StatisticsManager...
[2025-09-19 14:23:58.535] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-19 14:23:58.540] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-19 14:23:58.540] [INFO] 历史数据加载完成
[2025-09-19 14:23:58.541] [INFO] 自动保存任务已启动
[2025-09-19 14:23:58.541] [INFO] StatisticsManager初始化完成
[2025-09-19 14:23:58.541] [INFO] Manager初始化完成，成功率: 100%
[2025-09-19 14:23:58.541] [INFO] 所有Manager初始化完成
[2025-09-19 14:23:58.544] [INFO] 自动保存循环开始
[2025-09-19 14:23:58.579] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 14:23:58.579] [INFO] 主界面布局创建完成
[2025-09-19 14:23:58.580] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-19 14:31:54.241] [INFO] 程序启动开始
[2025-09-19 14:31:54.243] [INFO] 配置系统初始化成功
[2025-09-19 14:31:54.291] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-19 14:31:54.291] [INFO] 配置系统初始化完成
[2025-09-19 14:31:54.291] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-19 14:31:54.315] [INFO] 开始初始化各个Manager...
[2025-09-19 14:31:54.315] [INFO] 初始化基础Manager...
[2025-09-19 14:31:54.321] [INFO] 开始初始化IOManager...
[2025-09-19 14:31:54.324] [INFO] 模拟初始化雷赛卡，卡号: 0
[2025-09-19 14:31:54.430] [INFO] IO监控任务已启动
[2025-09-19 14:31:54.430] [INFO] IOManager初始化完成
[2025-09-19 14:31:54.432] [INFO] IO监控循环开始
[2025-09-19 14:31:54.448] [INFO] 开始初始化MotorManager...
[2025-09-19 14:31:54.462] [INFO] 模拟初始化运动控制卡
[2025-09-19 14:31:54.683] [INFO] 加载了8个电机的默认配置
[2025-09-19 14:31:54.686] [INFO] 电机监控任务已启动
[2025-09-19 14:31:54.687] [INFO] 电机监控循环开始
[2025-09-19 14:31:54.687] [INFO] MotorManager初始化完成
[2025-09-19 14:31:54.688] [INFO] 初始化通信Manager...
[2025-09-19 14:31:54.694] [INFO] 开始初始化ScannerManager...
[2025-09-19 14:31:54.696] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-19 14:31:54.698] [INFO] 串口初始化完成
[2025-09-19 14:31:54.700] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-19 14:31:54.703] [INFO] 扫描枪连接成功: COM1
[2025-09-19 14:31:54.703] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-19 14:31:54.704] [INFO] ScannerManager初始化完成
[2025-09-19 14:31:54.706] [INFO] 开始初始化ModbusTcpManager...
[2025-09-19 14:31:54.708] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-19 14:31:54.711] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-19 14:31:59.818] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 14:31:59.836] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 14:31:59.837] [INFO] ModbusTcpManager初始化完成
[2025-09-19 14:31:59.839] [INFO] 开始初始化RobotTcpManager...
[2025-09-19 14:31:59.840] [INFO] 机器人TCP配置: 192.168.1.100:8080
[2025-09-19 14:31:59.843] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-19 14:32:04.886] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: 192.168.1.100:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 14:32:04.888] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 14:32:04.888] [INFO] RobotTcpManager初始化完成
[2025-09-19 14:32:04.889] [INFO] 初始化视觉Manager...
[2025-09-19 14:32:04.892] [INFO] 开始初始化VisionManager...
[2025-09-19 14:32:04.892] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-19 14:32:04.894] [INFO] 模拟初始化相机，索引: 0
[2025-09-19 14:32:05.426] [INFO] 相机初始化成功
[2025-09-19 14:32:05.431] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-19 14:32:05.432] [INFO] 视觉配置加载完成
[2025-09-19 14:32:05.433] [INFO] VisionManager初始化完成
[2025-09-19 14:32:05.434] [INFO] 初始化数据Manager...
[2025-09-19 14:32:05.450] [INFO] 开始初始化StatisticsManager...
[2025-09-19 14:32:05.453] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-19 14:32:05.468] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-19 14:32:05.469] [INFO] 历史数据加载完成
[2025-09-19 14:32:05.469] [INFO] 自动保存任务已启动
[2025-09-19 14:32:05.470] [INFO] StatisticsManager初始化完成
[2025-09-19 14:32:05.470] [INFO] Manager初始化完成，成功率: 100%
[2025-09-19 14:32:05.470] [INFO] 所有Manager初始化完成
[2025-09-19 14:32:05.472] [INFO] 自动保存循环开始
[2025-09-19 14:32:05.527] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 14:32:05.527] [INFO] 主界面布局创建完成
[2025-09-19 14:32:05.528] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-19 14:32:08.909] [INFO] IO状态缓存初始化完成
[2025-09-19 14:32:08.915] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
[2025-09-19 14:32:14.672] [INFO] IO输出控制面板初始化完成 - 支持基础IO和扩展IO
[2025-09-19 14:32:49.970] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
[2025-09-19 14:32:52.671] [INFO] IO输出控制面板初始化完成 - 支持基础IO和扩展IO
[2025-09-19 15:49:46.183] [INFO] 程序启动开始
[2025-09-19 15:49:46.185] [INFO] 配置系统初始化成功
[2025-09-19 15:49:46.238] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-19 15:49:46.238] [INFO] 配置系统初始化完成
[2025-09-19 15:49:46.238] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-19 15:49:46.261] [INFO] 开始初始化各个Manager...
[2025-09-19 15:49:46.262] [INFO] 初始化基础Manager...
[2025-09-19 15:49:46.268] [INFO] 开始初始化IOManager...
[2025-09-19 15:49:46.270] [INFO] 模拟初始化雷赛卡，卡号: 0
[2025-09-19 15:49:46.380] [INFO] IO监控任务已启动
[2025-09-19 15:49:46.380] [INFO] IOManager初始化完成
[2025-09-19 15:49:46.383] [INFO] IO监控循环开始
[2025-09-19 15:49:46.385] [INFO] 开始初始化MotorManager...
[2025-09-19 15:49:46.387] [INFO] 模拟初始化运动控制卡
[2025-09-19 15:49:46.599] [INFO] 加载了8个电机的默认配置
[2025-09-19 15:49:46.600] [INFO] 电机监控任务已启动
[2025-09-19 15:49:46.600] [INFO] MotorManager初始化完成
[2025-09-19 15:49:46.600] [INFO] 初始化通信Manager...
[2025-09-19 15:49:46.602] [INFO] 电机监控循环开始
[2025-09-19 15:49:46.604] [INFO] 开始初始化ScannerManager...
[2025-09-19 15:49:46.606] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-19 15:49:46.615] [INFO] 串口初始化完成
[2025-09-19 15:49:46.618] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-19 15:49:46.633] [INFO] 扫描枪连接成功: COM1
[2025-09-19 15:49:46.634] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-19 15:49:46.635] [INFO] ScannerManager初始化完成
[2025-09-19 15:49:46.647] [INFO] 开始初始化ModbusTcpManager...
[2025-09-19 15:49:46.650] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-19 15:49:46.653] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-19 15:49:51.704] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 15:49:51.720] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 15:49:51.720] [INFO] ModbusTcpManager初始化完成
[2025-09-19 15:49:51.722] [INFO] 开始初始化RobotTcpManager...
[2025-09-19 15:49:51.723] [INFO] 机器人TCP配置: 192.168.1.100:8080
[2025-09-19 15:49:51.726] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-19 15:49:56.777] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: 192.168.1.100:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 15:49:56.780] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 15:49:56.780] [INFO] RobotTcpManager初始化完成
[2025-09-19 15:49:56.780] [INFO] 初始化视觉Manager...
[2025-09-19 15:49:56.786] [INFO] 开始初始化VisionManager...
[2025-09-19 15:49:56.786] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-19 15:49:56.788] [INFO] 模拟初始化相机，索引: 0
[2025-09-19 15:49:57.313] [INFO] 相机初始化成功
[2025-09-19 15:49:57.316] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-19 15:49:57.317] [INFO] 视觉配置加载完成
[2025-09-19 15:49:57.318] [INFO] VisionManager初始化完成
[2025-09-19 15:49:57.318] [INFO] 初始化数据Manager...
[2025-09-19 15:49:57.335] [INFO] 开始初始化StatisticsManager...
[2025-09-19 15:49:57.336] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-19 15:49:57.341] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-19 15:49:57.342] [INFO] 历史数据加载完成
[2025-09-19 15:49:57.342] [INFO] 自动保存任务已启动
[2025-09-19 15:49:57.343] [INFO] StatisticsManager初始化完成
[2025-09-19 15:49:57.343] [INFO] Manager初始化完成，成功率: 100%
[2025-09-19 15:49:57.343] [INFO] 所有Manager初始化完成
[2025-09-19 15:49:57.345] [INFO] 自动保存循环开始
[2025-09-19 15:49:57.384] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 15:49:57.384] [INFO] 主界面布局创建完成
[2025-09-19 15:49:57.384] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-19 15:50:05.947] [INFO] 开始初始化EpsonRobotManager...
[2025-09-19 15:50:05.949] [INFO] Epson机器人配置 - 启动/停止: 192.168.1.100:5000, 数据收发: 192.168.1.101:5001
[2025-09-19 15:50:05.949] [INFO] EpsonRobotManager初始化完成
[2025-09-19 15:50:05.949] [INFO] Epson机器人管理器初始化完成
[2025-09-19 15:50:05.987] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-19 16:47:13.395] [INFO] 程序启动开始
[2025-09-19 16:47:13.396] [INFO] 配置系统初始化成功
[2025-09-19 16:47:13.460] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-19 16:47:13.460] [INFO] 配置系统初始化完成
[2025-09-19 16:47:13.461] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-19 16:47:13.485] [INFO] 开始初始化各个Manager...
[2025-09-19 16:47:13.486] [INFO] 初始化基础Manager...
[2025-09-19 16:47:13.493] [INFO] 开始初始化IOManager...
[2025-09-19 16:47:13.499] [INFO] 模拟初始化雷赛卡，卡号: 0
[2025-09-19 16:47:13.622] [INFO] IO监控任务已启动
[2025-09-19 16:47:13.622] [INFO] IOManager初始化完成
[2025-09-19 16:47:13.624] [INFO] IO监控循环开始
[2025-09-19 16:47:13.627] [INFO] 开始初始化MotorManager...
[2025-09-19 16:47:13.639] [INFO] 模拟初始化运动控制卡
[2025-09-19 16:47:13.874] [INFO] 加载了8个电机的默认配置
[2025-09-19 16:47:13.875] [INFO] 电机监控任务已启动
[2025-09-19 16:47:13.875] [INFO] MotorManager初始化完成
[2025-09-19 16:47:13.875] [INFO] 初始化通信Manager...
[2025-09-19 16:47:13.877] [INFO] 电机监控循环开始
[2025-09-19 16:47:13.878] [INFO] 开始初始化ScannerManager...
[2025-09-19 16:47:13.879] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-19 16:47:13.881] [INFO] 串口初始化完成
[2025-09-19 16:47:13.883] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-19 16:47:13.887] [INFO] 扫描枪连接成功: COM1
[2025-09-19 16:47:13.887] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-19 16:47:13.888] [INFO] ScannerManager初始化完成
[2025-09-19 16:47:13.890] [INFO] 开始初始化ModbusTcpManager...
[2025-09-19 16:47:13.892] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-19 16:47:13.896] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-19 16:47:18.962] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 16:47:18.976] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 16:47:18.977] [INFO] ModbusTcpManager初始化完成
[2025-09-19 16:47:18.979] [INFO] 开始初始化RobotTcpManager...
[2025-09-19 16:47:18.980] [INFO] 机器人TCP配置: 192.168.1.100:8080
[2025-09-19 16:47:18.982] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-19 16:47:24.024] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: 192.168.1.100:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 16:47:24.026] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 16:47:24.026] [INFO] RobotTcpManager初始化完成
[2025-09-19 16:47:24.027] [INFO] 初始化视觉Manager...
[2025-09-19 16:47:24.031] [INFO] 开始初始化VisionManager...
[2025-09-19 16:47:24.032] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-19 16:47:24.034] [INFO] 模拟初始化相机，索引: 0
[2025-09-19 16:47:24.577] [INFO] 相机初始化成功
[2025-09-19 16:47:24.579] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-19 16:47:24.579] [INFO] 视觉配置加载完成
[2025-09-19 16:47:24.579] [INFO] VisionManager初始化完成
[2025-09-19 16:47:24.580] [INFO] 初始化数据Manager...
[2025-09-19 16:47:24.587] [INFO] 开始初始化StatisticsManager...
[2025-09-19 16:47:24.588] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-19 16:47:24.600] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-19 16:47:24.600] [INFO] 历史数据加载完成
[2025-09-19 16:47:24.601] [INFO] 自动保存任务已启动
[2025-09-19 16:47:24.601] [INFO] StatisticsManager初始化完成
[2025-09-19 16:47:24.602] [INFO] Manager初始化完成，成功率: 100%
[2025-09-19 16:47:24.603] [INFO] 所有Manager初始化完成
[2025-09-19 16:47:24.604] [INFO] 自动保存循环开始
[2025-09-19 16:47:24.642] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 16:47:24.642] [INFO] 主界面布局创建完成
[2025-09-19 16:47:24.642] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-19 19:55:44.049] [INFO] 程序启动开始
[2025-09-19 19:55:44.052] [INFO] 配置系统初始化成功
[2025-09-19 19:55:44.122] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-19 19:55:44.122] [INFO] 配置系统初始化完成
[2025-09-19 19:55:44.122] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-19 19:55:44.157] [INFO] 开始初始化各个Manager...
[2025-09-19 19:55:44.157] [INFO] 初始化基础Manager...
[2025-09-19 19:55:44.162] [INFO] 开始初始化IOManager...
[2025-09-19 19:55:44.164] [INFO] 模拟初始化雷赛卡，卡号: 0
[2025-09-19 19:55:44.278] [INFO] IO监控任务已启动
[2025-09-19 19:55:44.278] [INFO] IOManager初始化完成
[2025-09-19 19:55:44.280] [INFO] IO监控循环开始
[2025-09-19 19:55:44.285] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-19 19:55:44.660] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败，返回卡数: 0
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__44_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 19:55:44.690] [WARN] DMC1000B电机管理器初始化失败
[2025-09-19 19:55:44.694] [INFO] 开始初始化MotorManager...
[2025-09-19 19:55:44.696] [INFO] 模拟初始化运动控制卡
[2025-09-19 19:55:44.944] [INFO] 加载了8个电机的默认配置
[2025-09-19 19:55:44.945] [INFO] 电机监控任务已启动
[2025-09-19 19:55:44.945] [INFO] MotorManager初始化完成
[2025-09-19 19:55:44.946] [INFO] 初始化通信Manager...
[2025-09-19 19:55:44.950] [INFO] 开始初始化ScannerManager...
[2025-09-19 19:55:44.955] [INFO] 电机监控循环开始
[2025-09-19 19:55:44.962] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-19 19:55:44.987] [INFO] 串口初始化完成
[2025-09-19 19:55:44.995] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-19 19:55:45.017] [INFO] 扫描枪连接成功: COM1
[2025-09-19 19:55:45.018] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-19 19:55:45.018] [INFO] ScannerManager初始化完成
[2025-09-19 19:55:45.022] [INFO] 开始初始化ModbusTcpManager...
[2025-09-19 19:55:45.025] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-19 19:55:45.030] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-19 19:55:50.093] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 19:55:50.094] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 19:55:50.095] [INFO] ModbusTcpManager初始化完成
[2025-09-19 19:55:50.097] [INFO] 开始初始化RobotTcpManager...
[2025-09-19 19:55:50.097] [INFO] 机器人TCP配置: 192.168.1.100:8080
[2025-09-19 19:55:50.100] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-19 19:55:55.168] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: 192.168.1.100:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 19:55:55.171] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 19:55:55.172] [INFO] RobotTcpManager初始化完成
[2025-09-19 19:55:55.172] [INFO] 初始化视觉Manager...
[2025-09-19 19:55:55.176] [INFO] 开始初始化VisionManager...
[2025-09-19 19:55:55.177] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-19 19:55:55.178] [INFO] 模拟初始化相机，索引: 0
[2025-09-19 19:55:55.712] [INFO] 相机初始化成功
[2025-09-19 19:55:55.715] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-19 19:55:55.715] [INFO] 视觉配置加载完成
[2025-09-19 19:55:55.716] [INFO] VisionManager初始化完成
[2025-09-19 19:55:55.716] [INFO] 初始化数据Manager...
[2025-09-19 19:55:55.721] [INFO] 开始初始化StatisticsManager...
[2025-09-19 19:55:55.721] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-19 19:55:55.728] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-19 19:55:55.728] [INFO] 历史数据加载完成
[2025-09-19 19:55:55.729] [INFO] 自动保存任务已启动
[2025-09-19 19:55:55.729] [INFO] StatisticsManager初始化完成
[2025-09-19 19:55:55.730] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-19 19:55:55.730] [INFO] 所有Manager初始化完成
[2025-09-19 19:55:55.732] [INFO] 自动保存循环开始
[2025-09-19 19:55:55.782] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 19:55:55.783] [INFO] 主界面布局创建完成
[2025-09-19 19:55:55.783] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-19 20:47:31.378] [INFO] 程序启动开始
[2025-09-19 20:47:31.382] [INFO] 配置系统初始化成功
[2025-09-19 20:47:31.440] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-19 20:47:31.441] [INFO] 配置系统初始化完成
[2025-09-19 20:47:31.441] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-19 20:47:31.464] [INFO] 开始初始化各个Manager...
[2025-09-19 20:47:31.464] [INFO] 初始化基础Manager...
[2025-09-19 20:47:31.469] [INFO] 开始初始化IOManager...
[2025-09-19 20:47:31.471] [INFO] 模拟初始化雷赛卡，卡号: 0
[2025-09-19 20:47:31.580] [INFO] IO监控任务已启动
[2025-09-19 20:47:31.580] [INFO] IOManager初始化完成
[2025-09-19 20:47:31.582] [INFO] IO监控循环开始
[2025-09-19 20:47:31.585] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-19 20:47:31.932] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败，返回卡数: 0
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__44_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 20:47:31.957] [WARN] DMC1000B电机管理器初始化失败
[2025-09-19 20:47:31.960] [INFO] 开始初始化MotorManager...
[2025-09-19 20:47:31.962] [INFO] 模拟初始化运动控制卡
[2025-09-19 20:47:32.175] [INFO] 加载了8个电机的默认配置
[2025-09-19 20:47:32.176] [INFO] 电机监控任务已启动
[2025-09-19 20:47:32.176] [INFO] MotorManager初始化完成
[2025-09-19 20:47:32.177] [INFO] 初始化通信Manager...
[2025-09-19 20:47:32.178] [INFO] 电机监控循环开始
[2025-09-19 20:47:32.179] [INFO] 开始初始化ScannerManager...
[2025-09-19 20:47:32.183] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-19 20:47:32.187] [INFO] 串口初始化完成
[2025-09-19 20:47:32.190] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-19 20:47:32.194] [INFO] 扫描枪连接成功: COM1
[2025-09-19 20:47:32.194] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-19 20:47:32.194] [INFO] ScannerManager初始化完成
[2025-09-19 20:47:32.197] [INFO] 开始初始化ModbusTcpManager...
[2025-09-19 20:47:32.199] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-19 20:47:32.203] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-19 20:47:37.289] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 20:47:37.292] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 20:47:37.292] [INFO] ModbusTcpManager初始化完成
[2025-09-19 20:47:37.296] [INFO] 开始初始化RobotTcpManager...
[2025-09-19 20:47:37.297] [INFO] 机器人TCP配置: 192.168.1.100:8080
[2025-09-19 20:47:37.300] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-19 20:47:42.360] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: 192.168.1.100:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 20:47:42.362] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 20:47:42.364] [INFO] RobotTcpManager初始化完成
[2025-09-19 20:47:42.364] [INFO] 初始化视觉Manager...
[2025-09-19 20:47:42.367] [INFO] 开始初始化VisionManager...
[2025-09-19 20:47:42.368] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-19 20:47:42.369] [INFO] 模拟初始化相机，索引: 0
[2025-09-19 20:47:42.916] [INFO] 相机初始化成功
[2025-09-19 20:47:42.917] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-19 20:47:42.917] [INFO] 视觉配置加载完成
[2025-09-19 20:47:42.917] [INFO] VisionManager初始化完成
[2025-09-19 20:47:42.918] [INFO] 初始化数据Manager...
[2025-09-19 20:47:42.920] [INFO] 开始初始化StatisticsManager...
[2025-09-19 20:47:42.921] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-19 20:47:42.925] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-19 20:47:42.925] [INFO] 历史数据加载完成
[2025-09-19 20:47:42.925] [INFO] 自动保存任务已启动
[2025-09-19 20:47:42.925] [INFO] StatisticsManager初始化完成
[2025-09-19 20:47:42.925] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-19 20:47:42.925] [INFO] 所有Manager初始化完成
[2025-09-19 20:47:42.927] [INFO] 自动保存循环开始
[2025-09-19 20:47:42.970] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 20:47:42.971] [INFO] 主界面布局创建完成
[2025-09-19 20:47:42.971] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-19 20:47:46.970] [INFO] IO状态缓存初始化完成
[2025-09-19 20:47:46.996] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
[2025-09-19 20:47:50.953] [INFO] IO输出控制面板初始化完成 - 支持基础IO和扩展IO
[2025-09-19 22:57:48.498] [INFO] 程序启动开始
[2025-09-19 22:57:48.500] [INFO] 配置系统初始化成功
[2025-09-19 22:57:48.552] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-19 22:57:48.552] [INFO] 配置系统初始化完成
[2025-09-19 22:57:48.552] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-19 22:57:48.577] [INFO] 开始初始化各个Manager...
[2025-09-19 22:57:48.577] [INFO] 初始化基础Manager...
[2025-09-19 22:57:48.582] [INFO] IO状态缓存初始化完成
[2025-09-19 22:57:48.585] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-19 22:57:48.587] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡
[2025-09-19 22:57:48.587] [WARN] DMC1000BIO管理器初始化失败
[2025-09-19 22:57:48.591] [INFO] 开始初始化IOManager...
[2025-09-19 22:57:48.593] [INFO] 模拟初始化雷赛卡，卡号: 0
[2025-09-19 22:57:48.703] [INFO] IO监控任务已启动
[2025-09-19 22:57:48.703] [INFO] IOManager初始化完成
[2025-09-19 22:57:48.705] [INFO] IO监控循环开始
[2025-09-19 22:57:48.711] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-19 22:57:48.836] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败，返回卡数: 0
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__44_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 22:57:48.859] [WARN] DMC1000B电机管理器初始化失败
[2025-09-19 22:57:48.863] [INFO] 开始初始化MotorManager...
[2025-09-19 22:57:48.866] [INFO] 模拟初始化运动控制卡
[2025-09-19 22:57:49.078] [INFO] 加载了8个电机的默认配置
[2025-09-19 22:57:49.078] [INFO] 电机监控任务已启动
[2025-09-19 22:57:49.078] [INFO] MotorManager初始化完成
[2025-09-19 22:57:49.079] [INFO] 初始化通信Manager...
[2025-09-19 22:57:49.080] [INFO] 电机监控循环开始
[2025-09-19 22:57:49.081] [INFO] 开始初始化ScannerManager...
[2025-09-19 22:57:49.084] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-19 22:57:49.088] [INFO] 串口初始化完成
[2025-09-19 22:57:49.091] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-19 22:57:49.093] [INFO] 扫描枪连接成功: COM1
[2025-09-19 22:57:49.094] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-19 22:57:49.094] [INFO] ScannerManager初始化完成
[2025-09-19 22:57:49.097] [INFO] 开始初始化ModbusTcpManager...
[2025-09-19 22:57:49.099] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-19 22:57:49.102] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-19 22:57:54.170] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 22:57:54.173] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 22:57:54.173] [INFO] ModbusTcpManager初始化完成
[2025-09-19 22:57:54.176] [INFO] 开始初始化RobotTcpManager...
[2025-09-19 22:57:54.177] [INFO] 机器人TCP配置: 192.168.1.100:8080
[2025-09-19 22:57:54.179] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-19 22:57:59.240] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: 192.168.1.100:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 22:57:59.242] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 22:57:59.243] [INFO] RobotTcpManager初始化完成
[2025-09-19 22:57:59.243] [INFO] 初始化视觉Manager...
[2025-09-19 22:57:59.247] [INFO] 开始初始化VisionManager...
[2025-09-19 22:57:59.247] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-19 22:57:59.249] [INFO] 模拟初始化相机，索引: 0
[2025-09-19 22:57:59.799] [INFO] 相机初始化成功
[2025-09-19 22:57:59.803] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-19 22:57:59.803] [INFO] 视觉配置加载完成
[2025-09-19 22:57:59.804] [INFO] VisionManager初始化完成
[2025-09-19 22:57:59.804] [INFO] 初始化数据Manager...
[2025-09-19 22:57:59.814] [INFO] 开始初始化StatisticsManager...
[2025-09-19 22:57:59.816] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-19 22:57:59.824] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-19 22:57:59.824] [INFO] 历史数据加载完成
[2025-09-19 22:57:59.825] [INFO] 自动保存任务已启动
[2025-09-19 22:57:59.825] [INFO] StatisticsManager初始化完成
[2025-09-19 22:57:59.825] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-19 22:57:59.826] [INFO] 所有Manager初始化完成
[2025-09-19 22:57:59.827] [INFO] 自动保存循环开始
[2025-09-19 22:57:59.887] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 22:57:59.887] [INFO] 主界面布局创建完成
[2025-09-19 22:57:59.888] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-19 22:58:04.835] [INFO] 对位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 22:58:06.955] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 22:58:11.789] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 22:58:11.790] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-19 22:58:11.798] [INFO] 翻转电机轴0参数设置成功: 脉冲当量=1000pulse/°, 最大速度=90°/s
[2025-09-19 22:58:11.798] [INFO] 翻转电机轴1参数设置成功: 脉冲当量=1000pulse/°, 最大速度=90°/s
[2025-09-19 22:58:11.798] [INFO] 翻转电机参数初始化完成
[2025-09-19 22:58:11.799] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-19 22:58:12.661] [INFO] 翻转电机控制面板资源释放完成
[2025-09-19 22:58:12.665] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 22:58:13.327] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 22:58:13.328] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-19 22:58:13.331] [INFO] 翻转电机轴0参数设置成功: 脉冲当量=1000pulse/°, 最大速度=90°/s
[2025-09-19 22:58:13.331] [INFO] 翻转电机轴1参数设置成功: 脉冲当量=1000pulse/°, 最大速度=90°/s
[2025-09-19 22:58:13.331] [INFO] 翻转电机参数初始化完成
[2025-09-19 22:58:13.331] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-19 22:58:18.364] [INFO] 翻转电机控制面板资源释放完成
[2025-09-19 22:58:18.372] [INFO] 开始初始化EpsonRobotManager...
[2025-09-19 22:58:18.373] [INFO] Epson机器人配置 - 启动/停止: 192.168.1.100:5000, 数据收发: 192.168.1.101:5001
[2025-09-19 22:58:18.374] [INFO] EpsonRobotManager初始化完成
[2025-09-19 22:58:18.374] [INFO] Epson机器人管理器初始化完成
[2025-09-19 22:58:18.403] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-19 22:58:19.385] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 22:58:19.386] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-19 22:58:19.386] [INFO] 翻转电机轴0参数设置成功: 脉冲当量=1000pulse/°, 最大速度=90°/s
[2025-09-19 22:58:19.386] [INFO] 翻转电机轴1参数设置成功: 脉冲当量=1000pulse/°, 最大速度=90°/s
[2025-09-19 22:58:19.386] [INFO] 翻转电机参数初始化完成
[2025-09-19 22:58:19.386] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-19 22:58:21.549] [INFO] 翻转电机控制面板资源释放完成
[2025-09-19 22:58:21.552] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-19 22:58:21.552] [INFO] Epson机器人管理器初始化完成
[2025-09-19 22:58:21.555] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-19 22:58:23.159] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-19 22:58:23.741] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-19 22:58:23.742] [INFO] Epson机器人管理器初始化完成
[2025-09-19 22:58:23.744] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-19 22:58:25.303] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-19 22:58:25.838] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-19 22:58:25.838] [INFO] Epson机器人管理器初始化完成
[2025-09-19 22:58:25.842] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-19 22:58:31.032] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-19 22:58:31.518] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-19 22:58:31.518] [INFO] Epson机器人管理器初始化完成
[2025-09-19 22:58:31.521] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-19 22:58:42.697] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 22:58:42.697] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-19 22:58:42.697] [INFO] 翻转电机轴0参数设置成功: 脉冲当量=1000pulse/°, 最大速度=90°/s
[2025-09-19 22:58:42.697] [INFO] 翻转电机轴1参数设置成功: 脉冲当量=1000pulse/°, 最大速度=90°/s
[2025-09-19 22:58:42.697] [INFO] 翻转电机参数初始化完成
[2025-09-19 22:58:42.697] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-19 22:58:43.341] [INFO] 翻转电机控制面板资源释放完成
[2025-09-19 22:58:43.344] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-19 22:58:43.344] [INFO] Epson机器人管理器初始化完成
[2025-09-19 22:58:43.347] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-19 22:59:20.135] [INFO] 程序启动开始
[2025-09-19 22:59:20.136] [INFO] 配置系统初始化成功
[2025-09-19 22:59:20.184] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-19 22:59:20.184] [INFO] 配置系统初始化完成
[2025-09-19 22:59:20.184] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-19 22:59:20.206] [INFO] 开始初始化各个Manager...
[2025-09-19 22:59:20.207] [INFO] 初始化基础Manager...
[2025-09-19 22:59:20.212] [INFO] IO状态缓存初始化完成
[2025-09-19 22:59:20.215] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-19 22:59:20.217] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡
[2025-09-19 22:59:20.217] [WARN] DMC1000BIO管理器初始化失败
[2025-09-19 22:59:20.220] [INFO] 开始初始化IOManager...
[2025-09-19 22:59:20.222] [INFO] 模拟初始化雷赛卡，卡号: 0
[2025-09-19 22:59:20.340] [INFO] IO监控任务已启动
[2025-09-19 22:59:20.341] [INFO] IOManager初始化完成
[2025-09-19 22:59:20.342] [INFO] IO监控循环开始
[2025-09-19 22:59:20.347] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-19 22:59:20.409] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败，返回卡数: 0
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__44_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 22:59:20.425] [WARN] DMC1000B电机管理器初始化失败
[2025-09-19 22:59:20.428] [INFO] 开始初始化MotorManager...
[2025-09-19 22:59:20.429] [INFO] 模拟初始化运动控制卡
[2025-09-19 22:59:20.642] [INFO] 加载了8个电机的默认配置
[2025-09-19 22:59:20.644] [INFO] 电机监控任务已启动
[2025-09-19 22:59:20.644] [INFO] MotorManager初始化完成
[2025-09-19 22:59:20.644] [INFO] 初始化通信Manager...
[2025-09-19 22:59:20.647] [INFO] 开始初始化ScannerManager...
[2025-09-19 22:59:20.647] [INFO] 电机监控循环开始
[2025-09-19 22:59:20.649] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-19 22:59:20.659] [INFO] 串口初始化完成
[2025-09-19 22:59:20.663] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-19 22:59:20.667] [INFO] 扫描枪连接成功: COM1
[2025-09-19 22:59:20.675] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-19 22:59:20.676] [INFO] ScannerManager初始化完成
[2025-09-19 22:59:20.690] [INFO] 开始初始化ModbusTcpManager...
[2025-09-19 22:59:20.693] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-19 22:59:20.701] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-19 22:59:25.817] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 22:59:25.818] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 22:59:25.818] [INFO] ModbusTcpManager初始化完成
[2025-09-19 22:59:25.821] [INFO] 开始初始化RobotTcpManager...
[2025-09-19 22:59:25.821] [INFO] 机器人TCP配置: 192.168.1.100:8080
[2025-09-19 22:59:25.824] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-19 22:59:30.859] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: 192.168.1.100:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-19 22:59:30.861] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-19 22:59:30.861] [INFO] RobotTcpManager初始化完成
[2025-09-19 22:59:30.861] [INFO] 初始化视觉Manager...
[2025-09-19 22:59:30.865] [INFO] 开始初始化VisionManager...
[2025-09-19 22:59:30.865] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-19 22:59:30.866] [INFO] 模拟初始化相机，索引: 0
[2025-09-19 22:59:31.414] [INFO] 相机初始化成功
[2025-09-19 22:59:31.416] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-19 22:59:31.416] [INFO] 视觉配置加载完成
[2025-09-19 22:59:31.416] [INFO] VisionManager初始化完成
[2025-09-19 22:59:31.416] [INFO] 初始化数据Manager...
[2025-09-19 22:59:31.420] [INFO] 开始初始化StatisticsManager...
[2025-09-19 22:59:31.421] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-19 22:59:31.426] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-19 22:59:31.426] [INFO] 历史数据加载完成
[2025-09-19 22:59:31.426] [INFO] 自动保存任务已启动
[2025-09-19 22:59:31.426] [INFO] StatisticsManager初始化完成
[2025-09-19 22:59:31.427] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-19 22:59:31.427] [INFO] 所有Manager初始化完成
[2025-09-19 22:59:31.428] [INFO] 自动保存循环开始
[2025-09-19 22:59:31.466] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 22:59:31.467] [INFO] 主界面布局创建完成
[2025-09-19 22:59:31.467] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-19 22:59:33.135] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 22:59:33.136] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-19 22:59:33.141] [INFO] 翻转电机轴0参数设置成功: 脉冲当量=1000pulse/°, 最大速度=90°/s
[2025-09-19 22:59:33.142] [INFO] 翻转电机轴1参数设置成功: 脉冲当量=1000pulse/°, 最大速度=90°/s
[2025-09-19 22:59:33.142] [INFO] 翻转电机参数初始化完成
[2025-09-19 22:59:33.142] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-19 22:59:34.014] [INFO] 翻转电机控制面板资源释放完成
[2025-09-19 22:59:34.028] [INFO] 开始初始化EpsonRobotManager...
[2025-09-19 22:59:34.030] [INFO] Epson机器人配置 - 启动/停止: 192.168.1.100:5000, 数据收发: 192.168.1.101:5001
[2025-09-19 22:59:34.030] [INFO] EpsonRobotManager初始化完成
[2025-09-19 22:59:34.030] [INFO] Epson机器人管理器初始化完成
[2025-09-19 22:59:34.038] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-19 22:59:34.674] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-19 22:59:34.675] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-19 22:59:34.675] [INFO] 翻转电机轴0参数设置成功: 脉冲当量=1000pulse/°, 最大速度=90°/s
[2025-09-19 22:59:34.675] [INFO] 翻转电机轴1参数设置成功: 脉冲当量=1000pulse/°, 最大速度=90°/s
[2025-09-19 22:59:34.675] [INFO] 翻转电机参数初始化完成
[2025-09-19 22:59:34.675] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-19 22:59:42.605] [INFO] 翻转电机控制面板资源释放完成
[2025-09-19 22:59:42.634] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
[2025-09-19 22:59:43.729] [INFO] 生产日志管理面板初始化完成 - 按HTML原型设计
[2025-09-19 22:59:44.558] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
[2025-09-19 22:59:48.939] [INFO] IO输出控制面板初始化完成 - 支持基础IO和扩展IO
[2025-09-19 22:59:57.034] [WARN] IO管理器未初始化，仅更新UI显示 - O0001: ON
[2025-09-19 22:59:57.809] [WARN] IO管理器未初始化，仅更新UI显示 - O0001: OFF
[2025-09-19 22:59:58.409] [WARN] IO管理器未初始化，仅更新UI显示 - O0002: ON
[2025-09-19 22:59:58.898] [WARN] IO管理器未初始化，仅更新UI显示 - O0002: OFF
[2025-09-19 22:59:59.546] [WARN] IO管理器未初始化，仅更新UI显示 - O0003: ON
[2025-09-19 22:59:59.914] [WARN] IO管理器未初始化，仅更新UI显示 - O0003: OFF
[2025-09-19 23:02:21.590] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
