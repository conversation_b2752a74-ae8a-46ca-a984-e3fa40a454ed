@echo off
echo ========================================
echo MyHMI 项目编译脚本
echo ========================================

REM 设置编译参数
set CONFIGURATION=Debug
set PLATFORM=x64
set PROJECT_FILE=MyHMI.csproj
set SOLUTION_FILE=MyHMI.sln

echo 编译配置: %CONFIGURATION%
echo 目标平台: %PLATFORM%
echo.

REM 尝试找到 MSBuild
set MSBUILD_PATH=""

REM 检查 Visual Studio 2022
if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
    echo 找到 Visual Studio 2022 Professional MSBuild
    goto :build
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    echo 找到 Visual Studio 2022 Community MSBuild
    goto :build
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
    echo 找到 Visual Studio 2022 Enterprise MSBuild
    goto :build
)

REM 检查 Visual Studio 2019
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe"
    echo 找到 Visual Studio 2019 Professional MSBuild
    goto :build
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
    echo 找到 Visual Studio 2019 Community MSBuild
    goto :build
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
    echo 找到 Visual Studio 2019 Enterprise MSBuild
    goto :build
)

REM 检查 Build Tools
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe"
    echo 找到 Visual Studio 2019 Build Tools MSBuild
    goto :build
)

REM 检查系统 PATH 中的 MSBuild
where msbuild >nul 2>&1
if %errorlevel% == 0 (
    set MSBUILD_PATH=msbuild
    echo 使用系统 PATH 中的 MSBuild
    goto :build
)

echo 错误: 未找到 MSBuild
echo 请安装 Visual Studio 或 Visual Studio Build Tools
pause
exit /b 1

:build
echo.
echo 开始编译...
echo 使用 MSBuild: %MSBUILD_PATH%
echo.

REM 清理之前的编译结果
echo 清理之前的编译结果...
%MSBUILD_PATH% %SOLUTION_FILE% /t:Clean /p:Configuration=%CONFIGURATION% /p:Platform=%PLATFORM% /v:minimal

REM 还原 NuGet 包
echo.
echo 还原 NuGet 包...
if exist "nuget.exe" (
    nuget.exe restore %SOLUTION_FILE%
) else (
    echo 警告: 未找到 nuget.exe，跳过包还原
)

REM 编译项目
echo.
echo 编译项目...
%MSBUILD_PATH% %SOLUTION_FILE% /p:Configuration=%CONFIGURATION% /p:Platform=%PLATFORM% /v:minimal /p:WarningLevel=1

if %errorlevel% == 0 (
    echo.
    echo ========================================
    echo 编译成功！
    echo ========================================
    echo.
    echo 输出目录: bin\%PLATFORM%\%CONFIGURATION%\
    
    REM 检查输出文件
    if exist "bin\%PLATFORM%\%CONFIGURATION%\MyHMI.exe" (
        echo 主程序: MyHMI.exe
        echo 文件大小: 
        dir "bin\%PLATFORM%\%CONFIGURATION%\MyHMI.exe" | find "MyHMI.exe"
    ) else (
        echo 警告: 未找到输出的可执行文件
    )
    
    echo.
    echo 是否要运行程序？(Y/N)
    set /p run_choice=
    if /i "%run_choice%"=="Y" (
        echo 启动程序...
        cd "bin\%PLATFORM%\%CONFIGURATION%"
        start MyHMI.exe
        cd ..\..\..
    )
) else (
    echo.
    echo ========================================
    echo 编译失败！
    echo ========================================
    echo 请检查上面的错误信息
)

echo.
pause
