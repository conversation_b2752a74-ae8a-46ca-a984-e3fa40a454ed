using System;
using System.IO;
using System.Runtime.Serialization.Formatters.Binary;
using MyHMI.Helpers;

namespace MyHMI.Settings
{
    /// <summary>
    /// 统一设置管理器 - 超级简单的参数管理
    /// 使用方式：
    /// 1. 程序启动时调用 Settings.Load()
    /// 2. 任何地方直接访问 Settings.Current.Motor.LeftFlipPulseEquivalent
    /// 3. 修改参数后调用 Settings.Save()
    /// </summary>
    public static class Settings
    {
        #region 私有字段
        internal static AppSettings _current = new AppSettings(); // 改为internal以供迁移工具访问
        private static readonly string _filePath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "HR2", "settings.dat");
        private static readonly object _lockObject = new object();
        #endregion

        #region 公共属性
        /// <summary>
        /// 当前设置实例 - 直接访问所有参数
        /// </summary>
        public static AppSettings Current => _current;

        /// <summary>
        /// 设置文件路径
        /// </summary>
        public static string FilePath => _filePath;

        /// <summary>
        /// 是否已加载设置
        /// </summary>
        public static bool IsLoaded { get; private set; } = false;
        #endregion

        #region 公共方法（测试支持）
        /// <summary>
        /// 获取设置文件路径（供测试使用）
        /// </summary>
        /// <returns>设置文件完整路径</returns>
        public static string GetSettingsFilePath()
        {
            return _filePath;
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 加载设置 - 程序启动时调用
        /// </summary>
        /// <returns>是否成功加载</returns>
        public static bool Load()
        {
            lock (_lockObject)
            {
                try
                {
                    if (File.Exists(_filePath))
                    {
                        using (var fs = new FileStream(_filePath, FileMode.Open))
                        {
                            var formatter = new BinaryFormatter();
                            _current = (AppSettings)formatter.Deserialize(fs);
                        }
                        LogHelper.Info($"设置加载成功: {_filePath}");
                    }
                    else
                    {
                        LogHelper.Info($"设置文件不存在，使用默认设置: {_filePath}");
                        _current = new AppSettings();
                        Save(); // 保存默认设置
                    }

                    IsLoaded = true;
                    return true;
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"加载设置失败: {_filePath}", ex);
                    _current = new AppSettings(); // 使用默认设置
                    IsLoaded = true;
                    return false;
                }
            }
        }

        /// <summary>
        /// 保存设置 - 参数修改后调用
        /// </summary>
        /// <returns>是否成功保存</returns>
        public static bool Save()
        {
            lock (_lockObject)
            {
                try
                {
                    // 确保目录存在
                    string directory = Path.GetDirectoryName(_filePath);
                    if (!Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }

                    using (var fs = new FileStream(_filePath, FileMode.Create))
                    {
                        var formatter = new BinaryFormatter();
                        formatter.Serialize(fs, _current);
                    }

                    LogHelper.Info($"设置保存成功: {_filePath}");
                    return true;
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"保存设置失败: {_filePath}", ex);
                    return false;
                }
            }
        }

        /// <summary>
        /// 重置为默认设置
        /// </summary>
        /// <returns>是否成功重置</returns>
        public static bool Reset()
        {
            lock (_lockObject)
            {
                try
                {
                    _current = new AppSettings();
                    bool saveResult = Save();
                    LogHelper.Info("设置已重置为默认值");
                    return saveResult;
                }
                catch (Exception ex)
                {
                    LogHelper.Error("重置设置失败", ex);
                    return false;
                }
            }
        }

        /// <summary>
        /// 备份当前设置
        /// </summary>
        /// <param name="backupPath">备份文件路径，如果为空则使用默认路径</param>
        /// <returns>是否成功备份</returns>
        public static bool Backup(string backupPath = null)
        {
            lock (_lockObject)
            {
                try
                {
                    if (string.IsNullOrEmpty(backupPath))
                    {
                        string directory = Path.GetDirectoryName(_filePath);
                        string fileName = $"settings_backup_{DateTime.Now:yyyyMMdd_HHmmss}.dat";
                        backupPath = Path.Combine(directory, fileName);
                    }

                    // 确保备份目录存在
                    string backupDirectory = Path.GetDirectoryName(backupPath);
                    if (!Directory.Exists(backupDirectory))
                    {
                        Directory.CreateDirectory(backupDirectory);
                    }

                    using (var fs = new FileStream(backupPath, FileMode.Create))
                    {
                        var formatter = new BinaryFormatter();
                        formatter.Serialize(fs, _current);
                    }

                    LogHelper.Info($"设置备份成功: {backupPath}");
                    return true;
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"备份设置失败: {backupPath}", ex);
                    return false;
                }
            }
        }

        /// <summary>
        /// 从备份文件恢复设置
        /// </summary>
        /// <param name="backupPath">备份文件路径</param>
        /// <returns>是否成功恢复</returns>
        public static bool Restore(string backupPath)
        {
            lock (_lockObject)
            {
                try
                {
                    if (!File.Exists(backupPath))
                    {
                        LogHelper.Warning($"备份文件不存在: {backupPath}");
                        return false;
                    }

                    using (var fs = new FileStream(backupPath, FileMode.Open))
                    {
                        var formatter = new BinaryFormatter();
                        _current = (AppSettings)formatter.Deserialize(fs);
                    }

                    // 保存恢复的设置
                    bool saveResult = Save();
                    LogHelper.Info($"设置恢复成功: {backupPath}");
                    return saveResult;
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"恢复设置失败: {backupPath}", ex);
                    return false;
                }
            }
        }

        /// <summary>
        /// 获取设置文件大小
        /// </summary>
        /// <returns>文件大小（字节），如果文件不存在返回-1</returns>
        public static long GetFileSize()
        {
            try
            {
                if (File.Exists(_filePath))
                {
                    var fileInfo = new FileInfo(_filePath);
                    return fileInfo.Length;
                }
                return -1;
            }
            catch
            {
                return -1;
            }
        }

        /// <summary>
        /// 获取设置文件最后修改时间
        /// </summary>
        /// <returns>最后修改时间，如果文件不存在返回DateTime.MinValue</returns>
        public static DateTime GetLastModifiedTime()
        {
            try
            {
                if (File.Exists(_filePath))
                {
                    return File.GetLastWriteTime(_filePath);
                }
                return DateTime.MinValue;
            }
            catch
            {
                return DateTime.MinValue;
            }
        }

        /// <summary>
        /// 验证设置文件完整性
        /// </summary>
        /// <returns>是否完整</returns>
        public static bool ValidateFile()
        {
            try
            {
                if (!File.Exists(_filePath))
                    return false;

                using (var fs = new FileStream(_filePath, FileMode.Open))
                {
                    var formatter = new BinaryFormatter();
                    var testSettings = (AppSettings)formatter.Deserialize(fs);
                    return testSettings != null;
                }
            }
            catch
            {
                return false;
            }
        }
        #endregion

        #region 便捷方法
        /// <summary>
        /// 快速保存电机参数
        /// </summary>
        public static void SaveMotorSettings()
        {
            Save();
        }

        /// <summary>
        /// 快速保存通信参数
        /// </summary>
        public static void SaveCommunicationSettings()
        {
            Save();
        }

        /// <summary>
        /// 快速保存UI参数
        /// </summary>
        public static void SaveUISettings()
        {
            Save();
        }

        /// <summary>
        /// 快速保存系统参数
        /// </summary>
        public static void SaveSystemSettings()
        {
            Save();
        }
        #endregion
    }
}
