# HR2项目架构总览

## 📋 项目基本信息

**项目名称**: HR2 上位机控制系统 (MyHMI)  
**技术栈**: C# .NET Framework 4.8 + WinForms  
**架构模式**: 分层架构 + 单例模式 + 事件驱动  
**开发环境**: Visual Studio 2019+  
**目标平台**: Windows x64  

## 🏗️ 整体架构设计

### 核心架构模式
采用 **"UI Thread + Background Task + Event Callback"** 模式：
- **UI线程**：只负责界面更新、用户交互，不阻塞
- **后台Task**：所有耗时操作（IO、通信、视觉处理等）在后台线程执行
- **事件回调**：模块间通过事件通信，避免强耦合

### 分层架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    表示层 (UI Layer)                        │
│  MainForm  │  UserControls  │  TestRunnerForm  │  Dialogs   │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层 (Manager Layer)                │
│  WorkflowManager  │  各种硬件Manager  │  StatisticsManager  │
├─────────────────────────────────────────────────────────────┤
│                    服务层 (Helper Layer)                    │
│  LogHelper  │  ConfigHelper  │  ExceptionHelper  │  UIHelper │
├─────────────────────────────────────────────────────────────┤
│                    数据层 (Model Layer)                     │
│  Models  │  Events  │  Configuration  │  File System       │
└─────────────────────────────────────────────────────────────┘
```

## 📁 项目目录结构

```
HR2/
├── Program.cs                     # 程序入口点
├── MyHMI.csproj                   # 项目文件
├── App.config                     # 应用配置
├── packages.config                # NuGet包配置
├── Managers/                      # 核心管理器模块
│   ├── WorkflowManager.cs         # 工作流协调器
│   ├── BeltMotorAutoModeController.cs  # 皮带电机自动控制
│   ├── DMC1000BCardManager.cs     # DMC1000B控制卡管理
│   ├── DMC1000BMotorManager.cs    # 电机控制管理
│   ├── DMC1000BIOManager.cs       # IO控制管理
│   ├── EpsonRobotManager.cs       # Epson机器人管理
│   ├── ScannerManager.cs          # 扫码器管理
│   ├── VisionManager.cs           # 视觉系统管理
│   └── StatisticsManager.cs       # 统计数据管理
├── UI/                            # 用户界面模块
│   ├── MainForm.cs                # 主窗体(1920×1080)
│   └── Controls/                  # 用户控件
│       ├── MotorControlPanel.cs   # 电机控制面板
│       ├── IOControlPanel.cs      # IO控制面板
│       ├── VisionPanel.cs         # 视觉控制面板
│       └── ...                    # 其他控制面板
├── Models/                        # 数据模型
│   ├── MotorModels.cs             # 电机相关模型
│   ├── IOModels.cs                # IO相关模型
│   ├── ProductionModels.cs        # 生产数据模型
│   └── CommunicationModels.cs     # 通信相关模型
├── Events/                        # 事件参数类
│   ├── MotorEventArgs.cs          # 电机事件参数
│   ├── IOEventArgs.cs             # IO事件参数
│   └── SystemModeEventArgs.cs     # 系统模式事件参数
├── Helpers/                       # 工具辅助类
│   ├── LogHelper.cs               # 日志辅助类
│   ├── ExceptionHelper.cs         # 异常处理辅助类
│   └── UIHelper.cs                # UI辅助类
├── Settings/                      # 配置管理
│   ├── Settings.cs                # 配置管理类
│   └── AppSettings.cs             # 应用设置
├── Testing/                       # 测试框架
│   ├── TestRunner.cs              # 测试运行器
│   └── SystemTests.cs             # 系统测试
├── Development_Documents/         # 开发文档
├── Data/                          # 数据存储
├── Logs/                          # 日志文件
└── Export/                        # 导出数据
```

## 🔧 技术栈详解

### 核心技术
- **.NET Framework 4.8**: 稳定的企业级框架
- **WinForms**: 成熟的桌面应用UI框架
- **P/Invoke**: 调用原生DLL（DMC1000B控制卡）
- **TCP/IP**: 机器人通信协议
- **Modbus TCP**: 工业通信协议

### 第三方依赖
```xml
<packages>
  <package id="Newtonsoft.Json" version="13.0.3" />      <!-- JSON序列化 -->
  <package id="NLog" version="5.2.8" />                  <!-- 日志框架 -->
  <package id="NModbus4" version="2.1.0" />              <!-- Modbus通信 -->
  <package id="EPPlus" version="*******" />              <!-- Excel操作 -->
  <package id="CsvHelper" version="30.0.1" />            <!-- CSV操作 -->
  <package id="System.IO.Ports" version="8.0.0" />      <!-- 串口通信 -->
</packages>
```

### 硬件接口
- **DMC1000B运动控制卡**: 4轴步进电机控制
- **Epson 6轴机器人**: TCP/IP通信（双端口模式）
- **工业相机**: 视觉检测系统
- **扫码器**: 产品识别
- **IO设备**: 传感器和执行器

## 🎯 核心设计原则

### 1. 单一职责原则
- 每个Manager类专注于特定硬件或功能
- WorkflowManager专注于流程协调
- UI组件只负责显示和交互

### 2. 事件驱动架构
- 模块间通过事件进行松耦合通信
- 异步事件处理，避免阻塞
- 完整的事件传播机制

### 3. 单例模式
- 所有Manager类采用单例模式
- 确保全局状态一致性
- 简化对象管理

### 4. 异步编程
- 大量使用async/await模式
- 避免UI线程阻塞
- 提高系统响应性能

### 5. 线程安全
- 使用SemaphoreSlim进行异步同步
- 状态变更使用适当的锁机制
- UI更新确保在UI线程执行

## 🚀 系统启动流程

```
Program.Main()
    ↓
1. 初始化日志系统
    ↓
2. 加载Settings配置
    ↓
3. 异步初始化所有Manager
    ├── DMC1000BCardManager (控制卡)
    ├── DMC1000BIOManager (IO管理)
    ├── DMC1000BMotorManager (电机管理)
    ├── EpsonRobotManager (机器人管理)
    ├── ScannerManager (扫码器管理)
    ├── VisionManager (视觉管理)
    └── StatisticsManager (统计管理)
    ↓
4. 启动MainForm主界面
    ↓
5. 建立事件订阅关系
    ↓
6. 系统就绪，等待用户操作
```

## 📊 性能特点

### 优化措施
- **面板缓存机制**: 避免重复创建UI控件
- **双缓冲绘制**: 减少界面闪烁
- **异步操作**: 所有IO操作异步执行
- **事件驱动**: 减少轮询，提高响应速度
- **资源管理**: 及时释放不需要的资源

### 监控频率
- **IO状态监控**: 10Hz (100ms间隔)
- **电机状态监控**: 20Hz (50ms间隔)
- **UI时间更新**: 1Hz (1秒间隔)
- **传感器检查**: 20Hz (50ms间隔)

## 🔄 工作流控制架构

### 控制层次结构
```
UI层（主界面全局按钮）
    ↓
WorkflowManager（统一控制协调器）
    ↓
├── BeltMotorAutoModeController（皮带电机自动模式）
├── ScannerAutoModeManager（扫码器自动模式）
├── EpsonRobotAutoModeController（6轴机器人自动模式）
└── ScaraAutoModeController（SCARA翻转电机自动模式）
```

### 状态管理
- **WorkflowState**: Idle → WaitingForScan → MotorMoving → RobotOperating → VisionDetecting → Idle
- **智能状态切换**: 根据当前状态自动选择操作类型
- **错误恢复机制**: 多层次的错误处理和恢复

## 📈 扩展性设计

### 添加新硬件设备
1. 在`Managers/`创建新的管理器类
2. 实现单例模式和异步接口
3. 在`Models/`定义相关数据模型
4. 在`Events/`定义事件参数
5. 在`UI/Controls/`创建控制面板
6. 在`WorkflowManager`中集成

### 添加新功能模块
1. 遵循现有架构模式
2. 实现统一的接口规范
3. 建立完整的事件机制
4. 添加相应的测试用例

这个架构为HR2项目提供了坚实的基础，具有良好的可维护性、可扩展性和可测试性。
