# 控制卡初始化与释放流程

## 初始化控制卡
- `d1000_board_init`

## 脉冲模式设置
- 如 `d1000_set_pls_outmode`

## 中断控制函数
- 如用中断

## 运动过程处理
### 运动函数调用
- 如 `d1000_start_t_move`

### 运动状态判断
- 如 `d1000_check_done`
- ...

## 用户定义中断服务程序

## 释放控制卡
- `d1000_board_close`

这个Markdown格式的内容反映了图片中的流程图结构，从初始化控制卡开始，经过脉冲模式设置、中断控制函数的使用，到运动过程的处理，包括运动函数的调用和运动状态的判断，最后是控制卡的释放。同时，也提到了用户可以定义自己的中断服务程序。


# DMC1000/DMC1000B用户手册 Version 2.1

**技术支持热线：** 0755-26417593
**网址：** www.szleadtech.com.cn

---

## 8 附录

### 8.1 硬件信号接口表

#### 8.1.1 接口X1引脚定义

X1是DMC（1000、1000B）电机控制、I/O信号控制的主要接口，为SCSI-Ⅱ型68针插座。



**表 8-1 接口 X1引脚号与信号关系表**

| 针脚 | 名称   | 说明                     | 针脚 | 名称   | 说明                     |
| :--- | :----- | :----------------------- | :--- | :----- | :----------------------- |
| 1    | GND    | BUS电源地                | 35   | PUL2+  | 第2轴输出脉冲信号(+)     |
| 2    | PUL0+  | 第0轴输出脉冲信号(+)     | 36   | PUL2-  | 第2轴输出脉冲信号(-)     |
| 3    | PUL0-  | 第0轴输出脉冲信号(-)     | 37   | DIR2+  | 第2轴输出方向信号(+)     |
| 4    | DIR0+  | 第0轴输出方向信号(+)     | 38   | DIR2-  | 第2轴输出方向信号(-)     |
| 5    | DIR0-  | 第0轴输出方向信号(-)     | 39   | PUL3+  | 第3轴输出脉冲信号(+)     |
| 6    | PUL1+  | 第1轴输出脉冲信号(+)     | 40   | PUL3-  | 第3轴输出脉冲信号(-)     |
| 7    | PUL1-  | 第1轴输出脉冲信号(-)     | 41   | DIR3+  | 第3轴输出方向信号(+)     |
| 8    | DIR1+  | 第1轴输出方向信号(+)     | 42   | DIR3-  | 第3轴输出方向信号(-)     |
| 9    | DIR1-  | 第1轴输出方向信号(-)     | 43   | OUT1   | 隔离通用输出信号1        |
| 10   | EL0+   | 隔离第0轴正向限位信号(+) | 44   | OUT2   | 隔离通用输出信号2        |
| 11   | EL0-   | 隔离第0轴负向限位信号(-) | 45   | OUT3   | 隔离通用输出信号3        |
| 12   | SD0+   | 隔离第0轴正向减速信号(+) | 46   | OUT4   | 隔离通用输出信号4        |
| 13   | SD0-   | 隔离第0轴负向减速信号(-) | 47   | OUT5   | 隔离通用输出信号5        |
| 14   | ORG0   | 隔离第0轴原点信号        | 48   | OUT6   | 隔离通用输出信号6        |
| 15   | EL1+   | 隔离第1轴正向限位信号(+) | 49   | OUT7   | 隔离通用输出信号7        |
| 16   | EL1-   | 隔离第1轴负向限位信号(-) | 50   | OUT8   | 隔离通用输出信号8        |
| 17   | SD1+   | 隔离第1轴正向减速信号(+) | 51   | OUT9   | 隔离通用输出信号9        |
| 18   | SD1-   | 隔离第1轴负向减速信号(-) | 52   | OUT10  | 隔离通用输出信号10       |
| 19   | ORG1   | 隔离第1轴原点信号        | 53   | OUT11  | 隔离通用输出信号11       |
| 20   | EL2+   | 隔离第2轴正向限位信号(+) | 54   | OUT12  | 隔离通用输出信号12       |
| 21   | EL2-   | 隔离第2轴负向限位信号(-) | 55   | IN5    | 隔离通用输入信号5        |
| 22   | SD2+   | 隔离第2轴正向减速信号(+) | 56   | IN6    | 隔离通用输入信号6        |
| 23   | SD2-   | 隔离第2轴负向减速信号(-) | 57   | IN7    | 隔离通用输入信号7        |
| 24   | ORG2   | 隔离第2轴原点信号        | 58   | IN8    | 隔离通用输入信号8        |
| 25   | EL3+   | 隔离第3轴正向限位信号(+) | 59   | IN9    | 隔离通用输入信号9        |
| 26   | EL3-   | 隔离第3轴负向限位信号(-) | 60   | IN10   | 隔离通用输入信号10       |
| 27   | SD3+   | 隔离第3轴正向减速信号(+) | 61   | IN11   | 隔离通用输入信号11       |
| 28   | SD3-   | 隔离第3轴负向减速信号(-) | 62   | IN12   | 隔离通用输入信号12       |
| 29   | ORG3   | 隔离第3轴原点信号        | 63   | IN13   | 隔离通用输入信号13       |
| 30   | IN1    | 隔离通用输入信号1        | 64   | IN14   | 隔离通用输入信号14       |
| 31   | IN2    | 隔离通用输入信号2        | 65   | IN15   | 隔离通用输入信号15       |
| 32   | IN3    | 隔离通用输入信号3        | 66   | EXGND  | 外部电源地，输入         |
| 33   | IN4    | 隔离通用输入信号4        | 67   | VDD    | 外部电源+24V，输入       |
| 34   | EXGND  | 外部电源地，输入         | 68   | IN16   | 隔离通用输入信号16       |

**注：** 当需要使用IO信号时，如限位、原点等专用信号或通用IO信号时，请务必在67脚和66脚输入+24VDC电源（请不要将电源接反）。使用ACC68_V3.2接线板的用户请注意：其上标有“VDD”与“EXGND”的引脚号，这些引脚仅仅是为接线板的电源指示灯提供电源的，当要控制IO信号时，仍需在67脚与66脚输入外部+24VDC电源。

#### 8.1.2 接口X2（X3）引脚定义

X2（DMC1000B卡中为X3）是多卡同时启动、同时停止、同时减速信号输入/输出口。



**表 8-2 接口 X2/X3引脚号和信号关系表**

| 序号 | 名称 | 说明                 |
| :--- | :--- | :------------------- |
| 1    | GND  | PC电源地             |
| 2    | STP  | 同时停止信号输入/输出 |
| 3    | STA  | 同时启动信号输入/输出 |
| 4    | STP  | 同时停止信号输入/输出 |
| 5    | STA  | 同时启动信号输入/输出 |
| 6    | VCC  | PC电源（DMC1000B卡中是3.3V电压） |

#### 8.1.3 接口J1（DMC1000B）引脚定义

DMC1000B卡中有扩展IO口的接口，其标志为J1，且其上所有IO口都为非隔离的通用输入输出口。

**图 8-3 DMC1000B接口 J1示意图**

**表 8-3 DMC1000B接口 J1引脚号和信号关系表**

| 脚号 | 名称  | I/O | 功能                 | 脚号 | 名称  | I/O | 功能                 |
| :--- | :---- | :-- | :------------------- | :--- | :---- | :-- | :------------------- |
| 1    | IN17  | I   | 非隔离通用输入信号17 | 20   | GND   | O   | PC电源地，输出       |
| 2    | IN18  | I   | 非隔离通用输入信号18 | 21   | OUT13 | O   | 非隔离通用输出信号13 |
| 3    | IN19  | I   | 非隔离通用输入信号19 | 22   | OUT14 | O   | 非隔离通用输出信号14 |
| 4    | IN20  | I   | 非隔离通用输入信号20 | 23   | OUT15 | O   | 非隔离通用输出信号15 |
| 5    | IN21  | I   | 非隔离通用输入信号21 | 24   | OUT16 | O   | 非隔离通用输出信号16 |
| 6    | IN22  | I   | 非隔离通用输入信号22 | 25   | OUT17 | O   | 非隔离通用输出信号17 |
| 7    | IN23  | I   | 非隔离通用输入信号23 | 26   | OUT18 | O   | 非隔离通用输出信号18 |
| 8    | IN24  | I   | 非隔离通用输入信号24 | 27   | OUT19 | O   | 非隔离通用输出信号19 |
| 9    | IN25  | I   | 非隔离通用输入信号25 | 28   | OUT20 | O   | 非隔离通用输出信号20 |
| 10   | IN26  | I   | 非隔离通用输入信号26 | 29   | OUT21 | O   | 非隔离通用输出信号21 |
| 11   | IN27  | I   | 非隔离通用输入信号27 | 30   | OUT22 | O   | 非隔离通用输出信号22 |
| 12   | IN28  | I   | 非隔离通用输入信号28 | 31   | OUT23 | O   | 非隔离通用输出信号23 |
| 13   | IN29  | I   | 非隔离通用输入信号29 | 32   | OUT24 | O   | 非隔离通用输出信号24 |
| 14   | IN30  | I   | 非隔离通用输入信号30 | 33   | OUT25 | O   | 非隔离通用输出信号25 |
| 15   | IN31  | I   | 非隔离通用输入信号31 | 34   | OUT26 | O   | 非隔离通用输出信号26 |
| 16   | IN32  | I   | 非隔离通用输入信号32 | 35   | OUT27 | O   | 非隔离通用输出信号27 |
| 17   | 3.3V  | O   | PC电源，输出         | 36   | —     | —   | 未用                 |
| 18   | 3.3V  | O   | PC电源，输出         | 37   | GND   | O   | PC电源地，输出       |
| 19   | GND   | O   | PC电源地，输出       |      |       |     |                      |

为方便用户使用DMC1000B的扩展IO口，雷赛公司提供了专用的光电隔离接线板ACC37_7480，使用ACC37_7480接线板后可实现DMC1000B扩展IO接口的输入输出信号全部光电隔离。

**表 8-4 ACC37_7480引脚与DMC1000B_J1信号关系表**

| 引脚号 | 信号名称 | I/O | 功能               | 引脚号 | 信号名称 | I/O | 功能               |
| :----- | :------- | :-- | :----------------- | :----- | :------- | :-- | :----------------- |
| IN1    | IN17     | I   | 隔离后的通用输入17 | OUT1   | OUT13    | O   | 隔离后的通用输出13 |
| IN2    | IN18     | I   | 隔离后的通用输入18 | OUT2   | OUT14    | O   | 隔离后的通用输出14 |
| IN3    | IN19     | I   | 隔离后的通用输入19 | OUT3   | OUT15    | O   | 隔离后的通用输出15 |
| IN4    | IN20     | I   | 隔离后的通用输入20 | OUT4   | OUT16    | O   | 隔离后的通用输出16 |
| IN5    | IN21     | I   | 隔离后的通用输入21 | OUT5   | OUT17    | O   | 隔离后的通用输出17 |
| IN6    | IN22     | I   | 隔离后的通用输入22 | OUT6   | OUT18    | O   | 隔离后的通用输出18 |
| IN7    | IN23     | I   | 隔离后的通用输入23 | OUT7   | OUT19    | O   | 隔离后的通用输出19 |
| IN8    | IN24     | I   | 隔离后的通用输入24 | OUT8   | OUT20    | O   | 隔离后的通用输出20 |
| IN9    | IN25     | I   | 隔离后的通用输入25 | OUT9   | OUT21    | O   | 隔离后的通用输出21 |
| IN10   | IN26     | I   | 隔离后的通用输入26 | OUT10  | OUT22    | O   | 隔离后的通用输出22 |
| IN11   | IN27     | I   | 隔离后的通用输入27 | OUT11  | OUT23    | O   | 隔离后的通用输出23 |
| IN12   | IN28     | I   | 隔离后的通用输入28 | OUT12  | OUT24    | O   | 隔离后的通用输出24 |
| IN13   | IN29     | I   | 隔离后的通用输入29 | OUT13  | OUT25    | O   | 隔离后的通用输出25 |
| IN14   | IN30     | I   | 隔离后的通用输入30 | OUT14  | OUT26    | O   | 隔离后的通用输出26 |
| IN15   | IN31     | I   | 隔离后的通用输入31 | OUT15  | OUT27    | O   | 隔离后的通用输出27 |
| IN16   | IN32     | I   | 隔离后的通用输入32 | OUT16  | 未用     | —   | 未用               |
| EGND   | EXGND    | I   | 外部电源地，输入   | EGND   | EXGND    | I   | 外部电源地，输入   |
| +24V   | VDD      | I   | 外部电源+24V，输入 | EGND   | EXGND    | I   | 外部电源地，输入   |

---

### 8.2 运动控制函数库

DMC（1000、1000B）运动控制函数库是一个运动控制API函数库，在其基础上开发应用软件很简单：您只要用C/C++、Visual Basic开发用户界面，并调用DMC（1000、1000B）函数库中的相关运动控制函数，您就可以随心所欲的对自己的多轴自动化设备进行精确、高速、协调的控制。因为DMC（1000、1000B）的函数库能帮您处理所有与运动控制有关的复杂问题，这样您不必了解底层硬件细节就可以根据特定的应用要求像搭积木一样开发出自己的软件系统，从而大大缩短您的软件开发周期。

雷泰DMC1000运动控制卡的运动控制函数库共有11类27个函数，DMC1000B运动控制卡的运动控制函数库共有10类24个函数，分类列表如下：

#### 8.2.1 函数列表

|          | 函数名                   | 描述                               |
| :------- | :----------------------- | :--------------------------------- |
| 初始化函数 | `d1000_board_init`       | 初始化控制卡                       |
| 初始化函数 | `d1000_board_close`      | 关闭控制卡                         |
| 初始化函数 | `d1000_get_base_addr` (DMC1000) | 读取DMC1000卡的基地址              |
| 初始化函数 | `d1000_get_irq_channel` (DMC1000) | 读取DMC1000卡的中断号              |
| 脉冲模式设置函数 | `d1000_set_pls_outmode`  | 设定脉冲输出模式                   |
| 连续运动函数 | `d1000_start_tv_move`    | 以梯形速度曲线控制一个轴连续运动   |
| 连续运动函数 | `d1000_start_sv_move`    | 以S形速度曲线控制一个轴连续运动    |
| 连续运动函数 | `d1000_get_speed`        | 读取指定轴的脉冲输出速度           |
| 连续运动函数 | `d1000_change_speed`     | 改变指定轴的脉冲输出速度           |
| 连续运动函数 | `d1000_immediate_stop`   | 以梯形或S形急停一个轴              |
| 连续运动函数 | `d1000_decel_stop`       | 以梯形或S形减速停止一个轴          |
| 单轴运动函数 | `d1000_start_t_move`     | 以梯形速度曲线控制相对坐标的点位运动 |
| 单轴运动函数 | `d1000_start_ta_move`    | 以梯形速度曲线控制绝对坐标的点位运动 |
| 单轴运动函数 | `d1000_start_s_move`     | 以S形速度曲线控制相对坐标的点位运动  |
| 单轴运动函数 | `d1000_start_sa_move`    | 以S形速度曲线控制绝对坐标的点位运动  |
| 直线插补函数 | `d1000_start_t_line`     | 任意2、3、4轴相对坐标的直线插补运动 |
| 直线插补函数 | `d1000_start_ta_line`    | 任意2、3、4轴绝对坐标的直线插补运动 |
| 回原点函数 | `d1000_home_move`        | 回原点运动                         |
| 圆弧插补函数 | `d1000_start_t_arc` (DMC1000) | 任意2轴进行相对坐标的圆弧插补 (DMC1000B无此功能) |
| 运动状态检测函数 | `d1000_check_done`       | 检测当前运动状态                   |
| 指令位置设定和读取函数 | `d1000_get_command_pos`  | 读取指令位置计数器值               |
| 指令位置设定和读取函数 | `d1000_set_command_pos`  | 设置指令位置计数器值               |
| 通用IO接口函数 | `d1000_out_bit`          | 通用输出口输出                     |
| 通用IO接口函数 | `d1000_in_bit`           | 读取通用输入口状态                 |
| 通用IO接口函数 | `d1000_get_outbit`       | 读取通用输出口状态                 |
| 专用IO接口函数 | `d1000_set_sd`           | 设置减速开关是否有效               |
| 专用IO接口函数 | `d1000_get_axis_status`  | 读取指定轴的所有输入状态           |

#### 8.2.2 函数说明

##### 8.2.2.1 初始化函数

*   **`DWORD d1000_board_init(void)`**
    *   **功能：** 为控制卡分配系统资源，并初始化控制卡。
    *   **参数：** 无。
    *   **返回值：** 卡数：0~12，其中0表示没有卡。

*   **`DWORD d1000_board_close(void)`**
    *   **功能：** 关闭控制卡，释放系统资源。
    *   **参数：** 无。
    *   **返回值：** 正确：返回 `ERR_NoError`；错误：返回相关错误码，具体含义参照8.2.3资源文件，下同。

*   **`DWORD d1000_get_base_addr(short CardNo)`**
    *   **功能：** 读取DMC1000卡在PC机中占用的基地址（注：DMC1000B卡无此项功能）。
    *   **参数：** `CardNo` - 卡号。
    *   **返回值：** 指定卡的基地址。

*   **`DWORD d1000_get_irq_channel(short CardNo)`**
    *   **功能：** 读取DMC1000卡的中断号。（注：DMC1000B卡无此项功能）
    *   **参数：** `CardNo` - 卡号。
    *   **返回值：** 指定卡占用的中断号。

**注意：** 在Windows 2000下，退出程序之前一定要调用 `d1000_board_close` 函数释放控制卡占用的资源！

##### 8.2.2.2 脉冲输出设置函数

*   **`DWORD d1000_set_pls_outmode(short axis, short pls_outmode)`**
    *   **功能：** 设置控制卡脉冲输出模式，用户可以根据驱动器具体接收脉冲的模式来选择控制卡不同脉冲输出模式。
    *   **参数：**
        *   `axis`：轴号。范围0～(n×4-1)，n为卡数。多卡运行时，轴号参考表2-1多卡运行时轴号对照表。
        *   `pls_outmode`：脉冲输出模式：
            *   0：pulse/dir模式，脉冲上升沿有效；
            *   1：pulse/dir模式，脉冲下降沿有效；
            *   2：CW/CCW模式，脉冲上升沿有效；
            *   3：CW/CCW模式，脉冲下降沿有效。
    *   **返回值：** 正确：返回 `ERR_NoError`；错误：返回相关错误码。

##### 8.2.2.3 连续运动函数

*   **`DWORD d1000_start_tv_move(short axis, long StrVel, Long MaxVel, double Tacc)`**
    *   **功能：** 以梯形速度曲线控制指定轴至运行速度，并以运行速度连续运行。
    *   **参数：**
        *   `axis`：轴号，范围0～(n×4-1)，n为卡数。
        *   `StrVel`：初始速度，单位：pps。
        *   `MaxVel`：运行速度，单位：pps，其值的正负表示运动方向。
        *   `Tacc`：加速时间，单位：s。
    *   **返回值：** 正确：返回 `ERR_NoError`；错误：返回相关错误码。

*   **`DWORD d1000_start_sv_move(short axis, long StrVel, Long MaxVel, double Tacc)`**
    *   **功能：** 以S形速度曲线控制指定轴至运行速度，并以运行速度连续运行。
    *   **参数：**
        *   `axis`：轴号，范围0～(n×4-1)，n为卡数。
        *   `StrVel`：初始速度，单位：pps。
        *   `MaxVel`：运行速度，单位：pps，其值的正负表示运动方向。
        *   `Tacc`：加速时间，单位：s。
    *   **返回值：** 正确：返回 `ERR_NoError`；错误：返回相关错误码。

*   **`DWORD d1000_get_speed(short axis)`**
    *   **功能：** 读取指定轴当前脉冲输出速度。
    *   **参数：** `axis`：轴号，范围0～(n×4-1)，n为卡数。
    *   **返回值：** 指定轴当前的运动速度，单位pps。

*   **`DWORD d1000_change_speed(short axis, long NewVel)`**
    *   **功能：** 改变指定轴当前脉冲输出速度。
    *   **参数：**
        *   `axis`：轴号，范围0～(n×4-1)，n为卡数。
        *   `NewVel`：新设置的速度，单位：pps，取值范围：1~409550。
    *   **返回值：** 正确：返回 `ERR_NoError`；错误：返回相关错误码。
    *   **注意：** `d1000_change_speed`函数变速后的速度与实际设定速度之间有一个允许的误差，误差值为0~50 pps，具体的误差值与当前速度及速度改变量ΔV有关，其绝对值\|ΔV\|越小，则误差越小。

*   **`DWORD d1000_decel_stop(short axis)`**
    *   **功能：** 减速停止指定轴脉冲输出。
    *   **参数：** `axis`：轴号，范围0～(n×4-1)，n为卡数。
    *   **返回值：** 正确：返回 `ERR_NoError`；错误：返回相关错误码。

*   **`DWORD d1000_immediate_stop(short axis)`**
    *   **功能：** 急停指定轴脉冲输出。
    *   **参数：** `axis`：轴号，范围0～(n×4-1)，n为卡数。
    *   **返回值：** 正确：返回 `ERR_NoError`；错误：返回相关错误码。

##### 8.2.2.4 点位运动函数

*   **`DWORD d1000_start_t_move(short axis, long Dist, long StrVel, long MaxVel, double Tacc)`**
    *   **功能：** 以梯形速度曲线控制指定轴至运行速度，并以相对坐标运行一段指定距离。
    *   **参数：**
        *   `axis`：轴号，范围0～(n×4-1)，n为卡数。
        *   `Dist`：相对运动距离，单位：pulse，其值的正负表示运动方向。
        *   `StrVel`：初始速度，单位：pps。
        *   `MaxVel`：运行速度，单位：pps。
        *   `Tacc`：加速时间，单位：s。
    *   **返回值：** 正确：返回 `ERR_NoError`；错误：返回相关错误码。

*   **`DWORD d1000_start_ta_move(short axis, long Pos, double StrVel, double MaxVel, double Tacc)`**
    *   **功能：** 以梯形速度曲线控制指定轴至运行速度，并以绝对坐标运行一段指定距离。
    *   **参数：**
        *   `axis`：轴号，范围0～(n×4-1)，n为卡数。
        *   `Pos`：绝对运动位置，单位：pulse。
        *   `StrVel`：初始速度，单位：pps。
        *   `MaxVel`：运行速度，单位：pps。
        *   `Tacc`：加速时间，单位：s。
    *   **返回值：** 正确：返回 `ERR_NoError`；错误：返回相关错误码。

*   **`DWORD d1000_start_s_move(short axis, long Dist, long StrVel, long MaxVel, double Tacc)`**
    *   **功能：** 以S形速度曲线控制指定轴至运行速度，并以相对坐标运行一段指定距离。
    *   **参数：**
        *   `axis`：轴号，范围0～(n×4-1)，n为卡数。
        *   `Dist`：相对运动距离，单位：pulse，其值的正负表示运动方向。
        *   `StrVel`：初始速度，单位：pps。
        *   `MaxVel`：运行速度，单位：pps。
        *   `Tacc`：加速时间，单位：s。
    *   **返回值：** 正确：返回 `ERR_NoError`；错误：返回相关错误码。

*   **`DWORD d1000_start_sa_move(short axis, long Pos, long StrVel, long MaxVel, double Tacc)`**
    *   **功能：** 以S形速度曲线控制指定轴至运行速度，并以绝对坐标运行一段指定距离。
    *   **参数：**
        *   `axis`：轴号，范围0～(n×4-1)，n为卡数。
        *   `Pos`：绝对运动位置，单位：pulse。
        *   `StrVel`：初始速度，单位：pps。
        *   `MaxVel`：运行速度，单位：pps。
        *   `Tacc`：加速时间，单位：s。
    *   **返回值：** 正确：返回 `ERR_NoError`；错误：返回相关错误码。

##### 8.2.2.5 直线插补函数

*   **`DWORD d1000_start_t_line(short TotalAxis, short* AxisArray, short *DistArray, long StrVel, long MaxVel, double Tacc)`**
    *   **功能：** 启动多轴相对坐标的直线插补。
    *   **参数：**
        *   `TotalAxis`：插补轴数，范围2～4。
        *   `*AxisArray`，`AxisArray`：轴号列表。
        *   `*DistArray`，`DistArray`：对应轴号列表各轴的相对坐标的距离列表。
        *   `StrVel`：初始速度，单位：pps。
        *   `MaxVel`：运行速度，单位：pps。
        *   `Tacc`：加速时间，单位：s。
    *   **返回值：** 正确：返回 `ERR_NoError`；错误：返回相关错误码。

*   **`DWORD d1000_start_ta_line(short TotalAxis, short* AxisArray, short *PosArray, long StrVel, long MaxVel, double Tacc)`**
    *   **功能：** 启动多轴绝对坐标的直线插补。
    *   **参数：**
        *   `TotalAxis`：插补轴数，范围2～4。
        *   `*AxisArray`，`AxisArray`：轴号列表。
        *   `*PosArray`，`PosArray`：对应轴号列表各轴的绝对坐标的位置列表。
        *   `StrVel`：初始速度，单位：pps。
        *   `MaxVel`：运行速度，单位：pps。
        *   `Tacc`：加速时间，单位：s。
    *   **返回值：** 正确：返回 `ERR_NoError`；错误：返回相关错误码。

##### 8.2.2.6 圆弧插补函数

*   **`DWORD d1000_start_t_arc(short* AxisArray, short OffsetC1, short OffsetC2, double Angle, long StrVel, long MaxVel, double Tacc)`**
    *   **功能：** 启动任意2轴圆弧插补运动。(DMC1000B卡不支持圆弧插补)。
    *   **参数：**
        *   `*AxisArray`：轴号列表。
        *   `OffsetC1`：第1个轴的圆心位置，单位：pulse。
        *   `OffsetC2`：第2个轴的圆心位置，单位：pulse。
        *   `Angle`：圆弧插补角度，单位：degree。
        *   `StrVel`：插补初始速度，单位：pps。
        *   `MaxVel`：插补运行速度，单位：pps。
        *   `Tacc`：加速时间，单位：s。
    *   **返回值：** 正确：返回 `ERR_NoError`；错误：返回相关错误码。
    *   **注意：** `Angle`参数取值单位为“度”，而不是“弧度”；`Angle`为负值时表示顺时针运动，`Angle`为正值时表示逆时针运动。

##### 8.2.2.7 回原点函数

*   **`DWORD d1000_home_move(short axis, long StrVel, long MaxVel, double Tacc)`**
    *   **功能：** 启动指定轴进行回原点运动。
    *   **参数：**
        *   `axis`：轴号，范围0～(n×4-1)，n为卡数。
        *   `StrVel`：回原点运动初始速度，单位：pps。
        *   `MaxVel`：回原点运动速度，负值表示往负方向找原点，正值表示往正方向找原点，单位：pps。
        *   `Tacc`：加速时间，单位：s。
    *   **返回值：** 正确：返回 `ERR_NoError`；错误：返回相关错误码。

##### 8.2.2.8 运动状态检测函数

*   **`DWORD d1000_check_done(short axis)`**
    *   **功能：** 检测指定轴的运动状态。
    *   **参数：** `axis`：轴号，范围0～(n×4-1)，n为卡数。
    *   **返回值：**
        *   0：正在运行；
        *   1：脉冲输出完毕停止；
        *   2：指令停止（如调用了 `d1000_decel_stop` 函数）；
        *   3：遇限位停止；
        *   4：遇原点停止。
    *   **注意：**
        1.  当指定轴遇限位信号停止时，`d1000_check_done`函数返回3，若限位信号撤去，`d1000_check_done`函数返回值变为1。
        2.  当指定轴遇原点信号停止时，`d1000_check_done`函数返回4，若原点信号撤去，`d1000_check_done`函数返回值变为1。

##### 8.2.2.9 指令位置设定和读取函数

*   **`DWORD d1000_get_command_pos(short axis)`**
    *   **功能：** 读取指令位置计数器计数值。
    *   **参数：** `axis`：轴号，范围0～(n×4-1)，n为卡数。
    *   **返回值：** 指定轴当前指令位置计数器值，单位：pulse。

*   **`DWORD d1000_set_command_pos(short axis, double Pos)`**
    *   **功能：** 设置指令位置计数器计数值。
    *   **参数：**
        *   `axis`：轴号，范围0～(n×4-1)，n为卡数。
        *   `Pos`：设置指令位置计数器值，单位：pulse。
    *   **返回值：** 正确：返回 `ERR_NoError`；错误：返回相关错误码。

##### 8.2.2.10 通用I/O接口函数

*   **`DWORD d1000_out_bit(short BitNo, short BitData)`**
    *   **功能：** 输出通用输出信号。
    *   **参数：**
        *   `BitNo`：表示要输出的通用输出口的位号，范围参考表8-5及表8-6。
        *   `BitData`：输出信号：0-表示低电平；1-表示高电平。
    *   **返回值：** 正确：返回 `ERR_NoError`；错误：返回相关错误码。

*   **`DWORD d1000_in_bit(short BitNo)`**
    *   **功能：** 读取通用输入信号状态。
    *   **参数：** `BitNo`：表示要读取的通用输入口的位号，范围参考表8-5及表8-6。
    *   **返回值：** 输入口状态：0-表示低电平；1-表示高电平。

*   **`DWORD d1000_get_outbit(short BitNo)`**
    *   **功能：** 读取通用输出信号状态。
    *   **参数：** `BitNo`：通用输出口位号，范围参考表8-5及表8-6。
    *   **返回值：** 输出口状态：0-表示低电平；1-表示高电平。

**注：** 多卡IO位号分配说明，如表8-5和8-6所示：

**表 8-5 DMC1000B多卡 IO位号分配**

|      | 通用输出口       | 通用输入口       |
| :--- | :--------------- | :--------------- |
| 卡 1 | 1~27             | 1~32             |
| 卡 2 | 32+(1~27)        | 32+(1~32)        |
| 卡 3 | 64+(1~27)        | 64+(1~32)        |
| 卡 n | (n-1)\*32+(1~27) | (n-1)\*32+(1~32) |

**表 8-6 DMC1000多卡 IO位号分配**

|      | 通用输出口       | 通用输入口       |
| :--- | :--------------- | :--------------- |
| 卡 1 | 1~12             | 1~16             |
| 卡 2 | 12+(1~12)        | 16+(1~16)        |
| 卡 3 | 24+(1~12)        | 32+(1~16)        |
| 卡 n | (n-1)\*12+(1~12) | (n-1)\*16+(1~16) |

##### 8.2.2.11 专用I/O接口函数

*   **`DWORD d1000_set_sd(short axis, short SdMode)`**
    *   **功能：** 设置减速信号是否使能。
    *   **参数：**
        *   `axis`：轴号，范围0～(n×4-1)，n为卡数。
        *   `SdMode`：减速使能模式
            *   0：SD信号无效；
            *   1：SD信号有效。
    *   **返回值：** 正确：返回 `ERR_NoError`；错误：返回相关错误码。

*   **`DWORD d1000_get_axis_status(short axis)`**
    *   **功能：** 读取指定轴的专用接口信号状态，包括EL+、EL-、STP、STA、SD+、SD-等信号状态。
    *   **参数：** `axis`：轴号，范围0～(n×4-1)，n为卡数。
    *   **返回值：** 指定轴专用信号接口状态，取低字节，其二进制位号与信号的对应关系如表8-7所示：

**表 8-7 返回值的位号与信号对应表**

| 位号 | 信号 | 电平         |
| :--- | :--- | :----------- |
| 0    | -EL  | 0：高，1：低 |
| 1    | +EL  | 0：高，1：低 |
| 2    | ORG  | 0：高，1：低 |
| 3    | STP  | 0：高，1：低 |
| 4    | STA  | 0：高，1：低 |
| 5    | -SD  | 0：高，1：低 |
| 6    | +SD  | 0：高，1：低 |
| 7    | 保留 | 保留         |

#### 8.2.3 资源文件

*   C/C++：`DMC1000.h`
*   Visual Basic：`DMC1000.bas`

**注意：**
（1）在所有运动函数参数设置中，`MaxVel`的绝对值一定要大于 `StrVel`的绝对值，否则参数设置无效，实际速度仍然为上次设置的值。
（2）在所有运动函数中，单次运动的位移设置范围为16777215，设置参数时应在此范围内，超出最大值的设置会导致运动异常。
（3）错误号的含义如下：
    *   `ERR_NoError`值为0，代表参数设置正确；
    *   `ERR_BoardNumber`值为1，代表卡号参数设置错误；
    *   `ERR_ParaAxis`值为2，代表轴号参数设置错误；
    *   `ERR_ParaData`值为3，除以上参数外的其他参数设置错误。

---

### 8.3 DMC1000B与DMC1000卡之间的一些说明

DMC1000B与DMC1000运动控制卡功能大致差不多，但还是存在一些差别，具体见以下小节中说明。

#### 8.3.1 DMC1000B与DMC1000卡的差异说明

**一、硬件、文件差异**

1.  **DMC1000B板卡。** DMC1000B是DMC1000的升级版本，它们的外部接口完全一样，因此，DMC1000B替换DMC1000时，硬件上只需要更换新板卡DMC1000B，电缆线、接线板都不需要更换，不影响用户设备外部接线。
2.  **DMC1000B驱动程序。** DMC1000B与DMC1000使用不同的驱动程序，因此，使用DMC1000B需要安装DMC1000B的驱动程序（原来DMC1000的驱动程序可以不卸载）。
3.  **DMC1000.dll函数库不同，但头文件相同。**

**二、性能差异**

1.  DMC1000B的最大脉冲输出频率为1.2M，而DMC1000最大脉冲输出频率只有400K，因此，在高细分高速度运动控制中DMC1000B更有优势。
2.  DMC1000B增加I/O扩展口，通用I/O口更多。
3.  DMC1000B不支持圆弧插补。

**三、编程差异**

1.  DMC1000B没有圆弧插补函数：`d1000_start_t_arc`
2.  DMC1000B没有基地址读取函数：`d1000_get_base_addr`(dos下使用)
3.  DMC1000B没有中断号读取函数：`d1000_get_irq_channel`(dos下使用)

#### 8.3.2 DMC1000B卡替换DMC1000卡的步骤

DMC1000B卡替换DMC1000卡的步骤如下:

1.  更换新板卡DMC1000B，硬件安装参考3.硬件配置与安装。
2.  更新驱动程序，参考5驱动程序安装。
3.  更新编程文件(`DMC1000.dll`)。
4.  完成替换。

**注：** DMC1000B可以多卡运行，但是不能与DMC1000在同一个系统中使用。

---

### 8.4 常见问题库

| 出现问题                                   | 解决建议                                                                                                                                 |
| :----------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------- |
| 板卡插上后，PC机系统还不能识别DMC（1000、1000B）。 | 检查板卡驱动是否正确安装，在WINDOWS的设备管理器（可参看WINDOWS帮助文件）中查看驱动程序安装是否正常。如果发现有相关的黄色感叹号标志，说明安装不正确，需要按照软件部分安装指引，重新安装；计算机主板兼容性差，请咨询主板供应商；PCI插槽是否完好；PCI金手指是否有异物，可用酒精清洗。 |
| PC机不能和DMC（1000、1000B）通讯。              | PCI金手指是否有异物，可用酒精清洗；参考软件手册检查应用软件是否编写正确。                                                               |
| 板卡和驱动器电机连接后，发出脉冲时，电机不转动。     | 板卡上的设置脉冲发送方式和驱动器的输入脉冲方式是否匹配，跳线J1~J8是否正确；可以用motion（1000、1000B）软件进行测试，观察脉冲计数等是否正常；是否已经接上供给脉冲和方向的外部电源。 |
| 控制卡已经正常工作，正常发出脉冲，但电机不转动。     | 检查驱动器和电机之间的连接是否正确。可以使用motion（1000、1000B）软件进行测试。确保驱动器工作正常，没有出现报警。                      |
| 电机可以转动，但工作不正常。                     | 检查控制卡和驱动器是否正确接地，抗干扰措施是否做好；脉冲和方向信号输出端光电隔离电路中使用的限流电阻过大，工作电流偏小。                 |
| 能够控制电机，但电机出现振荡或是过冲。             | 可能是驱动器参数设置不当，检查驱动器参数设置；应用程序中加减速时间和运动速度设置不合理。                                                 |
| 能够控制电机，但工作时，回原点定位不准。           | 检查屏蔽线是否接地；原点信号开关是否工作正常；所有编码信号和原点信号是否受到干扰。                                                       |
| 限位信号不起作用。                             | 限位传感器工作不正常；限位传感器信号受干扰；应用程序紊乱。                                                                               |
| 数字输入信号不能读取。                         | 接线是否正常；检查函数调用是否正确。                                                                                                     |
| 数字输出信号不正常。                           | 接线是否正常；检查函数调用是否正确。                                                                                                     |