# UI界面布局设计文档

## 整体布局规范
- **分辨率**: 1920 × 1080 像素
- **主题风格**: 工业HMI深色主题
- **布局方式**: 嵌入式面板设计，采用一级菜单+二级Tab页的结构

## 界面区域划分

### 1. 顶部标题栏 (1920×60px)
- 背景色: #1e2329
- 内容: 系统标题 "DMC1000运动控制系统"
- 字体: 24px, 白色
- 位置: 居中显示

### 2. 系统菜单栏 (1920×40px)  
- 背景色: #252930
- 菜单项: 系统日志 | 安全设置 | 外部设备状态
- 字体: 14px, #ffffff
- 排列: 水平左对齐

### 3. 主要功能区域 (1920×920px)

#### 3.1 左侧一级功能菜单 (200×920px)
- 背景色: #2d3339
- 菜单项目 (每项 200×153px):
  1. 视觉控制
  2. 电机控制  
  3. 6轴机器人控制
  4. scara控制
  5. io控制
  6. 生产日志管理
- 字体: 16px, #ffffff
- 文字方向: 水平显示
- 激活状态: 背景色 #3498db

#### 3.2 功能显示区 (700×920px)
- 背景色: #34495e
- 边框: 1px solid #4a5661
- 结构:
  - **二级Tab菜单栏** (700×40px): 根据一级菜单显示对应的二级Tab页
  - **功能面板区** (700×880px): 显示选中Tab页的具体控制面板

#### 3.3 右侧区域 (1020×920px)

##### 3.3.1 工业相机显示区域 (1020×460px)
**上半部分: 工业相机1显示区 (510×460px)**
- 背景色: #2c3e50
- 边框: 1px solid #34495e
- 标题: "定位相机"
- 内容: 黑色背景空白区域，预留相机显示

**上半部分: 工业相机2显示区 (510×460px)**
- 背景色: #2c3e50  
- 边框: 1px solid #34495e
- 标题: "对位相机"
- 内容: 黑色背景空白区域，预留相机显示

##### 3.3.2 控制操作区 (1020×230px)
**生产日志显示区 (720×230px)**
- 背景色: #2c3e50
- 边框: 1px solid #34495e
- 内容: 实时生产日志滚动显示

**开始&复位&暂停&停止区 (300×230px)**
- 背景色: #2c3e50
- 4个控制按钮 (每个 140×50px):
  - 开始按钮: #27ae60 (绿色)
  - 复位按钮: #3498db (蓝色)  
  - 暂停按钮: #f39c12 (橙色)
  - 停止按钮: #e74c3c (红色)

##### 3.3.3 产量统计区 (1020×230px)
- 背景色: #2c3e50
- 边框: 1px solid #34495e
- 显示内容:
  - 总产量计数器
  - OK产量计数器 (绿色)
  - NG产量计数器 (红色)
  - 当前效率百分比

### 4. 底部状态栏 (1920×60px)
- 背景色: #1e2329
- 状态信息显示:
  - 机器人运行状态 (执行中/暂停中/停止)
  - 左翻转电机状态 (执行中:红色/空闲中:绿色)
  - 右翻转电机状态 (执行中:红色/空闲中:绿色)
  - 系统时间
  - 连接状态指示

## 二级Tab菜单设计

### 1. 视觉控制Tab页
- **定位相机控制**: 预留空白面板，后期开发使用
- **对位相机控制**: 预留空白面板，后期开发使用

### 2. 电机控制Tab页  
- **翻转电机控制**:
  - 采用上下垂直布局，左翻转电机在上，右翻转电机在下
  - 左翻转电机控制:
    * 电机参数设置: 脉冲当量、最大加速度、运行速度
    * 点动控制: 正转、反转按钮
    * 示教功能: 回原点、保存位置1、保存位置2、保存位置3
    * 位置移动: 移动到位置1、移动到位置2、移动到位置3
  - 右翻转电机控制:
    * 电机参数设置: 脉冲当量、最大加速度、运行速度  
    * 点动控制: 正转、反转按钮
    * 示教功能: 回原点、保存位置1、保存位置2、保存位置3
    * 位置移动: 移动到位置1、移动到位置2、移动到位置3
- **皮带电机控制**:
  - 参数设置: 脉冲当量(pulse/mm)、最大加速度(mm/s²)、运行速度(mm/s)、点动移动距离(mm)
  - 控制操作: 正转点动、反转点动、连续正转、连续反转、停止
  - 位置控制: 相对移动距离输入框、绝对位置移动

### 3. 6轴机器人控制Tab页
- **机器人控制**:
  - 通讯设置: 协议类型(TCP/IP)、IP地址、端口
  - 控制操作: 启动、急停、复位
  - 数据发送: 指令输入框、发送按钮
  - 通信监控: 发送数据显示、接收数据显示、连接状态
  - 状态显示: 机器人当前状态、错误信息

### 4. scara控制Tab页
- **scara通信管理**:
  - 通信设置: 协议类型(Modbus TCP)、IP地址、端口、从Slave ID
  - 连接控制: 连接、断开、重连按钮
  - 通信监控: 通信状态、数据收发记录
- **示教功能**:
  - 回零操作: 全轴回零、单轴回零选择
  - 四轴独立控制:
    * 轴1(X轴): 参数设置(速度、加速度)、正转、反转、点动距离
    * 轴2(Y轴): 参数设置(速度、加速度)、正转、反转、点动距离  
    * 轴3(Z轴): 参数设置(速度、加速度)、正转、反转、点动距离
    * 轴4(R轴): 参数设置(速度、加速度)、正转、反转、点动距离
  - 坐标设置:
    * 左J2节点坐标(X,Y,Z,R)
    * 右J2节点坐标(X,Y,Z,R)
    * 左放置位置坐标(X,Y,Z,R)
    * 右放置位置坐标(X,Y,Z,R)
    * 对位相机位置坐标(X,Y,Z,R)
  - 抓取参数: 抓取高度、抓取速度、放置高度

### 5. IO控制Tab页
- **读取IO**: 显示所有输入端口状态(I0001-I0016)，实时状态指示灯
- **写入IO**: 所有输出端口控制开关(O0001-O0012)，手动控制按钮

### 6. 生产日志管理Tab页
- **选择日志时间**: 按日查看、按周查看、按月查看
- **数据操作**: 数据导出、数据删除
- **统计显示**: 总产量、OK量、NG量、良品率

## 颜色规范
- 主背景色: #2c3e50
- 次背景色: #34495e  
- 菜单背景: #2d3339
- 标题栏: #1e2329
- 激活色: #3498db
- 成功色: #27ae60
- 警告色: #f39c12
- 危险色: #e74c3c
- 文字色: #ffffff
- 边框色: #4a5661