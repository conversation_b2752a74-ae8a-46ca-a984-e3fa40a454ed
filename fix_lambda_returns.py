#!/usr/bin/env python3
"""
批量修复 lambda 表达式返回值问题的脚本
"""

import os
import re
import glob

def fix_lambda_returns_in_file(file_path):
    """修复单个文件中的 lambda 表达式返回值问题"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 查找 SafeExecuteAsync 调用中缺少返回值的 lambda 表达式
        # 模式：await ExceptionHelper.SafeExecuteAsync(async () => { ... }, false, "...")
        pattern = r'(await\s+ExceptionHelper\.SafeExecuteAsync\s*\(\s*async\s*\(\s*\)\s*=>\s*\{[^}]*?)(\s*\}\s*,\s*false\s*,\s*"[^"]*"\s*\))'
        
        def replace_lambda(match):
            lambda_body = match.group(1)
            closing = match.group(2)
            
            # 检查是否已经有 return 语句
            if 'return ' in lambda_body:
                return match.group(0)  # 已经有返回值，不修改
            
            # 在最后的 } 前添加 return true;
            return lambda_body + '\n                return true;' + closing
        
        content = re.sub(pattern, replace_lambda, content, flags=re.DOTALL)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Fixed lambda returns in: {file_path}")
            return True
        
        return False
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    """主函数"""
    # 获取所有 C# 文件
    cs_files = []
    for pattern in ['**/*.cs']:
        cs_files.extend(glob.glob(pattern, recursive=True))
    
    fixed_count = 0
    for file_path in cs_files:
        if fix_lambda_returns_in_file(file_path):
            fixed_count += 1
    
    print(f"\nTotal files fixed: {fixed_count}")

if __name__ == "__main__":
    main()
