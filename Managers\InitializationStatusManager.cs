using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MyHMI.Helpers;

namespace MyHMI.Managers
{
    /// <summary>
    /// 初始化级别枚举
    /// </summary>
    public enum InitializationLevel
    {
        Critical,   // 关键模块（必须成功）
        Important,  // 重要模块（失败时警告）
        Optional    // 可选模块（失败时提示）
    }

    /// <summary>
    /// 系统运行模式枚举
    /// </summary>
    public enum SystemOperationMode
    {
        Full,       // 完整功能（所有模块可用）
        Degraded,   // 降级模式（部分模块不可用）
        Minimal     // 最小模式（仅基本功能）
    }

    /// <summary>
    /// 模块初始化状态
    /// </summary>
    public class ModuleStatus
    {
        public string ModuleName { get; set; }
        public InitializationLevel Level { get; set; }
        public bool IsInitialized { get; set; }
        public bool IsRunning { get; set; }
        public string ErrorMessage { get; set; }
        public DateTime LastCheckTime { get; set; }
        public string Description { get; set; }
    }

    /// <summary>
    /// 初始化报告
    /// </summary>
    public class InitializationReport
    {
        public List<ModuleStatus> CriticalModules { get; set; } = new List<ModuleStatus>();
        public List<ModuleStatus> ImportantModules { get; set; } = new List<ModuleStatus>();
        public List<ModuleStatus> OptionalModules { get; set; } = new List<ModuleStatus>();
        public SystemOperationMode RecommendedMode { get; set; }
        public List<string> UserActions { get; set; } = new List<string>();
        public List<string> WarningMessages { get; set; } = new List<string>();
        public List<string> ErrorMessages { get; set; } = new List<string>();
        public bool CanSwitchToAutomatic { get; set; }
        public DateTime ReportTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 初始化状态管理器
    /// 负责跟踪和管理各个模块的初始化状态，提供详细的状态报告
    /// </summary>
    public class InitializationStatusManager
    {
        #region 单例模式
        private static readonly Lazy<InitializationStatusManager> _instance = 
            new Lazy<InitializationStatusManager>(() => new InitializationStatusManager());
        public static InitializationStatusManager Instance => _instance.Value;

        private InitializationStatusManager()
        {
            InitializeModuleDefinitions();
        }
        #endregion

        #region 私有字段
        private readonly Dictionary<string, ModuleStatus> _moduleStatuses = new Dictionary<string, ModuleStatus>();
        private readonly object _lockObject = new object();
        #endregion

        #region 模块定义初始化
        /// <summary>
        /// 初始化模块定义
        /// </summary>
        private void InitializeModuleDefinitions()
        {
            // 关键模块（必须成功）
            RegisterModule("SystemConfiguration", InitializationLevel.Critical, "系统配置管理");
            RegisterModule("SettingsSystem", InitializationLevel.Critical, "配置系统");

            // 重要模块（失败时警告）
            RegisterModule("DMC1000BCard", InitializationLevel.Important, "DMC1000B控制卡");
            RegisterModule("IOManager", InitializationLevel.Important, "IO管理器");
            RegisterModule("MotorManager", InitializationLevel.Important, "电机管理器");
            RegisterModule("SafetyManager", InitializationLevel.Important, "安全管理器");

            // 可选模块（失败时提示）
            RegisterModule("Scanner", InitializationLevel.Optional, "扫描器");
            RegisterModule("ModbusTCP", InitializationLevel.Optional, "Modbus TCP通信");
            RegisterModule("Robot1", InitializationLevel.Optional, "机器人1");
            RegisterModule("Robot2", InitializationLevel.Optional, "机器人2");
        }

        /// <summary>
        /// 注册模块
        /// </summary>
        private void RegisterModule(string moduleName, InitializationLevel level, string description)
        {
            lock (_lockObject)
            {
                _moduleStatuses[moduleName] = new ModuleStatus
                {
                    ModuleName = moduleName,
                    Level = level,
                    IsInitialized = false,
                    IsRunning = false,
                    Description = description,
                    LastCheckTime = DateTime.Now
                };
            }
        }
        #endregion

        #region 状态更新方法
        /// <summary>
        /// 更新模块状态
        /// </summary>
        public void UpdateModuleStatus(string moduleName, bool isInitialized, bool isRunning = false, string errorMessage = null)
        {
            lock (_lockObject)
            {
                if (_moduleStatuses.ContainsKey(moduleName))
                {
                    var status = _moduleStatuses[moduleName];
                    status.IsInitialized = isInitialized;
                    status.IsRunning = isRunning;
                    status.ErrorMessage = errorMessage;
                    status.LastCheckTime = DateTime.Now;

                    LogHelper.Debug($"模块状态更新: {moduleName} - 初始化:{isInitialized}, 运行:{isRunning}");
                }
                else
                {
                    LogHelper.Warning($"尝试更新未注册的模块状态: {moduleName}");
                }
            }
        }

        /// <summary>
        /// 批量检查模块状态
        /// </summary>
        public async Task<InitializationReport> CheckAllModulesAsync()
        {
            var report = new InitializationReport();

            try
            {
                // 检查关键模块
                await CheckCriticalModulesAsync(report);

                // 检查重要模块
                await CheckImportantModulesAsync(report);

                // 检查可选模块
                await CheckOptionalModulesAsync(report);

                // 确定推荐的运行模式
                DetermineRecommendedMode(report);

                // 生成用户操作建议
                GenerateUserActions(report);

                LogHelper.Info($"系统初始化检查完成 - 推荐模式: {report.RecommendedMode}");
            }
            catch (Exception ex)
            {
                LogHelper.Error("检查模块状态时发生异常", ex);
                report.ErrorMessages.Add($"状态检查异常: {ex.Message}");
            }

            return report;
        }
        #endregion

        #region 私有检查方法
        /// <summary>
        /// 检查关键模块
        /// </summary>
        private async Task CheckCriticalModulesAsync(InitializationReport report)
        {
            await Task.CompletedTask; // 消除警告

            lock (_lockObject)
            {
                foreach (var kvp in _moduleStatuses.Where(m => m.Value.Level == InitializationLevel.Critical))
                {
                    var status = kvp.Value;
                    
                    // 检查系统配置
                    if (status.ModuleName == "SystemConfiguration")
                    {
                        try
                        {
                            var config = Settings.Settings.Current;
                            status.IsInitialized = config != null;
                        }
                        catch (Exception ex)
                        {
                            status.IsInitialized = false;
                            status.ErrorMessage = ex.Message;
                        }
                    }
                    
                    report.CriticalModules.Add(status);
                    
                    if (!status.IsInitialized)
                    {
                        report.ErrorMessages.Add($"关键模块初始化失败: {status.Description}");
                    }
                }
            }
        }

        /// <summary>
        /// 检查重要模块
        /// </summary>
        private async Task CheckImportantModulesAsync(InitializationReport report)
        {
            await Task.CompletedTask; // 消除警告

            lock (_lockObject)
            {
                foreach (var kvp in _moduleStatuses.Where(m => m.Value.Level == InitializationLevel.Important))
                {
                    var status = kvp.Value;
                    
                    // 检查具体模块状态
                    CheckSpecificModuleStatus(status);
                    
                    report.ImportantModules.Add(status);
                    
                    if (!status.IsInitialized)
                    {
                        report.WarningMessages.Add($"重要模块初始化失败: {status.Description} - {status.ErrorMessage}");
                    }
                }
            }
        }

        /// <summary>
        /// 检查可选模块
        /// </summary>
        private async Task CheckOptionalModulesAsync(InitializationReport report)
        {
            await Task.CompletedTask; // 消除警告

            lock (_lockObject)
            {
                foreach (var kvp in _moduleStatuses.Where(m => m.Value.Level == InitializationLevel.Optional))
                {
                    var status = kvp.Value;
                    
                    // 检查具体模块状态
                    CheckSpecificModuleStatus(status);
                    
                    report.OptionalModules.Add(status);
                    
                    if (!status.IsInitialized)
                    {
                        LogHelper.Info($"可选模块不可用: {status.Description}");
                    }
                }
            }
        }

        /// <summary>
        /// 检查具体模块状态
        /// </summary>
        private void CheckSpecificModuleStatus(ModuleStatus status)
        {
            try
            {
                switch (status.ModuleName)
                {
                    case "DMC1000BCard":
                        status.IsInitialized = DMC1000BCardManager.Instance.IsCardAvailable();
                        break;
                    case "IOManager":
                        status.IsInitialized = DMC1000BIOManager.Instance.IsInitialized;
                        status.IsRunning = DMC1000BIOManager.Instance.IsMonitoring;
                        break;
                    case "MotorManager":
                        status.IsInitialized = DMC1000BMotorManager.Instance.IsInitialized;
                        break;
                    case "SafetyManager":
                        status.IsInitialized = SafetyManager.Instance.IsInitialized;
                        status.IsRunning = SafetyManager.Instance.IsRunning;
                        break;
                    default:
                        // 其他模块的检查逻辑
                        break;
                }
                
                status.LastCheckTime = DateTime.Now;
            }
            catch (Exception ex)
            {
                status.IsInitialized = false;
                status.ErrorMessage = ex.Message;
                LogHelper.Warning($"检查模块 {status.ModuleName} 状态时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 确定推荐的运行模式
        /// </summary>
        private void DetermineRecommendedMode(InitializationReport report)
        {
            // 检查关键模块
            bool allCriticalOk = report.CriticalModules.All(m => m.IsInitialized);
            if (!allCriticalOk)
            {
                report.RecommendedMode = SystemOperationMode.Minimal;
                report.CanSwitchToAutomatic = false;
                return;
            }

            // 检查重要模块
            int importantOkCount = report.ImportantModules.Count(m => m.IsInitialized);
            int totalImportant = report.ImportantModules.Count;

            if (importantOkCount == totalImportant)
            {
                report.RecommendedMode = SystemOperationMode.Full;
                report.CanSwitchToAutomatic = true;
            }
            else if (importantOkCount >= totalImportant / 2)
            {
                report.RecommendedMode = SystemOperationMode.Degraded;
                report.CanSwitchToAutomatic = true; // 允许降级运行
            }
            else
            {
                report.RecommendedMode = SystemOperationMode.Minimal;
                // 根据用户需求，即使重要模块大部分失败，也允许最小模式运行
                report.CanSwitchToAutomatic = true; // 允许最小模式运行
            }
        }

        /// <summary>
        /// 生成用户操作建议
        /// </summary>
        private void GenerateUserActions(InitializationReport report)
        {
            if (report.RecommendedMode == SystemOperationMode.Full)
            {
                report.UserActions.Add("✅ 系统完全就绪，所有功能可用");
            }
            else if (report.RecommendedMode == SystemOperationMode.Degraded)
            {
                report.UserActions.Add("⚠️ 系统在降级模式下运行，部分功能受限");
                report.UserActions.Add("💡 建议检查硬件连接后重启程序");
            }
            else
            {
                report.UserActions.Add("❌ 系统仅能在最小模式下运行");
                report.UserActions.Add("🔧 请检查系统配置和硬件连接");
                report.UserActions.Add("📞 如需帮助，请联系技术支持");
            }

            // 添加具体的故障模块建议
            foreach (var module in report.ImportantModules.Where(m => !m.IsInitialized))
            {
                switch (module.ModuleName)
                {
                    case "DMC1000BCard":
                        report.UserActions.Add("🔌 请检查DMC1000B控制卡连接");
                        break;
                    case "IOManager":
                        report.UserActions.Add("⚡ IO管理器不可用，请检查控制卡状态");
                        break;
                    case "SafetyManager":
                        report.UserActions.Add("🛡️ 安全管理器不可用，自动模式功能受限");
                        break;
                }
            }
        }
        #endregion
    }
}
