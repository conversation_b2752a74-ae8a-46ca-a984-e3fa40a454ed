# MyHMI 上位机控制系统 - 架构设计文档

## 系统架构概述

MyHMI 采用分层架构设计，结合事件驱动模式和单例模式，实现了高内聚、低耦合的系统架构。系统遵循"UI Thread + Background Task + Event Callback"的核心架构模式。

## 架构层次结构

```
┌─────────────────────────────────────────────────────────────┐
│                    表示层 (Presentation Layer)                │
├─────────────────────────────────────────────────────────────┤
│  MainForm  │  UserControls  │  TestRunnerForm  │  Dialogs   │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    业务逻辑层 (Business Layer)                │
├─────────────────────────────────────────────────────────────┤
│  WorkflowManager  │  各种硬件Manager  │  StatisticsManager  │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    服务层 (Service Layer)                    │
├─────────────────────────────────────────────────────────────┤
│  LogHelper  │  ConfigHelper  │  ExceptionHelper  │  UIHelper │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    数据层 (Data Layer)                       │
├─────────────────────────────────────────────────────────────┤
│  Models  │  Events  │  Configuration  │  File System       │
└─────────────────────────────────────────────────────────────┘
```

## 核心架构模式

### 1. "UI Thread + Background Task + Event Callback" 模式

这是系统的核心架构模式，确保了 UI 响应性和系统稳定性：

```csharp
// UI 线程处理用户交互
private async void StartButton_Click(object sender, EventArgs e)
{
    // 在后台线程执行耗时操作
    await Task.Run(() => MotorManager.Instance.MoveToAsync(0, 100, 1000));
}

// 后台任务执行硬件操作
public async Task<bool> MoveToAsync(int axisId, double position, double speed)
{
    // 执行硬件操作
    var result = await ExecuteMotorCommand(axisId, position, speed);
    
    // 通过事件回调通知 UI
    MotorPositionChanged?.Invoke(this, new MotorPositionEventArgs(axisId, position));
    
    return result;
}

// 事件回调更新 UI
private void OnMotorPositionChanged(object sender, MotorPositionEventArgs e)
{
    UIHelper.SafeInvoke(() => {
        // 线程安全的 UI 更新
        UpdateMotorPositionDisplay(e.AxisId, e.Position);
    });
}
```

### 2. 单例模式 (Singleton Pattern)

所有 Manager 类采用线程安全的单例模式：

```csharp
public class MotorManager
{
    private static readonly Lazy<MotorManager> _instance = 
        new Lazy<MotorManager>(() => new MotorManager());
    
    public static MotorManager Instance => _instance.Value;
    
    private MotorManager() { }
}
```

**优势**：
- 确保全局状态一致性
- 简化对象生命周期管理
- 节省系统资源

### 3. 事件驱动架构 (Event-Driven Architecture)

系统采用发布-订阅模式实现模块间通信：

```csharp
// 发布者
public event EventHandler<IOStateChangedEventArgs> IOStateChanged;

// 订阅者
IOManager.Instance.IOStateChanged += OnIOStateChanged;

// 事件处理
private void OnIOStateChanged(object sender, IOStateChangedEventArgs e)
{
    // 处理 IO 状态变化
}
```

## 设计原则

### 1. 单一职责原则 (Single Responsibility Principle)
- 每个类只负责一个特定的功能
- Manager 类专注于硬件控制
- Helper 类专注于辅助功能
- Model 类专注于数据表示

### 2. 开闭原则 (Open-Closed Principle)
- 系统对扩展开放，对修改关闭
- 通过接口和抽象类支持功能扩展
- 新增硬件设备无需修改现有代码

### 3. 依赖倒置原则 (Dependency Inversion Principle)
- 高层模块不依赖低层模块
- 通过事件和接口实现解耦
- 支持依赖注入和测试

### 4. 接口隔离原则 (Interface Segregation Principle)
- 接口设计精简，避免冗余
- 客户端只依赖需要的接口
- 支持模块化开发

## 组件间通信机制

### 1. 工作流驱动通信

```
扫描枪 → 条码识别 → 视觉定位 → 电机运动 → 机器人操作
   │         │         │         │         │
   ▼         ▼         ▼         ▼         ▼
事件发布 → 事件处理 → 事件发布 → 事件处理 → 事件发布
```

### 2. 事件链路图

```
ScannerManager.BarcodeScanned
    ↓
WorkflowManager.OnBarcodeScanned
    ↓
VisionManager.StartPositioning
    ↓
VisionManager.PositioningCompleted
    ↓
WorkflowManager.OnPositioningCompleted
    ↓
MotorManager.MoveToPosition
    ↓
MotorManager.MotorPositionChanged
    ↓
WorkflowManager.OnMotorPositionChanged
    ↓
RobotTcpManager.SendCommand
```

## 异步编程模式

### 1. async/await 模式
```csharp
public async Task<bool> InitializeAsync()
{
    try
    {
        await Task.Run(() => InitializeHardware());
        return true;
    }
    catch (Exception ex)
    {
        LogHelper.Error("初始化失败", ex);
        return false;
    }
}
```

### 2. 任务并行处理
```csharp
public async Task InitializeAllManagersAsync()
{
    var tasks = new[]
    {
        IOManager.Instance.InitializeAsync(),
        MotorManager.Instance.InitializeAsync(),
        VisionManager.Instance.InitializeAsync()
    };
    
    await Task.WhenAll(tasks);
}
```

## 线程安全设计

### 1. UI 线程安全更新
```csharp
public static class UIHelper
{
    public static void SafeInvoke(Action action)
    {
        if (Application.OpenForms.Count > 0)
        {
            var form = Application.OpenForms[0];
            if (form.InvokeRequired)
            {
                form.Invoke(action);
            }
            else
            {
                action();
            }
        }
    }
}
```

### 2. 共享资源保护
```csharp
private readonly object _lockObject = new object();

public void UpdateSharedResource()
{
    lock (_lockObject)
    {
        // 线程安全的资源访问
    }
}
```

## 错误处理架构

### 1. 统一异常处理
```csharp
public static class ExceptionHelper
{
    public static async Task<T> SafeExecuteAsync<T>(
        Func<Task<T>> action, 
        T defaultValue, 
        string operationName)
    {
        try
        {
            return await action();
        }
        catch (Exception ex)
        {
            LogHelper.Error($"{operationName}执行失败", ex);
            return defaultValue;
        }
    }
}
```

### 2. 分层错误处理
- **UI 层**：显示用户友好的错误信息
- **业务层**：记录详细错误日志，实现重试机制
- **服务层**：提供统一的异常处理接口

## 配置管理架构

### 1. 多层配置支持
```
App.config (应用程序基础配置)
    ↓
SystemConfig.json (系统详细配置)
    ↓
运行时动态配置
```

### 2. 配置热重载
```csharp
public bool ReloadConfiguration()
{
    try
    {
        LoadConfigurationFromFile();
        NotifyConfigurationChanged();
        return true;
    }
    catch (Exception ex)
    {
        LogHelper.Error("配置重载失败", ex);
        return false;
    }
}
```

## 测试架构

### 1. Mock 对象模式
```csharp
public class MockIOManager : IIOManager
{
    // 模拟硬件行为，支持无硬件测试
}
```

### 2. 单元测试框架
```csharp
public class SimpleTestFramework
{
    // 轻量级测试框架，支持断言和测试报告
}
```

## 性能优化策略

### 1. 异步操作
- 所有硬件操作采用异步模式
- 避免 UI 线程阻塞
- 提高系统响应性

### 2. 事件去抖动
```csharp
private Timer _debounceTimer;

private void OnHighFrequencyEvent()
{
    _debounceTimer?.Stop();
    _debounceTimer = new Timer(500);
    _debounceTimer.Elapsed += ProcessEvent;
    _debounceTimer.Start();
}
```

### 3. 资源池化
- 重用对象实例
- 减少 GC 压力
- 提高内存使用效率

## 扩展性设计

### 1. 插件化架构支持
- 通过接口定义扩展点
- 支持动态加载新模块
- 实现热插拔功能

### 2. 配置驱动开发
- 通过配置文件控制功能开关
- 支持不同硬件配置
- 实现灵活的系统定制

这个架构设计确保了系统的可维护性、可扩展性、可测试性和高性能，为上位机控制系统提供了坚实的技术基础。
