# 运动控制卡功能封装指南

## 1. 概述

本指南旨在总结DMC1000B运动控制卡在C#中的功能封装方法。通过使用.NET的P/Invoke技术，将原生DLL库中的函数封装成易于使用的C#类，从而简化运动控制应用的开发。

## 2. 封装方法

### 2.1 使用P/Invoke技术

所有与运动控制卡的交互都是通过调用`Dmc1000.dll`动态库实现的。在C#中，使用`DllImport`特性来声明外部函数：

```csharp
[DllImport("Dmc1000.dll", EntryPoint = "函数名", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
public static extern 返回类型 函数名(参数列表);
```

### 2.2 命名空间和类结构

所有函数被封装在一个名为`Dmc1000`的类中，该类位于`csDmc1000`命名空间下。这种结构使得功能组织清晰，便于调用。

### 2.3 函数分类

根据功能，封装的函数可以分为以下几类：

1. **板卡初始化与关闭**
   - `d1000_board_init()`: 初始化控制卡
   - `d1000_board_close()`: 关闭控制卡

2. **轴参数设置**
   - `d1000_set_pls_outmode()`: 设置脉冲输出模式
   - `d1000_set_s_profile()`: 设置S型速度曲线参数
   - `d1000_get_s_profile()`: 读取S型速度曲线参数

3. **运动控制**
   - `d1000_start_t_move()`: 指定脉冲数的相对运动
   - `d1000_start_ta_move()`: 指定绝对位置的绝对运动
   - `d1000_start_s_move()`: S型速度曲线的相对运动
   - `d1000_start_sa_move()`: S型速度曲线的绝对运动
   - `d1000_start_tv_move()`: 指定速度的连续运动
   - `d1000_start_sv_move()`: S型速度曲线的连续运动
   - `d1000_start_t_line()`: 多轴直线插补相对运动
   - `d1000_start_ta_line()`: 多轴直线插补绝对运动
   - `d1000_home_move()`: 回零运动

4. **速度控制**
   - `d1000_get_speed()`: 读取当前速度
   - `d1000_change_speed()`: 动态改变运行速度
   - `d1000_decel_stop()`: 减速停止
   - `d1000_immediate_stop()`: 立即停止

5. **状态检测**
   - `d1000_check_done()`: 检查运动是否完成
   - `d1000_get_command_pos()`: 读取指令位置
   - `d1000_set_command_pos()`: 设置指令位置
   - `d1000_get_axis_status()`: 读取轴状态

6. **IO控制**
   - `d1000_out_bit()`: 输出位操作
   - `d1000_in_bit()`: 输入位检测
   - `d1000_get_outbit()`: 读取输出位状态
   - `d1000_in_enable()`: 启用输入口

7. **其他功能**
   - `d1000_set_sd()`: 设置减速停止模式
   - `d1000_WriteDWord()`: 写双字数据
   - `d1000_ReadDWord()`: 读双字数据

## 3. 使用示例

```csharp
// 初始化控制卡
int result = Dmc1000.d1000_board_init();

// 设置轴0的脉冲输出模式
Dmc1000.d1000_set_pls_outmode(0, 0);

// 启动轴0的相对运动
Dmc1000.d1000_start_t_move(0, 1000, 100, 500, 0.5);

// 检查运动是否完成
while (Dmc1000.d1000_check_done(0) == 0)
{
    System.Threading.Thread.Sleep(10);
}

// 关闭控制卡
Dmc1000.d1000_board_close();
```

## 4. 注意事项

1. 在使用任何功能之前，必须先调用`d1000_board_init()`初始化控制卡。
2. 所有运动函数都是非阻塞的，需要通过`d1000_check_done()`来检测运动是否完成。
3. 在程序结束前，应调用`d1000_board_close()`关闭控制卡，释放资源。
4. 参数传递时要注意数据类型匹配，特别是涉及到指针的参数需要使用`ref`关键字。