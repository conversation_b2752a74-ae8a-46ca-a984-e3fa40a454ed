using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using MyHMI.Helpers;
using MyHMI.Managers;
using MyHMI.Models;
using MyHMI.Events;


namespace MyHMI.UI.Controls
{
    /// <summary>
    /// 6轴机器人2控制面板 - Epson机器人控制
    /// </summary>
    public partial class Robot6AxisPanel2 : UserControl
    {
        #region 私有字段

        private Panel _mainPanel;
        private Label _titleLabel;
        private Panel _commGroup;
        private Panel _controlGroup;
        private Panel _dataGroup;
        private Panel _monitorGroup;

        // Epson机器人管理器 - 机器人2使用独立的管理器实例
        private EpsonRobotManager2 _epsonRobotManager;

        // 机器人IP配置控件
        private TextBox _robotIPTextBox;
        private TextBox _controlPortTextBox;
        private TextBox _dataPortTextBox;

        // 参数保存相关
        private bool _isInitializing = false; // 防止初始化时触发保存

        // 连接控制按钮
        private Button _controlConnectButton;
        private Button _controlDisconnectButton;
        private Button _dataConnectButton;
        private Button _dataDisconnectButton;

        // 机器人控制按钮
        private Button _startButton;
        private Button _stopButton;

        // 数据输入发送控件
        private TextBox _commandInputTextBox;
        private Button _sendButton;

        // 通讯监听控件
        private TextBox _sendDataTextBox;
        private TextBox _receiveDataTextBox;
        private Label _controlConnectionStatusLabel;
        private Label _dataConnectionStatusLabel;

        #endregion

        #region 构造函数

        public Robot6AxisPanel2()
        {
            InitializeComponent();
            InitializeEpsonRobotManager();
            InitializeInterface();
        }



        #endregion

        #region 界面初始化

        /// <summary>
        /// 初始化界面 - 按HTML原型精确设计
        /// </summary>
        private void InitializeInterface()
        {
            try
            {
                // 设置面板属性 - 增加高度以容纳所有控件
                this.Size = new Size(660, 1000);
                this.BackColor = ColorTranslator.FromHtml("#34495e");
                this.Dock = DockStyle.Fill;
                this.Padding = new Padding(0);
                this.AutoScroll = true; // 启用滚动条

                // 创建主面板 - 按HTML原型padding: 20px
                CreateMainPanel();

                // 创建标题 - 按HTML原型样式
                CreateTitle();

                // 创建通讯设置组 - 按HTML原型样式
                CreateCommGroup();

                // 创建控制操作组 - 按HTML原型样式
                CreateControlGroup();

                // 创建数据发送组 - 按HTML原型样式
                CreateDataGroup();

                // 创建通信监控组 - 按HTML原型样式
                CreateMonitorGroup();

                LogHelper.Info("6轴机器人2控制面板初始化完成 - 按HTML原型设计");
            }
            catch (Exception ex)
            {
                LogHelper.Error("6轴机器人2控制面板初始化失败", ex);
            }
        }

        /// <summary>
        /// 创建主面板 - 按HTML原型
        /// </summary>
        private void CreateMainPanel()
        {
            _mainPanel = new Panel
            {
                Size = new Size(620, 1200), // 增加高度，减少宽度以适应滚动条
                Location = new Point(0, 0),
                BackColor = ColorTranslator.FromHtml("#34495e"),
                Padding = new Padding(20), // 按HTML原型 padding: 20px
                AutoSize = true,
                AutoSizeMode = AutoSizeMode.GrowAndShrink
            };

            this.Controls.Add(_mainPanel);
        }

        /// <summary>
        /// 创建标题 - 按HTML原型样式
        /// </summary>
        private void CreateTitle()
        {
            _titleLabel = new Label
            {
                Text = "6轴机器人2控制",
                Font = new Font("微软雅黑", 20F, FontStyle.Bold), // 按HTML原型 font-size: 20px
                ForeColor = ColorTranslator.FromHtml("#3498db"),   // 按HTML原型 color: #3498db
                Size = new Size(300, 30),
                Location = new Point(0, 0),
                AutoSize = true
            };

            _mainPanel.Controls.Add(_titleLabel);
        }

        /// <summary>
        /// 创建通讯设置组 - Epson机器人单IP双端口配置
        /// </summary>
        private void CreateCommGroup()
        {
            _commGroup = new Panel
            {
                Size = new Size(580, 220), // 减少宽度以适应滚动条
                Location = new Point(0, 50), // 标题下方20px间距
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15)
            };

            // 设置边框颜色
            _commGroup.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _commGroup.Width - 1, _commGroup.Height - 1);
                }
            };

            // 创建组标题
            var groupTitle = new Label
            {
                Text = "通讯设置（单IP双端口）",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(300, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };

            // 从配置文件加载默认值
            var defaultConfig = LoadDefaultRobotConfig();

            // 设置初始化标志
            _isInitializing = true;

            // 机器人IP配置
            CreateRobotIPConfigGroup(_commGroup, "机器人IP:", defaultConfig.IPAddress, defaultConfig.ControlPort.ToString(), 35,
                out _robotIPTextBox, out _controlPortTextBox);

            // 数据端口配置
            CreatePortConfigGroup(_commGroup, "数据端口:", defaultConfig.DataPort.ToString(), 95,
                out _dataPortTextBox);

            // 连接按钮组
            CreateConnectionButtons(_commGroup, 125);

            // 连接状态显示
            CreateConnectionStatus(_commGroup, 165);

            _commGroup.Controls.Add(groupTitle);
            _mainPanel.Controls.Add(_commGroup);

            // 订阅参数变更事件（在控件创建完成后）
            SubscribeToConfigurationChanges();

            // 初始化完成
            _isInitializing = false;
        }

        /// <summary>
        /// 创建IP配置组
        /// </summary>
        /// <param name="parent">父控件</param>
        /// <param name="labelText">标签文本</param>
        /// <param name="defaultIP">默认IP</param>
        /// <param name="defaultPort">默认端口</param>
        /// <param name="yOffset">Y偏移</param>
        /// <param name="ipTextBox">IP输入框</param>
        /// <param name="portTextBox">端口输入框</param>
        private void CreateIPConfigGroup(Panel parent, string labelText, string defaultIP, string defaultPort,
            int yOffset, out TextBox ipTextBox, out TextBox portTextBox)
        {
            // 标签
            var label = new Label
            {
                Text = labelText,
                Font = new Font("微软雅黑", 14F),
                ForeColor = Color.White,
                Size = new Size(120, 30),
                Location = new Point(0, yOffset),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // IP地址输入框
            ipTextBox = new TextBox
            {
                Text = defaultIP,
                Font = new Font("微软雅黑", 12F),
                Size = new Size(120, 25),
                Location = new Point(130, yOffset + 2),
                BackColor = ColorTranslator.FromHtml("#34495e"),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // 端口标签
            var portLabel = new Label
            {
                Text = "端口:",
                Font = new Font("微软雅黑", 12F),
                ForeColor = Color.White,
                Size = new Size(40, 25),
                Location = new Point(260, yOffset + 2),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // 端口输入框
            portTextBox = new TextBox
            {
                Text = defaultPort,
                Font = new Font("微软雅黑", 12F),
                Size = new Size(80, 25),
                Location = new Point(300, yOffset + 2),
                BackColor = ColorTranslator.FromHtml("#34495e"),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            parent.Controls.Add(label);
            parent.Controls.Add(ipTextBox);
            parent.Controls.Add(portLabel);
            parent.Controls.Add(portTextBox);
        }

        /// <summary>
        /// 创建机器人IP配置组（IP + 控制端口）
        /// </summary>
        private void CreateRobotIPConfigGroup(Panel parent, string labelText, string defaultIP, string defaultPort,
            int yOffset, out TextBox ipTextBox, out TextBox portTextBox)
        {
            // 标签
            var label = new Label
            {
                Text = labelText,
                Font = new Font("微软雅黑", 14F),
                ForeColor = Color.White,
                Size = new Size(100, 30),
                Location = new Point(0, yOffset),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // IP地址输入框
            ipTextBox = new TextBox
            {
                Text = defaultIP,
                Font = new Font("微软雅黑", 12F),
                Size = new Size(120, 25),
                Location = new Point(110, yOffset + 2),
                BackColor = ColorTranslator.FromHtml("#34495e"),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // 控制端口标签
            var portLabel = new Label
            {
                Text = "控制端口:",
                Font = new Font("微软雅黑", 12F),
                ForeColor = Color.White,
                Size = new Size(70, 25),
                Location = new Point(240, yOffset + 2),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // 控制端口输入框
            portTextBox = new TextBox
            {
                Text = defaultPort,
                Font = new Font("微软雅黑", 12F),
                Size = new Size(80, 25),
                Location = new Point(310, yOffset + 2),
                BackColor = ColorTranslator.FromHtml("#34495e"),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            parent.Controls.Add(label);
            parent.Controls.Add(ipTextBox);
            parent.Controls.Add(portLabel);
            parent.Controls.Add(portTextBox);
        }

        /// <summary>
        /// 创建端口配置组（只显示端口）
        /// </summary>
        private void CreatePortConfigGroup(Panel parent, string labelText, string defaultPort,
            int yOffset, out TextBox portTextBox)
        {
            // 标签
            var label = new Label
            {
                Text = labelText,
                Font = new Font("微软雅黑", 14F),
                ForeColor = Color.White,
                Size = new Size(100, 30),
                Location = new Point(0, yOffset),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // 端口输入框
            portTextBox = new TextBox
            {
                Text = defaultPort,
                Font = new Font("微软雅黑", 12F),
                Size = new Size(80, 25),
                Location = new Point(110, yOffset + 2),
                BackColor = ColorTranslator.FromHtml("#34495e"),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            parent.Controls.Add(label);
            parent.Controls.Add(portTextBox);
        }

        /// <summary>
        /// 创建连接按钮组
        /// </summary>
        private void CreateConnectionButtons(Panel parent, int yOffset)
        {
            // 控制连接按钮
            _controlConnectButton = new Button
            {
                Text = "控制连接",
                Font = new Font("微软雅黑", 10F),
                Size = new Size(80, 30),
                Location = new Point(0, yOffset),
                BackColor = ColorTranslator.FromHtml("#27ae60"),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
            _controlConnectButton.FlatAppearance.BorderSize = 0;
            _controlConnectButton.Click += ControlConnectButton_Click;

            // 控制断开按钮
            _controlDisconnectButton = new Button
            {
                Text = "控制断开",
                Font = new Font("微软雅黑", 10F),
                Size = new Size(80, 30),
                Location = new Point(90, yOffset),
                BackColor = ColorTranslator.FromHtml("#e74c3c"),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false,
                Enabled = false
            };
            _controlDisconnectButton.FlatAppearance.BorderSize = 0;
            _controlDisconnectButton.Click += ControlDisconnectButton_Click;

            // 数据连接按钮
            _dataConnectButton = new Button
            {
                Text = "数据连接",
                Font = new Font("微软雅黑", 10F),
                Size = new Size(80, 30),
                Location = new Point(200, yOffset),
                BackColor = ColorTranslator.FromHtml("#3498db"),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false,
                Enabled = false // 初始状态禁用，需要机器人启动后才能连接数据端口
            };
            _dataConnectButton.FlatAppearance.BorderSize = 0;
            _dataConnectButton.Click += DataConnectButton_Click;

            // 数据断开按钮
            _dataDisconnectButton = new Button
            {
                Text = "数据断开",
                Font = new Font("微软雅黑", 10F),
                Size = new Size(80, 30),
                Location = new Point(290, yOffset),
                BackColor = ColorTranslator.FromHtml("#e67e22"),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false,
                Enabled = false
            };
            _dataDisconnectButton.FlatAppearance.BorderSize = 0;
            _dataDisconnectButton.Click += DataDisconnectButton_Click;

            parent.Controls.Add(_controlConnectButton);
            parent.Controls.Add(_controlDisconnectButton);
            parent.Controls.Add(_dataConnectButton);
            parent.Controls.Add(_dataDisconnectButton);
        }

        /// <summary>
        /// 创建连接状态显示
        /// </summary>
        private void CreateConnectionStatus(Panel parent, int yOffset)
        {
            // 控制端口状态
            var controlStatusLabel = new Label
            {
                Text = "控制端口:",
                Font = new Font("微软雅黑", 12F),
                ForeColor = Color.White,
                Size = new Size(80, 20),
                Location = new Point(0, yOffset),
                AutoSize = true
            };

            _controlConnectionStatusLabel = new Label
            {
                Text = "未连接",
                Font = new Font("微软雅黑", 12F),
                ForeColor = ColorTranslator.FromHtml("#e74c3c"), // 红色表示未连接
                Size = new Size(60, 20),
                Location = new Point(80, yOffset),
                AutoSize = true
            };

            // 数据端口状态
            var dataStatusLabel = new Label
            {
                Text = "数据端口:",
                Font = new Font("微软雅黑", 12F),
                ForeColor = Color.White,
                Size = new Size(80, 20),
                Location = new Point(200, yOffset),
                AutoSize = true
            };

            _dataConnectionStatusLabel = new Label
            {
                Text = "未连接",
                Font = new Font("微软雅黑", 12F),
                ForeColor = ColorTranslator.FromHtml("#e74c3c"), // 红色表示未连接
                Size = new Size(60, 20),
                Location = new Point(280, yOffset),
                AutoSize = true
            };

            parent.Controls.Add(controlStatusLabel);
            parent.Controls.Add(_controlConnectionStatusLabel);
            parent.Controls.Add(dataStatusLabel);
            parent.Controls.Add(_dataConnectionStatusLabel);
        }

        /// <summary>
        /// 创建控制操作组 - Epson机器人启动停止控制
        /// </summary>
        private void CreateControlGroup()
        {
            _controlGroup = new Panel
            {
                Size = new Size(580, 80), // 减少高度，移除自动化状态显示
                Location = new Point(0, 290), // 修复位置：通讯设置组(50+220)+20间距=290
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15)
            };

            // 设置边框颜色
            _controlGroup.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _controlGroup.Width - 1, _controlGroup.Height - 1);
                }
            };

            // 创建组标题
            var groupTitle = new Label
            {
                Text = "控制操作",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(200, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };

            // 创建启动按钮
            _startButton = CreateControlButton("启动机器人", new Point(0, 35), ColorTranslator.FromHtml("#27ae60"));
            _startButton.Click += StartButton_Click;
            _startButton.Enabled = false; // 初始状态禁用，需要先连接

            // 创建停止按钮
            _stopButton = CreateControlButton("停止机器人", new Point(140, 35), ColorTranslator.FromHtml("#e74c3c"));
            _stopButton.Click += StopButton_Click;
            _stopButton.Enabled = false; // 初始状态禁用

            _controlGroup.Controls.Add(groupTitle);
            _controlGroup.Controls.Add(_startButton);
            _controlGroup.Controls.Add(_stopButton);

            _mainPanel.Controls.Add(_controlGroup);
        }

        /// <summary>
        /// 创建数据发送组 - Epson机器人数据发送
        /// </summary>
        private void CreateDataGroup()
        {
            _dataGroup = new Panel
            {
                Size = new Size(580, 80), // 减少宽度以适应滚动条
                Location = new Point(0, 430), // 修复位置：控制操作组(290+120)+20间距=430
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15)
            };

            // 设置边框颜色
            _dataGroup.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _dataGroup.Width - 1, _dataGroup.Height - 1);
                }
            };

            // 创建组标题
            var groupTitle = new Label
            {
                Text = "数据发送",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(200, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };

            // 数据输入
            var label = new Label
            {
                Text = "数据发送:",
                Font = new Font("微软雅黑", 14F),
                ForeColor = Color.White,
                Size = new Size(100, 30),
                Location = new Point(0, 35),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _commandInputTextBox = new TextBox
            {
                Font = new Font("微软雅黑", 12F),
                Size = new Size(210, 25),
                Location = new Point(110, 37), // 调整位置以适应更长的标签
                BackColor = ColorTranslator.FromHtml("#34495e"),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Text = "" // 用户输入数据
            };

            _sendButton = new Button
            {
                Text = "发送",
                Font = new Font("微软雅黑", 12F),
                Size = new Size(80, 25),
                Location = new Point(330, 37), // 位置保持不变
                BackColor = ColorTranslator.FromHtml("#3498db"),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false,
                Cursor = Cursors.Hand
            };
            _sendButton.Click += SendButton_Click;

            _dataGroup.Controls.Add(groupTitle);
            _dataGroup.Controls.Add(label);
            _dataGroup.Controls.Add(_commandInputTextBox);
            _dataGroup.Controls.Add(_sendButton);

            _mainPanel.Controls.Add(_dataGroup);
        }

        /// <summary>
        /// 创建通信监控组 - Epson机器人通信监听
        /// </summary>
        private void CreateMonitorGroup()
        {
            _monitorGroup = new Panel
            {
                Size = new Size(580, 200), // 减少宽度以适应滚动条
                Location = new Point(0, 530), // 修复位置：数据发送组(430+80)+20间距=530
                BackColor = ColorTranslator.FromHtml("#2c3e50"),
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15)
            };

            // 设置边框颜色
            _monitorGroup.Paint += (s, e) => {
                using (var pen = new Pen(ColorTranslator.FromHtml("#4a5661"), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, _monitorGroup.Width - 1, _monitorGroup.Height - 1);
                }
            };

            // 创建组标题
            var groupTitle = new Label
            {
                Text = "通信监控",
                Font = new Font("微软雅黑", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(200, 25),
                Location = new Point(0, 0),
                AutoSize = true
            };

            // 创建发送数据区域
            var sendLabel = new Label
            {
                Text = "发送数据:",
                Font = new Font("微软雅黑", 12F),
                ForeColor = Color.White,
                Size = new Size(100, 20),
                Location = new Point(0, 35),
                AutoSize = true
            };

            _sendDataTextBox = new TextBox
            {
                Font = new Font("Consolas", 10F),
                Size = new Size(270, 100),
                Location = new Point(0, 55),
                BackColor = ColorTranslator.FromHtml("#1e1e1e"),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                Text = "" // 实时显示发送的数据
            };

            // 创建接收数据区域
            var receiveLabel = new Label
            {
                Text = "接收数据:",
                Font = new Font("微软雅黑", 12F),
                ForeColor = Color.White,
                Size = new Size(100, 20),
                Location = new Point(290, 35),
                AutoSize = true
            };

            _receiveDataTextBox = new TextBox
            {
                Font = new Font("Consolas", 10F),
                Size = new Size(270, 100),
                Location = new Point(290, 55),
                BackColor = ColorTranslator.FromHtml("#1e1e1e"),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                Text = "" // 实时显示接收的数据
            };

            _monitorGroup.Controls.Add(groupTitle);
            _monitorGroup.Controls.Add(sendLabel);
            _monitorGroup.Controls.Add(_sendDataTextBox);
            _monitorGroup.Controls.Add(receiveLabel);
            _monitorGroup.Controls.Add(_receiveDataTextBox);

            _mainPanel.Controls.Add(_monitorGroup);
        }

        /// <summary>
        /// 创建控制按钮 - 按HTML原型样式
        /// </summary>
        private Button CreateControlButton(string text, Point location, Color backColor)
        {
            var button = new Button
            {
                Text = text,
                Location = location,
                Size = new Size(100, 35), // 按HTML原型调整按钮尺寸
                BackColor = backColor,
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 14F, FontStyle.Bold), // 按HTML原型字体
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false,
                Cursor = Cursors.Hand
            };

            // 设置按钮边框 - 按HTML原型
            button.FlatAppearance.BorderSize = 1;
            button.FlatAppearance.BorderColor = ColorTranslator.FromHtml("#4a5661");

            return button;
        }

        #endregion

        #region Epson机器人管理器初始化和事件处理

        /// <summary>
        /// 初始化Epson机器人管理器
        /// </summary>
        private async void InitializeEpsonRobotManager()
        {
            try
            {
                _epsonRobotManager = EpsonRobotManager2.Instance;

                // 订阅事件
                SubscribeToEpsonRobotEvents();

                // 初始化管理器
                await _epsonRobotManager.InitializeAsync();

                LogHelper.Info("Epson机器人管理器初始化完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("初始化Epson机器人管理器失败", ex);
                MessageBox.Show($"初始化Epson机器人管理器失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 订阅Epson机器人事件
        /// </summary>
        private void SubscribeToEpsonRobotEvents()
        {
            if (_epsonRobotManager != null)
            {
                _epsonRobotManager.ConnectionStatusChanged += OnConnectionStatusChanged;
                _epsonRobotManager.ResponseReceived += OnResponseReceived;
                _epsonRobotManager.CommandSent += OnCommandSent;
                _epsonRobotManager.RobotStarted += OnRobotStarted;
                _epsonRobotManager.SpecialCommandReceived += OnSpecialCommandReceived;
                _epsonRobotManager.RobotError += OnRobotError;
            }
        }

        /// <summary>
        /// 取消订阅Epson机器人事件
        /// </summary>
        private void UnsubscribeFromEpsonRobotEvents()
        {
            if (_epsonRobotManager != null)
            {
                _epsonRobotManager.ConnectionStatusChanged -= OnConnectionStatusChanged;
                _epsonRobotManager.ResponseReceived -= OnResponseReceived;
                _epsonRobotManager.CommandSent -= OnCommandSent;
                _epsonRobotManager.RobotStarted -= OnRobotStarted;
                _epsonRobotManager.SpecialCommandReceived -= OnSpecialCommandReceived;
                _epsonRobotManager.RobotError -= OnRobotError;
            }
        }

        #endregion

        #region 配置管理

        /// <summary>
        /// 加载默认机器人配置
        /// </summary>
        /// <returns>默认配置</returns>
        private EpsonRobotConfiguration LoadDefaultRobotConfig()
        {
            try
            {
                var communicationSettings = Settings.Settings.Current.Communication;
                if (communicationSettings != null)
                {
                    return new EpsonRobotConfiguration
                    {
                        IPAddress = communicationSettings.EpsonRobot2IP ?? "*************",
                        ControlPort = communicationSettings.EpsonRobot2ControlPort,
                        DataPort = communicationSettings.EpsonRobot2DataPort,
                        Password = communicationSettings.EpsonRobot2Password ?? "EPSON"
                    };
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("加载机器人配置失败", ex);
            }

            // 返回硬编码默认值
            return new EpsonRobotConfiguration
            {
                IPAddress = "*************",
                ControlPort = 5000,
                DataPort = 5001,
                Password = "EPSON"
            };
        }

        #endregion

        #region 事件处理方法

        /// <summary>
        /// 连接状态变化事件处理
        /// </summary>
        private void OnConnectionStatusChanged(object sender, EpsonRobotConnectionStatusChangedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnConnectionStatusChanged(sender, e)));
                return;
            }

            try
            {
                string statusText = "";
                Color statusColor = Color.White;

                switch (e.Status)
                {
                    case CommunicationStatus.Connected:
                        statusText = $"{e.ConnectionType}已连接";
                        statusColor = ColorTranslator.FromHtml("#27ae60"); // 绿色
                        break;
                    case CommunicationStatus.Connecting:
                        statusText = $"{e.ConnectionType}连接中...";
                        statusColor = ColorTranslator.FromHtml("#f39c12"); // 橙色
                        break;
                    case CommunicationStatus.Disconnected:
                        statusText = $"{e.ConnectionType}已断开";
                        statusColor = ColorTranslator.FromHtml("#e74c3c"); // 红色
                        break;
                    case CommunicationStatus.Error:
                        statusText = $"{e.ConnectionType}连接错误";
                        statusColor = ColorTranslator.FromHtml("#e74c3c"); // 红色
                        break;
                }

                _controlConnectionStatusLabel.Text = statusText;
                _controlConnectionStatusLabel.ForeColor = statusColor;

                LogHelper.Info($"连接状态变化: {statusText} - {e.Description}");
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理连接状态变化事件失败", ex);
            }
        }

        /// <summary>
        /// 响应接收事件处理
        /// </summary>
        private void OnResponseReceived(object sender, EpsonRobotResponseReceivedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnResponseReceived(sender, e)));
                return;
            }

            try
            {
                string timestamp = DateTime.Now.ToString("HH:mm:ss");
                string message = $"[{timestamp}] 接收({e.ConnectionType}): {e.Response.RawResponse}\r\n";

                _receiveDataTextBox.AppendText(message);
                _receiveDataTextBox.ScrollToCaret();

                LogHelper.Debug($"接收到响应: {e.Response.RawResponse} ({e.ConnectionType})");
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理响应接收事件失败", ex);
            }
        }

        /// <summary>
        /// 命令发送事件处理
        /// </summary>
        private void OnCommandSent(object sender, EpsonRobotCommandSentEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnCommandSent(sender, e)));
                return;
            }

            try
            {
                string timestamp = DateTime.Now.ToString("HH:mm:ss");
                string message = $"[{timestamp}] 发送({e.ConnectionType}): {e.Command.GetFullCommand().Trim()}\r\n";

                _sendDataTextBox.AppendText(message);
                _sendDataTextBox.ScrollToCaret();

                LogHelper.Debug($"发送命令: {e.Command.GetFullCommand().Trim()} ({e.ConnectionType})");
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理命令发送事件失败", ex);
            }
        }

        /// <summary>
        /// 机器人启动事件处理
        /// </summary>
        private void OnRobotStarted(object sender, EpsonRobotStartedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnRobotStarted(sender, e)));
                return;
            }

            try
            {
                if (e.IsSuccess)
                {
                    MessageBox.Show("机器人已启动成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    _startButton.BackColor = ColorTranslator.FromHtml("#2ecc71"); // 更深的绿色表示已启动
                    _startButton.Text = "已启动";
                    _startButton.Enabled = false;
                    _stopButton.Enabled = true;

                    // 启动自动化流程
                    Task.Run(async () => await _epsonRobotManager.StartAutomationAsync());
                }
                else
                {
                    MessageBox.Show($"机器人启动失败: {e.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

                LogHelper.Info($"机器人启动结果: {(e.IsSuccess ? "成功" : "失败")} - {e.Message}");
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理机器人启动事件失败", ex);
            }
        }

        /// <summary>
        /// 特殊命令接收事件处理
        /// </summary>
        private void OnSpecialCommandReceived(object sender, SpecialCommandReceivedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnSpecialCommandReceived(sender, e)));
                return;
            }

            try
            {
                string commandName = e.CommandType.ToString();
                string message = $"收到型号：{commandName}，等待手动发送输入框命令";

                MessageBox.Show(message, "特殊命令", MessageBoxButtons.OK, MessageBoxIcon.Information);

                LogHelper.Info($"接收到特殊命令: {commandName} - {e.RawMessage}");
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理特殊命令接收事件失败", ex);
            }
        }



        /// <summary>
        /// 机器人错误事件处理
        /// </summary>
        private void OnRobotError(object sender, EpsonRobotErrorEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnRobotError(sender, e)));
                return;
            }

            try
            {
                string errorMessage = $"机器人错误 ({e.ConnectionType}): {e.ErrorMessage}";

                if (e.IsCritical)
                {
                    MessageBox.Show(errorMessage, "严重错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

                LogHelper.Error($"机器人错误: {e.ErrorType} - {e.ErrorMessage} ({e.ConnectionType})");
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理机器人错误事件失败", ex);
            }
        }

        #endregion

        #region 按钮点击事件

        /// <summary>
        /// 启动按钮点击事件 - 发送Start命令给机器人
        /// </summary>
        private async void StartButton_Click(object sender, EventArgs e)
        {
            try
            {
                _startButton.Enabled = false;
                _startButton.Text = "启动中...";

                LogHelper.Info("发送启动命令给机器人...");

                // 发送Start命令给机器人（根据EPSON RC+文档）
                bool startResult = await _epsonRobotManager.StartRobotAsync();

                if (startResult)
                {
                    _startButton.BackColor = ColorTranslator.FromHtml("#2ecc71"); // 更深的绿色表示已启动
                    _startButton.Text = "已启动";
                    _startButton.Enabled = false;
                    _stopButton.Enabled = true;

                    // 机器人启动成功后，启用数据连接按钮
                    _dataConnectButton.Enabled = true;

                    LogHelper.Info("机器人启动成功，数据端口连接已启用");
                    MessageBox.Show("机器人启动成功！\n\n下一步：点击\"数据连接\"按钮连接数据端口进行数据收发", "启动成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("机器人启动失败，请检查机器人状态", "启动失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    _startButton.Enabled = true;
                    _startButton.Text = "启动机器人";
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("机器人启动失败", ex);
                MessageBox.Show($"机器人启动失败: {ex.Message}", "启动失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                _startButton.Enabled = true;
                _startButton.Text = "启动机器人";
            }
        }

        /// <summary>
        /// 停止按钮点击事件 - 发送Stop命令给机器人
        /// </summary>
        private async void StopButton_Click(object sender, EventArgs e)
        {
            try
            {
                _stopButton.Enabled = false;
                _stopButton.Text = "停止中...";

                LogHelper.Info("发送停止命令给机器人...");

                // 发送Stop命令给机器人（根据EPSON RC+文档）
                bool stopResult = await _epsonRobotManager.StopRobotAsync();

                if (stopResult)
                {
                    // 重置UI状态
                    _startButton.BackColor = ColorTranslator.FromHtml("#27ae60");
                    _startButton.Text = "启动机器人";
                    _startButton.Enabled = true;
                    _stopButton.Enabled = true; // 停止成功后保持可用状态
                    _stopButton.Text = "停止机器人";

                    // 机器人停止后，禁用数据连接按钮并断开数据连接
                    _dataConnectButton.Enabled = false;
                    if (_dataDisconnectButton.Enabled)
                    {
                        // 如果数据端口已连接，先断开
                        await _epsonRobotManager.DisconnectDataAsync();
                        _dataConnectButton.Enabled = false;
                        _dataDisconnectButton.Enabled = false;
                        _dataConnectionStatusLabel.Text = "未连接";
                        _dataConnectionStatusLabel.ForeColor = ColorTranslator.FromHtml("#e74c3c");
                    }

                    LogHelper.Info("机器人已停止，数据端口连接已禁用");
                    MessageBox.Show("机器人停止成功，数据端口连接已断开", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("机器人停止失败，请检查机器人状态", "停止失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    _stopButton.Enabled = true;
                    _stopButton.Text = "停止机器人";
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("机器人停止失败", ex);
                MessageBox.Show($"机器人停止失败: {ex.Message}", "停止失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                _stopButton.Enabled = true;
                _stopButton.Text = "停止机器人";
            }
        }

        /// <summary>
        /// 发送按钮点击事件 - 通过数据端口发送数据
        /// </summary>
        private async void SendButton_Click(object sender, EventArgs e)
        {
            try
            {
                string data = _commandInputTextBox.Text.Trim();
                if (string.IsNullOrEmpty(data))
                {
                    MessageBox.Show("请输入要发送的数据", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                _sendButton.Enabled = false;
                _sendButton.Text = "发送中...";

                // 通过数据端口发送数据
                var response = await _epsonRobotManager.SendCustomCommandAsync(data, "Data");

                if (response != null && response.IsSuccess)
                {
                    LogHelper.Info($"数据发送成功: {data}");
                    // 删除弹窗提示，保留日志记录
                    // MessageBox.Show($"数据发送成功: {data}\n响应: {response.Data}", "数据发送成功",
                    //     MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // 如果当前正在等待手动输入，继续扫描
                    if (_epsonRobotManager.IsWaitingForManualInput)
                    {
                        await _epsonRobotManager.ContinueScanningAsync();
                    }
                }
                else
                {
                    LogHelper.Error($"数据发送失败: {response?.ErrorMessage}");
                    // 删除弹窗提示，保留日志记录
                    // MessageBox.Show($"数据发送失败: {response?.ErrorMessage}", "数据发送失败",
                    //     MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

                _commandInputTextBox.Clear();
            }
            catch (Exception ex)
            {
                LogHelper.Error("数据发送处理失败", ex);
                // 删除弹窗提示，保留日志记录
                // MessageBox.Show($"数据发送失败: {ex.Message}", "数据发送错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _sendButton.Enabled = true;
                _sendButton.Text = "发送";
            }
        }

        /// <summary>
        /// 控制连接按钮点击事件
        /// </summary>
        private async void ControlConnectButton_Click(object sender, EventArgs e)
        {
            try
            {
                _controlConnectButton.Enabled = false;
                _controlConnectButton.Text = "连接中...";

                // 验证输入参数
                string ipAddress = _robotIPTextBox.Text.Trim();
                if (string.IsNullOrEmpty(ipAddress))
                {
                    MessageBox.Show("请输入IP地址", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (!int.TryParse(_controlPortTextBox.Text.Trim(), out int controlPort) || controlPort <= 0 || controlPort > 65535)
                {
                    MessageBox.Show("请输入有效的控制端口号 (1-65535)", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                LogHelper.Info($"开始连接控制端口 - IP: {ipAddress}, 控制端口: {controlPort}");

                // 更新配置 - 正确使用await
                await UpdateRobotConfigurationAsync();

                // 连接控制端口
                bool connectResult = await _epsonRobotManager.ConnectStartStopAsync();
                if (!connectResult)
                {
                    string errorMsg = $"控制端口连接失败\n" +
                                    $"IP地址: {ipAddress}\n" +
                                    $"控制端口: {controlPort}\n" +
                                    $"请检查网络连接和机器人状态";
                    MessageBox.Show(errorMsg, "连接失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                LogHelper.Info("控制端口连接成功，开始登录...");
                _controlConnectButton.Text = "登录中...";

                // 登录机器人（根据EPSON RC+文档，连接后必须在5分钟内Login）
                bool loginResult = await _epsonRobotManager.LoginAsync();
                if (!loginResult)
                {
                    MessageBox.Show("机器人登录失败，请检查密码和机器人状态", "登录失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    // 登录失败时断开连接
                    await _epsonRobotManager.DisconnectStartStopAsync();
                    return;
                }

                // 连接和登录都成功
                _controlConnectButton.Enabled = false;
                _controlDisconnectButton.Enabled = true;
                _controlConnectionStatusLabel.Text = "已连接";
                _controlConnectionStatusLabel.ForeColor = ColorTranslator.FromHtml("#27ae60");

                // 启用启动按钮（只有控制端口连接并登录成功后才能启动机器人）
                _startButton.Enabled = true;

                LogHelper.Info("控制端口连接并登录成功，可以启动机器人");
                MessageBox.Show("控制端口连接并登录成功！\n\n下一步：点击\"启动机器人\"按钮启动机器人", "连接成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                LogHelper.Error("控制连接失败", ex);
                MessageBox.Show($"控制连接失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 只有在连接失败时才重新启用按钮
                if (_controlConnectButton.Enabled == false && _controlDisconnectButton.Enabled == false)
                {
                    _controlConnectButton.Enabled = true;
                    _controlConnectButton.Text = "控制连接";
                }
            }
        }

        /// <summary>
        /// 控制断开按钮点击事件 - 发送退出登录指令并断开控制端口
        /// </summary>
        private async void ControlDisconnectButton_Click(object sender, EventArgs e)
        {
            try
            {
                // 先发送退出登录指令（根据EPSON RC+文档）
                LogHelper.Info("发送退出登录指令...");
                await _epsonRobotManager.LogoutAsync();

                // 然后断开控制端口连接
                await _epsonRobotManager.DisconnectStartStopAsync();

                _controlConnectButton.Enabled = true;
                _controlDisconnectButton.Enabled = false;
                _controlConnectionStatusLabel.Text = "未连接";
                _controlConnectionStatusLabel.ForeColor = ColorTranslator.FromHtml("#e74c3c");

                // 禁用启动按钮
                _startButton.Enabled = false;
                _stopButton.Enabled = false;

                // 禁用数据连接按钮并断开数据连接
                _dataConnectButton.Enabled = false;
                if (_dataDisconnectButton.Enabled)
                {
                    // 如果数据端口已连接，先断开
                    await _epsonRobotManager.DisconnectDataAsync();
                    _dataDisconnectButton.Enabled = false;
                    _dataConnectionStatusLabel.Text = "未连接";
                    _dataConnectionStatusLabel.ForeColor = ColorTranslator.FromHtml("#e74c3c");
                }

                LogHelper.Info("控制端口已断开，数据端口连接已禁用");
            }
            catch (Exception ex)
            {
                LogHelper.Error("控制断开失败", ex);
                MessageBox.Show($"控制断开失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 数据连接按钮点击事件 - 需要机器人启动后才能连接数据端口
        /// </summary>
        private async void DataConnectButton_Click(object sender, EventArgs e)
        {
            try
            {
                _dataConnectButton.Enabled = false;
                _dataConnectButton.Text = "连接中...";

                // 验证输入参数
                string ipAddress = _robotIPTextBox.Text.Trim();
                if (string.IsNullOrEmpty(ipAddress))
                {
                    MessageBox.Show("请输入IP地址", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (!int.TryParse(_dataPortTextBox.Text.Trim(), out int dataPort) || dataPort <= 0 || dataPort > 65535)
                {
                    MessageBox.Show("请输入有效的数据端口号 (1-65535)", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                LogHelper.Info($"开始连接数据端口 - IP: {ipAddress}, 数据端口: {dataPort}");

                // 注意：配置已经在控制连接时设置好了，这里直接连接数据端口
                // 连接数据端口
                bool connectResult = await _epsonRobotManager.ConnectDataAsync();
                if (!connectResult)
                {
                    string errorMsg = $"数据端口连接失败\n" +
                                    $"IP地址: {ipAddress}\n" +
                                    $"数据端口: {dataPort}\n" +
                                    $"请检查网络连接和机器人状态";
                    MessageBox.Show(errorMsg, "连接失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 数据端口连接成功（数据端口通常不需要单独登录）
                _dataConnectButton.Enabled = false;
                _dataDisconnectButton.Enabled = true;
                _dataConnectionStatusLabel.Text = "已连接";
                _dataConnectionStatusLabel.ForeColor = ColorTranslator.FromHtml("#3498db");

                LogHelper.Info("数据端口连接成功，可以进行数据收发");
                MessageBox.Show("数据端口连接成功！\n\n现在可以通过数据端口进行数据收发操作", "数据连接成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                LogHelper.Error("数据连接失败", ex);
                MessageBox.Show($"数据连接失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 只有在连接失败时才重新启用按钮
                if (_dataConnectButton.Enabled == false && _dataDisconnectButton.Enabled == false)
                {
                    _dataConnectButton.Enabled = true;
                    _dataConnectButton.Text = "数据连接";
                }
            }
        }

        /// <summary>
        /// 数据断开按钮点击事件
        /// </summary>
        private async void DataDisconnectButton_Click(object sender, EventArgs e)
        {
            try
            {
                // 断开数据端口连接
                await _epsonRobotManager.DisconnectDataAsync();

                // 只有在机器人启动状态下才能重新连接数据端口
                _dataConnectButton.Enabled = _startButton.Text == "已启动";
                _dataDisconnectButton.Enabled = false;
                _dataConnectionStatusLabel.Text = "未连接";
                _dataConnectionStatusLabel.ForeColor = ColorTranslator.FromHtml("#e74c3c");
                LogHelper.Info("数据端口已断开");
            }
            catch (Exception ex)
            {
                LogHelper.Error("数据断开失败", ex);
                MessageBox.Show($"数据断开失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 更新机器人配置
        /// </summary>
        private async Task UpdateRobotConfigurationAsync()
        {
            if (_epsonRobotManager != null)
            {
                var config = new EpsonRobotConfiguration
                {
                    IPAddress = _robotIPTextBox.Text.Trim(),
                    ControlPort = int.TryParse(_controlPortTextBox.Text.Trim(), out int controlPort) ? controlPort : 5000,
                    DataPort = int.TryParse(_dataPortTextBox.Text.Trim(), out int dataPort) ? dataPort : 5001,
                    Password = "EPSON",
                    ConnectTimeout = 10000, // 增加超时时间
                    ReceiveTimeout = 5000,
                    SendTimeout = 5000,
                    ScanInterval = 100,
                    AutoReconnect = true,
                    ReconnectInterval = 5000,
                    MaxReconnectAttempts = 10
                };

                // 重新初始化管理器 - 正确使用await
                await _epsonRobotManager.InitializeAsync(config);

                // 保存配置到Settings系统
                bool saveSuccess = await _epsonRobotManager.SaveConfigurationAsync(config);
                if (saveSuccess)
                {
                    LogHelper.Info($"机器人2配置已更新并保存 - IP: {config.IPAddress}, 控制端口: {config.ControlPort}, 数据端口: {config.DataPort}");
                }
                else
                {
                    LogHelper.Warning($"机器人2配置更新成功但保存失败 - IP: {config.IPAddress}, 控制端口: {config.ControlPort}, 数据端口: {config.DataPort}");
                }
            }
        }

        /// <summary>
        /// 订阅配置变更事件，实现实时参数保存
        /// </summary>
        private void SubscribeToConfigurationChanges()
        {
            try
            {
                // 订阅IP地址文本框变更事件
                if (_robotIPTextBox != null)
                    _robotIPTextBox.TextChanged += OnConfigurationChanged;

                // 订阅控制端口文本框变更事件
                if (_controlPortTextBox != null)
                    _controlPortTextBox.TextChanged += OnConfigurationChanged;

                // 订阅数据端口文本框变更事件
                if (_dataPortTextBox != null)
                    _dataPortTextBox.TextChanged += OnConfigurationChanged;

                LogHelper.Debug("机器人2配置变更事件订阅完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("机器人2配置变更事件订阅失败", ex);
            }
        }

        /// <summary>
        /// 取消订阅配置变更事件
        /// </summary>
        private void UnsubscribeFromConfigurationChanges()
        {
            try
            {
                // 取消订阅IP地址文本框变更事件
                if (_robotIPTextBox != null)
                    _robotIPTextBox.TextChanged -= OnConfigurationChanged;

                // 取消订阅控制端口文本框变更事件
                if (_controlPortTextBox != null)
                    _controlPortTextBox.TextChanged -= OnConfigurationChanged;

                // 取消订阅数据端口文本框变更事件
                if (_dataPortTextBox != null)
                    _dataPortTextBox.TextChanged -= OnConfigurationChanged;

                LogHelper.Debug("机器人2配置变更事件取消订阅完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("机器人2配置变更事件取消订阅失败", ex);
            }
        }

        /// <summary>
        /// 配置变更事件处理 - 实时保存配置
        /// </summary>
        private async void OnConfigurationChanged(object sender, EventArgs e)
        {
            try
            {
                // 如果正在初始化，跳过保存
                if (_isInitializing)
                    return;

                // 延迟保存，避免频繁保存
                await Task.Delay(1000);

                // 保存当前配置到Settings
                await SaveConfigurationFromUI();
            }
            catch (Exception ex)
            {
                LogHelper.Error("机器人2配置变更处理失败", ex);
            }
        }

        /// <summary>
        /// 从UI控件保存配置到Settings
        /// </summary>
        private async Task SaveConfigurationFromUI()
        {
            await Task.Run(async () =>
            {
                try
                {
                    if (_epsonRobotManager == null)
                        return;

                    // 获取当前UI控件的值
                    string ipAddress = _robotIPTextBox?.Text?.Trim() ?? "*************";
                    int controlPort = int.TryParse(_controlPortTextBox?.Text?.Trim(), out int cp) ? cp : 5000;
                    int dataPort = int.TryParse(_dataPortTextBox?.Text?.Trim(), out int dp) ? dp : 5001;

                    // 创建配置对象
                    var config = new EpsonRobotConfiguration
                    {
                        IPAddress = ipAddress,
                        ControlPort = controlPort,
                        DataPort = dataPort,
                        Password = "EPSON",
                        ConnectTimeout = 10000,
                        ReceiveTimeout = 5000,
                        SendTimeout = 5000,
                        ScanInterval = 100,
                        AutoReconnect = true,
                        ReconnectInterval = 5000,
                        MaxReconnectAttempts = 10
                    };

                    // 保存配置到Settings系统
                    bool saveSuccess = await _epsonRobotManager.SaveConfigurationAsync(config);
                    if (saveSuccess)
                    {
                        LogHelper.Info($"机器人2配置实时保存成功: IP={ipAddress}, 控制端口={controlPort}, 数据端口={dataPort}");
                    }
                    else
                    {
                        LogHelper.Warning($"机器人2配置实时保存失败: IP={ipAddress}, 控制端口={controlPort}, 数据端口={dataPort}");
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Error("机器人2配置实时保存失败", ex);
                }
            });
        }



        #endregion
    }
}
