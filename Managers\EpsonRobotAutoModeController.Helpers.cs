using System;
using System.Linq;
using System.Threading.Tasks;
using MyHMI.Helpers;
using MyHMI.Models;

namespace MyHMI.Managers
{
    /// <summary>
    /// EpsonRobotAutoModeController 辅助方法部分
    /// </summary>
    public partial class EpsonRobotAutoModeController
    {
        #region 映射辅助方法

        /// <summary>
        /// 根据机器人ID获取对应的扫码器ID
        /// </summary>
        /// <param name="robotId">机器人ID</param>
        /// <returns>扫码器ID</returns>
        private int GetScannerIdByRobotId(int robotId)
        {
            try
            {
                if (robotId == _configuration.Robot1Id)
                {
                    return _configuration.ScannerMapping.Scanner1Id;
                }
                else if (robotId == _configuration.Robot2Id)
                {
                    return _configuration.ScannerMapping.Scanner2Id;
                }
                else
                {
                    LogHelper.Warning($"未知的机器人ID: {robotId}，使用默认扫码器");
                    return _configuration.ScannerMapping.Scanner3Id;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"获取机器人{robotId}对应扫码器ID失败", ex);
                return 1; // 默认返回1号扫码器
            }
        }

        /// <summary>
        /// 根据机器人ID获取对应的输出IO
        /// </summary>
        /// <param name="robotId">机器人ID</param>
        /// <returns>输出IO</returns>
        private string GetRobotOutputIO(int robotId)
        {
            try
            {
                if (robotId == _configuration.Robot1Id)
                {
                    return _configuration.IOMapping.Robot1OutputIO;
                }
                else if (robotId == _configuration.Robot2Id)
                {
                    return _configuration.IOMapping.Robot2OutputIO;
                }
                else
                {
                    LogHelper.Warning($"未知的机器人ID: {robotId}，使用默认输出IO");
                    return _configuration.IOMapping.Robot1OutputIO;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"获取机器人{robotId}输出IO失败", ex);
                return "O0001"; // 默认返回O0001
            }
        }

        /// <summary>
        /// 根据机器人ID获取对应的输入IO
        /// </summary>
        /// <param name="robotId">机器人ID</param>
        /// <returns>输入IO</returns>
        private string GetRobotInputIO(int robotId)
        {
            try
            {
                if (robotId == _configuration.Robot1Id)
                {
                    return _configuration.IOMapping.Robot1InputIO;
                }
                else if (robotId == _configuration.Robot2Id)
                {
                    return _configuration.IOMapping.Robot2InputIO;
                }
                else
                {
                    LogHelper.Warning($"未知的机器人ID: {robotId}，使用默认输入IO");
                    return _configuration.IOMapping.Robot1InputIO;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"获取机器人{robotId}输入IO失败", ex);
                return "I0006"; // 默认返回I0006
            }
        }

        /// <summary>
        /// 根据机器人ID获取对应的NG状态IO
        /// </summary>
        /// <param name="robotId">机器人ID</param>
        /// <returns>NG状态IO</returns>
        private string GetRobotNGStatusIO(int robotId)
        {
            try
            {
                if (robotId == _configuration.Robot1Id)
                {
                    return _configuration.IOMapping.Robot1NGStatusIO;
                }
                else if (robotId == _configuration.Robot2Id)
                {
                    return _configuration.IOMapping.Robot2NGStatusIO;
                }
                else
                {
                    LogHelper.Warning($"未知的机器人ID: {robotId}，使用默认NG状态IO");
                    return _configuration.IOMapping.Robot1NGStatusIO;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"获取机器人{robotId}NG状态IO失败", ex);
                return "I0104"; // 默认返回I0104
            }
        }

        #endregion

        #region 状态查询方法

        /// <summary>
        /// 获取机器人工作流状态
        /// </summary>
        /// <param name="robotId">机器人ID</param>
        /// <returns>工作流状态</returns>
        public RobotWorkflowState GetRobotWorkflowState(int robotId)
        {
            try
            {
                return _robotStates.ContainsKey(robotId) ? _robotStates[robotId] : RobotWorkflowState.Ready;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"获取机器人{robotId}工作流状态失败", ex);
                return RobotWorkflowState.Error;
            }
        }

        /// <summary>
        /// 获取所有机器人状态摘要
        /// </summary>
        /// <returns>状态摘要</returns>
        public string GetRobotStatusSummary()
        {
            try
            {
                var summary = $"控制器状态: {CurrentState}, ";
                summary += $"机器人1: {Robot1State}, ";
                summary += $"机器人2: {Robot2State}, ";
                summary += $"待处理消息: {PendingMessageCount}, ";
                summary += $"待发送响应: {PendingResponseCount}";
                
                return summary;
            }
            catch (Exception ex)
            {
                LogHelper.Error("获取机器人状态摘要失败", ex);
                return "状态获取失败";
            }
        }

        /// <summary>
        /// 检查是否有机器人处于错误状态
        /// </summary>
        /// <returns>是否有错误</returns>
        public bool HasRobotErrors()
        {
            try
            {
                return _robotStates.Values.Any(state => state == RobotWorkflowState.Error) || 
                       CurrentState == EpsonAutoModeState.Error;
            }
            catch (Exception ex)
            {
                LogHelper.Error("检查机器人错误状态失败", ex);
                return true; // 安全起见，返回true
            }
        }

        /// <summary>
        /// 获取错误机器人列表
        /// </summary>
        /// <returns>错误机器人ID列表</returns>
        public int[] GetErrorRobots()
        {
            try
            {
                return _robotStates.Where(kvp => kvp.Value == RobotWorkflowState.Error)
                                  .Select(kvp => kvp.Key)
                                  .ToArray();
            }
            catch (Exception ex)
            {
                LogHelper.Error("获取错误机器人列表失败", ex);
                return new int[0];
            }
        }

        #endregion

        #region 性能监控方法

        /// <summary>
        /// 记录操作开始时间
        /// </summary>
        /// <param name="operationName">操作名称</param>
        private void RecordOperationStart(string operationName)
        {
            try
            {
                _operationStartTimes[operationName] = DateTime.Now;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"记录操作开始时间失败: {operationName}", ex);
            }
        }

        /// <summary>
        /// 记录操作完成时间
        /// </summary>
        /// <param name="operationName">操作名称</param>
        private void RecordOperationEnd(string operationName)
        {
            try
            {
                if (_operationStartTimes.ContainsKey(operationName))
                {
                    var duration = DateTime.Now - _operationStartTimes[operationName];
                    _operationDurations[operationName] = duration;
                    
                    // 更新操作计数
                    if (_operationCounts.ContainsKey(operationName))
                    {
                        _operationCounts[operationName]++;
                    }
                    else
                    {
                        _operationCounts[operationName] = 1;
                    }

                    _operationStartTimes.Remove(operationName);
                    
                    if (_configuration.EnableVerboseLogging)
                    {
                        LogHelper.Info($"操作 {operationName} 耗时: {duration.TotalMilliseconds}ms");
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"记录操作完成时间失败: {operationName}", ex);
            }
        }

        /// <summary>
        /// 记录错误
        /// </summary>
        /// <param name="errorType">错误类型</param>
        private void RecordError(string errorType)
        {
            try
            {
                if (_errorCounts.ContainsKey(errorType))
                {
                    _errorCounts[errorType]++;
                }
                else
                {
                    _errorCounts[errorType] = 1;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"记录错误失败: {errorType}", ex);
            }
        }

        /// <summary>
        /// 获取性能报告
        /// </summary>
        /// <returns>性能报告</returns>
        public string GetPerformanceReport()
        {
            try
            {
                var report = "=== 性能报告 ===\n";
                
                // 操作统计
                report += "操作统计:\n";
                foreach (var kvp in _operationCounts)
                {
                    var avgDuration = _operationDurations.ContainsKey(kvp.Key) 
                        ? _operationDurations[kvp.Key].TotalMilliseconds 
                        : 0;
                    report += $"  {kvp.Key}: {kvp.Value}次, 平均耗时: {avgDuration:F2}ms\n";
                }

                // 错误统计
                report += "错误统计:\n";
                foreach (var kvp in _errorCounts)
                {
                    report += $"  {kvp.Key}: {kvp.Value}次\n";
                }

                // 队列状态
                report += $"队列状态:\n";
                report += $"  待处理消息: {PendingMessageCount}\n";
                report += $"  待发送响应: {PendingResponseCount}\n";

                return report;
            }
            catch (Exception ex)
            {
                LogHelper.Error("获取性能报告失败", ex);
                return "性能报告获取失败";
            }
        }

        /// <summary>
        /// 清理性能统计数据
        /// </summary>
        public void ClearPerformanceData()
        {
            try
            {
                _operationStartTimes.Clear();
                _operationDurations.Clear();
                _operationCounts.Clear();
                _errorCounts.Clear();
                
                LogHelper.Info("性能统计数据已清理");
            }
            catch (Exception ex)
            {
                LogHelper.Error("清理性能统计数据失败", ex);
            }
        }

        #endregion

        #region 调试和诊断方法

        /// <summary>
        /// 获取详细状态信息
        /// </summary>
        /// <returns>详细状态信息</returns>
        public string GetDetailedStatus()
        {
            try
            {
                var status = "=== 详细状态信息 ===\n";
                status += $"控制器状态: {CurrentState}\n";
                status += $"运行状态: {(IsRunning ? "运行中" : "已停止")}\n";
                status += $"配置有效: {_configuration?.Validate().IsValid}\n";
                
                status += "\n机器人状态:\n";
                foreach (var kvp in _robotStates)
                {
                    status += $"  机器人{kvp.Key}: {kvp.Value}\n";
                }

                status += "\n依赖管理器状态:\n";
                status += $"  机器人1管理器: {(_robot1Manager != null ? "已初始化" : "未初始化")}\n";
                status += $"  机器人2管理器: {(_robot2Manager != null ? "已初始化" : "未初始化")}\n";
                status += $"  IO管理器: {(_ioManager != null ? "已初始化" : "未初始化")}\n";
                status += $"  扫码器管理器: {(_scannerManager?.IsInitialized == true ? "已初始化" : "未初始化")}\n";

                status += $"\n队列状态:\n";
                status += $"  待处理消息: {PendingMessageCount}\n";
                status += $"  待发送响应: {PendingResponseCount}\n";

                return status;
            }
            catch (Exception ex)
            {
                LogHelper.Error("获取详细状态信息失败", ex);
                return "状态信息获取失败";
            }
        }

        /// <summary>
        /// 执行自诊断
        /// </summary>
        /// <returns>诊断结果</returns>
        public string PerformSelfDiagnosis()
        {
            try
            {
                var diagnosis = "=== 自诊断结果 ===\n";
                bool hasIssues = false;

                // 检查配置
                if (_configuration == null)
                {
                    diagnosis += "❌ 配置未加载\n";
                    hasIssues = true;
                }
                else
                {
                    var validationResult = _configuration.Validate();
                    if (validationResult.IsValid)
                    {
                        diagnosis += "✅ 配置验证通过\n";
                    }
                    else
                    {
                        diagnosis += $"❌ 配置验证失败: {validationResult.GetErrorSummary()}\n";
                        hasIssues = true;
                    }
                }

                // 检查依赖管理器
                if (_robot1Manager == null)
                {
                    diagnosis += "❌ 机器人1管理器未初始化\n";
                    hasIssues = true;
                }
                else
                {
                    diagnosis += "✅ 机器人1管理器已初始化\n";
                }

                if (_robot2Manager == null)
                {
                    diagnosis += "❌ 机器人2管理器未初始化\n";
                    hasIssues = true;
                }
                else
                {
                    diagnosis += "✅ 机器人2管理器已初始化\n";
                }

                if (_ioManager == null)
                {
                    diagnosis += "❌ IO管理器未初始化\n";
                    hasIssues = true;
                }
                else
                {
                    diagnosis += "✅ IO管理器已初始化\n";
                }

                if (_scannerManager?.IsInitialized != true)
                {
                    diagnosis += "❌ 扫码器管理器未初始化\n";
                    hasIssues = true;
                }
                else
                {
                    diagnosis += "✅ 扫码器管理器已初始化\n";
                }

                // 检查机器人状态
                if (HasRobotErrors())
                {
                    var errorRobots = GetErrorRobots();
                    diagnosis += $"❌ 机器人错误: {string.Join(", ", errorRobots.Select(id => $"机器人{id}"))}\n";
                    hasIssues = true;
                }
                else
                {
                    diagnosis += "✅ 所有机器人状态正常\n";
                }

                diagnosis += $"\n总体状态: {(hasIssues ? "❌ 发现问题" : "✅ 系统正常")}\n";
                
                return diagnosis;
            }
            catch (Exception ex)
            {
                LogHelper.Error("执行自诊断失败", ex);
                return "自诊断执行失败";
            }
        }

        /// <summary>
        /// 根据机器人ID获取对应的机器人管理器
        /// </summary>
        /// <param name="robotId">机器人ID</param>
        /// <returns>机器人管理器实例</returns>
        private dynamic GetRobotManager(int robotId)
        {
            try
            {
                if (robotId == _configuration.Robot1Id)
                {
                    return _robot1Manager;
                }
                else if (robotId == _configuration.Robot2Id)
                {
                    return _robot2Manager;
                }
                else
                {
                    LogHelper.Warning($"未知的机器人ID: {robotId}，返回机器人1管理器");
                    return _robot1Manager;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"获取机器人{robotId}管理器失败", ex);
                return _robot1Manager; // 返回默认值
            }
        }

        /// <summary>
        /// 发送机器人控制命令
        /// 根据EPSONRC+UsersGuide.MD发送标准控制命令
        /// </summary>
        /// <param name="robotManager">机器人管理器</param>
        /// <param name="command">控制命令 (Start, Stop, Pause, Continue, Reset等)</param>
        /// <param name="robotName">机器人名称（用于日志）</param>
        /// <returns>命令执行结果</returns>
        private async Task<bool> SendRobotControlCommand(dynamic robotManager, string command, string robotName)
        {
            try
            {
                if (robotManager == null)
                {
                    LogHelper.Error($"{robotName}: 机器人管理器为空");
                    return false;
                }

                LogHelper.Info($"{robotName}: 发送控制命令 {command}");

                // 根据EPSONRC+UsersGuide.MD格式构建命令
                string commandString = $"${command}";

                // 发送命令到控制端口（主端口）
                var response = await robotManager.SendCustomCommandAsync(commandString, "StartStop");

                if (response != null)
                {
                    LogHelper.Info($"{robotName}: 控制命令 {command} 执行成功");
                    return true;
                }
                else
                {
                    LogHelper.Error($"{robotName}: 控制命令 {command} 执行失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"{robotName}: 发送控制命令 {command} 异常", ex);
                return false;
            }
        }

        #endregion
    }
}
