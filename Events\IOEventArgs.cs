using System;
using System.Collections.Generic;

namespace MyHMI.Events
{
    /// <summary>
    /// IO状态变化事件参数
    /// </summary>
    public class IoStateChangedEventArgs : EventArgs
    {
        /// <summary>
        /// IO索引
        /// </summary>
        public int IoIndex { get; set; }

        /// <summary>
        /// IO状态（true: 高电平/ON, false: 低电平/OFF）
        /// </summary>
        public bool State { get; set; }

        /// <summary>
        /// IO类型
        /// </summary>
        public IoType IoType { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="ioIndex">IO索引</param>
        /// <param name="state">IO状态</param>
        /// <param name="ioType">IO类型</param>
        public IoStateChangedEventArgs(int ioIndex, bool state, IoType ioType)
        {
            IoIndex = ioIndex;
            State = state;
            IoType = ioType;
            EventTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 批量IO状态变化事件参数
    /// </summary>
    public class BatchIoStateChangedEventArgs : EventArgs
    {
        /// <summary>
        /// IO状态字典（索引 -> 状态）
        /// </summary>
        public Dictionary<int, bool> IoStates { get; set; }

        /// <summary>
        /// IO类型
        /// </summary>
        public IoType IoType { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="ioStates">IO状态字典</param>
        /// <param name="ioType">IO类型</param>
        public BatchIoStateChangedEventArgs(Dictionary<int, bool> ioStates, IoType ioType)
        {
            IoStates = ioStates ?? new Dictionary<int, bool>();
            IoType = ioType;
            EventTime = DateTime.Now;
        }
    }

    /// <summary>
    /// IO类型枚举
    /// </summary>
    public enum IoType
    {
        /// <summary>
        /// 输入IO
        /// </summary>
        Input,

        /// <summary>
        /// 输出IO
        /// </summary>
        Output
    }

    /// <summary>
    /// IO类型枚举别名（大写版本）
    /// </summary>
    public enum IOType
    {
        /// <summary>
        /// 输入IO
        /// </summary>
        Input = 0,

        /// <summary>
        /// 输出IO
        /// </summary>
        Output = 1,

        /// <summary>
        /// 数字输入（兼容性）
        /// </summary>
        DigitalInput = 0,

        /// <summary>
        /// 数字输出（兼容性）
        /// </summary>
        DigitalOutput = 1
    }

    /// <summary>
    /// IO状态变化事件参数别名（大写IO版本）
    /// </summary>
    public class IOStateChangedEventArgs : IoStateChangedEventArgs
    {
        /// <summary>
        /// IO类型（兼容性属性）
        /// </summary>
        public IoType IOType => IoType;

        /// <summary>
        /// IO索引（兼容性属性）
        /// </summary>
        public int IOIndex => IoIndex;

        /// <summary>
        /// 新状态（兼容性属性）
        /// </summary>
        public bool NewState => State;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="ioIndex">IO索引</param>
        /// <param name="state">IO状态</param>
        /// <param name="ioType">IO类型</param>
        public IOStateChangedEventArgs(int ioIndex, bool state, IoType ioType)
            : base(ioIndex, state, ioType)
        {
        }
    }

    /// <summary>
    /// IO输入状态变化事件参数
    /// </summary>
    public class IOInputStateChangedEventArgs : IOStateChangedEventArgs
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="ioIndex">IO索引</param>
        /// <param name="state">IO状态</param>
        public IOInputStateChangedEventArgs(int ioIndex, bool state)
            : base(ioIndex, state, IoType.Input)
        {
        }
    }

    /// <summary>
    /// IO输出状态变化事件参数
    /// </summary>
    public class IOOutputStateChangedEventArgs : IOStateChangedEventArgs
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="ioIndex">IO索引</param>
        /// <param name="state">IO状态</param>
        public IOOutputStateChangedEventArgs(int ioIndex, bool state)
            : base(ioIndex, state, IoType.Output)
        {
        }
    }
}
