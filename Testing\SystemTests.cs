using System;
using System.Threading.Tasks;
using MyHMI.Settings;
using MyHMI.Helpers;

namespace MyHMI.Testing
{
    /// <summary>
    /// 系统测试类
    /// </summary>
    public static class SystemTests
    {
        /// <summary>
        /// 运行所有系统测试
        /// </summary>
        /// <returns>测试结果</returns>
        public static async Task<TestResult> RunAllSystemTestsAsync()
        {
            var framework = new SimpleTestFramework();

            // 配置系统测试
            RegisterConfigurationTests(framework);

            // Mock管理器测试
            RegisterMockManagerTests(framework);

            // 辅助类测试
            RegisterHelperTests(framework);

            // 运行所有测试
            return await framework.RunAllTestsAsync();
        }

        /// <summary>
        /// 注册配置系统测试
        /// </summary>
        /// <param name="framework">测试框架</param>
        private static void RegisterConfigurationTests(SimpleTestFramework framework)
        {
            framework.AddTest("配置系统初始化测试", async () =>
            {
                // 测试新的Settings系统初始化
                Settings.Settings.Load();

                var settings = Settings.Settings.Current;
                SimpleTestFramework.AssertNotNull(settings, "设置对象不应该为空");
                SimpleTestFramework.AssertNotNull(settings.System, "系统设置不应该为空");
                SimpleTestFramework.AssertEqual("上位机控制系统", settings.System.Name, "系统名称应该正确");

                await Task.CompletedTask;
            }, "Configuration");

            framework.AddTest("配置保存和重新加载测试", async () =>
            {
                var config = Settings.Settings.Current;
                string originalName = config.System.Name;
                string testName = "测试系统名称";

                // 修改配置
                config.System.Name = testName;
                Settings.Settings.Save();
                SimpleTestFramework.AssertTrue(true, "配置保存应该成功");

                // 重新加载配置
                Settings.Settings.Load();
                SimpleTestFramework.AssertTrue(true, "配置重新加载应该成功");

                var reloadedConfig = Settings.Settings.Current;
                SimpleTestFramework.AssertEqual(testName, reloadedConfig.System.Name, "重新加载的配置应该包含修改的值");

                // 恢复原始配置
                config.System.Name = originalName;
                Settings.Settings.Save();

                await Task.CompletedTask;
            }, "Configuration");
        }

        /// <summary>
        /// 注册Mock管理器测试
        /// </summary>
        /// <param name="framework">测试框架</param>
        private static void RegisterMockManagerTests(SimpleTestFramework framework)
        {
            framework.AddTest("MockIOManager初始化测试", async () =>
            {
                var mockIO = MockIOManager.Instance;
                
                // 测试初始化
                bool initResult = await mockIO.InitializeAsync();
                SimpleTestFramework.AssertTrue(initResult, "MockIOManager初始化应该成功");
                SimpleTestFramework.AssertTrue(mockIO.IsInitialized, "MockIOManager应该处于已初始化状态");

                // 测试重复初始化
                bool reinitResult = await mockIO.InitializeAsync();
                SimpleTestFramework.AssertTrue(reinitResult, "MockIOManager重复初始化应该成功");
            }, "MockManager");

            framework.AddTest("MockIOManager IO操作测试", async () =>
            {
                var mockIO = MockIOManager.Instance;
                
                if (!mockIO.IsInitialized)
                {
                    await mockIO.InitializeAsync();
                }

                // 测试输出设置
                bool setResult = await mockIO.SetOutputAsync(0, true);
                SimpleTestFramework.AssertTrue(setResult, "设置输出应该成功");

                // 测试输出读取
                bool outputState = await mockIO.ReadOutputAsync(0);
                SimpleTestFramework.AssertTrue(outputState, "读取的输出状态应该为true");

                // 测试输入读取
                bool inputState = await mockIO.ReadInputAsync(0);
                // 输入状态可能是任意值，只要不抛异常就算成功
                SimpleTestFramework.AssertTrue(true, "读取输入状态应该成功");

                // 测试范围检查
                SimpleTestFramework.AssertThrows<ArgumentOutOfRangeException>(() =>
                {
                    var task = mockIO.SetOutputAsync(-1, true);
                    task.Wait();
                }, "负数通道号应该抛出异常");

                SimpleTestFramework.AssertThrows<ArgumentOutOfRangeException>(() =>
                {
                    var task = mockIO.SetOutputAsync(16, true);
                    task.Wait();
                }, "超出范围的通道号应该抛出异常");
            }, "MockManager");

            framework.AddTest("MockMotorManager初始化测试", async () =>
            {
                var mockMotor = MockMotorManager.Instance;
                
                // 测试初始化
                bool initResult = await mockMotor.InitializeAsync();
                SimpleTestFramework.AssertTrue(initResult, "MockMotorManager初始化应该成功");
                SimpleTestFramework.AssertTrue(mockMotor.IsInitialized, "MockMotorManager应该处于已初始化状态");

                // 测试获取电机状态
                var status = await mockMotor.GetMotorStatusAsync(0);
                SimpleTestFramework.AssertNotNull(status, "电机状态不应该为空");
                SimpleTestFramework.AssertEqual(0, status.AxisId, "轴号应该正确");
                SimpleTestFramework.AssertEqual(0.0, status.CurrentPosition, "初始位置应该为0");
                SimpleTestFramework.AssertFalse(status.IsMoving, "初始状态应该不在运动");
            }, "MockManager");

            framework.AddTest("MockMotorManager运动测试", async () =>
            {
                var mockMotor = MockMotorManager.Instance;
                
                if (!mockMotor.IsInitialized)
                {
                    await mockMotor.InitializeAsync();
                }

                // 测试移动到指定位置
                bool moveResult = await mockMotor.MoveToAsync(0, 100.0, 1000.0);
                SimpleTestFramework.AssertTrue(moveResult, "电机移动应该成功");

                // 等待运动完成
                await Task.Delay(200);

                var status = await mockMotor.GetMotorStatusAsync(0);
                AssertDoubleEqual(100.0, status.CurrentPosition, "电机应该到达目标位置", 0.1);
                SimpleTestFramework.AssertFalse(status.IsMoving, "运动完成后应该停止");

                // 测试停止电机
                await mockMotor.MoveToAsync(0, 200.0, 500.0);
                await Task.Delay(50); // 让运动开始
                
                bool stopResult = await mockMotor.StopMotorAsync(0);
                SimpleTestFramework.AssertTrue(stopResult, "停止电机应该成功");

                status = await mockMotor.GetMotorStatusAsync(0);
                SimpleTestFramework.AssertFalse(status.IsMoving, "停止后电机应该不在运动");
            }, "MockManager");
        }

        /// <summary>
        /// 注册辅助类测试
        /// </summary>
        /// <param name="framework">测试框架</param>
        private static void RegisterHelperTests(SimpleTestFramework framework)
        {
            framework.AddTest("LogHelper基本功能测试", () =>
            {
                // 测试日志记录（不会抛异常就算成功）
                LogHelper.Debug("测试调试日志");
                LogHelper.Info("测试信息日志");
                LogHelper.Warning("测试警告日志");
                LogHelper.Error("测试错误日志");

                SimpleTestFramework.AssertTrue(true, "日志记录应该不抛异常");
            }, "Helper");

            framework.AddTest("ExceptionHelper安全执行测试", async () =>
            {
                // 测试成功执行
                bool result1 = await ExceptionHelper.SafeExecuteAsync(async () =>
                {
                    await Task.Delay(10);
                    return true;
                }, false, "测试操作");

                SimpleTestFramework.AssertTrue(result1, "正常操作应该返回true");

                // 测试异常处理
                bool result2 = await ExceptionHelper.SafeExecuteAsync(async () =>
                {
                    await Task.Delay(10);
                    throw new InvalidOperationException("测试异常");
                    return true;
                }, false, "测试异常操作");

                SimpleTestFramework.AssertFalse(result2, "异常操作应该返回false");
            }, "Helper");

            framework.AddTest("UIHelper线程安全调用测试", () =>
            {
                // 测试SafeInvoke（在非UI线程中调用）
                bool executed = false;
                UIHelper.SafeInvoke(() =>
                {
                    executed = true;
                });

                // 由于我们在非UI线程中，SafeInvoke应该直接执行
                SimpleTestFramework.AssertTrue(executed, "SafeInvoke应该执行委托");
            }, "Helper");
        }

        /// <summary>
        /// 断言双精度数相等（带容差）
        /// </summary>
        /// <param name="expected">期望值</param>
        /// <param name="actual">实际值</param>
        /// <param name="message">失败消息</param>
        /// <param name="tolerance">容差</param>
        private static void AssertDoubleEqual(double expected, double actual, string message, double tolerance = 0.001)
        {
            if (Math.Abs(expected - actual) > tolerance)
            {
                throw new AssertionException($"AssertDoubleEqual失败: {message}. 期望: {expected}, 实际: {actual}, 容差: {tolerance}");
            }
        }
    }
}
