using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
// UI.Core.Threading命名空间已移除 - 改为简化的线程安全操作
// using MyHMI.UI.Core.Threading; // 已移除

namespace MyHMI.Helpers
{
    /// <summary>
    /// UI线程安全更新辅助类
    /// 提供从后台线程安全更新UI控件的方法
    /// </summary>
    public static class UIHelper
    {
        private static SynchronizationContext uiContext;

        /// <summary>
        /// 初始化UI上下文（在UI线程中调用）
        /// </summary>
        public static void Initialize()
        {
            uiContext = SynchronizationContext.Current;
            if (uiContext == null)
            {
                LogHelper.Warning("UI上下文初始化失败，将使用Control.Invoke方式");
            }
        }

        /// <summary>
        /// 在UI线程中安全执行操作（别名方法）
        /// </summary>
        /// <param name="action">要执行的操作</param>
        public static void SafeInvoke(Action action)
        {
            InvokeOnUIThread(action);
        }

        /// <summary>
        /// 在UI线程中安全执行操作
        /// </summary>
        /// <param name="action">要执行的操作</param>
        public static void InvokeOnUIThread(Action action)
        {
            try
            {
                if (action == null) return;

                // UI线程安全管理器已移除，使用简化的线程安全调用

                // 备用方案：原有逻辑
                if (uiContext != null)
                {
                    // 使用SynchronizationContext方式
                    if (SynchronizationContext.Current == uiContext)
                    {
                        // 已经在UI线程中，直接执行
                        action();
                    }
                    else
                    {
                        // 切换到UI线程执行
                        uiContext.Post(_ => action(), null);
                    }
                }
                else
                {
                    // 备用方案：使用Application.Invoke
                    if (Application.OpenForms.Count > 0)
                    {
                        var mainForm = Application.OpenForms[0];
                        if (mainForm.InvokeRequired)
                        {
                            mainForm.Invoke(action);
                        }
                        else
                        {
                            action();
                        }
                    }
                    else
                    {
                        LogHelper.Warning("没有可用的UI窗体进行线程切换");
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("UI线程调用失败", ex);
            }
        }

        /// <summary>
        /// 在UI线程中安全执行操作（带返回值）
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="func">要执行的函数</param>
        /// <returns>函数返回值</returns>
        public static T InvokeOnUIThread<T>(Func<T> func)
        {
            try
            {
                if (func == null) return default(T);

                if (uiContext != null && SynchronizationContext.Current == uiContext)
                {
                    // 已经在UI线程中，直接执行
                    return func();
                }
                else if (Application.OpenForms.Count > 0)
                {
                    var mainForm = Application.OpenForms[0];
                    if (mainForm.InvokeRequired)
                    {
                        return (T)mainForm.Invoke(func);
                    }
                    else
                    {
                        return func();
                    }
                }
                else
                {
                    LogHelper.Warning("没有可用的UI窗体进行线程切换");
                    return default(T);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("UI线程调用失败", ex);
                return default(T);
            }
        }

        /// <summary>
        /// 安全更新控件文本
        /// </summary>
        /// <param name="control">要更新的控件</param>
        /// <param name="text">新文本</param>
        public static void UpdateControlText(Control control, string text)
        {
            if (control == null) return;

            InvokeOnUIThread(() =>
            {
                try
                {
                    if (!control.IsDisposed)
                    {
                        control.Text = text;
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"更新控件文本失败: {control.Name}", ex);
                }
            });
        }

        /// <summary>
        /// 安全更新控件可见性
        /// </summary>
        /// <param name="control">要更新的控件</param>
        /// <param name="visible">是否可见</param>
        public static void UpdateControlVisibility(Control control, bool visible)
        {
            if (control == null) return;

            InvokeOnUIThread(() =>
            {
                try
                {
                    if (!control.IsDisposed)
                    {
                        control.Visible = visible;
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"更新控件可见性失败: {control.Name}", ex);
                }
            });
        }

        /// <summary>
        /// 安全更新控件启用状态
        /// </summary>
        /// <param name="control">要更新的控件</param>
        /// <param name="enabled">是否启用</param>
        public static void UpdateControlEnabled(Control control, bool enabled)
        {
            if (control == null) return;

            InvokeOnUIThread(() =>
            {
                try
                {
                    if (!control.IsDisposed)
                    {
                        control.Enabled = enabled;
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"更新控件启用状态失败: {control.Name}", ex);
                }
            });
        }

        /// <summary>
        /// 安全显示消息框
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">标题</param>
        /// <param name="buttons">按钮类型</param>
        /// <param name="icon">图标类型</param>
        /// <returns>用户选择结果</returns>
        public static DialogResult ShowMessageBox(string message, string title = "提示", 
            MessageBoxButtons buttons = MessageBoxButtons.OK, MessageBoxIcon icon = MessageBoxIcon.Information)
        {
            return InvokeOnUIThread(() =>
            {
                try
                {
                    return MessageBox.Show(message, title, buttons, icon);
                }
                catch (Exception ex)
                {
                    LogHelper.Error("显示消息框失败", ex);
                    return DialogResult.None;
                }
            });
        }

        /// <summary>
        /// 检查是否在UI线程中
        /// </summary>
        /// <returns>是否在UI线程</returns>
        public static bool IsOnUIThread()
        {
            // UI线程安全管理器已移除，使用简化的线程检查

            // 备用方案：原有逻辑
            if (uiContext != null)
            {
                return SynchronizationContext.Current == uiContext;
            }
            else if (Application.OpenForms.Count > 0)
            {
                return !Application.OpenForms[0].InvokeRequired;
            }
            return false;
        }

        /// <summary>
        /// 在UI线程中安全执行异步操作
        /// </summary>
        /// <param name="asyncAction">要执行的异步操作</param>
        /// <returns>操作任务</returns>
        public static async Task InvokeOnUIThreadAsync(Func<Task> asyncAction)
        {
            try
            {
                if (asyncAction == null) return;

                // UI线程安全管理器已移除，使用简化的异步调用

                // 备用方案：在UI线程中执行
                await Task.Run(async () =>
                {
                    var tcs = new TaskCompletionSource<bool>();
                    InvokeOnUIThread(() =>
                    {
                        // 启动异步操作但不等待，通过TaskCompletionSource处理结果
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                await asyncAction();
                                tcs.SetResult(true);
                            }
                            catch (Exception ex)
                            {
                                tcs.SetException(ex);
                            }
                        });
                    });
                    await tcs.Task;
                });
            }
            catch (Exception ex)
            {
                LogHelper.Error("UI线程异步调用失败", ex);
            }
        }

        /// <summary>
        /// 在UI线程中安全执行异步操作（带返回值）
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="asyncFunc">要执行的异步函数</param>
        /// <returns>函数返回值</returns>
        public static async Task<T> InvokeOnUIThreadAsync<T>(Func<Task<T>> asyncFunc)
        {
            try
            {
                if (asyncFunc == null) return default(T);

                // UI线程安全管理器已移除，使用简化的异步调用

                // 备用方案：在UI线程中执行
                var tcs = new TaskCompletionSource<T>();

                InvokeOnUIThread(() =>
                {
                    // 启动异步操作但不等待，通过TaskCompletionSource处理结果
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            var result = await asyncFunc();
                            tcs.SetResult(result);
                        }
                        catch (Exception ex)
                        {
                            tcs.SetException(ex);
                        }
                    });
                });

                return await tcs.Task;
            }
            catch (Exception ex)
            {
                LogHelper.Error("UI线程异步调用失败", ex);
                return default(T);
            }
        }

        /// <summary>
        /// 执行长时间运行的异步操作
        /// </summary>
        /// <param name="operation">异步操作</param>
        /// <param name="operationName">操作名称</param>
        /// <param name="timeout">超时时间</param>
        /// <returns>操作结果</returns>
        public static async Task<bool> ExecuteLongRunningOperationAsync(
            Func<CancellationToken, Task> operation,
            string operationName = null,
            TimeSpan? timeout = null)
        {
            try
            {
                if (operation == null) return false;

                // 异步操作管理器已移除，使用简化的异步操作
                // if (AsyncOperationManager.Instance != null) { ... } // 已移除

                // 备用方案：直接执行
                using (var cts = new CancellationTokenSource(timeout ?? TimeSpan.FromMinutes(5)))
                {
                    await operation(cts.Token);
                    return true;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"执行长时间运行操作失败: {operationName}", ex);
                return false;
            }
        }

        /// <summary>
        /// 执行长时间运行的异步操作（带返回值）
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="operation">异步操作</param>
        /// <param name="operationName">操作名称</param>
        /// <param name="timeout">超时时间</param>
        /// <returns>操作结果</returns>
        public static async Task<T> ExecuteLongRunningOperationAsync<T>(
            Func<CancellationToken, Task<T>> operation,
            string operationName = null,
            TimeSpan? timeout = null)
        {
            try
            {
                if (operation == null) return default(T);

                // 异步操作管理器已移除，使用简化的异步操作

                // 备用方案：直接执行
                using (var cts = new CancellationTokenSource(timeout ?? TimeSpan.FromMinutes(5)))
                {
                    return await operation(cts.Token);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"执行长时间运行操作失败: {operationName}", ex);
                return default(T);
            }
        }
    }
}
