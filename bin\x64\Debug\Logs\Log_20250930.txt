[2025-09-30 13:27:34.215] [INFO] 程序启动开始
[2025-09-30 13:27:34.216] [INFO] 加载Settings系统配置...
[2025-09-30 13:27:34.220] [INFO] 设置加载成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:34.220] [INFO] Settings系统配置加载完成
[2025-09-30 13:27:34.222] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-30 13:27:34.230] [INFO] 开始初始化各个Manager...
[2025-09-30 13:27:34.231] [INFO] 初始化DMC1000B控制卡...
[2025-09-30 13:27:34.234] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-30 13:27:34.253] [INFO] DMC1000B控制卡初始化成功，检测到 1 张控制卡
[2025-09-30 13:27:34.254] [INFO] 初始化基础Manager...
[2025-09-30 13:27:34.264] [INFO] IO状态缓存初始化完成
[2025-09-30 13:27:34.272] [ERROR] 批量读取所有输入IO 执行失败
异常详情: IO管理器未初始化
堆栈跟踪:    在 MyHMI.Managers.DMC1000BIOManager.<<ReadAllInputsAsync>b__31_0>d.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Managers\DMC1000BIOManager.cs:行号 384
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-30 13:27:34.286] [ERROR] 批量读取所有输出IO 执行失败
异常详情: IO管理器未初始化
堆栈跟踪:    在 MyHMI.Managers.DMC1000BIOManager.<<ReadAllOutputsAsync>b__36_0>d.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Managers\DMC1000BIOManager.cs:行号 668
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-30 13:27:34.287] [INFO] IO状态监控线程启动成功，监控频率: 4.0Hz
[2025-09-30 13:27:34.287] [INFO] DMC1000B IO管理器初始化成功
[2025-09-30 13:27:34.288] [INFO] IO监控线程开始工作
[2025-09-30 13:27:34.289] [INFO] 开始初始化DMC1000B电机管理器...
[2025-09-30 13:27:34.291] [INFO] 设置脉冲输出模式成功
[2025-09-30 13:27:34.294] [INFO] 从Settings系统加载输入皮带电机参数成功: 脉冲当量=0.01mm/pulse
[2025-09-30 13:27:34.294] [INFO] 从Settings系统加载输出皮带电机参数成功: 脉冲当量=0.01mm/pulse
[2025-09-30 13:27:34.296] [INFO] 电机参数初始化完成
[2025-09-30 13:27:34.297] [INFO] 电机监控任务已启动
[2025-09-30 13:27:34.297] [INFO] DMC1000B运动控制卡初始化完成
[2025-09-30 13:27:34.297] [INFO] 初始化系统模式管理器...
[2025-09-30 13:27:34.298] [INFO] 电机监控循环开始
[2025-09-30 13:27:34.304] [INFO] 从Settings系统加载安全管理器配置成功
[2025-09-30 13:27:34.306] [INFO] 安全管理器实例已创建
[2025-09-30 13:27:34.308] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-30 13:27:34.309] [INFO] 开始初始化MotorManager...
[2025-09-30 13:27:34.309] [INFO] 当前模式已经是 Manual，无需切换
[2025-09-30 13:27:34.310] [INFO] 模拟初始化运动控制卡
[2025-09-30 13:27:34.526] [INFO] 加载了8个电机的默认配置
[2025-09-30 13:27:34.527] [INFO] 电机监控任务已启动
[2025-09-30 13:27:34.527] [INFO] MotorManager初始化完成
[2025-09-30 13:27:34.527] [INFO] 初始化通信Manager...
[2025-09-30 13:27:34.528] [INFO] 电机监控循环开始
[2025-09-30 13:27:34.529] [INFO] 开始初始化ScannerManager...
[2025-09-30 13:27:34.530] [INFO] 扫描枪配置从Settings系统加载: COM1, 9600, 8, One, None
[2025-09-30 13:27:34.531] [INFO] 串口初始化完成
[2025-09-30 13:27:34.532] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-30 13:27:34.534] [INFO] 扫描枪连接成功: COM1
[2025-09-30 13:27:34.534] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-30 13:27:34.534] [INFO] ScannerManager初始化完成
[2025-09-30 13:27:34.538] [INFO] 扫描枪1从Settings加载配置: COM1, 115200, 8, One, None
[2025-09-30 13:27:34.539] [INFO] 扫描枪2从Settings加载配置: COM2, 115200, 8, One, None
[2025-09-30 13:27:34.539] [INFO] 扫描枪3从Settings加载配置: COM3, 115200, 8, One, None
[2025-09-30 13:27:34.540] [INFO] SCARA通信管理器已初始化
[2025-09-30 13:27:34.541] [INFO] ScannerAutoModeManager管理器引用初始化完成
[2025-09-30 13:27:34.543] [INFO] 开始初始化ScannerAutoModeManager...
[2025-09-30 13:27:34.545] [INFO] 开始初始化MultiScannerManager...
[2025-09-30 13:27:34.546] [INFO] 开始初始化扫描枪1...
[2025-09-30 13:27:34.547] [INFO] 从Settings系统加载扫描枪1配置: COM1, 115200
[2025-09-30 13:27:34.548] [INFO] 扫描枪1串口初始化完成
[2025-09-30 13:27:34.548] [INFO] 扫描枪1初始化完成
[2025-09-30 13:27:34.549] [INFO] 开始初始化扫描枪2...
[2025-09-30 13:27:34.549] [INFO] 从Settings系统加载扫描枪2配置: COM2, 115200
[2025-09-30 13:27:34.549] [INFO] 扫描枪2串口初始化完成
[2025-09-30 13:27:34.549] [INFO] 扫描枪2初始化完成
[2025-09-30 13:27:34.549] [INFO] 开始初始化扫描枪3...
[2025-09-30 13:27:34.549] [INFO] 从Settings系统加载扫描枪3配置: COM3, 115200
[2025-09-30 13:27:34.549] [INFO] 扫描枪3串口初始化完成
[2025-09-30 13:27:34.549] [INFO] 扫描枪3初始化完成
[2025-09-30 13:27:34.549] [INFO] MultiScannerManager初始化完成
[2025-09-30 13:27:34.550] [INFO] ScannerAutoModeManager初始化完成
[2025-09-30 13:27:34.550] [INFO] 跳过Modbus TCP管理器初始化 - 由第三方控制
[2025-09-30 13:27:34.552] [INFO] 开始初始化EpsonRobotManager...
[2025-09-30 13:27:34.553] [INFO] 从Settings系统加载Epson机器人1配置成功
[2025-09-30 13:27:34.553] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-30 13:27:34.553] [INFO] EpsonRobotManager初始化完成
[2025-09-30 13:27:34.553] [INFO] 初始化视觉Manager...
[2025-09-30 13:27:34.555] [INFO] 开始初始化VisionManager...
[2025-09-30 13:27:34.555] [INFO] 视觉系统配置从Settings系统加载: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms, 置信度阈值=0.8
[2025-09-30 13:27:34.556] [INFO] 模拟初始化相机，索引: 0
[2025-09-30 13:27:35.059] [INFO] 相机初始化成功
[2025-09-30 13:27:35.060] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-30 13:27:35.060] [INFO] 视觉配置加载完成
[2025-09-30 13:27:35.060] [INFO] VisionManager初始化完成
[2025-09-30 13:27:35.060] [INFO] 初始化数据Manager...
[2025-09-30 13:27:35.063] [INFO] 开始初始化StatisticsManager...
[2025-09-30 13:27:35.063] [INFO] 统计管理器配置: 数据目录=Export, 缓存大小=1000, 自动保存间隔=24分钟
[2025-09-30 13:27:35.071] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-30 13:27:35.071] [INFO] 历史数据加载完成
[2025-09-30 13:27:35.071] [INFO] StatisticsManager初始化完成
[2025-09-30 13:27:35.071] [INFO] Manager初始化完成，成功率: 100%
[2025-09-30 13:27:35.071] [INFO] 所有Manager初始化完成
[2025-09-30 13:27:35.099] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-30 13:27:35.100] [INFO] 创建并缓存面板: vision-position
[2025-09-30 13:27:35.100] [INFO] 主界面布局创建完成
[2025-09-30 13:27:35.101] [INFO] 时间更新定时器初始化完成
[2025-09-30 13:27:35.101] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-30 13:27:35.173] [INFO] 开始初始化系统
[2025-09-30 13:27:35.175] [INFO] 初始化业务逻辑
[2025-09-30 13:27:35.177] [WARN] IO管理器已经初始化
[2025-09-30 13:27:35.178] [INFO] IO管理器初始化完成
[2025-09-30 13:27:35.178] [WARN] DMC1000BMotorManager已经初始化，跳过重复初始化
[2025-09-30 13:27:35.178] [INFO] 电机管理器初始化完成
[2025-09-30 13:27:35.181] [INFO] IO事件订阅完成
[2025-09-30 13:27:35.181] [INFO] 电机事件订阅完成
[2025-09-30 13:27:35.181] [INFO] 业务层交互机制建立完成
[2025-09-30 13:27:35.181] [INFO] 业务逻辑初始化完成
[2025-09-30 13:27:35.183] [INFO] 执行UI界面刷新
[2025-09-30 13:27:35.187] [INFO] UI界面刷新完成
[2025-09-30 13:27:35.187] [INFO] 系统初始化完成
[2025-09-30 13:27:39.398] [INFO] 开始切换到自动模式...
[2025-09-30 13:27:39.400] [INFO] 开始检查系统是否准备好进入自动模式...
[2025-09-30 13:27:39.405] [INFO] 可选模块不可用: 扫描器
[2025-09-30 13:27:39.405] [INFO] 可选模块不可用: Modbus TCP通信
[2025-09-30 13:27:39.405] [INFO] 可选模块不可用: 机器人1
[2025-09-30 13:27:39.405] [INFO] 可选模块不可用: 机器人2
[2025-09-30 13:27:39.407] [INFO] 系统初始化检查完成 - 推荐模式: Degraded
[2025-09-30 13:27:39.407] [INFO] 系统初始化检查完成 - 推荐模式: Degraded
[2025-09-30 13:27:39.407] [INFO] ✅ 关键模块 系统配置管理 状态正常
[2025-09-30 13:27:39.407] [INFO] ✅ 关键模块 配置系统 状态正常
[2025-09-30 13:27:39.407] [INFO] ✅ 重要模块 DMC1000B控制卡 状态正常
[2025-09-30 13:27:39.407] [INFO] ✅ 重要模块 IO管理器 状态正常
[2025-09-30 13:27:39.407] [INFO] ✅ 重要模块 电机管理器 状态正常
[2025-09-30 13:27:39.407] [WARN] ⚠️ 重要模块 安全管理器 初始化失败: 
[2025-09-30 13:27:39.407] [INFO] ℹ️ 可选模块 扫描器 不可用: 
[2025-09-30 13:27:39.407] [INFO] ℹ️ 可选模块 Modbus TCP通信 不可用: 
[2025-09-30 13:27:39.407] [INFO] ℹ️ 可选模块 机器人1 不可用: 
[2025-09-30 13:27:39.407] [INFO] ℹ️ 可选模块 机器人2 不可用: 
[2025-09-30 13:27:39.407] [INFO] 💡 ⚠️ 系统在降级模式下运行，部分功能受限
[2025-09-30 13:27:39.407] [INFO] 💡 💡 建议检查硬件连接后重启程序
[2025-09-30 13:27:39.408] [INFO] 💡 🛡️ 安全管理器不可用，自动模式功能受限
[2025-09-30 13:27:39.408] [INFO] 系统检查完成，可以切换到自动模式（运行模式: Degraded）
[2025-09-30 13:27:39.408] [INFO] 系统模式已从 Manual 切换到 Automatic
[2025-09-30 13:27:39.410] [INFO] 初始化安全管理器...
[2025-09-30 13:27:39.412] [INFO] 开始初始化安全管理器...
[2025-09-30 13:27:39.412] [INFO] 所有依赖可用，安全管理器将在完整功能模式下运行
[2025-09-30 13:27:39.413] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:39.413] [INFO] 系统模式已保存到配置文件: Automatic
[2025-09-30 13:27:39.414] [INFO] 安全管理器初始化完成 - 运行模式: Full
[2025-09-30 13:27:39.414] [INFO] 启动安全管理器...
[2025-09-30 13:27:39.416] [INFO] 启动安全管理器监控...
[2025-09-30 13:27:39.420] [INFO] 设置输出IO O0008 成功: 逻辑OFF, 物理ON
[2025-09-30 13:27:39.421] [INFO] 设置输出IO O0009 成功: 逻辑OFF, 物理ON
[2025-09-30 13:27:39.421] [INFO] 设置输出IO O0010 成功: 逻辑OFF, 物理ON
[2025-09-30 13:27:39.438] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:39.439] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:39.453] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:39.522] [INFO] 设置输出IO O0010 成功: 逻辑ON, 物理OFF
[2025-09-30 13:27:39.523] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:39.524] [INFO] 安全管理器监控已启动
[2025-09-30 13:27:39.528] [INFO] 安全管理器监控循环已启动
[2025-09-30 13:27:39.529] [INFO] 开始执行开机自检流程...
[2025-09-30 13:27:39.534] [INFO] ========== 开始执行开机自检流程 ==========
[2025-09-30 13:27:39.535] [INFO] 支持脱机测试模式：硬件未连接时将记录警告但继续执行
[2025-09-30 13:27:39.538] [INFO] 步骤1：开始初始化DMC1000B运动控制卡...
[2025-09-30 13:27:39.538] [INFO] DMC1000B控制卡状态检查成功
[2025-09-30 13:27:39.539] [INFO] DMC1000B电机管理器已初始化
[2025-09-30 13:27:39.539] [INFO] DMC1000B IO管理器已初始化
[2025-09-30 13:27:39.539] [INFO] DMC1000B运动控制卡初始化流程完成
[2025-09-30 13:27:39.539] [INFO] 检测到启动信号边沿触发
[2025-09-30 13:27:39.540] [INFO] 设置输出IO O0008 成功: 逻辑OFF, 物理ON
[2025-09-30 13:27:39.541] [INFO] 设置输出IO O0009 成功: 逻辑OFF, 物理ON
[2025-09-30 13:27:39.541] [INFO] 设置输出IO O0010 成功: 逻辑OFF, 物理ON
[2025-09-30 13:27:39.542] [INFO] 步骤2：开始执行翻转电机自动归零...
[2025-09-30 13:27:39.543] [INFO] 执行左翻转电机归零操作...
[2025-09-30 13:27:39.547] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:39.549] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:39.550] [INFO] 翻转电机轴0开始回零
[2025-09-30 13:27:39.550] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:39.553] [INFO] 翻转电机轴0回零参数: 起始速度=833pps, 最大速度=-1667pps, 加速时间=0.1s
[2025-09-30 13:27:39.556] [INFO] 翻转电机轴0开始监控回零过程，超时时间: 30000ms
[2025-09-30 13:27:39.558] [INFO] 翻转电机轴0回零成功：遇原点停止
[2025-09-30 13:27:39.559] [INFO] 翻转电机轴0归零状态已更新: IsHomedThisSession=true, LastHomedTime=2025/9/30 13:27:39
[2025-09-30 13:27:39.559] [INFO] 翻转电机轴0回零成功
[2025-09-30 13:27:39.559] [INFO] 左翻转电机归零成功，移动到位置1...
[2025-09-30 13:27:39.560] [INFO] 开始移动到翻转电机轴0位置1
[2025-09-30 13:27:39.564] [INFO] 翻转电机轴0归零状态检查: 本次会话已归零，直接返回true
[2025-09-30 13:27:39.564] [ERROR] 移动到翻转电机轴0位置1 执行失败
异常详情: 翻转电机轴0位置1未保存
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass53_0.<<MoveToFlipMotorPositionAsync>b__0>d.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Managers\DMC1000BMotorManager.cs:行号 655
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-30 13:27:39.564] [WARN] 左翻转电机移动到位置1失败
[2025-09-30 13:27:39.564] [INFO] 执行右翻转电机归零操作...
[2025-09-30 13:27:39.565] [INFO] 翻转电机轴1开始回零
[2025-09-30 13:27:39.565] [INFO] 翻转电机轴1回零参数: 起始速度=833pps, 最大速度=-1667pps, 加速时间=0.1s
[2025-09-30 13:27:39.565] [INFO] 翻转电机轴1开始监控回零过程，超时时间: 30000ms
[2025-09-30 13:27:39.565] [INFO] 翻转电机轴1回零成功：遇原点停止
[2025-09-30 13:27:39.565] [INFO] 翻转电机轴1归零状态已更新: IsHomedThisSession=true, LastHomedTime=2025/9/30 13:27:39
[2025-09-30 13:27:39.565] [INFO] 翻转电机轴1回零成功
[2025-09-30 13:27:39.565] [INFO] 右翻转电机归零成功，移动到位置1...
[2025-09-30 13:27:39.565] [INFO] 开始移动到翻转电机轴1位置1
[2025-09-30 13:27:39.565] [INFO] 翻转电机轴1归零状态检查: 本次会话已归零，直接返回true
[2025-09-30 13:27:39.566] [ERROR] 移动到翻转电机轴1位置1 执行失败
异常详情: 翻转电机轴1位置1未保存
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass53_0.<<MoveToFlipMotorPositionAsync>b__0>d.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Managers\DMC1000BMotorManager.cs:行号 655
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-30 13:27:39.566] [WARN] 右翻转电机移动到位置1失败
[2025-09-30 13:27:39.566] [INFO] 翻转电机自动归零流程完成
[2025-09-30 13:27:39.567] [INFO] 步骤3：开始连接双机器人...
[2025-09-30 13:27:39.575] [INFO] 开始连接机器人1...
[2025-09-30 13:27:39.576] [INFO] 初始化机器人1管理器...
[2025-09-30 13:27:39.649] [INFO] 设置输出IO O0009 成功: 逻辑ON, 物理OFF
[2025-09-30 13:27:39.649] [INFO] 安全状态变化: Safe -> Starting, 原因: 启动信号触发
[2025-09-30 13:27:39.649] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:39.832] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-30 13:27:39.849] [INFO] 连接机器人1控制端口...
[2025-09-30 13:27:39.852] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-30 13:27:39.854] [INFO] 开始TCP连接到 *************:5000
[2025-09-30 13:27:39.858] [INFO] 开始连接机器人2...
[2025-09-30 13:27:39.858] [INFO] 初始化机器人2管理器...
[2025-09-30 13:27:39.861] [INFO] 开始初始化EpsonRobotManager2...
[2025-09-30 13:27:39.861] [INFO] 从Settings系统加载Epson机器人2配置成功
[2025-09-30 13:27:39.861] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-30 13:27:39.861] [INFO] EpsonRobotManager2初始化完成
[2025-09-30 13:27:39.861] [INFO] 连接机器人2控制端口...
[2025-09-30 13:27:39.864] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-30 13:27:39.864] [INFO] 开始TCP连接到 *************:5000
[2025-09-30 13:27:40.153] [INFO] 安全状态变化: Starting -> Safe, 原因: 启动完成
[2025-09-30 13:27:40.153] [INFO] 系统启动完成
[2025-09-30 13:27:40.156] [INFO] 检测到停止信号边沿触发
[2025-09-30 13:27:40.163] [INFO] 设置输出IO O0008 成功: 逻辑OFF, 物理ON
[2025-09-30 13:27:40.164] [INFO] 设置输出IO O0009 成功: 逻辑OFF, 物理ON
[2025-09-30 13:27:40.164] [INFO] 设置输出IO O0010 成功: 逻辑OFF, 物理ON
[2025-09-30 13:27:40.165] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:40.167] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:40.191] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:40.278] [INFO] 设置输出IO O0010 成功: 逻辑ON, 物理OFF
[2025-09-30 13:27:40.278] [INFO] 安全状态变化: Safe -> ProgramStopped, 原因: 停止信号触发
[2025-09-30 13:27:40.278] [INFO] 程序已停止
[2025-09-30 13:27:40.278] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:42.610] [INFO] 开始执行开机自检流程...
[2025-09-30 13:27:42.721] [WARN] 开机自检已在运行中，跳过重复执行
[2025-09-30 13:27:42.721] [WARN] 开机自检发现一些问题，但系统可以在脱机测试模式下运行
[2025-09-30 13:27:42.722] [INFO] 自动化流程已启动
[2025-09-30 13:27:42.723] [INFO] 开机自检完成，自动化流程已启动
[2025-09-30 13:27:42.729] [INFO] 开始执行自动化工作流程...
[2025-09-30 13:27:42.731] [INFO] 初始化WorkflowManager...
[2025-09-30 13:27:42.734] [INFO] 开始初始化WorkflowManager...
[2025-09-30 13:27:42.735] [INFO] 初始化AutoMode控制器引用...
[2025-09-30 13:27:42.736] [INFO] 开始初始化SCARA自动模式控制器...
[2025-09-30 13:27:42.737] [INFO] SCARA自动模式控制器初始化完成
[2025-09-30 13:27:42.743] [INFO] 开始初始化EpsonRobotAutoModeController...
[2025-09-30 13:27:42.746] [INFO] 配置加载完成
[2025-09-30 13:27:42.747] [INFO] EpsonRobotAutoModeController初始化完成
[2025-09-30 13:27:42.748] [INFO] AutoMode控制器引用初始化完成
[2025-09-30 13:27:42.748] [INFO] 开始订阅各Manager事件...
[2025-09-30 13:27:42.748] [INFO] 已订阅皮带电机控制器事件
[2025-09-30 13:27:42.749] [INFO] 已订阅扫码器自动模式管理器事件
[2025-09-30 13:27:42.749] [INFO] Manager事件订阅完成
[2025-09-30 13:27:42.749] [INFO] WorkflowManager初始化完成
[2025-09-30 13:27:42.749] [INFO] 启动工作流管理器...
[2025-09-30 13:27:42.751] [INFO] 启动工作流（产品ID: ）
[2025-09-30 13:27:42.753] [INFO] 开始执行工作流启动序列...
[2025-09-30 13:27:42.755] [INFO] 工作流状态变更: Idle -> WaitingForScan
[2025-09-30 13:27:42.755] [INFO] 步骤1: 初始化AutoMode控制器
[2025-09-30 13:27:42.757] [INFO] 初始化所有AutoMode控制器...
[2025-09-30 13:27:42.760] [INFO] 开始初始化BeltMotorAutoModeController...
[2025-09-30 13:27:42.762] [INFO] 初始化皮带电机自动控制依赖...
[2025-09-30 13:27:42.762] [INFO] 皮带电机自动控制依赖初始化完成
[2025-09-30 13:27:42.762] [INFO] BeltMotorAutoModeController初始化完成
[2025-09-30 13:27:42.764] [INFO] 控制器状态变化: Idle -> Initializing
[2025-09-30 13:27:42.765] [INFO] 开始初始化依赖管理器...
[2025-09-30 13:27:42.765] [WARN] MultiScannerManager已经初始化，跳过重复初始化
[2025-09-30 13:27:42.765] [WARN] ScannerAutoModeManager已经初始化，跳过重复初始化
[2025-09-30 13:27:42.766] [INFO] 开始初始化机器人连接流程...
[2025-09-30 13:27:42.774] [INFO] 开始初始化机器人1连接...
[2025-09-30 13:27:42.774] [INFO] 机器人1: 连接主端口...
[2025-09-30 13:27:42.775] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-30 13:27:42.775] [INFO] 开始TCP连接到 *************:5000
[2025-09-30 13:27:44.858] [ERROR] TCP连接超时到 *************:5000，超时时间: 5000ms
[2025-09-30 13:27:44.858] [ERROR] 连接启动/停止TCP/IP 执行失败
异常详情: 控制端口连接超时: *************:5000
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<<ConnectStartStopAsync>b__71_0>d.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Managers\EpsonRobotManager.cs:行号 258
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-30 13:27:44.862] [WARN] 机器人1控制端口连接失败
[2025-09-30 13:27:44.874] [ERROR] TCP连接超时到 *************:5000，超时时间: 5000ms
[2025-09-30 13:27:44.874] [ERROR] 连接启动/停止TCP/IP 执行失败
异常详情: 控制端口连接超时: *************:5000
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager2.<<ConnectStartStopAsync>b__71_0>d.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Managers\EpsonRobotManager2.cs:行号 258
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-30 13:27:44.876] [WARN] 机器人2控制端口连接失败
[2025-09-30 13:27:44.876] [INFO] 机器人连接结果 - 机器人1: 失败, 机器人2: 失败
[2025-09-30 13:27:44.876] [WARN] 双机器人连接失败，系统运行在脱机测试模式
[2025-09-30 13:27:44.883] [INFO] ========== 步骤4：扫码器连接验证 ==========
[2025-09-30 13:27:44.883] [INFO] 正在验证扫码器1连接...
[2025-09-30 13:27:44.889] [INFO] 扫描枪1状态变更: Connecting - 正在连接扫描枪1...
[2025-09-30 13:27:44.893] [INFO] 扫码器1状态变化: Connecting - 正在连接扫描枪1...
[2025-09-30 13:27:44.897] [ERROR] 连接扫描枪1 执行失败
异常详情: 对端口“COM1”的访问被拒绝。
堆栈跟踪:    在 System.IO.Ports.InternalResources.WinIOError(Int32 errorCode, String str)
   在 System.IO.Ports.SerialStream..ctor(String portName, Int32 baudRate, Parity parity, Int32 dataBits, StopBits stopBits, Int32 readTimeout, Int32 writeTimeout, Handshake handshake, Boolean dtrEnable, Boolean rtsEnable, Boolean discardNull, Byte parityReplace)
   在 System.IO.Ports.SerialPort.Open()
   在 MyHMI.Managers.ScannerInstance.<ConnectAsync>b__32_1() 位置 F:\Project\C#_project\hr\HR2\Managers\MultiScannerManager.cs:行号 941
   在 System.Threading.Tasks.Task.Execute()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   在 MyHMI.Managers.ScannerInstance.<<ConnectAsync>b__32_0>d.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Managers\MultiScannerManager.cs:行号 935
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-30 13:27:44.899] [WARN] 扫码器1连接失败
[2025-09-30 13:27:44.899] [INFO] 正在验证扫码器2连接...
[2025-09-30 13:27:44.899] [INFO] 扫描枪2状态变更: Connecting - 正在连接扫描枪2...
[2025-09-30 13:27:44.900] [INFO] 扫码器2状态变化: Connecting - 正在连接扫描枪2...
[2025-09-30 13:27:44.901] [INFO] 扫描枪2连接成功: COM2
[2025-09-30 13:27:44.902] [INFO] 扫描枪2状态变更: Connected - 扫描枪2连接成功
[2025-09-30 13:27:44.902] [INFO] 扫码器2状态变化: Connected - 扫描枪2连接成功
[2025-09-30 13:27:44.907] [INFO] 扫描枪2准备发送数据: [hello] (长度:5)
[2025-09-30 13:27:44.907] [INFO] 扫描枪2发送普通数据: hello + CRLF
[2025-09-30 13:27:45.919] [INFO] 扫码器2连接验证完成
[2025-09-30 13:27:46.429] [INFO] 正在验证扫码器3连接...
[2025-09-30 13:27:46.429] [INFO] 扫描枪3状态变更: Connecting - 正在连接扫描枪3...
[2025-09-30 13:27:46.429] [INFO] 扫码器3状态变化: Connecting - 正在连接扫描枪3...
[2025-09-30 13:27:46.435] [INFO] 扫描枪3连接成功: COM3
[2025-09-30 13:27:46.435] [INFO] 扫描枪3状态变更: Connected - 扫描枪3连接成功
[2025-09-30 13:27:46.435] [INFO] 扫码器3状态变化: Connected - 扫描枪3连接成功
[2025-09-30 13:27:46.436] [INFO] 扫描枪3准备发送数据: [hello] (长度:5)
[2025-09-30 13:27:46.436] [INFO] 扫描枪3发送普通数据: hello + CRLF
[2025-09-30 13:27:47.447] [INFO] 扫码器3连接验证完成
[2025-09-30 13:27:47.782] [ERROR] TCP连接超时到 *************:5000，超时时间: 5000ms
[2025-09-30 13:27:47.782] [ERROR] 连接启动/停止TCP/IP 执行失败
异常详情: 控制端口连接超时: *************:5000
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<<ConnectStartStopAsync>b__71_0>d.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Managers\EpsonRobotManager.cs:行号 258
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-30 13:27:47.804] [ERROR] 机器人1: 主端口连接失败
[2025-09-30 13:27:47.804] [ERROR] 机器人连接初始化失败
[2025-09-30 13:27:47.805] [INFO] 控制器状态变化: Initializing -> Error
[2025-09-30 13:27:47.805] [ERROR] 6轴机器人控制器初始化失败
[2025-09-30 13:27:47.805] [ERROR] AutoMode控制器初始化失败
[2025-09-30 13:27:47.805] [ERROR] 工作流启动序列执行失败
[2025-09-30 13:27:47.805] [INFO] 工作流状态变更: WaitingForScan -> Error
[2025-09-30 13:27:47.805] [WARN] 工作流启动失败，可能是硬件未连接，但系统可以在脱机模式下运行
[2025-09-30 13:27:47.805] [WARN] 工作流处于错误状态，尝试重置...
[2025-09-30 13:27:47.806] [INFO] 重置工作流...
[2025-09-30 13:27:47.807] [INFO] 停止工作流...
[2025-09-30 13:27:47.808] [INFO] 停止所有AutoMode控制器...
[2025-09-30 13:27:47.809] [WARN] 皮带电机自动控制未在运行
[2025-09-30 13:27:47.810] [INFO] SCARA自动模式已停止
[2025-09-30 13:27:47.811] [INFO] 扫码器自动模式未启动，无需停止
[2025-09-30 13:27:47.812] [INFO] 自动模式未运行，无需停止
[2025-09-30 13:27:47.813] [INFO] 6轴机器人自动模式控制器已停止
[2025-09-30 13:27:47.813] [INFO] 所有AutoMode控制器停止完成
[2025-09-30 13:27:47.813] [INFO] 工作流状态变更: Error -> Idle
[2025-09-30 13:27:47.813] [INFO] 工作流停止完成
[2025-09-30 13:27:47.814] [INFO] 重置所有AutoMode控制器...
[2025-09-30 13:27:47.815] [INFO] 重置皮带电机自动控制...
[2025-09-30 13:27:47.815] [WARN] 皮带电机自动控制未在运行
[2025-09-30 13:27:47.815] [INFO] 皮带电机自动控制重置完成
[2025-09-30 13:27:47.815] [INFO] 重置SCARA自动模式...
[2025-09-30 13:27:47.816] [INFO] SCARA自动模式已停止
[2025-09-30 13:27:47.816] [INFO] 重置SCARA通信字段...
[2025-09-30 13:27:47.816] [INFO] SCARA通信字段重置完成
[2025-09-30 13:27:47.816] [INFO] SCARA自动模式重置完成
[2025-09-30 13:27:47.816] [INFO] 扫码器自动模式未启动，无需停止
[2025-09-30 13:27:47.818] [INFO] 扫码器自动模式已启动
[2025-09-30 13:27:47.818] [INFO] 扫码器自动模式状态变化: 启用 - 自动模式已启动
[2025-09-30 13:27:47.818] [INFO] 扫码器状态已重置
[2025-09-30 13:27:47.820] [INFO] 开始重置控制器状态...
[2025-09-30 13:27:47.848] [INFO] 机器人1: 发送控制命令 Reset
[2025-09-30 13:27:47.854] [ERROR] 发送Epson机器人命令: Custom 执行失败
异常详情: StartStopTCP/IP未连接
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<>c__DisplayClass82_0.<<SendCommandAsync>b__0>d.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Managers\EpsonRobotManager.cs:行号 724
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-30 13:27:47.857] [INFO] 机器人1: 控制命令 Reset 执行成功
[2025-09-30 13:27:47.858] [INFO] 机器人2: 发送控制命令 Reset
[2025-09-30 13:27:47.861] [ERROR] 发送Epson机器人命令: Custom 执行失败
异常详情: StartStopTCP/IP未连接
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager2.<>c__DisplayClass82_0.<<SendCommandAsync>b__0>d.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Managers\EpsonRobotManager2.cs:行号 724
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-30 13:27:47.862] [INFO] 机器人2: 控制命令 Reset 执行成功
[2025-09-30 13:27:47.864] [INFO] 控制器状态变化: Error -> Idle
[2025-09-30 13:27:47.864] [INFO] 控制器状态重置完成
[2025-09-30 13:27:47.864] [INFO] 6轴机器人自动模式控制器已重置
[2025-09-30 13:27:47.864] [INFO] 所有AutoMode控制器重置完成
[2025-09-30 13:27:47.864] [INFO] 工作流重置完成
[2025-09-30 13:27:47.956] [WARN] 部分扫码器连接验证失败，但系统将继续运行（支持脱机测试模式）
[2025-09-30 13:27:47.957] [INFO] ========== 扫码器连接验证完成 ==========
[2025-09-30 13:27:47.958] [INFO] 步骤4：开始启动皮带电机自动控制...
[2025-09-30 13:27:47.960] [INFO] 启动皮带电机自动控制...
[2025-09-30 13:27:47.962] [INFO] 执行皮带电机初始启动检查...
[2025-09-30 13:27:47.962] [INFO] 输入皮带传感器I0004初始状态: 1(无产品)
[2025-09-30 13:27:47.962] [INFO] 输入皮带传感器为1(无产品),启动输入皮带电机
[2025-09-30 13:27:47.964] [INFO] 皮带电机轴2开始连续正向运转
[2025-09-30 13:27:47.965] [INFO] 输入皮带电机初始启动成功
[2025-09-30 13:27:47.965] [INFO] 输出皮带传感器I0106初始状态: 1(无产品)
[2025-09-30 13:27:47.965] [INFO] 输出皮带传感器为1(无产品),输出皮带电机保持停止状态
[2025-09-30 13:27:47.965] [INFO] 皮带电机自动控制启动成功
[2025-09-30 13:27:47.965] [INFO] 皮带电机控制器状态变更: Idle -> Running
[2025-09-30 13:27:47.966] [INFO] 皮带电机状态变化: Idle -> Running
[2025-09-30 13:27:47.966] [INFO] 工作流状态变更: Idle -> MotorMoving
[2025-09-30 13:27:47.966] [INFO] 皮带电机自动控制启动成功
[2025-09-30 13:27:47.967] [INFO] 皮带电机自动控制启动成功
[2025-09-30 13:27:47.967] [INFO] 皮带电机自动控制启动流程完成
[2025-09-30 13:27:47.967] [INFO] ========== 开机自检流程执行完成 ==========
[2025-09-30 13:27:47.968] [INFO] ========== 开机自检结果摘要 ==========
[2025-09-30 13:27:47.968] [INFO] 整体结果: 成功
[2025-09-30 13:27:47.968] [INFO] DMC1000B控制卡: 成功
[2025-09-30 13:27:47.969] [INFO] 电机管理器: 成功
[2025-09-30 13:27:47.969] [INFO] IO管理器: 成功
[2025-09-30 13:27:47.969] [INFO] 左翻转电机: 失败/未连接
[2025-09-30 13:27:47.969] [INFO] 右翻转电机: 失败/未连接
[2025-09-30 13:27:47.969] [INFO] 机器人1: 失败/未连接
[2025-09-30 13:27:47.969] [INFO] 输入皮带控制线程启动
[2025-09-30 13:27:47.969] [INFO] 输出皮带控制线程启动
[2025-09-30 13:27:47.970] [INFO] 机器人2: 失败/未连接
[2025-09-30 13:27:47.970] [INFO] 皮带电机自动控制: 成功
[2025-09-30 13:27:47.970] [INFO] 警告信息 (3条):
[2025-09-30 13:27:47.970] [WARN]   - 左翻转电机移动到位置1失败
[2025-09-30 13:27:47.970] [WARN]   - 右翻转电机移动到位置1失败
[2025-09-30 13:27:47.970] [WARN]   - 双机器人连接失败，系统运行在脱机测试模式
[2025-09-30 13:27:47.970] [INFO] 系统已准备好进入自动模式
[2025-09-30 13:27:47.970] [INFO] ==========================================
[2025-09-30 13:27:47.971] [INFO] 自动化流程已启动
[2025-09-30 13:27:47.972] [INFO] 开机自检完成，自动化流程已启动
[2025-09-30 13:27:47.972] [INFO] 成功切换到自动模式并启动自动化流程
[2025-09-30 13:27:47.973] [INFO] 开始执行自动化工作流程...
[2025-09-30 13:27:47.973] [INFO] 启动工作流管理器...
[2025-09-30 13:27:47.973] [WARN] 工作流正在运行中，当前状态: MotorMoving
[2025-09-30 13:27:47.973] [WARN] 工作流启动失败，可能是硬件未连接，但系统可以在脱机模式下运行
[2025-09-30 13:27:50.331] [INFO] 自动化流程已经在运行中
[2025-09-30 13:27:51.525] [INFO] 自动化流程已经在运行中
[2025-09-30 13:27:54.268] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-30 13:27:54.269] [INFO] 保存Settings系统配置...
[2025-09-30 13:27:54.270] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:54.270] [INFO] Settings系统配置保存完成
[2025-09-30 13:27:54.272] [INFO] 开始按顺序释放各个Manager资源...
[2025-09-30 13:27:54.272] [INFO] 清理UI资源和面板缓存...
[2025-09-30 13:27:54.272] [INFO] 时间更新定时器资源已释放
[2025-09-30 13:27:54.273] [INFO] 开始清理面板缓存，共1个面板
[2025-09-30 13:27:54.278] [INFO] 面板缓存字典已清空
[2025-09-30 13:27:54.279] [INFO] 面板缓存清理完成
[2025-09-30 13:27:54.279] [INFO] UI资源清理完成
[2025-09-30 13:27:54.279] [INFO] 释放工作流管理器资源...
[2025-09-30 13:27:54.280] [INFO] 开始释放WorkflowManager资源...
[2025-09-30 13:27:54.280] [INFO] 停止工作流...
[2025-09-30 13:27:54.280] [INFO] 停止所有AutoMode控制器...
[2025-09-30 13:27:54.280] [INFO] 停止皮带电机自动控制...
[2025-09-30 13:27:54.281] [INFO] 输入皮带控制线程结束，电机已停止
[2025-09-30 13:27:54.283] [INFO] 所有皮带电机已停止
[2025-09-30 13:27:54.283] [INFO] 皮带电机自动控制停止成功
[2025-09-30 13:27:54.283] [INFO] 皮带电机控制器状态变更: Running -> Idle
[2025-09-30 13:27:54.283] [INFO] 皮带电机状态变化: Running -> Idle
[2025-09-30 13:27:54.283] [INFO] 皮带电机自动控制停止成功
[2025-09-30 13:27:54.283] [INFO] SCARA自动模式已停止
[2025-09-30 13:27:54.283] [INFO] 扫码器自动模式已停止
[2025-09-30 13:27:54.283] [INFO] 扫码器自动模式状态变化: 禁用 - 自动模式已停止
[2025-09-30 13:27:54.283] [WARN] 扫码器自动模式已禁用，工作流可能受到影响
[2025-09-30 13:27:54.283] [INFO] 自动模式未运行，无需停止
[2025-09-30 13:27:54.283] [INFO] 6轴机器人自动模式控制器已停止
[2025-09-30 13:27:54.283] [INFO] 所有AutoMode控制器停止完成
[2025-09-30 13:27:54.283] [INFO] 工作流状态变更: MotorMoving -> Idle
[2025-09-30 13:27:54.283] [INFO] 工作流停止完成
[2025-09-30 13:27:54.284] [INFO] 开始取消订阅各Manager事件...
[2025-09-30 13:27:54.284] [INFO] 已取消订阅皮带电机控制器事件
[2025-09-30 13:27:54.284] [INFO] 已取消订阅扫码器自动模式管理器事件
[2025-09-30 13:27:54.284] [INFO] Manager事件取消订阅完成
[2025-09-30 13:27:54.284] [INFO] WorkflowManager资源释放完成
[2025-09-30 13:27:54.284] [INFO] 工作流管理器资源释放完成
[2025-09-30 13:27:54.284] [INFO] 释放启动自检管理器资源...
[2025-09-30 13:27:54.285] [INFO] 开始释放启动自检管理器资源...
[2025-09-30 13:27:54.285] [INFO] 启动自检管理器资源释放完成
[2025-09-30 13:27:54.285] [INFO] 启动自检管理器资源释放完成
[2025-09-30 13:27:54.285] [INFO] 释放DMC1000B电机管理器资源...
[2025-09-30 13:27:54.286] [INFO] 开始释放DMC1000B资源...
[2025-09-30 13:27:54.288] [INFO] 停止所有电机部分失败
[2025-09-30 13:27:54.289] [INFO] 电机监控循环被取消
[2025-09-30 13:27:54.289] [INFO] 电机监控循环结束
[2025-09-30 13:27:54.289] [INFO] 电机监控任务已停止
[2025-09-30 13:27:54.289] [INFO] DMC1000B资源释放完成
[2025-09-30 13:27:54.289] [INFO] DMC1000B电机管理器资源释放完成
[2025-09-30 13:27:54.289] [INFO] 设置IO安全状态并释放DMC1000BIO管理器资源...
[2025-09-30 13:27:54.289] [INFO] 程序关闭时设置IO安全状态...
[2025-09-30 13:27:54.291] [INFO] 开始设置所有输出IO到安全状态...
[2025-09-30 13:27:54.291] [INFO] 安全状态配置: OutputSafeState=False, InvertAllOutputs=True
[2025-09-30 13:27:54.291] [INFO] 安全状态计算: 目标物理状态=True(硬件关闭), 目标逻辑状态=False, 逻辑反转=True
[2025-09-30 13:27:54.291] [INFO] 设置输出IO O0001 成功: 逻辑OFF, 物理ON
[2025-09-30 13:27:54.291] [INFO] 设置输出IO O0002 成功: 逻辑OFF, 物理ON
[2025-09-30 13:27:54.291] [INFO] 设置输出IO O0003 成功: 逻辑OFF, 物理ON
[2025-09-30 13:27:54.292] [INFO] 设置输出IO O0004 成功: 逻辑OFF, 物理ON
[2025-09-30 13:27:54.292] [INFO] 设置输出IO O0005 成功: 逻辑OFF, 物理ON
[2025-09-30 13:27:54.292] [INFO] 设置输出IO O0006 成功: 逻辑OFF, 物理ON
[2025-09-30 13:27:54.292] [INFO] 设置输出IO O0007 成功: 逻辑OFF, 物理ON
[2025-09-30 13:27:54.292] [INFO] 设置输出IO O0008 成功: 逻辑OFF, 物理ON
[2025-09-30 13:27:54.292] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:54.292] [INFO] 设置输出IO O0009 成功: 逻辑OFF, 物理ON
[2025-09-30 13:27:54.292] [INFO] 设置输出IO O0010 成功: 逻辑OFF, 物理ON
[2025-09-30 13:27:54.292] [INFO] 设置输出IO O0011 成功: 逻辑OFF, 物理ON
[2025-09-30 13:27:54.292] [INFO] 设置输出IO O0113 成功: 逻辑OFF, 物理ON
[2025-09-30 13:27:54.293] [INFO] 设置输出IO O0114 成功: 逻辑OFF, 物理ON
[2025-09-30 13:27:54.293] [INFO] 设置输出IO O0115 成功: 逻辑OFF, 物理ON
[2025-09-30 13:27:54.293] [INFO] 输出IO安全状态设置完成: 成功14/14个
[2025-09-30 13:27:54.293] [INFO] IO安全状态设置成功
[2025-09-30 13:27:54.293] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:54.293] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:54.295] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:54.296] [INFO] IO监控线程结束工作
[2025-09-30 13:27:54.296] [INFO] IO状态监控线程停止成功
[2025-09-30 13:27:54.296] [INFO] DMC1000B IO管理器资源释放完成
[2025-09-30 13:27:54.296] [INFO] DMC1000BIO管理器资源释放完成
[2025-09-30 13:27:54.296] [INFO] 释放其他Manager资源...
[2025-09-30 13:27:54.298] [INFO] 开始释放MotorManager资源...
[2025-09-30 13:27:54.302] [INFO] 电机7停止运动
[2025-09-30 13:27:54.302] [INFO] 电机6停止运动
[2025-09-30 13:27:54.302] [INFO] 电机5停止运动
[2025-09-30 13:27:54.302] [INFO] 电机4停止运动
[2025-09-30 13:27:54.302] [INFO] 电机3停止运动
[2025-09-30 13:27:54.302] [INFO] 电机2停止运动
[2025-09-30 13:27:54.302] [INFO] 电机1停止运动
[2025-09-30 13:27:54.303] [INFO] 电机0停止运动
[2025-09-30 13:27:54.303] [INFO] 所有电机已停止
[2025-09-30 13:27:54.303] [INFO] 电机监控循环被取消
[2025-09-30 13:27:54.304] [INFO] 电机监控循环结束
[2025-09-30 13:27:54.304] [INFO] 电机监控任务已停止
[2025-09-30 13:27:54.304] [INFO] 模拟释放运动控制卡资源
[2025-09-30 13:27:54.304] [INFO] MotorManager资源释放完成
[2025-09-30 13:27:54.304] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:54.305] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:54.306] [INFO] 开始释放ScannerManager资源...
[2025-09-30 13:27:54.314] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:54.315] [ERROR] 读取输入IO I0001 执行失败
异常详情: IO管理器未初始化
堆栈跟踪:    在 MyHMI.Managers.DMC1000BIOManager.<>c__DisplayClass30_0.<<ReadInputAsync>b__0>d.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Managers\DMC1000BIOManager.cs:行号 335
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-30 13:27:54.315] [INFO] 检测到启动信号边沿触发
[2025-09-30 13:27:54.316] [ERROR] 读取输入IO I0101 执行失败
异常详情: IO管理器未初始化
堆栈跟踪:    在 MyHMI.Managers.DMC1000BIOManager.<>c__DisplayClass30_0.<<ReadInputAsync>b__0>d.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Managers\DMC1000BIOManager.cs:行号 335
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-30 13:27:54.316] [WARN] 存在紧急停止条件，无法启动
[2025-09-30 13:27:54.316] [ERROR] 读取输入IO I0002 执行失败
异常详情: IO管理器未初始化
堆栈跟踪:    在 MyHMI.Managers.DMC1000BIOManager.<>c__DisplayClass30_0.<<ReadInputAsync>b__0>d.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Managers\DMC1000BIOManager.cs:行号 335
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-30 13:27:54.316] [INFO] 检测到停止信号边沿触发
[2025-09-30 13:27:54.316] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:54.316] [INFO] 程序已停止
[2025-09-30 13:27:54.316] [ERROR] 读取输入IO I0003 执行失败
异常详情: IO管理器未初始化
堆栈跟踪:    在 MyHMI.Managers.DMC1000BIOManager.<>c__DisplayClass30_0.<<ReadInputAsync>b__0>d.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Managers\DMC1000BIOManager.cs:行号 335
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-30 13:27:54.316] [ERROR] 读取输入IO I0101 执行失败
异常详情: IO管理器未初始化
堆栈跟踪:    在 MyHMI.Managers.DMC1000BIOManager.<>c__DisplayClass30_0.<<ReadInputAsync>b__0>d.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Managers\DMC1000BIOManager.cs:行号 335
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-30 13:27:54.317] [WARN] 触发紧急停止: 安全门开启
[2025-09-30 13:27:54.317] [WARN] IO管理器不可用，无法设置指示灯状态: Red，原因: 安全状态变化: 安全门开启
[2025-09-30 13:27:54.317] [INFO] 安全状态变化: ProgramStopped -> EmergencyStop, 原因: 安全门开启
[2025-09-30 13:27:54.317] [ERROR] 读取输入IO I0102 执行失败
异常详情: IO管理器未初始化
堆栈跟踪:    在 MyHMI.Managers.DMC1000BIOManager.<>c__DisplayClass30_0.<<ReadInputAsync>b__0>d.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Managers\DMC1000BIOManager.cs:行号 335
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-30 13:27:54.318] [ERROR] 读取输入IO I0103 执行失败
异常详情: IO管理器未初始化
堆栈跟踪:    在 MyHMI.Managers.DMC1000BIOManager.<>c__DisplayClass30_0.<<ReadInputAsync>b__0>d.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Managers\DMC1000BIOManager.cs:行号 335
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-30 13:27:54.323] [INFO] 扫描枪连接已断开
[2025-09-30 13:27:54.324] [INFO] 扫描枪状态变更: Disconnected - 扫描枪连接已断开
[2025-09-30 13:27:54.324] [INFO] ScannerManager资源释放完成
[2025-09-30 13:27:54.326] [INFO] 开始释放EpsonRobotManager资源...
[2025-09-30 13:27:54.329] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:54.330] [INFO] EpsonRobotManager资源释放完成
[2025-09-30 13:27:54.331] [INFO] 开始释放EpsonRobotManager2资源...
[2025-09-30 13:27:54.336] [INFO] EpsonRobotManager2资源释放完成
[2025-09-30 13:27:54.338] [INFO] 开始释放VisionManager资源...
[2025-09-30 13:27:54.339] [INFO] 模拟释放相机资源
[2025-09-30 13:27:54.340] [INFO] VisionManager资源释放完成
[2025-09-30 13:27:54.341] [INFO] 开始释放StatisticsManager资源...
[2025-09-30 13:27:54.341] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:54.341] [INFO] 自动保存任务已停止
[2025-09-30 13:27:54.344] [INFO] StatisticsManager资源释放完成
[2025-09-30 13:27:54.344] [INFO] 其他Manager资源释放完成
[2025-09-30 13:27:54.344] [INFO] 释放DMC1000B控制卡资源...
[2025-09-30 13:27:54.345] [INFO] 开始释放DMC1000B控制卡资源...
[2025-09-30 13:27:54.345] [INFO] DMC1000B控制卡资源释放成功
[2025-09-30 13:27:54.346] [INFO] DMC1000B控制卡资源释放完成
[2025-09-30 13:27:54.346] [INFO] 所有Manager资源释放流程完成
[2025-09-30 13:27:54.346] [INFO] 所有资源释放完成，程序即将退出
[2025-09-30 13:27:54.346] [INFO] 程序正在关闭，开始释放所有Manager资源...
[2025-09-30 13:27:54.350] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
[2025-09-30 13:27:54.354] [ERROR] 读取输入IO I0001 执行失败
异常详情: IO管理器未初始化
堆栈跟踪:    在 MyHMI.Managers.DMC1000BIOManager.<>c__DisplayClass30_0.<<ReadInputAsync>b__0>d.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Managers\DMC1000BIOManager.cs:行号 335
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-30 13:27:54.354] [ERROR] 读取输入IO I0002 执行失败
异常详情: IO管理器未初始化
堆栈跟踪:    在 MyHMI.Managers.DMC1000BIOManager.<>c__DisplayClass30_0.<<ReadInputAsync>b__0>d.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Managers\DMC1000BIOManager.cs:行号 335
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-30 13:27:54.355] [ERROR] 读取输入IO I0003 执行失败
异常详情: IO管理器未初始化
堆栈跟踪:    在 MyHMI.Managers.DMC1000BIOManager.<>c__DisplayClass30_0.<<ReadInputAsync>b__0>d.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Managers\DMC1000BIOManager.cs:行号 335
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-30 13:27:54.355] [ERROR] 读取输入IO I0101 执行失败
异常详情: IO管理器未初始化
堆栈跟踪:    在 MyHMI.Managers.DMC1000BIOManager.<>c__DisplayClass30_0.<<ReadInputAsync>b__0>d.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Managers\DMC1000BIOManager.cs:行号 335
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-30 13:27:54.355] [ERROR] 读取输入IO I0102 执行失败
异常详情: IO管理器未初始化
堆栈跟踪:    在 MyHMI.Managers.DMC1000BIOManager.<>c__DisplayClass30_0.<<ReadInputAsync>b__0>d.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Managers\DMC1000BIOManager.cs:行号 335
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-30 13:27:54.356] [ERROR] 读取输入IO I0103 执行失败
异常详情: IO管理器未初始化
堆栈跟踪:    在 MyHMI.Managers.DMC1000BIOManager.<>c__DisplayClass30_0.<<ReadInputAsync>b__0>d.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Managers\DMC1000BIOManager.cs:行号 335
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 F:\Project\C#_project\hr\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-30 13:27:54.365] [INFO] 设置保存成功: C:\Users\<USER>\AppData\Roaming\HR2\settings.dat
