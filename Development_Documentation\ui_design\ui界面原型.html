<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DMC1000运动控制系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #2c3e50;
            color: #ffffff;
            overflow: hidden;
        }

        /* 顶部标题栏 */
        .title-bar {
            width: 1920px;
            height: 60px;
            background-color: #1e2329;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
        }

        /* 系统菜单栏 */
        .system-menu {
            width: 1920px;
            height: 40px;
            background-color: #252930;
            display: flex;
            align-items: center;
            padding-left: 20px;
            font-size: 14px;
        }

        .system-menu span {
            margin-right: 30px;
            cursor: pointer;
            padding: 8px 15px;
            border-radius: 3px;
            transition: background-color 0.3s;
        }

        .system-menu span:hover {
            background-color: #3498db;
        }

        /* 主要功能区域 */
        .main-area {
            width: 1920px;
            height: 920px;
            display: flex;
        }

        /* 左侧一级功能菜单 */
        .left-menu {
            width: 200px;
            height: 920px;
            background-color: #2d3339;
        }

        .menu-item {
            width: 200px;
            height: 153px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            cursor: pointer;
            border-bottom: 1px solid #4a5661;
            transition: background-color 0.3s;
        }

        .menu-item:hover, .menu-item.active {
            background-color: #3498db;
        }

        /* 功能显示区 */
        .function-area {
            width: 700px;
            height: 920px;
            background-color: #34495e;
            border: 1px solid #4a5661;
            display: flex;
            flex-direction: column;
        }

        /* 二级Tab菜单栏 */
        .tab-menu {
            width: 100%;
            height: 40px;
            background-color: #2d3339;
            display: flex;
            align-items: center;
            border-bottom: 1px solid #4a5661;
        }

        .tab-item {
            padding: 8px 16px;
            margin-right: 2px;
            background-color: #3b434a;
            color: #ffffff;
            cursor: pointer;
            font-size: 14px;
            border-radius: 3px 3px 0 0;
            transition: background-color 0.3s;
        }

        .tab-item:hover, .tab-item.active {
            background-color: #3498db;
        }

        /* 功能面板区 */
        .panel-area {
            width: 100%;
            height: 880px;
            padding: 20px;
            overflow-y: auto;
        }

        /* 右侧区域 */
        .right-area {
            width: 1020px;
            height: 920px;
            display: flex;
            flex-direction: column;
        }

        /* 工业相机显示区域 */
        .camera-area {
            width: 1020px;
            height: 460px;
            display: flex;
        }

        .camera-display {
            width: 510px;
            height: 460px;
            background-color: #2c3e50;
            border: 1px solid #34495e;
            position: relative;
        }

        .camera-title {
            position: absolute;
            top: 10px;
            left: 10px;
            font-size: 14px;
            background-color: rgba(0,0,0,0.5);
            padding: 5px 10px;
            border-radius: 3px;
        }

        .camera-content {
            width: 100%;
            height: calc(100% - 40px);
            margin-top: 40px;
            background-color: #000000;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }

        /* 控制操作区 */
        .control-area {
            width: 1020px;
            height: 230px;
            display: flex;
        }

        .production-log {
            width: 720px;
            height: 230px;
            background-color: #2c3e50;
            border: 1px solid #34495e;
            padding: 10px;
        }

        .control-buttons {
            width: 300px;
            height: 230px;
            background-color: #2c3e50;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .control-btn {
            width: 140px;
            height: 50px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s;
        }

        .control-btn:hover {
            opacity: 0.8;
        }

        .btn-start { background-color: #27ae60; color: white; }
        .btn-run { background-color: #3498db; color: white; }
        .btn-pause { background-color: #f39c12; color: white; }
        .btn-stop { background-color: #e74c3c; color: white; }

        /* 产量统计区 */
        .statistics-area {
            width: 1020px;
            height: 230px;
            background-color: #2c3e50;
            border: 1px solid #34495e;
            display: flex;
            align-items: center;
            justify-content: space-around;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            color: #bdc3c7;
        }

        .stat-ok { color: #27ae60; }
        .stat-ng { color: #e74c3c; }

        /* 底部状态栏 */
        .status-bar {
            width: 1920px;
            height: 60px;
            background-color: #1e2329;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            font-size: 14px;
        }

        .status-left, .status-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .indicator-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .dot-green { background-color: #27ae60; }
        .dot-red { background-color: #e74c3c; }
        .dot-yellow { background-color: #f39c12; }

        /* 功能面板样式 */
        .panel {
            display: none;
        }

        .panel.active {
            display: block;
        }

        .panel-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #3498db;
        }

        .control-group {
            margin-bottom: 15px;
            padding: 12px;
            background-color: #2c3e50;
            border-radius: 5px;
            border: 1px solid #4a5661;
        }

        .motor-group {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .motor-left, .motor-right {
            width: 100%;
            padding: 12px;
            background-color: #34495e;
            border-radius: 5px;
            border: 1px solid #4a5661;
        }

        .motor-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-top: 8px;
        }

        .motor-controls .btn {
            padding: 5px 10px;
            font-size: 12px;
            margin-right: 0;
            margin-bottom: 0;
        }

        .motor-params {
            display: flex;
            gap: 15px;
            margin-bottom: 10px;
        }

        .motor-params .input-group {
            margin-bottom: 5px;
        }

        .motor-params .input-group input {
            width: 80px;
            height: 25px;
            font-size: 12px;
        }

        .motor-params .input-group label {
            width: 80px;
            font-size: 12px;
        }

        .control-group h3 {
            font-size: 16px;
            margin-bottom: 10px;
            color: #ecf0f1;
        }

        .input-group {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .input-group label {
            width: 120px;
            font-size: 14px;
        }

        .input-group input {
            width: 150px;
            height: 30px;
            padding: 5px;
            border: 1px solid #4a5661;
            border-radius: 3px;
            background-color: #34495e;
            color: #ffffff;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 5px;
        }

        .btn-primary { background-color: #3498db; color: white; }
        .btn-success { background-color: #27ae60; color: white; }
        .btn-warning { background-color: #f39c12; color: white; }
        .btn-danger { background-color: #e74c3c; color: white; }

        .io-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
        }

        .io-item {
            display: flex;
            align-items: center;
            padding: 8px;
            background-color: #2c3e50;
            border-radius: 3px;
            font-size: 12px;
        }

        .io-status {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-on { background-color: #27ae60; }
        .status-off { background-color: #95a5a6; }
    </style>
</head>
<body>
    <!-- 顶部标题栏 -->
    <div class="title-bar">
        DMC1000运动控制系统
    </div>

    <!-- 系统菜单栏 -->
    <div class="system-menu">
        <span onclick="openSystemLog()">系统日志</span>
        <span onclick="openSecurity()">安全设置</span>
        <span onclick="openDeviceStatus()">外部设备状态</span>
    </div>

    <!-- 主要功能区域 -->
    <div class="main-area">
        <!-- 左侧一级功能菜单 -->
        <div class="left-menu">
            <div class="menu-item active" onclick="showPanel('vision')">视觉控制</div>
            <div class="menu-item" onclick="showPanel('motor')">电机控制</div>
            <div class="menu-item" onclick="showPanel('robot6')">6轴机器人控制</div>
            <div class="menu-item" onclick="showPanel('scara')">scara控制</div>
            <div class="menu-item" onclick="showPanel('io')">io控制</div>
            <div class="menu-item" onclick="showPanel('log')">生产日志管理</div>
        </div>

        <!-- 功能显示区 -->
        <div class="function-area">
            <!-- 二级Tab菜单栏 -->
            <div class="tab-menu" id="tab-menu">
                <!-- Tab页将根据选中的一级菜单动态显示 -->
            </div>

            <!-- 功能面板区 -->
            <div class="panel-area">
                <!-- 视觉控制面板 -->
                <div id="vision-panels" class="tab-content">
                    <div id="vision-position" class="panel active">
                        <div class="panel-title">定位相机控制</div>
                        <div class="control-group">
                            <p style="color: #95a5a6;">面板留空白，后期开发使用</p>
                        </div>
                    </div>
                    <div id="vision-align" class="panel">
                        <div class="panel-title">对位相机控制</div>
                        <div class="control-group">
                            <p style="color: #95a5a6;">面板留空白，后期开发使用</p>
                        </div>
                    </div>
                </div>

                <!-- 电机控制面板 -->
                <div id="motor-panels" class="tab-content" style="display: none;">
                    <div id="motor-flip" class="panel active">
                        <div class="panel-title">翻转电机控制</div>
                        <div class="motor-group">
                            <div class="motor-left">
                                <h4>左翻转电机</h4>
                                <div class="motor-params">
                                    <div>
                                        <div class="input-group">
                                            <label>脉冲当量:</label>
                                            <input type="number" value="1000" /> pulse/mm
                                        </div>
                                        <div class="input-group">
                                            <label>最大加速度:</label>
                                            <input type="number" value="500" /> mm/s²
                                        </div>
                                        <div class="input-group">
                                            <label>运行速度:</label>
                                            <input type="number" value="100" /> mm/s
                                        </div>
                                    </div>
                                </div>
                                <div class="motor-controls">
                                    <button class="btn btn-primary">正转</button>
                                    <button class="btn btn-primary">反转</button>
                                    <button class="btn btn-warning">回原点</button>
                                    <button class="btn btn-success">保存位置1</button>
                                    <button class="btn btn-success">保存位置2</button>
                                    <button class="btn btn-success">保存位置3</button>
                                </div>
                                <div class="motor-controls">
                                    <button class="btn btn-primary">移动到位置1</button>
                                    <button class="btn btn-primary">移动到位置2</button>
                                    <button class="btn btn-primary">移动到位置3</button>
                                </div>
                            </div>
                            <div class="motor-right">
                                <h4>右翻转电机</h4>
                                <div class="motor-params">
                                    <div>
                                        <div class="input-group">
                                            <label>脉冲当量:</label>
                                            <input type="number" value="1000" /> pulse/mm
                                        </div>
                                        <div class="input-group">
                                            <label>最大加速度:</label>
                                            <input type="number" value="500" /> mm/s²
                                        </div>
                                        <div class="input-group">
                                            <label>运行速度:</label>
                                            <input type="number" value="100" /> mm/s
                                        </div>
                                    </div>
                                </div>
                                <div class="motor-controls">
                                    <button class="btn btn-primary">正转</button>
                                    <button class="btn btn-primary">反转</button>
                                    <button class="btn btn-warning">回原点</button>
                                    <button class="btn btn-success">保存位置1</button>
                                    <button class="btn btn-success">保存位置2</button>
                                    <button class="btn btn-success">保存位置3</button>
                                </div>
                                <div class="motor-controls">
                                    <button class="btn btn-primary">移动到位置1</button>
                                    <button class="btn btn-primary">移动到位置2</button>
                                    <button class="btn btn-primary">移动到位置3</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="motor-belt" class="panel">
                        <div class="panel-title">皮带电机控制</div>
                        <div class="control-group">
                            <h3>参数设置</h3>
                            <div class="input-group">
                                <label>脉冲当量:</label>
                                <input type="number" value="1000" /> pulse/mm
                            </div>
                            <div class="input-group">
                                <label>最大加速度:</label>
                                <input type="number" value="500" /> mm/s²
                            </div>
                            <div class="input-group">
                                <label>运行速度:</label>
                                <input type="number" value="100" /> mm/s
                            </div>
                            <div class="input-group">
                                <label>点动移动距离:</label>
                                <input type="number" value="10" /> mm
                            </div>
                        </div>
                        <div class="control-group">
                            <h3>控制操作</h3>
                            <button class="btn btn-primary">正转点动</button>
                            <button class="btn btn-primary">反转点动</button>
                            <button class="btn btn-success">连续正转</button>
                            <button class="btn btn-success">连续反转</button>
                            <button class="btn btn-danger">停止</button>
                        </div>
                        <div class="control-group">
                            <h3>位置控制</h3>
                            <div class="input-group">
                                <label>相对移动距离:</label>
                                <input type="number" value="50" /> mm
                                <button class="btn btn-primary">移动</button>
                            </div>
                            <div class="input-group">
                                <label>绝对位置:</label>
                                <input type="number" value="100" /> mm
                                <button class="btn btn-primary">移动到</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 6轴机器人控制面板 -->
                <div id="robot6-panels" class="tab-content" style="display: none;">
                    <div id="robot6-control" class="panel active">
                        <div class="panel-title">6轴机器人控制</div>
                        <div class="control-group">
                            <h3>通讯设置</h3>
                            <div class="input-group">
                                <label>协议类型:</label>
                                <select style="width: 150px; height: 30px; padding: 5px; border: 1px solid #4a5661; border-radius: 3px; background-color: #34495e; color: #ffffff;">
                                    <option>TCP/IP</option>
                                </select>
                            </div>
                            <div class="input-group">
                                <label>IP地址:</label>
                                <input type="text" value="*************" />
                            </div>
                            <div class="input-group">
                                <label>端口:</label>
                                <input type="number" value="502" />
                            </div>
                        </div>
                        <div class="control-group">
                            <h3>控制操作</h3>
                            <button class="btn btn-success">启动</button>
                            <button class="btn btn-danger">急停</button>
                            <button class="btn btn-warning">复位</button>
                        </div>
                        <div class="control-group">
                            <h3>数据发送</h3>
                            <div class="input-group">
                                <label>指令:</label>
                                <input type="text" style="width: 200px;" placeholder="输入机器人指令" />
                                <button class="btn btn-primary">发送</button>
                            </div>
                        </div>
                        <div class="control-group">
                            <h3>通信监控</h3>
                            <div style="display: flex; gap: 15px;">
                                <div style="flex: 1;">
                                    <label>发送数据:</label>
                                    <div style="height: 80px; background-color: #1e1e1e; border: 1px solid #4a5661; padding: 5px; font-size: 12px; overflow-y: auto;">
                                        <div>[10:30:15] MOVE_TO_POINT(100,200,50)</div>
                                        <div>[10:30:20] GET_STATUS</div>
                                    </div>
                                </div>
                                <div style="flex: 1;">
                                    <label>接收数据:</label>
                                    <div style="height: 80px; background-color: #1e1e1e; border: 1px solid #4a5661; padding: 5px; font-size: 12px; overflow-y: auto;">
                                        <div>[10:30:16] OK</div>
                                        <div>[10:30:21] STATUS:IDLE</div>
                                    </div>
                                </div>
                            </div>
                            <div style="margin-top: 10px;">
                                <span>连接状态: </span>
                                <span style="color: #27ae60;">已连接</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- scara控制面板 -->
                <div id="scara-panels" class="tab-content" style="display: none;">
                    <div id="scara-comm" class="panel active">
                        <div class="panel-title">scara通信管理</div>
                        <div class="control-group">
                            <h3>通信设置</h3>
                            <div class="input-group">
                                <label>协议类型:</label>
                                <select style="width: 150px; height: 30px; padding: 5px; border: 1px solid #4a5661; border-radius: 3px; background-color: #34495e; color: #ffffff;">
                                    <option>Modbus TCP</option>
                                </select>
                            </div>
                            <div class="input-group">
                                <label>IP地址:</label>
                                <input type="text" value="*************" />
                            </div>
                            <div class="input-group">
                                <label>端口:</label>
                                <input type="number" value="502" />
                            </div>
                            <div class="input-group">
                                <label>从Slave ID:</label>
                                <input type="number" value="1" />
                            </div>
                            <div style="margin-top: 10px;">
                                <button class="btn btn-success">连接</button>
                                <button class="btn btn-danger">断开</button>
                                <button class="btn btn-warning">重连</button>
                            </div>
                        </div>
                        <div class="control-group">
                            <h3>通信监控</h3>
                            <div style="margin-bottom: 10px;">
                                <span>通信状态: </span>
                                <span style="color: #27ae60;">已连接</span>
                            </div>
                            <div style="height: 100px; background-color: #1e1e1e; border: 1px solid #4a5661; padding: 5px; font-size: 12px; overflow-y: auto;">
                                <div>[10:30:15] 连接成功</div>
                                <div>[10:30:20] 读取寄存器 40001-40010</div>
                                <div>[10:30:21] 返回数据: 1000,2000,3000...</div>
                            </div>
                        </div>
                    </div>
                    <div id="scara-teach" class="panel">
                        <div class="panel-title">示教功能</div>
                        <div class="control-group">
                            <h3>回零操作</h3>
                            <button class="btn btn-warning">全轴回零</button>
                            <button class="btn btn-primary">轴1回零</button>
                            <button class="btn btn-primary">轴2回零</button>
                            <button class="btn btn-primary">轴3回零</button>
                            <button class="btn btn-primary">轴4回零</button>
                        </div>
                        <div class="control-group">
                            <h3>四轴独立控制</h3>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div style="border: 1px solid #4a5661; padding: 10px; border-radius: 5px;">
                                    <h4>轴1(X轴)</h4>
                                    <div class="input-group">
                                        <label>速度:</label>
                                        <input type="number" value="100" style="width: 80px;" /> mm/s
                                    </div>
                                    <div class="input-group">
                                        <label>加速度:</label>
                                        <input type="number" value="500" style="width: 80px;" /> mm/s²
                                    </div>
                                    <div class="input-group">
                                        <label>点动距离:</label>
                                        <input type="number" value="10" style="width: 80px;" /> mm
                                    </div>
                                    <div style="margin-top: 10px;">
                                        <button class="btn btn-primary" style="font-size: 12px;">正转</button>
                                        <button class="btn btn-primary" style="font-size: 12px;">反转</button>
                                    </div>
                                </div>
                                <div style="border: 1px solid #4a5661; padding: 10px; border-radius: 5px;">
                                    <h4>轴2(Y轴)</h4>
                                    <div class="input-group">
                                        <label>速度:</label>
                                        <input type="number" value="100" style="width: 80px;" /> mm/s
                                    </div>
                                    <div class="input-group">
                                        <label>加速度:</label>
                                        <input type="number" value="500" style="width: 80px;" /> mm/s²
                                    </div>
                                    <div class="input-group">
                                        <label>点动距离:</label>
                                        <input type="number" value="10" style="width: 80px;" /> mm
                                    </div>
                                    <div style="margin-top: 10px;">
                                        <button class="btn btn-primary" style="font-size: 12px;">正转</button>
                                        <button class="btn btn-primary" style="font-size: 12px;">反转</button>
                                    </div>
                                </div>
                                <div style="border: 1px solid #4a5661; padding: 10px; border-radius: 5px;">
                                    <h4>轴3(Z轴)</h4>
                                    <div class="input-group">
                                        <label>速度:</label>
                                        <input type="number" value="50" style="width: 80px;" /> mm/s
                                    </div>
                                    <div class="input-group">
                                        <label>加速度:</label>
                                        <input type="number" value="200" style="width: 80px;" /> mm/s²
                                    </div>
                                    <div class="input-group">
                                        <label>点动距离:</label>
                                        <input type="number" value="5" style="width: 80px;" /> mm
                                    </div>
                                    <div style="margin-top: 10px;">
                                        <button class="btn btn-primary" style="font-size: 12px;">正转</button>
                                        <button class="btn btn-primary" style="font-size: 12px;">反转</button>
                                    </div>
                                </div>
                                <div style="border: 1px solid #4a5661; padding: 10px; border-radius: 5px;">
                                    <h4>轴4(R轴)</h4>
                                    <div class="input-group">
                                        <label>速度:</label>
                                        <input type="number" value="180" style="width: 80px;" /> °/s
                                    </div>
                                    <div class="input-group">
                                        <label>加速度:</label>
                                        <input type="number" value="360" style="width: 80px;" /> °/s²
                                    </div>
                                    <div class="input-group">
                                        <label>点动距离:</label>
                                        <input type="number" value="10" style="width: 80px;" /> °
                                    </div>
                                    <div style="margin-top: 10px;">
                                        <button class="btn btn-primary" style="font-size: 12px;">正转</button>
                                        <button class="btn btn-primary" style="font-size: 12px;">反转</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="control-group">
                            <h3>坐标设置</h3>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                                <div>
                                    <h4>左J2节点</h4>
                                    <div class="input-group">
                                        <label>X:</label><input type="number" value="100" style="width: 60px;" />
                                        <label>Y:</label><input type="number" value="200" style="width: 60px;" />
                                    </div>
                                    <div class="input-group">
                                        <label>Z:</label><input type="number" value="50" style="width: 60px;" />
                                        <label>R:</label><input type="number" value="0" style="width: 60px;" />
                                    </div>
                                    <button class="btn btn-success" style="font-size: 12px;">保存</button>
                                    <button class="btn btn-primary" style="font-size: 12px;">移动到</button>
                                </div>
                                <div>
                                    <h4>右J2节点</h4>
                                    <div class="input-group">
                                        <label>X:</label><input type="number" value="300" style="width: 60px;" />
                                        <label>Y:</label><input type="number" value="200" style="width: 60px;" />
                                    </div>
                                    <div class="input-group">
                                        <label>Z:</label><input type="number" value="50" style="width: 60px;" />
                                        <label>R:</label><input type="number" value="0" style="width: 60px;" />
                                    </div>
                                    <button class="btn btn-success" style="font-size: 12px;">保存</button>
                                    <button class="btn btn-primary" style="font-size: 12px;">移动到</button>
                                </div>
                            </div>
                        </div>
                        <div class="control-group">
                            <h3>抓取参数</h3>
                            <div class="input-group">
                                <label>抓取高度:</label>
                                <input type="number" value="80" /> mm
                            </div>
                            <div class="input-group">
                                <label>抓取速度:</label>
                                <input type="number" value="50" /> mm/s
                            </div>
                            <div class="input-group">
                                <label>放置高度:</label>
                                <input type="number" value="100" /> mm
                            </div>
                        </div>
                    </div>
                </div>

                <!-- IO控制面板 -->
                <div id="io-panels" class="tab-content" style="display: none;">
                    <div id="io-read" class="panel active">
                        <div class="panel-title">IO读取状态</div>
                        <div class="control-group">
                            <h3>输入端口状态 (I0001-I0016)</h3>
                            <div class="io-grid">
                                <div class="io-item"><div class="io-status status-on"></div>I0001 启动</div>
                                <div class="io-item"><div class="io-status status-off"></div>I0002 暂停</div>
                                <div class="io-item"><div class="io-status status-off"></div>I0003 急停</div>
                                <div class="io-item"><div class="io-status status-on"></div>I0004 来料感应</div>
                                <div class="io-item"><div class="io-status status-off"></div>I0005 左夹料夹感应</div>
                                <div class="io-item"><div class="io-status status-on"></div>I0006 左夹料松感应</div>
                                <div class="io-item"><div class="io-status status-off"></div>I0007 左顶料进感应</div>
                                <div class="io-item"><div class="io-status status-on"></div>I0008 左顶料退感应</div>
                                <div class="io-item"><div class="io-status status-off"></div>I0009 右夹料夹感应</div>
                                <div class="io-item"><div class="io-status status-on"></div>I0010 右夹料松感应</div>
                                <div class="io-item"><div class="io-status status-off"></div>I0011 右顶料进感应</div>
                                <div class="io-item"><div class="io-status status-on"></div>I0012 右顶料退感应</div>
                                <div class="io-item"><div class="io-status status-off"></div>I0013 左NG盘感应</div>
                                <div class="io-item"><div class="io-status status-on"></div>I0014 右NG盘感应</div>
                                <div class="io-item"><div class="io-status status-off"></div>I0015 备用</div>
                                <div class="io-item"><div class="io-status status-on"></div>I0016 板卡上电检测</div>
                            </div>
                        </div>
                    </div>
                    <div id="io-write" class="panel">
                        <div class="panel-title">IO输出控制</div>
                        <div class="control-group">
                            <h3>输出端口控制 (O0001-O0012)</h3>
                            <div class="io-grid">
                                <div class="io-item">
                                    <button class="btn btn-primary" style="font-size: 10px;">O0001 左夹料气缸</button>
                                </div>
                                <div class="io-item">
                                    <button class="btn btn-primary" style="font-size: 10px;">O0002 左顶料气缸</button>
                                </div>
                                <div class="io-item">
                                    <button class="btn btn-primary" style="font-size: 10px;">O0003 右夹料气缸</button>
                                </div>
                                <div class="io-item">
                                    <button class="btn btn-primary" style="font-size: 10px;">O0004 右顶料气缸</button>
                                </div>
                                <div class="io-item">
                                    <button class="btn btn-primary" style="font-size: 10px;">O0005 主光源</button>
                                </div>
                                <div class="io-item">
                                    <button class="btn btn-primary" style="font-size: 10px;">O0006 左NG灯</button>
                                </div>
                                <div class="io-item">
                                    <button class="btn btn-primary" style="font-size: 10px;">O0007 右NG灯</button>
                                </div>
                                <div class="io-item">
                                    <button class="btn btn-primary" style="font-size: 10px;">O0008 三色灯红</button>
                                </div>
                                <div class="io-item">
                                    <button class="btn btn-primary" style="font-size: 10px;">O0009 三色灯绿</button>
                                </div>
                                <div class="io-item">
                                    <button class="btn btn-primary" style="font-size: 10px;">O0010 三色灯黄</button>
                                </div>
                                <div class="io-item">
                                    <button class="btn btn-primary" style="font-size: 10px;">O0011 上料光源</button>
                                </div>
                                <div class="io-item">
                                    <button class="btn btn-primary" style="font-size: 10px;">O0012 备用</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 生产日志管理面板 -->
                <div id="log-panels" class="tab-content" style="display: none;">
                    <div id="log-time" class="panel active">
                        <div class="panel-title">生产日志管理</div>
                        <div class="control-group">
                            <h3>时间选择</h3>
                            <button class="btn btn-primary">按日查看</button>
                            <button class="btn btn-primary">按周查看</button>
                            <button class="btn btn-primary">按月查看</button>
                        </div>
                        <div class="control-group">
                            <h3>数据操作</h3>
                            <button class="btn btn-success">数据导出</button>
                            <button class="btn btn-danger">数据删除</button>
                        </div>
                        <div class="control-group">
                            <h3>生产统计</h3>
                            <div style="display: flex; gap: 20px;">
                                <div>总产量: <span style="color: #3498db; font-weight: bold;">1234</span></div>
                                <div>OK量: <span style="color: #27ae60; font-weight: bold;">1200</span></div>
                                <div>NG量: <span style="color: #e74c3c; font-weight: bold;">34</span></div>
                                <div>良品率: <span style="color: #3498db; font-weight: bold;">97.2%</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧区域 -->
        <div class="right-area">
            <!-- 工业相机显示区域 -->
            <div class="camera-area">
                <div class="camera-display">
                    <div class="camera-title">定位相机</div>
                    <div class="camera-content">预留相机显示区域</div>
                </div>
                <div class="camera-display">
                    <div class="camera-title">对位相机</div>
                    <div class="camera-content">预留相机显示区域</div>
                </div>
            </div>

            <!-- 控制操作区 -->
            <div class="control-area">
                <div class="production-log">
                    <h3 style="margin-bottom: 10px;">生产日志</h3>
                    <div id="log-content" style="height: 180px; overflow-y: auto; font-size: 12px; line-height: 1.4;">
                        <div>[2025-09-17 10:30:15] 系统启动</div>
                        <div>[2025-09-17 10:30:20] 左翻转电机回原点完成</div>
                        <div>[2025-09-17 10:30:25] 右翻转电机回原点完成</div>
                        <div>[2025-09-17 10:30:30] 开始生产作业</div>
                        <div>[2025-09-17 10:31:05] 产品1检测完成 - OK</div>
                        <div>[2025-09-17 10:31:40] 产品2检测完成 - OK</div>
                        <div>[2025-09-17 10:32:15] 产品3检测完成 - NG</div>
                        <div>[2025-09-17 10:32:50] 产品4检测完成 - OK</div>
                    </div>
                </div>
                <div class="control-buttons">
                    <button class="control-btn btn-start" onclick="startProduction()">开始</button>
                    <button class="control-btn btn-run" onclick="resetProduction()">复位</button>
                    <button class="control-btn btn-pause" onclick="pauseProduction()">暂停</button>
                    <button class="control-btn btn-stop" onclick="stopProduction()">停止</button>
                </div>
            </div>

            <!-- 产量统计区 -->
            <div class="statistics-area">
                <div class="stat-item">
                    <div class="stat-value">1234</div>
                    <div class="stat-label">总产量</div>
                </div>
                <div class="stat-item stat-ok">
                    <div class="stat-value">1200</div>
                    <div class="stat-label">OK产量</div>
                </div>
                <div class="stat-item stat-ng">
                    <div class="stat-value">34</div>
                    <div class="stat-label">NG产量</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">97.2%</div>
                    <div class="stat-label">良品率</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部状态栏 -->
    <div class="status-bar">
        <div class="status-left">
            <div class="status-indicator">
                <div class="indicator-dot dot-green"></div>
                <span>机器人: 运行中</span>
            </div>
            <div class="status-indicator">
                <div class="indicator-dot dot-green"></div>
                <span>左翻转电机: 空闲中</span>
            </div>
            <div class="status-indicator">
                <div class="indicator-dot dot-red"></div>
                <span>右翻转电机: 执行中</span>
            </div>
        </div>
        <div class="status-right">
            <div class="status-indicator">
                <div class="indicator-dot dot-green"></div>
                <span>DMC1000连接正常</span>
            </div>
            <span id="current-time">2025-09-17 10:33:25</span>
        </div>
    </div>

    <script>
        // Tab菜单配置
        const tabConfig = {
            'vision': [
                { id: 'vision-position', name: '定位相机控制' },
                { id: 'vision-align', name: '对位相机控制' }
            ],
            'motor': [
                { id: 'motor-flip', name: '翻转电机控制' },
                { id: 'motor-belt', name: '皮带电机控制' }
            ],
            'robot6': [
                { id: 'robot6-control', name: '机器人控制' }
            ],
            'scara': [
                { id: 'scara-comm', name: '通信管理' },
                { id: 'scara-teach', name: '示教功能' }
            ],
            'io': [
                { id: 'io-read', name: '读取IO' },
                { id: 'io-write', name: '写入IO' }
            ],
            'log': [
                { id: 'log-time', name: '日志时间选择' }
            ]
        };

        // 菜单切换功能
        function showPanel(panelName) {
            // 隐藏所有tab-content
            const allTabContents = document.querySelectorAll('.tab-content');
            allTabContents.forEach(content => content.style.display = 'none');
            
            // 显示选中的tab-content
            const targetContent = document.getElementById(panelName + '-panels');
            if (targetContent) {
                targetContent.style.display = 'block';
            }
            
            // 更新一级菜单状态
            const menuItems = document.querySelectorAll('.menu-item');
            menuItems.forEach(item => item.classList.remove('active'));
            event.target.classList.add('active');
            
            // 更新Tab菜单
            updateTabMenu(panelName);
        }

        // 更新Tab菜单
        function updateTabMenu(panelName) {
            const tabMenu = document.getElementById('tab-menu');
            const tabs = tabConfig[panelName] || [];
            
            tabMenu.innerHTML = '';
            
            tabs.forEach((tab, index) => {
                const tabItem = document.createElement('div');
                tabItem.className = 'tab-item' + (index === 0 ? ' active' : '');
                tabItem.textContent = tab.name;
                tabItem.onclick = () => showTabPanel(panelName, tab.id, tabItem);
                tabMenu.appendChild(tabItem);
            });
            
            // 默认显示第一个Tab
            if (tabs.length > 0) {
                showTabPanel(panelName, tabs[0].id);
            }
        }

        // 显示Tab面板
        function showTabPanel(panelName, tabId, tabElement = null) {
            // 隐藏当前组的所有panel
            const currentContent = document.getElementById(panelName + '-panels');
            if (currentContent) {
                const panels = currentContent.querySelectorAll('.panel');
                panels.forEach(panel => panel.classList.remove('active'));
                
                // 显示选中的panel
                const targetPanel = document.getElementById(tabId);
                if (targetPanel) {
                    targetPanel.classList.add('active');
                }
            }
            
            // 更新Tab状态
            if (tabElement) {
                const tabItems = document.querySelectorAll('.tab-item');
                tabItems.forEach(item => item.classList.remove('active'));
                tabElement.classList.add('active');
            }
        }

        // 生产控制功能
        function startProduction() {
            addLog('开始生产作业');
        }

        function resetProduction() {
            addLog('系统复位');
        }

        function pauseProduction() {
            addLog('暂停生产作业');
        }

        function stopProduction() {
            addLog('停止生产作业');
        }

        // 系统菜单功能
        function openSystemLog() {
            alert('系统日志功能 - 后期开发');
        }

        function openSecurity() {
            alert('安全设置功能 - 后期开发');
        }

        function openDeviceStatus() {
            alert('外部设备状态功能 - 后期开发');
        }

        // 日志添加功能
        function addLog(message) {
            const logContent = document.getElementById('log-content');
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN', {hour12: false});
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timeStr}] ${message}`;
            logContent.appendChild(logEntry);
            logContent.scrollTop = logContent.scrollHeight;
        }

        // 时间更新
        function updateTime() {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN', {hour12: false});
            document.getElementById('current-time').textContent = timeStr;
        }

        // 初始化
        function initInterface() {
            // 默认显示视觉控制
            updateTabMenu('vision');
            // 启动时间更新
            setInterval(updateTime, 1000);
            updateTime();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initInterface);
    </script>
</body>
</html>