[2025-09-24 00:05:06.112] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 00:11:15.904] [INFO] 程序启动开始
[2025-09-24 00:11:15.906] [INFO] 配置系统初始化成功
[2025-09-24 00:11:15.961] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 00:11:15.961] [INFO] 配置系统初始化完成
[2025-09-24 00:11:15.961] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-24 00:11:15.992] [INFO] 开始初始化各个Manager...
[2025-09-24 00:11:15.993] [INFO] 初始化基础Manager...
[2025-09-24 00:11:15.999] [INFO] IO状态缓存初始化完成
[2025-09-24 00:11:16.004] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 00:11:16.005] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 00:11:16.007] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 00:11:16.007] [ERROR] DMC1000B控制卡初始化失败
[2025-09-24 00:11:16.007] [WARN] DMC1000BIO管理器初始化失败
[2025-09-24 00:11:16.011] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-24 00:11:16.011] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 00:11:16.011] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 00:11:16.012] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 00:11:16.049] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 00:11:16.063] [WARN] DMC1000B电机管理器初始化失败
[2025-09-24 00:11:16.064] [INFO] 初始化系统模式管理器...
[2025-09-24 00:11:16.067] [INFO] 开始初始化MotorManager...
[2025-09-24 00:11:16.070] [INFO] 模拟初始化运动控制卡
[2025-09-24 00:11:16.333] [INFO] 加载了8个电机的默认配置
[2025-09-24 00:11:16.334] [INFO] 电机监控任务已启动
[2025-09-24 00:11:16.334] [INFO] MotorManager初始化完成
[2025-09-24 00:11:16.334] [INFO] 初始化通信Manager...
[2025-09-24 00:11:16.337] [INFO] 电机监控循环开始
[2025-09-24 00:11:16.338] [INFO] 开始初始化ScannerManager...
[2025-09-24 00:11:16.341] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-24 00:11:16.366] [INFO] 串口初始化完成
[2025-09-24 00:11:16.451] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-24 00:11:16.484] [INFO] 扫描枪连接成功: COM1
[2025-09-24 00:11:16.484] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-24 00:11:16.484] [INFO] ScannerManager初始化完成
[2025-09-24 00:11:16.508] [INFO] 开始初始化ModbusTcpManager...
[2025-09-24 00:11:16.511] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-24 00:11:16.519] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-24 00:11:21.604] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 00:11:21.606] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-24 00:11:21.606] [INFO] ModbusTcpManager初始化完成
[2025-09-24 00:11:21.609] [INFO] 开始初始化EpsonRobotManager...
[2025-09-24 00:11:21.609] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-24 00:11:21.611] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-24 00:11:21.611] [INFO] EpsonRobotManager初始化完成
[2025-09-24 00:11:21.611] [INFO] 初始化视觉Manager...
[2025-09-24 00:11:21.614] [INFO] 开始初始化VisionManager...
[2025-09-24 00:11:21.614] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-24 00:11:21.616] [INFO] 模拟初始化相机，索引: 0
[2025-09-24 00:11:22.132] [INFO] 相机初始化成功
[2025-09-24 00:11:22.133] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-24 00:11:22.133] [INFO] 视觉配置加载完成
[2025-09-24 00:11:22.133] [INFO] VisionManager初始化完成
[2025-09-24 00:11:22.134] [INFO] 初始化数据Manager...
[2025-09-24 00:11:22.137] [INFO] 开始初始化StatisticsManager...
[2025-09-24 00:11:22.138] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-24 00:11:22.142] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-24 00:11:22.143] [INFO] 历史数据加载完成
[2025-09-24 00:11:22.143] [INFO] 自动保存任务已启动
[2025-09-24 00:11:22.143] [INFO] StatisticsManager初始化完成
[2025-09-24 00:11:22.144] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-24 00:11:22.144] [INFO] 所有Manager初始化完成
[2025-09-24 00:11:22.145] [INFO] 自动保存循环开始
[2025-09-24 00:11:22.199] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:11:22.200] [INFO] 主界面布局创建完成
[2025-09-24 00:11:22.202] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-24 00:11:23.860] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:11:23.861] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 00:11:23.867] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 00:11:23.867] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 00:11:34.546] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 00:11:34.556] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 00:11:34.556] [INFO] Epson机器人管理器初始化完成
[2025-09-24 00:11:34.574] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:11:35.298] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:11:35.298] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 00:11:35.299] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 00:11:35.299] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 00:11:44.273] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 00:11:44.278] [INFO] 翻转电机示教面板业务逻辑初始化完成
[2025-09-24 00:11:44.285] [INFO] 翻转电机示教面板初始化完成 - 按HTML原型设计
[2025-09-24 00:11:46.617] [INFO] 翻转电机示教面板资源释放完成
[2025-09-24 00:11:46.640] [INFO] 皮带电机控制面板初始化完成 - 双电机设计
[2025-09-24 00:11:46.640] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 00:11:46.646] [INFO] 皮带电机轴3参数设置成功: 脉冲当量=0.01mm/pulse, 最大速度=100mm/s
[2025-09-24 00:11:46.647] [INFO] 皮带电机轴2参数设置成功: 脉冲当量=0.01mm/pulse, 最大速度=100mm/s
[2025-09-24 00:11:46.651] [INFO] 电机参数显示刷新完成
[2025-09-24 00:11:46.651] [INFO] 皮带电机参数初始化完成
[2025-09-24 00:11:46.652] [INFO] 皮带电机控制面板业务逻辑初始化完成
[2025-09-24 00:11:46.664] [INFO] 系统配置保存成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 00:11:46.664] [INFO] 皮带电机配置保存成功
[2025-09-24 00:11:46.665] [INFO] 系统配置保存成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 00:11:46.665] [INFO] 皮带电机配置保存成功
[2025-09-24 00:11:48.465] [INFO] 皮带电机控制面板资源释放完成
[2025-09-24 00:11:48.471] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:11:48.471] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 00:11:48.472] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 00:11:48.472] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 00:11:56.524] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 00:11:56.528] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 00:11:56.528] [INFO] Epson机器人管理器初始化完成
[2025-09-24 00:11:56.531] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:11:59.433] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:11:59.433] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 00:11:59.434] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 00:11:59.434] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 00:12:00.429] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 00:12:00.432] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 00:12:00.433] [INFO] Epson机器人管理器初始化完成
[2025-09-24 00:12:00.435] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:12:11.556] [INFO] 开始连接控制端口 - IP: ************, 端口: 6000
[2025-09-24 00:12:11.557] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 00:12:11.559] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-24 00:12:11.561] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-24 00:12:16.689] [ERROR] 连接启动/停止TCP/IP 执行失败
异常详情: 控制端口连接超时: *************:5000
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<<ConnectStartStopAsync>b__71_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 243
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 00:16:26.173] [INFO] 程序启动开始
[2025-09-24 00:16:26.175] [INFO] 配置系统初始化成功
[2025-09-24 00:16:26.244] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 00:16:26.244] [INFO] 配置系统初始化完成
[2025-09-24 00:16:26.245] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-24 00:16:26.279] [INFO] 开始初始化各个Manager...
[2025-09-24 00:16:26.279] [INFO] 初始化基础Manager...
[2025-09-24 00:16:26.285] [INFO] IO状态缓存初始化完成
[2025-09-24 00:16:26.291] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 00:16:26.291] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 00:16:26.293] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 00:16:26.293] [ERROR] DMC1000B控制卡初始化失败
[2025-09-24 00:16:26.294] [WARN] DMC1000BIO管理器初始化失败
[2025-09-24 00:16:26.297] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-24 00:16:26.298] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 00:16:26.298] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 00:16:26.298] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 00:16:26.334] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 00:16:26.348] [WARN] DMC1000B电机管理器初始化失败
[2025-09-24 00:16:26.348] [INFO] 初始化系统模式管理器...
[2025-09-24 00:16:26.352] [INFO] 开始初始化MotorManager...
[2025-09-24 00:16:26.353] [INFO] 模拟初始化运动控制卡
[2025-09-24 00:16:26.572] [INFO] 加载了8个电机的默认配置
[2025-09-24 00:16:26.576] [INFO] 电机监控任务已启动
[2025-09-24 00:16:26.577] [INFO] MotorManager初始化完成
[2025-09-24 00:16:26.579] [INFO] 初始化通信Manager...
[2025-09-24 00:16:26.589] [INFO] 电机监控循环开始
[2025-09-24 00:16:26.590] [INFO] 开始初始化ScannerManager...
[2025-09-24 00:16:26.592] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-24 00:16:26.606] [INFO] 串口初始化完成
[2025-09-24 00:16:26.610] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-24 00:16:26.639] [INFO] 扫描枪连接成功: COM1
[2025-09-24 00:16:26.649] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-24 00:16:26.649] [INFO] ScannerManager初始化完成
[2025-09-24 00:16:26.657] [INFO] 开始初始化ModbusTcpManager...
[2025-09-24 00:16:26.660] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-24 00:16:26.663] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-24 00:16:31.738] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 00:16:31.740] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-24 00:16:31.740] [INFO] ModbusTcpManager初始化完成
[2025-09-24 00:16:31.743] [INFO] 开始初始化EpsonRobotManager...
[2025-09-24 00:16:31.744] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-24 00:16:31.745] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-24 00:16:31.745] [INFO] EpsonRobotManager初始化完成
[2025-09-24 00:16:31.745] [INFO] 初始化视觉Manager...
[2025-09-24 00:16:31.748] [INFO] 开始初始化VisionManager...
[2025-09-24 00:16:31.749] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-24 00:16:31.750] [INFO] 模拟初始化相机，索引: 0
[2025-09-24 00:16:32.258] [INFO] 相机初始化成功
[2025-09-24 00:16:32.259] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-24 00:16:32.259] [INFO] 视觉配置加载完成
[2025-09-24 00:16:32.259] [INFO] VisionManager初始化完成
[2025-09-24 00:16:32.260] [INFO] 初始化数据Manager...
[2025-09-24 00:16:32.262] [INFO] 开始初始化StatisticsManager...
[2025-09-24 00:16:32.262] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-24 00:16:32.266] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-24 00:16:32.266] [INFO] 历史数据加载完成
[2025-09-24 00:16:32.267] [INFO] 自动保存任务已启动
[2025-09-24 00:16:32.267] [INFO] StatisticsManager初始化完成
[2025-09-24 00:16:32.267] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-24 00:16:32.267] [INFO] 所有Manager初始化完成
[2025-09-24 00:16:32.268] [INFO] 自动保存循环开始
[2025-09-24 00:16:32.302] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:16:32.303] [INFO] 主界面布局创建完成
[2025-09-24 00:16:32.304] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-24 00:16:33.764] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:16:33.765] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 00:16:33.769] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 00:16:33.770] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 00:16:34.405] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 00:16:34.411] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 00:16:34.411] [INFO] Epson机器人管理器初始化完成
[2025-09-24 00:16:34.424] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:16:35.291] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:16:35.291] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 00:16:35.291] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 00:16:35.292] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 00:16:36.164] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 00:16:36.168] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 00:16:36.169] [INFO] Epson机器人管理器初始化完成
[2025-09-24 00:16:36.172] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:16:52.155] [INFO] 开始连接控制端口 - IP: ************, 端口: 6000
[2025-09-24 00:16:52.156] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 00:16:52.158] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-24 00:16:52.161] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-24 00:16:57.296] [ERROR] 连接启动/停止TCP/IP 执行失败
异常详情: 控制端口连接超时: *************:5000
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<<ConnectStartStopAsync>b__71_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 243
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 00:18:04.001] [INFO] 程序启动开始
[2025-09-24 00:18:04.002] [INFO] 配置系统初始化成功
[2025-09-24 00:18:04.056] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 00:18:04.056] [INFO] 配置系统初始化完成
[2025-09-24 00:18:04.056] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-24 00:18:04.110] [INFO] 开始初始化各个Manager...
[2025-09-24 00:18:04.110] [INFO] 初始化基础Manager...
[2025-09-24 00:18:04.115] [INFO] IO状态缓存初始化完成
[2025-09-24 00:18:04.120] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 00:18:04.121] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 00:18:04.123] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 00:18:04.123] [ERROR] DMC1000B控制卡初始化失败
[2025-09-24 00:18:04.123] [WARN] DMC1000BIO管理器初始化失败
[2025-09-24 00:18:04.127] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-24 00:18:04.127] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 00:18:04.127] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 00:18:04.128] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 00:18:04.179] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 00:18:04.201] [WARN] DMC1000B电机管理器初始化失败
[2025-09-24 00:18:04.201] [INFO] 初始化系统模式管理器...
[2025-09-24 00:18:04.216] [INFO] 开始初始化MotorManager...
[2025-09-24 00:18:04.220] [INFO] 模拟初始化运动控制卡
[2025-09-24 00:18:04.438] [INFO] 加载了8个电机的默认配置
[2025-09-24 00:18:04.441] [INFO] 电机监控任务已启动
[2025-09-24 00:18:04.442] [INFO] MotorManager初始化完成
[2025-09-24 00:18:04.442] [INFO] 初始化通信Manager...
[2025-09-24 00:18:04.444] [INFO] 电机监控循环开始
[2025-09-24 00:18:04.445] [INFO] 开始初始化ScannerManager...
[2025-09-24 00:18:04.447] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-24 00:18:04.450] [INFO] 串口初始化完成
[2025-09-24 00:18:04.460] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-24 00:18:04.467] [INFO] 扫描枪连接成功: COM1
[2025-09-24 00:18:04.467] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-24 00:18:04.468] [INFO] ScannerManager初始化完成
[2025-09-24 00:18:04.471] [INFO] 开始初始化ModbusTcpManager...
[2025-09-24 00:18:04.473] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-24 00:18:04.478] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-24 00:18:09.527] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 00:18:09.528] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-24 00:18:09.529] [INFO] ModbusTcpManager初始化完成
[2025-09-24 00:18:09.531] [INFO] 开始初始化EpsonRobotManager...
[2025-09-24 00:18:09.532] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-24 00:18:09.533] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-24 00:18:09.533] [INFO] EpsonRobotManager初始化完成
[2025-09-24 00:18:09.533] [INFO] 初始化视觉Manager...
[2025-09-24 00:18:09.536] [INFO] 开始初始化VisionManager...
[2025-09-24 00:18:09.536] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-24 00:18:09.537] [INFO] 模拟初始化相机，索引: 0
[2025-09-24 00:18:10.055] [INFO] 相机初始化成功
[2025-09-24 00:18:10.060] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-24 00:18:10.060] [INFO] 视觉配置加载完成
[2025-09-24 00:18:10.061] [INFO] VisionManager初始化完成
[2025-09-24 00:18:10.062] [INFO] 初始化数据Manager...
[2025-09-24 00:18:10.072] [INFO] 开始初始化StatisticsManager...
[2025-09-24 00:18:10.074] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-24 00:18:10.087] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-24 00:18:10.088] [INFO] 历史数据加载完成
[2025-09-24 00:18:10.089] [INFO] 自动保存任务已启动
[2025-09-24 00:18:10.089] [INFO] StatisticsManager初始化完成
[2025-09-24 00:18:10.089] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-24 00:18:10.090] [INFO] 所有Manager初始化完成
[2025-09-24 00:18:10.092] [INFO] 自动保存循环开始
[2025-09-24 00:18:10.138] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:18:10.138] [INFO] 主界面布局创建完成
[2025-09-24 00:18:10.139] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-24 00:18:13.281] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 00:18:13.282] [INFO] Epson机器人管理器初始化完成
[2025-09-24 00:18:13.296] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:18:14.575] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:18:14.576] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 00:18:14.581] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 00:18:14.582] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 00:18:16.749] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 00:18:16.752] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 00:18:16.752] [INFO] Epson机器人管理器初始化完成
[2025-09-24 00:18:16.755] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:18:26.196] [INFO] 开始连接控制端口 - IP: ************, 端口: 6000
[2025-09-24 00:18:26.197] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 00:18:26.200] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-24 00:18:26.202] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-24 00:18:31.291] [ERROR] 连接启动/停止TCP/IP 执行失败
异常详情: 控制端口连接超时: *************:5000
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<<ConnectStartStopAsync>b__71_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 243
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 00:24:50.957] [INFO] 程序启动开始
[2025-09-24 00:24:50.959] [INFO] 配置系统初始化成功
[2025-09-24 00:24:51.017] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 00:24:51.017] [INFO] 配置系统初始化完成
[2025-09-24 00:24:51.018] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-24 00:24:51.039] [INFO] 开始初始化各个Manager...
[2025-09-24 00:24:51.039] [INFO] 初始化基础Manager...
[2025-09-24 00:24:51.045] [INFO] IO状态缓存初始化完成
[2025-09-24 00:24:51.052] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 00:24:51.053] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 00:24:51.054] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 00:24:51.054] [ERROR] DMC1000B控制卡初始化失败
[2025-09-24 00:24:51.055] [WARN] DMC1000BIO管理器初始化失败
[2025-09-24 00:24:51.061] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-24 00:24:51.061] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 00:24:51.061] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 00:24:51.062] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 00:24:51.105] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 00:24:51.120] [WARN] DMC1000B电机管理器初始化失败
[2025-09-24 00:24:51.120] [INFO] 初始化系统模式管理器...
[2025-09-24 00:24:51.124] [INFO] 开始初始化MotorManager...
[2025-09-24 00:24:51.126] [INFO] 模拟初始化运动控制卡
[2025-09-24 00:24:51.358] [INFO] 加载了8个电机的默认配置
[2025-09-24 00:24:51.361] [INFO] 电机监控任务已启动
[2025-09-24 00:24:51.361] [INFO] MotorManager初始化完成
[2025-09-24 00:24:51.366] [INFO] 电机监控循环开始
[2025-09-24 00:24:51.366] [INFO] 初始化通信Manager...
[2025-09-24 00:24:51.372] [INFO] 开始初始化ScannerManager...
[2025-09-24 00:24:51.376] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-24 00:24:51.408] [INFO] 串口初始化完成
[2025-09-24 00:24:51.411] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-24 00:24:51.415] [INFO] 扫描枪连接成功: COM1
[2025-09-24 00:24:51.415] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-24 00:24:51.416] [INFO] ScannerManager初始化完成
[2025-09-24 00:24:51.424] [INFO] 开始初始化ModbusTcpManager...
[2025-09-24 00:24:51.427] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-24 00:24:51.430] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-24 00:24:56.525] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 00:24:56.526] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-24 00:24:56.527] [INFO] ModbusTcpManager初始化完成
[2025-09-24 00:24:56.529] [INFO] 开始初始化EpsonRobotManager...
[2025-09-24 00:24:56.530] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-24 00:24:56.531] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-24 00:24:56.531] [INFO] EpsonRobotManager初始化完成
[2025-09-24 00:24:56.532] [INFO] 初始化视觉Manager...
[2025-09-24 00:24:56.534] [INFO] 开始初始化VisionManager...
[2025-09-24 00:24:56.534] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-24 00:24:56.535] [INFO] 模拟初始化相机，索引: 0
[2025-09-24 00:24:57.057] [INFO] 相机初始化成功
[2025-09-24 00:24:57.062] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-24 00:24:57.063] [INFO] 视觉配置加载完成
[2025-09-24 00:24:57.063] [INFO] VisionManager初始化完成
[2025-09-24 00:24:57.064] [INFO] 初始化数据Manager...
[2025-09-24 00:24:57.073] [INFO] 开始初始化StatisticsManager...
[2025-09-24 00:24:57.075] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-24 00:24:57.088] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-24 00:24:57.089] [INFO] 历史数据加载完成
[2025-09-24 00:24:57.093] [INFO] 自动保存任务已启动
[2025-09-24 00:24:57.093] [INFO] StatisticsManager初始化完成
[2025-09-24 00:24:57.094] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-24 00:24:57.095] [INFO] 所有Manager初始化完成
[2025-09-24 00:24:57.098] [INFO] 自动保存循环开始
[2025-09-24 00:24:57.144] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:24:57.144] [INFO] 主界面布局创建完成
[2025-09-24 00:24:57.146] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-24 00:25:04.631] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 00:25:04.631] [INFO] Epson机器人管理器初始化完成
[2025-09-24 00:25:04.646] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:25:06.598] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-24 00:25:07.967] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 00:25:07.967] [INFO] Epson机器人管理器初始化完成
[2025-09-24 00:25:07.971] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:25:28.980] [INFO] 开始连接控制端口 - IP: ************, 端口: 6000
[2025-09-24 00:25:28.981] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 00:25:28.982] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 00:25:28.984] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-24 00:25:28.987] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-24 00:25:28.988] [INFO] 开始TCP连接到 ************:6000
[2025-09-24 00:25:28.996] [INFO] TCP连接成功到 ************:6000
[2025-09-24 00:25:28.996] [INFO] Epson机器人StartStop连接状态变更: Connected - 启动/停止TCP/IP连接成功
[2025-09-24 00:25:28.999] [INFO] 启动/停止TCP监听循环开始
[2025-09-24 00:25:29.000] [INFO] 连接状态变化: StartStop已连接 - 启动/停止TCP/IP连接成功
[2025-09-24 00:25:29.001] [INFO] 控制端口连接成功: ************:6000
[2025-09-24 00:25:29.002] [INFO] 控制端口连接成功
[2025-09-24 00:25:31.333] [INFO] 开始连接数据端口 - IP: ************, 端口: 5000
[2025-09-24 00:25:31.333] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 00:25:31.333] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 00:25:31.336] [INFO] Epson机器人Data连接状态变更: Connecting - 正在连接数据收发TCP/IP...
[2025-09-24 00:25:31.337] [INFO] 连接状态变化: Data连接中... - 正在连接数据收发TCP/IP...
[2025-09-24 00:25:31.434] [INFO] Epson机器人Data连接状态变更: Connected - 数据收发TCP/IP连接成功
[2025-09-24 00:25:31.435] [INFO] 连接状态变化: Data已连接 - 数据收发TCP/IP连接成功
[2025-09-24 00:25:31.435] [INFO] 数据端口连接成功: ************:5000
[2025-09-24 00:25:31.437] [INFO] 数据端口连接成功
[2025-09-24 00:25:31.437] [INFO] 数据收发TCP监听循环开始
[2025-09-24 00:25:37.581] [INFO] 开始连接Epson机器人 - IP: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 00:25:37.581] [INFO] 配置详情 - IP: ************, 控制端口: 6000, 数据端口: 5000, 连接超时: 10000ms
[2025-09-24 00:25:37.581] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 00:25:37.581] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 00:25:37.582] [INFO] 管理器初始化结果: True
[2025-09-24 00:25:37.582] [INFO] 启动/停止TCP/IP已经连接
[2025-09-24 00:25:37.582] [INFO] 数据收发TCP/IP已经连接
[2025-09-24 00:25:37.584] [INFO] 发送登录命令: $Login,EPSON
[2025-09-24 00:25:37.591] [INFO] 发送Epson机器人命令: $Login,EPSON (StartStop)
[2025-09-24 00:25:47.747] [ERROR] 发送Epson机器人命令: Login 执行失败
异常详情: 命令响应超时: $Login,EPSON
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<>c__DisplayClass81_0.<<SendCommandAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 707
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 00:25:47.749] [ERROR] Epson机器人登录失败: 发送命令失败
[2025-09-24 00:25:47.750] [ERROR] 机器人错误: LoginFailed - 发送命令失败 (StartStop)
[2025-09-24 00:25:55.018] [INFO] 开始连接数据端口 - IP: ************, 端口: 5000
[2025-09-24 00:25:55.019] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 00:25:55.019] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 00:25:55.019] [INFO] 数据收发TCP/IP已经连接
[2025-09-24 00:25:55.019] [INFO] 数据端口连接成功
[2025-09-24 00:25:56.525] [INFO] 数据收发TCP监听循环结束
[2025-09-24 00:25:56.527] [INFO] Epson机器人Data连接状态变更: Disconnected - 数据收发TCP/IP连接已断开
[2025-09-24 00:25:56.527] [INFO] 连接状态变化: Data已断开 - 数据收发TCP/IP连接已断开
[2025-09-24 00:25:56.527] [INFO] 数据收发TCP/IP连接已断开
[2025-09-24 00:25:56.530] [INFO] 数据端口已断开
[2025-09-24 00:25:57.954] [INFO] 开始连接数据端口 - IP: ************, 端口: 5000
[2025-09-24 00:25:57.954] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 00:25:57.954] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 00:25:57.954] [INFO] Epson机器人Data连接状态变更: Connecting - 正在连接数据收发TCP/IP...
[2025-09-24 00:25:57.956] [INFO] 连接状态变化: Data连接中... - 正在连接数据收发TCP/IP...
[2025-09-24 00:25:57.980] [INFO] Epson机器人Data连接状态变更: Connected - 数据收发TCP/IP连接成功
[2025-09-24 00:25:57.981] [INFO] 数据收发TCP监听循环开始
[2025-09-24 00:25:57.982] [INFO] 连接状态变化: Data已连接 - 数据收发TCP/IP连接成功
[2025-09-24 00:25:57.983] [INFO] 数据端口连接成功: ************:5000
[2025-09-24 00:25:57.985] [INFO] 数据端口连接成功
[2025-09-24 00:26:48.759] [INFO] 启动/停止TCP监听循环结束
[2025-09-24 00:26:48.761] [INFO] Epson机器人StartStop连接状态变更: Disconnected - 启动/停止TCP/IP连接已断开
[2025-09-24 00:26:48.762] [INFO] 连接状态变化: StartStop已断开 - 启动/停止TCP/IP连接已断开
[2025-09-24 00:26:48.762] [INFO] 启动/停止TCP/IP连接已断开
[2025-09-24 00:26:48.765] [INFO] 控制端口已断开
[2025-09-24 00:26:51.594] [INFO] 开始连接控制端口 - IP: ************, 端口: 6000
[2025-09-24 00:26:51.595] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 00:26:51.595] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 00:26:51.595] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-24 00:26:51.596] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-24 00:26:51.597] [INFO] 开始TCP连接到 ************:6000
[2025-09-24 00:26:51.630] [INFO] TCP连接成功到 ************:6000
[2025-09-24 00:26:51.630] [INFO] Epson机器人StartStop连接状态变更: Connected - 启动/停止TCP/IP连接成功
[2025-09-24 00:26:51.630] [INFO] 启动/停止TCP监听循环开始
[2025-09-24 00:26:51.632] [INFO] 连接状态变化: StartStop已连接 - 启动/停止TCP/IP连接成功
[2025-09-24 00:26:51.632] [INFO] 控制端口连接成功: ************:6000
[2025-09-24 00:26:51.635] [INFO] 控制端口连接成功
[2025-09-24 00:26:59.388] [INFO] 开始连接数据端口 - IP: ************, 端口: 5000
[2025-09-24 00:26:59.388] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 00:26:59.388] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 00:26:59.389] [INFO] 数据收发TCP/IP已经连接
[2025-09-24 00:26:59.389] [INFO] 数据端口连接成功
[2025-09-24 00:27:22.427] [INFO] 开始连接Epson机器人 - IP: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 00:27:22.427] [INFO] 配置详情 - IP: ************, 控制端口: 6000, 数据端口: 5000, 连接超时: 10000ms
[2025-09-24 00:27:22.427] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 00:27:22.427] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 00:27:22.428] [INFO] 管理器初始化结果: True
[2025-09-24 00:27:22.428] [INFO] 启动/停止TCP/IP已经连接
[2025-09-24 00:27:22.428] [INFO] 数据收发TCP/IP已经连接
[2025-09-24 00:27:22.428] [INFO] 发送登录命令: $Login,EPSON
[2025-09-24 00:27:22.428] [INFO] 发送Epson机器人命令: $Login,EPSON (StartStop)
[2025-09-24 00:27:32.521] [ERROR] 发送Epson机器人命令: Login 执行失败
异常详情: 命令响应超时: $Login,EPSON
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<>c__DisplayClass81_0.<<SendCommandAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 707
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 00:27:32.523] [ERROR] Epson机器人登录失败: 发送命令失败
[2025-09-24 00:27:32.523] [ERROR] 机器人错误: LoginFailed - 发送命令失败 (StartStop)
[2025-09-24 00:27:39.750] [ERROR] 未登录，无法停止机器人
[2025-09-24 00:27:39.763] [INFO] 启动/停止TCP监听循环结束
[2025-09-24 00:27:39.764] [INFO] Epson机器人StartStop连接状态变更: Disconnected - 启动/停止TCP/IP连接已断开
[2025-09-24 00:27:39.766] [INFO] 连接状态变化: StartStop已断开 - 启动/停止TCP/IP连接已断开
[2025-09-24 00:27:39.766] [INFO] 启动/停止TCP/IP连接已断开
[2025-09-24 00:27:39.777] [INFO] 数据收发TCP监听循环结束
[2025-09-24 00:27:39.779] [INFO] Epson机器人Data连接状态变更: Disconnected - 数据收发TCP/IP连接已断开
[2025-09-24 00:27:39.780] [INFO] 连接状态变化: Data已断开 - 数据收发TCP/IP连接已断开
[2025-09-24 00:27:39.780] [INFO] 数据收发TCP/IP连接已断开
[2025-09-24 00:27:39.782] [INFO] 机器人已停止
[2025-09-24 00:27:41.867] [INFO] 开始连接Epson机器人 - IP: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 00:27:41.867] [INFO] 配置详情 - IP: ************, 控制端口: 6000, 数据端口: 5000, 连接超时: 10000ms
[2025-09-24 00:27:41.867] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 00:27:41.867] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 00:27:41.868] [INFO] 管理器初始化结果: True
[2025-09-24 00:27:41.868] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-24 00:27:41.869] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-24 00:27:41.869] [INFO] 开始TCP连接到 ************:6000
[2025-09-24 00:27:41.909] [INFO] TCP连接成功到 ************:6000
[2025-09-24 00:27:41.910] [INFO] Epson机器人StartStop连接状态变更: Connected - 启动/停止TCP/IP连接成功
[2025-09-24 00:27:41.910] [INFO] 启动/停止TCP监听循环开始
[2025-09-24 00:27:41.911] [INFO] 连接状态变化: StartStop已连接 - 启动/停止TCP/IP连接成功
[2025-09-24 00:27:41.911] [INFO] 控制端口连接成功: ************:6000
[2025-09-24 00:27:41.911] [INFO] Epson机器人Data连接状态变更: Connecting - 正在连接数据收发TCP/IP...
[2025-09-24 00:27:41.913] [INFO] 连接状态变化: Data连接中... - 正在连接数据收发TCP/IP...
[2025-09-24 00:27:41.917] [INFO] Epson机器人Data连接状态变更: Connected - 数据收发TCP/IP连接成功
[2025-09-24 00:27:41.918] [INFO] 数据收发TCP监听循环开始
[2025-09-24 00:27:41.919] [INFO] 连接状态变化: Data已连接 - 数据收发TCP/IP连接成功
[2025-09-24 00:27:41.919] [INFO] 数据端口连接成功: ************:5000
[2025-09-24 00:27:41.919] [INFO] 发送登录命令: $Login,EPSON
[2025-09-24 00:27:41.919] [INFO] 发送Epson机器人命令: $Login,EPSON (StartStop)
[2025-09-24 00:27:52.006] [ERROR] 发送Epson机器人命令: Login 执行失败
异常详情: 命令响应超时: $Login,EPSON
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<>c__DisplayClass81_0.<<SendCommandAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 707
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 00:27:52.008] [ERROR] Epson机器人登录失败: 发送命令失败
[2025-09-24 00:27:52.008] [ERROR] 机器人错误: LoginFailed - 发送命令失败 (StartStop)
[2025-09-24 00:28:07.061] [INFO] 开始连接Epson机器人 - IP: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 00:28:07.061] [INFO] 配置详情 - IP: ************, 控制端口: 6000, 数据端口: 5000, 连接超时: 10000ms
[2025-09-24 00:28:07.061] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 00:28:07.061] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 00:28:07.062] [INFO] 管理器初始化结果: True
[2025-09-24 00:28:07.062] [INFO] 启动/停止TCP/IP已经连接
[2025-09-24 00:28:07.062] [INFO] 数据收发TCP/IP已经连接
[2025-09-24 00:28:07.062] [INFO] 发送登录命令: $Login,EPSON
[2025-09-24 00:28:07.062] [INFO] 发送Epson机器人命令: $Login,EPSON (StartStop)
[2025-09-24 00:28:17.153] [ERROR] 发送Epson机器人命令: Login 执行失败
异常详情: 命令响应超时: $Login,EPSON
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<>c__DisplayClass81_0.<<SendCommandAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 707
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 00:28:17.153] [ERROR] Epson机器人登录失败: 发送命令失败
[2025-09-24 00:28:17.154] [ERROR] 机器人错误: LoginFailed - 发送命令失败 (StartStop)
[2025-09-24 00:28:21.330] [INFO] 开始连接控制端口 - IP: ************, 端口: 6000
[2025-09-24 00:28:21.330] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 00:28:21.331] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 00:28:21.331] [INFO] 启动/停止TCP/IP已经连接
[2025-09-24 00:28:21.332] [INFO] 控制端口连接成功
[2025-09-24 00:28:22.483] [INFO] 开始连接数据端口 - IP: ************, 端口: 5000
[2025-09-24 00:28:22.483] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 00:28:22.483] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 00:28:22.483] [INFO] 数据收发TCP/IP已经连接
[2025-09-24 00:28:22.484] [INFO] 数据端口连接成功
[2025-09-24 00:28:23.978] [INFO] 开始连接数据端口 - IP: ************, 端口: 5000
[2025-09-24 00:28:23.978] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 00:28:23.979] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 00:28:23.979] [INFO] 数据收发TCP/IP已经连接
[2025-09-24 00:28:23.979] [INFO] 数据端口连接成功
[2025-09-24 00:28:27.570] [INFO] 数据收发TCP监听循环结束
[2025-09-24 00:28:27.571] [INFO] Epson机器人Data连接状态变更: Disconnected - 数据收发TCP/IP连接已断开
[2025-09-24 00:28:27.573] [INFO] 连接状态变化: Data已断开 - 数据收发TCP/IP连接已断开
[2025-09-24 00:28:27.573] [INFO] 数据收发TCP/IP连接已断开
[2025-09-24 00:28:27.575] [INFO] 数据端口已断开
[2025-09-24 00:28:28.274] [INFO] 开始连接数据端口 - IP: ************, 端口: 5000
[2025-09-24 00:28:28.274] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 00:28:28.274] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 00:28:28.275] [INFO] Epson机器人Data连接状态变更: Connecting - 正在连接数据收发TCP/IP...
[2025-09-24 00:28:28.277] [INFO] 连接状态变化: Data连接中... - 正在连接数据收发TCP/IP...
[2025-09-24 00:28:28.295] [INFO] Epson机器人Data连接状态变更: Connected - 数据收发TCP/IP连接成功
[2025-09-24 00:28:28.296] [INFO] 数据收发TCP监听循环开始
[2025-09-24 00:28:28.297] [INFO] 连接状态变化: Data已连接 - 数据收发TCP/IP连接成功
[2025-09-24 00:28:28.297] [INFO] 数据端口连接成功: ************:5000
[2025-09-24 00:28:28.298] [INFO] 数据端口连接成功
[2025-09-24 00:28:34.908] [INFO] 开始连接Epson机器人 - IP: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 00:28:34.908] [INFO] 配置详情 - IP: ************, 控制端口: 6000, 数据端口: 5000, 连接超时: 10000ms
[2025-09-24 00:28:34.908] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 00:28:34.908] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 00:28:34.909] [INFO] 管理器初始化结果: True
[2025-09-24 00:28:34.909] [INFO] 启动/停止TCP/IP已经连接
[2025-09-24 00:28:34.909] [INFO] 数据收发TCP/IP已经连接
[2025-09-24 00:28:34.909] [INFO] 发送登录命令: $Login,EPSON
[2025-09-24 00:28:34.909] [INFO] 发送Epson机器人命令: $Login,EPSON (StartStop)
[2025-09-24 00:28:45.112] [ERROR] 发送Epson机器人命令: Login 执行失败
异常详情: 命令响应超时: $Login,EPSON
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<>c__DisplayClass81_0.<<SendCommandAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 707
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 00:28:45.112] [ERROR] Epson机器人登录失败: 发送命令失败
[2025-09-24 00:28:45.113] [ERROR] 机器人错误: LoginFailed - 发送命令失败 (StartStop)
[2025-09-24 00:30:43.996] [INFO] 程序启动开始
[2025-09-24 00:30:43.998] [INFO] 配置系统初始化成功
[2025-09-24 00:30:44.055] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 00:30:44.055] [INFO] 配置系统初始化完成
[2025-09-24 00:30:44.055] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-24 00:30:44.077] [INFO] 开始初始化各个Manager...
[2025-09-24 00:30:44.077] [INFO] 初始化基础Manager...
[2025-09-24 00:30:44.083] [INFO] IO状态缓存初始化完成
[2025-09-24 00:30:44.088] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 00:30:44.088] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 00:30:44.090] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 00:30:44.090] [ERROR] DMC1000B控制卡初始化失败
[2025-09-24 00:30:44.090] [WARN] DMC1000BIO管理器初始化失败
[2025-09-24 00:30:44.094] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-24 00:30:44.094] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 00:30:44.095] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 00:30:44.096] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 00:30:44.139] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 00:30:44.154] [WARN] DMC1000B电机管理器初始化失败
[2025-09-24 00:30:44.154] [INFO] 初始化系统模式管理器...
[2025-09-24 00:30:44.158] [INFO] 开始初始化MotorManager...
[2025-09-24 00:30:44.160] [INFO] 模拟初始化运动控制卡
[2025-09-24 00:30:44.383] [INFO] 加载了8个电机的默认配置
[2025-09-24 00:30:44.387] [INFO] 电机监控循环开始
[2025-09-24 00:30:44.404] [INFO] 电机监控任务已启动
[2025-09-24 00:30:44.405] [INFO] MotorManager初始化完成
[2025-09-24 00:30:44.406] [INFO] 初始化通信Manager...
[2025-09-24 00:30:44.409] [INFO] 开始初始化ScannerManager...
[2025-09-24 00:30:44.433] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-24 00:30:44.460] [INFO] 串口初始化完成
[2025-09-24 00:30:44.463] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-24 00:30:44.481] [INFO] 扫描枪连接成功: COM1
[2025-09-24 00:30:44.481] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-24 00:30:44.482] [INFO] ScannerManager初始化完成
[2025-09-24 00:30:44.486] [INFO] 开始初始化ModbusTcpManager...
[2025-09-24 00:30:44.488] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-24 00:30:44.494] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-24 00:30:49.571] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 00:30:49.573] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-24 00:30:49.573] [INFO] ModbusTcpManager初始化完成
[2025-09-24 00:30:49.577] [INFO] 开始初始化EpsonRobotManager...
[2025-09-24 00:30:49.577] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-24 00:30:49.579] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-24 00:30:49.580] [INFO] EpsonRobotManager初始化完成
[2025-09-24 00:30:49.580] [INFO] 初始化视觉Manager...
[2025-09-24 00:30:49.583] [INFO] 开始初始化VisionManager...
[2025-09-24 00:30:49.583] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-24 00:30:49.585] [INFO] 模拟初始化相机，索引: 0
[2025-09-24 00:30:50.096] [INFO] 相机初始化成功
[2025-09-24 00:30:50.102] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-24 00:30:50.103] [INFO] 视觉配置加载完成
[2025-09-24 00:30:50.104] [INFO] VisionManager初始化完成
[2025-09-24 00:30:50.104] [INFO] 初始化数据Manager...
[2025-09-24 00:30:50.117] [INFO] 开始初始化StatisticsManager...
[2025-09-24 00:30:50.118] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-24 00:30:50.129] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-24 00:30:50.129] [INFO] 历史数据加载完成
[2025-09-24 00:30:50.131] [INFO] 自动保存任务已启动
[2025-09-24 00:30:50.132] [INFO] StatisticsManager初始化完成
[2025-09-24 00:30:50.133] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-24 00:30:50.133] [INFO] 所有Manager初始化完成
[2025-09-24 00:30:50.135] [INFO] 自动保存循环开始
[2025-09-24 00:30:50.178] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:30:50.178] [INFO] 主界面布局创建完成
[2025-09-24 00:30:50.179] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-24 00:31:02.640] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 00:31:02.640] [INFO] Epson机器人管理器初始化完成
[2025-09-24 00:31:02.654] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:58:47.952] [INFO] 程序启动开始
[2025-09-24 00:58:47.954] [INFO] 配置系统初始化成功
[2025-09-24 00:58:48.012] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 00:58:48.012] [INFO] 配置系统初始化完成
[2025-09-24 00:58:48.013] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-24 00:58:48.048] [INFO] 开始初始化各个Manager...
[2025-09-24 00:58:48.048] [INFO] 初始化基础Manager...
[2025-09-24 00:58:48.053] [INFO] IO状态缓存初始化完成
[2025-09-24 00:58:48.059] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 00:58:48.059] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 00:58:48.061] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 00:58:48.062] [ERROR] DMC1000B控制卡初始化失败
[2025-09-24 00:58:48.062] [WARN] DMC1000BIO管理器初始化失败
[2025-09-24 00:58:48.065] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-24 00:58:48.066] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 00:58:48.066] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 00:58:48.066] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 00:58:48.107] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 00:58:48.121] [WARN] DMC1000B电机管理器初始化失败
[2025-09-24 00:58:48.121] [INFO] 初始化系统模式管理器...
[2025-09-24 00:58:48.126] [INFO] 开始初始化MotorManager...
[2025-09-24 00:58:48.128] [INFO] 模拟初始化运动控制卡
[2025-09-24 00:58:48.353] [INFO] 加载了8个电机的默认配置
[2025-09-24 00:58:48.354] [INFO] 电机监控任务已启动
[2025-09-24 00:58:48.354] [INFO] MotorManager初始化完成
[2025-09-24 00:58:48.355] [INFO] 初始化通信Manager...
[2025-09-24 00:58:48.382] [INFO] 开始初始化ScannerManager...
[2025-09-24 00:58:48.384] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-24 00:58:48.385] [INFO] 电机监控循环开始
[2025-09-24 00:58:48.388] [INFO] 串口初始化完成
[2025-09-24 00:58:48.391] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-24 00:58:48.428] [INFO] 扫描枪连接成功: COM1
[2025-09-24 00:58:48.429] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-24 00:58:48.429] [INFO] ScannerManager初始化完成
[2025-09-24 00:58:48.432] [INFO] 开始初始化ModbusTcpManager...
[2025-09-24 00:58:48.438] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-24 00:58:48.442] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-24 00:58:53.564] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 00:58:53.565] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-24 00:58:53.566] [INFO] ModbusTcpManager初始化完成
[2025-09-24 00:58:53.570] [INFO] 开始初始化EpsonRobotManager...
[2025-09-24 00:58:53.571] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-24 00:58:53.573] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-24 00:58:53.573] [INFO] EpsonRobotManager初始化完成
[2025-09-24 00:58:53.574] [INFO] 初始化视觉Manager...
[2025-09-24 00:58:53.578] [INFO] 开始初始化VisionManager...
[2025-09-24 00:58:53.579] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-24 00:58:53.581] [INFO] 模拟初始化相机，索引: 0
[2025-09-24 00:58:54.087] [INFO] 相机初始化成功
[2025-09-24 00:58:54.093] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-24 00:58:54.094] [INFO] 视觉配置加载完成
[2025-09-24 00:58:54.095] [INFO] VisionManager初始化完成
[2025-09-24 00:58:54.096] [INFO] 初始化数据Manager...
[2025-09-24 00:58:54.107] [INFO] 开始初始化StatisticsManager...
[2025-09-24 00:58:54.111] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-24 00:58:54.128] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-24 00:58:54.128] [INFO] 历史数据加载完成
[2025-09-24 00:58:54.129] [INFO] 自动保存任务已启动
[2025-09-24 00:58:54.130] [INFO] StatisticsManager初始化完成
[2025-09-24 00:58:54.130] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-24 00:58:54.131] [INFO] 所有Manager初始化完成
[2025-09-24 00:58:54.133] [INFO] 自动保存循环开始
[2025-09-24 00:58:54.199] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:58:54.199] [INFO] 主界面布局创建完成
[2025-09-24 00:58:54.201] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-24 00:59:01.189] [INFO] 对位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:59:01.749] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:59:02.518] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 00:59:02.519] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 00:59:02.523] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 00:59:02.523] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 00:59:12.712] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 00:59:12.720] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 00:59:12.720] [INFO] Epson机器人管理器初始化完成
[2025-09-24 00:59:12.752] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 01:00:13.904] [INFO] 开始连接控制端口 - IP: ************, 端口: 6000
[2025-09-24 01:00:13.905] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 01:00:13.905] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 01:00:13.909] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-24 01:00:13.915] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-24 01:00:13.916] [INFO] 开始TCP连接到 ************:6000
[2025-09-24 01:00:13.977] [INFO] TCP连接成功到 ************:6000
[2025-09-24 01:00:13.978] [INFO] Epson机器人StartStop连接状态变更: Connected - 启动/停止TCP/IP连接成功
[2025-09-24 01:00:13.980] [INFO] 启动/停止TCP监听循环开始
[2025-09-24 01:00:13.981] [INFO] 连接状态变化: StartStop已连接 - 启动/停止TCP/IP连接成功
[2025-09-24 01:00:13.982] [INFO] 控制端口连接成功: ************:6000
[2025-09-24 01:00:13.982] [INFO] 控制端口连接成功，开始登录...
[2025-09-24 01:00:13.985] [INFO] 发送登录命令: $Login,EPSON
[2025-09-24 01:00:13.990] [INFO] 发送Epson机器人命令: $Login,EPSON (StartStop)
[2025-09-24 01:00:24.165] [ERROR] 发送Epson机器人命令: Login 执行失败
异常详情: 命令响应超时: $Login,EPSON
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<>c__DisplayClass81_0.<<SendCommandAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 707
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 01:00:24.166] [ERROR] Epson机器人登录失败: 发送命令失败
[2025-09-24 01:00:24.168] [ERROR] 机器人错误: LoginFailed - 发送命令失败 (StartStop)
[2025-09-24 01:00:39.170] [INFO] 启动/停止TCP监听循环结束
[2025-09-24 01:00:39.172] [INFO] Epson机器人StartStop连接状态变更: Disconnected - 启动/停止TCP/IP连接已断开
[2025-09-24 01:00:39.173] [INFO] 连接状态变化: StartStop已断开 - 启动/停止TCP/IP连接已断开
[2025-09-24 01:00:39.173] [INFO] 启动/停止TCP/IP连接已断开
[2025-09-24 01:00:41.094] [INFO] 开始连接控制端口 - IP: ************, 端口: 6000
[2025-09-24 01:00:41.095] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 01:00:41.095] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 01:00:41.095] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-24 01:00:41.097] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-24 01:00:41.097] [INFO] 开始TCP连接到 ************:6000
[2025-09-24 01:00:41.116] [INFO] TCP连接成功到 ************:6000
[2025-09-24 01:00:41.117] [INFO] Epson机器人StartStop连接状态变更: Connected - 启动/停止TCP/IP连接成功
[2025-09-24 01:00:41.117] [INFO] 启动/停止TCP监听循环开始
[2025-09-24 01:00:41.118] [INFO] 连接状态变化: StartStop已连接 - 启动/停止TCP/IP连接成功
[2025-09-24 01:00:41.118] [INFO] 控制端口连接成功: ************:6000
[2025-09-24 01:00:41.118] [INFO] 控制端口连接成功，开始登录...
[2025-09-24 01:00:41.119] [INFO] 发送登录命令: $Login,EPSON
[2025-09-24 01:00:41.119] [INFO] 发送Epson机器人命令: $Login,EPSON (StartStop)
[2025-09-24 01:00:51.216] [ERROR] 发送Epson机器人命令: Login 执行失败
异常详情: 命令响应超时: $Login,EPSON
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<>c__DisplayClass81_0.<<SendCommandAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 707
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 01:00:51.217] [ERROR] Epson机器人登录失败: 发送命令失败
[2025-09-24 01:00:51.217] [ERROR] 机器人错误: LoginFailed - 发送命令失败 (StartStop)
[2025-09-24 01:00:52.711] [INFO] 启动/停止TCP监听循环结束
[2025-09-24 01:00:52.713] [INFO] Epson机器人StartStop连接状态变更: Disconnected - 启动/停止TCP/IP连接已断开
[2025-09-24 01:00:52.714] [INFO] 连接状态变化: StartStop已断开 - 启动/停止TCP/IP连接已断开
[2025-09-24 01:00:52.714] [INFO] 启动/停止TCP/IP连接已断开
[2025-09-24 08:52:54.429] [INFO] 程序启动开始
[2025-09-24 08:52:54.431] [INFO] 配置系统初始化成功
[2025-09-24 08:52:54.720] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 08:52:54.720] [INFO] 配置系统初始化完成
[2025-09-24 08:52:54.721] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-24 08:52:54.860] [INFO] 开始初始化各个Manager...
[2025-09-24 08:52:54.861] [INFO] 初始化基础Manager...
[2025-09-24 08:52:54.874] [INFO] IO状态缓存初始化完成
[2025-09-24 08:52:54.886] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 08:52:54.886] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 08:52:55.016] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 08:52:55.016] [ERROR] DMC1000B控制卡初始化失败
[2025-09-24 08:52:55.016] [WARN] DMC1000BIO管理器初始化失败
[2025-09-24 08:52:55.021] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-24 08:52:55.021] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 08:52:55.021] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 08:52:55.023] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 08:52:55.076] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 08:52:55.093] [WARN] DMC1000B电机管理器初始化失败
[2025-09-24 08:52:55.093] [INFO] 初始化系统模式管理器...
[2025-09-24 08:52:55.097] [INFO] 开始初始化MotorManager...
[2025-09-24 08:52:55.100] [INFO] 模拟初始化运动控制卡
[2025-09-24 08:52:55.314] [INFO] 加载了8个电机的默认配置
[2025-09-24 08:52:55.315] [INFO] 电机监控任务已启动
[2025-09-24 08:52:55.315] [INFO] MotorManager初始化完成
[2025-09-24 08:52:55.316] [INFO] 初始化通信Manager...
[2025-09-24 08:52:55.317] [INFO] 电机监控循环开始
[2025-09-24 08:52:55.318] [INFO] 开始初始化ScannerManager...
[2025-09-24 08:52:55.321] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-24 08:52:55.324] [INFO] 串口初始化完成
[2025-09-24 08:52:55.326] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-24 08:52:55.330] [INFO] 扫描枪连接成功: COM1
[2025-09-24 08:52:55.330] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-24 08:52:55.330] [INFO] ScannerManager初始化完成
[2025-09-24 08:52:55.333] [INFO] 开始初始化ModbusTcpManager...
[2025-09-24 08:52:55.334] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-24 08:52:55.337] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-24 08:53:00.393] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 08:53:00.394] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-24 08:53:00.394] [INFO] ModbusTcpManager初始化完成
[2025-09-24 08:53:00.396] [INFO] 开始初始化EpsonRobotManager...
[2025-09-24 08:53:00.397] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-24 08:53:00.398] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-24 08:53:00.398] [INFO] EpsonRobotManager初始化完成
[2025-09-24 08:53:00.398] [INFO] 初始化视觉Manager...
[2025-09-24 08:53:00.400] [INFO] 开始初始化VisionManager...
[2025-09-24 08:53:00.401] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-24 08:53:00.402] [INFO] 模拟初始化相机，索引: 0
[2025-09-24 08:53:00.921] [INFO] 相机初始化成功
[2025-09-24 08:53:00.921] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-24 08:53:00.922] [INFO] 视觉配置加载完成
[2025-09-24 08:53:00.922] [INFO] VisionManager初始化完成
[2025-09-24 08:53:00.922] [INFO] 初始化数据Manager...
[2025-09-24 08:53:00.925] [INFO] 开始初始化StatisticsManager...
[2025-09-24 08:53:00.925] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-24 08:53:00.929] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-24 08:53:00.929] [INFO] 历史数据加载完成
[2025-09-24 08:53:00.929] [INFO] 自动保存任务已启动
[2025-09-24 08:53:00.929] [INFO] StatisticsManager初始化完成
[2025-09-24 08:53:00.930] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-24 08:53:00.930] [INFO] 所有Manager初始化完成
[2025-09-24 08:53:00.931] [INFO] 自动保存循环开始
[2025-09-24 08:53:00.984] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 08:53:00.984] [INFO] 主界面布局创建完成
[2025-09-24 08:53:00.986] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-24 10:01:49.345] [INFO] 程序启动开始
[2025-09-24 10:01:49.346] [INFO] 配置系统初始化成功
[2025-09-24 10:01:49.387] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 10:01:49.387] [INFO] 配置系统初始化完成
[2025-09-24 10:01:49.387] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-24 10:01:49.399] [INFO] 开始初始化各个Manager...
[2025-09-24 10:01:49.399] [INFO] 初始化基础Manager...
[2025-09-24 10:01:49.403] [INFO] IO状态缓存初始化完成
[2025-09-24 10:01:49.408] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 10:01:49.408] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 10:01:49.410] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 10:01:49.410] [ERROR] DMC1000B控制卡初始化失败
[2025-09-24 10:01:49.410] [WARN] DMC1000BIO管理器初始化失败
[2025-09-24 10:01:49.413] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-24 10:01:49.414] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 10:01:49.414] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 10:01:49.414] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 10:01:49.415] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 10:01:49.435] [WARN] DMC1000B电机管理器初始化失败
[2025-09-24 10:01:49.435] [INFO] 初始化系统模式管理器...
[2025-09-24 10:01:49.438] [INFO] 开始初始化MotorManager...
[2025-09-24 10:01:49.440] [INFO] 模拟初始化运动控制卡
[2025-09-24 10:01:49.657] [INFO] 加载了8个电机的默认配置
[2025-09-24 10:01:49.660] [INFO] 电机监控任务已启动
[2025-09-24 10:01:49.660] [INFO] MotorManager初始化完成
[2025-09-24 10:01:49.661] [INFO] 初始化通信Manager...
[2025-09-24 10:01:49.666] [INFO] 电机监控循环开始
[2025-09-24 10:01:49.670] [INFO] 开始初始化ScannerManager...
[2025-09-24 10:01:49.677] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-24 10:01:49.683] [INFO] 串口初始化完成
[2025-09-24 10:01:49.687] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-24 10:01:49.692] [INFO] 扫描枪连接成功: COM1
[2025-09-24 10:01:49.692] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-24 10:01:49.692] [INFO] ScannerManager初始化完成
[2025-09-24 10:01:49.696] [INFO] 开始初始化ModbusTcpManager...
[2025-09-24 10:01:49.698] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-24 10:01:49.702] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-24 10:01:54.719] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 10:01:54.720] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-24 10:01:54.720] [INFO] ModbusTcpManager初始化完成
[2025-09-24 10:01:54.724] [INFO] 开始初始化EpsonRobotManager...
[2025-09-24 10:01:54.725] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-24 10:01:54.727] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-24 10:01:54.727] [INFO] EpsonRobotManager初始化完成
[2025-09-24 10:01:54.727] [INFO] 初始化视觉Manager...
[2025-09-24 10:01:54.730] [INFO] 开始初始化VisionManager...
[2025-09-24 10:01:54.731] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-24 10:01:54.732] [INFO] 模拟初始化相机，索引: 0
[2025-09-24 10:01:55.241] [INFO] 相机初始化成功
[2025-09-24 10:01:55.242] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-24 10:01:55.243] [INFO] 视觉配置加载完成
[2025-09-24 10:01:55.243] [INFO] VisionManager初始化完成
[2025-09-24 10:01:55.243] [INFO] 初始化数据Manager...
[2025-09-24 10:01:55.246] [INFO] 开始初始化StatisticsManager...
[2025-09-24 10:01:55.247] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-24 10:01:55.251] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-24 10:01:55.251] [INFO] 历史数据加载完成
[2025-09-24 10:01:55.252] [INFO] 自动保存任务已启动
[2025-09-24 10:01:55.252] [INFO] StatisticsManager初始化完成
[2025-09-24 10:01:55.252] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-24 10:01:55.252] [INFO] 所有Manager初始化完成
[2025-09-24 10:01:55.255] [INFO] 自动保存循环开始
[2025-09-24 10:01:55.295] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 10:01:55.296] [INFO] 主界面布局创建完成
[2025-09-24 10:01:55.297] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-24 10:01:57.316] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 10:01:57.317] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 10:01:57.320] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 10:01:57.321] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 10:01:59.001] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 10:01:59.006] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 10:01:59.006] [INFO] Epson机器人管理器初始化完成
[2025-09-24 10:01:59.040] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 10:02:01.254] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 10:02:01.254] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 10:02:01.254] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 10:02:01.254] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 10:02:03.696] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 10:02:03.699] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 10:02:03.699] [INFO] Epson机器人管理器初始化完成
[2025-09-24 10:02:03.701] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 10:02:44.874] [INFO] 开始连接控制端口 - IP: ************, 控制端口: 6000
[2025-09-24 10:02:44.875] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 10:02:44.875] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 6000
[2025-09-24 10:02:44.875] [INFO] 机器人配置已更新 - IP: ************, 控制端口: 6000, 数据端口: 6000
[2025-09-24 10:02:44.877] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-24 10:02:44.883] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-24 10:02:44.883] [INFO] 开始TCP连接到 ************:6000
[2025-09-24 10:02:54.896] [ERROR] TCP连接超时到 ************:6000，超时时间: 10000ms
[2025-09-24 10:02:54.897] [ERROR] 连接启动/停止TCP/IP 执行失败
异常详情: 控制端口连接超时: ************:6000
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<<ConnectStartStopAsync>b__71_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 256
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 10:03:09.935] [INFO] 开始连接控制端口 - IP: ************, 控制端口: 6000
[2025-09-24 10:03:09.935] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 10:03:09.935] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 10:03:09.935] [INFO] 机器人配置已更新 - IP: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 10:03:09.935] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-24 10:03:09.935] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-24 10:03:09.935] [INFO] 开始TCP连接到 ************:6000
[2025-09-24 10:03:09.963] [INFO] TCP连接成功到 ************:6000
[2025-09-24 10:03:09.963] [INFO] Epson机器人StartStop连接状态变更: Connected - 启动/停止TCP/IP连接成功
[2025-09-24 10:03:09.965] [INFO] 启动/停止TCP监听循环开始
[2025-09-24 10:03:09.966] [INFO] 连接状态变化: StartStop已连接 - 启动/停止TCP/IP连接成功
[2025-09-24 10:03:09.966] [INFO] 控制端口连接成功: ************:6000
[2025-09-24 10:03:09.967] [INFO] 控制端口连接成功，开始登录...
[2025-09-24 10:03:09.968] [INFO] 发送登录命令: $Login,EPSON
[2025-09-24 10:03:09.972] [INFO] 发送Epson机器人命令: $Login,EPSON (StartStop)
[2025-09-24 10:03:10.032] [INFO] Epson机器人登录成功
[2025-09-24 10:03:10.034] [INFO] 控制端口连接并登录成功，可以启动机器人
[2025-09-24 10:03:16.944] [INFO] 发送启动命令给机器人...
[2025-09-24 10:03:16.947] [INFO] 检查机器人状态，等待Auto为ON...
[2025-09-24 10:03:16.948] [INFO] 发送Epson机器人命令: $GetStatus (StartStop)
[2025-09-24 10:03:17.092] [INFO] 机器人Auto状态已就绪，可以执行Start命令
[2025-09-24 10:03:17.092] [INFO] 发送Epson机器人命令: $GetStatus (StartStop)
[2025-09-24 10:03:17.146] [INFO] 发送Epson机器人命令: $Start,0 (StartStop)
[2025-09-24 10:03:17.220] [INFO] Epson机器人启动成功
[2025-09-24 10:03:28.958] [INFO] 机器人启动结果: 成功 - 机器人启动成功
[2025-09-24 10:03:28.959] [INFO] 机器人启动成功，数据端口连接已启用
[2025-09-24 10:03:28.960] [ERROR] 数据收发TCP/IP未连接，无法启动自动化流程
[2025-09-24 10:03:33.496] [INFO] 开始连接数据端口 - IP: ************, 数据端口: 5000
[2025-09-24 10:03:33.498] [INFO] Epson机器人Data连接状态变更: Connecting - 正在连接数据收发TCP/IP...
[2025-09-24 10:03:33.498] [INFO] 连接状态变化: Data连接中... - 正在连接数据收发TCP/IP...
[2025-09-24 10:03:33.504] [INFO] Epson机器人Data连接状态变更: Connected - 数据收发TCP/IP连接成功
[2025-09-24 10:03:33.504] [INFO] 连接状态变化: Data已连接 - 数据收发TCP/IP连接成功
[2025-09-24 10:03:33.505] [INFO] 数据端口连接成功: ************:5000
[2025-09-24 10:03:33.506] [INFO] 数据端口连接成功，可以进行数据收发
[2025-09-24 10:03:33.506] [INFO] 数据收发TCP监听循环开始
[2025-09-24 10:03:45.106] [INFO] 扫描枪1默认配置: COM1, 115200, 8, One, None
[2025-09-24 10:03:45.106] [INFO] 扫描枪2默认配置: COM2, 115200, 8, One, None
[2025-09-24 10:03:45.106] [INFO] 扫描枪3默认配置: COM3, 115200, 8, One, None
[2025-09-24 10:03:45.106] [INFO] 扫描器控制面板获取MultiScannerManager实例成功
[2025-09-24 10:03:45.125] [INFO] 扫描枪1模块界面初始化完成
[2025-09-24 10:03:45.128] [INFO] 扫描枪2模块界面初始化完成
[2025-09-24 10:03:45.130] [INFO] 扫描枪3模块界面初始化完成
[2025-09-24 10:03:45.130] [INFO] 扫描器控制面板初始化完成
[2025-09-24 10:03:46.666] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 10:03:46.666] [INFO] Epson机器人管理器初始化完成
[2025-09-24 10:03:46.668] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 10:04:06.832] [INFO] 发送Epson机器人命令: $heelo (Data)
[2025-09-24 10:04:23.982] [INFO] 发送Epson机器人命令: $good (Data)
[2025-09-24 10:05:52.000] [INFO] 程序启动开始
[2025-09-24 10:05:52.002] [INFO] 配置系统初始化成功
[2025-09-24 10:05:52.123] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 10:05:52.123] [INFO] 配置系统初始化完成
[2025-09-24 10:05:52.124] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-24 10:05:52.173] [INFO] 开始初始化各个Manager...
[2025-09-24 10:05:52.174] [INFO] 初始化基础Manager...
[2025-09-24 10:05:52.187] [INFO] IO状态缓存初始化完成
[2025-09-24 10:05:52.199] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 10:05:52.199] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 10:05:52.203] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 10:05:52.203] [ERROR] DMC1000B控制卡初始化失败
[2025-09-24 10:05:52.204] [WARN] DMC1000BIO管理器初始化失败
[2025-09-24 10:05:52.210] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-24 10:05:52.210] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 10:05:52.210] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 10:05:52.211] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 10:05:52.264] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 10:05:52.286] [WARN] DMC1000B电机管理器初始化失败
[2025-09-24 10:05:52.286] [INFO] 初始化系统模式管理器...
[2025-09-24 10:05:52.291] [INFO] 开始初始化MotorManager...
[2025-09-24 10:05:52.293] [INFO] 模拟初始化运动控制卡
[2025-09-24 10:05:52.530] [INFO] 加载了8个电机的默认配置
[2025-09-24 10:05:52.532] [INFO] 电机监控任务已启动
[2025-09-24 10:05:52.533] [INFO] MotorManager初始化完成
[2025-09-24 10:05:52.533] [INFO] 初始化通信Manager...
[2025-09-24 10:05:52.534] [INFO] 电机监控循环开始
[2025-09-24 10:05:52.536] [INFO] 开始初始化ScannerManager...
[2025-09-24 10:05:52.538] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-24 10:05:52.545] [INFO] 串口初始化完成
[2025-09-24 10:05:52.547] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-24 10:05:52.552] [INFO] 扫描枪连接成功: COM1
[2025-09-24 10:05:52.552] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-24 10:05:52.552] [INFO] ScannerManager初始化完成
[2025-09-24 10:05:52.554] [INFO] 开始初始化ModbusTcpManager...
[2025-09-24 10:05:52.556] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-24 10:05:52.559] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-24 10:05:57.601] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 10:05:57.601] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-24 10:05:57.602] [INFO] ModbusTcpManager初始化完成
[2025-09-24 10:05:57.605] [INFO] 开始初始化EpsonRobotManager...
[2025-09-24 10:05:57.605] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-24 10:05:57.606] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-24 10:05:57.607] [INFO] EpsonRobotManager初始化完成
[2025-09-24 10:05:57.607] [INFO] 初始化视觉Manager...
[2025-09-24 10:05:57.609] [INFO] 开始初始化VisionManager...
[2025-09-24 10:05:57.610] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-24 10:05:57.611] [INFO] 模拟初始化相机，索引: 0
[2025-09-24 10:05:58.112] [INFO] 相机初始化成功
[2025-09-24 10:05:58.114] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-24 10:05:58.114] [INFO] 视觉配置加载完成
[2025-09-24 10:05:58.114] [INFO] VisionManager初始化完成
[2025-09-24 10:05:58.115] [INFO] 初始化数据Manager...
[2025-09-24 10:05:58.117] [INFO] 开始初始化StatisticsManager...
[2025-09-24 10:05:58.118] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-24 10:05:58.121] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-24 10:05:58.121] [INFO] 历史数据加载完成
[2025-09-24 10:05:58.122] [INFO] 自动保存任务已启动
[2025-09-24 10:05:58.122] [INFO] StatisticsManager初始化完成
[2025-09-24 10:05:58.122] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-24 10:05:58.122] [INFO] 所有Manager初始化完成
[2025-09-24 10:05:58.123] [INFO] 自动保存循环开始
[2025-09-24 10:05:58.166] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 10:05:58.167] [INFO] 主界面布局创建完成
[2025-09-24 10:05:58.168] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-24 10:05:59.713] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 10:05:59.714] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 10:05:59.719] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 10:05:59.719] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 10:06:00.113] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 10:06:00.119] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 10:06:00.119] [INFO] Epson机器人管理器初始化完成
[2025-09-24 10:06:00.134] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 10:06:00.573] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-24 10:06:01.322] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 10:06:01.323] [INFO] Epson机器人管理器初始化完成
[2025-09-24 10:06:01.325] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 10:06:02.163] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-24 10:06:10.527] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 10:06:10.527] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 10:06:10.527] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 10:06:10.527] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 10:06:12.001] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 10:06:12.004] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 10:06:12.005] [INFO] Epson机器人管理器初始化完成
[2025-09-24 10:06:12.007] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 10:26:01.139] [INFO] 开始连接控制端口 - IP: ************, 控制端口: 6000
[2025-09-24 10:26:01.140] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 10:26:01.140] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 10:26:01.141] [INFO] 机器人配置已更新 - IP: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 10:26:01.143] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-24 10:26:01.146] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-24 10:26:01.146] [INFO] 开始TCP连接到 ************:6000
[2025-09-24 10:26:01.192] [INFO] TCP连接成功到 ************:6000
[2025-09-24 10:26:01.193] [INFO] Epson机器人StartStop连接状态变更: Connected - 启动/停止TCP/IP连接成功
[2025-09-24 10:26:01.194] [INFO] 连接状态变化: StartStop已连接 - 启动/停止TCP/IP连接成功
[2025-09-24 10:26:01.195] [INFO] 控制端口连接成功: ************:6000
[2025-09-24 10:26:01.195] [INFO] 控制端口连接成功，开始登录...
[2025-09-24 10:26:01.197] [INFO] 启动/停止TCP监听循环开始
[2025-09-24 10:26:01.198] [INFO] 发送登录命令: $Login,EPSON
[2025-09-24 10:26:01.204] [INFO] 发送Epson机器人命令: $Login,EPSON (StartStop)
[2025-09-24 10:26:11.316] [ERROR] 发送Epson机器人命令: Login 执行失败
异常详情: 命令响应超时: $Login,EPSON
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<>c__DisplayClass81_0.<<SendCommandAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 707
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 10:26:11.317] [ERROR] Epson机器人登录失败: 发送命令失败
[2025-09-24 10:26:11.319] [ERROR] 机器人错误: LoginFailed - 发送命令失败 (StartStop)
[2025-09-24 10:26:46.627] [INFO] 启动/停止TCP监听循环结束
[2025-09-24 10:26:46.629] [INFO] Epson机器人StartStop连接状态变更: Disconnected - 启动/停止TCP/IP连接已断开
[2025-09-24 10:26:46.630] [INFO] 连接状态变化: StartStop已断开 - 启动/停止TCP/IP连接已断开
[2025-09-24 10:26:46.630] [INFO] 启动/停止TCP/IP连接已断开
[2025-09-24 10:26:48.368] [INFO] 开始连接控制端口 - IP: ************, 控制端口: 6000
[2025-09-24 10:26:48.368] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 10:26:48.369] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 10:26:48.369] [INFO] 机器人配置已更新 - IP: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 10:26:48.369] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-24 10:26:48.370] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-24 10:26:48.370] [INFO] 开始TCP连接到 ************:6000
[2025-09-24 10:26:48.379] [INFO] TCP连接成功到 ************:6000
[2025-09-24 10:26:48.379] [INFO] Epson机器人StartStop连接状态变更: Connected - 启动/停止TCP/IP连接成功
[2025-09-24 10:26:48.379] [INFO] 启动/停止TCP监听循环开始
[2025-09-24 10:26:48.381] [INFO] 连接状态变化: StartStop已连接 - 启动/停止TCP/IP连接成功
[2025-09-24 10:26:48.381] [INFO] 控制端口连接成功: ************:6000
[2025-09-24 10:26:48.381] [INFO] 控制端口连接成功，开始登录...
[2025-09-24 10:26:48.382] [INFO] 发送登录命令: $Login,EPSON
[2025-09-24 10:26:48.382] [INFO] 发送Epson机器人命令: $Login,EPSON (StartStop)
[2025-09-24 10:26:58.446] [ERROR] 发送Epson机器人命令: Login 执行失败
异常详情: 命令响应超时: $Login,EPSON
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<>c__DisplayClass81_0.<<SendCommandAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 707
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 10:26:58.446] [ERROR] Epson机器人登录失败: 发送命令失败
[2025-09-24 10:26:58.446] [ERROR] 机器人错误: LoginFailed - 发送命令失败 (StartStop)
[2025-09-24 10:27:00.125] [INFO] 启动/停止TCP监听循环结束
[2025-09-24 10:27:00.127] [INFO] Epson机器人StartStop连接状态变更: Disconnected - 启动/停止TCP/IP连接已断开
[2025-09-24 10:27:00.128] [INFO] 连接状态变化: StartStop已断开 - 启动/停止TCP/IP连接已断开
[2025-09-24 10:27:00.128] [INFO] 启动/停止TCP/IP连接已断开
[2025-09-24 10:40:19.179] [INFO] 程序启动开始
[2025-09-24 10:40:19.181] [INFO] 配置系统初始化成功
[2025-09-24 10:40:19.271] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 10:40:19.271] [INFO] 配置系统初始化完成
[2025-09-24 10:40:19.272] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-24 10:40:19.304] [INFO] 开始初始化各个Manager...
[2025-09-24 10:40:19.304] [INFO] 初始化基础Manager...
[2025-09-24 10:40:19.312] [INFO] IO状态缓存初始化完成
[2025-09-24 10:40:19.321] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 10:40:19.321] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 10:40:19.323] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 10:40:19.323] [ERROR] DMC1000B控制卡初始化失败
[2025-09-24 10:40:19.324] [WARN] DMC1000BIO管理器初始化失败
[2025-09-24 10:40:19.329] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-24 10:40:19.329] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 10:40:19.329] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 10:40:19.330] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 10:40:19.378] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 10:40:19.394] [WARN] DMC1000B电机管理器初始化失败
[2025-09-24 10:40:19.395] [INFO] 初始化系统模式管理器...
[2025-09-24 10:40:19.400] [INFO] 开始初始化MotorManager...
[2025-09-24 10:40:19.403] [INFO] 模拟初始化运动控制卡
[2025-09-24 10:40:19.620] [INFO] 加载了8个电机的默认配置
[2025-09-24 10:40:19.621] [INFO] 电机监控任务已启动
[2025-09-24 10:40:19.621] [INFO] MotorManager初始化完成
[2025-09-24 10:40:19.622] [INFO] 初始化通信Manager...
[2025-09-24 10:40:19.623] [INFO] 电机监控循环开始
[2025-09-24 10:40:19.625] [INFO] 开始初始化ScannerManager...
[2025-09-24 10:40:19.701] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-24 10:40:19.712] [INFO] 串口初始化完成
[2025-09-24 10:40:19.725] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-24 10:40:19.729] [INFO] 扫描枪连接成功: COM1
[2025-09-24 10:40:19.754] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-24 10:40:19.754] [INFO] ScannerManager初始化完成
[2025-09-24 10:40:19.757] [INFO] 开始初始化ModbusTcpManager...
[2025-09-24 10:40:19.759] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-24 10:40:19.763] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-24 10:40:24.806] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 10:40:24.807] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-24 10:40:24.808] [INFO] ModbusTcpManager初始化完成
[2025-09-24 10:40:24.811] [INFO] 开始初始化EpsonRobotManager...
[2025-09-24 10:40:24.811] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-24 10:40:24.813] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-24 10:40:24.813] [INFO] EpsonRobotManager初始化完成
[2025-09-24 10:40:24.813] [INFO] 初始化视觉Manager...
[2025-09-24 10:40:24.816] [INFO] 开始初始化VisionManager...
[2025-09-24 10:40:24.816] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-24 10:40:24.818] [INFO] 模拟初始化相机，索引: 0
[2025-09-24 10:40:25.322] [INFO] 相机初始化成功
[2025-09-24 10:40:25.323] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-24 10:40:25.323] [INFO] 视觉配置加载完成
[2025-09-24 10:40:25.324] [INFO] VisionManager初始化完成
[2025-09-24 10:40:25.324] [INFO] 初始化数据Manager...
[2025-09-24 10:40:25.327] [INFO] 开始初始化StatisticsManager...
[2025-09-24 10:40:25.327] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-24 10:40:25.331] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-24 10:40:25.332] [INFO] 历史数据加载完成
[2025-09-24 10:40:25.332] [INFO] 自动保存任务已启动
[2025-09-24 10:40:25.332] [INFO] StatisticsManager初始化完成
[2025-09-24 10:40:25.333] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-24 10:40:25.333] [INFO] 所有Manager初始化完成
[2025-09-24 10:40:25.334] [INFO] 自动保存循环开始
[2025-09-24 10:40:25.393] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 10:40:25.393] [INFO] 主界面布局创建完成
[2025-09-24 10:40:25.395] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-24 10:40:27.343] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 10:40:27.344] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 10:40:27.350] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 10:40:27.350] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 10:40:31.137] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 10:40:31.142] [INFO] 翻转电机示教面板业务逻辑初始化完成
[2025-09-24 10:40:31.156] [INFO] 翻转电机示教面板初始化完成 - 按HTML原型设计
[2025-09-24 10:40:33.376] [INFO] 翻转电机示教面板资源释放完成
[2025-09-24 10:40:33.410] [INFO] 皮带电机控制面板初始化完成 - 双电机设计
[2025-09-24 10:40:33.411] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 10:40:33.417] [INFO] 皮带电机轴3参数设置成功: 脉冲当量=0.01mm/pulse, 最大速度=100mm/s
[2025-09-24 10:40:33.418] [INFO] 皮带电机轴2参数设置成功: 脉冲当量=0.01mm/pulse, 最大速度=100mm/s
[2025-09-24 10:40:33.426] [INFO] 电机参数显示刷新完成
[2025-09-24 10:40:33.426] [INFO] 皮带电机参数初始化完成
[2025-09-24 10:40:33.427] [INFO] 皮带电机控制面板业务逻辑初始化完成
[2025-09-24 10:40:33.440] [INFO] 系统配置保存成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 10:40:33.440] [INFO] 皮带电机配置保存成功
[2025-09-24 10:40:33.441] [INFO] 系统配置保存成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 10:40:33.441] [INFO] 皮带电机配置保存成功
[2025-09-24 10:40:34.969] [INFO] 皮带电机控制面板资源释放完成
[2025-09-24 10:40:34.975] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 10:40:34.975] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 10:40:34.976] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 10:40:34.976] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 10:40:35.733] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 10:40:35.741] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 10:40:35.741] [INFO] Epson机器人管理器初始化完成
[2025-09-24 10:40:35.770] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 10:40:36.504] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-24 10:40:37.148] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 10:40:37.148] [INFO] Epson机器人管理器初始化完成
[2025-09-24 10:40:37.151] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 10:40:38.087] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-24 10:40:44.789] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 10:40:44.789] [INFO] Epson机器人管理器初始化完成
[2025-09-24 10:40:44.792] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 10:41:05.411] [INFO] 开始连接控制端口 - IP: ************, 控制端口: 6000
[2025-09-24 10:41:05.413] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 10:41:05.413] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 10:41:05.413] [INFO] 机器人配置已更新 - IP: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 10:41:05.418] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-24 10:41:05.422] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-24 10:41:05.422] [INFO] 开始TCP连接到 ************:6000
[2025-09-24 10:41:05.431] [INFO] TCP连接成功到 ************:6000
[2025-09-24 10:41:05.431] [INFO] Epson机器人StartStop连接状态变更: Connected - 启动/停止TCP/IP连接成功
[2025-09-24 10:41:05.433] [INFO] 连接状态变化: StartStop已连接 - 启动/停止TCP/IP连接成功
[2025-09-24 10:41:05.433] [INFO] 控制端口连接成功: ************:6000
[2025-09-24 10:41:05.433] [INFO] 控制端口连接成功，开始登录...
[2025-09-24 10:41:05.435] [INFO] 启动/停止TCP监听循环开始
[2025-09-24 10:41:05.436] [INFO] 发送登录命令: $Login,EPSON
[2025-09-24 10:41:05.441] [INFO] 发送Epson机器人命令: $Login,EPSON (StartStop)
[2025-09-24 10:41:15.536] [ERROR] 发送Epson机器人命令: Login 执行失败
异常详情: 命令响应超时: $Login,EPSON
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<>c__DisplayClass82_0.<<SendCommandAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 749
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 10:41:15.538] [ERROR] Epson机器人登录失败: 发送命令失败
[2025-09-24 10:41:15.540] [ERROR] 机器人错误: LoginFailed - 发送命令失败 (StartStop)
[2025-09-24 10:41:25.062] [INFO] 启动/停止TCP监听循环结束
[2025-09-24 10:41:25.064] [INFO] Epson机器人StartStop连接状态变更: Disconnected - 启动/停止TCP/IP连接已断开
[2025-09-24 10:41:25.065] [INFO] 连接状态变化: StartStop已断开 - 启动/停止TCP/IP连接已断开
[2025-09-24 10:41:25.065] [INFO] 启动/停止TCP/IP连接已断开
[2025-09-24 11:31:42.688] [INFO] 程序启动开始
[2025-09-24 11:31:42.690] [INFO] 配置系统初始化成功
[2025-09-24 11:31:43.161] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 11:31:43.162] [INFO] 配置系统初始化完成
[2025-09-24 11:31:43.163] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-24 11:31:43.284] [INFO] 开始初始化各个Manager...
[2025-09-24 11:31:43.297] [INFO] 初始化基础Manager...
[2025-09-24 11:31:43.326] [INFO] IO状态缓存初始化完成
[2025-09-24 11:31:43.343] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 11:31:43.344] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 11:31:43.499] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 11:31:43.500] [ERROR] DMC1000B控制卡初始化失败
[2025-09-24 11:31:43.500] [WARN] DMC1000BIO管理器初始化失败
[2025-09-24 11:31:43.505] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-24 11:31:43.506] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 11:31:43.506] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 11:31:43.507] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 11:31:43.563] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 11:31:43.585] [WARN] DMC1000B电机管理器初始化失败
[2025-09-24 11:31:43.585] [INFO] 初始化系统模式管理器...
[2025-09-24 11:31:43.591] [INFO] 开始初始化MotorManager...
[2025-09-24 11:31:43.594] [INFO] 模拟初始化运动控制卡
[2025-09-24 11:31:43.803] [INFO] 加载了8个电机的默认配置
[2025-09-24 11:31:43.804] [INFO] 电机监控任务已启动
[2025-09-24 11:31:43.804] [INFO] MotorManager初始化完成
[2025-09-24 11:31:43.805] [INFO] 初始化通信Manager...
[2025-09-24 11:31:43.806] [INFO] 电机监控循环开始
[2025-09-24 11:31:43.807] [INFO] 开始初始化ScannerManager...
[2025-09-24 11:31:43.810] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-24 11:31:43.823] [INFO] 串口初始化完成
[2025-09-24 11:31:43.826] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-24 11:31:43.831] [INFO] 扫描枪连接成功: COM1
[2025-09-24 11:31:43.831] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-24 11:31:43.831] [INFO] ScannerManager初始化完成
[2025-09-24 11:31:43.834] [INFO] 开始初始化ModbusTcpManager...
[2025-09-24 11:31:43.836] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-24 11:31:43.839] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-24 11:31:48.887] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 11:31:48.888] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-24 11:31:48.888] [INFO] ModbusTcpManager初始化完成
[2025-09-24 11:31:48.891] [INFO] 开始初始化EpsonRobotManager...
[2025-09-24 11:31:48.892] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-24 11:31:48.893] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-24 11:31:48.893] [INFO] EpsonRobotManager初始化完成
[2025-09-24 11:31:48.894] [INFO] 初始化视觉Manager...
[2025-09-24 11:31:48.896] [INFO] 开始初始化VisionManager...
[2025-09-24 11:31:48.896] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-24 11:31:48.898] [INFO] 模拟初始化相机，索引: 0
[2025-09-24 11:31:49.412] [INFO] 相机初始化成功
[2025-09-24 11:31:49.413] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-24 11:31:49.414] [INFO] 视觉配置加载完成
[2025-09-24 11:31:49.414] [INFO] VisionManager初始化完成
[2025-09-24 11:31:49.414] [INFO] 初始化数据Manager...
[2025-09-24 11:31:49.417] [INFO] 开始初始化StatisticsManager...
[2025-09-24 11:31:49.418] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-24 11:31:49.422] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-24 11:31:49.422] [INFO] 历史数据加载完成
[2025-09-24 11:31:49.422] [INFO] 自动保存任务已启动
[2025-09-24 11:31:49.423] [INFO] StatisticsManager初始化完成
[2025-09-24 11:31:49.423] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-24 11:31:49.424] [INFO] 所有Manager初始化完成
[2025-09-24 11:31:49.424] [INFO] 自动保存循环开始
[2025-09-24 11:31:49.479] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 11:31:49.479] [INFO] 主界面布局创建完成
[2025-09-24 11:31:49.481] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-24 11:31:53.707] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 11:31:53.708] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 11:31:53.714] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 11:31:53.714] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 11:31:54.335] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 11:31:54.342] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 11:31:54.342] [INFO] Epson机器人管理器初始化完成
[2025-09-24 11:31:54.375] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 11:32:17.967] [INFO] 开始连接控制端口 - IP: ************, 控制端口: 6000
[2025-09-24 11:32:17.969] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 11:32:17.969] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 11:32:17.969] [INFO] 机器人配置已更新 - IP: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 11:32:17.972] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-24 11:32:17.979] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-24 11:32:17.979] [INFO] 开始TCP连接到 ************:6000
[2025-09-24 11:32:17.983] [INFO] TCP连接成功到 ************:6000
[2025-09-24 11:32:17.984] [INFO] Epson机器人StartStop连接状态变更: Connected - 启动/停止TCP/IP连接成功
[2025-09-24 11:32:17.987] [INFO] 启动/停止TCP监听循环开始
[2025-09-24 11:32:17.989] [INFO] 连接状态变化: StartStop已连接 - 启动/停止TCP/IP连接成功
[2025-09-24 11:32:17.989] [INFO] 控制端口连接成功: ************:6000
[2025-09-24 11:32:17.989] [INFO] 控制端口连接成功，开始登录...
[2025-09-24 11:32:17.993] [INFO] 发送登录命令: $Login,EPSON
[2025-09-24 11:32:17.999] [INFO] 发送Epson机器人命令: $Login,EPSON (StartStop)
[2025-09-24 11:32:18.132] [INFO] Epson机器人登录成功
[2025-09-24 11:32:18.135] [INFO] 控制端口连接并登录成功，可以启动机器人
[2025-09-24 11:32:27.598] [INFO] 发送启动命令给机器人...
[2025-09-24 11:32:27.600] [INFO] 检查机器人状态，等待Auto为ON...
[2025-09-24 11:32:27.602] [INFO] 发送Epson机器人命令: $GetStatus (StartStop)
[2025-09-24 11:32:27.688] [INFO] 机器人Auto状态已就绪，可以执行Start命令
[2025-09-24 11:32:27.688] [INFO] 发送Epson机器人命令: $GetStatus (StartStop)
[2025-09-24 11:32:27.748] [INFO] 发送Epson机器人命令: $Start,0 (StartStop)
[2025-09-24 11:32:27.872] [INFO] Epson机器人启动成功
[2025-09-24 11:32:29.810] [INFO] 机器人启动结果: 成功 - 机器人启动成功
[2025-09-24 11:32:29.812] [INFO] 机器人启动成功，数据端口连接已启用
[2025-09-24 11:32:29.813] [ERROR] 数据收发TCP/IP未连接，无法启动自动化流程
[2025-09-24 11:32:38.237] [INFO] 开始连接数据端口 - IP: ************, 数据端口: 5000
[2025-09-24 11:32:38.240] [INFO] Epson机器人Data连接状态变更: Connecting - 正在连接数据收发TCP/IP...
[2025-09-24 11:32:38.241] [INFO] 连接状态变化: Data连接中... - 正在连接数据收发TCP/IP...
[2025-09-24 11:32:38.247] [INFO] Epson机器人Data连接状态变更: Connected - 数据收发TCP/IP连接成功
[2025-09-24 11:32:38.248] [INFO] 连接状态变化: Data已连接 - 数据收发TCP/IP连接成功
[2025-09-24 11:32:38.249] [INFO] 数据端口连接成功: ************:5000
[2025-09-24 11:32:38.250] [INFO] 数据收发TCP监听循环开始
[2025-09-24 11:32:38.251] [INFO] 数据端口连接成功，可以进行数据收发
[2025-09-24 11:33:13.879] [INFO] 发送Epson机器人命令: $asd (Data)
[2025-09-24 14:33:43.904] [INFO] 程序启动开始
[2025-09-24 14:33:43.906] [INFO] 配置系统初始化成功
[2025-09-24 14:33:44.154] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 14:33:44.154] [INFO] 配置系统初始化完成
[2025-09-24 14:33:44.155] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-24 14:33:44.184] [INFO] 开始初始化各个Manager...
[2025-09-24 14:33:44.185] [INFO] 初始化基础Manager...
[2025-09-24 14:33:44.194] [INFO] IO状态缓存初始化完成
[2025-09-24 14:33:44.202] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 14:33:44.202] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 14:33:44.296] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 14:33:44.297] [ERROR] DMC1000B控制卡初始化失败
[2025-09-24 14:33:44.297] [WARN] DMC1000BIO管理器初始化失败
[2025-09-24 14:33:44.302] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-24 14:33:44.303] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 14:33:44.303] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 14:33:44.304] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 14:33:44.342] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 14:33:44.356] [WARN] DMC1000B电机管理器初始化失败
[2025-09-24 14:33:44.356] [INFO] 初始化系统模式管理器...
[2025-09-24 14:33:44.361] [INFO] 开始初始化MotorManager...
[2025-09-24 14:33:44.364] [INFO] 模拟初始化运动控制卡
[2025-09-24 14:33:44.576] [INFO] 加载了8个电机的默认配置
[2025-09-24 14:33:44.577] [INFO] 电机监控任务已启动
[2025-09-24 14:33:44.577] [INFO] MotorManager初始化完成
[2025-09-24 14:33:44.578] [INFO] 初始化通信Manager...
[2025-09-24 14:33:44.580] [INFO] 电机监控循环开始
[2025-09-24 14:33:44.581] [INFO] 开始初始化ScannerManager...
[2025-09-24 14:33:44.584] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-24 14:33:44.586] [INFO] 串口初始化完成
[2025-09-24 14:33:44.591] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-24 14:33:44.596] [INFO] 扫描枪连接成功: COM1
[2025-09-24 14:33:44.596] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-24 14:33:44.597] [INFO] ScannerManager初始化完成
[2025-09-24 14:33:44.599] [INFO] 开始初始化ModbusTcpManager...
[2025-09-24 14:33:44.601] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-24 14:33:44.605] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-24 14:33:49.656] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 14:33:49.657] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-24 14:33:49.657] [INFO] ModbusTcpManager初始化完成
[2025-09-24 14:33:49.660] [INFO] 开始初始化EpsonRobotManager...
[2025-09-24 14:33:49.660] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-24 14:33:49.662] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-24 14:33:49.662] [INFO] EpsonRobotManager初始化完成
[2025-09-24 14:33:49.663] [INFO] 初始化视觉Manager...
[2025-09-24 14:33:49.665] [INFO] 开始初始化VisionManager...
[2025-09-24 14:33:49.666] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-24 14:33:49.667] [INFO] 模拟初始化相机，索引: 0
[2025-09-24 14:33:50.183] [INFO] 相机初始化成功
[2025-09-24 14:33:50.185] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-24 14:33:50.185] [INFO] 视觉配置加载完成
[2025-09-24 14:33:50.185] [INFO] VisionManager初始化完成
[2025-09-24 14:33:50.186] [INFO] 初始化数据Manager...
[2025-09-24 14:33:50.189] [INFO] 开始初始化StatisticsManager...
[2025-09-24 14:33:50.190] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-24 14:33:50.195] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-24 14:33:50.195] [INFO] 历史数据加载完成
[2025-09-24 14:33:50.196] [INFO] 自动保存任务已启动
[2025-09-24 14:33:50.196] [INFO] StatisticsManager初始化完成
[2025-09-24 14:33:50.197] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-24 14:33:50.197] [INFO] 自动保存循环开始
[2025-09-24 14:33:50.198] [INFO] 所有Manager初始化完成
[2025-09-24 14:33:50.244] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 14:33:50.244] [INFO] 主界面布局创建完成
[2025-09-24 14:33:50.247] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-24 14:33:52.632] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 14:33:52.633] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 14:33:52.639] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 14:33:52.640] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 14:33:54.523] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 14:33:54.528] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 14:33:55.214] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 14:33:55.214] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 14:33:55.215] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 14:33:55.215] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 14:33:56.127] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 14:33:56.133] [INFO] 翻转电机示教面板业务逻辑初始化完成
[2025-09-24 14:33:56.143] [INFO] 翻转电机示教面板初始化完成 - 按HTML原型设计
[2025-09-24 14:33:56.783] [INFO] 翻转电机示教面板资源释放完成
[2025-09-24 14:33:56.794] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 14:33:56.794] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 14:33:56.795] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 14:33:56.795] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 14:33:58.378] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 14:33:58.386] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 14:33:58.386] [INFO] Epson机器人管理器初始化完成
[2025-09-24 14:33:58.403] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 14:34:31.434] [INFO] 开始连接控制端口 - IP: ************, 控制端口: 6000
[2025-09-24 14:34:31.437] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 14:34:31.437] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 14:34:31.437] [INFO] 机器人配置已更新 - IP: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 14:34:31.440] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-24 14:34:31.443] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-24 14:34:31.443] [INFO] 开始TCP连接到 ************:6000
[2025-09-24 14:34:31.479] [INFO] TCP连接成功到 ************:6000
[2025-09-24 14:34:31.480] [INFO] Epson机器人StartStop连接状态变更: Connected - 启动/停止TCP/IP连接成功
[2025-09-24 14:34:31.481] [INFO] 连接状态变化: StartStop已连接 - 启动/停止TCP/IP连接成功
[2025-09-24 14:34:31.482] [INFO] 控制端口连接成功: ************:6000
[2025-09-24 14:34:31.482] [INFO] 控制端口连接成功，开始登录...
[2025-09-24 14:34:31.483] [INFO] 启动/停止TCP监听循环开始
[2025-09-24 14:34:31.485] [INFO] 发送登录命令: $Login,EPSON
[2025-09-24 14:34:31.489] [INFO] 发送Epson机器人命令: $Login,EPSON (StartStop)
[2025-09-24 14:34:31.611] [INFO] Epson机器人登录成功
[2025-09-24 14:34:31.614] [INFO] 控制端口连接并登录成功，可以启动机器人
[2025-09-24 14:34:41.760] [INFO] 发送启动命令给机器人...
[2025-09-24 14:34:41.763] [INFO] 检查机器人状态，等待Auto为ON...
[2025-09-24 14:34:41.765] [INFO] 发送Epson机器人命令: $GetStatus (StartStop)
[2025-09-24 14:34:41.885] [INFO] 机器人Auto状态已就绪，可以执行Start命令
[2025-09-24 14:34:41.886] [INFO] 发送Epson机器人命令: $GetStatus (StartStop)
[2025-09-24 14:34:42.021] [INFO] 发送Epson机器人命令: $Start,0 (StartStop)
[2025-09-24 14:34:42.082] [INFO] Epson机器人启动成功
[2025-09-24 14:34:43.614] [INFO] 机器人启动结果: 成功 - 机器人启动成功
[2025-09-24 14:34:43.615] [INFO] 机器人启动成功，数据端口连接已启用
[2025-09-24 14:34:43.616] [ERROR] 数据收发TCP/IP未连接，无法启动自动化流程
[2025-09-24 14:34:51.145] [INFO] 开始连接数据端口 - IP: ************, 数据端口: 5000
[2025-09-24 14:34:51.147] [INFO] Epson机器人Data连接状态变更: Connecting - 正在连接数据收发TCP/IP...
[2025-09-24 14:34:51.148] [INFO] 连接状态变化: Data连接中... - 正在连接数据收发TCP/IP...
[2025-09-24 14:34:51.155] [INFO] Epson机器人Data连接状态变更: Connected - 数据收发TCP/IP连接成功
[2025-09-24 14:34:51.156] [INFO] 连接状态变化: Data已连接 - 数据收发TCP/IP连接成功
[2025-09-24 14:34:51.156] [INFO] 数据端口连接成功: ************:5000
[2025-09-24 14:34:51.158] [INFO] 数据收发TCP监听循环开始
[2025-09-24 14:34:51.158] [INFO] 数据端口连接成功，可以进行数据收发
[2025-09-24 14:35:10.405] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-24 14:35:11.184] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
[2025-09-24 14:35:11.927] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-24 14:35:12.588] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 14:35:12.588] [INFO] Epson机器人管理器初始化完成
[2025-09-24 14:35:12.592] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 14:35:14.315] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 14:35:14.315] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 14:35:14.330] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 14:35:14.330] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 14:35:15.324] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 14:35:15.330] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 14:35:16.083] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 14:35:16.083] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 14:35:16.084] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 14:35:16.084] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 14:35:16.605] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 14:35:16.610] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 14:35:16.611] [INFO] Epson机器人管理器初始化完成
[2025-09-24 14:35:16.623] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 14:35:17.105] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-24 14:35:17.200] [ERROR] IO管理器初始化超时，无法启动IO监控
[2025-09-24 14:35:17.608] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
[2025-09-24 14:35:19.081] [INFO] 生产日志管理面板初始化完成 - 按HTML原型设计
[2025-09-24 14:35:23.091] [ERROR] IO管理器初始化超时，无法启动IO监控
[2025-09-24 14:35:29.589] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 14:35:29.589] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 14:35:29.590] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 14:35:29.590] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 14:36:24.297] [INFO] 开始切换到自动模式...
[2025-09-24 14:36:24.298] [ERROR] DMC1000B控制卡未初始化或不可用
[2025-09-24 14:36:24.298] [ERROR] 系统未准备好进入自动模式
[2025-09-24 14:36:28.559] [INFO] 开始切换到自动模式...
[2025-09-24 14:36:28.559] [ERROR] DMC1000B控制卡未初始化或不可用
[2025-09-24 14:36:28.559] [ERROR] 系统未准备好进入自动模式
[2025-09-24 14:36:31.141] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 14:36:31.147] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 14:36:32.389] [INFO] 开始切换到自动模式...
[2025-09-24 14:36:32.390] [ERROR] DMC1000B控制卡未初始化或不可用
[2025-09-24 14:36:32.390] [ERROR] 系统未准备好进入自动模式
[2025-09-24 15:01:11.305] [INFO] 程序启动开始
[2025-09-24 15:01:11.307] [INFO] 配置系统初始化成功
[2025-09-24 15:01:11.383] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 15:01:11.384] [INFO] 配置系统初始化完成
[2025-09-24 15:01:11.384] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-24 15:01:11.429] [INFO] 开始初始化各个Manager...
[2025-09-24 15:01:11.429] [INFO] 初始化基础Manager...
[2025-09-24 15:01:11.437] [INFO] IO状态缓存初始化完成
[2025-09-24 15:01:11.445] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 15:01:11.445] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 15:01:11.447] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 15:01:11.448] [ERROR] DMC1000B控制卡初始化失败
[2025-09-24 15:01:11.448] [WARN] DMC1000BIO管理器初始化失败
[2025-09-24 15:01:11.453] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-24 15:01:11.454] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 15:01:11.454] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 15:01:11.455] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 15:01:11.506] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 15:01:11.525] [WARN] DMC1000B电机管理器初始化失败
[2025-09-24 15:01:11.526] [INFO] 初始化系统模式管理器...
[2025-09-24 15:01:11.531] [INFO] 开始初始化MotorManager...
[2025-09-24 15:01:11.534] [INFO] 模拟初始化运动控制卡
[2025-09-24 15:01:11.746] [INFO] 加载了8个电机的默认配置
[2025-09-24 15:01:11.747] [INFO] 电机监控任务已启动
[2025-09-24 15:01:11.747] [INFO] MotorManager初始化完成
[2025-09-24 15:01:11.747] [INFO] 初始化通信Manager...
[2025-09-24 15:01:11.750] [INFO] 开始初始化ScannerManager...
[2025-09-24 15:01:11.752] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-24 15:01:11.816] [INFO] 串口初始化完成
[2025-09-24 15:01:11.817] [INFO] 电机监控循环开始
[2025-09-24 15:01:11.819] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-24 15:01:11.995] [INFO] 扫描枪连接成功: COM1
[2025-09-24 15:01:12.020] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-24 15:01:12.021] [INFO] ScannerManager初始化完成
[2025-09-24 15:01:12.058] [INFO] 开始初始化ModbusTcpManager...
[2025-09-24 15:01:12.060] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-24 15:01:12.064] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-24 15:01:17.110] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 15:01:17.115] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-24 15:01:17.115] [INFO] ModbusTcpManager初始化完成
[2025-09-24 15:01:17.119] [INFO] 开始初始化EpsonRobotManager...
[2025-09-24 15:01:17.120] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-24 15:01:17.122] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-24 15:01:17.122] [INFO] EpsonRobotManager初始化完成
[2025-09-24 15:01:17.122] [INFO] 初始化视觉Manager...
[2025-09-24 15:01:17.125] [INFO] 开始初始化VisionManager...
[2025-09-24 15:01:17.125] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-24 15:01:17.126] [INFO] 模拟初始化相机，索引: 0
[2025-09-24 15:01:17.638] [INFO] 相机初始化成功
[2025-09-24 15:01:17.639] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-24 15:01:17.640] [INFO] 视觉配置加载完成
[2025-09-24 15:01:17.640] [INFO] VisionManager初始化完成
[2025-09-24 15:01:17.640] [INFO] 初始化数据Manager...
[2025-09-24 15:01:17.643] [INFO] 开始初始化StatisticsManager...
[2025-09-24 15:01:17.644] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-24 15:01:17.648] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-24 15:01:17.648] [INFO] 历史数据加载完成
[2025-09-24 15:01:17.648] [INFO] 自动保存任务已启动
[2025-09-24 15:01:17.649] [INFO] StatisticsManager初始化完成
[2025-09-24 15:01:17.649] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-24 15:01:17.649] [INFO] 所有Manager初始化完成
[2025-09-24 15:01:17.650] [INFO] 自动保存循环开始
[2025-09-24 15:01:17.687] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:01:17.687] [INFO] 主界面布局创建完成
[2025-09-24 15:01:17.689] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-24 15:01:19.946] [INFO] 对位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:01:20.602] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:01:21.468] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:01:21.469] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 15:01:21.474] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 15:01:21.474] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 15:01:21.925] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 15:01:21.932] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 15:01:21.933] [INFO] Epson机器人管理器初始化完成
[2025-09-24 15:01:21.946] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:01:23.177] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 15:01:23.177] [INFO] Epson机器人管理器初始化完成
[2025-09-24 15:01:23.181] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:01:23.592] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:01:23.592] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 15:01:23.592] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 15:01:23.592] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 15:01:24.389] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 15:01:24.394] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 15:01:24.395] [INFO] Epson机器人管理器初始化完成
[2025-09-24 15:01:24.397] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:06:16.348] [INFO] 开始连接控制端口 - IP: ************, 控制端口: 6000
[2025-09-24 15:06:16.350] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 15:06:16.350] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 15:06:16.350] [INFO] 机器人配置已更新 - IP: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 15:06:16.353] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-24 15:06:16.355] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-24 15:06:16.355] [INFO] 开始TCP连接到 ************:6000
[2025-09-24 15:06:16.365] [INFO] TCP连接成功到 ************:6000
[2025-09-24 15:06:16.366] [INFO] Epson机器人StartStop连接状态变更: Connected - 启动/停止TCP/IP连接成功
[2025-09-24 15:06:16.367] [INFO] 连接状态变化: StartStop已连接 - 启动/停止TCP/IP连接成功
[2025-09-24 15:06:16.367] [INFO] 控制端口连接成功: ************:6000
[2025-09-24 15:06:16.367] [INFO] 控制端口连接成功，开始登录...
[2025-09-24 15:06:16.369] [INFO] 启动/停止TCP监听循环开始
[2025-09-24 15:06:16.370] [INFO] 发送登录命令: $Login,EPSON
[2025-09-24 15:06:16.375] [INFO] 发送Epson机器人命令: $Login,EPSON (StartStop)
[2025-09-24 15:06:16.428] [INFO] Epson机器人登录成功
[2025-09-24 15:06:16.431] [INFO] 控制端口连接并登录成功，可以启动机器人
[2025-09-24 15:06:21.642] [INFO] 发送启动命令给机器人...
[2025-09-24 15:06:21.645] [INFO] 检查机器人状态，等待Auto为ON...
[2025-09-24 15:06:21.646] [INFO] 发送Epson机器人命令: $GetStatus (StartStop)
[2025-09-24 15:06:21.744] [INFO] 机器人Auto状态已就绪，可以执行Start命令
[2025-09-24 15:06:21.744] [INFO] 发送Epson机器人命令: $GetStatus (StartStop)
[2025-09-24 15:06:21.803] [INFO] 发送Epson机器人命令: $Start,0 (StartStop)
[2025-09-24 15:06:21.881] [INFO] Epson机器人启动成功
[2025-09-24 15:06:24.663] [INFO] 机器人启动结果: 成功 - 机器人启动成功
[2025-09-24 15:06:24.665] [INFO] 机器人启动成功，数据端口连接已启用
[2025-09-24 15:06:24.665] [ERROR] 数据收发TCP/IP未连接，无法启动自动化流程
[2025-09-24 15:06:28.235] [INFO] 开始连接数据端口 - IP: ************, 数据端口: 5000
[2025-09-24 15:06:28.238] [INFO] Epson机器人Data连接状态变更: Connecting - 正在连接数据收发TCP/IP...
[2025-09-24 15:06:28.239] [INFO] 连接状态变化: Data连接中... - 正在连接数据收发TCP/IP...
[2025-09-24 15:06:28.245] [INFO] Epson机器人Data连接状态变更: Connected - 数据收发TCP/IP连接成功
[2025-09-24 15:06:28.247] [INFO] 连接状态变化: Data已连接 - 数据收发TCP/IP连接成功
[2025-09-24 15:06:28.247] [INFO] 数据端口连接成功: ************:5000
[2025-09-24 15:06:28.249] [INFO] 数据收发TCP监听循环开始
[2025-09-24 15:06:28.250] [INFO] 数据端口连接成功，可以进行数据收发
[2025-09-24 15:07:11.267] [INFO] 发送Epson机器人命令: $大苏打 (Data)
[2025-09-24 15:09:22.157] [INFO] 程序启动开始
[2025-09-24 15:09:22.159] [INFO] 配置系统初始化成功
[2025-09-24 15:09:22.224] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 15:09:22.224] [INFO] 配置系统初始化完成
[2025-09-24 15:09:22.225] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-24 15:09:22.249] [INFO] 开始初始化各个Manager...
[2025-09-24 15:09:22.250] [INFO] 初始化基础Manager...
[2025-09-24 15:09:22.256] [INFO] IO状态缓存初始化完成
[2025-09-24 15:09:22.262] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 15:09:22.262] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 15:09:22.264] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 15:09:22.264] [ERROR] DMC1000B控制卡初始化失败
[2025-09-24 15:09:22.264] [WARN] DMC1000BIO管理器初始化失败
[2025-09-24 15:09:22.268] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-24 15:09:22.268] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 15:09:22.268] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 15:09:22.269] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 15:09:22.324] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 15:09:22.338] [WARN] DMC1000B电机管理器初始化失败
[2025-09-24 15:09:22.338] [INFO] 初始化系统模式管理器...
[2025-09-24 15:09:22.344] [INFO] 开始初始化MotorManager...
[2025-09-24 15:09:22.347] [INFO] 模拟初始化运动控制卡
[2025-09-24 15:09:22.562] [INFO] 加载了8个电机的默认配置
[2025-09-24 15:09:22.563] [INFO] 电机监控任务已启动
[2025-09-24 15:09:22.563] [INFO] MotorManager初始化完成
[2025-09-24 15:09:22.563] [INFO] 初始化通信Manager...
[2025-09-24 15:09:22.564] [INFO] 电机监控循环开始
[2025-09-24 15:09:22.567] [INFO] 开始初始化ScannerManager...
[2025-09-24 15:09:22.570] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-24 15:09:22.572] [INFO] 串口初始化完成
[2025-09-24 15:09:22.573] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-24 15:09:22.577] [INFO] 扫描枪连接成功: COM1
[2025-09-24 15:09:22.578] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-24 15:09:22.578] [INFO] ScannerManager初始化完成
[2025-09-24 15:09:22.580] [INFO] 开始初始化ModbusTcpManager...
[2025-09-24 15:09:22.582] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-24 15:09:22.584] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-24 15:09:27.624] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 15:09:27.626] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-24 15:09:27.626] [INFO] ModbusTcpManager初始化完成
[2025-09-24 15:09:27.629] [INFO] 开始初始化EpsonRobotManager...
[2025-09-24 15:09:27.629] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-24 15:09:27.631] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-24 15:09:27.631] [INFO] EpsonRobotManager初始化完成
[2025-09-24 15:09:27.631] [INFO] 初始化视觉Manager...
[2025-09-24 15:09:27.633] [INFO] 开始初始化VisionManager...
[2025-09-24 15:09:27.633] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-24 15:09:27.635] [INFO] 模拟初始化相机，索引: 0
[2025-09-24 15:09:28.150] [INFO] 相机初始化成功
[2025-09-24 15:09:28.151] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-24 15:09:28.152] [INFO] 视觉配置加载完成
[2025-09-24 15:09:28.152] [INFO] VisionManager初始化完成
[2025-09-24 15:09:28.152] [INFO] 初始化数据Manager...
[2025-09-24 15:09:28.156] [INFO] 开始初始化StatisticsManager...
[2025-09-24 15:09:28.157] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-24 15:09:28.161] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-24 15:09:28.161] [INFO] 历史数据加载完成
[2025-09-24 15:09:28.161] [INFO] 自动保存任务已启动
[2025-09-24 15:09:28.162] [INFO] StatisticsManager初始化完成
[2025-09-24 15:09:28.162] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-24 15:09:28.162] [INFO] 所有Manager初始化完成
[2025-09-24 15:09:28.163] [INFO] 自动保存循环开始
[2025-09-24 15:09:28.204] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:09:28.205] [INFO] 主界面布局创建完成
[2025-09-24 15:09:28.206] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-24 15:10:00.903] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 15:10:00.903] [INFO] Epson机器人管理器初始化完成
[2025-09-24 15:10:00.918] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:12:30.289] [INFO] 扫描枪1默认配置: COM1, 115200, 8, One, None
[2025-09-24 15:12:30.290] [INFO] 扫描枪2默认配置: COM2, 115200, 8, One, None
[2025-09-24 15:12:30.290] [INFO] 扫描枪3默认配置: COM3, 115200, 8, One, None
[2025-09-24 15:12:30.290] [INFO] 扫描器控制面板获取MultiScannerManager实例成功
[2025-09-24 15:12:30.309] [INFO] 扫描枪1模块界面初始化完成
[2025-09-24 15:12:30.312] [INFO] 扫描枪2模块界面初始化完成
[2025-09-24 15:12:30.316] [INFO] 扫描枪3模块界面初始化完成
[2025-09-24 15:12:30.316] [INFO] 扫描器控制面板初始化完成
[2025-09-24 15:12:30.856] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 15:12:30.856] [INFO] Epson机器人管理器初始化完成
[2025-09-24 15:12:30.861] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:12:32.277] [INFO] 扫描器控制面板获取MultiScannerManager实例成功
[2025-09-24 15:12:32.281] [INFO] 扫描枪1模块界面初始化完成
[2025-09-24 15:12:32.284] [INFO] 扫描枪2模块界面初始化完成
[2025-09-24 15:12:32.286] [INFO] 扫描枪3模块界面初始化完成
[2025-09-24 15:12:32.286] [INFO] 扫描器控制面板初始化完成
[2025-09-24 15:12:33.070] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 15:12:33.070] [INFO] Epson机器人管理器初始化完成
[2025-09-24 15:12:33.073] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:18:51.576] [INFO] 程序启动开始
[2025-09-24 15:18:51.579] [INFO] 配置系统初始化成功
[2025-09-24 15:18:51.802] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 15:18:51.803] [INFO] 配置系统初始化完成
[2025-09-24 15:18:51.803] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-24 15:18:51.831] [INFO] 开始初始化各个Manager...
[2025-09-24 15:18:51.831] [INFO] 初始化基础Manager...
[2025-09-24 15:18:51.837] [INFO] IO状态缓存初始化完成
[2025-09-24 15:18:51.845] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 15:18:51.845] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 15:18:51.847] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 15:18:51.847] [ERROR] DMC1000B控制卡初始化失败
[2025-09-24 15:18:51.847] [WARN] DMC1000BIO管理器初始化失败
[2025-09-24 15:18:51.851] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-24 15:18:51.851] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 15:18:51.851] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 15:18:51.852] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 15:18:51.897] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 15:18:51.911] [WARN] DMC1000B电机管理器初始化失败
[2025-09-24 15:18:51.911] [INFO] 初始化系统模式管理器...
[2025-09-24 15:18:51.916] [INFO] 开始初始化MotorManager...
[2025-09-24 15:18:51.919] [INFO] 模拟初始化运动控制卡
[2025-09-24 15:18:52.140] [INFO] 加载了8个电机的默认配置
[2025-09-24 15:18:52.141] [INFO] 电机监控任务已启动
[2025-09-24 15:18:52.141] [INFO] MotorManager初始化完成
[2025-09-24 15:18:52.141] [INFO] 初始化通信Manager...
[2025-09-24 15:18:52.143] [INFO] 电机监控循环开始
[2025-09-24 15:18:52.144] [INFO] 开始初始化ScannerManager...
[2025-09-24 15:18:52.145] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-24 15:18:52.149] [INFO] 串口初始化完成
[2025-09-24 15:18:52.194] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-24 15:18:52.253] [INFO] 扫描枪连接成功: COM1
[2025-09-24 15:18:52.254] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-24 15:18:52.254] [INFO] ScannerManager初始化完成
[2025-09-24 15:18:52.257] [INFO] 开始初始化ModbusTcpManager...
[2025-09-24 15:18:52.258] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-24 15:18:52.262] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-24 15:18:57.319] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 15:18:57.319] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-24 15:18:57.320] [INFO] ModbusTcpManager初始化完成
[2025-09-24 15:18:57.323] [INFO] 开始初始化EpsonRobotManager...
[2025-09-24 15:18:57.323] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-24 15:18:57.325] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-24 15:18:57.325] [INFO] EpsonRobotManager初始化完成
[2025-09-24 15:18:57.325] [INFO] 初始化视觉Manager...
[2025-09-24 15:18:57.328] [INFO] 开始初始化VisionManager...
[2025-09-24 15:18:57.328] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-24 15:18:57.330] [INFO] 模拟初始化相机，索引: 0
[2025-09-24 15:18:57.843] [INFO] 相机初始化成功
[2025-09-24 15:18:57.844] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-24 15:18:57.848] [INFO] 视觉配置加载完成
[2025-09-24 15:18:57.848] [INFO] VisionManager初始化完成
[2025-09-24 15:18:57.849] [INFO] 初始化数据Manager...
[2025-09-24 15:18:57.852] [INFO] 开始初始化StatisticsManager...
[2025-09-24 15:18:57.853] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-24 15:18:57.856] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-24 15:18:57.856] [INFO] 历史数据加载完成
[2025-09-24 15:18:57.857] [INFO] 自动保存任务已启动
[2025-09-24 15:18:57.857] [INFO] StatisticsManager初始化完成
[2025-09-24 15:18:57.857] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-24 15:18:57.857] [INFO] 所有Manager初始化完成
[2025-09-24 15:18:57.858] [INFO] 自动保存循环开始
[2025-09-24 15:18:57.894] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:18:57.894] [INFO] 主界面布局创建完成
[2025-09-24 15:18:57.896] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-24 15:18:59.677] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 15:18:59.677] [INFO] Epson机器人管理器初始化完成
[2025-09-24 15:18:59.690] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:19:36.029] [INFO] 开始连接控制端口 - IP: ************, 控制端口: 6000
[2025-09-24 15:19:36.031] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 15:19:36.031] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 15:19:36.031] [INFO] 机器人配置已更新 - IP: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 15:19:36.034] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-24 15:19:36.036] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-24 15:19:36.037] [INFO] 开始TCP连接到 ************:6000
[2025-09-24 15:19:36.090] [INFO] TCP连接成功到 ************:6000
[2025-09-24 15:19:36.091] [INFO] Epson机器人StartStop连接状态变更: Connected - 启动/停止TCP/IP连接成功
[2025-09-24 15:19:36.092] [INFO] 连接状态变化: StartStop已连接 - 启动/停止TCP/IP连接成功
[2025-09-24 15:19:36.092] [INFO] 控制端口连接成功: ************:6000
[2025-09-24 15:19:36.092] [INFO] 控制端口连接成功，开始登录...
[2025-09-24 15:19:36.094] [INFO] 启动/停止TCP监听循环开始
[2025-09-24 15:19:36.095] [INFO] 发送登录命令: $Login,EPSON
[2025-09-24 15:19:36.099] [INFO] 发送Epson机器人命令: $Login,EPSON (StartStop)
[2025-09-24 15:19:36.159] [INFO] Epson机器人登录成功
[2025-09-24 15:19:36.162] [INFO] 控制端口连接并登录成功，可以启动机器人
[2025-09-24 15:19:38.907] [INFO] 发送启动命令给机器人...
[2025-09-24 15:19:38.910] [INFO] 检查机器人状态，等待Auto为ON...
[2025-09-24 15:19:38.911] [INFO] 发送Epson机器人命令: $GetStatus (StartStop)
[2025-09-24 15:19:38.976] [INFO] 机器人Auto状态已就绪，可以执行Start命令
[2025-09-24 15:19:38.976] [INFO] 发送Epson机器人命令: $GetStatus (StartStop)
[2025-09-24 15:19:39.100] [INFO] 发送Epson机器人命令: $Start,0 (StartStop)
[2025-09-24 15:19:39.226] [INFO] Epson机器人启动成功
[2025-09-24 15:19:40.376] [INFO] 机器人启动结果: 成功 - 机器人启动成功
[2025-09-24 15:19:40.378] [INFO] 机器人启动成功，数据端口连接已启用
[2025-09-24 15:19:40.379] [ERROR] 数据收发TCP/IP未连接，无法启动自动化流程
[2025-09-24 15:19:42.339] [INFO] 开始连接数据端口 - IP: ************, 数据端口: 5000
[2025-09-24 15:19:42.342] [INFO] Epson机器人Data连接状态变更: Connecting - 正在连接数据收发TCP/IP...
[2025-09-24 15:19:42.343] [INFO] 连接状态变化: Data连接中... - 正在连接数据收发TCP/IP...
[2025-09-24 15:19:42.348] [INFO] Epson机器人Data连接状态变更: Connected - 数据收发TCP/IP连接成功
[2025-09-24 15:19:42.349] [INFO] 连接状态变化: Data已连接 - 数据收发TCP/IP连接成功
[2025-09-24 15:19:42.349] [INFO] 数据端口连接成功: ************:5000
[2025-09-24 15:19:42.351] [INFO] 数据端口连接成功，可以进行数据收发
[2025-09-24 15:19:42.351] [INFO] 数据收发TCP监听循环开始
[2025-09-24 15:19:49.772] [INFO] 发送Epson机器人命令: $jjj (Data)
[2025-09-24 15:25:38.472] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:26:26.409] [INFO] 程序启动开始
[2025-09-24 15:26:26.411] [INFO] 配置系统初始化成功
[2025-09-24 15:26:26.470] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 15:26:26.470] [INFO] 配置系统初始化完成
[2025-09-24 15:26:26.470] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-24 15:26:26.496] [INFO] 开始初始化各个Manager...
[2025-09-24 15:26:26.497] [INFO] 初始化基础Manager...
[2025-09-24 15:26:26.505] [INFO] IO状态缓存初始化完成
[2025-09-24 15:26:26.513] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 15:26:26.513] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 15:26:26.515] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 15:26:26.516] [ERROR] DMC1000B控制卡初始化失败
[2025-09-24 15:26:26.516] [WARN] DMC1000BIO管理器初始化失败
[2025-09-24 15:26:26.521] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-24 15:26:26.522] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 15:26:26.522] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 15:26:26.523] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 15:26:26.564] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 15:26:26.579] [WARN] DMC1000B电机管理器初始化失败
[2025-09-24 15:26:26.580] [INFO] 初始化系统模式管理器...
[2025-09-24 15:26:26.585] [INFO] 开始初始化MotorManager...
[2025-09-24 15:26:26.587] [INFO] 模拟初始化运动控制卡
[2025-09-24 15:26:26.798] [INFO] 加载了8个电机的默认配置
[2025-09-24 15:26:26.799] [INFO] 电机监控任务已启动
[2025-09-24 15:26:26.799] [INFO] MotorManager初始化完成
[2025-09-24 15:26:26.799] [INFO] 初始化通信Manager...
[2025-09-24 15:26:26.801] [INFO] 电机监控循环开始
[2025-09-24 15:26:26.802] [INFO] 开始初始化ScannerManager...
[2025-09-24 15:26:26.805] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-24 15:26:26.826] [INFO] 串口初始化完成
[2025-09-24 15:26:26.849] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-24 15:26:26.864] [INFO] 扫描枪连接成功: COM1
[2025-09-24 15:26:26.865] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-24 15:26:26.865] [INFO] ScannerManager初始化完成
[2025-09-24 15:26:26.893] [INFO] 开始初始化ModbusTcpManager...
[2025-09-24 15:26:26.895] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-24 15:26:26.898] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-24 15:26:31.945] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 15:26:31.946] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-24 15:26:31.946] [INFO] ModbusTcpManager初始化完成
[2025-09-24 15:26:31.950] [INFO] 开始初始化EpsonRobotManager...
[2025-09-24 15:26:31.951] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-24 15:26:31.953] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-24 15:26:31.953] [INFO] EpsonRobotManager初始化完成
[2025-09-24 15:26:31.953] [INFO] 初始化视觉Manager...
[2025-09-24 15:26:31.956] [INFO] 开始初始化VisionManager...
[2025-09-24 15:26:31.956] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-24 15:26:31.958] [INFO] 模拟初始化相机，索引: 0
[2025-09-24 15:26:32.467] [INFO] 相机初始化成功
[2025-09-24 15:26:32.468] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-24 15:26:32.472] [INFO] 视觉配置加载完成
[2025-09-24 15:26:32.472] [INFO] VisionManager初始化完成
[2025-09-24 15:26:32.472] [INFO] 初始化数据Manager...
[2025-09-24 15:26:32.476] [INFO] 开始初始化StatisticsManager...
[2025-09-24 15:26:32.476] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-24 15:26:32.480] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-24 15:26:32.480] [INFO] 历史数据加载完成
[2025-09-24 15:26:32.480] [INFO] 自动保存任务已启动
[2025-09-24 15:26:32.481] [INFO] StatisticsManager初始化完成
[2025-09-24 15:26:32.481] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-24 15:26:32.481] [INFO] 所有Manager初始化完成
[2025-09-24 15:26:32.482] [INFO] 自动保存循环开始
[2025-09-24 15:26:32.520] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:26:32.520] [INFO] 主界面布局创建完成
[2025-09-24 15:26:32.522] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-24 15:26:36.879] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 15:26:36.879] [INFO] Epson机器人管理器初始化完成
[2025-09-24 15:26:36.896] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:26:53.382] [INFO] 开始连接控制端口 - IP: ************, 控制端口: 6000
[2025-09-24 15:26:53.384] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 15:26:53.384] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 15:26:53.384] [INFO] 机器人配置已更新 - IP: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 15:26:53.388] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-24 15:26:53.390] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-24 15:26:53.390] [INFO] 开始TCP连接到 ************:6000
[2025-09-24 15:26:53.445] [INFO] TCP连接成功到 ************:6000
[2025-09-24 15:26:53.446] [INFO] Epson机器人StartStop连接状态变更: Connected - 启动/停止TCP/IP连接成功
[2025-09-24 15:26:53.447] [INFO] 连接状态变化: StartStop已连接 - 启动/停止TCP/IP连接成功
[2025-09-24 15:26:53.447] [INFO] 控制端口连接成功: ************:6000
[2025-09-24 15:26:53.447] [INFO] 控制端口连接成功，开始登录...
[2025-09-24 15:26:53.449] [INFO] 启动/停止TCP监听循环开始
[2025-09-24 15:26:53.450] [INFO] 发送登录命令: $Login,EPSON
[2025-09-24 15:26:53.455] [INFO] 发送Epson机器人命令: $Login,EPSON (StartStop)
[2025-09-24 15:26:53.520] [INFO] Epson机器人登录成功
[2025-09-24 15:26:53.523] [INFO] 控制端口连接并登录成功，可以启动机器人
[2025-09-24 15:26:55.853] [INFO] 发送启动命令给机器人...
[2025-09-24 15:26:55.856] [INFO] 检查机器人状态，等待Auto为ON...
[2025-09-24 15:26:55.858] [INFO] 发送Epson机器人命令: $GetStatus (StartStop)
[2025-09-24 15:26:55.977] [INFO] 机器人Auto状态已就绪，可以执行Start命令
[2025-09-24 15:26:55.978] [INFO] 发送Epson机器人命令: $GetStatus (StartStop)
[2025-09-24 15:26:56.040] [INFO] 发送Epson机器人命令: $Start,0 (StartStop)
[2025-09-24 15:26:56.168] [INFO] Epson机器人启动成功
[2025-09-24 15:26:56.961] [INFO] 机器人启动结果: 成功 - 机器人启动成功
[2025-09-24 15:26:56.962] [INFO] 机器人启动成功，数据端口连接已启用
[2025-09-24 15:26:56.963] [ERROR] 数据收发TCP/IP未连接，无法启动自动化流程
[2025-09-24 15:26:58.868] [INFO] 开始连接数据端口 - IP: ************, 数据端口: 5000
[2025-09-24 15:26:58.870] [INFO] Epson机器人Data连接状态变更: Connecting - 正在连接数据收发TCP/IP...
[2025-09-24 15:26:58.871] [INFO] 连接状态变化: Data连接中... - 正在连接数据收发TCP/IP...
[2025-09-24 15:26:58.875] [INFO] Epson机器人Data连接状态变更: Connected - 数据收发TCP/IP连接成功
[2025-09-24 15:26:58.876] [INFO] 连接状态变化: Data已连接 - 数据收发TCP/IP连接成功
[2025-09-24 15:26:58.877] [INFO] 数据端口连接成功: ************:5000
[2025-09-24 15:26:58.878] [INFO] 数据端口连接成功，可以进行数据收发
[2025-09-24 15:26:58.879] [INFO] 数据收发TCP监听循环开始
[2025-09-24 15:27:06.702] [INFO] 发送Epson机器人命令: $sdasda (Data)
[2025-09-24 15:31:42.660] [INFO] 程序启动开始
[2025-09-24 15:31:42.662] [INFO] 配置系统初始化成功
[2025-09-24 15:31:42.720] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 15:31:42.720] [INFO] 配置系统初始化完成
[2025-09-24 15:31:42.720] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-24 15:31:42.746] [INFO] 开始初始化各个Manager...
[2025-09-24 15:31:42.747] [INFO] 初始化基础Manager...
[2025-09-24 15:31:42.753] [INFO] IO状态缓存初始化完成
[2025-09-24 15:31:42.759] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 15:31:42.759] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 15:31:42.761] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 15:31:42.761] [ERROR] DMC1000B控制卡初始化失败
[2025-09-24 15:31:42.761] [WARN] DMC1000BIO管理器初始化失败
[2025-09-24 15:31:42.765] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-24 15:31:42.765] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 15:31:42.766] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 15:31:42.766] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 15:31:42.821] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 15:31:42.836] [WARN] DMC1000B电机管理器初始化失败
[2025-09-24 15:31:42.836] [INFO] 初始化系统模式管理器...
[2025-09-24 15:31:42.841] [INFO] 开始初始化MotorManager...
[2025-09-24 15:31:42.844] [INFO] 模拟初始化运动控制卡
[2025-09-24 15:31:43.071] [INFO] 加载了8个电机的默认配置
[2025-09-24 15:31:43.073] [INFO] 电机监控任务已启动
[2025-09-24 15:31:43.073] [INFO] MotorManager初始化完成
[2025-09-24 15:31:43.073] [INFO] 初始化通信Manager...
[2025-09-24 15:31:43.077] [INFO] 开始初始化ScannerManager...
[2025-09-24 15:31:43.080] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-24 15:31:43.100] [INFO] 串口初始化完成
[2025-09-24 15:31:43.101] [INFO] 电机监控循环开始
[2025-09-24 15:31:43.126] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-24 15:31:43.183] [INFO] 扫描枪连接成功: COM1
[2025-09-24 15:31:43.184] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-24 15:31:43.184] [INFO] ScannerManager初始化完成
[2025-09-24 15:31:43.203] [INFO] 开始初始化ModbusTcpManager...
[2025-09-24 15:31:43.221] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-24 15:31:43.228] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-24 15:31:48.281] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 15:31:48.282] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-24 15:31:48.282] [INFO] ModbusTcpManager初始化完成
[2025-09-24 15:31:48.285] [INFO] 开始初始化EpsonRobotManager...
[2025-09-24 15:31:48.286] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-24 15:31:48.287] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-24 15:31:48.288] [INFO] EpsonRobotManager初始化完成
[2025-09-24 15:31:48.288] [INFO] 初始化视觉Manager...
[2025-09-24 15:31:48.290] [INFO] 开始初始化VisionManager...
[2025-09-24 15:31:48.290] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-24 15:31:48.292] [INFO] 模拟初始化相机，索引: 0
[2025-09-24 15:31:48.803] [INFO] 相机初始化成功
[2025-09-24 15:31:48.804] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-24 15:31:48.804] [INFO] 视觉配置加载完成
[2025-09-24 15:31:48.809] [INFO] VisionManager初始化完成
[2025-09-24 15:31:48.809] [INFO] 初始化数据Manager...
[2025-09-24 15:31:48.813] [INFO] 开始初始化StatisticsManager...
[2025-09-24 15:31:48.813] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-24 15:31:48.817] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-24 15:31:48.817] [INFO] 历史数据加载完成
[2025-09-24 15:31:48.817] [INFO] 自动保存任务已启动
[2025-09-24 15:31:48.817] [INFO] StatisticsManager初始化完成
[2025-09-24 15:31:48.818] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-24 15:31:48.818] [INFO] 所有Manager初始化完成
[2025-09-24 15:31:48.820] [INFO] 自动保存循环开始
[2025-09-24 15:31:48.856] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:31:48.856] [INFO] 主界面布局创建完成
[2025-09-24 15:31:48.857] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-24 15:31:51.196] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:31:51.197] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 15:31:51.204] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 15:31:51.204] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 15:31:51.510] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 15:31:51.519] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 15:31:51.519] [INFO] Epson机器人管理器初始化完成
[2025-09-24 15:31:51.531] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:32:27.000] [INFO] 开始连接控制端口 - IP: 10.144,144.2, 控制端口: 6000
[2025-09-24 15:32:27.001] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 15:32:27.002] [INFO] 配置已更新 - IP地址: 10.144,144.2, 控制端口: 6000, 数据端口: 5000
[2025-09-24 15:32:27.002] [INFO] 机器人配置已更新 - IP: 10.144,144.2, 控制端口: 6000, 数据端口: 5000
[2025-09-24 15:32:27.005] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-24 15:32:27.007] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-24 15:32:27.007] [INFO] 开始TCP连接到 10.144,144.2:6000
[2025-09-24 15:32:27.019] [ERROR] TCP连接失败到 10.144,144.2:6000: 不知道这样的主机。
[2025-09-24 15:32:27.089] [ERROR] 连接启动/停止TCP/IP 执行失败
异常详情: 不知道这样的主机。
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<<ConnectStartStopAsync>b__71_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 262
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 15:32:52.371] [INFO] 开始连接控制端口 - IP: ************, 控制端口: 6000
[2025-09-24 15:32:52.371] [INFO] EpsonRobotManager已初始化，更新配置...
[2025-09-24 15:32:52.371] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 15:32:52.372] [INFO] 机器人配置已更新 - IP: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 15:32:52.372] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-24 15:32:52.372] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-24 15:32:52.372] [INFO] 开始TCP连接到 ************:6000
[2025-09-24 15:32:52.381] [INFO] TCP连接成功到 ************:6000
[2025-09-24 15:32:52.382] [INFO] Epson机器人StartStop连接状态变更: Connected - 启动/停止TCP/IP连接成功
[2025-09-24 15:32:52.384] [INFO] 连接状态变化: StartStop已连接 - 启动/停止TCP/IP连接成功
[2025-09-24 15:32:52.384] [INFO] 控制端口连接成功: ************:6000
[2025-09-24 15:32:52.384] [INFO] 控制端口连接成功，开始登录...
[2025-09-24 15:32:52.386] [INFO] 启动/停止TCP监听循环开始
[2025-09-24 15:32:52.387] [INFO] 发送登录命令: $Login,EPSON
[2025-09-24 15:32:52.394] [INFO] 发送Epson机器人命令: $Login,EPSON (StartStop)
[2025-09-24 15:32:52.517] [INFO] Epson机器人登录成功
[2025-09-24 15:32:52.520] [INFO] 控制端口连接并登录成功，可以启动机器人
[2025-09-24 15:32:55.349] [INFO] 发送启动命令给机器人...
[2025-09-24 15:32:55.352] [INFO] 检查机器人状态，等待Auto为ON...
[2025-09-24 15:32:55.353] [INFO] 发送Epson机器人命令: $GetStatus (StartStop)
[2025-09-24 15:32:55.471] [INFO] 机器人Auto状态已就绪，可以执行Start命令
[2025-09-24 15:32:55.472] [INFO] 发送Epson机器人命令: $GetStatus (StartStop)
[2025-09-24 15:32:55.592] [INFO] 发送Epson机器人命令: $Start,0 (StartStop)
[2025-09-24 15:32:55.714] [INFO] Epson机器人启动成功
[2025-09-24 15:32:56.768] [INFO] 机器人启动结果: 成功 - 机器人启动成功
[2025-09-24 15:32:56.770] [INFO] 机器人启动成功，数据端口连接已启用
[2025-09-24 15:32:56.771] [ERROR] 数据收发TCP/IP未连接，无法启动自动化流程
[2025-09-24 15:32:58.773] [INFO] 开始连接数据端口 - IP: ************, 数据端口: 5000
[2025-09-24 15:32:58.775] [INFO] Epson机器人Data连接状态变更: Connecting - 正在连接数据收发TCP/IP...
[2025-09-24 15:32:58.776] [INFO] 连接状态变化: Data连接中... - 正在连接数据收发TCP/IP...
[2025-09-24 15:32:58.810] [INFO] Epson机器人Data连接状态变更: Connected - 数据收发TCP/IP连接成功
[2025-09-24 15:32:58.811] [INFO] 连接状态变化: Data已连接 - 数据收发TCP/IP连接成功
[2025-09-24 15:32:58.811] [INFO] 数据端口连接成功: ************:5000
[2025-09-24 15:32:58.812] [INFO] 数据收发TCP监听循环开始
[2025-09-24 15:32:58.813] [INFO] 数据端口连接成功，可以进行数据收发
[2025-09-24 15:33:07.549] [INFO] 发送Epson机器人命令: $gg (Data)
[2025-09-24 15:33:12.630] [ERROR] 发送Epson机器人命令: Custom 执行失败
异常详情: 命令响应超时: $gg
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager.<>c__DisplayClass82_0.<<SendCommandAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager.cs:行号 749
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 15:33:12.632] [ERROR] 数据发送失败: 发送命令失败
[2025-09-24 15:42:32.154] [INFO] 程序启动开始
[2025-09-24 15:42:32.156] [INFO] 配置系统初始化成功
[2025-09-24 15:42:32.229] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 15:42:32.240] [INFO] 配置系统初始化完成
[2025-09-24 15:42:32.250] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-24 15:42:32.572] [INFO] 开始初始化各个Manager...
[2025-09-24 15:42:32.573] [INFO] 初始化基础Manager...
[2025-09-24 15:42:32.584] [INFO] IO状态缓存初始化完成
[2025-09-24 15:42:32.607] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 15:42:32.612] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 15:42:32.617] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 15:42:32.617] [ERROR] DMC1000B控制卡初始化失败
[2025-09-24 15:42:32.617] [WARN] DMC1000BIO管理器初始化失败
[2025-09-24 15:42:32.623] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-24 15:42:32.623] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 15:42:32.623] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 15:42:32.624] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 15:42:32.682] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 15:42:32.704] [WARN] DMC1000B电机管理器初始化失败
[2025-09-24 15:42:32.704] [INFO] 初始化系统模式管理器...
[2025-09-24 15:42:32.709] [INFO] 开始初始化MotorManager...
[2025-09-24 15:42:32.712] [INFO] 模拟初始化运动控制卡
[2025-09-24 15:42:32.931] [INFO] 加载了8个电机的默认配置
[2025-09-24 15:42:32.939] [INFO] 电机监控任务已启动
[2025-09-24 15:42:32.940] [INFO] MotorManager初始化完成
[2025-09-24 15:42:32.940] [INFO] 初始化通信Manager...
[2025-09-24 15:42:32.944] [INFO] 开始初始化ScannerManager...
[2025-09-24 15:42:32.946] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-24 15:42:32.949] [INFO] 串口初始化完成
[2025-09-24 15:42:32.950] [INFO] 电机监控循环开始
[2025-09-24 15:42:32.952] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-24 15:42:33.082] [INFO] 扫描枪连接成功: COM1
[2025-09-24 15:42:33.082] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-24 15:42:33.083] [INFO] ScannerManager初始化完成
[2025-09-24 15:42:33.116] [INFO] 开始初始化ModbusTcpManager...
[2025-09-24 15:42:33.118] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-24 15:42:33.122] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-24 15:42:38.171] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 15:42:38.172] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-24 15:42:38.172] [INFO] ModbusTcpManager初始化完成
[2025-09-24 15:42:38.177] [INFO] 开始初始化EpsonRobotManager...
[2025-09-24 15:42:38.178] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-24 15:42:38.180] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-24 15:42:38.180] [INFO] EpsonRobotManager初始化完成
[2025-09-24 15:42:38.181] [INFO] 初始化视觉Manager...
[2025-09-24 15:42:38.184] [INFO] 开始初始化VisionManager...
[2025-09-24 15:42:38.185] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-24 15:42:38.186] [INFO] 模拟初始化相机，索引: 0
[2025-09-24 15:42:38.702] [INFO] 相机初始化成功
[2025-09-24 15:42:38.703] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-24 15:42:38.704] [INFO] 视觉配置加载完成
[2025-09-24 15:42:38.704] [INFO] VisionManager初始化完成
[2025-09-24 15:42:38.704] [INFO] 初始化数据Manager...
[2025-09-24 15:42:38.707] [INFO] 开始初始化StatisticsManager...
[2025-09-24 15:42:38.708] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-24 15:42:38.712] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-24 15:42:38.712] [INFO] 历史数据加载完成
[2025-09-24 15:42:38.712] [INFO] 自动保存任务已启动
[2025-09-24 15:42:38.713] [INFO] StatisticsManager初始化完成
[2025-09-24 15:42:38.713] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-24 15:42:38.713] [INFO] 所有Manager初始化完成
[2025-09-24 15:42:38.714] [INFO] 自动保存循环开始
[2025-09-24 15:42:38.762] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:42:38.762] [INFO] 主界面布局创建完成
[2025-09-24 15:42:38.764] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-24 15:42:41.921] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:42:41.922] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 15:42:41.929] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 15:42:41.929] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 15:42:43.000] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 15:42:43.006] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:42:44.096] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 15:42:44.097] [INFO] Epson机器人管理器初始化完成
[2025-09-24 15:42:44.109] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:42:45.515] [INFO] 开始初始化EpsonRobotManager2...
[2025-09-24 15:42:45.515] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-24 15:42:45.516] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-24 15:42:45.516] [INFO] EpsonRobotManager2初始化完成
[2025-09-24 15:42:45.516] [INFO] Epson机器人管理器初始化完成
[2025-09-24 15:42:45.526] [INFO] 6轴机器人2控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:42:46.361] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 15:42:46.361] [INFO] Epson机器人管理器初始化完成
[2025-09-24 15:42:46.366] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:42:47.381] [WARN] EpsonRobotManager2已经初始化，跳过重复初始化
[2025-09-24 15:42:47.382] [INFO] Epson机器人管理器初始化完成
[2025-09-24 15:42:47.385] [INFO] 6轴机器人2控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:43:20.054] [INFO] 开始连接控制端口 - IP: ************, 控制端口: 6000
[2025-09-24 15:43:20.056] [INFO] EpsonRobotManager2已初始化，更新配置...
[2025-09-24 15:43:20.056] [INFO] 配置已更新 - IP地址: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 15:43:20.056] [INFO] 机器人配置已更新 - IP: ************, 控制端口: 6000, 数据端口: 5000
[2025-09-24 15:43:20.059] [INFO] Epson机器人StartStop连接状态变更: Connecting - 正在连接启动/停止TCP/IP...
[2025-09-24 15:43:20.062] [INFO] 连接状态变化: StartStop连接中... - 正在连接启动/停止TCP/IP...
[2025-09-24 15:43:20.062] [INFO] 开始TCP连接到 ************:6000
[2025-09-24 15:43:20.095] [INFO] TCP连接成功到 ************:6000
[2025-09-24 15:43:20.096] [INFO] Epson机器人StartStop连接状态变更: Connected - 启动/停止TCP/IP连接成功
[2025-09-24 15:43:20.097] [INFO] 连接状态变化: StartStop已连接 - 启动/停止TCP/IP连接成功
[2025-09-24 15:43:20.097] [INFO] 控制端口连接成功: ************:6000
[2025-09-24 15:43:20.097] [INFO] 控制端口连接成功，开始登录...
[2025-09-24 15:43:20.099] [INFO] 启动/停止TCP监听循环开始
[2025-09-24 15:43:20.100] [INFO] 发送登录命令: $Login,EPSON
[2025-09-24 15:43:20.105] [INFO] 发送Epson机器人命令: $Login,EPSON (StartStop)
[2025-09-24 15:43:20.233] [INFO] Epson机器人登录成功
[2025-09-24 15:43:20.236] [INFO] 控制端口连接并登录成功，可以启动机器人
[2025-09-24 15:43:26.381] [INFO] 发送启动命令给机器人...
[2025-09-24 15:43:26.385] [INFO] 检查机器人状态，等待Auto为ON...
[2025-09-24 15:43:26.387] [INFO] 发送Epson机器人命令: $GetStatus (StartStop)
[2025-09-24 15:43:26.449] [INFO] 机器人Auto状态已就绪，可以执行Start命令
[2025-09-24 15:43:26.450] [INFO] 发送Epson机器人命令: $GetStatus (StartStop)
[2025-09-24 15:43:26.573] [INFO] 发送Epson机器人命令: $Start,0 (StartStop)
[2025-09-24 15:43:26.698] [INFO] Epson机器人启动成功
[2025-09-24 15:43:27.809] [INFO] 机器人启动结果: 成功 - 机器人启动成功
[2025-09-24 15:43:27.811] [INFO] 机器人启动成功，数据端口连接已启用
[2025-09-24 15:43:27.812] [ERROR] 数据收发TCP/IP未连接，无法启动自动化流程
[2025-09-24 15:43:30.500] [INFO] 开始连接数据端口 - IP: ************, 数据端口: 5000
[2025-09-24 15:43:30.503] [INFO] Epson机器人Data连接状态变更: Connecting - 正在连接数据收发TCP/IP...
[2025-09-24 15:43:30.505] [INFO] 连接状态变化: Data连接中... - 正在连接数据收发TCP/IP...
[2025-09-24 15:43:30.523] [INFO] Epson机器人Data连接状态变更: Connected - 数据收发TCP/IP连接成功
[2025-09-24 15:43:30.525] [INFO] 连接状态变化: Data已连接 - 数据收发TCP/IP连接成功
[2025-09-24 15:43:30.525] [INFO] 数据端口连接成功: ************:5000
[2025-09-24 15:43:30.526] [INFO] 数据收发TCP监听循环开始
[2025-09-24 15:43:30.528] [INFO] 数据端口连接成功，可以进行数据收发
[2025-09-24 15:43:31.765] [INFO] 开始切换到自动模式...
[2025-09-24 15:43:31.767] [ERROR] DMC1000B控制卡未初始化或不可用
[2025-09-24 15:43:31.768] [ERROR] 系统未准备好进入自动模式
[2025-09-24 15:43:37.886] [INFO] 发送Epson机器人命令: $111 (Data)
[2025-09-24 15:43:42.984] [ERROR] 发送Epson机器人命令: Custom 执行失败
异常详情: 命令响应超时: $111
堆栈跟踪:    在 MyHMI.Managers.EpsonRobotManager2.<>c__DisplayClass82_0.<<SendCommandAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\EpsonRobotManager2.cs:行号 749
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 15:43:42.985] [ERROR] 数据发送失败: 发送命令失败
[2025-09-24 15:43:48.257] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:43:48.257] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 15:43:48.258] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 15:43:48.258] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 15:43:48.846] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 15:43:48.851] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:43:49.505] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:43:49.506] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 15:43:49.506] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 15:43:49.506] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 15:43:49.936] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 15:43:49.941] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 15:43:49.941] [INFO] Epson机器人管理器初始化完成
[2025-09-24 15:43:49.945] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:43:56.605] [WARN] EpsonRobotManager2已经初始化，跳过重复初始化
[2025-09-24 15:43:56.605] [INFO] Epson机器人管理器初始化完成
[2025-09-24 15:43:56.608] [INFO] 6轴机器人2控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:43:59.308] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 15:43:59.309] [INFO] Epson机器人管理器初始化完成
[2025-09-24 15:43:59.311] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:44:00.244] [WARN] EpsonRobotManager2已经初始化，跳过重复初始化
[2025-09-24 15:44:00.245] [INFO] Epson机器人管理器初始化完成
[2025-09-24 15:44:00.247] [INFO] 6轴机器人2控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:44:01.569] [INFO] 扫描枪1默认配置: COM1, 115200, 8, One, None
[2025-09-24 15:44:01.569] [INFO] 扫描枪2默认配置: COM2, 115200, 8, One, None
[2025-09-24 15:44:01.569] [INFO] 扫描枪3默认配置: COM3, 115200, 8, One, None
[2025-09-24 15:44:01.569] [INFO] 扫描器控制面板获取MultiScannerManager实例成功
[2025-09-24 15:44:01.579] [INFO] 扫描枪1模块界面初始化完成
[2025-09-24 15:44:01.582] [INFO] 扫描枪2模块界面初始化完成
[2025-09-24 15:44:01.584] [INFO] 扫描枪3模块界面初始化完成
[2025-09-24 15:44:01.584] [INFO] 扫描器控制面板初始化完成
[2025-09-24 15:44:15.129] [INFO] 扫描器控制面板获取MultiScannerManager实例成功
[2025-09-24 15:44:15.132] [INFO] 扫描枪1模块界面初始化完成
[2025-09-24 15:44:15.134] [INFO] 扫描枪2模块界面初始化完成
[2025-09-24 15:44:15.137] [INFO] 扫描枪3模块界面初始化完成
[2025-09-24 15:44:15.137] [INFO] 扫描器控制面板初始化完成
[2025-09-24 15:45:00.777] [INFO] 生产日志管理面板初始化完成 - 按HTML原型设计
[2025-09-24 15:45:01.298] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
[2025-09-24 15:45:01.766] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-24 15:45:02.312] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 15:45:02.312] [INFO] Epson机器人管理器初始化完成
[2025-09-24 15:45:02.315] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:45:03.051] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:45:03.051] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 15:45:03.052] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 15:45:03.052] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 15:45:03.638] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 15:45:03.643] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 15:45:03.643] [INFO] Epson机器人管理器初始化完成
[2025-09-24 15:45:03.647] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:45:05.819] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:45:05.819] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 15:45:05.820] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 15:45:05.820] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 15:45:06.750] [ERROR] IO管理器初始化超时，无法启动IO监控
[2025-09-24 15:45:08.670] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 15:45:08.676] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-24 15:45:09.071] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 15:45:09.071] [INFO] Epson机器人管理器初始化完成
[2025-09-24 15:45:09.074] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:45:09.727] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:45:09.727] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 15:45:09.728] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 15:45:09.728] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 15:47:21.739] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 15:49:45.650] [INFO] 程序启动开始
[2025-09-24 15:49:45.653] [INFO] 配置系统初始化成功
[2025-09-24 15:49:45.720] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 15:49:45.720] [INFO] 配置系统初始化完成
[2025-09-24 15:49:45.721] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-24 15:49:45.750] [INFO] 开始初始化各个Manager...
[2025-09-24 15:49:45.750] [INFO] 初始化基础Manager...
[2025-09-24 15:49:45.755] [INFO] IO状态缓存初始化完成
[2025-09-24 15:49:45.760] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 15:49:45.761] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 15:49:45.763] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 15:49:45.763] [ERROR] DMC1000B控制卡初始化失败
[2025-09-24 15:49:45.763] [WARN] DMC1000BIO管理器初始化失败
[2025-09-24 15:49:45.768] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-24 15:49:45.768] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 15:49:45.768] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 15:49:45.769] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 15:49:45.810] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 15:49:45.823] [WARN] DMC1000B电机管理器初始化失败
[2025-09-24 15:49:45.824] [INFO] 初始化系统模式管理器...
[2025-09-24 15:49:45.828] [INFO] 开始初始化MotorManager...
[2025-09-24 15:49:45.830] [INFO] 模拟初始化运动控制卡
[2025-09-24 15:49:46.047] [INFO] 加载了8个电机的默认配置
[2025-09-24 15:49:46.048] [INFO] 电机监控任务已启动
[2025-09-24 15:49:46.048] [INFO] MotorManager初始化完成
[2025-09-24 15:49:46.048] [INFO] 初始化通信Manager...
[2025-09-24 15:49:46.050] [INFO] 电机监控循环开始
[2025-09-24 15:49:46.052] [INFO] 开始初始化ScannerManager...
[2025-09-24 15:49:46.056] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-24 15:49:46.063] [INFO] 串口初始化完成
[2025-09-24 15:49:46.068] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-24 15:49:46.072] [INFO] 扫描枪连接成功: COM1
[2025-09-24 15:49:46.073] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-24 15:49:46.073] [INFO] ScannerManager初始化完成
[2025-09-24 15:49:46.076] [INFO] 开始初始化ModbusTcpManager...
[2025-09-24 15:49:46.078] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-24 15:49:46.081] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-24 15:49:51.134] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 15:49:51.135] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-24 15:49:51.135] [INFO] ModbusTcpManager初始化完成
[2025-09-24 15:49:51.138] [INFO] 开始初始化EpsonRobotManager...
[2025-09-24 15:49:51.138] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-24 15:49:51.140] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-24 15:49:51.140] [INFO] EpsonRobotManager初始化完成
[2025-09-24 15:49:51.140] [INFO] 初始化视觉Manager...
[2025-09-24 15:49:51.142] [INFO] 开始初始化VisionManager...
[2025-09-24 15:49:51.143] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-24 15:49:51.144] [INFO] 模拟初始化相机，索引: 0
[2025-09-24 15:49:51.659] [INFO] 相机初始化成功
[2025-09-24 15:49:51.661] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-24 15:49:51.661] [INFO] 视觉配置加载完成
[2025-09-24 15:49:51.661] [INFO] VisionManager初始化完成
[2025-09-24 15:49:51.665] [INFO] 初始化数据Manager...
[2025-09-24 15:49:51.669] [INFO] 开始初始化StatisticsManager...
[2025-09-24 15:49:51.669] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-24 15:49:51.673] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-24 15:49:51.673] [INFO] 历史数据加载完成
[2025-09-24 15:49:51.674] [INFO] 自动保存任务已启动
[2025-09-24 15:49:51.674] [INFO] StatisticsManager初始化完成
[2025-09-24 15:49:51.674] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-24 15:49:51.674] [INFO] 所有Manager初始化完成
[2025-09-24 15:49:51.675] [INFO] 自动保存循环开始
[2025-09-24 15:49:51.713] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 15:49:51.713] [INFO] 主界面布局创建完成
[2025-09-24 15:49:51.715] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-24 16:40:36.819] [INFO] 程序启动开始
[2025-09-24 16:40:36.821] [INFO] 配置系统初始化成功
[2025-09-24 16:40:36.874] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 16:40:36.875] [INFO] 配置系统初始化完成
[2025-09-24 16:40:36.875] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-24 16:40:36.900] [INFO] 开始初始化各个Manager...
[2025-09-24 16:40:36.900] [INFO] 初始化基础Manager...
[2025-09-24 16:40:36.905] [INFO] IO状态缓存初始化完成
[2025-09-24 16:40:36.911] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 16:40:36.912] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 16:40:36.913] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 16:40:36.914] [ERROR] DMC1000B控制卡初始化失败
[2025-09-24 16:40:36.914] [WARN] DMC1000BIO管理器初始化失败
[2025-09-24 16:40:36.918] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-24 16:40:36.918] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 16:40:36.919] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 16:40:36.920] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 16:40:36.964] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 16:40:36.978] [WARN] DMC1000B电机管理器初始化失败
[2025-09-24 16:40:36.978] [INFO] 初始化系统模式管理器...
[2025-09-24 16:40:36.982] [INFO] 开始初始化MotorManager...
[2025-09-24 16:40:36.985] [INFO] 模拟初始化运动控制卡
[2025-09-24 16:40:37.219] [INFO] 加载了8个电机的默认配置
[2025-09-24 16:40:37.227] [INFO] 电机监控任务已启动
[2025-09-24 16:40:37.230] [INFO] MotorManager初始化完成
[2025-09-24 16:40:37.232] [INFO] 初始化通信Manager...
[2025-09-24 16:40:37.237] [INFO] 开始初始化ScannerManager...
[2025-09-24 16:40:37.240] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-24 16:40:37.245] [INFO] 电机监控循环开始
[2025-09-24 16:40:37.246] [INFO] 串口初始化完成
[2025-09-24 16:40:37.325] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-24 16:40:37.352] [INFO] 扫描枪连接成功: COM1
[2025-09-24 16:40:37.352] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-24 16:40:37.352] [INFO] ScannerManager初始化完成
[2025-09-24 16:40:37.428] [INFO] 开始初始化ModbusTcpManager...
[2025-09-24 16:40:37.430] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-24 16:40:37.437] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-24 16:40:42.479] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 16:40:42.480] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-24 16:40:42.480] [INFO] ModbusTcpManager初始化完成
[2025-09-24 16:40:42.483] [INFO] 开始初始化EpsonRobotManager...
[2025-09-24 16:40:42.484] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-24 16:40:42.486] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-24 16:40:42.486] [INFO] EpsonRobotManager初始化完成
[2025-09-24 16:40:42.487] [INFO] 初始化视觉Manager...
[2025-09-24 16:40:42.489] [INFO] 开始初始化VisionManager...
[2025-09-24 16:40:42.490] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-24 16:40:42.491] [INFO] 模拟初始化相机，索引: 0
[2025-09-24 16:40:43.000] [INFO] 相机初始化成功
[2025-09-24 16:40:43.001] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-24 16:40:43.004] [INFO] 视觉配置加载完成
[2025-09-24 16:40:43.004] [INFO] VisionManager初始化完成
[2025-09-24 16:40:43.005] [INFO] 初始化数据Manager...
[2025-09-24 16:40:43.008] [INFO] 开始初始化StatisticsManager...
[2025-09-24 16:40:43.008] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-24 16:40:43.011] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-24 16:40:43.012] [INFO] 历史数据加载完成
[2025-09-24 16:40:43.012] [INFO] 自动保存任务已启动
[2025-09-24 16:40:43.012] [INFO] StatisticsManager初始化完成
[2025-09-24 16:40:43.012] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-24 16:40:43.012] [INFO] 所有Manager初始化完成
[2025-09-24 16:40:43.014] [INFO] 自动保存循环开始
[2025-09-24 16:40:43.052] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 16:40:43.053] [INFO] 主界面布局创建完成
[2025-09-24 16:40:43.054] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-24 16:40:54.629] [INFO] 开始切换到自动模式...
[2025-09-24 16:40:54.630] [ERROR] DMC1000B控制卡未初始化或不可用
[2025-09-24 16:40:54.631] [ERROR] 系统未准备好进入自动模式
[2025-09-24 16:43:39.473] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 16:43:39.474] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 16:43:39.479] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 16:43:39.479] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 16:43:39.986] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 16:43:39.994] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 16:43:39.994] [INFO] Epson机器人管理器初始化完成
[2025-09-24 16:43:40.007] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 17:32:29.074] [INFO] 程序启动开始
[2025-09-24 17:32:29.076] [INFO] 配置系统初始化成功
[2025-09-24 17:32:29.128] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 17:32:29.129] [INFO] 配置系统初始化完成
[2025-09-24 17:32:29.129] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-24 17:32:29.151] [INFO] 开始初始化各个Manager...
[2025-09-24 17:32:29.151] [INFO] 初始化基础Manager...
[2025-09-24 17:32:29.158] [INFO] IO状态缓存初始化完成
[2025-09-24 17:32:29.166] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 17:32:29.166] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 17:32:29.168] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 17:32:29.168] [ERROR] DMC1000B控制卡初始化失败
[2025-09-24 17:32:29.168] [WARN] DMC1000BIO管理器初始化失败
[2025-09-24 17:32:29.172] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-24 17:32:29.172] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 17:32:29.172] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 17:32:29.173] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 17:32:29.212] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 17:32:29.231] [WARN] DMC1000B电机管理器初始化失败
[2025-09-24 17:32:29.232] [INFO] 初始化系统模式管理器...
[2025-09-24 17:32:29.235] [INFO] 开始初始化MotorManager...
[2025-09-24 17:32:29.237] [INFO] 模拟初始化运动控制卡
[2025-09-24 17:32:29.447] [INFO] 加载了8个电机的默认配置
[2025-09-24 17:32:29.448] [INFO] 电机监控任务已启动
[2025-09-24 17:32:29.448] [INFO] MotorManager初始化完成
[2025-09-24 17:32:29.449] [INFO] 初始化通信Manager...
[2025-09-24 17:32:29.451] [INFO] 电机监控循环开始
[2025-09-24 17:32:29.452] [INFO] 开始初始化ScannerManager...
[2025-09-24 17:32:29.453] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-24 17:32:29.457] [INFO] 串口初始化完成
[2025-09-24 17:32:29.461] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-24 17:32:29.493] [INFO] 扫描枪连接成功: COM1
[2025-09-24 17:32:29.493] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-24 17:32:29.494] [INFO] ScannerManager初始化完成
[2025-09-24 17:32:29.496] [INFO] 开始初始化ModbusTcpManager...
[2025-09-24 17:32:29.498] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-24 17:32:29.502] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-24 17:32:34.576] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 17:32:34.577] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-24 17:32:34.577] [INFO] ModbusTcpManager初始化完成
[2025-09-24 17:32:34.581] [INFO] 开始初始化EpsonRobotManager...
[2025-09-24 17:32:34.581] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-24 17:32:34.583] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-24 17:32:34.583] [INFO] EpsonRobotManager初始化完成
[2025-09-24 17:32:34.583] [INFO] 初始化视觉Manager...
[2025-09-24 17:32:34.585] [INFO] 开始初始化VisionManager...
[2025-09-24 17:32:34.586] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-24 17:32:34.587] [INFO] 模拟初始化相机，索引: 0
[2025-09-24 17:32:35.104] [INFO] 相机初始化成功
[2025-09-24 17:32:35.110] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-24 17:32:35.111] [INFO] 视觉配置加载完成
[2025-09-24 17:32:35.126] [INFO] VisionManager初始化完成
[2025-09-24 17:32:35.127] [INFO] 初始化数据Manager...
[2025-09-24 17:32:35.139] [INFO] 开始初始化StatisticsManager...
[2025-09-24 17:32:35.140] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-24 17:32:35.146] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-24 17:32:35.146] [INFO] 历史数据加载完成
[2025-09-24 17:32:35.147] [INFO] 自动保存任务已启动
[2025-09-24 17:32:35.147] [INFO] StatisticsManager初始化完成
[2025-09-24 17:32:35.148] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-24 17:32:35.148] [INFO] 所有Manager初始化完成
[2025-09-24 17:32:35.150] [INFO] 自动保存循环开始
[2025-09-24 17:32:35.194] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 17:32:35.195] [INFO] 主界面布局创建完成
[2025-09-24 17:32:35.196] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-24 17:32:45.124] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 17:32:45.124] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 17:32:45.130] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 17:32:45.131] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 17:32:46.173] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 17:32:46.180] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 17:32:46.180] [INFO] Epson机器人管理器初始化完成
[2025-09-24 17:32:46.196] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 17:32:46.915] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 17:32:46.915] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 17:32:46.916] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 17:32:46.916] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 17:32:49.669] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 17:32:49.673] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 17:32:50.288] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 17:32:50.288] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 17:32:50.288] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 17:32:50.289] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 17:32:50.854] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 17:32:50.857] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 17:32:50.857] [INFO] Epson机器人管理器初始化完成
[2025-09-24 17:32:50.860] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 17:32:51.428] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 17:32:51.428] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 17:32:51.428] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 17:32:51.428] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 17:32:59.794] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 17:32:59.811] [INFO] 皮带电机控制面板初始化完成 - 双电机设计
[2025-09-24 17:32:59.811] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 17:32:59.818] [INFO] 皮带电机轴3参数设置成功: 脉冲当量=0.01mm/pulse, 最大速度=100mm/s
[2025-09-24 17:32:59.819] [INFO] 皮带电机轴2参数设置成功: 脉冲当量=0.01mm/pulse, 最大速度=100mm/s
[2025-09-24 17:32:59.825] [INFO] 电机参数显示刷新完成
[2025-09-24 17:32:59.826] [INFO] 皮带电机参数初始化完成
[2025-09-24 17:32:59.826] [INFO] 皮带电机控制面板业务逻辑初始化完成
[2025-09-24 17:32:59.845] [INFO] 系统配置保存成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 17:32:59.845] [INFO] 皮带电机配置保存成功
[2025-09-24 17:32:59.845] [INFO] 系统配置保存成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 17:32:59.846] [INFO] 皮带电机配置保存成功
[2025-09-24 17:33:55.687] [INFO] 皮带电机控制面板资源释放完成
[2025-09-24 17:33:55.696] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
[2025-09-24 17:34:01.168] [ERROR] IO管理器初始化超时，无法启动IO监控
[2025-09-24 19:09:14.120] [INFO] 程序启动开始
[2025-09-24 19:09:14.122] [INFO] 配置系统初始化成功
[2025-09-24 19:09:14.199] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 19:09:14.200] [INFO] 配置系统初始化完成
[2025-09-24 19:09:14.201] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-24 19:09:14.246] [INFO] 开始初始化各个Manager...
[2025-09-24 19:09:14.247] [INFO] 初始化基础Manager...
[2025-09-24 19:09:14.253] [INFO] IO状态缓存初始化完成
[2025-09-24 19:09:14.261] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 19:09:14.261] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 19:09:14.264] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 19:09:14.265] [ERROR] DMC1000B控制卡初始化失败
[2025-09-24 19:09:14.265] [WARN] DMC1000BIO管理器初始化失败
[2025-09-24 19:09:14.271] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-24 19:09:14.271] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 19:09:14.271] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 19:09:14.272] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 19:09:14.316] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 19:09:14.332] [WARN] DMC1000B电机管理器初始化失败
[2025-09-24 19:09:14.333] [INFO] 初始化系统模式管理器...
[2025-09-24 19:09:14.338] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-24 19:09:14.339] [INFO] 开始初始化MotorManager...
[2025-09-24 19:09:14.339] [INFO] 当前模式已经是 Manual，无需切换
[2025-09-24 19:09:14.342] [INFO] 模拟初始化运动控制卡
[2025-09-24 19:09:14.554] [INFO] 加载了8个电机的默认配置
[2025-09-24 19:09:14.555] [INFO] 电机监控任务已启动
[2025-09-24 19:09:14.555] [INFO] MotorManager初始化完成
[2025-09-24 19:09:14.555] [INFO] 初始化通信Manager...
[2025-09-24 19:09:14.557] [INFO] 电机监控循环开始
[2025-09-24 19:09:14.558] [INFO] 开始初始化ScannerManager...
[2025-09-24 19:09:14.560] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-24 19:09:14.563] [INFO] 串口初始化完成
[2025-09-24 19:09:14.568] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-24 19:09:14.571] [INFO] 扫描枪连接成功: COM1
[2025-09-24 19:09:14.572] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-24 19:09:14.572] [INFO] ScannerManager初始化完成
[2025-09-24 19:09:14.575] [INFO] 开始初始化ModbusTcpManager...
[2025-09-24 19:09:14.577] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-24 19:09:14.580] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-24 19:09:19.645] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 19:09:19.647] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-24 19:09:19.647] [INFO] ModbusTcpManager初始化完成
[2025-09-24 19:09:19.651] [INFO] 开始初始化EpsonRobotManager...
[2025-09-24 19:09:19.652] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-24 19:09:19.653] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-24 19:09:19.654] [INFO] EpsonRobotManager初始化完成
[2025-09-24 19:09:19.654] [INFO] 初始化视觉Manager...
[2025-09-24 19:09:19.656] [INFO] 开始初始化VisionManager...
[2025-09-24 19:09:19.657] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-24 19:09:19.658] [INFO] 模拟初始化相机，索引: 0
[2025-09-24 19:09:20.169] [INFO] 相机初始化成功
[2025-09-24 19:09:20.171] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-24 19:09:20.171] [INFO] 视觉配置加载完成
[2025-09-24 19:09:20.171] [INFO] VisionManager初始化完成
[2025-09-24 19:09:20.172] [INFO] 初始化数据Manager...
[2025-09-24 19:09:20.175] [INFO] 开始初始化StatisticsManager...
[2025-09-24 19:09:20.176] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-24 19:09:20.179] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-24 19:09:20.180] [INFO] 历史数据加载完成
[2025-09-24 19:09:20.180] [INFO] 自动保存任务已启动
[2025-09-24 19:09:20.180] [INFO] StatisticsManager初始化完成
[2025-09-24 19:09:20.180] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-24 19:09:20.181] [INFO] 所有Manager初始化完成
[2025-09-24 19:09:20.182] [INFO] 自动保存循环开始
[2025-09-24 19:09:20.220] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 19:09:20.220] [INFO] 主界面布局创建完成
[2025-09-24 19:09:20.222] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-24 19:09:29.959] [INFO] 开始切换到自动模式...
[2025-09-24 19:09:29.960] [ERROR] DMC1000B控制卡未初始化或不可用
[2025-09-24 19:09:29.960] [ERROR] 系统未准备好进入自动模式
[2025-09-24 19:44:00.935] [INFO] 程序启动开始
[2025-09-24 19:44:00.938] [INFO] 配置系统初始化成功
[2025-09-24 19:44:00.994] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-24 19:44:00.994] [INFO] 配置系统初始化完成
[2025-09-24 19:44:00.995] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-24 19:44:01.021] [INFO] 开始初始化各个Manager...
[2025-09-24 19:44:01.022] [INFO] 初始化基础Manager...
[2025-09-24 19:44:01.028] [INFO] IO状态缓存初始化完成
[2025-09-24 19:44:01.033] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 19:44:01.034] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 19:44:01.037] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 19:44:01.037] [ERROR] DMC1000B控制卡初始化失败
[2025-09-24 19:44:01.037] [WARN] DMC1000BIO管理器初始化失败
[2025-09-24 19:44:01.042] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-24 19:44:01.043] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-24 19:44:01.043] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-24 19:44:01.044] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-24 19:44:01.084] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__46_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 137
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 19:44:01.098] [WARN] DMC1000B电机管理器初始化失败
[2025-09-24 19:44:01.098] [INFO] 初始化系统模式管理器...
[2025-09-24 19:44:01.103] [INFO] 开始恢复上次退出时的系统模式...
[2025-09-24 19:44:01.104] [INFO] 开始初始化MotorManager...
[2025-09-24 19:44:01.104] [INFO] 当前模式已经是 Manual，无需切换
[2025-09-24 19:44:01.106] [INFO] 模拟初始化运动控制卡
[2025-09-24 19:44:01.327] [INFO] 加载了8个电机的默认配置
[2025-09-24 19:44:01.328] [INFO] 电机监控任务已启动
[2025-09-24 19:44:01.328] [INFO] MotorManager初始化完成
[2025-09-24 19:44:01.329] [INFO] 初始化通信Manager...
[2025-09-24 19:44:01.330] [INFO] 电机监控循环开始
[2025-09-24 19:44:01.332] [INFO] 开始初始化ScannerManager...
[2025-09-24 19:44:01.333] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-24 19:44:01.337] [INFO] 串口初始化完成
[2025-09-24 19:44:01.339] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-24 19:44:01.342] [INFO] 扫描枪连接成功: COM1
[2025-09-24 19:44:01.343] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-24 19:44:01.343] [INFO] ScannerManager初始化完成
[2025-09-24 19:44:01.345] [INFO] 开始初始化ModbusTcpManager...
[2025-09-24 19:44:01.347] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-24 19:44:01.349] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-24 19:44:06.442] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-24 19:44:06.443] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-24 19:44:06.444] [INFO] ModbusTcpManager初始化完成
[2025-09-24 19:44:06.447] [INFO] 开始初始化EpsonRobotManager...
[2025-09-24 19:44:06.448] [INFO] 从配置文件加载Epson机器人配置成功
[2025-09-24 19:44:06.449] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-24 19:44:06.450] [INFO] EpsonRobotManager初始化完成
[2025-09-24 19:44:06.450] [INFO] 初始化视觉Manager...
[2025-09-24 19:44:06.452] [INFO] 开始初始化VisionManager...
[2025-09-24 19:44:06.453] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-24 19:44:06.454] [INFO] 模拟初始化相机，索引: 0
[2025-09-24 19:44:06.974] [INFO] 相机初始化成功
[2025-09-24 19:44:06.979] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-24 19:44:06.979] [INFO] 视觉配置加载完成
[2025-09-24 19:44:06.979] [INFO] VisionManager初始化完成
[2025-09-24 19:44:06.980] [INFO] 初始化数据Manager...
[2025-09-24 19:44:06.982] [INFO] 开始初始化StatisticsManager...
[2025-09-24 19:44:06.983] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-24 19:44:06.986] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-24 19:44:06.986] [INFO] 历史数据加载完成
[2025-09-24 19:44:06.986] [INFO] 自动保存任务已启动
[2025-09-24 19:44:06.986] [INFO] StatisticsManager初始化完成
[2025-09-24 19:44:06.987] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-24 19:44:06.987] [INFO] 所有Manager初始化完成
[2025-09-24 19:44:06.988] [INFO] 自动保存循环开始
[2025-09-24 19:44:07.028] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 19:44:07.028] [INFO] 主界面布局创建完成
[2025-09-24 19:44:07.029] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-24 19:44:08.597] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 19:44:08.598] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 19:44:08.603] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 19:44:08.603] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 19:44:09.270] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 19:44:09.276] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-24 19:44:09.276] [INFO] Epson机器人管理器初始化完成
[2025-09-24 19:44:09.288] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-24 19:44:10.187] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-24 19:44:10.187] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-24 19:44:10.188] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-24 19:44:10.188] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-24 19:44:11.634] [INFO] 翻转电机控制面板资源释放完成
[2025-09-24 19:44:11.639] [INFO] 翻转电机示教面板业务逻辑初始化完成
[2025-09-24 19:44:11.644] [INFO] 翻转电机示教面板初始化完成 - 按HTML原型设计
[2025-09-24 19:44:21.449] [INFO] 翻转电机示教面板资源释放完成
