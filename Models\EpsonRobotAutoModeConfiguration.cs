using System;
using System.Collections.Generic;

namespace MyHMI.Models
{
    /// <summary>
    /// Epson机器人自动模式配置类
    /// </summary>
    public class EpsonAutoModeConfiguration
    {
        /// <summary>
        /// 机器人1 ID
        /// </summary>
        public int Robot1Id { get; set; } = 1;

        /// <summary>
        /// 机器人2 ID
        /// </summary>
        public int Robot2Id { get; set; } = 2;

        /// <summary>
        /// IO操作延迟时间（毫秒）
        /// </summary>
        public int IOOperationDelayMs { get; set; } = 1000;

        /// <summary>
        /// 复位延迟时间（毫秒）
        /// </summary>
        public int ResetDelayMs { get; set; } = 1000;

        /// <summary>
        /// 消息处理超时时间（毫秒）
        /// </summary>
        public int MessageTimeoutMs { get; set; } = 30000;

        /// <summary>
        /// 状态检查间隔（毫秒）
        /// </summary>
        public int StateCheckIntervalMs { get; set; } = 100;

        /// <summary>
        /// 是否启用自动恢复
        /// </summary>
        public bool EnableAutoRecovery { get; set; } = true;

        /// <summary>
        /// 自动恢复重试次数
        /// </summary>
        public int AutoRecoveryRetryCount { get; set; } = 3;

        /// <summary>
        /// 自动恢复延迟时间（毫秒）
        /// </summary>
        public int AutoRecoveryDelayMs { get; set; } = 5000;

        /// <summary>
        /// 是否启用详细日志
        /// </summary>
        public bool EnableVerboseLogging { get; set; } = true;

        /// <summary>
        /// 是否启用性能监控
        /// </summary>
        public bool EnablePerformanceMonitoring { get; set; } = true;

        /// <summary>
        /// NG状态监控超时时间（毫秒）
        /// </summary>
        public int NGStatusMonitorTimeoutMs { get; set; } = 30000;

        /// <summary>
        /// NG状态检查间隔时间（毫秒）
        /// </summary>
        public int NGStatusCheckIntervalMs { get; set; } = 100;

        /// <summary>
        /// NG状态稳定时间（毫秒）
        /// </summary>
        public int NGStatusStableTimeMs { get; set; } = 1000;

        /// <summary>
        /// 连接检查间隔时间（毫秒）
        /// </summary>
        public int ConnectionCheckIntervalMs { get; set; } = 30000;

        /// <summary>
        /// 是否启用自动重连
        /// </summary>
        public bool AutoReconnect { get; set; } = true;

        /// <summary>
        /// 是否启用自动错误恢复
        /// </summary>
        public bool AutoRecover { get; set; } = true;

        /// <summary>
        /// IO点位映射配置
        /// </summary>
        public IOMapping IOMapping { get; set; } = new IOMapping();

        /// <summary>
        /// 扫码器映射配置
        /// </summary>
        public ScannerMapping ScannerMapping { get; set; } = new ScannerMapping();

        /// <summary>
        /// 消息重试配置
        /// </summary>
        public MessageRetryConfiguration MessageRetry { get; set; } = new MessageRetryConfiguration();

        /// <summary>
        /// 构造函数
        /// </summary>
        public EpsonAutoModeConfiguration()
        {
            // 设置默认值
            IOMapping = new IOMapping();
            ScannerMapping = new ScannerMapping();
            MessageRetry = new MessageRetryConfiguration();
        }

        /// <summary>
        /// 验证配置有效性
        /// </summary>
        /// <returns>验证结果</returns>
        public ConfigurationValidationResult Validate()
        {
            var result = new ConfigurationValidationResult();

            // 验证机器人ID
            if (Robot1Id <= 0 || Robot2Id <= 0)
            {
                result.AddError("机器人ID必须大于0");
            }

            if (Robot1Id == Robot2Id)
            {
                result.AddError("机器人1和机器人2的ID不能相同");
            }

            // 验证时间配置
            if (IOOperationDelayMs < 0)
            {
                result.AddError("IO操作延迟时间不能为负数");
            }

            if (MessageTimeoutMs <= 0)
            {
                result.AddError("消息超时时间必须大于0");
            }

            if (StateCheckIntervalMs <= 0)
            {
                result.AddError("状态检查间隔必须大于0");
            }

            // 验证IO映射
            var ioValidation = IOMapping.Validate();
            if (!ioValidation.IsValid)
            {
                result.AddErrors(ioValidation.Errors);
            }

            // 验证扫码器映射
            var scannerValidation = ScannerMapping.Validate();
            if (!scannerValidation.IsValid)
            {
                result.AddErrors(scannerValidation.Errors);
            }

            return result;
        }
    }

    /// <summary>
    /// IO点位映射配置
    /// </summary>
    public class IOMapping
    {
        /// <summary>
        /// 机器人1输出控制IO
        /// </summary>
        public string Robot1OutputIO { get; set; } = "O0001";

        /// <summary>
        /// 机器人1输入检测IO
        /// </summary>
        public string Robot1InputIO { get; set; } = "I0006";

        /// <summary>
        /// 机器人1 NG状态IO
        /// </summary>
        public string Robot1NGStatusIO { get; set; } = "I0104";

        /// <summary>
        /// 机器人2输出控制IO
        /// </summary>
        public string Robot2OutputIO { get; set; } = "O0003";

        /// <summary>
        /// 机器人2输入检测IO
        /// </summary>
        public string Robot2InputIO { get; set; } = "I0010";

        /// <summary>
        /// 机器人2 NG状态IO
        /// </summary>
        public string Robot2NGStatusIO { get; set; } = "I0105";

        /// <summary>
        /// OK品放置状态IO（共用）
        /// </summary>
        public string OKPutStatusIO { get; set; } = "I0106";

        /// <summary>
        /// 验证IO映射配置
        /// </summary>
        /// <returns>验证结果</returns>
        public ConfigurationValidationResult Validate()
        {
            var result = new ConfigurationValidationResult();

            var ioList = new[]
            {
                Robot1OutputIO, Robot1InputIO, Robot1NGStatusIO,
                Robot2OutputIO, Robot2InputIO, Robot2NGStatusIO,
                OKPutStatusIO
            };

            foreach (var io in ioList)
            {
                if (string.IsNullOrEmpty(io))
                {
                    result.AddError("IO点位不能为空");
                }
                else if (!io.StartsWith("I") && !io.StartsWith("O"))
                {
                    result.AddError($"IO点位格式错误: {io}");
                }
            }

            return result;
        }
    }

    /// <summary>
    /// 扫码器映射配置
    /// </summary>
    public class ScannerMapping
    {
        /// <summary>
        /// 1号扫码器ID（对应机器人1的扫码需求）
        /// </summary>
        public int Scanner1Id { get; set; } = 1;

        /// <summary>
        /// 2号扫码器ID（对应机器人2的扫码需求）
        /// </summary>
        public int Scanner2Id { get; set; } = 2;

        /// <summary>
        /// 3号扫码器ID（备用或特殊用途）
        /// </summary>
        public int Scanner3Id { get; set; } = 3;

        /// <summary>
        /// 验证扫码器映射配置
        /// </summary>
        /// <returns>验证结果</returns>
        public ConfigurationValidationResult Validate()
        {
            var result = new ConfigurationValidationResult();

            var scannerIds = new[]
            {
                Scanner1Id, Scanner2Id, Scanner3Id
            };

            foreach (var id in scannerIds)
            {
                if (id <= 0)
                {
                    result.AddError("扫码器ID必须大于0");
                }
            }

            // 检查ID重复
            var uniqueIds = new HashSet<int>(scannerIds);
            if (uniqueIds.Count != scannerIds.Length)
            {
                result.AddError("扫码器ID不能重复");
            }

            return result;
        }
    }

    /// <summary>
    /// 消息重试配置
    /// </summary>
    public class MessageRetryConfiguration
    {
        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetryCount { get; set; } = 3;

        /// <summary>
        /// 重试延迟时间（毫秒）
        /// </summary>
        public int RetryDelayMs { get; set; } = 1000;

        /// <summary>
        /// 是否使用指数退避
        /// </summary>
        public bool UseExponentialBackoff { get; set; } = true;

        /// <summary>
        /// 指数退避倍数
        /// </summary>
        public double BackoffMultiplier { get; set; } = 2.0;
    }

    /// <summary>
    /// 配置验证结果
    /// </summary>
    public class ConfigurationValidationResult
    {
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid => Errors.Count == 0;

        /// <summary>
        /// 错误列表
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// 添加错误
        /// </summary>
        /// <param name="error">错误消息</param>
        public void AddError(string error)
        {
            if (!string.IsNullOrEmpty(error))
            {
                Errors.Add(error);
            }
        }

        /// <summary>
        /// 添加多个错误
        /// </summary>
        /// <param name="errors">错误列表</param>
        public void AddErrors(IEnumerable<string> errors)
        {
            if (errors != null)
            {
                Errors.AddRange(errors);
            }
        }

        /// <summary>
        /// 获取错误摘要
        /// </summary>
        /// <returns>错误摘要</returns>
        public string GetErrorSummary()
        {
            return IsValid ? "配置有效" : $"发现 {Errors.Count} 个错误: {string.Join("; ", Errors)}";
        }
    }
}
