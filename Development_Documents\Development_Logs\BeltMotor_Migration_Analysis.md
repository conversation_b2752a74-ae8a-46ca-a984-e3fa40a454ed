# 皮带电机代码迁移分析报告

## 分析时间
2025-09-27

## 分析目标
详细分析WorkflowManager.cs中所有皮带电机相关的代码，准备迁移到BeltMotorAutoModeController.cs的清单。

## 需要迁移的代码清单

### 1. 私有字段
```csharp
// 皮带电机自动控制相关字段
private bool _beltMotorAutoControlEnabled = false;
private CancellationTokenSource _beltMotorCancellationTokenSource;
private Task _inputBeltControlTask;
private Task _outputBeltControlTask;
private readonly SemaphoreSlim _beltMotorSemaphore = new SemaphoreSlim(1, 1);

// 管理器依赖
private DMC1000BMotorManager _motorManager;
private DMC1000BIOManager _ioManager;
```

### 2. 常量定义
```csharp
// 皮带电机控制常量
private const short INPUT_BELT_AXIS = 2;   // 输入皮带电机轴号
private const short OUTPUT_BELT_AXIS = 3;  // 输出皮带电机轴号
private const string INPUT_SENSOR_IO = "I0004";   // 输入皮带传感器
private const string OUTPUT_SENSOR_IO = "I0106";  // 输出皮带传感器
private const int SENSOR_CHECK_INTERVAL_MS = 50;  // 传感器检查间隔（毫秒）
private const int MOTOR_STOP_DELAY_MS = 100;      // 电机停止延迟（毫秒）
```

### 3. 初始化和释放方法
```csharp
// 需要迁移的方法
private async Task<bool> InitializeBeltMotorControlDependenciesAsync()
```

### 4. 核心控制方法
```csharp
// 公共接口方法
public async Task<bool> StartBeltMotorAutoControlAsync()
public async Task<bool> StopBeltMotorAutoControlAsync()
public async Task<bool> StartOutputBeltAsync()
public async Task<bool> StopOutputBeltAsync()

// 私有控制方法
private async Task InputBeltControlLoopAsync(CancellationToken cancellationToken)
private async Task OutputBeltControlLoopAsync(CancellationToken cancellationToken)
private async Task StopAllBeltMotorsAsync()
```

### 5. 注释和文档
- 类注释中的"负责管理皮带电机自动控制功能"需要修改
- 各方法的中文注释需要保持

## 需要保留在WorkflowManager中的代码

### 1. 工作流状态管理
```csharp
// 工作流状态
private WorkflowState _currentState = WorkflowState.Idle;
private bool _isWorkflowEnabled = true;
```

### 2. 工作流控制方法
```csharp
public async Task<bool> StartWorkflowAsync(string productId = "")
public async Task<bool> StopWorkflowAsync()
public async Task<bool> ResetWorkflowAsync()
```

### 3. 事件定义
```csharp
public event EventHandler<WorkflowStateChangedEventArgs> WorkflowStateChanged;
public event EventHandler<WorkflowCompletedEventArgs> WorkflowCompleted;
public event EventHandler<WorkflowErrorEventArgs> WorkflowError;
```

## 迁移后的调用关系变化

### 原调用方式
```csharp
// StartupSelfCheckManager.cs
var workflowManager = WorkflowManager.Instance;
bool startResult = await workflowManager.StartBeltMotorAutoControlAsync();
```

### 新调用方式
```csharp
// StartupSelfCheckManager.cs
var beltMotorController = BeltMotorAutoModeController.Instance;
bool startResult = await beltMotorController.StartAsync();
```

## 迁移注意事项

1. **保持原有逻辑不变**：所有皮带电机控制逻辑必须完全保持不变
2. **线程安全**：保持原有的信号量和线程管理机制
3. **异常处理**：保持原有的异常处理和日志记录
4. **依赖关系**：确保对DMC1000BMotorManager和DMC1000BIOManager的依赖正确
5. **接口统一**：实现与其他AutoMode控制器一致的接口设计

## 预计迁移工作量

- **代码行数**：约400行代码需要迁移
- **方法数量**：8个方法需要迁移
- **常量数量**：6个常量需要迁移
- **字段数量**：7个字段需要迁移

## 验证清单

- [ ] 所有皮带电机相关代码已迁移
- [ ] 编译无错误
- [ ] 皮带电机控制功能正常
- [ ] 线程管理正常
- [ ] 异常处理正常
- [ ] 日志记录正常
- [ ] 接口调用正常
