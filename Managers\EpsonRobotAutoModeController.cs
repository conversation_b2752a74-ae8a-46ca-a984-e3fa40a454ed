using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MyHMI.Events;
using MyHMI.Helpers;
using MyHMI.Models;

namespace MyHMI.Managers
{
    /// <summary>
    /// Epson机器人自动模式控制器
    /// 负责协调两台Epson六轴机器人的自动模式通信和业务逻辑处理
    /// </summary>
    public partial class EpsonRobotAutoModeController : IDisposable
    {
        #region 单例模式
        private static readonly Lazy<EpsonRobotAutoModeController> _instance = 
            new Lazy<EpsonRobotAutoModeController>(() => new EpsonRobotAutoModeController());
        
        /// <summary>
        /// 获取控制器实例
        /// </summary>
        public static EpsonRobotAutoModeController Instance => _instance.Value;

        /// <summary>
        /// 私有构造函数
        /// </summary>
        private EpsonRobotAutoModeController()
        {
            InitializeController();
        }
        #endregion

        #region 事件定义
        /// <summary>
        /// 自动模式状态变化事件
        /// </summary>
        public event EventHandler<AutoModeStateChangedEventArgs> StateChanged;

        /// <summary>
        /// 机器人消息接收事件
        /// </summary>
        public event EventHandler<RobotMessageReceivedEventArgs> MessageReceived;

        /// <summary>
        /// 机器人工作流状态变化事件
        /// </summary>
        public event EventHandler<RobotWorkflowStateChangedEventArgs> WorkflowStateChanged;

        /// <summary>
        /// 工作流程完成事件
        /// </summary>
        public event EventHandler<RobotWorkflowCompletedEventArgs> WorkflowCompleted;

        /// <summary>
        /// 自动模式错误事件
        /// </summary>
        public event EventHandler<AutoModeErrorEventArgs> ErrorOccurred;

        /// <summary>
        /// 响应发送事件
        /// </summary>
        public event EventHandler<ResponseSentEventArgs> ResponseSent;
        #endregion

        #region 私有字段
        // 状态管理
        private EpsonAutoModeState _currentState = EpsonAutoModeState.Idle;
        private readonly object _stateLock = new object();

        // 线程控制
        private CancellationTokenSource _cancellationTokenSource;
        private Task _autoModeTask;
        private bool _isRunning = false;

        // 管理器依赖
        private EpsonRobotManager _robot1Manager;
        private EpsonRobotManager2 _robot2Manager;
        private DMC1000BIOManager _ioManager;
        private MultiScannerManager _scannerManager;
        private ScannerAutoModeManager _scannerAutoModeManager;
        private ScaraCommunicationManager _scaraCommunicationManager;

        // 业务状态跟踪
        private readonly Dictionary<int, RobotWorkflowState> _robotStates = new Dictionary<int, RobotWorkflowState>();
        private readonly Dictionary<int, string> _pendingMessages = new Dictionary<int, string>();

        // 消息队列
        private readonly ConcurrentQueue<RobotMessage> _messageQueue = new ConcurrentQueue<RobotMessage>();
        private readonly ConcurrentQueue<ControllerResponse> _responseQueue = new ConcurrentQueue<ControllerResponse>();

        // 配置
        private EpsonAutoModeConfiguration _configuration;

        // 性能监控
        private readonly Dictionary<string, DateTime> _operationStartTimes = new Dictionary<string, DateTime>();
        private readonly Dictionary<string, TimeSpan> _operationDurations = new Dictionary<string, TimeSpan>();
        private readonly Dictionary<string, int> _operationCounts = new Dictionary<string, int>();
        private readonly Dictionary<string, int> _errorCounts = new Dictionary<string, int>();

        // 错误处理和维护
        private DateTime? _lastConnectionCheck;
        #endregion

        #region 公共属性
        /// <summary>
        /// 当前控制器状态
        /// </summary>
        public EpsonAutoModeState CurrentState
        {
            get
            {
                lock (_stateLock)
                {
                    return _currentState;
                }
            }
            private set
            {
                lock (_stateLock)
                {
                    if (_currentState != value)
                    {
                        var oldState = _currentState;
                        _currentState = value;
                        OnStateChanged(new AutoModeStateChangedEventArgs(oldState, value));
                    }
                }
            }
        }

        /// <summary>
        /// 是否正在运行
        /// </summary>
        public bool IsRunning => _isRunning;

        /// <summary>
        /// 机器人1状态
        /// </summary>
        public RobotWorkflowState Robot1State => _robotStates.ContainsKey(1) ? _robotStates[1] : RobotWorkflowState.Ready;

        /// <summary>
        /// 机器人2状态
        /// </summary>
        public RobotWorkflowState Robot2State => _robotStates.ContainsKey(2) ? _robotStates[2] : RobotWorkflowState.Ready;

        /// <summary>
        /// 配置信息
        /// </summary>
        public EpsonAutoModeConfiguration Configuration => _configuration;

        /// <summary>
        /// 待处理消息数量
        /// </summary>
        public int PendingMessageCount => _messageQueue.Count;

        /// <summary>
        /// 待发送响应数量
        /// </summary>
        public int PendingResponseCount => _responseQueue.Count;
        #endregion

        #region 初始化和释放
        /// <summary>
        /// 初始化控制器
        /// </summary>
        private void InitializeController()
        {
            try
            {
                LogHelper.Info("开始初始化EpsonRobotAutoModeController...");

                // 加载配置
                LoadConfiguration();

                // 初始化机器人状态
                _robotStates[_configuration.Robot1Id] = RobotWorkflowState.Ready;
                _robotStates[_configuration.Robot2Id] = RobotWorkflowState.Ready;

                LogHelper.Info("EpsonRobotAutoModeController初始化完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("EpsonRobotAutoModeController初始化失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 异步初始化控制器
        /// </summary>
        /// <returns>初始化结果</returns>
        public async Task<bool> InitializeAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (CurrentState != EpsonAutoModeState.Idle)
                {
                    LogHelper.Warning("控制器已经初始化或正在运行，跳过初始化");
                    return true;
                }

                CurrentState = EpsonAutoModeState.Initializing;

                // 初始化依赖管理器
                if (!await InitializeDependencyManagers())
                {
                    CurrentState = EpsonAutoModeState.Error;
                    return false;
                }

                // 订阅机器人事件
                SubscribeToRobotEvents();

                CurrentState = EpsonAutoModeState.Idle;
                LogHelper.Info("EpsonRobotAutoModeController异步初始化完成");
                return true;

            }, false, "EpsonRobotAutoModeController异步初始化");
        }

        /// <summary>
        /// 初始化依赖管理器
        /// </summary>
        /// <returns>初始化结果</returns>
        private async Task<bool> InitializeDependencyManagers()
        {
            try
            {
                LogHelper.Info("开始初始化依赖管理器...");

                // 获取机器人管理器实例
                _robot1Manager = EpsonRobotManager.Instance;
                _robot2Manager = EpsonRobotManager2.Instance;

                // 获取IO管理器实例
                _ioManager = DMC1000BIOManager.Instance;

                // 获取扫码器管理器实例
                _scannerManager = MultiScannerManager.Instance;
                _scannerAutoModeManager = ScannerAutoModeManager.Instance;

                // 获取SCARA通信管理器实例（用于设置L_moto_ready和R_moto_ready）
                _scaraCommunicationManager = ScaraCommunicationManager.Instance;

                // 初始化扫码器管理器
                if (!await _scannerManager.InitializeAsync())
                {
                    LogHelper.Error("扫码器管理器初始化失败");
                    return false;
                }

                // 初始化扫码器自动模式管理器
                if (!await _scannerAutoModeManager.InitializeAsync())
                {
                    LogHelper.Error("扫码器自动模式管理器初始化失败");
                    return false;
                }

                // 执行机器人连接流程
                if (!await InitializeRobotConnections())
                {
                    LogHelper.Error("机器人连接初始化失败");
                    return false;
                }

                LogHelper.Info("依赖管理器初始化完成");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("依赖管理器初始化失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 初始化机器人连接流程
        /// 按照需求：连接主端口 → 登录 → 启动 → 连接数据端口
        /// </summary>
        /// <returns>初始化结果</returns>
        private async Task<bool> InitializeRobotConnections()
        {
            try
            {
                LogHelper.Info("开始初始化机器人连接流程...");

                // 初始化机器人1连接
                if (!await InitializeSingleRobotConnection(_robot1Manager, "机器人1"))
                {
                    return false;
                }

                // 初始化机器人2连接
                if (!await InitializeSingleRobotConnection(_robot2Manager, "机器人2"))
                {
                    return false;
                }

                LogHelper.Info("机器人连接流程初始化完成");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("机器人连接流程初始化失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 初始化单个机器人的连接流程
        /// </summary>
        /// <param name="robotManager">机器人管理器</param>
        /// <param name="robotName">机器人名称</param>
        /// <returns>初始化结果</returns>
        private async Task<bool> InitializeSingleRobotConnection(dynamic robotManager, string robotName)
        {
            try
            {
                LogHelper.Info($"开始初始化{robotName}连接...");

                // 1. 连接主端口（控制端口）
                LogHelper.Info($"{robotName}: 连接主端口...");
                if (!await robotManager.ConnectStartStopAsync())
                {
                    LogHelper.Error($"{robotName}: 主端口连接失败");
                    return false;
                }

                // 2. 登录机器人
                LogHelper.Info($"{robotName}: 执行登录...");
                if (!await robotManager.LoginAsync())
                {
                    LogHelper.Error($"{robotName}: 登录失败");
                    return false;
                }

                // 3. 启动机器人
                LogHelper.Info($"{robotName}: 启动机器人...");
                if (!await robotManager.StartRobotAsync())
                {
                    LogHelper.Error($"{robotName}: 启动失败");
                    return false;
                }

                // 4. 连接数据端口
                LogHelper.Info($"{robotName}: 连接数据端口...");
                if (!await robotManager.ConnectDataAsync())
                {
                    LogHelper.Error($"{robotName}: 数据端口连接失败");
                    return false;
                }

                LogHelper.Info($"{robotName}: 连接流程完成");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"{robotName}: 连接流程异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 订阅机器人事件
        /// </summary>
        private void SubscribeToRobotEvents()
        {
            try
            {
                // 订阅机器人1特殊命令事件
                if (_robot1Manager != null)
                {
                    _robot1Manager.SpecialCommandReceived += OnRobot1SpecialCommandReceived;
                    LogHelper.Info("已订阅机器人1特殊命令事件");
                }

                // 订阅机器人2特殊命令事件
                if (_robot2Manager != null)
                {
                    _robot2Manager.SpecialCommandReceived += OnRobot2SpecialCommandReceived;
                    LogHelper.Info("已订阅机器人2特殊命令事件");
                }

                LogHelper.Info("机器人事件订阅完成 - 数据端口监听机制已激活");
            }
            catch (Exception ex)
            {
                LogHelper.Error("机器人事件订阅失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 启动数据端口监听
        /// 确保两台机器人的数据端口都在监听状态
        /// </summary>
        /// <returns>启动结果</returns>
        public async Task<bool> StartDataPortListeningAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info("开始启动数据端口监听...");

                // 检查机器人1数据端口监听状态
                if (!await EnsureRobotDataListening(_robot1Manager, "机器人1"))
                {
                    return false;
                }

                // 检查机器人2数据端口监听状态
                if (!await EnsureRobotDataListening(_robot2Manager, "机器人2"))
                {
                    return false;
                }

                LogHelper.Info("数据端口监听启动完成");
                return true;

            }, false, "启动数据端口监听");
        }

        /// <summary>
        /// 确保单个机器人的数据端口正在监听
        /// </summary>
        /// <param name="robotManager">机器人管理器</param>
        /// <param name="robotName">机器人名称</param>
        /// <returns>确保结果</returns>
        private async Task<bool> EnsureRobotDataListening(dynamic robotManager, string robotName)
        {
            try
            {
                if (robotManager == null)
                {
                    LogHelper.Error($"{robotName}管理器为空");
                    return false;
                }

                // 检查数据端口连接状态
                if (!robotManager.IsDataConnected)
                {
                    LogHelper.Warning($"{robotName}数据端口未连接，尝试重新连接...");
                    if (!await robotManager.ConnectDataAsync())
                    {
                        LogHelper.Error($"{robotName}数据端口连接失败");
                        return false;
                    }
                }

                // 启动扫描（这会激活数据端口监听）
                if (!robotManager.IsScanning)
                {
                    LogHelper.Info($"{robotName}开始扫描数据端口...");
                    await robotManager.StartScanningAsync();
                }

                LogHelper.Info($"{robotName}数据端口监听状态正常");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"确保{robotName}数据端口监听失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 停止数据端口监听
        /// </summary>
        /// <returns>停止结果</returns>
        public async Task<bool> StopDataPortListeningAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info("开始停止数据端口监听...");

                // 停止机器人1自动化流程
                if (_robot1Manager != null && _robot1Manager.IsScanning)
                {
                    await _robot1Manager.StopAutomationAsync();
                    LogHelper.Info("机器人1自动化流程已停止");
                }

                // 停止机器人2自动化流程
                if (_robot2Manager != null && _robot2Manager.IsScanning)
                {
                    await _robot2Manager.StopAutomationAsync();
                    LogHelper.Info("机器人2自动化流程已停止");
                }

                LogHelper.Info("数据端口监听停止完成");
                return true;

            }, false, "停止数据端口监听");
        }

        /// <summary>
        /// 获取数据端口监听状态摘要
        /// </summary>
        /// <returns>监听状态摘要</returns>
        public string GetDataPortListeningStatus()
        {
            try
            {
                var status = "=== 数据端口监听状态 ===\n";

                // 机器人1状态
                if (_robot1Manager != null)
                {
                    status += $"机器人1:\n";
                    status += $"  数据端口连接: {(_robot1Manager.IsDataConnected ? "已连接" : "未连接")}\n";
                    status += $"  监听状态: {(_robot1Manager.IsScanning ? "监听中" : "未监听")}\n";
                    status += $"  等待手动输入: {(_robot1Manager.IsWaitingForManualInput ? "是" : "否")}\n";
                }
                else
                {
                    status += "机器人1: 管理器未初始化\n";
                }

                // 机器人2状态
                if (_robot2Manager != null)
                {
                    status += $"机器人2:\n";
                    status += $"  数据端口连接: {(_robot2Manager.IsDataConnected ? "已连接" : "未连接")}\n";
                    status += $"  监听状态: {(_robot2Manager.IsScanning ? "监听中" : "未监听")}\n";
                    status += $"  等待手动输入: {(_robot2Manager.IsWaitingForManualInput ? "是" : "否")}\n";
                }
                else
                {
                    status += "机器人2: 管理器未初始化\n";
                }

                // 消息队列状态
                status += $"消息队列: {_messageQueue.Count} 条待处理消息\n";

                return status;
            }
            catch (Exception ex)
            {
                LogHelper.Error("获取数据端口监听状态失败", ex);
                return "监听状态获取失败";
            }
        }

        /// <summary>
        /// 检查数据端口监听是否正常工作
        /// </summary>
        /// <returns>监听状态检查结果</returns>
        public bool IsDataPortListeningActive()
        {
            try
            {
                bool robot1Listening = _robot1Manager?.IsDataConnected == true && _robot1Manager?.IsScanning == true;
                bool robot2Listening = _robot2Manager?.IsDataConnected == true && _robot2Manager?.IsScanning == true;

                return robot1Listening && robot2Listening;
            }
            catch (Exception ex)
            {
                LogHelper.Error("检查数据端口监听状态失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取消息队列统计信息
        /// </summary>
        /// <returns>消息队列统计</returns>
        public string GetMessageQueueStats()
        {
            try
            {
                return $"消息队列统计: 当前队列长度={_messageQueue.Count}, 控制器状态={CurrentState}";
            }
            catch (Exception ex)
            {
                LogHelper.Error("获取消息队列统计失败", ex);
                return "消息队列统计获取失败";
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public async Task DisposeAsync()
        {
            try
            {
                LogHelper.Info("开始释放EpsonRobotAutoModeController资源...");

                // 停止自动模式
                await StopAsync();

                // 取消订阅事件
                UnsubscribeFromRobotEvents();

                // 清理状态
                _robotStates.Clear();
                _pendingMessages.Clear();

                // 清理队列
                while (_messageQueue.TryDequeue(out _)) { }
                while (_responseQueue.TryDequeue(out _)) { }

                LogHelper.Info("EpsonRobotAutoModeController资源释放完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("EpsonRobotAutoModeController资源释放失败", ex);
            }
        }

        /// <summary>
        /// 取消订阅机器人事件
        /// </summary>
        private void UnsubscribeFromRobotEvents()
        {
            try
            {
                if (_robot1Manager != null)
                {
                    _robot1Manager.SpecialCommandReceived -= OnRobot1SpecialCommandReceived;
                }

                if (_robot2Manager != null)
                {
                    _robot2Manager.SpecialCommandReceived -= OnRobot2SpecialCommandReceived;
                }

                LogHelper.Info("机器人事件取消订阅完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("机器人事件取消订阅失败", ex);
            }
        }
        #endregion

        #region 配置管理
        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfiguration()
        {
            try
            {
                _configuration = new EpsonAutoModeConfiguration();

                // 验证配置
                var validationResult = _configuration.Validate();
                if (!validationResult.IsValid)
                {
                    LogHelper.Warning($"配置验证失败: {validationResult.GetErrorSummary()}");
                }

                LogHelper.Info("配置加载完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("配置加载失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 更新配置
        /// </summary>
        /// <param name="newConfiguration">新配置</param>
        /// <returns>更新结果</returns>
        public bool UpdateConfiguration(EpsonAutoModeConfiguration newConfiguration)
        {
            try
            {
                if (newConfiguration == null)
                {
                    LogHelper.Warning("新配置为空，跳过更新");
                    return false;
                }

                var validationResult = newConfiguration.Validate();
                if (!validationResult.IsValid)
                {
                    LogHelper.Error($"新配置验证失败: {validationResult.GetErrorSummary()}");
                    return false;
                }

                _configuration = newConfiguration;
                LogHelper.Info("配置更新成功");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("配置更新失败", ex);
                return false;
            }
        }
        #endregion

        #region 控制方法
        /// <summary>
        /// 启动自动模式
        /// </summary>
        /// <returns>启动结果</returns>
        public async Task<bool> StartAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_isRunning)
                {
                    LogHelper.Warning("自动模式已经在运行中");
                    return true;
                }

                if (CurrentState != EpsonAutoModeState.Idle)
                {
                    LogHelper.Warning($"当前状态 {CurrentState} 不允许启动自动模式");
                    return false;
                }

                LogHelper.Info("开始启动自动模式...");

                // 检查依赖管理器状态
                if (!await CheckDependencyManagersStatus())
                {
                    CurrentState = EpsonAutoModeState.Error;
                    return false;
                }

                // 启动数据端口监听
                if (!await StartDataPortListeningAsync())
                {
                    LogHelper.Error("数据端口监听启动失败");
                    CurrentState = EpsonAutoModeState.Error;
                    return false;
                }

                // 创建取消令牌
                _cancellationTokenSource = new CancellationTokenSource();

                // 启动主控制循环
                _autoModeTask = Task.Run(() => AutoModeMainLoop(_cancellationTokenSource.Token));

                // 根据文档要求：初始化机器人准备状态字段
                await InitializeRobotReadyStatus();

                _isRunning = true;
                CurrentState = EpsonAutoModeState.Running;

                LogHelper.Info("自动模式启动成功");
                return true;

            }, false, "启动自动模式");
        }

        /// <summary>
        /// 停止自动模式
        /// </summary>
        /// <returns>停止结果</returns>
        public async Task<bool> StopAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (!_isRunning)
                {
                    LogHelper.Info("自动模式未运行，无需停止");
                    return true;
                }

                LogHelper.Info("开始停止自动模式...");
                CurrentState = EpsonAutoModeState.Stopping;

                // 发送Stop命令到两台机器人
                bool robot1Stopped = await SendRobotControlCommand(_robot1Manager, "Stop", "机器人1");
                bool robot2Stopped = await SendRobotControlCommand(_robot2Manager, "Stop", "机器人2");

                if (!robot1Stopped || !robot2Stopped)
                {
                    LogHelper.Warning("部分机器人停止命令发送失败，继续执行停止流程");
                }

                // 停止数据端口监听
                await StopDataPortListeningAsync();

                // 取消主控制循环
                _cancellationTokenSource?.Cancel();

                // 等待主控制循环结束
                if (_autoModeTask != null)
                {
                    await _autoModeTask;
                    _autoModeTask = null;
                }

                // 清理资源
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;

                _isRunning = false;
                CurrentState = EpsonAutoModeState.Idle;

                LogHelper.Info("自动模式停止成功");
                return true;

            }, false, "停止自动模式");
        }

        /// <summary>
        /// 暂停自动模式
        /// </summary>
        /// <returns>暂停结果</returns>
        public bool Pause()
        {
            try
            {
                if (CurrentState != EpsonAutoModeState.Running)
                {
                    LogHelper.Warning($"当前状态 {CurrentState} 不允许暂停");
                    return false;
                }

                CurrentState = EpsonAutoModeState.Paused;
                LogHelper.Info("自动模式已暂停");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("暂停自动模式失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 恢复自动模式
        /// </summary>
        /// <returns>恢复结果</returns>
        public bool Resume()
        {
            try
            {
                if (CurrentState != EpsonAutoModeState.Paused)
                {
                    LogHelper.Warning($"当前状态 {CurrentState} 不允许恢复");
                    return false;
                }

                CurrentState = EpsonAutoModeState.Running;
                LogHelper.Info("自动模式已恢复");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("恢复自动模式失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 暂停自动模式
        /// 根据EPSONRC+UsersGuide.MD: Pause命令暂停所有任务
        /// 仅供WorkflowManager内部调用
        /// </summary>
        /// <returns>暂停结果</returns>
        internal async Task<bool> PauseAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (!_isRunning)
                {
                    LogHelper.Warning("自动模式未运行，无法暂停");
                    return false;
                }

                if (CurrentState == EpsonAutoModeState.Paused)
                {
                    LogHelper.Info("自动模式已经暂停");
                    return true;
                }

                LogHelper.Info("开始暂停自动模式...");

                // 发送Pause命令到两台机器人
                bool robot1Paused = await SendRobotControlCommand(_robot1Manager, "Pause", "机器人1");
                bool robot2Paused = await SendRobotControlCommand(_robot2Manager, "Pause", "机器人2");

                if (robot1Paused && robot2Paused)
                {
                    CurrentState = EpsonAutoModeState.Paused;
                    LogHelper.Info("自动模式暂停成功");
                    return true;
                }
                else
                {
                    LogHelper.Error("自动模式暂停失败");
                    return false;
                }

            }, false, "暂停自动模式");
        }

        /// <summary>
        /// 恢复自动模式
        /// 根据EPSONRC+UsersGuide.MD: Continue命令继续已暂停的任务
        /// 仅供WorkflowManager内部调用
        /// </summary>
        /// <returns>恢复结果</returns>
        internal async Task<bool> ResumeAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (CurrentState != EpsonAutoModeState.Paused)
                {
                    LogHelper.Warning("自动模式未暂停，无法恢复");
                    return false;
                }

                LogHelper.Info("开始恢复自动模式...");

                // 发送Continue命令到两台机器人
                bool robot1Resumed = await SendRobotControlCommand(_robot1Manager, "Continue", "机器人1");
                bool robot2Resumed = await SendRobotControlCommand(_robot2Manager, "Continue", "机器人2");

                if (robot1Resumed && robot2Resumed)
                {
                    CurrentState = EpsonAutoModeState.Running;
                    LogHelper.Info("自动模式恢复成功");
                    return true;
                }
                else
                {
                    LogHelper.Error("自动模式恢复失败");
                    return false;
                }

            }, false, "恢复自动模式");
        }

        /// <summary>
        /// 重置控制器状态
        /// 根据EPSONRC+UsersGuide.MD: Reset命令清除紧急停止和错误
        /// </summary>
        /// <returns>重置结果</returns>
        public async Task<bool> ResetAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info("开始重置控制器状态...");

                // 发送Reset命令到两台机器人
                bool robot1Reset = await SendRobotControlCommand(_robot1Manager, "Reset", "机器人1");
                bool robot2Reset = await SendRobotControlCommand(_robot2Manager, "Reset", "机器人2");

                if (robot1Reset && robot2Reset)
                {
                    // 如果正在运行，先停止
                    if (_isRunning)
                    {
                        await StopAsync();
                    }

                    // 重置机器人状态
                    foreach (var robotId in _robotStates.Keys.ToArray())
                    {
                        SetRobotWorkflowState(robotId, RobotWorkflowState.Ready);
                    }

                    // 清理消息队列
                    while (_messageQueue.TryDequeue(out _)) { }
                    while (_responseQueue.TryDequeue(out _)) { }

                    // 清理待处理消息
                    _pendingMessages.Clear();

                    // 重置性能统计
                    _operationStartTimes.Clear();
                    _operationDurations.Clear();
                    _operationCounts.Clear();
                    _errorCounts.Clear();

                    CurrentState = EpsonAutoModeState.Idle;
                    LogHelper.Info("控制器状态重置完成");
                    return true;
                }
                else
                {
                    LogHelper.Error("控制器状态重置失败");
                    return false;
                }

            }, false, "重置控制器状态");
        }
        #endregion

        #region 连接管理方法
        /// <summary>
        /// 手动重新初始化机器人连接
        /// </summary>
        /// <returns>重新连接结果</returns>
        public async Task<bool> ReconnectRobotsAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info("开始手动重新连接机器人...");

                // 如果正在运行，先暂停
                bool wasRunning = _isRunning;
                if (wasRunning)
                {
                    Pause();
                }

                try
                {
                    // 重新初始化机器人连接
                    bool result = await InitializeRobotConnections();

                    if (result)
                    {
                        LogHelper.Info("机器人重新连接成功");

                        // 如果之前在运行，恢复运行
                        if (wasRunning)
                        {
                            Resume();
                        }
                    }
                    else
                    {
                        LogHelper.Error("机器人重新连接失败");
                        CurrentState = EpsonAutoModeState.Error;
                    }

                    return result;
                }
                catch (Exception ex)
                {
                    LogHelper.Error("机器人重新连接异常", ex);
                    CurrentState = EpsonAutoModeState.Error;
                    return false;
                }

            }, false, "手动重新连接机器人");
        }

        /// <summary>
        /// 获取机器人连接状态摘要
        /// </summary>
        /// <returns>连接状态摘要</returns>
        public string GetRobotConnectionSummary()
        {
            try
            {
                var summary = "=== 机器人连接状态 ===\n";

                // 机器人1状态
                if (_robot1Manager != null)
                {
                    summary += $"机器人1:\n";
                    summary += $"  主端口连接: {(_robot1Manager.IsStartStopConnected ? "已连接" : "未连接")}\n";
                    summary += $"  数据端口连接: {(_robot1Manager.IsDataConnected ? "已连接" : "未连接")}\n";
                    summary += $"  登录状态: {(_robot1Manager.IsLoggedIn ? "已登录" : "未登录")}\n";
                }
                else
                {
                    summary += "机器人1: 管理器未初始化\n";
                }

                // 机器人2状态
                if (_robot2Manager != null)
                {
                    summary += $"机器人2:\n";
                    summary += $"  主端口连接: {(_robot2Manager.IsStartStopConnected ? "已连接" : "未连接")}\n";
                    summary += $"  数据端口连接: {(_robot2Manager.IsDataConnected ? "已连接" : "未连接")}\n";
                    summary += $"  登录状态: {(_robot2Manager.IsLoggedIn ? "已登录" : "未登录")}\n";
                }
                else
                {
                    summary += "机器人2: 管理器未初始化\n";
                }

                return summary;
            }
            catch (Exception ex)
            {
                LogHelper.Error("获取机器人连接状态摘要失败", ex);
                return "连接状态获取失败";
            }
        }

        /// <summary>
        /// 检查是否所有机器人都已正确连接
        /// </summary>
        /// <returns>是否全部连接</returns>
        public bool AreAllRobotsConnected()
        {
            try
            {
                bool robot1Connected = _robot1Manager?.IsStartStopConnected == true &&
                                     _robot1Manager?.IsDataConnected == true &&
                                     _robot1Manager?.IsLoggedIn == true;

                bool robot2Connected = _robot2Manager?.IsStartStopConnected == true &&
                                     _robot2Manager?.IsDataConnected == true &&
                                     _robot2Manager?.IsLoggedIn == true;

                return robot1Connected && robot2Connected;
            }
            catch (Exception ex)
            {
                LogHelper.Error("检查机器人连接状态失败", ex);
                return false;
            }
        }
        #endregion

        #region 事件处理
        /// <summary>
        /// 机器人1特殊命令接收事件处理
        /// </summary>
        /// <param name="sender">发送者</param>
        /// <param name="e">事件参数</param>
        private void OnRobot1SpecialCommandReceived(object sender, SpecialCommandReceivedEventArgs e)
        {
            try
            {
                var robotMessage = new RobotMessage(_configuration.Robot1Id,
                    RobotMessage.ParseMessageType(e.CommandString), e.CommandString);

                _messageQueue.Enqueue(robotMessage);
                OnMessageReceived(new RobotMessageReceivedEventArgs(robotMessage));

                LogHelper.Info($"机器人1特殊命令接收: {e.CommandString}");
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理机器人1特殊命令失败", ex);
                OnErrorOccurred(new AutoModeErrorEventArgs("处理机器人1特殊命令失败", _configuration.Robot1Id, CurrentState, ex));
            }
        }

        /// <summary>
        /// 机器人2特殊命令接收事件处理
        /// </summary>
        /// <param name="sender">发送者</param>
        /// <param name="e">事件参数</param>
        private void OnRobot2SpecialCommandReceived(object sender, SpecialCommandReceivedEventArgs e)
        {
            try
            {
                var robotMessage = new RobotMessage(_configuration.Robot2Id,
                    RobotMessage.ParseMessageType(e.CommandString), e.CommandString);

                _messageQueue.Enqueue(robotMessage);
                OnMessageReceived(new RobotMessageReceivedEventArgs(robotMessage));

                LogHelper.Info($"机器人2特殊命令接收: {e.CommandString}");
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理机器人2特殊命令失败", ex);
                OnErrorOccurred(new AutoModeErrorEventArgs("处理机器人2特殊命令失败", _configuration.Robot2Id, CurrentState, ex));
            }
        }
        #endregion

        #region 事件触发方法
        /// <summary>
        /// 触发状态变化事件（异步执行避免阻塞）
        /// </summary>
        /// <param name="e">事件参数</param>
        protected virtual void OnStateChanged(AutoModeStateChangedEventArgs e)
        {
            LogHelper.Info($"控制器状态变化: {e.OldState} -> {e.NewState}");

            // 异步触发事件，避免阻塞主循环
            if (StateChanged != null)
            {
                Task.Run(() =>
                {
                    try
                    {
                        StateChanged.Invoke(this, e);
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error("触发状态变化事件时发生异常", ex);
                    }
                });
            }
        }

        /// <summary>
        /// 触发消息接收事件（异步执行避免阻塞）
        /// </summary>
        /// <param name="e">事件参数</param>
        protected virtual void OnMessageReceived(RobotMessageReceivedEventArgs e)
        {
            // 异步触发事件，避免阻塞消息处理
            if (MessageReceived != null)
            {
                Task.Run(() =>
                {
                    try
                    {
                        MessageReceived.Invoke(this, e);
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error("触发消息接收事件时发生异常", ex);
                    }
                });
            }
        }

        /// <summary>
        /// 触发工作流状态变化事件（异步执行避免阻塞）
        /// </summary>
        /// <param name="e">事件参数</param>
        protected virtual void OnWorkflowStateChanged(RobotWorkflowStateChangedEventArgs e)
        {
            LogHelper.Info($"机器人{e.RobotId}工作流状态变化: {e.OldState} -> {e.NewState}");

            // 异步触发事件，避免阻塞状态管理
            if (WorkflowStateChanged != null)
            {
                Task.Run(() =>
                {
                    try
                    {
                        WorkflowStateChanged.Invoke(this, e);
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error("触发工作流状态变化事件时发生异常", ex);
                    }
                });
            }
        }

        /// <summary>
        /// 触发工作流完成事件（异步执行避免阻塞）
        /// </summary>
        /// <param name="e">事件参数</param>
        protected virtual void OnWorkflowCompleted(RobotWorkflowCompletedEventArgs e)
        {
            LogHelper.Info($"机器人{e.RobotId}工作流完成: {e.WorkflowType}, 成功: {e.IsSuccess}");

            // 异步触发事件，避免阻塞工作流处理
            if (WorkflowCompleted != null)
            {
                Task.Run(() =>
                {
                    try
                    {
                        WorkflowCompleted.Invoke(this, e);
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error("触发工作流完成事件时发生异常", ex);
                    }
                });
            }
        }

        /// <summary>
        /// 触发错误事件（异步执行避免阻塞）
        /// </summary>
        /// <param name="e">事件参数</param>
        protected virtual void OnErrorOccurred(AutoModeErrorEventArgs e)
        {
            LogHelper.Error($"自动模式错误: {e.ErrorMessage}");

            // 异步触发事件，避免阻塞错误处理
            if (ErrorOccurred != null)
            {
                Task.Run(() =>
                {
                    try
                    {
                        ErrorOccurred.Invoke(this, e);
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error("触发错误事件时发生异常", ex);
                    }
                });
            }
        }

        /// <summary>
        /// 触发响应发送事件（异步执行避免阻塞）
        /// </summary>
        /// <param name="e">事件参数</param>
        protected virtual void OnResponseSent(ResponseSentEventArgs e)
        {
            LogHelper.Info($"响应发送: 机器人{e.Response.TargetRobotId}, 类型: {e.Response.ResponseType}, 成功: {e.IsSuccess}");

            // 异步触发事件，避免阻塞响应发送
            if (ResponseSent != null)
            {
                Task.Run(() =>
                {
                    try
                    {
                        ResponseSent.Invoke(this, e);
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error("触发响应发送事件时发生异常", ex);
                    }
                });
            }
        }
        #endregion

        #region 辅助方法
        /// <summary>
        /// 检查依赖管理器状态
        /// </summary>
        /// <returns>检查结果</returns>
        private async Task<bool> CheckDependencyManagersStatus()
        {
            try
            {
                // 检查机器人管理器
                if (_robot1Manager == null || _robot2Manager == null)
                {
                    LogHelper.Error("机器人管理器未初始化");
                    return false;
                }

                // 检查IO管理器
                if (_ioManager == null)
                {
                    LogHelper.Error("IO管理器未初始化");
                    return false;
                }

                // 检查扫码器管理器
                if (_scannerManager == null || !_scannerManager.IsInitialized)
                {
                    LogHelper.Error("扫码器管理器未初始化");
                    return false;
                }

                // 检查机器人连接状态
                if (!await CheckRobotConnectionStatus())
                {
                    LogHelper.Error("机器人连接状态检查失败");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error("检查依赖管理器状态失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 检查机器人连接状态
        /// </summary>
        /// <returns>连接状态检查结果</returns>
        private async Task<bool> CheckRobotConnectionStatus()
        {
            try
            {
                LogHelper.Info("检查机器人连接状态...");

                // 检查机器人1连接状态
                bool robot1Connected = await CheckSingleRobotConnection(_robot1Manager, "机器人1");

                // 检查机器人2连接状态
                bool robot2Connected = await CheckSingleRobotConnection(_robot2Manager, "机器人2");

                bool allConnected = robot1Connected && robot2Connected;
                LogHelper.Info($"机器人连接状态检查完成: 机器人1={robot1Connected}, 机器人2={robot2Connected}, 总体={allConnected}");

                return allConnected;
            }
            catch (Exception ex)
            {
                LogHelper.Error("检查机器人连接状态失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 检查单个机器人的连接状态
        /// </summary>
        /// <param name="robotManager">机器人管理器</param>
        /// <param name="robotName">机器人名称</param>
        /// <returns>连接状态</returns>
        private async Task<bool> CheckSingleRobotConnection(dynamic robotManager, string robotName)
        {
            try
            {
                // 检查主端口连接状态
                bool startStopConnected = robotManager.IsStartStopConnected;

                // 检查数据端口连接状态
                bool dataConnected = robotManager.IsDataConnected;

                // 检查登录状态
                bool isLoggedIn = robotManager.IsLoggedIn;

                LogHelper.Info($"{robotName}连接状态: 主端口={startStopConnected}, 数据端口={dataConnected}, 已登录={isLoggedIn}");

                // 如果连接断开，尝试重新连接
                if (!startStopConnected || !dataConnected || !isLoggedIn)
                {
                    LogHelper.Warning($"{robotName}连接状态异常，尝试重新连接...");
                    return await InitializeSingleRobotConnection(robotManager, robotName);
                }

                return true;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"检查{robotName}连接状态失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 设置机器人工作流状态
        /// </summary>
        /// <param name="robotId">机器人ID</param>
        /// <param name="newState">新状态</param>
        private void SetRobotWorkflowState(int robotId, RobotWorkflowState newState)
        {
            try
            {
                if (_robotStates.ContainsKey(robotId))
                {
                    var oldState = _robotStates[robotId];
                    if (oldState != newState)
                    {
                        _robotStates[robotId] = newState;
                        OnWorkflowStateChanged(new RobotWorkflowStateChangedEventArgs(robotId, oldState, newState));
                    }
                }
                else
                {
                    _robotStates[robotId] = newState;
                    OnWorkflowStateChanged(new RobotWorkflowStateChangedEventArgs(robotId, RobotWorkflowState.Ready, newState));
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"设置机器人{robotId}工作流状态失败", ex);
            }
        }

        /// <summary>
        /// 主控制循环
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务</returns>
        private async Task AutoModeMainLoop(CancellationToken cancellationToken)
        {
            LogHelper.Info("自动模式主控制循环开始");

            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    // 如果暂停，等待恢复
                    if (CurrentState == EpsonAutoModeState.Paused)
                    {
                        await Task.Delay(100, cancellationToken);
                        continue;
                    }

                    // 处理待处理的消息队列
                    await ProcessPendingMessages(cancellationToken);

                    // 处理待发送的响应队列
                    await ProcessPendingResponses(cancellationToken);

                    // 检查机器人状态
                    await CheckRobotStates(cancellationToken);

                    // 执行定期维护任务
                    await PerformMaintenance(cancellationToken);

                    // 短暂延迟
                    await Task.Delay(_configuration.StateCheckIntervalMs, cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                LogHelper.Info("自动模式主控制循环被取消");
            }
            catch (Exception ex)
            {
                LogHelper.Error("自动模式主控制循环异常", ex);
                CurrentState = EpsonAutoModeState.Error;
                OnErrorOccurred(new AutoModeErrorEventArgs("主控制循环异常", ex, "MainLoop"));
            }
            finally
            {
                LogHelper.Info("自动模式主控制循环结束");
            }
        }

        /// <summary>
        /// 处理待处理的消息
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务</returns>
        private async Task ProcessPendingMessages(CancellationToken cancellationToken)
        {
            try
            {
                while (_messageQueue.TryDequeue(out RobotMessage message) && !cancellationToken.IsCancellationRequested)
                {
                    await ProcessRobotMessage(message, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理待处理消息失败", ex);
                OnErrorOccurred(new AutoModeErrorEventArgs("处理待处理消息失败", ex, "MessageProcessing"));
            }
        }

        /// <summary>
        /// 处理待发送的响应
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务</returns>
        private async Task ProcessPendingResponses(CancellationToken cancellationToken)
        {
            try
            {
                while (_responseQueue.TryDequeue(out ControllerResponse response) && !cancellationToken.IsCancellationRequested)
                {
                    await SendResponseToRobot(response, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("处理待发送响应失败", ex);
                OnErrorOccurred(new AutoModeErrorEventArgs("处理待发送响应失败", ex, "ResponseProcessing"));
            }
        }

        /// <summary>
        /// 检查机器人状态
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务</returns>
        private async Task CheckRobotStates(CancellationToken cancellationToken)
        {
            try
            {
                // 这里可以添加定期状态检查逻辑
                // 例如检查超时、状态异常等
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                LogHelper.Error("检查机器人状态失败", ex);
                OnErrorOccurred(new AutoModeErrorEventArgs("检查机器人状态失败", ex, "StateCheck"));
            }
        }

        /// <summary>
        /// 执行定期维护任务
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务</returns>
        private async Task PerformMaintenance(CancellationToken cancellationToken)
        {
            try
            {
                // 定期检查连接状态
                await PerformConnectionHealthCheck(cancellationToken);

                // 清理过期数据
                await CleanupExpiredData(cancellationToken);

                // 性能统计
                await UpdatePerformanceStatistics(cancellationToken);

                // 错误恢复检查
                await CheckAndRecoverFromErrors(cancellationToken);
            }
            catch (Exception ex)
            {
                LogHelper.Error("执行定期维护任务失败", ex);
                OnErrorOccurred(new AutoModeErrorEventArgs("执行定期维护任务失败", ex, "Maintenance"));
            }
        }

        /// <summary>
        /// 执行连接健康检查
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务</returns>
        private async Task PerformConnectionHealthCheck(CancellationToken cancellationToken)
        {
            try
            {
                // 每隔一定时间检查连接状态
                if (_lastConnectionCheck == null ||
                    (DateTime.Now - _lastConnectionCheck.Value).TotalMilliseconds >= _configuration.ConnectionCheckIntervalMs)
                {
                    LogHelper.Debug("执行连接健康检查...");

                    bool allConnected = await CheckRobotConnectionStatus();
                    if (!allConnected && _configuration.AutoReconnect)
                    {
                        LogHelper.Warning("检测到连接异常，尝试自动重连...");
                        await ReconnectRobotsAsync();
                    }

                    _lastConnectionCheck = DateTime.Now;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("连接健康检查失败", ex);
            }
        }

        /// <summary>
        /// 清理过期数据
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务</returns>
        private async Task CleanupExpiredData(CancellationToken cancellationToken)
        {
            try
            {
                // 清理过期的响应数据
                // 这里可以添加具体的清理逻辑
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                LogHelper.Error("清理过期数据失败", ex);
            }
        }

        /// <summary>
        /// 更新性能统计
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务</returns>
        private async Task UpdatePerformanceStatistics(CancellationToken cancellationToken)
        {
            try
            {
                if (_configuration.EnablePerformanceMonitoring)
                {
                    // 更新性能统计数据
                    // 这里可以添加具体的统计逻辑
                    await Task.CompletedTask;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("更新性能统计失败", ex);
            }
        }

        /// <summary>
        /// 检查并从错误中恢复
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务</returns>
        private async Task CheckAndRecoverFromErrors(CancellationToken cancellationToken)
        {
            try
            {
                // 检查是否有机器人处于错误状态
                if (HasRobotErrors() && _configuration.AutoRecover)
                {
                    LogHelper.Info("检测到错误状态，尝试自动恢复...");
                    await AttemptErrorRecovery(cancellationToken);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("错误恢复检查失败", ex);
            }
        }

        /// <summary>
        /// 尝试错误恢复
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务</returns>
        private async Task AttemptErrorRecovery(CancellationToken cancellationToken)
        {
            try
            {
                LogHelper.Info("开始错误恢复流程...");

                // 重置错误状态的机器人
                foreach (var robotState in _robotStates.ToList())
                {
                    if (robotState.Value == RobotWorkflowState.Error)
                    {
                        LogHelper.Info($"重置机器人{robotState.Key}错误状态");
                        SetRobotWorkflowState(robotState.Key, RobotWorkflowState.Ready);
                    }
                }

                // 如果控制器处于错误状态，尝试恢复
                if (CurrentState == EpsonAutoModeState.Error)
                {
                    LogHelper.Info("重置控制器错误状态");
                    CurrentState = EpsonAutoModeState.Running;
                }

                // 等待恢复延迟时间
                await Task.Delay(_configuration.AutoRecoveryDelayMs, cancellationToken);

                LogHelper.Info("错误恢复流程完成");
            }
            catch (Exception ex)
            {
                LogHelper.Error("错误恢复失败", ex);
            }
        }

        /// <summary>
        /// 处理机器人消息
        /// </summary>
        /// <param name="message">机器人消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务</returns>
        private async Task ProcessRobotMessage(RobotMessage message, CancellationToken cancellationToken)
        {
            try
            {
                if (message == null || message.IsProcessed)
                {
                    return;
                }

                LogHelper.Info($"开始处理机器人{message.RobotId}消息: {message.MessageType}");

                // 根据消息类型处理
                switch (message.MessageType)
                {
                    case RobotMessageType.GETPICK:
                        await HandleGetPickMessage(message, cancellationToken);
                        break;
                    case RobotMessageType.INPICK:
                        await HandleInPickMessage(message, cancellationToken);
                        break;
                    case RobotMessageType.GETNGPUT:
                        await HandleGetNGPutMessage(message, cancellationToken);
                        break;
                    case RobotMessageType.NGPUTFULL:
                        await HandleNGPutFullMessage(message, cancellationToken);
                        break;
                    case RobotMessageType.GETOKPUT:
                        await HandleGetOKPutMessage(message, cancellationToken);
                        break;
                    default:
                        LogHelper.Warning($"未知消息类型: {message.MessageType}");
                        break;
                }

                message.IsProcessed = true;
                message.ProcessResult = "处理完成";

                LogHelper.Info($"机器人{message.RobotId}消息处理完成: {message.MessageType}");
            }
            catch (Exception ex)
            {
                LogHelper.Error($"处理机器人{message.RobotId}消息失败: {message.MessageType}", ex);
                message.ProcessResult = $"处理失败: {ex.Message}";
                OnErrorOccurred(new AutoModeErrorEventArgs($"处理机器人{message.RobotId}消息失败", message.RobotId, CurrentState, ex));
            }
        }

        /// <summary>
        /// 发送响应给机器人
        /// </summary>
        /// <param name="response">控制器响应</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务</returns>
        private async Task SendResponseToRobot(ControllerResponse response, CancellationToken cancellationToken)
        {
            try
            {
                if (response == null || response.IsSent)
                {
                    return;
                }

                bool sendResult = false;
                string responseString = response.GetResponseString();

                // 根据目标机器人发送响应
                if (response.TargetRobotId == _configuration.Robot1Id && _robot1Manager != null)
                {
                    var result = await _robot1Manager.SendCustomCommandAsync(responseString, "Data");
                    sendResult = result != null;
                }
                else if (response.TargetRobotId == _configuration.Robot2Id && _robot2Manager != null)
                {
                    var result = await _robot2Manager.SendCustomCommandAsync(responseString, "Data");
                    sendResult = result != null;
                }
                else
                {
                    LogHelper.Error($"无效的目标机器人ID: {response.TargetRobotId}");
                    sendResult = false;
                }

                response.IsSent = sendResult;
                response.SentTime = sendResult ? DateTime.Now : (DateTime?)null;

                OnResponseSent(new ResponseSentEventArgs(response, sendResult,
                    sendResult ? "发送成功" : "发送失败"));

                LogHelper.Info($"响应发送结果: 机器人{response.TargetRobotId}, 类型: {response.ResponseType}, 成功: {sendResult}");
            }
            catch (Exception ex)
            {
                LogHelper.Error($"发送响应给机器人{response.TargetRobotId}失败", ex);
                OnResponseSent(new ResponseSentEventArgs(response, false, $"发送异常: {ex.Message}"));
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                // 使用ConfigureAwait(false)避免死锁
                StopAsync().ConfigureAwait(false).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                LogHelper.Error("停止自动模式时发生异常", ex);
            }
            finally
            {
                // 释放取消令牌源
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;

                LogHelper.Info("EpsonRobotAutoModeController资源已释放");
            }
        }
        #endregion
    }
}
