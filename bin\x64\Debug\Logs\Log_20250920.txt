[2025-09-20 01:27:16.087] [INFO] 程序启动开始
[2025-09-20 01:27:16.088] [INFO] 配置系统初始化成功
[2025-09-20 01:27:16.151] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-20 01:27:16.151] [INFO] 配置系统初始化完成
[2025-09-20 01:27:16.151] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-20 01:27:16.174] [INFO] 开始初始化各个Manager...
[2025-09-20 01:27:16.175] [INFO] 初始化基础Manager...
[2025-09-20 01:27:16.181] [INFO] IO状态缓存初始化完成
[2025-09-20 01:27:16.186] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-20 01:27:16.186] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-20 01:27:16.188] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-20 01:27:16.188] [ERROR] DMC1000B控制卡初始化失败
[2025-09-20 01:27:16.188] [WARN] DMC1000BIO管理器初始化失败
[2025-09-20 01:27:16.193] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-20 01:27:16.193] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-20 01:27:16.193] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-20 01:27:16.194] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-20 01:27:16.233] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__42_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 130
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:27:16.246] [WARN] DMC1000B电机管理器初始化失败
[2025-09-20 01:27:16.246] [INFO] 初始化系统模式管理器...
[2025-09-20 01:27:16.250] [INFO] 开始初始化MotorManager...
[2025-09-20 01:27:16.252] [INFO] 模拟初始化运动控制卡
[2025-09-20 01:27:16.458] [INFO] 加载了8个电机的默认配置
[2025-09-20 01:27:16.459] [INFO] 电机监控任务已启动
[2025-09-20 01:27:16.459] [INFO] MotorManager初始化完成
[2025-09-20 01:27:16.460] [INFO] 初始化通信Manager...
[2025-09-20 01:27:16.461] [INFO] 电机监控循环开始
[2025-09-20 01:27:16.463] [INFO] 开始初始化ScannerManager...
[2025-09-20 01:27:16.466] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-20 01:27:16.468] [INFO] 串口初始化完成
[2025-09-20 01:27:16.470] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-20 01:27:16.472] [INFO] 扫描枪连接成功: COM1
[2025-09-20 01:27:16.474] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-20 01:27:16.474] [INFO] ScannerManager初始化完成
[2025-09-20 01:27:16.476] [INFO] 开始初始化ModbusTcpManager...
[2025-09-20 01:27:16.478] [INFO] Modbus TCP配置: *************:502, SlaveId: 1
[2025-09-20 01:27:16.481] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-20 01:27:21.544] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: *************:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:27:21.544] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-20 01:27:21.545] [INFO] ModbusTcpManager初始化完成
[2025-09-20 01:27:21.548] [INFO] 开始初始化RobotTcpManager...
[2025-09-20 01:27:21.549] [INFO] 机器人TCP配置: *************:8080
[2025-09-20 01:27:21.552] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-20 01:27:26.593] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: *************:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:27:26.593] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-20 01:27:26.594] [INFO] RobotTcpManager初始化完成
[2025-09-20 01:27:26.594] [INFO] 初始化视觉Manager...
[2025-09-20 01:27:26.597] [INFO] 开始初始化VisionManager...
[2025-09-20 01:27:26.597] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-20 01:27:26.598] [INFO] 模拟初始化相机，索引: 0
[2025-09-20 01:27:27.110] [INFO] 相机初始化成功
[2025-09-20 01:27:27.113] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-20 01:27:27.114] [INFO] 视觉配置加载完成
[2025-09-20 01:27:27.114] [INFO] VisionManager初始化完成
[2025-09-20 01:27:27.115] [INFO] 初始化数据Manager...
[2025-09-20 01:27:27.122] [INFO] 开始初始化StatisticsManager...
[2025-09-20 01:27:27.124] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-20 01:27:27.135] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-20 01:27:27.136] [INFO] 历史数据加载完成
[2025-09-20 01:27:27.137] [INFO] 自动保存任务已启动
[2025-09-20 01:27:27.137] [INFO] StatisticsManager初始化完成
[2025-09-20 01:27:27.138] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-20 01:27:27.138] [INFO] 所有Manager初始化完成
[2025-09-20 01:27:27.142] [INFO] 自动保存循环开始
[2025-09-20 01:27:27.211] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-20 01:27:27.212] [INFO] 主界面布局创建完成
[2025-09-20 01:27:27.213] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-20 01:27:31.249] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-20 01:27:31.250] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-20 01:27:31.332] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 脉冲当量不能大于0.1
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:27:31.390] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 脉冲当量不能大于0.1
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:27:31.390] [INFO] 翻转电机参数初始化完成
[2025-09-20 01:27:31.390] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-20 01:27:40.432] [INFO] 翻转电机控制面板资源释放完成
[2025-09-20 01:27:40.451] [INFO] 皮带电机控制面板初始化完成 - 双电机设计
[2025-09-20 01:27:40.451] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-20 01:27:40.513] [ERROR] 设置皮带电机轴3参数 执行失败
异常详情: 皮带电机参数无效: 脉冲当量不能大于0.1
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass50_0.<<SetBeltMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 479
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:27:40.567] [ERROR] 设置皮带电机轴2参数 执行失败
异常详情: 皮带电机参数无效: 脉冲当量不能大于0.1
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass50_0.<<SetBeltMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 479
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:27:40.567] [INFO] 皮带电机参数初始化完成
[2025-09-20 01:27:40.568] [INFO] 皮带电机控制面板业务逻辑初始化完成
[2025-09-20 01:27:41.960] [INFO] 皮带电机控制面板资源释放完成
[2025-09-20 01:27:41.966] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-20 01:27:41.967] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-20 01:27:42.021] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 脉冲当量不能大于0.1
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:27:42.073] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 脉冲当量不能大于0.1
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:27:42.074] [INFO] 翻转电机参数初始化完成
[2025-09-20 01:27:42.074] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-20 01:27:44.865] [INFO] 翻转电机控制面板资源释放完成
[2025-09-20 01:27:44.874] [INFO] 开始初始化EpsonRobotManager...
[2025-09-20 01:27:44.875] [INFO] Epson机器人配置 - 启动/停止: *************:5000, 数据收发: *************:5001
[2025-09-20 01:27:44.876] [INFO] EpsonRobotManager初始化完成
[2025-09-20 01:27:44.876] [INFO] Epson机器人管理器初始化完成
[2025-09-20 01:27:44.884] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-20 01:27:45.998] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-20 01:27:45.998] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-20 01:27:46.057] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 脉冲当量不能大于0.1
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:27:46.114] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 脉冲当量不能大于0.1
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:27:46.114] [INFO] 翻转电机参数初始化完成
[2025-09-20 01:27:46.114] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-20 01:27:58.904] [INFO] 翻转电机控制面板资源释放完成
[2025-09-20 01:27:58.912] [INFO] 皮带电机控制面板初始化完成 - 双电机设计
[2025-09-20 01:27:58.912] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-20 01:27:58.966] [ERROR] 设置皮带电机轴3参数 执行失败
异常详情: 皮带电机参数无效: 脉冲当量不能大于0.1
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass50_0.<<SetBeltMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 479
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:27:59.020] [ERROR] 设置皮带电机轴2参数 执行失败
异常详情: 皮带电机参数无效: 脉冲当量不能大于0.1
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass50_0.<<SetBeltMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 479
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:27:59.021] [INFO] 皮带电机参数初始化完成
[2025-09-20 01:27:59.021] [INFO] 皮带电机控制面板业务逻辑初始化完成
[2025-09-20 01:28:05.344] [INFO] 皮带电机控制面板资源释放完成
[2025-09-20 01:28:05.349] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-20 01:28:05.349] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-20 01:28:05.403] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 脉冲当量不能大于0.1
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:28:05.456] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 脉冲当量不能大于0.1
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:28:05.456] [INFO] 翻转电机参数初始化完成
[2025-09-20 01:28:05.456] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-20 01:30:09.746] [INFO] 翻转电机控制面板资源释放完成
[2025-09-20 01:35:58.183] [INFO] 程序启动开始
[2025-09-20 01:35:58.184] [INFO] 配置系统初始化成功
[2025-09-20 01:35:58.234] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-20 01:35:58.235] [INFO] 配置系统初始化完成
[2025-09-20 01:35:58.235] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-20 01:35:58.258] [INFO] 开始初始化各个Manager...
[2025-09-20 01:35:58.259] [INFO] 初始化基础Manager...
[2025-09-20 01:35:58.266] [INFO] IO状态缓存初始化完成
[2025-09-20 01:35:58.273] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-20 01:35:58.273] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-20 01:35:58.275] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-20 01:35:58.275] [ERROR] DMC1000B控制卡初始化失败
[2025-09-20 01:35:58.276] [WARN] DMC1000BIO管理器初始化失败
[2025-09-20 01:35:58.280] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-20 01:35:58.280] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-20 01:35:58.280] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-20 01:35:58.281] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-20 01:35:58.320] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__42_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 130
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:35:58.334] [WARN] DMC1000B电机管理器初始化失败
[2025-09-20 01:35:58.334] [INFO] 初始化系统模式管理器...
[2025-09-20 01:35:58.338] [INFO] 开始初始化MotorManager...
[2025-09-20 01:35:58.339] [INFO] 模拟初始化运动控制卡
[2025-09-20 01:35:58.546] [INFO] 加载了8个电机的默认配置
[2025-09-20 01:35:58.547] [INFO] 电机监控任务已启动
[2025-09-20 01:35:58.547] [INFO] MotorManager初始化完成
[2025-09-20 01:35:58.547] [INFO] 初始化通信Manager...
[2025-09-20 01:35:58.548] [INFO] 电机监控循环开始
[2025-09-20 01:35:58.550] [INFO] 开始初始化ScannerManager...
[2025-09-20 01:35:58.552] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-20 01:35:58.554] [INFO] 串口初始化完成
[2025-09-20 01:35:58.556] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-20 01:35:58.558] [INFO] 扫描枪连接成功: COM1
[2025-09-20 01:35:58.558] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-20 01:35:58.559] [INFO] ScannerManager初始化完成
[2025-09-20 01:35:58.561] [INFO] 开始初始化ModbusTcpManager...
[2025-09-20 01:35:58.563] [INFO] Modbus TCP配置: *************:502, SlaveId: 1
[2025-09-20 01:35:58.565] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-20 01:36:03.613] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: *************:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:36:03.614] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-20 01:36:03.614] [INFO] ModbusTcpManager初始化完成
[2025-09-20 01:36:03.616] [INFO] 开始初始化RobotTcpManager...
[2025-09-20 01:36:03.617] [INFO] 机器人TCP配置: *************:8080
[2025-09-20 01:36:03.619] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-20 01:36:08.733] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: *************:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:36:08.734] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-20 01:36:08.734] [INFO] RobotTcpManager初始化完成
[2025-09-20 01:36:08.734] [INFO] 初始化视觉Manager...
[2025-09-20 01:36:08.737] [INFO] 开始初始化VisionManager...
[2025-09-20 01:36:08.737] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-20 01:36:08.738] [INFO] 模拟初始化相机，索引: 0
[2025-09-20 01:36:09.255] [INFO] 相机初始化成功
[2025-09-20 01:36:09.258] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-20 01:36:09.258] [INFO] 视觉配置加载完成
[2025-09-20 01:36:09.259] [INFO] VisionManager初始化完成
[2025-09-20 01:36:09.259] [INFO] 初始化数据Manager...
[2025-09-20 01:36:09.266] [INFO] 开始初始化StatisticsManager...
[2025-09-20 01:36:09.267] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-20 01:36:09.274] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-20 01:36:09.274] [INFO] 历史数据加载完成
[2025-09-20 01:36:09.274] [INFO] 自动保存任务已启动
[2025-09-20 01:36:09.274] [INFO] StatisticsManager初始化完成
[2025-09-20 01:36:09.275] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-20 01:36:09.275] [INFO] 所有Manager初始化完成
[2025-09-20 01:36:09.276] [INFO] 自动保存循环开始
[2025-09-20 01:36:09.325] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-20 01:36:09.325] [INFO] 主界面布局创建完成
[2025-09-20 01:36:09.326] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-20 01:36:13.596] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-20 01:36:13.596] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-20 01:36:13.668] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:36:13.723] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:36:13.724] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-20 01:36:13.724] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-20 01:36:16.549] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:36:16.549] [INFO] 左翻转电机回零方向已设置为: 正方向
[2025-09-20 01:36:17.470] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:36:17.470] [INFO] 左翻转电机回零方向已设置为: 负方向
[2025-09-20 01:36:30.523] [INFO] 翻转电机控制面板资源释放完成
[2025-09-20 01:36:30.527] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-20 01:36:31.663] [INFO] 开始初始化EpsonRobotManager...
[2025-09-20 01:36:31.664] [INFO] Epson机器人配置 - 启动/停止: *************:5000, 数据收发: *************:5001
[2025-09-20 01:36:31.664] [INFO] EpsonRobotManager初始化完成
[2025-09-20 01:36:31.665] [INFO] Epson机器人管理器初始化完成
[2025-09-20 01:36:31.691] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-20 01:36:32.407] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-20 01:36:32.407] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-20 01:36:32.468] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:36:32.526] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:36:32.527] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-20 01:36:32.528] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-20 01:36:33.035] [INFO] 翻转电机控制面板资源释放完成
[2025-09-20 01:36:33.040] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-20 01:36:34.571] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-20 01:36:35.098] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
[2025-09-20 01:36:38.051] [INFO] IO输出控制面板初始化完成 - 支持基础IO和扩展IO
[2025-09-20 01:36:41.074] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
[2025-09-20 01:36:42.921] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-20 01:36:42.921] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-20 01:36:42.977] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:36:43.035] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 01:36:43.035] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-20 01:36:43.035] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-20 01:36:43.723] [INFO] 翻转电机控制面板资源释放完成
[2025-09-20 01:36:43.727] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-20 14:36:54.595] [INFO] 程序启动开始
[2025-09-20 14:36:54.597] [INFO] 配置系统初始化成功
[2025-09-20 14:36:54.786] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-20 14:36:54.786] [INFO] 配置系统初始化完成
[2025-09-20 14:36:54.786] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-20 14:36:54.825] [INFO] 开始初始化各个Manager...
[2025-09-20 14:36:54.825] [INFO] 初始化基础Manager...
[2025-09-20 14:36:54.830] [INFO] IO状态缓存初始化完成
[2025-09-20 14:36:54.836] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-20 14:36:54.837] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-20 14:36:54.923] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-20 14:36:54.923] [ERROR] DMC1000B控制卡初始化失败
[2025-09-20 14:36:54.923] [WARN] DMC1000BIO管理器初始化失败
[2025-09-20 14:36:54.929] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-20 14:36:54.929] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-20 14:36:54.929] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-20 14:36:54.930] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-20 14:36:54.974] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__42_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 130
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 14:36:54.992] [WARN] DMC1000B电机管理器初始化失败
[2025-09-20 14:36:54.992] [INFO] 初始化系统模式管理器...
[2025-09-20 14:36:54.998] [INFO] 开始初始化MotorManager...
[2025-09-20 14:36:55.001] [INFO] 模拟初始化运动控制卡
[2025-09-20 14:36:55.221] [INFO] 加载了8个电机的默认配置
[2025-09-20 14:36:55.222] [INFO] 电机监控任务已启动
[2025-09-20 14:36:55.223] [INFO] MotorManager初始化完成
[2025-09-20 14:36:55.223] [INFO] 初始化通信Manager...
[2025-09-20 14:36:55.224] [INFO] 电机监控循环开始
[2025-09-20 14:36:55.226] [INFO] 开始初始化ScannerManager...
[2025-09-20 14:36:55.229] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-20 14:36:55.231] [INFO] 串口初始化完成
[2025-09-20 14:36:55.234] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-20 14:36:55.237] [INFO] 扫描枪连接成功: COM1
[2025-09-20 14:36:55.237] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-20 14:36:55.237] [INFO] ScannerManager初始化完成
[2025-09-20 14:36:55.241] [INFO] 开始初始化ModbusTcpManager...
[2025-09-20 14:36:55.242] [INFO] Modbus TCP配置: *************:502, SlaveId: 1
[2025-09-20 14:36:55.245] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-20 14:37:00.302] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: *************:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 14:37:00.302] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-20 14:37:00.302] [INFO] ModbusTcpManager初始化完成
[2025-09-20 14:37:00.305] [INFO] 开始初始化RobotTcpManager...
[2025-09-20 14:37:00.306] [INFO] 机器人TCP配置: *************:8080
[2025-09-20 14:37:00.308] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-20 14:37:05.350] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: *************:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 14:37:05.352] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-20 14:37:05.352] [INFO] RobotTcpManager初始化完成
[2025-09-20 14:37:05.352] [INFO] 初始化视觉Manager...
[2025-09-20 14:37:05.355] [INFO] 开始初始化VisionManager...
[2025-09-20 14:37:05.355] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-20 14:37:05.356] [INFO] 模拟初始化相机，索引: 0
[2025-09-20 14:37:05.860] [INFO] 相机初始化成功
[2025-09-20 14:37:05.861] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-20 14:37:05.861] [INFO] 视觉配置加载完成
[2025-09-20 14:37:05.861] [INFO] VisionManager初始化完成
[2025-09-20 14:37:05.862] [INFO] 初始化数据Manager...
[2025-09-20 14:37:05.864] [INFO] 开始初始化StatisticsManager...
[2025-09-20 14:37:05.864] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-20 14:37:05.868] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-20 14:37:05.868] [INFO] 历史数据加载完成
[2025-09-20 14:37:05.868] [INFO] 自动保存任务已启动
[2025-09-20 14:37:05.868] [INFO] StatisticsManager初始化完成
[2025-09-20 14:37:05.869] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-20 14:37:05.869] [INFO] 所有Manager初始化完成
[2025-09-20 14:37:05.870] [INFO] 自动保存循环开始
[2025-09-20 14:37:05.922] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-20 14:37:05.923] [INFO] 主界面布局创建完成
[2025-09-20 14:37:05.924] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-20 14:37:07.932] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-20 14:37:07.933] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-20 14:37:08.012] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 14:37:08.068] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 14:37:08.069] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-20 14:37:08.069] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-20 14:37:09.884] [INFO] 翻转电机控制面板资源释放完成
[2025-09-20 14:37:09.894] [INFO] 开始初始化EpsonRobotManager...
[2025-09-20 14:37:09.895] [INFO] Epson机器人配置 - 启动/停止: *************:5000, 数据收发: *************:5001
[2025-09-20 14:37:09.895] [INFO] EpsonRobotManager初始化完成
[2025-09-20 14:37:09.896] [INFO] Epson机器人管理器初始化完成
[2025-09-20 14:37:09.922] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-20 14:37:10.801] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-20 14:37:11.684] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
[2025-09-20 14:37:12.509] [INFO] Scara通信管理面板初始化完成 - 按HTML原型设计
[2025-09-20 14:37:13.685] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-20 14:37:13.685] [INFO] Epson机器人管理器初始化完成
[2025-09-20 14:37:13.688] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-20 14:37:14.598] [INFO] IO读取状态面板初始化完成 - 支持基础IO和扩展IO
[2025-09-20 14:37:15.273] [INFO] 生产日志管理面板初始化完成 - 按HTML原型设计
[2025-09-20 14:37:16.574] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-20 14:48:51.356] [INFO] 程序启动开始
[2025-09-20 14:48:51.358] [INFO] 配置系统初始化成功
[2025-09-20 14:48:51.409] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-20 14:48:51.410] [INFO] 配置系统初始化完成
[2025-09-20 14:48:51.410] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-20 14:48:51.434] [INFO] 开始初始化各个Manager...
[2025-09-20 14:48:51.434] [INFO] 初始化基础Manager...
[2025-09-20 14:48:51.439] [INFO] IO状态缓存初始化完成
[2025-09-20 14:48:51.444] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-20 14:48:51.445] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-20 14:48:51.446] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-20 14:48:51.446] [ERROR] DMC1000B控制卡初始化失败
[2025-09-20 14:48:51.446] [WARN] DMC1000BIO管理器初始化失败
[2025-09-20 14:48:51.450] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-20 14:48:51.450] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-20 14:48:51.451] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-20 14:48:51.451] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-20 14:48:51.489] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__42_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 130
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 14:48:51.504] [WARN] DMC1000B电机管理器初始化失败
[2025-09-20 14:48:51.504] [INFO] 初始化系统模式管理器...
[2025-09-20 14:48:51.510] [INFO] 开始初始化MotorManager...
[2025-09-20 14:48:51.513] [INFO] 模拟初始化运动控制卡
[2025-09-20 14:48:51.728] [INFO] 加载了8个电机的默认配置
[2025-09-20 14:48:51.729] [INFO] 电机监控任务已启动
[2025-09-20 14:48:51.729] [INFO] MotorManager初始化完成
[2025-09-20 14:48:51.730] [INFO] 初始化通信Manager...
[2025-09-20 14:48:51.731] [INFO] 电机监控循环开始
[2025-09-20 14:48:51.733] [INFO] 开始初始化ScannerManager...
[2025-09-20 14:48:51.735] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-20 14:48:51.776] [INFO] 串口初始化完成
[2025-09-20 14:48:51.809] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-20 14:48:51.812] [INFO] 扫描枪连接成功: COM1
[2025-09-20 14:48:51.812] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-20 14:48:51.812] [INFO] ScannerManager初始化完成
[2025-09-20 14:48:51.814] [INFO] 开始初始化ModbusTcpManager...
[2025-09-20 14:48:51.815] [INFO] Modbus TCP配置: *************:502, SlaveId: 1
[2025-09-20 14:48:51.817] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-20 14:48:56.865] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: *************:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 14:48:56.866] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-20 14:48:56.866] [INFO] ModbusTcpManager初始化完成
[2025-09-20 14:48:56.868] [INFO] 开始初始化RobotTcpManager...
[2025-09-20 14:48:56.869] [INFO] 机器人TCP配置: *************:8080
[2025-09-20 14:48:56.871] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-20 14:49:01.905] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: *************:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 14:49:01.905] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-20 14:49:01.906] [INFO] RobotTcpManager初始化完成
[2025-09-20 14:49:01.906] [INFO] 初始化视觉Manager...
[2025-09-20 14:49:01.908] [INFO] 开始初始化VisionManager...
[2025-09-20 14:49:01.908] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-20 14:49:01.910] [INFO] 模拟初始化相机，索引: 0
[2025-09-20 14:49:02.412] [INFO] 相机初始化成功
[2025-09-20 14:49:02.414] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-20 14:49:02.414] [INFO] 视觉配置加载完成
[2025-09-20 14:49:02.414] [INFO] VisionManager初始化完成
[2025-09-20 14:49:02.414] [INFO] 初始化数据Manager...
[2025-09-20 14:49:02.417] [INFO] 开始初始化StatisticsManager...
[2025-09-20 14:49:02.417] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-20 14:49:02.420] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-20 14:49:02.420] [INFO] 历史数据加载完成
[2025-09-20 14:49:02.421] [INFO] 自动保存任务已启动
[2025-09-20 14:49:02.421] [INFO] StatisticsManager初始化完成
[2025-09-20 14:49:02.421] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-20 14:49:02.421] [INFO] 所有Manager初始化完成
[2025-09-20 14:49:02.422] [INFO] 自动保存循环开始
[2025-09-20 14:49:02.458] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-20 14:49:02.458] [INFO] 主界面布局创建完成
[2025-09-20 14:49:02.460] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-20 14:49:06.440] [INFO] 开始初始化EpsonRobotManager...
[2025-09-20 14:49:06.442] [INFO] Epson机器人配置 - 启动/停止: *************:5000, 数据收发: *************:5001
[2025-09-20 14:49:06.442] [INFO] EpsonRobotManager初始化完成
[2025-09-20 14:49:06.442] [INFO] Epson机器人管理器初始化完成
[2025-09-20 14:49:06.453] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-20 14:49:11.863] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-20 14:49:11.863] [INFO] Epson机器人管理器初始化完成
[2025-09-20 14:49:11.866] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-20 15:06:32.679] [INFO] 程序启动开始
[2025-09-20 15:06:32.681] [INFO] 配置系统初始化成功
[2025-09-20 15:06:32.733] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-20 15:06:32.733] [INFO] 配置系统初始化完成
[2025-09-20 15:06:32.734] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-20 15:06:32.755] [INFO] 开始初始化各个Manager...
[2025-09-20 15:06:32.755] [INFO] 初始化基础Manager...
[2025-09-20 15:06:32.760] [INFO] IO状态缓存初始化完成
[2025-09-20 15:06:32.765] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-20 15:06:32.765] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-20 15:06:32.767] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-20 15:06:32.767] [ERROR] DMC1000B控制卡初始化失败
[2025-09-20 15:06:32.767] [WARN] DMC1000BIO管理器初始化失败
[2025-09-20 15:06:32.771] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-20 15:06:32.772] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-20 15:06:32.772] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-20 15:06:32.772] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-20 15:06:32.826] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__42_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 130
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 15:06:32.842] [WARN] DMC1000B电机管理器初始化失败
[2025-09-20 15:06:32.843] [INFO] 初始化系统模式管理器...
[2025-09-20 15:06:32.847] [INFO] 开始初始化MotorManager...
[2025-09-20 15:06:32.850] [INFO] 模拟初始化运动控制卡
[2025-09-20 15:06:33.063] [INFO] 加载了8个电机的默认配置
[2025-09-20 15:06:33.064] [INFO] 电机监控任务已启动
[2025-09-20 15:06:33.064] [INFO] MotorManager初始化完成
[2025-09-20 15:06:33.064] [INFO] 初始化通信Manager...
[2025-09-20 15:06:33.066] [INFO] 电机监控循环开始
[2025-09-20 15:06:33.068] [INFO] 开始初始化ScannerManager...
[2025-09-20 15:06:33.081] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-20 15:06:33.168] [INFO] 串口初始化完成
[2025-09-20 15:06:33.175] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-20 15:06:33.179] [INFO] 扫描枪连接成功: COM1
[2025-09-20 15:06:33.180] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-20 15:06:33.180] [INFO] ScannerManager初始化完成
[2025-09-20 15:06:33.185] [INFO] 开始初始化ModbusTcpManager...
[2025-09-20 15:06:33.189] [INFO] Modbus TCP配置: *************:502, SlaveId: 1
[2025-09-20 15:06:33.193] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-20 15:06:38.244] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: *************:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 15:06:38.245] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-20 15:06:38.245] [INFO] ModbusTcpManager初始化完成
[2025-09-20 15:06:38.247] [INFO] 开始初始化RobotTcpManager...
[2025-09-20 15:06:38.248] [INFO] 机器人TCP配置: *************:8080
[2025-09-20 15:06:38.250] [INFO] 机器人TCP状态变更: Connecting - 正在连接机器人...
[2025-09-20 15:06:43.281] [ERROR] 连接机器人TCP 执行失败
异常详情: 连接超时: *************:8080
堆栈跟踪:    在 MyHMI.Managers.RobotTcpManager.<<ConnectAsync>b__32_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\RobotTcpManager.cs:行号 162
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 15:06:43.281] [WARN] 机器人TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-20 15:06:43.281] [INFO] RobotTcpManager初始化完成
[2025-09-20 15:06:43.282] [INFO] 初始化视觉Manager...
[2025-09-20 15:06:43.284] [INFO] 开始初始化VisionManager...
[2025-09-20 15:06:43.284] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-20 15:06:43.286] [INFO] 模拟初始化相机，索引: 0
[2025-09-20 15:06:43.792] [INFO] 相机初始化成功
[2025-09-20 15:06:43.793] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-20 15:06:43.794] [INFO] 视觉配置加载完成
[2025-09-20 15:06:43.794] [INFO] VisionManager初始化完成
[2025-09-20 15:06:43.794] [INFO] 初始化数据Manager...
[2025-09-20 15:06:43.798] [INFO] 开始初始化StatisticsManager...
[2025-09-20 15:06:43.798] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-20 15:06:43.803] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-20 15:06:43.803] [INFO] 历史数据加载完成
[2025-09-20 15:06:43.804] [INFO] 自动保存任务已启动
[2025-09-20 15:06:43.804] [INFO] StatisticsManager初始化完成
[2025-09-20 15:06:43.804] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-20 15:06:43.805] [INFO] 所有Manager初始化完成
[2025-09-20 15:06:43.806] [INFO] 自动保存循环开始
[2025-09-20 15:06:43.846] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-20 15:06:43.846] [INFO] 主界面布局创建完成
[2025-09-20 15:06:43.848] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-20 15:06:45.965] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-20 15:06:45.966] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-20 15:06:46.040] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 15:06:46.099] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 15:06:46.100] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-20 15:06:46.100] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-20 15:06:46.724] [INFO] 翻转电机控制面板资源释放完成
[2025-09-20 15:06:46.734] [INFO] 开始初始化EpsonRobotManager...
[2025-09-20 15:06:46.735] [INFO] Epson机器人配置 - 启动/停止: *************:5000, 数据收发: *************:5001
[2025-09-20 15:06:46.735] [INFO] EpsonRobotManager初始化完成
[2025-09-20 15:06:46.735] [INFO] Epson机器人管理器初始化完成
[2025-09-20 15:06:46.745] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-20 15:35:48.111] [INFO] 程序启动开始
[2025-09-20 15:35:48.113] [INFO] 配置系统初始化成功
[2025-09-20 15:35:48.171] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-20 15:35:48.171] [INFO] 配置系统初始化完成
[2025-09-20 15:35:48.171] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-20 15:35:48.197] [INFO] 开始初始化各个Manager...
[2025-09-20 15:35:48.198] [INFO] 初始化基础Manager...
[2025-09-20 15:35:48.204] [INFO] IO状态缓存初始化完成
[2025-09-20 15:35:48.212] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-20 15:35:48.213] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-20 15:35:48.215] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-20 15:35:48.215] [ERROR] DMC1000B控制卡初始化失败
[2025-09-20 15:35:48.215] [WARN] DMC1000BIO管理器初始化失败
[2025-09-20 15:35:48.221] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-20 15:35:48.221] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-20 15:35:48.221] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-20 15:35:48.222] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-20 15:35:48.268] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__42_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 130
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 15:35:48.281] [WARN] DMC1000B电机管理器初始化失败
[2025-09-20 15:35:48.281] [INFO] 初始化系统模式管理器...
[2025-09-20 15:35:48.285] [INFO] 开始初始化MotorManager...
[2025-09-20 15:35:48.288] [INFO] 模拟初始化运动控制卡
[2025-09-20 15:35:48.495] [INFO] 加载了8个电机的默认配置
[2025-09-20 15:35:48.496] [INFO] 电机监控任务已启动
[2025-09-20 15:35:48.499] [INFO] 电机监控循环开始
[2025-09-20 15:35:48.499] [INFO] MotorManager初始化完成
[2025-09-20 15:35:48.499] [INFO] 初始化通信Manager...
[2025-09-20 15:35:48.539] [INFO] 开始初始化ScannerManager...
[2025-09-20 15:35:48.541] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-20 15:35:48.543] [INFO] 串口初始化完成
[2025-09-20 15:35:48.545] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-20 15:35:48.549] [INFO] 扫描枪连接成功: COM1
[2025-09-20 15:35:48.550] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-20 15:35:48.551] [INFO] ScannerManager初始化完成
[2025-09-20 15:35:48.554] [INFO] 开始初始化ModbusTcpManager...
[2025-09-20 15:35:48.557] [INFO] Modbus TCP配置: *************:502, SlaveId: 1
[2025-09-20 15:35:48.559] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-20 15:35:54.324] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: *************:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 15:35:54.325] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-20 15:35:54.325] [INFO] ModbusTcpManager初始化完成
[2025-09-20 15:35:54.359] [INFO] 开始初始化EpsonRobotManager...
[2025-09-20 15:35:54.361] [INFO] Epson机器人配置 - 启动/停止: *************:5000, 数据收发: *************:5001
[2025-09-20 15:35:54.361] [INFO] EpsonRobotManager初始化完成
[2025-09-20 15:35:54.361] [INFO] 初始化视觉Manager...
[2025-09-20 15:35:54.375] [INFO] 开始初始化VisionManager...
[2025-09-20 15:35:54.375] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-20 15:35:54.405] [INFO] 模拟初始化相机，索引: 0
[2025-09-20 15:35:55.539] [INFO] 相机初始化成功
[2025-09-20 15:35:55.541] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-20 15:35:55.542] [INFO] 视觉配置加载完成
[2025-09-20 15:35:55.542] [INFO] VisionManager初始化完成
[2025-09-20 15:35:55.542] [INFO] 初始化数据Manager...
[2025-09-20 15:35:55.547] [INFO] 开始初始化StatisticsManager...
[2025-09-20 15:35:55.547] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-20 15:35:55.552] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-20 15:35:55.553] [INFO] 历史数据加载完成
[2025-09-20 15:35:55.553] [INFO] 自动保存任务已启动
[2025-09-20 15:35:55.553] [INFO] StatisticsManager初始化完成
[2025-09-20 15:35:55.553] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-20 15:35:55.554] [INFO] 所有Manager初始化完成
[2025-09-20 15:35:55.559] [INFO] 自动保存循环开始
[2025-09-20 15:35:55.662] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-20 15:35:55.663] [INFO] 主界面布局创建完成
[2025-09-20 15:35:55.665] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-20 15:35:57.599] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-20 15:35:57.599] [INFO] Epson机器人管理器初始化完成
[2025-09-20 15:35:57.615] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
[2025-09-20 16:07:54.898] [INFO] 程序启动开始
[2025-09-20 16:07:54.900] [INFO] 配置系统初始化成功
[2025-09-20 16:07:54.950] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-20 16:07:54.950] [INFO] 配置系统初始化完成
[2025-09-20 16:07:54.951] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-20 16:07:54.979] [INFO] 开始初始化各个Manager...
[2025-09-20 16:07:54.979] [INFO] 初始化基础Manager...
[2025-09-20 16:07:54.987] [INFO] IO状态缓存初始化完成
[2025-09-20 16:07:54.995] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-20 16:07:54.995] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-20 16:07:54.997] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-20 16:07:54.998] [ERROR] DMC1000B控制卡初始化失败
[2025-09-20 16:07:54.999] [WARN] DMC1000BIO管理器初始化失败
[2025-09-20 16:07:55.009] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-20 16:07:55.009] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-20 16:07:55.010] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-20 16:07:55.011] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-20 16:07:55.057] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__42_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 130
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 16:07:55.074] [WARN] DMC1000B电机管理器初始化失败
[2025-09-20 16:07:55.074] [INFO] 初始化系统模式管理器...
[2025-09-20 16:07:55.079] [INFO] 开始初始化MotorManager...
[2025-09-20 16:07:55.082] [INFO] 模拟初始化运动控制卡
[2025-09-20 16:07:55.289] [INFO] 加载了8个电机的默认配置
[2025-09-20 16:07:55.290] [INFO] 电机监控任务已启动
[2025-09-20 16:07:55.291] [INFO] MotorManager初始化完成
[2025-09-20 16:07:55.291] [INFO] 初始化通信Manager...
[2025-09-20 16:07:55.293] [INFO] 电机监控循环开始
[2025-09-20 16:07:55.295] [INFO] 开始初始化ScannerManager...
[2025-09-20 16:07:55.301] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-20 16:07:55.315] [INFO] 串口初始化完成
[2025-09-20 16:07:55.326] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-20 16:07:55.328] [INFO] 扫描枪连接成功: COM1
[2025-09-20 16:07:55.329] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-20 16:07:55.330] [INFO] ScannerManager初始化完成
[2025-09-20 16:07:55.332] [INFO] 开始初始化ModbusTcpManager...
[2025-09-20 16:07:55.335] [INFO] Modbus TCP配置: *************:502, SlaveId: 1
[2025-09-20 16:07:55.338] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-20 16:08:00.391] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: *************:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-20 16:08:00.392] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-20 16:08:00.392] [INFO] ModbusTcpManager初始化完成
[2025-09-20 16:08:00.395] [INFO] 开始初始化EpsonRobotManager...
[2025-09-20 16:08:00.396] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-20 16:08:00.396] [INFO] EpsonRobotManager初始化完成
[2025-09-20 16:08:00.396] [INFO] 初始化视觉Manager...
[2025-09-20 16:08:00.398] [INFO] 开始初始化VisionManager...
[2025-09-20 16:08:00.399] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-20 16:08:00.400] [INFO] 模拟初始化相机，索引: 0
[2025-09-20 16:08:00.905] [INFO] 相机初始化成功
[2025-09-20 16:08:00.907] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-20 16:08:00.908] [INFO] 视觉配置加载完成
[2025-09-20 16:08:00.908] [INFO] VisionManager初始化完成
[2025-09-20 16:08:00.908] [INFO] 初始化数据Manager...
[2025-09-20 16:08:00.911] [INFO] 开始初始化StatisticsManager...
[2025-09-20 16:08:00.912] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-20 16:08:00.916] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-20 16:08:00.916] [INFO] 历史数据加载完成
[2025-09-20 16:08:00.916] [INFO] 自动保存任务已启动
[2025-09-20 16:08:00.921] [INFO] StatisticsManager初始化完成
[2025-09-20 16:08:00.921] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-20 16:08:00.921] [INFO] 所有Manager初始化完成
[2025-09-20 16:08:00.923] [INFO] 自动保存循环开始
[2025-09-20 16:08:00.973] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-20 16:08:00.973] [INFO] 主界面布局创建完成
[2025-09-20 16:08:00.974] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-20 16:08:25.925] [WARN] EpsonRobotManager已经初始化，跳过重复初始化
[2025-09-20 16:08:25.925] [INFO] Epson机器人管理器初始化完成
[2025-09-20 16:08:25.964] [INFO] 6轴机器人控制面板初始化完成 - 按HTML原型设计
