# MyHMI 上位机控制系统 - 故障排除指南

## 概述

本指南提供了 MyHMI 系统常见问题的诊断方法和解决方案，帮助开发者和用户快速定位和解决系统故障。

## 故障分类

### 1. 启动问题
### 2. 硬件通信问题
### 3. 配置问题
### 4. 性能问题
### 5. UI 界面问题
### 6. 网络通信问题
### 7. 日志和调试问题

## 启动问题

### 问题1：程序无法启动

#### 症状
- 双击程序图标无反应
- 出现错误对话框后程序退出
- 程序启动后立即崩溃

#### 可能原因和解决方案

**原因1：缺少 .NET Framework 4.8**
```
错误信息: "应用程序无法启动，因为缺少 .NET Framework 4.8"
解决方案:
1. 下载并安装 .NET Framework 4.8 Runtime
2. 重启计算机
3. 重新运行程序
```

**原因2：缺少依赖库**
```
错误信息: "无法加载文件或程序集 'XXX.dll'"
解决方案:
1. 检查程序目录是否包含所有必需的 DLL 文件
2. 重新部署完整的程序包
3. 检查 packages.config 确认所有依赖项
```

**原因3：权限不足**
```
错误信息: "访问被拒绝" 或硬件初始化失败
解决方案:
1. 右键点击程序图标，选择"以管理员身份运行"
2. 或者修改程序属性，设置为始终以管理员身份运行
```

**原因4：配置文件损坏**
```
错误信息: "配置文件格式错误" 或 JSON 解析错误
解决方案:
1. 检查 Config/SystemConfig.json 文件格式
2. 使用 JSON 验证工具检查语法
3. 恢复默认配置文件
```

#### 诊断步骤
```csharp
// 在 Program.cs 中添加启动诊断
static void Main()
{
    try
    {
        // 检查 .NET Framework 版本
        var version = Environment.Version;
        LogHelper.Info($".NET Framework 版本: {version}");
        
        // 检查依赖库
        CheckDependencies();
        
        // 检查权限
        CheckPermissions();
        
        // 检查配置文件
        CheckConfiguration();
        
        Application.Run(new MainForm());
    }
    catch (Exception ex)
    {
        LogHelper.Error("程序启动失败", ex);
        MessageBox.Show($"程序启动失败: {ex.Message}", "错误", 
            MessageBoxButtons.OK, MessageBoxIcon.Error);
    }
}
```

### 问题2：程序启动缓慢

#### 症状
- 程序启动时间超过 30 秒
- 启动过程中界面无响应

#### 解决方案
```csharp
// 优化启动流程
public async Task OptimizedStartup()
{
    // 显示启动画面
    var splashForm = new SplashForm();
    splashForm.Show();
    
    try
    {
        // 并行初始化管理器
        var initTasks = new[]
        {
            IOManager.Instance.InitializeAsync(),
            MotorManager.Instance.InitializeAsync(),
            VisionManager.Instance.InitializeAsync()
        };
        
        await Task.WhenAll(initTasks);
    }
    finally
    {
        splashForm.Close();
    }
}
```

## 硬件通信问题

### 问题1：IO 控制卡无法连接

#### 症状
- IOManager 初始化失败
- 无法读取或设置 IO 状态

#### 诊断步骤
```csharp
public async Task<bool> DiagnoseIOCard()
{
    try
    {
        LogHelper.Info("开始诊断 IO 控制卡...");
        
        // 检查控制卡是否存在
        bool cardExists = CheckIOCardExists();
        LogHelper.Info($"控制卡存在: {cardExists}");
        
        if (!cardExists)
        {
            LogHelper.Error("未检测到 IO 控制卡");
            return false;
        }
        
        // 检查驱动程序
        bool driverInstalled = CheckIODriverInstalled();
        LogHelper.Info($"驱动程序已安装: {driverInstalled}");
        
        // 尝试初始化
        bool initResult = await IOManager.Instance.InitializeAsync();
        LogHelper.Info($"初始化结果: {initResult}");
        
        return initResult;
    }
    catch (Exception ex)
    {
        LogHelper.Error("IO 控制卡诊断失败", ex);
        return false;
    }
}
```

#### 解决方案
```
1. 检查硬件连接
   - 确认控制卡正确插入 PCI 插槽
   - 检查电源连接
   - 确认设备管理器中显示设备

2. 检查驱动程序
   - 安装厂商提供的驱动程序
   - 确认驱动版本兼容性

3. 检查配置参数
   - 验证 CardIndex 设置
   - 确认通道数配置正确
```

### 问题2：电机控制异常

#### 症状
- 电机无法移动
- 位置反馈错误
- 电机运动不平滑

#### 诊断代码
```csharp
public async Task<string> DiagnoseMotorIssues(int axisId)
{
    var diagnosticInfo = new StringBuilder();
    
    try
    {
        // 检查电机状态
        var status = await MotorManager.Instance.GetMotorStatusAsync(axisId);
        diagnosticInfo.AppendLine($"轴 {axisId} 状态:");
        diagnosticInfo.AppendLine($"  当前位置: {status.CurrentPosition}");
        diagnosticInfo.AppendLine($"  是否运动: {status.IsMoving}");
        diagnosticInfo.AppendLine($"  是否回零: {status.IsHomed}");
        diagnosticInfo.AppendLine($"  是否有错误: {status.HasError}");
        
        if (status.HasError)
        {
            diagnosticInfo.AppendLine($"  错误信息: {status.ErrorMessage}");
        }
        
        // 检查电机参数
        var parameters = await MotorManager.Instance.GetMotorParamsAsync(axisId);
        diagnosticInfo.AppendLine($"电机参数:");
        diagnosticInfo.AppendLine($"  最大速度: {parameters.MaxSpeed}");
        diagnosticInfo.AppendLine($"  最大加速度: {parameters.MaxAcceleration}");
        diagnosticInfo.AppendLine($"  位置范围: {parameters.MinPosition} ~ {parameters.MaxPosition}");
        
    }
    catch (Exception ex)
    {
        diagnosticInfo.AppendLine($"诊断过程中发生错误: {ex.Message}");
    }
    
    return diagnosticInfo.ToString();
}
```

## 配置问题

### 问题1：配置文件加载失败

#### 症状
- 系统使用默认配置
- 配置修改不生效
- JSON 解析错误

#### 解决方案
```csharp
public bool ValidateAndFixConfiguration()
{
    try
    {
        var configPath = SystemConfiguration.Instance.GetConfigFilePath();
        
        // 检查文件是否存在
        if (!File.Exists(configPath))
        {
            LogHelper.Warning("配置文件不存在，创建默认配置");
            CreateDefaultConfiguration();
            return true;
        }
        
        // 验证 JSON 格式
        var jsonContent = File.ReadAllText(configPath);
        try
        {
            JObject.Parse(jsonContent);
        }
        catch (JsonException ex)
        {
            LogHelper.Error("配置文件 JSON 格式错误", ex);
            
            // 备份损坏的配置文件
            var backupPath = $"{configPath}.backup_{DateTime.Now:yyyyMMdd_HHmmss}";
            File.Copy(configPath, backupPath);
            
            // 创建新的默认配置
            CreateDefaultConfiguration();
            return true;
        }
        
        // 验证配置内容
        var config = SystemConfiguration.Instance.Config;
        if (config == null)
        {
            LogHelper.Error("配置对象为空");
            return false;
        }
        
        return ValidateConfigurationContent(config);
    }
    catch (Exception ex)
    {
        LogHelper.Error("配置验证失败", ex);
        return false;
    }
}
```

### 问题2：参数配置错误

#### 常见配置错误
```json
// 错误的配置示例
{
  "Motor": {
    "Axes": [
      {
        "AxisId": 0,
        "MaxSpeed": -1000,        // 错误：负数速度
        "MaxPosition": 100,
        "MinPosition": 200        // 错误：最小值大于最大值
      }
    ]
  },
  "Communication": {
    "ModbusTcp": {
      "IPAddress": "invalid_ip", // 错误：无效IP地址
      "Port": 70000             // 错误：端口号超出范围
    }
  }
}
```

#### 配置验证代码
```csharp
public List<string> ValidateConfiguration(SystemConfig config)
{
    var errors = new List<string>();
    
    // 验证电机配置
    foreach (var axis in config.Motor.Axes)
    {
        if (axis.MaxSpeed <= 0)
            errors.Add($"轴 {axis.AxisId}: 最大速度必须大于0");
            
        if (axis.MinPosition >= axis.MaxPosition)
            errors.Add($"轴 {axis.AxisId}: 最小位置必须小于最大位置");
    }
    
    // 验证通信配置
    if (!IPAddress.TryParse(config.Communication.ModbusTcp.IPAddress, out _))
        errors.Add("Modbus TCP IP地址格式错误");
        
    if (config.Communication.ModbusTcp.Port < 1 || config.Communication.ModbusTcp.Port > 65535)
        errors.Add("Modbus TCP 端口号超出有效范围");
    
    return errors;
}
```

## 性能问题

### 问题1：界面响应缓慢

#### 症状
- 按钮点击无响应
- 界面更新延迟
- 程序假死

#### 解决方案
```csharp
// 使用异步操作避免界面阻塞
private async void Button_Click(object sender, EventArgs e)
{
    try
    {
        // 禁用按钮防止重复点击
        ((Button)sender).Enabled = false;
        
        // 显示进度指示
        progressBar.Visible = true;
        
        // 在后台线程执行耗时操作
        await Task.Run(() => LongRunningOperation());
        
        // 更新界面
        UIHelper.SafeInvoke(() => UpdateUI());
    }
    catch (Exception ex)
    {
        LogHelper.Error("操作失败", ex);
        MessageBox.Show($"操作失败: {ex.Message}");
    }
    finally
    {
        // 恢复界面状态
        ((Button)sender).Enabled = true;
        progressBar.Visible = false;
    }
}
```

### 问题2：内存泄漏

#### 诊断方法
```csharp
public class MemoryMonitor
{
    private Timer _monitorTimer;
    
    public void StartMonitoring()
    {
        _monitorTimer = new Timer(5000); // 每5秒检查一次
        _monitorTimer.Elapsed += (s, e) =>
        {
            var memoryUsage = GC.GetTotalMemory(false);
            LogHelper.Debug($"内存使用量: {memoryUsage / 1024 / 1024} MB");
            
            if (memoryUsage > 500 * 1024 * 1024) // 超过500MB
            {
                LogHelper.Warning("内存使用量过高，建议检查内存泄漏");
                GC.Collect(); // 强制垃圾回收
            }
        };
        _monitorTimer.Start();
    }
}
```

## 网络通信问题

### 问题1：Modbus TCP 连接失败

#### 诊断步骤
```csharp
public async Task<string> DiagnoseModbusTcpConnection()
{
    var result = new StringBuilder();
    var config = SystemConfiguration.Instance.Config.Communication.ModbusTcp;
    
    try
    {
        // 1. 检查网络连通性
        result.AppendLine("1. 检查网络连通性...");
        var ping = new Ping();
        var pingReply = await ping.SendPingAsync(config.IPAddress, 5000);
        result.AppendLine($"   Ping 结果: {pingReply.Status}");
        
        if (pingReply.Status != IPStatus.Success)
        {
            result.AppendLine("   网络不通，请检查网络连接和IP地址");
            return result.ToString();
        }
        
        // 2. 检查端口连通性
        result.AppendLine("2. 检查端口连通性...");
        using (var tcpClient = new TcpClient())
        {
            var connectTask = tcpClient.ConnectAsync(config.IPAddress, config.Port);
            var timeoutTask = Task.Delay(config.ConnectTimeout);
            
            if (await Task.WhenAny(connectTask, timeoutTask) == connectTask)
            {
                result.AppendLine("   端口连接成功");
            }
            else
            {
                result.AppendLine("   端口连接超时");
            }
        }
        
        // 3. 尝试 Modbus 通信
        result.AppendLine("3. 尝试 Modbus 通信...");
        var modbusManager = ModbusTcpManager.Instance;
        bool connectResult = await modbusManager.ConnectAsync(config.IPAddress, config.Port);
        result.AppendLine($"   Modbus 连接结果: {connectResult}");
        
    }
    catch (Exception ex)
    {
        result.AppendLine($"诊断过程中发生错误: {ex.Message}");
    }
    
    return result.ToString();
}
```

### 问题2：串口通信问题

#### 解决方案
```csharp
public string DiagnoseSerialPort(string portName)
{
    var result = new StringBuilder();
    
    try
    {
        // 检查串口是否存在
        var availablePorts = SerialPort.GetPortNames();
        result.AppendLine($"可用串口: {string.Join(", ", availablePorts)}");
        
        if (!availablePorts.Contains(portName))
        {
            result.AppendLine($"错误: 串口 {portName} 不存在");
            return result.ToString();
        }
        
        // 尝试打开串口
        using (var serialPort = new SerialPort(portName, 9600))
        {
            serialPort.Open();
            result.AppendLine($"串口 {portName} 打开成功");
            
            // 发送测试数据
            serialPort.WriteLine("TEST");
            result.AppendLine("测试数据发送成功");
        }
    }
    catch (Exception ex)
    {
        result.AppendLine($"串口诊断失败: {ex.Message}");
    }
    
    return result.ToString();
}
```

## 日志和调试

### 启用详细日志
```xml
<!-- 在 App.config 中配置详细日志 -->
<nlog>
  <targets>
    <target xsi:type="File" name="debugTarget"
            fileName=".\Logs\Debug-${shortdate}.log"
            layout="${longdate} ${level} ${logger} ${message} ${exception:format=tostring}" />
  </targets>
  <rules>
    <logger name="*" minlevel="Debug" writeTo="debugTarget" />
  </rules>
</nlog>
```

### 调试工具类
```csharp
public static class DebugHelper
{
    public static void DumpSystemInfo()
    {
        LogHelper.Info("=== 系统信息 ===");
        LogHelper.Info($"操作系统: {Environment.OSVersion}");
        LogHelper.Info($".NET 版本: {Environment.Version}");
        LogHelper.Info($"工作目录: {Environment.CurrentDirectory}");
        LogHelper.Info($"内存使用: {GC.GetTotalMemory(false) / 1024 / 1024} MB");
    }
    
    public static void DumpConfiguration()
    {
        LogHelper.Info("=== 配置信息 ===");
        var config = SystemConfiguration.Instance.Config;
        LogHelper.Info($"系统名称: {config.System.Name}");
        LogHelper.Info($"调试模式: {config.System.DebugMode}");
        LogHelper.Info($"IO 通道数: {config.IO.InputChannels}/{config.IO.OutputChannels}");
    }
}
```

## 紧急恢复

### 系统重置
```csharp
public async Task<bool> EmergencyReset()
{
    try
    {
        LogHelper.Warning("执行紧急重置...");
        
        // 1. 停止所有运动
        await MotorManager.Instance.StopAllMotorsAsync();
        
        // 2. 重置所有输出
        for (int i = 0; i < 16; i++)
        {
            await IOManager.Instance.SetOutputAsync(i, false);
        }
        
        // 3. 断开所有通信连接
        await ModbusTcpManager.Instance.DisconnectAsync();
        await ScannerManager.Instance.ClosePortAsync();
        
        // 4. 重置工作流状态
        await WorkflowManager.Instance.ResetWorkflowAsync();
        
        LogHelper.Info("紧急重置完成");
        return true;
    }
    catch (Exception ex)
    {
        LogHelper.Error("紧急重置失败", ex);
        return false;
    }
}
```

### 配置恢复
```csharp
public bool RestoreDefaultConfiguration()
{
    try
    {
        var configPath = SystemConfiguration.Instance.GetConfigFilePath();
        var backupPath = $"{configPath}.backup_{DateTime.Now:yyyyMMdd_HHmmss}";
        
        // 备份当前配置
        if (File.Exists(configPath))
        {
            File.Copy(configPath, backupPath);
        }
        
        // 创建默认配置
        CreateDefaultConfiguration();
        
        // 重新加载配置
        return SystemConfiguration.Instance.ReloadConfiguration();
    }
    catch (Exception ex)
    {
        LogHelper.Error("配置恢复失败", ex);
        return false;
    }
}
```

## 技术支持

### 收集诊断信息
```csharp
public string CollectDiagnosticInfo()
{
    var info = new StringBuilder();
    
    info.AppendLine("=== MyHMI 诊断报告 ===");
    info.AppendLine($"生成时间: {DateTime.Now}");
    info.AppendLine($"程序版本: {Assembly.GetExecutingAssembly().GetName().Version}");
    info.AppendLine();
    
    // 系统信息
    info.AppendLine("=== 系统信息 ===");
    info.AppendLine($"操作系统: {Environment.OSVersion}");
    info.AppendLine($".NET 版本: {Environment.Version}");
    info.AppendLine($"内存使用: {GC.GetTotalMemory(false) / 1024 / 1024} MB");
    info.AppendLine();
    
    // 配置信息
    info.AppendLine("=== 配置信息 ===");
    var config = SystemConfiguration.Instance.Config;
    info.AppendLine(JsonConvert.SerializeObject(config, Formatting.Indented));
    info.AppendLine();
    
    // 最近的错误日志
    info.AppendLine("=== 最近错误日志 ===");
    var logFiles = Directory.GetFiles("./Logs", "*.log")
        .OrderByDescending(f => File.GetLastWriteTime(f))
        .Take(3);
        
    foreach (var logFile in logFiles)
    {
        info.AppendLine($"--- {Path.GetFileName(logFile)} ---");
        var lines = File.ReadAllLines(logFile)
            .Where(line => line.Contains("ERROR"))
            .TakeLast(10);
        foreach (var line in lines)
        {
            info.AppendLine(line);
        }
        info.AppendLine();
    }
    
    return info.ToString();
}
```

通过本故障排除指南，可以快速定位和解决 MyHMI 系统的常见问题，确保系统稳定运行。
