using System;
using System.Collections.Generic;
using MyHMI.Events;

namespace MyHMI.Models
{
    /// <summary>
    /// I/O信号状态记录
    /// </summary>
    public class IOSignalState
    {
        /// <summary>
        /// I/O信号编号
        /// </summary>
        public string IONumber { get; set; }

        /// <summary>
        /// 信号类型
        /// </summary>
        public IOSignalType SignalType { get; set; }

        /// <summary>
        /// 当前信号值
        /// </summary>
        public bool CurrentValue { get; set; }

        /// <summary>
        /// 上一次信号值
        /// </summary>
        public bool PreviousValue { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; }

        /// <summary>
        /// 信号稳定时间（用于防抖动）
        /// </summary>
        public DateTime StableTime { get; set; }

        /// <summary>
        /// 是否稳定（防抖动处理后）
        /// </summary>
        public bool IsStable { get; set; }

        /// <summary>
        /// 边沿检测历史（最近的边沿变化）
        /// </summary>
        public List<EdgeDetectionRecord> EdgeHistory { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="ioNumber">I/O编号</param>
        /// <param name="signalType">信号类型</param>
        public IOSignalState(string ioNumber, IOSignalType signalType)
        {
            IONumber = ioNumber ?? string.Empty;
            SignalType = signalType;
            CurrentValue = false;
            PreviousValue = false;
            LastUpdateTime = DateTime.Now;
            StableTime = DateTime.Now;
            IsStable = true;
            EdgeHistory = new List<EdgeDetectionRecord>();
        }

        /// <summary>
        /// 更新信号值
        /// </summary>
        /// <param name="newValue">新的信号值</param>
        /// <param name="debounceTimeMs">防抖动时间（毫秒）</param>
        /// <returns>是否发生了稳定的信号变化</returns>
        public bool UpdateValue(bool newValue, int debounceTimeMs = 50)
        {
            var now = DateTime.Now;
            
            // 如果值没有变化，更新稳定时间
            if (newValue == CurrentValue)
            {
                StableTime = now;
                IsStable = true;
                return false;
            }

            // 值发生变化，检查是否需要防抖动处理
            if (!IsStable && (now - LastUpdateTime).TotalMilliseconds < debounceTimeMs)
            {
                // 还在防抖动期间，不更新值
                return false;
            }

            // 记录边沿变化
            bool isRisingEdge = newValue && !CurrentValue;
            EdgeHistory.Add(new EdgeDetectionRecord
            {
                Timestamp = now,
                OldValue = CurrentValue,
                NewValue = newValue,
                IsRisingEdge = isRisingEdge
            });

            // 保持最近10次边沿记录
            if (EdgeHistory.Count > 10)
            {
                EdgeHistory.RemoveAt(0);
            }

            // 更新值
            PreviousValue = CurrentValue;
            CurrentValue = newValue;
            LastUpdateTime = now;
            StableTime = now;
            IsStable = true;

            return true;
        }

        /// <summary>
        /// 检测是否有边沿触发
        /// </summary>
        /// <returns>边沿检测结果</returns>
        public EdgeDetectionResult DetectEdge()
        {
            if (EdgeHistory.Count == 0)
                return null;

            var lastEdge = EdgeHistory[EdgeHistory.Count - 1];
            
            // 检查是否是最近的边沿（在很短时间内）
            if ((DateTime.Now - lastEdge.Timestamp).TotalMilliseconds > 100)
                return null;

            return new EdgeDetectionResult
            {
                IONumber = IONumber,
                SignalType = SignalType,
                IsRisingEdge = lastEdge.IsRisingEdge,
                OldValue = lastEdge.OldValue,
                NewValue = lastEdge.NewValue,
                Timestamp = lastEdge.Timestamp
            };
        }
    }

    /// <summary>
    /// 边沿检测记录
    /// </summary>
    public class EdgeDetectionRecord
    {
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 旧值
        /// </summary>
        public bool OldValue { get; set; }

        /// <summary>
        /// 新值
        /// </summary>
        public bool NewValue { get; set; }

        /// <summary>
        /// 是否为上升沿
        /// </summary>
        public bool IsRisingEdge { get; set; }
    }

    /// <summary>
    /// 边沿检测结果
    /// </summary>
    public class EdgeDetectionResult
    {
        /// <summary>
        /// I/O信号编号
        /// </summary>
        public string IONumber { get; set; }

        /// <summary>
        /// 信号类型
        /// </summary>
        public IOSignalType SignalType { get; set; }

        /// <summary>
        /// 是否为上升沿
        /// </summary>
        public bool IsRisingEdge { get; set; }

        /// <summary>
        /// 旧值
        /// </summary>
        public bool OldValue { get; set; }

        /// <summary>
        /// 新值
        /// </summary>
        public bool NewValue { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 安全管理器状态
    /// </summary>
    public class SafetyManagerStatus
    {
        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized { get; set; }

        /// <summary>
        /// 是否正在运行
        /// </summary>
        public bool IsRunning { get; set; }

        /// <summary>
        /// 当前安全状态
        /// </summary>
        public SafetyState CurrentSafetyState { get; set; }

        /// <summary>
        /// 当前指示灯状态
        /// </summary>
        public IndicatorLightState CurrentIndicatorState { get; set; }

        /// <summary>
        /// 监控的I/O信号状态
        /// </summary>
        public Dictionary<string, IOSignalState> IOSignalStates { get; set; }

        /// <summary>
        /// 最后一次状态更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; }

        /// <summary>
        /// 启动时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 运行时长
        /// </summary>
        public TimeSpan RunningTime => IsRunning ? DateTime.Now - StartTime : TimeSpan.Zero;

        /// <summary>
        /// 错误计数
        /// </summary>
        public int ErrorCount { get; set; }

        /// <summary>
        /// 紧急停止触发次数
        /// </summary>
        public int EmergencyStopCount { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public SafetyManagerStatus()
        {
            IsInitialized = false;
            IsRunning = false;
            CurrentSafetyState = SafetyState.Safe;
            CurrentIndicatorState = IndicatorLightState.Off;
            IOSignalStates = new Dictionary<string, IOSignalState>();
            LastUpdateTime = DateTime.Now;
            StartTime = DateTime.Now;
            ErrorCount = 0;
            EmergencyStopCount = 0;
        }

        /// <summary>
        /// 获取指定I/O信号的状态
        /// </summary>
        /// <param name="ioNumber">I/O编号</param>
        /// <returns>信号状态，如果不存在返回null</returns>
        public IOSignalState GetIOSignalState(string ioNumber)
        {
            return IOSignalStates.ContainsKey(ioNumber) ? IOSignalStates[ioNumber] : null;
        }

        /// <summary>
        /// 更新状态时间戳
        /// </summary>
        public void UpdateTimestamp()
        {
            LastUpdateTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 安全管理器配置
    /// </summary>
    public class SafetyManagerConfiguration
    {
        /// <summary>
        /// I/O监控轮询间隔（毫秒）
        /// </summary>
        public int MonitoringIntervalMs { get; set; } = 20;

        /// <summary>
        /// 信号防抖动时间（毫秒）
        /// </summary>
        public int DebounceTimeMs { get; set; } = 50;

        /// <summary>
        /// 是否启用详细日志
        /// </summary>
        public bool EnableVerboseLogging { get; set; } = false;

        /// <summary>
        /// 紧急停止后的恢复延迟时间（毫秒）
        /// </summary>
        public int EmergencyStopRecoveryDelayMs { get; set; } = 1000;

        /// <summary>
        /// 指示灯切换延迟时间（毫秒）
        /// </summary>
        public int IndicatorLightSwitchDelayMs { get; set; } = 100;

        /// <summary>
        /// 最大错误重试次数
        /// </summary>
        public int MaxErrorRetryCount { get; set; } = 3;

        /// <summary>
        /// I/O信号配置映射
        /// </summary>
        public Dictionary<IOSignalType, string> IOSignalMapping { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public SafetyManagerConfiguration()
        {
            IOSignalMapping = new Dictionary<IOSignalType, string>
            {
                { IOSignalType.StartSignal, "I0001" },
                { IOSignalType.StopSignal, "I0002" },
                { IOSignalType.SafetyLockSignal, "I0003" },
                { IOSignalType.SafetyDoorSignal, "I0101" },
                { IOSignalType.LightCurtain1Signal, "I0102" },
                { IOSignalType.LightCurtain2Signal, "I0103" }
            };
        }
    }

    /// <summary>
    /// I/O监控统计信息
    /// </summary>
    public class IOMonitoringStatistics
    {
        /// <summary>
        /// 监控的信号总数
        /// </summary>
        public int TotalSignalsMonitored { get; set; }

        /// <summary>
        /// 当前激活的信号数量
        /// </summary>
        public int ActiveSignalsCount { get; set; }

        /// <summary>
        /// 总边沿检测次数
        /// </summary>
        public int TotalEdgeDetections { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; }

        /// <summary>
        /// 监控间隔（毫秒）
        /// </summary>
        public int MonitoringInterval { get; set; }

        /// <summary>
        /// 防抖动时间（毫秒）
        /// </summary>
        public int DebounceTime { get; set; }

        /// <summary>
        /// 错误计数
        /// </summary>
        public int ErrorCount { get; set; }

        /// <summary>
        /// 紧急停止触发次数
        /// </summary>
        public int EmergencyStopCount { get; set; }

        /// <summary>
        /// 各个信号的统计信息
        /// </summary>
        public Dictionary<string, IOSignalStatistics> SignalStatistics { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public IOMonitoringStatistics()
        {
            SignalStatistics = new Dictionary<string, IOSignalStatistics>();
        }
    }

    /// <summary>
    /// 单个I/O信号统计信息
    /// </summary>
    public class IOSignalStatistics
    {
        /// <summary>
        /// I/O编号
        /// </summary>
        public string IONumber { get; set; }

        /// <summary>
        /// 信号类型
        /// </summary>
        public IOSignalType SignalType { get; set; }

        /// <summary>
        /// 当前值
        /// </summary>
        public bool CurrentValue { get; set; }

        /// <summary>
        /// 是否稳定
        /// </summary>
        public bool IsStable { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; }

        /// <summary>
        /// 边沿检测次数
        /// </summary>
        public int EdgeDetectionCount { get; set; }

        /// <summary>
        /// 最后一次边沿检测时间
        /// </summary>
        public DateTime LastEdgeTime { get; set; }
    }

    /// <summary>
    /// 紧急停止条件详情
    /// </summary>
    public class EmergencyStopConditions
    {
        /// <summary>
        /// 安全门是否开启 (I0101为0时为true)
        /// </summary>
        public bool SafetyDoorOpen { get; set; }

        /// <summary>
        /// 光栅1是否触发 (I0102为1时为true)
        /// </summary>
        public bool LightCurtain1Triggered { get; set; }

        /// <summary>
        /// 光栅2是否触发 (I0103为1时为true)
        /// </summary>
        public bool LightCurtain2Triggered { get; set; }

        /// <summary>
        /// 是否安全锁定 (I0003为1时为true)
        /// </summary>
        public bool SafetyLocked { get; set; }

        /// <summary>
        /// 是否存在任何紧急停止条件
        /// </summary>
        public bool HasAnyEmergencyCondition { get; set; }

        /// <summary>
        /// 检查时间
        /// </summary>
        public DateTime CheckTime { get; set; }

        /// <summary>
        /// 获取触发的条件描述
        /// </summary>
        /// <returns>条件描述列表</returns>
        public List<string> GetTriggeredConditions()
        {
            var conditions = new List<string>();

            if (SafetyDoorOpen)
                conditions.Add("安全门开启");

            if (LightCurtain1Triggered)
                conditions.Add("光栅1触发");

            if (LightCurtain2Triggered)
                conditions.Add("光栅2触发");

            if (SafetyLocked)
                conditions.Add("安全锁定");

            return conditions;
        }

        /// <summary>
        /// 获取条件摘要描述
        /// </summary>
        /// <returns>摘要描述</returns>
        public string GetSummary()
        {
            if (!HasAnyEmergencyCondition && !SafetyLocked)
                return "所有条件正常";

            var triggeredConditions = GetTriggeredConditions();
            return $"触发条件: {string.Join(", ", triggeredConditions)}";
        }
    }

    /// <summary>
    /// 启动检查结果
    /// </summary>
    public class StartupCheckResult
    {
        /// <summary>
        /// 是否可以启动
        /// </summary>
        public bool CanStart { get; set; }

        /// <summary>
        /// 阻塞启动的原因列表
        /// </summary>
        public List<string> BlockingReasons { get; set; }

        /// <summary>
        /// 是否安全锁定
        /// </summary>
        public bool SafetyLocked { get; set; }

        /// <summary>
        /// 紧急停止条件详情
        /// </summary>
        public EmergencyStopConditions EmergencyConditions { get; set; }

        /// <summary>
        /// 当前安全状态
        /// </summary>
        public SafetyState CurrentSafetyState { get; set; }

        /// <summary>
        /// 检查时间
        /// </summary>
        public DateTime CheckTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public StartupCheckResult()
        {
            CanStart = false;
            BlockingReasons = new List<string>();
            SafetyLocked = false;
            EmergencyConditions = new EmergencyStopConditions();
            CurrentSafetyState = SafetyState.Safe;
            CheckTime = DateTime.Now;
        }

        /// <summary>
        /// 获取检查结果摘要
        /// </summary>
        /// <returns>摘要描述</returns>
        public string GetSummary()
        {
            if (CanStart)
                return "系统可以启动";

            return $"系统无法启动，原因: {string.Join("; ", BlockingReasons)}";
        }
    }

    /// <summary>
    /// 启动停止信号状态
    /// </summary>
    public class StartStopSignalStatus
    {
        /// <summary>
        /// 启动信号值 (I0001)
        /// </summary>
        public bool StartSignalValue { get; set; }

        /// <summary>
        /// 停止信号值 (I0002)
        /// </summary>
        public bool StopSignalValue { get; set; }

        /// <summary>
        /// 安全锁定信号值 (I0003)
        /// </summary>
        public bool SafetyLockValue { get; set; }

        /// <summary>
        /// 启动信号最后一次边沿检测记录
        /// </summary>
        public EdgeDetectionRecord StartSignalLastEdge { get; set; }

        /// <summary>
        /// 停止信号最后一次边沿检测记录
        /// </summary>
        public EdgeDetectionRecord StopSignalLastEdge { get; set; }

        /// <summary>
        /// 安全锁定信号最后一次边沿检测记录
        /// </summary>
        public EdgeDetectionRecord SafetyLockLastEdge { get; set; }

        /// <summary>
        /// 读取时间
        /// </summary>
        public DateTime ReadTime { get; set; }

        /// <summary>
        /// 当前安全状态
        /// </summary>
        public SafetyState CurrentSafetyState { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public StartStopSignalStatus()
        {
            StartSignalValue = false;
            StopSignalValue = false;
            SafetyLockValue = false;
            ReadTime = DateTime.Now;
            CurrentSafetyState = SafetyState.Safe;
        }

        /// <summary>
        /// 获取信号状态摘要
        /// </summary>
        /// <returns>摘要描述</returns>
        public string GetSummary()
        {
            return $"启动信号: {StartSignalValue}, 停止信号: {StopSignalValue}, 安全锁定: {SafetyLockValue}, 安全状态: {CurrentSafetyState}";
        }
    }

    /// <summary>
    /// 指示灯状态详情
    /// </summary>
    public class IndicatorLightStatus
    {
        /// <summary>
        /// 红灯输出状态 (O0008)
        /// </summary>
        public bool RedLightOutput { get; set; }

        /// <summary>
        /// 绿灯输出状态 (O0009)
        /// </summary>
        public bool GreenLightOutput { get; set; }

        /// <summary>
        /// 黄灯输出状态 (O0010)
        /// </summary>
        public bool YellowLightOutput { get; set; }

        /// <summary>
        /// 期望的指示灯状态
        /// </summary>
        public IndicatorLightState ExpectedState { get; set; }

        /// <summary>
        /// 实际的指示灯状态
        /// </summary>
        public IndicatorLightState ActualState { get; set; }

        /// <summary>
        /// 期望状态与实际状态是否一致
        /// </summary>
        public bool IsConsistent { get; set; }

        /// <summary>
        /// 读取时间
        /// </summary>
        public DateTime ReadTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public IndicatorLightStatus()
        {
            RedLightOutput = false;
            GreenLightOutput = false;
            YellowLightOutput = false;
            ExpectedState = IndicatorLightState.Off;
            ActualState = IndicatorLightState.Off;
            IsConsistent = true;
            ReadTime = DateTime.Now;
        }

        /// <summary>
        /// 获取状态摘要
        /// </summary>
        /// <returns>摘要描述</returns>
        public string GetSummary()
        {
            var outputs = new List<string>();
            if (RedLightOutput) outputs.Add("红灯");
            if (GreenLightOutput) outputs.Add("绿灯");
            if (YellowLightOutput) outputs.Add("黄灯");

            string outputStatus = outputs.Count > 0 ? string.Join("+", outputs) : "全部关闭";
            return $"输出状态: {outputStatus}, 期望: {ExpectedState}, 一致性: {(IsConsistent ? "正常" : "异常")}";
        }
    }

    /// <summary>
    /// 指示灯控制统计信息
    /// </summary>
    public class IndicatorLightStatistics
    {
        /// <summary>
        /// 当前指示灯状态
        /// </summary>
        public IndicatorLightState CurrentState { get; set; }

        /// <summary>
        /// 最后一次状态变化时间
        /// </summary>
        public DateTime LastChangeTime { get; set; }

        /// <summary>
        /// 指示灯切换延迟时间（毫秒）
        /// </summary>
        public int SwitchDelayMs { get; set; }

        /// <summary>
        /// 红灯激活次数
        /// </summary>
        public int RedLightActivations { get; set; }

        /// <summary>
        /// 绿灯激活次数
        /// </summary>
        public int GreenLightActivations { get; set; }

        /// <summary>
        /// 黄灯激活次数
        /// </summary>
        public int YellowLightActivations { get; set; }

        /// <summary>
        /// 总状态切换次数
        /// </summary>
        public int TotalStateChanges { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public IndicatorLightStatistics()
        {
            CurrentState = IndicatorLightState.Off;
            LastChangeTime = DateTime.Now;
            SwitchDelayMs = 100;
            RedLightActivations = 0;
            GreenLightActivations = 0;
            YellowLightActivations = 0;
            TotalStateChanges = 0;
        }

        /// <summary>
        /// 获取统计摘要
        /// </summary>
        /// <returns>摘要描述</returns>
        public string GetSummary()
        {
            return $"当前状态: {CurrentState}, 总切换次数: {TotalStateChanges}, " +
                   $"红灯: {RedLightActivations}次, 绿灯: {GreenLightActivations}次, 黄灯: {YellowLightActivations}次";
        }
    }
}
