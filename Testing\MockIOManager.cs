using System;
using System.Threading.Tasks;
using MyHMI.Events;
using MyHMI.Helpers;

namespace MyHMI.Testing
{
    /// <summary>
    /// IO管理器Mock类，用于无硬件测试
    /// </summary>
    public class MockIOManager
    {
        #region 单例模式
        private static readonly Lazy<MockIOManager> _instance = new Lazy<MockIOManager>(() => new MockIOManager());
        public static MockIOManager Instance => _instance.Value;
        private MockIOManager() { }
        #endregion

        #region 私有字段
        private bool _isInitialized = false;
        private bool[] _inputStates = new bool[16];
        private bool[] _outputStates = new bool[16];
        private Random _random = new Random();
        #endregion

        #region 公共属性
        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;
        #endregion

        #region 事件定义
        /// <summary>
        /// IO状态变化事件
        /// </summary>
        public event EventHandler<IOStateChangedEventArgs> IOStateChanged;
        #endregion

        #region 初始化和释放
        /// <summary>
        /// 异步初始化Mock IO管理器
        /// </summary>
        /// <returns></returns>
        public async Task<bool> InitializeAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (_isInitialized)
                {
                    LogHelper.Warning("MockIOManager已经初始化，跳过重复初始化");
                    return true;
                }

                LogHelper.Info("开始初始化MockIOManager...");

                // 模拟初始化延迟
                await Task.Delay(500);

                // 初始化IO状态
                for (int i = 0; i < 16; i++)
                {
                    _inputStates[i] = false;
                    _outputStates[i] = false;
                }

                _isInitialized = true;
                LogHelper.Info("MockIOManager初始化完成");
                return true;

            }, false, "MockIOManager初始化");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public async Task DisposeAsync()
        {
            await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                LogHelper.Info("开始释放MockIOManager资源...");

                await Task.Delay(100); // 模拟释放延迟

                _isInitialized = false;
                LogHelper.Info("MockIOManager资源释放完成");

                return true;
            }, false, "MockIOManager资源释放");
        }
        #endregion

        #region IO操作方法
        /// <summary>
        /// 读取输入状态
        /// </summary>
        /// <param name="channel">通道号</param>
        /// <returns>输入状态</returns>
        public async Task<bool> ReadInputAsync(int channel)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (!_isInitialized)
                {
                    throw new InvalidOperationException("MockIOManager未初始化");
                }

                if (channel < 0 || channel >= 16)
                {
                    throw new ArgumentOutOfRangeException(nameof(channel), "通道号必须在0-15之间");
                }

                // 模拟读取延迟
                await Task.Delay(10);

                // 随机模拟输入变化
                if (_random.NextDouble() < 0.1) // 10%概率变化
                {
                    _inputStates[channel] = !_inputStates[channel];
                    
                    // 触发状态变化事件
                    IOStateChanged?.Invoke(this, new IOStateChangedEventArgs(channel, true, IoType.Input));
                }

                return _inputStates[channel];

            }, false, $"读取输入通道{channel}");
        }

        /// <summary>
        /// 设置输出状态
        /// </summary>
        /// <param name="channel">通道号</param>
        /// <param name="state">输出状态</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SetOutputAsync(int channel, bool state)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (!_isInitialized)
                {
                    throw new InvalidOperationException("MockIOManager未初始化");
                }

                if (channel < 0 || channel >= 16)
                {
                    throw new ArgumentOutOfRangeException(nameof(channel), "通道号必须在0-15之间");
                }

                // 模拟设置延迟
                await Task.Delay(10);

                bool oldState = _outputStates[channel];
                _outputStates[channel] = state;

                LogHelper.Debug($"Mock设置输出通道{channel}: {oldState} -> {state}");

                // 触发状态变化事件
                if (oldState != state)
                {
                    IOStateChanged?.Invoke(this, new IOStateChangedEventArgs(channel, state, IoType.Output));
                }

                return true;

            }, false, $"设置输出通道{channel}为{state}");
        }

        /// <summary>
        /// 读取输出状态
        /// </summary>
        /// <param name="channel">通道号</param>
        /// <returns>输出状态</returns>
        public async Task<bool> ReadOutputAsync(int channel)
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (!_isInitialized)
                {
                    throw new InvalidOperationException("MockIOManager未初始化");
                }

                if (channel < 0 || channel >= 16)
                {
                    throw new ArgumentOutOfRangeException(nameof(channel), "通道号必须在0-15之间");
                }

                // 模拟读取延迟
                await Task.Delay(5);

                return _outputStates[channel];

            }, false, $"读取输出通道{channel}");
        }

        /// <summary>
        /// 读取所有输入状态
        /// </summary>
        /// <returns>输入状态数组</returns>
        public async Task<bool[]> ReadAllInputsAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (!_isInitialized)
                {
                    throw new InvalidOperationException("MockIOManager未初始化");
                }

                // 模拟读取延迟
                await Task.Delay(20);

                // 随机模拟一些输入变化
                for (int i = 0; i < 16; i++)
                {
                    if (_random.NextDouble() < 0.05) // 5%概率变化
                    {
                        bool oldState = _inputStates[i];
                        _inputStates[i] = !_inputStates[i];
                        
                        if (oldState != _inputStates[i])
                        {
                            IOStateChanged?.Invoke(this, new IOStateChangedEventArgs(i, _inputStates[i], IoType.Input));
                        }
                    }
                }

                return (bool[])_inputStates.Clone();

            }, new bool[16], "读取所有输入状态");
        }

        /// <summary>
        /// 读取所有输出状态
        /// </summary>
        /// <returns>输出状态数组</returns>
        public async Task<bool[]> ReadAllOutputsAsync()
        {
            return await ExceptionHelper.SafeExecuteAsync(async () =>
            {
                if (!_isInitialized)
                {
                    throw new InvalidOperationException("MockIOManager未初始化");
                }

                // 模拟读取延迟
                await Task.Delay(20);

                return (bool[])_outputStates.Clone();

            }, new bool[16], "读取所有输出状态");
        }
        #endregion

        #region 测试辅助方法
        /// <summary>
        /// 模拟输入变化（用于测试）
        /// </summary>
        /// <param name="channel">通道号</param>
        /// <param name="state">新状态</param>
        public void SimulateInputChange(int channel, bool state)
        {
            if (channel >= 0 && channel < 16)
            {
                bool oldState = _inputStates[channel];
                _inputStates[channel] = state;
                
                if (oldState != state)
                {
                    LogHelper.Debug($"Mock模拟输入通道{channel}变化: {oldState} -> {state}");
                    IOStateChanged?.Invoke(this, new IOStateChangedEventArgs(channel, state, IoType.Input));
                }
            }
        }

        /// <summary>
        /// 获取当前所有状态（用于测试验证）
        /// </summary>
        /// <returns>输入和输出状态</returns>
        public (bool[] inputs, bool[] outputs) GetAllStates()
        {
            return ((bool[])_inputStates.Clone(), (bool[])_outputStates.Clone());
        }

        /// <summary>
        /// 重置所有状态（用于测试）
        /// </summary>
        public void ResetAllStates()
        {
            for (int i = 0; i < 16; i++)
            {
                _inputStates[i] = false;
                _outputStates[i] = false;
            }
            LogHelper.Debug("Mock重置所有IO状态");
        }
        #endregion
    }
}
