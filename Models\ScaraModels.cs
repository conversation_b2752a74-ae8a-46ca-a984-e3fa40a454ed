using System;

namespace MyHMI.Models
{
    /// <summary>
    /// SCARA工作流程状态枚举
    /// </summary>
    public enum ScaraWorkflowState
    {
        /// <summary>
        /// 空闲状态
        /// </summary>
        Idle,

        /// <summary>
        /// 工作中
        /// </summary>
        Working,

        /// <summary>
        /// 已完成
        /// </summary>
        Completed,

        /// <summary>
        /// 错误状态
        /// </summary>
        Error
    }

    /// <summary>
    /// SCARA状态变化事件参数
    /// </summary>
    public class ScaraStateChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 电机侧（Left/Right）
        /// </summary>
        public string MotorSide { get; }

        /// <summary>
        /// 旧状态
        /// </summary>
        public ScaraWorkflowState OldState { get; }

        /// <summary>
        /// 新状态
        /// </summary>
        public ScaraWorkflowState NewState { get; }

        /// <summary>
        /// 状态变化时间
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="motorSide">电机侧</param>
        /// <param name="oldState">旧状态</param>
        /// <param name="newState">新状态</param>
        public ScaraStateChangedEventArgs(string motorSide, ScaraWorkflowState oldState, ScaraWorkflowState newState)
        {
            MotorSide = motorSide;
            OldState = oldState;
            NewState = newState;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// SCARA工作流程完成事件参数
    /// </summary>
    public class ScaraWorkflowCompletedEventArgs : EventArgs
    {
        /// <summary>
        /// 电机侧（Left/Right）
        /// </summary>
        public string MotorSide { get; }

        /// <summary>
        /// 是否成功完成
        /// </summary>
        public bool Success { get; }

        /// <summary>
        /// 完成时间
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="motorSide">电机侧</param>
        /// <param name="success">是否成功</param>
        public ScaraWorkflowCompletedEventArgs(string motorSide, bool success)
        {
            MotorSide = motorSide;
            Success = success;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// SCARA错误事件参数
    /// </summary>
    public class ScaraErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 错误消息
        /// </summary>
        public string Message { get; }

        /// <summary>
        /// 异常对象
        /// </summary>
        public Exception Exception { get; }

        /// <summary>
        /// 错误时间
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="exception">异常对象</param>
        public ScaraErrorEventArgs(string message, Exception exception = null)
        {
            Message = message;
            Exception = exception;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// SCARA配置信息
    /// </summary>
    public class ScaraConfiguration
    {
        /// <summary>
        /// 左电机名称
        /// </summary>
        public string LeftMotorName { get; set; } = "LeftMotor";

        /// <summary>
        /// 右电机名称
        /// </summary>
        public string RightMotorName { get; set; } = "RightMotor";

        /// <summary>
        /// 工作流程超时时间（毫秒）
        /// </summary>
        public int WorkflowTimeoutMs { get; set; } = 30000;

        /// <summary>
        /// IO操作延迟时间（毫秒）
        /// </summary>
        public int IODelayMs { get; set; } = 100;

        /// <summary>
        /// 字段等待超时时间（毫秒）
        /// </summary>
        public int FieldWaitTimeoutMs { get; set; } = 30000;

        /// <summary>
        /// 错误恢复延迟时间（毫秒）
        /// </summary>
        public int ErrorRecoveryDelayMs { get; set; } = 5000;

        /// <summary>
        /// 工作流程完成后的延迟时间（毫秒）
        /// </summary>
        public int CompletionDelayMs { get; set; } = 1000;
    }

    /// <summary>
    /// SCARA状态信息
    /// </summary>
    public class ScaraStatusInfo
    {
        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized { get; set; }

        /// <summary>
        /// 是否正在运行
        /// </summary>
        public bool IsRunning { get; set; }

        /// <summary>
        /// 左电机状态
        /// </summary>
        public ScaraWorkflowState LeftMotorState { get; set; }

        /// <summary>
        /// 右电机状态
        /// </summary>
        public ScaraWorkflowState RightMotorState { get; set; }

        /// <summary>
        /// 左电机工作流程完成次数
        /// </summary>
        public int LeftMotorCompletedCount { get; set; }

        /// <summary>
        /// 右电机工作流程完成次数
        /// </summary>
        public int RightMotorCompletedCount { get; set; }

        /// <summary>
        /// 左电机错误次数
        /// </summary>
        public int LeftMotorErrorCount { get; set; }

        /// <summary>
        /// 右电机错误次数
        /// </summary>
        public int RightMotorErrorCount { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// SCARA工作流程步骤枚举
    /// </summary>
    public enum ScaraWorkflowStep
    {
        /// <summary>
        /// 等待位置到达
        /// </summary>
        WaitingForPosition,

        /// <summary>
        /// 夹爪控制
        /// </summary>
        GripperControl,

        /// <summary>
        /// 等待角度矫正
        /// </summary>
        WaitingForAngle,

        /// <summary>
        /// 等待安全距离
        /// </summary>
        WaitingForSafety,

        /// <summary>
        /// 移动到位置2
        /// </summary>
        MovingToPosition2,

        /// <summary>
        /// 顶料控制
        /// </summary>
        PushControl,

        /// <summary>
        /// 移动到位置3
        /// </summary>
        MovingToPosition3,

        /// <summary>
        /// 退料控制
        /// </summary>
        RetractControl,

        /// <summary>
        /// 移动到位置4
        /// </summary>
        MovingToPosition4,

        /// <summary>
        /// 设置数据获取标志
        /// </summary>
        SettingDataFlag,

        /// <summary>
        /// 工作流程完成
        /// </summary>
        Completed
    }

    /// <summary>
    /// SCARA工作流程详细状态
    /// </summary>
    public class ScaraWorkflowDetailStatus
    {
        /// <summary>
        /// 电机侧
        /// </summary>
        public string MotorSide { get; set; }

        /// <summary>
        /// 当前状态
        /// </summary>
        public ScaraWorkflowState CurrentState { get; set; }

        /// <summary>
        /// 当前步骤
        /// </summary>
        public ScaraWorkflowStep CurrentStep { get; set; }

        /// <summary>
        /// 步骤开始时间
        /// </summary>
        public DateTime StepStartTime { get; set; }

        /// <summary>
        /// 工作流程开始时间
        /// </summary>
        public DateTime WorkflowStartTime { get; set; }

        /// <summary>
        /// 是否有错误
        /// </summary>
        public bool HasError { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 进度百分比（0-100）
        /// </summary>
        public int ProgressPercentage { get; set; }
    }
}
