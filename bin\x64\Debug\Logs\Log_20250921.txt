[2025-09-21 20:32:45.097] [INFO] 程序启动开始
[2025-09-21 20:32:45.099] [INFO] 配置系统初始化成功
[2025-09-21 20:32:45.169] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-21 20:32:45.169] [INFO] 配置系统初始化完成
[2025-09-21 20:32:45.170] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-21 20:32:45.198] [INFO] 开始初始化各个Manager...
[2025-09-21 20:32:45.198] [INFO] 初始化基础Manager...
[2025-09-21 20:32:45.204] [INFO] IO状态缓存初始化完成
[2025-09-21 20:32:45.211] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-21 20:32:45.212] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-21 20:32:45.214] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-21 20:32:45.215] [ERROR] DMC1000B控制卡初始化失败
[2025-09-21 20:32:45.215] [WARN] DMC1000BIO管理器初始化失败
[2025-09-21 20:32:45.220] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-21 20:32:45.220] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-21 20:32:45.221] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-21 20:32:45.221] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-21 20:32:45.290] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__42_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 130
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-21 20:32:45.306] [WARN] DMC1000B电机管理器初始化失败
[2025-09-21 20:32:45.306] [INFO] 初始化系统模式管理器...
[2025-09-21 20:32:45.310] [INFO] 开始初始化MotorManager...
[2025-09-21 20:32:45.312] [INFO] 模拟初始化运动控制卡
[2025-09-21 20:32:45.521] [INFO] 加载了8个电机的默认配置
[2025-09-21 20:32:45.522] [INFO] 电机监控任务已启动
[2025-09-21 20:32:45.522] [INFO] MotorManager初始化完成
[2025-09-21 20:32:45.522] [INFO] 初始化通信Manager...
[2025-09-21 20:32:45.523] [INFO] 电机监控循环开始
[2025-09-21 20:32:45.525] [INFO] 开始初始化ScannerManager...
[2025-09-21 20:32:45.527] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-21 20:32:45.562] [INFO] 串口初始化完成
[2025-09-21 20:32:45.565] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-21 20:32:45.569] [INFO] 扫描枪连接成功: COM1
[2025-09-21 20:32:45.569] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-21 20:32:45.569] [INFO] ScannerManager初始化完成
[2025-09-21 20:32:45.572] [INFO] 开始初始化ModbusTcpManager...
[2025-09-21 20:32:45.574] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-21 20:32:45.578] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-21 20:32:50.631] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-21 20:32:50.631] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-21 20:32:50.632] [INFO] ModbusTcpManager初始化完成
[2025-09-21 20:32:50.634] [INFO] 开始初始化EpsonRobotManager...
[2025-09-21 20:32:50.636] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-21 20:32:50.636] [INFO] EpsonRobotManager初始化完成
[2025-09-21 20:32:50.636] [INFO] 初始化视觉Manager...
[2025-09-21 20:32:50.639] [INFO] 开始初始化VisionManager...
[2025-09-21 20:32:50.639] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-21 20:32:50.641] [INFO] 模拟初始化相机，索引: 0
[2025-09-21 20:32:51.143] [INFO] 相机初始化成功
[2025-09-21 20:32:51.144] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-21 20:32:51.145] [INFO] 视觉配置加载完成
[2025-09-21 20:32:51.145] [INFO] VisionManager初始化完成
[2025-09-21 20:32:51.145] [INFO] 初始化数据Manager...
[2025-09-21 20:32:51.149] [INFO] 开始初始化StatisticsManager...
[2025-09-21 20:32:51.155] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-21 20:32:51.160] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-21 20:32:51.160] [INFO] 历史数据加载完成
[2025-09-21 20:32:51.160] [INFO] 自动保存任务已启动
[2025-09-21 20:32:51.160] [INFO] StatisticsManager初始化完成
[2025-09-21 20:32:51.160] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-21 20:32:51.161] [INFO] 所有Manager初始化完成
[2025-09-21 20:32:51.162] [INFO] 自动保存循环开始
[2025-09-21 20:32:51.210] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-21 20:32:51.210] [INFO] 主界面布局创建完成
[2025-09-21 20:32:51.211] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-21 20:32:58.632] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-21 20:32:58.633] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-21 20:32:58.710] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-21 20:32:58.766] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-21 20:32:58.767] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-21 20:32:58.767] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-21 20:56:08.725] [INFO] 翻转电机控制面板资源释放完成
[2025-09-21 21:21:28.542] [INFO] 程序启动开始
[2025-09-21 21:21:28.543] [INFO] 配置系统初始化成功
[2025-09-21 21:21:28.595] [INFO] 系统配置加载成功: E:\projects\C#_projects\HR2\bin\x64\Debug\Config\SystemConfig.json
[2025-09-21 21:21:28.595] [INFO] 配置系统初始化完成
[2025-09-21 21:21:28.596] [WARN] UI上下文初始化失败，将使用Control.Invoke方式
[2025-09-21 21:21:28.623] [INFO] 开始初始化各个Manager...
[2025-09-21 21:21:28.624] [INFO] 初始化基础Manager...
[2025-09-21 21:21:28.628] [INFO] IO状态缓存初始化完成
[2025-09-21 21:21:28.636] [INFO] DMC1000BIOManager 请求初始化控制卡，当前引用计数: 1
[2025-09-21 21:21:28.636] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-21 21:21:28.638] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-21 21:21:28.638] [ERROR] DMC1000B控制卡初始化失败
[2025-09-21 21:21:28.638] [WARN] DMC1000BIO管理器初始化失败
[2025-09-21 21:21:28.642] [INFO] 开始初始化DMC1000B运动控制卡...
[2025-09-21 21:21:28.643] [INFO] DMC1000BMotorManager 请求初始化控制卡，当前引用计数: 1
[2025-09-21 21:21:28.643] [INFO] 开始初始化DMC1000B控制卡...
[2025-09-21 21:21:28.643] [ERROR] DMC1000B控制卡初始化失败：未检测到控制卡，返回值: 0
[2025-09-21 21:21:28.681] [ERROR] DMC1000B初始化 执行失败
异常详情: DMC1000B控制卡初始化失败
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<<InitializeAsync>b__42_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 130
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-21 21:21:28.696] [WARN] DMC1000B电机管理器初始化失败
[2025-09-21 21:21:28.696] [INFO] 初始化系统模式管理器...
[2025-09-21 21:21:28.701] [INFO] 开始初始化MotorManager...
[2025-09-21 21:21:28.704] [INFO] 模拟初始化运动控制卡
[2025-09-21 21:21:28.912] [INFO] 加载了8个电机的默认配置
[2025-09-21 21:21:28.913] [INFO] 电机监控任务已启动
[2025-09-21 21:21:28.913] [INFO] MotorManager初始化完成
[2025-09-21 21:21:28.913] [INFO] 初始化通信Manager...
[2025-09-21 21:21:28.914] [INFO] 电机监控循环开始
[2025-09-21 21:21:28.916] [INFO] 开始初始化ScannerManager...
[2025-09-21 21:21:28.938] [INFO] 扫描枪配置: COM1, 9600, 8, One, None
[2025-09-21 21:21:28.941] [INFO] 串口初始化完成
[2025-09-21 21:21:28.943] [INFO] 扫描枪状态变更: Connecting - 正在连接扫描枪...
[2025-09-21 21:21:28.947] [INFO] 扫描枪连接成功: COM1
[2025-09-21 21:21:28.947] [INFO] 扫描枪状态变更: Connected - 扫描枪连接成功
[2025-09-21 21:21:28.947] [INFO] ScannerManager初始化完成
[2025-09-21 21:21:28.950] [INFO] 开始初始化ModbusTcpManager...
[2025-09-21 21:21:28.952] [INFO] Modbus TCP配置: 192.168.1.101:502, SlaveId: 1
[2025-09-21 21:21:28.955] [INFO] Modbus TCP状态变更: Connecting - 正在连接Modbus TCP...
[2025-09-21 21:21:33.994] [ERROR] 连接Modbus TCP 执行失败
异常详情: 连接超时: 192.168.1.101:502
堆栈跟踪:    在 MyHMI.Managers.ModbusTcpManager.<<ConnectAsync>b__28_0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\ModbusTcpManager.cs:行号 152
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-21 21:21:33.995] [WARN] Modbus TCP连接失败，但初始化完成，可稍后重试连接
[2025-09-21 21:21:33.995] [INFO] ModbusTcpManager初始化完成
[2025-09-21 21:21:33.997] [INFO] 开始初始化EpsonRobotManager...
[2025-09-21 21:21:33.999] [INFO] Epson机器人配置 - IP地址: *************, 控制端口: 5000, 数据端口: 5001
[2025-09-21 21:21:33.999] [INFO] EpsonRobotManager初始化完成
[2025-09-21 21:21:33.999] [INFO] 初始化视觉Manager...
[2025-09-21 21:21:34.002] [INFO] 开始初始化VisionManager...
[2025-09-21 21:21:34.002] [INFO] 视觉系统配置: 相机索引=0, 配置文件=Config\VisionConfig.json, 采集间隔=1000ms
[2025-09-21 21:21:34.004] [INFO] 模拟初始化相机，索引: 0
[2025-09-21 21:21:34.520] [INFO] 相机初始化成功
[2025-09-21 21:21:34.525] [INFO] 模拟加载视觉配置文件: Config\VisionConfig.json
[2025-09-21 21:21:34.525] [INFO] 视觉配置加载完成
[2025-09-21 21:21:34.526] [INFO] VisionManager初始化完成
[2025-09-21 21:21:34.527] [INFO] 初始化数据Manager...
[2025-09-21 21:21:34.543] [INFO] 开始初始化StatisticsManager...
[2025-09-21 21:21:34.546] [INFO] 统计管理器配置: 数据目录=Data\Statistics, 缓存大小=10000, 自动保存间隔=5分钟
[2025-09-21 21:21:34.569] [INFO] 加载统计数据: 日统计0条, 月统计0条
[2025-09-21 21:21:34.570] [INFO] 历史数据加载完成
[2025-09-21 21:21:34.570] [INFO] 自动保存任务已启动
[2025-09-21 21:21:34.571] [INFO] StatisticsManager初始化完成
[2025-09-21 21:21:34.571] [INFO] Manager初始化完成，成功率: 部分失败
[2025-09-21 21:21:34.571] [INFO] 所有Manager初始化完成
[2025-09-21 21:21:34.574] [INFO] 自动保存循环开始
[2025-09-21 21:21:34.614] [INFO] 定位相机控制面板初始化完成 - 按HTML原型设计
[2025-09-21 21:21:34.615] [INFO] 主界面布局创建完成
[2025-09-21 21:21:34.616] [INFO] 主界面初始化完成 - 按HTML原型设计
[2025-09-21 21:24:10.672] [INFO] 翻转电机控制面板初始化完成 - 按HTML原型设计
[2025-09-21 21:24:10.673] [WARN] DMC1000BMotorManager未初始化，电机控制功能可能无法正常工作
[2025-09-21 21:24:10.746] [ERROR] 设置翻转电机轴0参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-21 21:24:10.805] [ERROR] 设置翻转电机轴1参数 执行失败
异常详情: 翻转电机参数无效: 回零速度不能超过最大速度的30%
堆栈跟踪:    在 MyHMI.Managers.DMC1000BMotorManager.<>c__DisplayClass44_0.<<SetFlipMotorParamsAsync>b__0>d.MoveNext() 位置 E:\projects\C#_projects\HR2\Managers\DMC1000BMotorManager.cs:行号 192
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   在 System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()
   在 MyHMI.Helpers.ExceptionHelper.<SafeExecuteAsync>d__3`1.MoveNext() 位置 E:\projects\C#_projects\HR2\Helpers\ExceptionHelper.cs:行号 119
[2025-09-21 21:24:10.806] [INFO] 翻转电机参数初始化完成 - 已配置传感器IO和安全限制
[2025-09-21 21:24:10.806] [INFO] 翻转电机控制面板业务逻辑初始化完成
[2025-09-21 21:29:42.205] [INFO] 翻转电机控制面板资源释放完成
